import pandas as pd

from ingestion.data_dictionaries_json import read_data_dict


"""
To convert admin dictionaries to excel in the ncdr format
"""

if __name__ == '__main__':

    dataset = 'Discharges'

    sections = []
    elements = []
    definitions = []
    selections = []
    ranges = []
    parent_child_validations = []

    data = read_data_dict(f'biome_{dataset.lower()}.json')['Dictionary']

    for section in data['Sections']:
        sections.append({
            'Section Code': section['Section Code'],
            'Section Display Name': section['Section Display Name'],
            'Section Type': section['Section Type'],
            'Parent Section': section['Parent Section'],
            'Container Class': section['Container Class'],
            'Cardinality': section['Cardinality'],
            'Table': section['Table']
        })
        for element in section['Elements']:
            elements.append({
                'Section Code': section['Section Code'],
                'Element Reference': element['Element Reference'],
                'Short Name': element['Short Name'],
                'Name': element['Name'],
                'DB Data Type': element['DB Data Type'],
                'Role': element['Role'],
                'PHI': element['PHI'],
                'Missing Data': element['Missing Data'],
                'Selection Type': element['Selection Type'],
                'Precision': element['Precision'],
                'Is Identifier': element['Is Identifier'],
                'Is Base Element': element['Is Base Element'],
                'Is Followup Element': element['Is Followup Element']
            })
            if element['Definition']:
                element['Definition']['Element Reference'] = element['Element Reference']
                element['Definition']['Name'] = element['Name']
                definitions.append(element['Definition'])
            if element['Selections']:
                for selection in element['Selections']:
                    selection['Element Reference'] = element['Element Reference']
                    selections.append(selection)
            if element['Ranges']:
                for range in element['Ranges']:
                    range['Element Reference'] = element['Element Reference']
                    ranges.append(range)
            if element['Parent Child Validations']:
                for parent_child_validation in element['Parent Child Validations']:
                    parent_child_validation['Element Reference'] = element['Element Reference']
                    parent_child_validations.append(parent_child_validation)

    # dataframes
    sections = pd.DataFrame(sections)
    elements = pd.DataFrame(elements)
    definitions = pd.DataFrame(definitions)
    selections = pd.DataFrame(selections)
    ranges = pd.DataFrame(ranges)
    parent_child_validations = pd.DataFrame(parent_child_validations)

    # write data to excel with multiple sheets
    with pd.ExcelWriter(f'ingestion/data_dictionaries/{dataset}New.xlsx') as writer:
        sections.to_excel(writer, sheet_name='Section Containment Structure', index=False)
        elements.to_excel(writer, sheet_name='Elements', index=False)
        definitions.to_excel(writer, sheet_name='Supporting Definitions', index=False)
        selections.to_excel(writer, sheet_name='Selections', index=False)
        ranges.to_excel(writer, sheet_name='Element Ranges', index=False)
        parent_child_validations.to_excel(writer, sheet_name='Parent Child Validations', index=False)
