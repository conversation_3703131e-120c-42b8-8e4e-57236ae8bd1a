"""
This script is used to build the vendor version of the dictionary for the admin dataset.
"""

from hashlib import blake2b

import numpy as np
import pandas as pd

from ingestion.config import read_all_clients_config
from ingestion.data_dictionaries_json import write_data_dict
from ingestion.data_dictionaries import read_data_dict


def get_source_fields(client_mappings, target_field):
    target_field = target_field.lower()
    clients = {}
    for client, mappings in client_mappings.items():
        required_fields = mappings['required_fields']
        rename_fields = mappings['rename_fields']
        if target_field in required_fields:
            source_fields = []
            for field_map in rename_fields:
                if field_map['Old Field'].lower() == target_field:
                    source_fields.append(field_map['New Field'])
            if len(source_fields) == 0:
                source_fields.append(target_field)
            clients[client] = source_fields
        else:
            clients[client] = []
    return clients


def blake_encode(value):
    h = blake2b(digest_size=4)
    h.update(str(value).encode('ascii'))
    h2int = int(h.hexdigest(), 16)
    return h2int


def build_dict(dataset, dataset_id, source_dict_path, write=False):
    client_mappings = read_all_clients_config(dataset)

    elements = read_data_dict(source_dict_path, sheet_name='Fields').rename(
        columns={'Name': 'Short Name', 'Data Type': 'DB Data Type'}
    )[['Short Name', 'DB Data Type', 'Role', 'PHI', 'Required']]

    elements['Element Reference'] = elements['Short Name'].str.lower().apply(blake_encode)

    elements['Definition'] = None
    elements['Missing Data'] = np.where(elements['Required'] == 1, 'Illegal', 'No Action')
    elements['Selection Type'] = 'Single'
    elements['Selections'] = None
    elements['Precision'] = None
    elements['Parent Child Validations'] = None
    elements['Ranges'] = None
    elements['Name'] = elements['Short Name']
    elements['Is Identifier'] = np.where(elements['Role'].str.contains('identifier', case=False), 'Yes', 'No')
    elements['Is Base Element'] = 'Yes'
    elements['Is Followup Element'] = 'No'
    elements['Source Fields'] = elements['Short Name'].apply(lambda x: get_source_fields(client_mappings, x))

    elements['Role'] = elements['Role'].apply(lambda x: None if pd.isna(x) else x)

    elements.drop(columns=['Required'], inplace=True)

    elements = elements.to_dict(orient='records')

    data = {
        'Dictionary': {
            'Dataset': {
                'Code': dataset,
                'Id': dataset_id
            },
            'Version': '1.0',
            'Type': 'Vendor',
            'Sections': [
                {
                    "Container Class": "defaultContainer",
                    "Parent Section": "Root",
                    "Section Display Name": "Default",
                    "Section Code": "DEFAULT",
                    "Section Type": "Section",
                    "Cardinality": "1..1",
                    "Table": "RAW",
                    "Elements": elements
                }
            ]
        }
    }
    if write:
        write_data_dict(f'{dataset}.json', data)


if __name__ == '__main__':
    from ingestion.schema.cpt import SchemaCpt as schema

    build_dict(schema.DATASET.lower(), dataset_id=schema.DATASET_ID, source_dict_path=schema.DATA_DICT, write=True)
