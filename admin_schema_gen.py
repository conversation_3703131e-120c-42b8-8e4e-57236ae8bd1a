import yaml
from functools import reduce

from ingestion.utils.db import read_sql

from dotenv import load_dotenv


def convert_yaml(group, client, dataset):
    mapped_fields = group[group['fieldname'] != group['mappedfieldname']]
    mapped_fields = mapped_fields[['fieldname', 'mappedfieldname']]

    rename_fields = []
    for idx, row in mapped_fields.iterrows():
        rename_fields.append(
            {
                'New Field': row['fieldname'],
                'Old Field': row['mappedfieldname']
            }
        )

    print('Client:', client)
    fields = {
        'required_fields': group['mappedfieldname'].str.lower().unique().tolist(),
        'rename_fields': rename_fields
    }

    with open(f'ingestion/config/{dataset}-{client}.yaml', 'w') as f:
        yaml.dump(fields, f)


def get_client_schema(dataset):
    load_dotenv("env.dev")
    qry = f"""
    select distinct cdf.client,cf.fieldname,cf.mappedfieldname, registrysubset
    from Ingestion_Prod.clientdatafiles cdf
    join Ingestion_Prod.clientfields cf
    on cf.fileid =
    cdf.id
    where contenttype = 'admin' and registrysubset like '%admin%{dataset}%' and
    FROM_UNIXTIME(cdf.createdon) >= '2022-01-01'
    and cf.mappedfieldname in (SELECT name from Ingestion_Prod.fields where subsetid like '%|41|%')
    order by client desc;"""

    qry2 = f"""
    select upper(ifnull(a.SourceName, b.`name`)) client_field_name
    , upper(a.Client) client_name
    , if (IFNULL(b.name, '')='', replace(replace(replace(replace(replace(replace(replace(replace(replace(concat(
    a.SourceName, a.id), "'", ""), ' ', ''), ',', ''), "'", ''), '"', ''), '.', ''), '(', ''), ')', ''), '-', ''),
     substr(b.`Name` from 1 for 60)) as field_name
    , Upper(concat(RegistryName, '__', SubsetName)) registry_subset_name
    , b.id field_id
    , c.id subset_id
    , a.id client_field_mapping_id
    , ifnull(fa.SortOrder, 0) sort_order
    from Subsets c
    join `Fields` b on b.SubsetId like concat('%|', c.id, '|%')
    left join `FieldAttributes` fa on b.id = fa.FieldId and c.id = fa.SubsetId
    left join ClientFieldMapping a on b.id = a.TargetFieldId
    where Upper(concat(RegistryName, '__', SubsetName)) like 'ADMIN__{dataset.upper()}%'
    order by registry_subset_name,  sort_order"""

    df = read_sql(qry, 'ingestion_prod')
    df['client'] = df['client'].str.lower()
    df['fieldname'] = df['fieldname'].str.lower()
    df['mappedfieldname'] = df['mappedfieldname'].str.lower()

    df2 = read_sql(qry2, 'ingestion_prod')
    df2['client_field_name'] = df2['client_field_name'].str.lower()
    df2['field_name'] = df2['field_name'].str.lower()
    df2['client'] = df2['client_name'].str.lower()

    # join df and df2
    df = df.merge(df2, how='inner', left_on=['client', 'fieldname', 'mappedfieldname'],
                  right_on=['client', 'client_field_name', 'field_name'])

    # retain only df columns
    df = df[['client', 'fieldname', 'mappedfieldname', 'registry_subset_name']]

    # df3 where client is missing
    df3 = df2[df2['client'].isna()].drop_duplicates()
    df3 = df3[~df3['client_field_name'].isin(['', 'tenantid', 'clientfileid'])]

    # rename fields in df3 as per df
    df3 = df3.rename(columns={'client_field_name': 'fieldname', 'field_name': 'mappedfieldname'})
    convert_yaml(df3, client='global', dataset=dataset)

    for client, group in df.groupby('client'):
        convert_yaml(group, client, dataset=dataset)


def get_biome_schema(dataset):
    load_dotenv("env_41.dev")
    qry = f"""select distinct TABLE_SCHEMA, COLUMN_NAME, COLUMN_TYPE from information_schema.columns
     where table_name like '%{dataset}%' and Table_schema like '%master%'"""

    df = read_sql(qry, 'db_earass')
    df['COLUMN_NAME'] = df['COLUMN_NAME'].str.lower()
    cols = df.groupby('TABLE_SCHEMA')['COLUMN_NAME'].apply(set)
    common_cols = reduce(set.intersection, cols)

    schema = []
    for col in common_cols:
        schema.append({
            'Field': col,
            'Dtype': df[df['COLUMN_NAME'] == col]['COLUMN_TYPE'].values[0],
            'Role': '',
            'PHI': False
        })

    with open(f'ingestion/config/{dataset}.yaml', 'w') as f:
        yaml.dump({
            'version': '1.0',
            'biome_schema': schema
        }, f)


if __name__ == '__main__':
    get_client_schema('diagnosis')
