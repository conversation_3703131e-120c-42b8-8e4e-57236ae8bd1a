"""
This script is used to build the biome version of the dictionary for the admin dataset.
"""

from hashlib import blake2b

import numpy as np

from ingestion.config import read_config
from ingestion.data_dictionaries_json import write_data_dict, read_data_dict


def blake_encode(value):
    h = blake2b(digest_size=4)
    h.update(str(value).encode('ascii'))
    h2int = int(h.hexdigest(), 16)
    return h2int


def build_dict(dataset, dataset_id, write=False):
    biome_schema = read_config(dataset=dataset, config='biome_schema').rename(
        columns={'Dtype': 'DB Data Type', 'Field': 'Short Name'}).drop_duplicates()
    biome_schema['Element Reference'] = biome_schema['Short Name'].str.lower().apply(blake_encode)
    version = read_config(dataset=dataset, config='version', as_df=False)

    columns = ['Element Reference', 'Short Name', 'DB Data Type', 'Role', 'PHI']
    elements = biome_schema[columns]
    elements['Definition'] = [
        {
            "Title": "",
            "Definition": "",
            "Source": "Admin",
            "Display Order": 1
        } for _ in range(len(elements))
    ]
    elements['Missing Data'] = 'Illegal'
    elements['Selection Type'] = 'Single'
    elements['Selections'] = [
        [
            {
                "Name": "",
                "Selection Name": "",
                "Selection Definition": "",
                "Display Order": 1
            }
        ] for _ in range(len(elements))
    ]
    elements['Precision'] = None
    elements['Parent Child Validations'] = None
    elements['Ranges'] = None
    elements['Name'] = elements['Short Name']
    elements['Is Identifier'] = np.where(elements['Role'].str.contains('identifier', case=False), 'Yes', 'No')
    elements['Is Base Element'] = 'Yes'
    elements['Is Followup Element'] = 'No'

    elements = elements.to_dict(orient='records')

    data = {
        'Dictionary': {
            'Dataset': {
                'Code': dataset,
                'Id': dataset_id
            },
            'Version': str(version),
            'Type': 'Biome',
            'Sections': [
                {
                    "Container Class": "defaultContainer",
                    "Parent Section": "Root",
                    "Section Display Name": "Default",
                    "Section Code": "DEFAULT",
                    "Section Type": "Section",
                    "Cardinality": "1..1",
                    "Table": "RAW",
                    "Elements": elements
                }
            ]
        }
    }
    if write:
        write_data_dict(f'biome_{dataset}.json', data)


def modify_dict(dataset):
    """
    Modifying already existing dictionary json file to add mandatory fields
    """
    data_dict = read_data_dict(f'biome_{dataset}.json')

    elements = data_dict['Dictionary']['Sections'][0]['Elements']

    for element in elements:
        element['Name'] = element['Short Name']
        element['Is Identifier'] = 'Yes' if 'identifier' in str(element['Role']).lower() else 'No'
        element['Is Base Element'] = 'Yes'
        element['Is Followup Element'] = 'No'

    data_dict['Dictionary']['Sections'][0]['Elements'] = elements

    write_data_dict(f'biome_{dataset}.json', data_dict)


if __name__ == '__main__':
    """
    Note:

    1. Fill some of the attributes manually in the dictionary json
    """
    build_dict(dataset='cpt', dataset_id=179, write=True)

    # modify_dict(dataset='cost')
