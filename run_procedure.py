from dotenv import load_dotenv
import pandas as pd

from ingestion.schema.procedure import SchemaProcedure

from ingestion.readers.procedure import ReaderProcedure

from ingestion.translators.translate_procedure_to_biome import TranslateProcedureToBiome

vars_loaded = load_dotenv("env.dev")

if __name__ == '__main__':
    target_tables = []
    schema = SchemaProcedure(rebuild_schema=True, target_db='DB_EARASS', export_schema=False)
    schema.execute()

    files = pd.read_csv('procedure_files.csv')
    files = files[files['client'].str.lower() != 'commonspirit']  # oshpd issue
    files = files[files['client'].str.lower() != 'trinity']  # oshpd issue

    # files = files[files['client'].str.lower() == 'montefiore']

    # files = files[files['id'] == 54527]

    for ind, row in files.iterrows():
        filename = row['filename'] + '.' + row['filetype'].lower()
        client = row['client']
        file_id = row['id']

        file_dir = f'phi/client_files/{client}/'
        file = f'{file_dir}{filename}'
        write_data = True
        target_db = 'DB_EARASS'
        #

        print(f"Reading file: {file}, index: {ind}")

        reader = ReaderProcedure(filepath=file, schema=schema, write=write_data, client=client,
                                 file_id=file_id, target_db=target_db)
        reader.execute()

        print(f"Translating file: {file}, index: {ind}")

        translate = TranslateProcedureToBiome(filepath=file, schema=schema, reader=reader, file_id=file_id,
                                              client=client, write=write_data, target_db=target_db)
        translate.execute()

        target_tables.append(
            {
                'client': client,
                'file_id': file_id,
                'target_table': translate.target_table,
                'target_db': target_db,
                'file': filename
            }
        )

        # write to csv
        # target_tables_df = pd.DataFrame(target_tables)
        # target_tables_df.to_csv('new_tables_procedure_3.csv', index=False)
