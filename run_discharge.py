from dotenv import load_dotenv

from ingestion.adaptor.discharge import Discharge


vars_loaded = load_dotenv(".env.dev")
if not vars_loaded:
    print("Unable to load .env.dev file")
    exit(1)


if __name__ == '__main__':
    file = ('/Users/<USER>/code/ingestion3/<EMAIL>'
            '__UCSF__UCSF__Biome - Admission Encounter Data CY25 Q1.xlsx')
    client = 'UCSF'
    write_data = True
    file_id = 60353

    dischage = Discharge(filepath=file, write=write_data, client=client, rebuild_schema=True, client_file_id=file_id,
                         db_as_source=True, testing=True)

    # run any of the below methods as per the requirement if only specific task is required
    # dischage.build()  # This will create the schema only, run this if only schema is required
    # dischage.read()  # This will read the data from the file and insert in the clien_raw db
    dischage.translate()  # This will translate the data and write data as a single table as per biome schema

    # use the below to run all the above methods in sequence.
    # dischage.run()  # This will run all the above methods in sequence
