from dotenv import load_dotenv

from ingestion.adaptor.laao import LAAOFU14

vars_loaded = load_dotenv("env.dev")
if not vars_loaded:
    print("Unable to load .env.dev file")
    exit(1)

if __name__ == '__main__':
    file = ''
    client = 'rwj'
    write_data = True
    file_id = 50880

    laao = LAAOFU14(filepath=file, write=write_data, client=client, rebuild_schema=True, client_file_id=file_id,
                    db_as_ncdr_source=True, testing=True, export_schema=True)

    # run any of the below methods as per the requirement if only specific task is required
    laao.build()  # This will create the schema only, run this if only schema is required
    # laao.read()  # This will read the xml data from the file and insert in the tables using ncdr schema
    # laao.translate()  # This will translate the data and write data as a single table as per biome schema

    # use the below to run all the above methods in sequence.
    # This will build the ncdr schema, read the xml data from the file, and translate the data as per biome schema
    # laao.run()  # This will run all the above methods in sequence
