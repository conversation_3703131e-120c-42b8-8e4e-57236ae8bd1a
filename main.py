import argparse
from datetime import datetime
import logging
import os
import pytz
from dotenv import load_dotenv
from typing import Optional

from ingestion.utils import events
from ingestion.utils.file import File
from ingestion.utils.common import get_code_version, getenv
from ingestion.utils.exceptions import RequiredFieldsError


logging.basicConfig(
    format='%(asctime)s %(message)s',
    datefmt='%m/%d/%Y %I:%M:%S %p'
)
logging.root.setLevel(logging.INFO)

# all executions variables
DEFAULT_INPUT_FILE_ENV_VAR = "INGESTION_INPUT_FILE"
DEFAULT_OUTPUT_DIR_ENV_VAR = "OUTPUT_DIR"
CLIENT_ENV_VAR = "CLIENT"
DEFAULT_CLIENT_VALUE = "biome"
FILE_ID_ENV_VAR = "FILE_ID"

# event data variables
TIMESTAMP_FORMAT = "%Y%m%d-%H%M%S"
ROOT_TRIGGER_EVENT_ID_KEY = "ROOT_TRIGGER_EVENT_ID"
ROOT_TRIGGER_EVENT_ID_DEFAULT = "manual"
TRIGGER_EVENT_ID_KEY = "TRIGGER_EVENT_ID"
TRIGGER_EVENT_ID_DEFAULT = "manual"
EVENT_TOPIC_ENDPOINT_KEY = "EVENT_TOPIC_ENDPOINT"
EVENT_TOPIC_ENDPOINT_DEFAULT = (
    "https://ingestion-stg.eastus-1.eventgrid.azure.net/api/events"
)


def init_argparse():
    parser = argparse.ArgumentParser(
        usage="%(prog)s --input-file [INPUT_FILE] --output-dir [OUTPUT_DIR] --client [CLIENT]",
        description="Processes a file through Ingestion to load the data into a database. "
    )
    parser.add_argument(
        "--input-file",
        type=str,
        help=("Input file. If not specified, "
              f"reads from {DEFAULT_INPUT_FILE_ENV_VAR} environment variable."),
    )
    parser.add_argument(
        "--output-dir",
        type=str,
        help=("Output directory. If not specified, "
              f"reads from {DEFAULT_OUTPUT_DIR_ENV_VAR} environment variable."),
    )
    parser.add_argument(
        "--client",
        type=str,
        help=("Client who provided the file. If not specified, "
              f"will attempt to read from file name or use default value {DEFAULT_CLIENT_VALUE}."),
    )
    return parser


def schema_gen(file: File, client: str, dataset_type: Optional[str] = None):
    if not dataset_type:
        dataset_type = file.get_dataset_type()
    logging.info(f"Rebuilding NCDR Schema for {dataset_type}")
    rebuild_schema = True

    if not dataset_type:
        logging.error(f"Unable to determine the DSM for the input file: {file.file_name}")
        exit(1)
    elif dataset_type == 'ACC-NCDR-CathPCI-5.0':
        from ingestion.adaptor.cathpci import CathPCI5
        pci = CathPCI5(client=client, rebuild_schema=rebuild_schema)
        pci.build()
    elif dataset_type == 'ACC-NCDR-CPMI-3.0':
        from ingestion.adaptor.cpmi import CPMI30
        cpmi = CPMI30(client=client, rebuild_schema=rebuild_schema)
        cpmi.build()
    elif dataset_type == 'ACC-NCDR-CPMI-3.1':
        from ingestion.adaptor.cpmi import CPMI31
        cpmi = CPMI31(client=client, rebuild_schema=rebuild_schema)
        cpmi.build()
    elif dataset_type == 'ACC-NCDR-LAAO-1.4':
        from ingestion.adaptor.laao import LAAO14
        laao = LAAO14(client=client, rebuild_schema=rebuild_schema)
        laao.build()
    elif dataset_type == 'ACC-NCDR-LAAO-1.4-FUP':
        from ingestion.adaptor.laao import LAAOFU14
        laaofu = LAAOFU14(client=client, rebuild_schema=rebuild_schema)
        laaofu.build()
    elif dataset_type == 'ACC-NCDR-ICD-2.3':
        from ingestion.adaptor.icd import ICD23
        icd = ICD23(client=client, rebuild_schema=rebuild_schema)
        icd.build()
    elif dataset_type == 'ACC-NCDR-ICD-3.0':
        from ingestion.adaptor.icd import ICD30
        icd = ICD30(client=client, rebuild_schema=rebuild_schema)
        icd.build()
    elif dataset_type == 'ACC-NCDR-TVT-3.0':
        from ingestion.adaptor.tvt import TVT30
        tvt = TVT30(client=client, rebuild_schema=rebuild_schema)
        tvt.build()
    elif dataset_type == 'ACC-NCDR-TVT-3.0-FUP':
        from ingestion.adaptor.tvt import TVTFU30
        tvt = TVTFU30(client=client, rebuild_schema=rebuild_schema)
        tvt.build()
    elif dataset_type == 'ACC-NCDR-AFib-1.0':
        from ingestion.adaptor.afib import AFib10
        afib = AFib10(client=client, rebuild_schema=rebuild_schema)
        afib.build()
    elif dataset_type == 'ACC-NCDR-AFib-2.0':
        from ingestion.adaptor.afib import AFib20
        afib = AFib20(client=client, rebuild_schema=rebuild_schema)
        afib.build()
    elif dataset_type == 'CSTK__DEFAULT':
        from ingestion.adaptor.cstk import CSTK
        cstk = CSTK(client=client, rebuild_schema=rebuild_schema)
        cstk.build()
    elif 'ADMIN__DISCHARGES' in dataset_type or 'ADMIN__DISCHARGES_E' in dataset_type:
        from ingestion.adaptor.discharge import Discharge
        discharge = Discharge(client=client, rebuild_schema=rebuild_schema)
        discharge.build()
    elif 'ADMIN__DIAGNOSIS' in dataset_type:
        from ingestion.adaptor.diagnosis import Diagnosis
        diagnosis = Diagnosis(client=client, rebuild_schema=rebuild_schema)
        diagnosis.build()
    elif 'ADMIN__PROCEDURE' in dataset_type:
        from ingestion.adaptor.procedure import Procedure
        procedure = Procedure(client=client, rebuild_schema=rebuild_schema)
        procedure.build()
    elif 'ADMIN__CPT' in dataset_type:
        from ingestion.adaptor.cpt import Cpt
        cpt = Cpt(client=client, rebuild_schema=rebuild_schema)
        cpt.build()
    elif dataset_type == 'STROKE__DEFAULT':
        from ingestion.adaptor.stroke import Stroke
        stroke = Stroke(client=client, rebuild_schema=rebuild_schema)
        stroke.build()
    else:
        logging.error(f"DSM {dataset_type} not supported.")
        exit(1)  # TODO: change exit code for AKS handling


def preprocessor(file: File):
    pass


def ingestion(file: File, file_id: str, client: str, dataset_type: Optional[str] = None):
    write_data = True

    if not dataset_type:
        dataset_type = file.get_dataset_type()

    if not dataset_type:
        logging.error(f"Unable to determine the dataset type for the input file: {file.file_name}")
        exit(1)
    elif dataset_type == 'ACC-NCDR-CathPCI-5.0':
        from ingestion.adaptor.cathpci import CathPCI5
        pci = CathPCI5(filepath=file.local_path, client_file_id=file_id, client=client, write=write_data)
        pci.read()
        return pci
    elif dataset_type == 'ACC-NCDR-CPMI-3.0':
        from ingestion.adaptor.cpmi import CPMI30
        cpmi = CPMI30(filepath=file.local_path, client_file_id=file_id, client=client, write=write_data)
        cpmi.read()
        return cpmi
    elif dataset_type == 'ACC-NCDR-CPMI-3.1':
        from ingestion.adaptor.cpmi import CPMI31
        cpmi = CPMI31(filepath=file.local_path, client_file_id=file_id, client=client, write=write_data)
        cpmi.read()
        return cpmi
    elif dataset_type == 'ACC-NCDR-LAAO-1.4':
        from ingestion.adaptor.laao import LAAO14
        laao = LAAO14(filepath=file.local_path, client_file_id=file_id, client=client, write=write_data)
        laao.read()
        return laao
    elif dataset_type == 'ACC-NCDR-LAAO-1.4-FUP':
        from ingestion.adaptor.laao import LAAOFU14
        laaofu = LAAOFU14(filepath=file.local_path, client_file_id=file_id, client=client, write=write_data)
        laaofu.read()
        return laaofu
    elif dataset_type == 'ACC-NCDR-ICD-2.3':
        from ingestion.adaptor.icd import ICD23
        icd = ICD23(filepath=file.local_path, client_file_id=file_id, client=client, write=write_data)
        icd.read()
        return icd
    elif dataset_type == 'ACC-NCDR-ICD-3.0':
        from ingestion.adaptor.icd import ICD30
        icd = ICD30(filepath=file.local_path, client_file_id=file_id, client=client, write=write_data)
        icd.read()
        return icd
    elif dataset_type == 'ACC-NCDR-TVT-3.0':
        from ingestion.adaptor.tvt import TVT30
        tvt = TVT30(filepath=file.local_path, client_file_id=file_id, client=client, write=write_data)
        tvt.read()
        return tvt
    elif dataset_type == 'ACC-NCDR-TVT-3.0-FUP':
        from ingestion.adaptor.tvt import TVTFU30
        tvt = TVTFU30(filepath=file.local_path, client_file_id=file_id, client=client, write=write_data)
        tvt.read()
        return tvt
    elif dataset_type == 'ACC-NCDR-AFib-1.0':
        from ingestion.adaptor.afib import AFib10
        afib = AFib10(filepath=file.local_path, client_file_id=file_id, client=client, write=write_data)
        afib.read()
        return afib
    elif dataset_type == 'ACC-NCDR-AFib-2.0':
        from ingestion.adaptor.afib import AFib20
        afib = AFib20(filepath=file.local_path, client_file_id=file_id, client=client, write=write_data)
        afib.read()
        return afib
    elif dataset_type == 'CSTK__DEFAULT':
        from ingestion.adaptor.cstk import CSTK
        cstk = CSTK(filepath=file.local_path, client_file_id=file_id, client=client, write=write_data)
        cstk.read()
        return cstk
    elif 'ADMIN__COST' in dataset_type or 'ADMIN__COST_COMPACT' in dataset_type:
        from ingestion.adaptor.cost import Cost
        cost = Cost(filepath=file.local_path, client_file_id=file_id, client=client, write=write_data)
        cost.read()
        return cost
    elif 'ADMIN__DISCHARGES' in dataset_type or 'ADMIN__DISCHARGES_E' in dataset_type:
        from ingestion.adaptor.discharge import Discharge
        discharge = Discharge(filepath=file.local_path, client_file_id=file_id, client=client, write=write_data)
        discharge.read()
        return discharge
    elif 'ADMIN__DIAGNOSIS' in dataset_type:
        from ingestion.adaptor.diagnosis import Diagnosis
        diagnosis = Diagnosis(filepath=file.local_path, client_file_id=file_id, client=client, write=write_data)
        diagnosis.read()
        return diagnosis
    elif 'ADMIN__PROCEDURE' in dataset_type:
        from ingestion.adaptor.procedure import Procedure
        procedure = Procedure(filepath=file.local_path, client_file_id=file_id, client=client, write=write_data)
        procedure.read()
        return procedure
    elif 'ADMIN__CPT' in dataset_type:
        from ingestion.adaptor.cpt import Cpt
        cpt = Cpt(filepath=file.local_path, client_file_id=file_id, client=client, write=write_data)
        cpt.read()
        return cpt
    elif dataset_type == 'STROKE__DEFAULT':
        from ingestion.adaptor.stroke import Stroke
        stroke = Stroke(filepath=file.local_path, client_file_id=file_id, client=client, write=write_data)
        stroke.read()
        return stroke
    else:
        logging.error(f"DSM {file.dataset_type} not supported.")
        exit(1)  # TODO: change exit code for AKS handling


def ncdr_biome_translator(file_id: str, client: str, dataset_type: Optional[str] = None):
    write_data = True

    if not dataset_type:
        logging.error(f"Unable to determine the dataset type for the input file: {file.file_name}")
        exit(1)
    elif dataset_type == 'ACC-NCDR-CathPCI-5.0':
        from ingestion.adaptor.cathpci import CathPCI5
        pci = CathPCI5(client_file_id=file_id, write=write_data, client=client, db_as_ncdr_source=True)
        pci.translate()
        return pci
    elif dataset_type == 'ACC-NCDR-CPMI-3.0':
        from ingestion.adaptor.cpmi import CPMI30
        cpmi = CPMI30(client_file_id=file_id, write=write_data, client=client, db_as_ncdr_source=True)
        cpmi.translate()
        return cpmi
    elif dataset_type == 'ACC-NCDR-CPMI-3.1':
        from ingestion.adaptor.cpmi import CPMI31
        cpmi = CPMI31(client_file_id=file_id, write=write_data, client=client, db_as_ncdr_source=True)
        cpmi.translate()
        return cpmi
    elif dataset_type == 'ACC-NCDR-LAAO-1.4':
        from ingestion.adaptor.laao import LAAO14
        laao = LAAO14(client_file_id=file_id, write=write_data, client=client, db_as_ncdr_source=True)
        laao.translate()
        return laao
    elif dataset_type == 'ACC-NCDR-LAAO-1.4-FUP':
        from ingestion.adaptor.laao import LAAOFU14
        laaofu = LAAOFU14(client_file_id=file_id, write=write_data, client=client, db_as_ncdr_source=True)
        laaofu.translate()
        return laaofu
    elif dataset_type == 'ACC-NCDR-ICD-2.3':
        from ingestion.adaptor.icd import ICD23
        icd = ICD23(client_file_id=file_id, write=write_data, client=client, db_as_ncdr_source=True)
        icd.translate()
        return icd
    elif dataset_type == 'ACC-NCDR-ICD-3.0':
        from ingestion.adaptor.icd import ICD30
        icd = ICD30(client_file_id=file_id, write=write_data, client=client, db_as_ncdr_source=True)
        icd.translate()
        return icd
    elif dataset_type == 'ACC-NCDR-TVT-3.0':
        from ingestion.adaptor.tvt import TVT30
        tvt = TVT30(client_file_id=file_id, write=write_data, client=client, db_as_ncdr_source=True)
        tvt.translate()
        return tvt
    elif dataset_type == 'ACC-NCDR-TVT-3.0-FUP':
        from ingestion.adaptor.tvt import TVTFU30
        tvt = TVTFU30(client_file_id=file_id, write=write_data, client=client, db_as_ncdr_source=True)
        tvt.translate()
        return tvt
    elif dataset_type == 'ACC-NCDR-AFib-1.0':
        from ingestion.adaptor.afib import AFib10
        afib = AFib10(client_file_id=file_id, write=write_data, client=client, db_as_ncdr_source=True)
        afib.translate()
        return afib
    elif dataset_type == 'ACC-NCDR-AFib-2.0':
        from ingestion.adaptor.afib import AFib20
        afib = AFib20(client_file_id=file_id, write=write_data, client=client, db_as_ncdr_source=True)
        afib.translate()
        return afib
    elif dataset_type == 'CSTK__DEFAULT':
        from ingestion.adaptor.cstk import CSTK
        cstk = CSTK(client_file_id=file_id, write=write_data, client=client, db_as_ncdr_source=True)
        cstk.translate()
        return cstk
    elif 'ADMIN__COST' in dataset_type or 'ADMIN__COST_COMPACT' in dataset_type:
        from ingestion.adaptor.cost import Cost
        cost = Cost(client_file_id=file_id, write=write_data, client=client, db_as_source=True)
        cost.translate()
        return cost
    elif 'ADMIN__DISCHARGES' in dataset_type or 'ADMIN__DISCHARGES_E' in dataset_type:
        from ingestion.adaptor.discharge import Discharge
        discharge = Discharge(client_file_id=file_id, write=write_data, client=client, db_as_source=True)
        discharge.translate()
        return discharge
    elif 'ADMIN__DIAGNOSIS' in dataset_type:
        from ingestion.adaptor.diagnosis import Diagnosis
        diagnosis = Diagnosis(client_file_id=file_id, write=write_data, client=client, db_as_source=True)
        diagnosis.translate()
        return diagnosis
    elif 'ADMIN__PROCEDURE' in dataset_type:
        from ingestion.adaptor.procedure import Procedure
        procedure = Procedure(client_file_id=file_id, write=write_data, client=client, db_as_source=True)
        procedure.translate()
        return procedure
    elif 'ADMIN__CPT' in dataset_type:
        from ingestion.adaptor.cpt import Cpt
        cpt = Cpt(client_file_id=file_id, write=write_data, client=client, db_as_source=True)
        cpt.translate()
        return cpt
    elif dataset_type == 'STROKE__DEFAULT':
        from ingestion.adaptor.stroke import Stroke
        stroke = Stroke(client_file_id=file_id, write=write_data, client=client, db_as_ncdr_source=True)
        stroke.translate()
        return stroke
    else:
        logging.error(f"Dataset type {dataset_type} not supported.")
        exit(1)  # TODO: could be a return something else instead of exit


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    logging.getLogger("azure").setLevel(logging.ERROR)
    logging.getLogger("urllib3").setLevel(logging.ERROR)
    logging.getLogger('msal').setLevel(logging.ERROR)  # verbose Azure authentication logging
    parser = init_argparse()
    args = parser.parse_args()
    input_file = args.input_file
    output_dir = args.output_dir
    client = args.client

    _env = getenv("ENVIRONMENT", "dev")
    if _env.lower() not in ["stg", "prod"]:
        logging.info(f"Invalid environment: {_env}. Setting to 'dev'")
        loaded_vars = load_dotenv(".env.dev")
        if not loaded_vars:
            logging.error("Environment not set correctly for production or staging and "
                          "unable to load .env.dev file. To continue edit the sample local "
                          "file in .env.sample as outlined in the README.")
            exit(1)

    if not input_file:
        input_file = getenv(DEFAULT_INPUT_FILE_ENV_VAR)
        logging.info(f"Reading input-file from env {DEFAULT_INPUT_FILE_ENV_VAR}: {input_file}")
    else:
        logging.info(f"Input file: {input_file}")
    if not output_dir:
        output_dir = getenv(DEFAULT_OUTPUT_DIR_ENV_VAR, ".")
        logging.info(f"Reading output-dir from env {DEFAULT_OUTPUT_DIR_ENV_VAR}: {output_dir}.")
    if not client:
        try:
            client = os.path.basename(input_file).split("__")[2]
            logging.info(
                f"Client not specified. Assuming the client based on input_file: {client}"
            )
        except Exception as e:
            client = DEFAULT_CLIENT_VALUE
            logging.info(
                "Client not specified, and unable to be read from "
                f"file name with error {e}. Using default value: {client}"
            )
    logging.info(f"Input file: {input_file}")
    logging.info(f"Output dir: {output_dir}")
    logging.info(f"Client: {client}")

    if not input_file and not output_dir and not client:
        logging.error("Required inputs not provided. Exiting.")
        exit(1)  # TODO: edit for AKS handling

    env_event_publishing = getenv("PUBLISH_EVENTS", "no")
    publish_events = str(env_event_publishing).lower() in ["1", "true", "yes", "y"]
    file_id = getenv(FILE_ID_ENV_VAR)

    # Publishing startup event
    trigger_event_id = getenv(TRIGGER_EVENT_ID_KEY, TRIGGER_EVENT_ID_DEFAULT)
    root_trigger_event_id = getenv(ROOT_TRIGGER_EVENT_ID_KEY, ROOT_TRIGGER_EVENT_ID_DEFAULT)
    event_topic_endpoint = getenv(EVENT_TOPIC_ENDPOINT_KEY, EVENT_TOPIC_ENDPOINT_DEFAULT)
    code_version = get_code_version()
    pod_name = getenv("PODNAME", "unknown")
    BPN_TOKEN_KEY = getenv("BPN_TOKEN_KEY", "assimilation-bpn-token")
    db_host = getenv("DB_HOST")
    dataset_type = getenv("DATASET_TYPE")

    # stage must be in (schema-gen, preprocessor, ingestion, ncdr-biome-translator)
    stage = getenv("STAGE")
    logging.info(f"Executing code for stage: {stage}")
    if stage.lower() == 'dictionary-kb-extract':
        pass
    elif stage.lower() == 'schema-gen':
        file = File(path=input_file, file_id=file_id)
        local_file = file.download()
        schema_gen(file, client)
    elif stage.lower() == "preprocessor":
        file = File(path=input_file, file_id=file_id)
        local_file = file.download()
        preprocessor(file)
    if stage.lower() == "ingestion":
        file = File(path=input_file, file_id=file_id)
        local_file = file.download()
        logging.info(f"Beginning Ingestion for {file.file_name}")

        start_event_type = "baseSchemaLoad.start"
        base_event_data = {
            "input_file": input_file,
            "output_dir": output_dir,
            "client": client,
            "root_trigger_id": root_trigger_event_id,
            "trigger_id": trigger_event_id,
            "processing_timestamp": datetime.now(pytz.timezone("US/Eastern")).strftime(TIMESTAMP_FORMAT),
            "code_version": code_version,
            "hostname": pod_name,
            "db_host": db_host,
            "client_file_id": file_id,
        }
        event = events.build_event(input_file, base_event_data, start_event_type,
                                   event_time=datetime.now(pytz.timezone("US/Eastern")))
        events.publish_event(publish_events, event_topic_endpoint, event)

        try:
            if not dataset_type:
                dataset_type = file.get_dataset_type()
                logging.info(f"File dataset type: {dataset_type}")
            ing = ingestion(file, file_id, client, dataset_type)
            reader_metadata = ing.reader.file_info
            base_event_data['dataframes'] = reader_metadata['dataframes']
            base_event_data['num_patients'] = reader_metadata['num_patients']
            base_event_data['client_file_id'] = reader_metadata['id']
            base_event_data['dataset_id'] = ing.reader.dataset_id
            # Required single dataset_type for translator job env var and we only support a single dataset type
            # per file, not single file can have more than one dataset type
            base_event_data['dataset_type'] = dataset_type[0] if isinstance(dataset_type, list) else dataset_type
            if hasattr(ing.reader, 'additional_info'):
                base_event_data['additional_fields_info'] = ing.reader.additional_info
            success_event_type = "baseSchemaLoad.success"
            event = events.build_event(input_file, base_event_data, success_event_type,
                                       event_time=datetime.now(pytz.timezone("US/Eastern")))
            events.publish_event(publish_events, event_topic_endpoint, event)
            logging.info(f"Ingestion complete for {file.file_name}")

        except RequiredFieldsError as e:
            logging.error(e)
            failure_event_type = "baseSchemaLoad.failure"
            base_event_data['exception'] = str(e)
            event = events.build_event(input_file, base_event_data, failure_event_type,
                                       event_time=datetime.now(pytz.timezone("US/Eastern")))
            events.publish_event(publish_events, event_topic_endpoint, event)
            raise

        except Exception as e:
            logging.error(f'Error in ingestion: {e}')
            failure_event_type = "baseSchemaLoad.failure"
            base_event_data['exception'] = str(e)
            event = events.build_event(input_file, base_event_data, failure_event_type,
                                       event_time=datetime.now(pytz.timezone("US/Eastern")))
            events.publish_event(publish_events, event_topic_endpoint, event)
            raise

    if stage.lower() == "ncdr-biome-translator":
        logging.info(f"Beginning NCDR to Biome Translation for {file_id}")
        start_event_type = "baseBiomeTranslate.start"
        base_event_data = {
            "client": client,
            "root_trigger_id": root_trigger_event_id,
            "trigger_id": trigger_event_id,
            "processing_timestamp": datetime.now(pytz.timezone("US/Eastern")).strftime(TIMESTAMP_FORMAT),
            "code_version": code_version,
            "hostname": pod_name,
            "db_host": db_host,
            'input_schema': dataset_type,
            'output_schema': 'biome',
            'client_file_id': file_id,
        }
        event = events.build_event(input_file, base_event_data, start_event_type,
                                   event_time=datetime.now(pytz.timezone("US/Eastern")))
        events.publish_event(publish_events, event_topic_endpoint, event)

        try:
            if not dataset_type:
                raise ValueError("Dataset type not provided.")
            tr = ncdr_biome_translator(file_id, client, dataset_type)
            translator_metadata = tr.translator.file_info
            base_event_data['dataframes'] = [
                {
                    'name': translator_metadata['table_name'],
                    'row_count': translator_metadata['row_count'],
                    'data_start_date': translator_metadata['data_start_date'],
                    'data_end_date': translator_metadata['data_end_date'],
                    'entity': translator_metadata['hospital'],
                    'registry_subset': translator_metadata['registry_subset'],
                }
            ]
            base_event_data['dataset_id'] = tr.translator.dataset_id
            base_event_data['dataset_type'] = dataset_type
            success_event_type = "baseBiomeTranslate.success"
            event = events.build_event(input_file, base_event_data, success_event_type,
                                       event_time=datetime.now(pytz.timezone("US/Eastern")))
            events.publish_event(publish_events, event_topic_endpoint, event)
            logging.info(f"Translation complete for {file_id}")

        except Exception as e:
            logging.error(f"Error in Translation: {e}")
            failure_event_type = "baseBiomeTranslate.failure"
            base_event_data['exception'] = str(e)
            event = events.build_event(input_file, base_event_data, failure_event_type,
                                       event_time=datetime.now(pytz.timezone("US/Eastern")))
            events.publish_event(publish_events, event_topic_endpoint, event)
            raise
