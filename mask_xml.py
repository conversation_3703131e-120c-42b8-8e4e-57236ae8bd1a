import xml.etree.ElementTree as ET


def mask_xml(file_path, fields_to_replace, replace_by, output_path):
    """
    Mask xml file by replacing field values with specified string based on the field name or code.
    """
    tree = ET.parse(file_path)
    root = tree.getroot()

    for item in root.findall('patient'):
        if 'ncdrPatientId' in item.attrib:
            item.attrib['ncdrPatientId'] = '12345'
        for element in item.iter('element'):
            display_name = element.get('displayName')
            code = element.get('code')
            if replace_by == 'code':
                for field in fields_to_replace:
                    if code == field['Code']:
                        for value_element in element.iter('value'):
                            value_element.set('value', field['Replace With'])
            else:
                for field in fields_to_replace:
                    if display_name == field['Short Name']:
                        for value_element in element.iter('value'):
                            value_element.set('value', field['Replace With'])

    tree.write(output_path)


if __name__ == '__main__':
    fields_to_replace = [{'Short Name': 'LastName', 'Code': '**********', 'Replace With': ''},
                         {'Short Name': 'FirstName', 'Code': '**********', 'Replace With': ''},
                         {'Short Name': 'MidName', 'Code': '**********', 'Replace With': ''},
                         {'Short Name': 'NCDRPatientID', 'Code': '2.16.840.1.113883.3.3478.4.842', 'Replace With': ''},
                         {'Short Name': 'SSN', 'Code': '2.16.840.1.113883.4.1', 'Replace With': ''},
                         {'Short Name': 'OtherID', 'Code': '2.16.840.1.113883.3.3478.4.843', 'Replace With': ''},
                         {'Short Name': 'DOB', 'Code': '**********', 'Replace With': ''},
                         {'Short Name': 'ZipCode', 'Code': '**********', 'Replace With': ''},
                         {'Short Name': 'ArrivalDateTime', 'Code': '**********', 'Replace With': ''},
                         {'Short Name': 'AdmitDateTime', 'Code': '112000000162', 'Replace With': ''},
                         {'Short Name': 'AdmLName', 'Code': '**********', 'Replace With': ''},
                         {'Short Name': 'AdmFName', 'Code': '**********', 'Replace With': ''},
                         {'Short Name': 'AdmMName', 'Code': '**********', 'Replace With': ''},
                         {'Short Name': 'AttLName', 'Code': '1000142452', 'Replace With': ''},
                         {'Short Name': 'AttFName', 'Code': '1000142452', 'Replace With': ''},
                         {'Short Name': 'AttMName', 'Code': '1000142452', 'Replace With': ''},
                         {'Short Name': 'ProcedureStartDateTime', 'Code': '1000142460', 'Replace With': ''},
                         {'Short Name': 'ProcedureEndDateTime', 'Code': '1000142459', 'Replace With': ''},
                         {'Short Name': 'DCathLName', 'Code': '1000142454', 'Replace With': ''},
                         {'Short Name': 'DCathFName', 'Code': '1000142454', 'Replace With': ''},
                         {'Short Name': 'DCathMName', 'Code': '1000142454', 'Replace With': ''},
                         {'Short Name': 'PCILName', 'Code': '1000142455', 'Replace With': ''},
                         {'Short Name': 'PCIFName', 'Code': '1000142455', 'Replace With': ''},
                         {'Short Name': 'PCIMName', 'Code': '1000142455', 'Replace With': ''},
                         {'Short Name': 'DCDateTime', 'Code': '1000142457', 'Replace With': ''},
                         {'Short Name': 'DCLName', 'Code': '1000142453', 'Replace With': ''},
                         {'Short Name': 'DCFName', 'Code': '1000142453', 'Replace With': ''},
                         {'Short Name': 'DCMName', 'Code': '1000142453', 'Replace With': ''}]
    xml_file_path = 'phi/client_files/20231113__kbeltz__Trinity__Trinity__CPMI538639-2023Q1.xml'

    mask_xml(xml_file_path, fields_to_replace, replace_by='code',
             output_path='20231113__kbeltz__Trinity__Trinity__CPMI538639-2023Q1_masked.xml')
