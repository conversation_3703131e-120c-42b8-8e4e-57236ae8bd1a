"""
This script is used to build the Biome data dictionary for NCDR datasets.
"""

import json
from hashlib import blake2b
import pandas as pd
import numpy as np

from ingestion.config import read_config
from ingestion.data_dictionaries import read_data_dict
from ingestion.data_dictionaries_json import write_data_dict
from ingestion.schema.schema_gen import create_grouped_list


def blake_encode(value):
    h = blake2b(digest_size=4)
    h.update(str(value).encode('ascii'))
    h2int = int(h.hexdigest(), 16)
    return h2int


def replace_new_elements(parent_child_val, element_ref_map):
    updated = []
    for item in parent_child_val:
        if (item['Parent Element Reference'] in element_ref_map.keys() or item['Parent Element Reference'] in
                element_ref_map.values()):
            item['Parent Element Reference'] = element_ref_map.get(item['Parent Element Reference'],
                                                                   item['Parent Element Reference'])
            updated.append(item)
    return updated


def get_sections(schema_class):
    """
    Get sections from the schema object.
    """
    schema_obj = schema_class()
    schema_obj.execute()
    return schema_obj.sections[['Section Code', 'Table']].drop_duplicates()


def load_config_tables(dataset):
    """
    Load configuration tables for the Biome schema.

    Returns:
        biome_schema: DataFrame with biome schema configuration (with renamed columns and deduplicated).
        rename_fields: DataFrame with field renaming configuration.
        custom_fields: DataFrame with custom field configuration, with an added 'IsCustom' flag.
    """
    biome_schema = (
        read_config(dataset=dataset, config='biome_schema')
        .rename(columns={'Dtype': 'DB Data Type'})
        .drop_duplicates()
    )
    rename_fields = (
        read_config(dataset=dataset, config='rename_fields')
        .rename(columns={'Old Field': 'Field', 'New Field': 'NCDR Field'})
        .drop_duplicates()
    )
    custom_fields = read_config(dataset=dataset, config='custom_fields')[
        ['Field', 'Reference Field', 'Derivative Value', 'Value Map', 'Delimiter', 'Section', 'Derivative Field']
    ]
    custom_fields['IsCustom'] = True
    return biome_schema, rename_fields, custom_fields


def process_ncdr_elements(ncdr_dict_path, sections):
    """
    Process NCDR elements by reading the Elements dictionary and merging with section info.

    This function extracts NCDR elements, creates section elements by merging with the provided sections,
    and sets additional columns to None.
    """
    ncdr_elements = read_data_dict(ncdr_dict_path, 'Elements')
    section_elements = (
        ncdr_elements[['Section Code', 'Element Reference', 'Short Name']]
        .drop_duplicates()
        .merge(sections, on='Section Code', how='left')
        .drop(columns='Section Code')
        .rename(columns={'Table': 'Section'})
    )
    section_elements['Section'] = section_elements['Section'].str.upper()

    # Set extra columns to None for further processing
    for col in ['Section Code', 'Section Display Name', 'Code', 'Code System', 'Code System Name']:
        ncdr_elements[col] = None

    return ncdr_elements, section_elements


def process_ncdr_selections(ncdr_dict_path, section_elements):
    """
    Process NCDR selections by reading the Selections dictionary and merging with section elements.

    Returns:
         ncdr_selections: Grouped selections list.
         ncdr_selections_raw: DataFrame with selections merged with section elements.
    """
    ncdr_selections_raw = read_data_dict(ncdr_dict_path, 'Selections')
    for col in ['Code', 'Code System', 'Code System Name']:
        ncdr_selections_raw[col] = None

    ncdr_selections = create_grouped_list(
        data=ncdr_selections_raw.set_index('Element Reference'),
        name='Selections'
    )
    ncdr_selections_raw = ncdr_selections_raw.merge(section_elements, how='inner', on='Element Reference')
    return ncdr_selections, ncdr_selections_raw


def process_ncdr_definitions(ncdr_dict_path):
    """
    Process NCDR definitions by reading the Supporting Definitions dictionary and grouping them.

    Returns:
        ncdr_definitions: Grouped list of definitions with only the first definition kept.
    """
    ncdr_definitions = read_data_dict(ncdr_dict_path, 'Supporting Definitions').set_index('Element Reference')
    ncdr_definitions = create_grouped_list(data=ncdr_definitions, name='Definition')
    ncdr_definitions['Definition'] = ncdr_definitions['Definition'].str[0]
    return ncdr_definitions


def process_ncdr_parent_child(ncdr_dict_path):
    """
    Process NCDR parent-child validations by reading the Parent Child Validations dictionary and grouping them.
    """
    ncdr_parent_child = read_data_dict(ncdr_dict_path, 'Parent Child Validations').set_index('Element Reference')
    ncdr_parent_child = create_grouped_list(data=ncdr_parent_child, name='Parent Child Validations')
    return ncdr_parent_child


def process_ncdr_ranges(ncdr_dict_path):
    """
    Process NCDR ranges by reading the Element Ranges dictionary and grouping them.
    """
    ncdr_ranges = read_data_dict(ncdr_dict_path, 'Element Ranges').set_index('Element Reference')
    ncdr_ranges = create_grouped_list(data=ncdr_ranges, name='Ranges')
    return ncdr_ranges


def process_custom_fields(custom_fields, ncdr_selections_raw):
    """
    Process custom fields by normalizing and merging with NCDR selections.

    This function converts fields to lowercase, strips derivative values using delimiters,
    merges with selections, groups the custom selections, and cleans up unneeded columns.
    """
    custom_fields['Field'] = custom_fields['Field'].str.lower()
    custom_fields['Derivative Value'] = custom_fields.apply(
        lambda x: x['Derivative Value'].strip(x['Delimiter'])
        if str(x['Delimiter']).lower() not in ['nan', 'none'] else x['Derivative Value'],
        axis=1
    )
    custom_fields['Section'] = custom_fields['Section'].str.upper()
    custom_fields = custom_fields.merge(
        ncdr_selections_raw,
        left_on=['Derivative Value', 'Section', 'Derivative Field'],
        right_on=['Selection Name', 'Section', 'Short Name'],
        how='inner'
    )
    custom_fields = custom_fields.drop_duplicates(subset=['Field']).drop(
        columns=['Section', 'Short Name', 'Derivative Field']
    )
    custom_fields['Selection Name'] = custom_fields.apply(
        lambda x: x['Value Map'].get(
            str(x['Delimiter']) + x['Selection Name'] + str(x['Delimiter'])
            if str(x['Delimiter']).lower() not in ['nan', 'none'] and str(x['Selection Name']).lower() not in ['nan',
                                                                                                               'none']
            else x['Selection Name'],
            x['Selection Name']
        ) if x['Value Map'] else x['Selection Name'],
        axis=1
    )
    custom_fields.drop(columns=['Derivative Value', 'Value Map', 'Delimiter', 'Element Reference'], inplace=True)
    custom_fields = create_grouped_list(
        data=custom_fields.set_index(['Field', 'Reference Field', 'IsCustom']),
        name='Custom Selections'
    ).reset_index()
    custom_fields[['Field', 'Reference Field', 'IsCustom']] = pd.DataFrame(
        custom_fields['index'].tolist(), index=custom_fields.index
    )
    custom_fields.drop(columns=['index'], inplace=True)
    custom_fields['Custom Selections'] = custom_fields['Custom Selections'].apply(
        lambda x: None if str(x[0]['Name']) == 'nan' else x
    )
    return custom_fields


def merge_schema_with_configs(biome_schema, rename_fields, custom_fields):
    """
    Merge the Biome schema with rename and custom field configurations.

    This function ensures that field names are lowercase, merges the rename_fields and custom_fields
    into the biome_schema, fills missing NCDR Field values, and drops unnecessary columns.
    """
    biome_schema['Field'] = biome_schema['Field'].str.lower()
    rename_fields['Field'] = rename_fields['Field'].str.lower()
    biome_schema = biome_schema.merge(rename_fields, on='Field', how='left')
    biome_schema = biome_schema.merge(custom_fields, on='Field', how='left')
    biome_schema['NCDR Field'] = (
        biome_schema['NCDR Field']
        .fillna(biome_schema['Reference Field'])
        .str.lower()
        .fillna(biome_schema['Field'])
    )
    biome_schema = biome_schema.drop(columns=['Reference Field'])
    return biome_schema


def merge_ncdr_elements(biome_schema, ncdr_elements):
    """
    Merge NCDR elements into the Biome schema.

    This function converts the 'Short Name' to lowercase, merges with the biome_schema based on NCDR Field,
    and renames and drops columns as needed.
    """
    ncdr_elements['Short Name'] = ncdr_elements['Short Name'].str.lower()
    biome_schema = biome_schema.merge(
        ncdr_elements,
        left_on='NCDR Field',
        right_on='Short Name',
        how='left'
    )
    biome_schema = biome_schema.drop(columns=['Short Name', 'NCDR Field']).set_index('Element Reference')
    biome_schema.rename(columns={'Field': 'Short Name'}, inplace=True)
    return biome_schema


def merge_ncdr_lists(biome_schema, ncdr_selections, ncdr_definitions, ncdr_parent_child, ncdr_ranges):
    """
    Join additional NCDR lists (selections, definitions, parent-child validations, ranges) to the Biome schema.

    This function performs successive joins and updates the Selections field based on custom conditions,
    then removes temporary columns.
    """
    biome_schema = (
        biome_schema.join(ncdr_selections)
        .join(ncdr_definitions)
        .join(ncdr_parent_child)
        .join(ncdr_ranges)
        .reset_index()
    )
    biome_schema['Selections'] = np.where(
        biome_schema['IsCustom'] == True,
        biome_schema['Custom Selections'],
        biome_schema['Selections']
    )
    biome_schema['Selections'] = np.where(
        biome_schema['Data Type'] == 'CD',
        biome_schema['Selections'],
        None
    )
    biome_schema.drop(columns=['Custom Selections', 'IsCustom'], inplace=True)
    return biome_schema


def finalize_biome_schema(biome_schema):
    """
    Finalize the Biome schema by encoding element references and validating duplicates.

    This function applies a custom encoding function to generate new element references,
    replaces parent-child validation references, and fills default values. It raises an error
    if duplicate element references are found.
    """
    biome_schema['Element Reference New'] = biome_schema['Short Name'].str.lower().apply(blake_encode)
    biome_schema['Element Reference'] = biome_schema['Element Reference'].astype('Int64')
    element_ref_map = biome_schema.set_index('Element Reference')['Element Reference New'].to_dict()
    biome_schema['Element Reference'] = biome_schema['Element Reference New']
    biome_schema.drop(columns=['Element Reference New'], inplace=True)
    biome_schema['Parent Child Validations'] = biome_schema['Parent Child Validations'].apply(
        lambda x: replace_new_elements(x, element_ref_map) if str(x) != 'nan' else x
    )
    if biome_schema['Element Reference'].duplicated().any():
        raise ValueError('Duplicate Element Reference found in Biome Schema. Possibly duplicate field names')
    biome_schema['Name'] = biome_schema['Name'].fillna(biome_schema['Short Name'])
    biome_schema['Selection Type'] = biome_schema['Selection Type'].fillna('Single')
    biome_schema['Default Value'] = biome_schema['Default Value'].fillna('Null')
    return biome_schema


def build_data_structure(biome_schema, dataset_code, dataset_id, version):
    """
    Build the final data dictionary structure.

    Returns a dictionary containing dataset information and the biome schema elements in JSON format.
    """
    elements_json = json.loads(biome_schema.to_json(orient='records'))
    data = {
        'Dictionary': {
            'Dataset': {
                'Code': dataset_code,
                'Id': dataset_id
            },
            'Version': str(version),
            'Type': 'Biome',
            'Sections': [
                {
                    "Container Class": "defaultContainer",
                    "Parent Section": "Root",
                    "Section Display Name": "Default",
                    "Section Code": "DEFAULT",
                    "Section Type": "Section",
                    "Cardinality": "1..1",
                    "Table": "RAW",
                    "Elements": elements_json
                }
            ]
        }
    }
    return data


def build_dict(schema_class, dataset, ncdr_dict_path, dataset_id, dataset_code, version, write=False):
    """
    Build a Biome dictionary from a NCDR dictionary and a Biome schema.

    This function orchestrates the process by invoking helper functions to:
      1. Load and extract schema sections.
      2. Load configuration tables.
      3. Process NCDR elements, selections, definitions, parent-child validations, and ranges.
      4. Process custom fields and merge them with selections.
      5. Merge configuration data with the Biome schema.
      6. Integrate NCDR elements and additional lists.
      7. Finalize the schema (encoding element references and validating).
      8. Build the final data dictionary structure.

    Optionally, if write is True, the dictionary is written to a JSON file.
    """
    # 1. Load the schema sections
    sections = get_sections(schema_class)

    # 2. Load configuration tables
    biome_schema, rename_fields, custom_fields = load_config_tables(dataset)

    # 3. Process NCDR Elements and Selections
    ncdr_elements, section_elements = process_ncdr_elements(ncdr_dict_path, sections)
    ncdr_selections, ncdr_selections_raw = process_ncdr_selections(ncdr_dict_path, section_elements)
    ncdr_definitions = process_ncdr_definitions(ncdr_dict_path)
    ncdr_parent_child = process_ncdr_parent_child(ncdr_dict_path)
    ncdr_ranges = process_ncdr_ranges(ncdr_dict_path)

    # 4. Process and merge custom fields
    custom_fields = process_custom_fields(custom_fields, ncdr_selections_raw)

    # 5. Merge the Biome schema with configuration data
    biome_schema = merge_schema_with_configs(biome_schema, rename_fields, custom_fields)

    # 6. Merge in the NCDR elements and additional lists
    biome_schema = merge_ncdr_elements(biome_schema, ncdr_elements)
    biome_schema = merge_ncdr_lists(biome_schema, ncdr_selections, ncdr_definitions, ncdr_parent_child, ncdr_ranges)

    # 7. Finalize the schema (encoding element references and validating)
    biome_schema = finalize_biome_schema(biome_schema)

    # 8. Build the final dictionary structure
    data = build_data_structure(biome_schema, dataset_code, dataset_id, version)

    # Optionally write the data to a file
    if write:
        write_data_dict(f'biome_{dataset}.json', data)

    return data


if __name__ == '__main__':
    """
    Note:

    1. For CPMI 3.1, update the selections for all insurance fields manually as they do not directly join with the
    ncdr selections

    2. version is the biome version of the dict which is by default the version of ncdr. However, if something changes
    in biome schema, the version should be updated accordingly
    """

    from ingestion.schema.tvt import SchemaTVT30 as schema

    build_dict(schema, 'tvt30', schema.DATA_DICT, dataset_id=schema.DATASET_ID,
               dataset_code=schema.DATASET, version=schema.VERSION, write=True)
