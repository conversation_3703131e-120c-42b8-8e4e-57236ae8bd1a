image: python:3.10-slim

variables:
  ACR_NAME: 'biomecrnew.azurecr.io'
  PROJ_SLUG: 'ingestion3'
  PROJ_VER: '0.11.8'
  CODE_VERSION: "$CI_COMMIT_TAG" # this is the git ta
  COMMIT_HASH: "$CI_COMMIT_SHA"

stages:
  - test
  - staging
  - release


testing source:
  stage: test
  script:
    - apt-get update
    - apt-get install default-jdk -y
    - pip install --extra-index-url $PYPI_URL --trusted-host $PYPI_HOST -r requirements.txt
    - pip install coverage flake8 pytest
    - flake8 --max-line-length=120 --ignore=F841,E712,W605,W504
    - coverage run --source=ingestion --branch -m pytest tests
    - coverage report --fail-under=58
  except:
    - tags


publish staging image:
  stage: staging
  image: kroniak/ssh-client:3.9
  script:
    - mkdir ~/.ssh
    - echo ${KNOWN_HOSTS_AKS_STAG} >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
    - chmod 600 ${ID_RSA_AKS_STAG}
    - eval $(ssh-agent -s)
    - ssh-add ${ID_RSA_AKS_STAG}
    - ssh ubuntu@${VM_AKS_STAG} "rm -r $PROJ_SLUG || true && mkdir $PROJ_SLUG || true"
    - scp -r * ubuntu@${VM_AKS_STAG}:/home/<USER>/$PROJ_SLUG
    - ssh ubuntu@${VM_AKS_STAG} "docker login -u $ACR_USER_NEW -p $ACR_TOKEN_NEW $ACR_NAME_NEW"
    - echo "Adding versioning file"
    - echo ${CODE_VERSION} > $PROJ_SLUG\version.txt
    - echo ${COMMIT_HASH} >> $PROJ_SLUG\version.txt
    - echo "Building docker image"
    - ssh ubuntu@${VM_AKS_STAG} "docker rmi $ACR_NAME_NEW/$PROJ_SLUG-stg:latest $ACR_NAME_NEW/$PROJ_SLUG-stg:$PROJ_VER" || true
    - ssh ubuntu@${VM_AKS_STAG} "cd $PROJ_SLUG && docker build -t $ACR_NAME_NEW/$PROJ_SLUG-stg:$PROJ_VER ."
    - ssh ubuntu@${VM_AKS_STAG} "docker push $ACR_NAME_NEW/$PROJ_SLUG-stg:$PROJ_VER"
    - ssh ubuntu@${VM_AKS_STAG} "az acr repository untag -n $ACR_USER_NEW --image $PROJ_SLUG-stg:latest" || true
    - ssh ubuntu@${VM_AKS_STAG} "az acr import -n $ACR_USER_NEW --source $ACR_NAME_NEW/$PROJ_SLUG-stg:$PROJ_VER -t $PROJ_SLUG-stg:latest"
  only:
    - main

publish prod image:
  stage: release
  image: kroniak/ssh-client:3.9
  script:
    - mkdir ~/.ssh
    - echo ${KNOWN_HOSTS_AKS_STAG} >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
    - chmod 600 ${ID_RSA_AKS_STAG}
    - eval $(ssh-agent -s)
    - ssh-add ${ID_RSA_AKS_STAG}
    - ssh ubuntu@${VM_AKS_STAG} "rm -r $PROJ_SLUG || true && mkdir $PROJ_SLUG || true"
    - scp -r * ubuntu@${VM_AKS_STAG}:/home/<USER>/$PROJ_SLUG
    - ssh ubuntu@${VM_AKS_STAG} "docker login -u $ACR_USER_NEW -p $ACR_TOKEN_NEW $ACR_NAME_NEW"
    - echo "Adding versioning file"
    - echo ${CODE_VERSION} > $PROJ_SLUG\version.txt
    - echo ${COMMIT_HASH} >> $PROJ_SLUG\version.txt
    - echo "Building docker image"
    - ssh ubuntu@${VM_AKS_STAG} "docker rmi $ACR_NAME_NEW/$PROJ_SLUG-prod:latest $ACR_NAME_NEW/$PROJ_SLUG-prod:$PROJ_VER" || true
    - ssh ubuntu@${VM_AKS_STAG} "cd $PROJ_SLUG && docker build -t $ACR_NAME_NEW/$PROJ_SLUG-prod:$PROJ_VER ."
    - ssh ubuntu@${VM_AKS_STAG} "docker push $ACR_NAME_NEW/$PROJ_SLUG-prod:$PROJ_VER"
    - ssh ubuntu@${VM_AKS_STAG} "az acr repository untag -n $ACR_USER_NEW --image $PROJ_SLUG-prod:latest" || true
    - ssh ubuntu@${VM_AKS_STAG} "az acr import -n $ACR_USER_NEW --source $ACR_NAME_NEW/$PROJ_SLUG-prod:$PROJ_VER -t $PROJ_SLUG-prod:latest"
  only:
    - tags


