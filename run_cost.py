from dotenv import load_dotenv

from ingestion.adaptor.cost import Cost

vars_loaded = load_dotenv('.env.dev')
if not vars_loaded:
    print("Unable to load .env.dev file")
    exit(1)


if __name__ == '__main__':

    # file = ('/Users/<USER>/biome/scripts/Ingestion/phi/<EMAIL>'
    #         '__UCSF__UCSF__Biome - Cost Utilization CY25 Q1 (Part 2 of 2).xlsx')
    file = ('/Users/<USER>/biome/scripts/Ingestion/phi/ucfs admin test/'
            '20250610__ucsfauto_sftp@biomedata.io__UCSF__UCSF__Biome - Cost Utilization CY25 Q1 (Part 2 of 2).xlsx')
    client = 'UCSF'
    write_data = True
    file_id = 60360
    # 56354 for Montefiore
    # 56355 for bjc
    cost = Cost(filepath=file, write=write_data, client=client, rebuild_schema=True, client_file_id=file_id,
                db_as_source=True, testing=True)

    # run any of the below methods as per the requirement if only specific task is required
    # cost.build()  # This will create the schema only, run this if only schema is required
    # cost.read()  # This will read the data from the file and insert in the client_raw db
    cost.translate()  # This will translate the data and write data as a single table as per biome schema

    # use the below to run all the above methods in sequence.
    # cost.run()  # This will run all the above methods in sequence
