import pandas as pd
import logging
from ingestion.utils.db import get_db_engine, read_sql
import warnings
import os
from dotenv import load_dotenv
warnings.filterwarnings("ignore")


log_file_path = 'qa.log'
logging.basicConfig(
    filename=log_file_path, level=logging.INFO,
    format='%(asctime)s %(message)s',
    datefmt='%m/%d/%Y %I:%M:%S %p'
)
logging.root.setLevel(logging.INFO)


load_dotenv("../env.dev")


def compare_output(out_dir, db_table_current, db_table_new):
    new_data = read_sql(f'Select * from {db_table_new}')
    old_data = read_sql(f'Select * from {db_table_current}')

    new_data.columns = new_data.columns.str.lower()
    old_data.columns = old_data.columns.str.lower()

    key_col = 'encounternumber'
    ids_new = new_data[key_col].astype(str).unique().tolist()
    ids_old = old_data[key_col].astype(str).unique().tolist()

    missing_in_new = [i for i in ids_old if i not in ids_new]
    missing_in_old = [i for i in ids_new if i not in ids_old]

    if missing_in_new:
        with open(f'{out_dir}/missing_ids_in_{db_table_new}.txt', 'w') as file:
            file.write(""" \n """.join(missing_in_new))
        logging.info('Ids missing in new')
    if missing_in_old:
        with open(f'{out_dir}/missing_ids_in_{db_table_current}.txt', 'w') as file:
            file.write(""" \n """.join(missing_in_old))
        logging.info('Ids missing in old')

    if not missing_in_old and not missing_in_new:
        logging.info('ids present on both sides')

    count_new = new_data.shape[0]
    count_old = old_data.shape[0]

    if count_new != count_old:
        if count_old * 2 == count_new:
            logging.info('Double the rows in new data')
        else:
            logging.info(f'row count does not match. {count_new} vs {count_old}')

    else:
        logging.info('row count match')

    ignore_cols = [key_col, 'biomeimportdt']
    out_df = pd.DataFrame()
    for field in old_data.columns:
        if field in new_data.columns and field not in ignore_cols and not field.endswith('rowid'):
            try:
                new_data[field] = pd.to_datetime(new_data[field])
                old_data[field] = pd.to_datetime(old_data[field])
            except Exception as e:
                pass

            try:
                new_data[field] = new_data[field].astype(float).round(0)
                old_data[field] = old_data[field].astype(float).round(0)
            except Exception as e:
                pass

            df_new = new_data[[key_col, field]].sort_values(field)
            df_old = old_data[[key_col, field]].sort_values(field)

            df_new[field] = df_new[field].astype(str).replace('\.0', '', regex=True)
            df_old[field] = df_old[field].astype(str).replace('\.0', '', regex=True)

            df_new_gr = df_new.groupby(key_col)[field].agg(list).apply(lambda x: '--'.join(list(map(str, set(x)))))
            df_old_gr = df_old.groupby(key_col)[field].agg(list).apply(lambda x: '--'.join(list(map(str, set(x)))))

            df_new_gr = pd.DataFrame(df_new_gr).rename(columns={field: 'NewData'})
            df_old_gr = pd.DataFrame(df_old_gr).rename(columns={field: 'OldData'})

            df_compared = df_new_gr.join(df_old_gr)
            df_compared = df_compared[df_compared['NewData'] != df_compared['OldData']]
            df_compared['Field'] = field

            out_df = pd.concat([out_df, df_compared])

    if not out_df.empty:
        out_df.to_csv(f'{out_dir}/Differences_{db_table_current}.csv')
    else:
        logging.info('No differences found')


def check_missing_fields(out_dir, db_table_current, db_table_new):
    db_con = get_db_engine(db='DB_EARASS')
    current = pd.read_sql(f'DESC {db_table_current}', con=db_con)
    current_fields = current['Field'].str.lower().tolist()

    new = pd.read_sql(f'DESC {db_table_new}', con=db_con)
    new_fields = new['Field'].str.lower().tolist()

    missing_in_new = [field for field in current_fields if field not in new_fields]
    missing_in_current = [field for field in new_fields if field not in current_fields]

    print('Missing in current: ', missing_in_current)
    print('Missing in new: ', missing_in_new)
    print('Missing in new: ', len(missing_in_new))

    if missing_in_new:
        with open(f'{out_dir}/missing_fields_in_{db_table_new}.txt', 'w') as file:
            file.write(""" \n """.join(missing_in_new))
    if missing_in_current:
        with open(f'{out_dir}/missing_fields_in_{db_table_current}.txt', 'w') as file:
            file.write(""" \n """.join(missing_in_current))


def execute(out_dir, db_table_current, db_table_new):
    if not os.path.exists(out_dir):
        os.makedirs(out_dir)
    check_missing_fields(out_dir, db_table_current, db_table_new)
    compare_output(out_dir, db_table_current=db_table_current, db_table_new=db_table_new)


if __name__ == '__main__':

    out_dir = 'diagnosis'

    files = pd.read_csv('../new_tables_diagnosis_one_file.csv')
    # files = files[files['ignore'] == 0]

    for ind, row in files.iterrows():
        db_table_current = f"{row['dbname']}.{row['tablename']}"
        db_table_new = f'db_earass.{row["target_table"]}'
        print(db_table_current, db_table_new, row['file_id'])
        execute(out_dir, db_table_current, db_table_new)

    # db_table_current = f'BELLIN_MASTER.20240529_ADMIN_Bellin_None_457_DIAGNOSIS'
    # db_table_new = f'db_earass.20240529_diagnosis_1_bellin_none_327'
    # execute(out_dir, db_table_current, db_table_new)
