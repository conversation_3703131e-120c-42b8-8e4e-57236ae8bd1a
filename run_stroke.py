from ingestion.adaptor.stroke import Stroke
# from dotenv import load_dotenv

# vars_loaded = load_dotenv("E:/Biome/ingestion projects/ingestion3/env.dev")
# if not vars_loaded:
#     print("Unable to load .env.dev file")
#     exit(1)

if __name__ == '__main__':
    file = ('E:/Biome/ingestion projects/ingestion3/tests/mock_data/stroke/stroke.csv')
    client = 'Sutter'
    file_id = 43324

    stroke = Stroke(client='Sutter', filepath=file, client_file_id=file_id, write=True, export_schema=True,
                    rebuild_schema=True, testing=True, db_as_ncdr_source=False)
    # stroke.build()
    # stroke.read()
    stroke.translate()
