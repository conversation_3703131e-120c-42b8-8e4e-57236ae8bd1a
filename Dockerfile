FROM python:3.10-slim


# Azure configuration environment variables
ENV KEY_VAULT_NAME="biome-apps-pro"
ENV KEY_VAULT_URL="https://biome-ingestion-prod.vault.azure.net/"

WORKDIR /usr/app/src

# If this image changes, it's likely because the contents of this repository
# have changed (i.e. the COPY . ./ command), and not the requirements.txt
# file. Docker caches in layers, so running the pip3 install first, as it
# is the more stable command, will allow us to skip pulling from pypi on each
# `docker build`.

COPY requirements.txt ./
RUN pip3 install --upgrade pip
RUN pip3 install -r requirements.txt --extra-index-url http://********* --trusted-host *********

COPY . ./

# Run the ingestion job.
CMD ["python", "main.py"]