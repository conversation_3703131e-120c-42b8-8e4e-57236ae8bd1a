from dotenv import load_dotenv
from ingestion.adaptor.afib import AFib10

vars_loaded = load_dotenv(".env.dev")
if not vars_loaded:
    print("Unable to load .env.dev")
    exit(1)

if __name__ == "__main__":
    file = "E:/Biome/Ingestion3_files/20240419__SutterAuto__Sutter__Multi__AFib721841-2023Q4-masked_updated.xml"
    file_id = "56349"
    client = "sutter"
    write_data = True
    afib = AFib10(filepath=file, write=write_data, client=client, client_file_id=file_id, rebuild_schema=True,
                  db_as_ncdr_source=True, testing=True)

    # run any of the below methods as per the requirement if only specific task is required
    # afib.build()  # This will create the schema only, run this if only schema is required
    # afib.read()  # This will read the xml data from the file and insert in the tables using ncdr schema
    afib.translate()  # This will translate the data and write data as a single table as per biome schema

    # use the below to run all the above methods in sequence.
    # This will build the ncdr schema, read the xml data from the file, and translate the data as per biome schema
    # afib.run()  # This will run all the above methods in sequence
