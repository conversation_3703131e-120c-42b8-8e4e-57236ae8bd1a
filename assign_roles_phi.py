import yaml
import pandas as pd

from ingestion.utils.db import read_sql
from ingestion.config import read_config

from dotenv import load_dotenv

vars_loaded = load_dotenv("env.dev")


def assign_roles_phi(dataset):
    roles_qry = f"""
    SELECT distinct Role, field_name, field_id from (
        select
        COALESCE( fa.Role, b.Role)  Role, SubsetName, RegistryName
        , Upper(concat(RegistryName, '__', SubsetName)) registry_subset_name
        , b.id field_id
        , b.name field_name
        , c.id subset_id
        from Subsets c
        join `Fields` b on b.SubsetId like concat('%|', c.id, '|%')
        left join `FieldAttributes` fa on b.id = fa.FieldId and c.id = fa.SubsetId
        order by registry_subset_name
    )x where RegistryName like 'admin' and SubsetName = '{dataset}' and role is not null
    """

    phi_roles = [
        'IDENTIFIER',
        'AGE',
        'MEDRECNUM',
        'PATIENT_ZIP',
        'ARRIVAL_DATE',
        'ADMISSION_DATE',
        'DATE',
        'PROCEDURE_DATE',
        'ANCHOR_DATE',
        'DISCHARGE_DATE',
        'FREE_TEXT',
        'ANY_ID',
        'NAME',
        'DOB',
        'GENDER',
        'DATE_ONLY',
    ]

    def is_phi_role(role):
        return any([i in str(role) for i in phi_roles])

    roles = read_sql(roles_qry, 'ingestion_prod')
    biome_schema = read_config(dataset, 'biome_schema')
    biome_schema.drop(columns=['Role', 'PHI'], inplace=True)

    original_rows = biome_schema.shape[0]

    biome_schema = biome_schema.merge(
        roles,
        left_on=biome_schema['Field'].str.lower(),
        right_on=roles['field_name'].str.lower(),
        how='left'
    )

    biome_schema.loc[biome_schema['Field'].str.contains('mrn', case=False), 'Role'] = 'MEDRECNUM'

    if not biome_schema.shape[0] == original_rows:
        raise ValueError('Mismatch in number of rows')

    biome_schema['PHI'] = biome_schema['Role'].apply(is_phi_role)

    biome_schema = biome_schema.drop(columns=['key_0', 'field_name', 'field_id'])

    biome_schema = biome_schema.where(pd.notnull(biome_schema), None)

    print(yaml.dump(biome_schema.to_dict(orient='records')))


if __name__ == '__main__':
    assign_roles_phi('cpt')
