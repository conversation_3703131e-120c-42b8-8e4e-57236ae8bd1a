from dotenv import load_dotenv

from ingestion.adaptor.icd import ICD30

var_loaded = load_dotenv(".env.dev")
if not var_loaded:
    print("Unable to load .env.dev file")
    exit(1)

if __name__ == "__main__":
    file = "E:/Biome/Ingestion3_files/ICD 3.0/Updated/" \
           "20250219__SutterAuto__Sutter__Multi__ICD714754-2025Q1-masked_updated.xml"
    client = "sutter"
    write_data = True
    file_id = 12345

    icd = ICD30(filepath=file, write=write_data, client=client, rebuild_schema=True, client_file_id=file_id,
                db_as_ncdr_source=True, testing=True, export_schema=False)

    # run any of the below methods as per the requirement if only specific task is required
    icd.build()  # This will create the schema only, run this if only schema is required
    icd.read()  # This will read the xml data from the file and insert in the tables using ncdr schema
    # icd.translate()  # This will translate the data and write data as a single table as per biome schema

    # use the below to run all the above methods in sequence.
    # This will build the ncdr schema, read the xml data from the file, and translate the data as per biome schema
    # icd.run()  # This will run all the above methods in sequence
