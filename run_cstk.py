from ingestion.adaptor.cstk import CSTK
from dotenv import load_dotenv

vars_loaded = load_dotenv("env.dev")
if not vars_loaded:
    print("Unable to load .env.dev file")
    exit(1)

if __name__ == '__main__':
    file = ('phi/client_files/Sutter/'
            '20240905__MManifield@biomedata.io__Sutter__Sutter__Sutter CSTK all sites Jan 2024 to Sept 2024.csv')
    client = 'Sutter'
    file_id = 54193

    cstk = CSTK(client='Sutter', filepath=file, client_file_id=file_id, write=True, export_schema=True,
                rebuild_schema=True, testing=True, db_as_ncdr_source=False)
    # cstk.build()
    cstk.read()
    cstk.translate()
