from dotenv import load_dotenv
import logging
import pandas as pd
import os

from ingestion.adaptor.diagnosis import Diagnosis
from ingestion.schema.diagnosis import SchemaDiagnosis
from ingestion.readers.diagnosis import ReaderDiagnosis
from ingestion.translators.translate_diagnosis_to_biome import TranslateDiagnosisToBiome

vars_loaded = load_dotenv(".env.dev")
if not vars_loaded:
    print("Unable to load .env.dev file")
    exit(1)

target_raw_db = 'ingestion3_raw'
target_master_db = 'ingestion3_master'

if __name__ == '__main__':
    if os.path.exists('diagnosis_files.csv'):
        target_tables = []
        schema = SchemaDiagnosis(rebuild_schema=True, target_db=target_raw_db, export_schema=False)
        files = pd.read_csv('diagnosis_files.csv')
        file_dir = '/Users/<USER>/biome/scripts/Ingestion/phi/ing3-test/admin-diagnosis'

        for ind, row in files.iterrows():
            write_data = True

            filename = row['filename']
            client = row['client']
            file_id = row['id']
            file = os.path.join(file_dir, filename)

            if client in ['UCSF']:
                # SKIPPING UCLA XLSX due to versioning issue for libraries on local execution
                continue

            logging.info(f"Reading file: {file}, index: {ind}")
            reader = ReaderDiagnosis(filepath=file, schema=schema, write=write_data, client=client, file_id=file_id,
                                     target_db=target_raw_db)
            reader.execute()

            logging.info(f"Translating file: {file}, index: {ind}")

            translate = TranslateDiagnosisToBiome(filepath=file, schema=schema, reader=reader, file_id=file_id,
                                                  client=client,
                                                  write=write_data, target_db=target_master_db, db_as_source=True,
                                                  source_db=target_raw_db)
            translate.execute()

            target_tables.append(
                {
                    'client': client,
                    'file_id': file_id,
                    'file': filename,
                    'target_db': target_master_db,
                    'target_table': translate.target_table,
                    'sql_qry': (f"select '{translate.target_table}' as tbl, "
                                f"a.* from {target_master_db}.{translate.target_table} as a union")
                }
            )

        # write to csv
        target_tables_df = pd.DataFrame(target_tables)
        target_tables_df.to_csv('new_tables_diagnosis.csv', index=False)
        logging.info('New tables written to new_tables_diagnosis.csv')
    else:
        logging.info('diagnosis_files.csv does not exist in directory, attempting single file')
        file = ('/Users/<USER>/biome/scripts/Ingestion/phi/ucfs admin test/'
                '20250610__ucsfauto_sftp@biomedata.io__UCSF__UCSF__Biome Diagnosis ICD Codes CY25 Q1.xlsx')
        client = 'UCSF'
        write_data = True
        file_id = 60400

        diagnosis = Diagnosis(filepath=file, write=write_data, client=client, rebuild_schema=True,
                              client_file_id=file_id, db_as_source=True, testing=True)
        # diagnosis.build()
        # diagnosis.read()
        diagnosis.translate()
