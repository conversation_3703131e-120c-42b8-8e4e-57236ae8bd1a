import pandas as pd
import numpy as np
from dotenv import load_dotenv
import json

from ingestion.config_json import write_config
from ingestion.data_dictionaries import read_data_dict
from ingestion.config import read_config


vars_loaded = load_dotenv("env.dev")


def create_rows_dict(row):
    return {
        'Reference Field': row['Reference Field'],
        'Reference Field Code': row['Reference Field Code'],
        'Derivative Field': row['Derivative Field'],
        'Derivative Field Code': row['Derivative Field Code'],
        'Derivative Value': row['Derivative Value'],
        'Derivative Value Code': row['Derivative Value Code'],
        'Value Map': row['Value Map'],
        'Exact Match': row['Exact Match'],
        'Case Match': row.get('Case Match', 0),
        'Regex Match': row.get('Regex Match', 0),
        'Delimiter': row['Delimiter'],
    }


if __name__ == '__main__':
    from ingestion.schema.icd import SchemaICD30 as schema

    data_dict = schema.DATA_DICT
    dataset = schema.DATASET
    dataset_id = schema.DATASET_ID
    source_version = schema.VERSION
    version_str = source_version.replace('.', '')

    config_name = dataset + version_str

    biome_version = read_config(config_name, 'version', as_df=False)

    biome_version_str = biome_version.replace('.', '')

    ncdr_fields = read_data_dict(data_dict, sheet_name='Elements')['Short Name'].str.lower().tolist()

    biome_schema = read_config(config_name, 'biome_schema')

    biome_fields = pd.DataFrame(biome_schema['Field'].str.lower().tolist(), columns=['Field'])
    biome_fields = biome_fields.merge(pd.DataFrame(ncdr_fields, columns=['Source']), left_on='Field',
                                      right_on='Source', how='left')

    custom_fields = read_config(config_name, 'custom_fields')

    custom_fields['Field'] = custom_fields['Field'].str.lower()

    custom_fields['Derivative Field Code'] = custom_fields['Derivative Field Code'].astype(str).\
        apply(lambda r: r.replace('.0', ''))
    custom_fields['Derivative Field Code'] = custom_fields['Derivative Field Code'].replace('nan', None)
    custom_fields['Derivative Value Code'] = custom_fields['Derivative Value Code'].astype(str). \
        apply(lambda r: r.replace('.0', ''))
    custom_fields['Derivative Value Code'] = custom_fields['Derivative Value Code'].replace('nan', None)

    # For some NCDRs Case Match is not required
    if hasattr(custom_fields, 'Case Match'):
        custom_fields['Case Match'] = custom_fields['Case Match'].astype(str).apply(lambda r: r.replace('.0', ''))
        custom_fields['Case Match'] = custom_fields['Case Match'].replace('nan', None)

    # For follow-up Regex Match is not required.
    # custom_fields['Regex Match'] = custom_fields['Regex Match'].astype(str).apply(lambda r: r.replace('.0', ''))
    # custom_fields['Regex Match'] = custom_fields['Regex Match'].replace('nan', None)

    custom_fields['Transformation'] = custom_fields.apply(create_rows_dict, axis=1)

    biome_fields = biome_fields.merge(custom_fields[['Field', 'Transformation']], on='Field', how='left')

    rename_fields = read_config(config_name, 'rename_fields').rename(columns={'Old Field': 'Field'})
    rename_fields['Field'] = rename_fields['Field'].str.lower()

    biome_fields = biome_fields.merge(rename_fields, on='Field', how='left')
    biome_fields['Source'] = np.where(biome_fields['Source'].isna(), biome_fields['New Field'].str.lower(),
                                      biome_fields['Source'])

    value_mapping = read_config(config_name, 'value_mapping')
    if not value_mapping.empty:
        value_mapping['Field'] = value_mapping['Field'].str.lower()
        biome_fields = biome_fields.merge(value_mapping, on='Field', how='left')
    else:
        biome_fields['Value Map'] = None

    compute_fields = read_config(config_name, 'transform_fields').rename(columns={'Computation': 'is_computation'})
    # compute_fields['Computation'] = {'Transform Type': compute_fields['Transform Type'],
    #                                  ' Args':compute_fields['Args']

    compute_fields = pd.DataFrame({
        'Field': compute_fields['Field'],
        'Computation': compute_fields.apply(lambda row: {'Transform_Type': row['Transform Type'],
                                                         'Args': row['Args'],
                                                         'is_computation': row['is_computation']}, axis=1)
    })

    compute_fields['Field'] = compute_fields['Field'].str.lower()
    aggregated_compute_fields = compute_fields.groupby('Field').agg(
        Computation=('Computation', list)
    ).reset_index()
    # compute_fields['Computation'] = compute_fields['Computation'].str.replace('r.', '')

    biome_fields = biome_fields.merge(aggregated_compute_fields, on='Field', how='left')

    biome_fields = biome_fields.rename(columns={'Field': 'Target'})

    data = {
        'TranslatorConfig': {
            'Version': biome_version,
            'Source': {
                'Dataset': {
                    'Code': dataset,
                    'Id': dataset_id
                },
                'Version': source_version,
                'Type': 'Vendor'
            },
            'Target': {
                'Dataset': {
                    'Code': dataset,
                    'Id': dataset_id
                },
                'Version': biome_version,
                'Type': 'Biome'
            },
            'Translations': json.loads(biome_fields[['Target', 'Source', 'Transformation',
                                                     'Value Map', 'Computation']].to_json(orient='records'))
        }
    }

    filename = f'translator_{dataset}{version_str}_biome{biome_version_str}'.lower()

    write_config(f'{filename}.json', data)
