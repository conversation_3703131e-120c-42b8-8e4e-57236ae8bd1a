from dotenv import load_dotenv

from ingestion.adaptor.tvt import TVTFU30

vars_loaded = load_dotenv(".env.dev")
if not vars_loaded:
    print("Unable to load .env.dev file")
    exit(1)

if __name__ == '__main__':
    file = '20250618__mcasey@biomedata.io__Emory__Emory__TVT_F_904532-2023Q1.xml'
    client = 'Emory'
    write_data = True
    file_id = 60913

    tvt = TVTFU30(filepath=file, write=write_data, client=client, rebuild_schema=True, client_file_id=file_id,
                  db_as_ncdr_source=True, testing=True)

    # run any of the below methods as per the requirement if only specific task is required
    # tvt.build()  # This will create the schema only, run this if only schema is required
    # tvt.read()  # This will read the xml data from the file and insert in the tables using ncdr schema
    tvt.translate()  # This will translate the data and write data as a single table as per biome schema

    # use the below to run all the above methods in sequence.
    # This will build the ncdr schema, read the xml data from the file, and translate the data as per biome schema
    # tvt.run()  # This will run all the above methods in sequence
