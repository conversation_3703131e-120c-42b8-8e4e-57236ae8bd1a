# ingestion3



## Setup

### Clone the repository

```bash
git clone https://git.biome.io/pipeline/ingestion3.git
```

### Install dependencies

```bash
pip install -r requirements.txt
```

### Environment Set up
Local development leverages dotenv to load environment variables from a text file. A sample file is provided and can be configured with the following options which must also be set in staging or production.

- `ENVIRONMENT`
    - Environment for execution which should be `prod`, `stg`, or `dev` depending on what environment the code is being executed in at run time.
    - Default value:  dev
- `DB_USERNAME` - Required
    - User name for target database
- `DB_PASSWORD` - Required
    - User password for target database
- `VAULT_NAME`
    - Keyvault that will be used to pull any remotely stored credentials or keys for normal execution
    - Default: `biome-apps-prod` 
- `STAGE` - Required
    - Determines what part of code will be executed for ingestion, 
    - Accepts: `ingestion` - other stages to be added soon
- `INPUT_FILE` - Required
    - Detemines what file is being used as input for execution of stages dependent upon an input file. If none is provided then this should be passed as an input argument to `main.py`
- `TARGET_DB` - Required
    - Target database for schema building and data ingestion.
- `PODNAME`
    - Variable written to events to log where this execution occurred.
    - Default: `unknown`
- `PUBLISH_EVENTS`
    - Determines if events will be published as part of execution allowing for this to be toggled independent of environment of execution
    - Default: `no`
    - Accepts: `yes` or `no` and is case insensitive

## Run the pipeline

- Rename the file `.env.sample` to `.env.dev` and provide valid values
- To run ingestion set `STAGE` in `.env.dev` of `ingestion` and execute `main.py`
- For schema building and translation execute the `run_cathpci5.py`, `run_cpmi30.py`, or `run_cpmi31.py`
- if only the schema is desired, call the `CathPCI5.build` method. To rebuild schema set `rebuild_schema` to `True`
- if the data needs to be read from xml and output as per the ncdr schema, call the `CathPCI5.read` method
- if the data needs to be translated to the biome schema, call the `CathPCI5.translate` method. To use NCDR tables from db set `db_as_source` to `True`
- All the above three can be done by calling the `CathPCI5.run` method alone
- The approach is similar for the cpmi30 and cpmi31, for which the run_cpmi30.py and run_cpmi31.py files are used respectively