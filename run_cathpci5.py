from ingestion.adaptor.cathpci import CathPCI5
from dotenv import load_dotenv

vars_loaded = load_dotenv(".env.dev")
if not vars_loaded:
    print("Unable to load .env.dev file")
    exit(1)

if __name__ == '__main__':
    file = '/Users/<USER>/biome/scripts/Ingestion/phi/20240916__SutterAuto__Sutter__Multi__PCI218907-2024Q2.xml'
    file_id = '54345'
    client = 'sutter'
    write_data = True
    pci = CathPCI5(filepath=file, write=write_data, client=client, client_file_id=file_id,
                   rebuild_schema=False, db_as_ncdr_source=True, testing=True, export_schema=True)
    # run any of the below methods as per the requirement if only specific task is required
    # pci.build()  # This will create the schema only, run this if only schema is required
    # pci.read()  # This will read the xml data from the file and insert in the tables using ncdr schema
    pci.translate()  # This will translate the data and write data as a single table as per biome schema

    # use the below to run all the above methods in sequence.
    # This will build the ncdr schema, read the xml data from the file, and translate the data as per biome schema
    # pci.translate()  # This will run all the above methods in sequence
