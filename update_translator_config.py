import yaml
import pandas as pd

from ingestion.utils.db import read_sql
from ingestion.config import read_config

from dotenv import load_dotenv

var_loads = load_dotenv('.env_41.dev')
if not var_loads:
    print('Unable to load env_41 file')
    exit(1)


def update_translator_config(dataset):
    config_query = f"""
    select upper(a.Client) client_name
   , if (IFNULL(b.name, '')='', replace(replace(replace(replace(replace(replace(replace(replace(replace(concat(
        a.SourceName, a.id), "'", ""), ' ', ''), ',', ''), "'", ''), '"', ''), '.', ''), '(', ''), ')', ''), '-',
        ''), substr(b.`Name` from 1 for 60)) as field_name
   , COALESCE(a.CleanerRegex, fa.CleanerRegex, b.CleanerRegex)  cleaner_regex
   , COALESCE(a.ValueMap, fa.ValueMap, b.ValueMap)  value_map
   , COALESCE(a.Transformation, fa.Transformation, b.Transformation)  transformation
   , COALESCE(fa.Computation, b.Computation)  computation
   from Subsets c
   join `Fields` b on b.SubsetId like concat('%|', c.id, '|%')
   left join `FieldAttributes` fa on b.id = fa.FieldId and c.id = fa.SubsetId
   left join ClientFieldMapping a on b.id = a.TargetFieldId
   where RegistryName like '%admin%' and subsetName = '{dataset}' and COALESCE(fa.Required, b.Required) = 1
   """
    df = read_sql(config_query, 'ingestion_prod')
    biome_schema = read_config(dataset, 'biome_schema', as_df=False)

    def update_dictionary(frame, dic, dic_col):
        dic['Field'].append(frame['field_name'].values[0])
        dic[dic_col].append(frame.loc[frame[dic_col].first_valid_index(), dic_col])

    value_map_dict = {'Field': [],
                      'Value Map': []}

    cleaner_regex_dict = {'Field': [],
                          'Regex': []}

    transformation_dict = {'Field': [],
                           'Expression': []}

    computation_dict = {'Field': [],
                        'Computation': []}

    df['field_name'] = df['field_name'].str.lower()
    df = df.rename(columns={'value_map': 'Value Map', 'cleaner_regex': 'Regex',
                            'transformation': 'Expression', 'computation': 'Computation'})

    for field, frame in df.groupby('field_name'):
        if frame['Value Map'].first_valid_index():
            update_dictionary(frame, value_map_dict, dic_col='Value Map')

        if frame['Regex'].first_valid_index():
            update_dictionary(frame, cleaner_regex_dict, dic_col='Regex')

        if frame['Expression'].first_valid_index():
            update_dictionary(frame, transformation_dict, dic_col='Expression')

        if frame['Computation'].first_valid_index():
            update_dictionary(frame, computation_dict, dic_col='Computation')

    value_mapping_df = pd.DataFrame(value_map_dict)
    cleaner_regex_df = pd.DataFrame(cleaner_regex_dict)
    transformation_df = pd.DataFrame(transformation_dict)
    computation_df = pd.DataFrame(computation_dict)

    value_mapping_df['Value Map'] = value_mapping_df['Value Map'].apply(
        lambda x: eval(x) if str(x) not in ['nan', ''] else None)

    config = {
        'version': '1.0',
        'biome_schema': biome_schema,
        'value_mapping': value_mapping_df.to_dict(orient='records'),
        'cleaner_regex': cleaner_regex_df.to_dict(orient='records'),
        'transform_fields': transformation_df.to_dict(orient='records'),
        'compute_fields': computation_df.to_dict(orient='records')
    }
    # print(yaml.dump(config, sort_keys=False, indent=4))

    with open(f'ingestion/config/{dataset}.yaml', 'w') as f:
        yaml.dump(config, f, sort_keys=False, indent=4, default_style=None)


if __name__ == '__main__':
    update_translator_config('discharges')
