Table,Short Name
ACCESSSYS,"['AccessSysID', 'AccessSysCounter']"
ADJMEDS,"['ADJ_MedID', 'ADJ_MedID', 'ADJ_MedID', 'ADJ_MedID', 'ADJ_MedID', 'ADJ_MedID', 'ADJ_MedID', 'ADJ_MedID', 'ADJ_MedID', 'ADJ_MedID', 'ADJ_MedID', 'ADJ_MedID', 'ADJ_MedID', 'ADJ_MedID', 'ADJ_MedID', 'ADJ_MedID', 'ADJ_MedID', 'ADJ_MedID', 'ADJ_MedID', 'ADJ_MedID', 'ADJ_MedAdmin', 'ADJ_MedAdmin']"
DCMEDS,"['DC_MedID', 'DC_MedID', 'DC_MedID', 'DC_MedID', 'DC_MedID', 'DC_MedID', 'DC_MedID', 'DC_MedID', 'DC_MedID', 'DC_MedID', 'DC_MedID', 'DC_MedID', 'DC_MedID', 'DC_MedID', 'DC_MedID', 'DC_MedID', 'DC_MedID', 'DC_MedID', 'DC_MedAdmin', 'DC_MedAdmin', 'DC_MedAdmin', 'DC_MedAdmin', 'DC_MedDose', 'DC_MedDose', 'DC_MedDose']"
DEMOGRAPHICS,"['LastName', 'FirstName', 'MidName', 'SSN', 'SSNNA', 'NCDRPatientID', 'OtherID', 'DOB', 'Sex', 'Sex', 'ZipCode', 'ZipCodeNA', 'RaceWhite', 'RaceBlack', 'RaceAsian', 'RaceAmIndian', 'RaceNatHaw', 'HispOrig', 'OrigPtID', 'OrigNCDRVen']"
DEVICES,"['LAADevID', 'DevCounter', 'Dev_UDIDirectID', 'LAAIsolationApproach', 'LAAIsolationApproach', 'OutDevUnsucDepl', 'OutDevUnsucDepl', 'OutDevUnsucDepl', 'DevSucdep']"
EPISODEOFCARE,"['EpisodeKey', 'ArrivalDate', 'HealthIns', 'HIPS', 'HIPS', 'HIPS', 'HIPS', 'HIPS', 'HIPS', 'HIPS', 'HIPS', 'EnrolledStudy', 'ChadCHF', 'NYHA', 'NYHA', 'NYHA', 'NYHA', 'ChadLVDysf', 'ChadHypertCont', 'ChadDM', 'ChadStroke', 'ChadTIA', 'ChadTE', 'ChadVascDis', 'PriorVD', 'PriorVD', 'PriorVD', 'PriorVD', 'PriorVD', 'PriorVD', 'PriorVD', 'HBHyperUncont', 'HBAbnRenal', 'HBAbnLiver', 'HBStroke', 'HBBleed', 'HBLabINR', 'HBAlcohol', 'HBDrugAP', 'HBDrugNSAID', 'CAD', 'ValvularAF', 'HxMVReplace', 'MechValveMitPos', 'HxMVRepair', 'AFibClass', 'AFibClass', 'AFibClass', 'AFibClass', 'PrevAFibTerm', 'PrevAFibTermPC', 'PrevAFibTermDC', 'PrevAFibTermCA', 'AFibCathAblDate', 'AFibPriorAblStrategyCode', 'AFibPriorAblStrategyCode', 'AFibPriorAblStrategyCode', 'AFibPriorAblStrategyCode', 'AFibPriorAblStrategyCode', 'AFibPriorAblStrategyCode', 'AFibPriorAblStrategyCode', 'AFibPriorAblStrategyCode', 'AFibPriorAblStrategyCode', 'AFibPriorAblStrategyCode', 'PrevAFibTermSA', 'AFibSurgAblDate', 'AFlutter', 'AFlutterType', 'AFlutterType', 'PrevAFLTerm', 'PrevAFLTermPC', 'PrevAFLTermDC', 'PrevAFLTermCA', 'AFibFlutterCathAblDate', 'CM', 'PriorCMType', 'PriorCMType', 'PriorCMType', 'PriorCMType', 'PriorCMType', 'ChronicLungDisease', 'SleepApnea', 'SleepApneaRxFollowed', 'AtrialRhythm', 'AtrialRhythm', 'AtrialRhythm', 'AtrialRhythm', 'AtrialRhythm', 'AtrialRhythm', 'AtrialRhythm', 'LVEFAssessed', 'LVEF', 'TTEPerf', 'TTEDate', 'BaselineImagingPerf', 'CTPerformed', 'CTImagingDate', 'MRPerformed', 'MRDate', 'Height', 'Weight', 'Pulse', 'SystolicBP', 'DiastolicBP', 'HGB', 'HGBND', 'PT', 'PTND', 'INR', 'INRND', 'PreProcCreat', 'PreProcCreatND', 'PostProc_RankinScaleNA', 'DCDate', 'DCStatus', 'DCStatus', 'DCLocation', 'DCLocation', 'DCLocation', 'DCLocation', 'DCLocation', 'DCLocation', 'DCHospice', 'DeathProcedure', 'DeathCause', 'DeathCause', 'DeathCause', 'DeathCause', 'DeathCause', 'DeathCause', 'DeathCause', 'DeathCause', 'DeathCause', 'DeathCause', 'DeathCause', 'DeathCause', 'DeathCause', 'DeathCause', 'DeathCause', 'DeathCause', 'DeathCause', 'DeathCause', 'DeathCause', 'DeathCause', 'DeathCause', 'MBI', 'PlateletCt', 'PlateletCtND', 'AFibInd', 'Albumin', 'Albumin_ND', 'LAAO_Adm', 'HBStrokeType', 'HBStrokeType', 'HBStrokeType', 'IncrFallRisk', 'ClinicBleedEvent', 'BleedEventType', 'BleedEventType', 'BleedEventType', 'BleedEventType', 'GeneticCoag', 'ConAntiCoagTx', 'HxRHVD', 'CardStrucInterv', 'CardStrucIntervType', 'CardStrucIntervType', 'CardStrucIntervType', 'CardStrucIntervType', 'CardStrucIntervType', 'CardStrucIntervType', 'CardStrucIntervType', 'CardStrucIntervType', 'CardStrucIntervType', 'CardStrucIntervType', 'CardStrucIntervType', 'CardStrucIntervType', 'CardStrucIntervType', 'CardStrucIntervType', 'CardStrucIntervType', 'CardStrucIntervType', 'LAAOInterv', 'RankinScale', 'RankinScale', 'RankinScale', 'RankinScale', 'RankinScale', 'RankinScale', 'RankinScale', 'LAAOType', 'LAAOType', 'LAAOType', 'LAAOType', 'LAAOType', 'LAAOType', 'MedCond', 'MedCond', 'MedCond', 'MedCond', 'MedCond', 'MedCond', 'MedCond', 'MedCond', 'MedCond', 'EpicardialAppCons', 'LupusCons', 'ICEPerf', 'ICEDate', 'Sx_F', 'PCIOther']"
FELLOW,"['FITProgID', 'FIT_LastName', 'FIT_FirstName', 'FIT_MidName', 'FIT_NPI']"
HOSPEVEADJ,"['AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_AdjudEvent', 'AJ_EventDate', 'ADJ_NeuroAdjStatus', 'ADJ_NeuroAdjStatus', 'ADJ_DeathDate', 'ADJ_NeuroSxOnset', 'ADJ_NeuroDeficit', 'ADJ_NeuroClinicPresent', 'ADJ_NeuroClinicPresent', 'ADJ_NeuroDxConfirmed', 'ADJ_NeuroBrainImaging', 'ADJ_NeuroBrainImagingType', 'ADJ_NeuroBrainImagingType', 'ADJ_NeuroBrainImagingType', 'ADJ_NeuroBrainImagingType', 'ADJ_NeuroDeficitType', 'ADJ_NeuroDeficitType', 'ADJ_NeuroDeficitType', 'ADJ_NeuroDeficitType', 'ADJ_NeuroIntracranType', 'ADJ_NeuroIntracranType', 'ADJ_NeuroIntracranType', 'ADJ_NeuroIVrTPA', 'ADJ_NeuroEndoTheraInter', 'ADJ_NeuroSxDuration', 'ADJ_NeuroSxDuration', 'ADJ_NeuroSxDuration', 'ADJ_NeuroTrauma', 'ADJ_RankinScale', 'ADJ_RankinScale', 'ADJ_RankinScale', 'ADJ_RankinScale', 'ADJ_RankinScale', 'ADJ_RankinScale', 'ADJ_RankinScale', 'ADJ_RankinScaleNA', 'ADJ_NeuroProcRelated', 'ADJ_NeuroProcRelated', 'ADJ_NeuroProcRelated', 'ADJ_NeuroProcRelated', 'ADJ_NeuroProcRelated', 'ADJ_BleedRBCTransfusion', 'ADJ_BleedRBCUnits', 'ADJ_BleedPreTransHgb', 'ADJ_BleedImagePerf', 'ADJ_BleedEndOrganDamage', 'ADJ_BleedAdjStatus', 'ADJ_BleedAdjStatus', 'ADJ_BleedProcRelated', 'ADJ_BleedProcRelated', 'ADJ_BleedProcRelated', 'ADJ_BleedProcRelated', 'ADJ_BleedProcRelated', 'ADJ_BleedDevRelated', 'ADJ_BleedDevRelated', 'ADJ_BleedDevRelated', 'ADJ_BleedDevRelated', 'ADJ_BleedDevRelated', 'ADJ_BleedMajorSurgery', 'ADJ_BleedPCI', 'ADJ_BleedInvInter', 'ADJ_BleedDeathDate', 'ADJ_NeuroDevRelated', 'ADJ_NeuroDevRelated', 'ADJ_NeuroDevRelated', 'ADJ_NeuroDevRelated', 'ADJ_NeuroDevRelated', 'ADJ_SysThromboAdjStatus', 'ADJ_SysThromboAdjStatus', 'ADJ_SysThromboDeathDate', 'ADJ_SysThromboDeathCause', 'ADJ_SysThromboHypoperfusion', 'ADJ_SysThromboImagMethod', 'ADJ_SysThromboImagMethod', 'ADJ_SysThromboImagMethod', 'ADJ_SysThromboImagMethod', 'ADJ_SysThromboImagMethod', 'ADJ_SysThromboTheraInterv', 'ADJ_SysThromboIntervType', 'ADJ_SysThromboIntervType', 'ADJ_SysThromboIntervType', 'ADJ_SysThromboIntervType', 'ADJ_SysThromboImagEvidence']"
IPPEVENTS,"['PostProcOccurred', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'ProcEvents', 'IntraPostProcEventDate']"
OPRINFO,"['OperA_FirstName2', 'OperA_LastName2', 'OperA_MidName2', 'OperA_NPI2']"
PREPROCMED,"['MedID', 'MedID', 'MedID', 'MedID', 'MedID', 'MedID', 'MedID', 'MedID', 'MedID', 'MedID', 'MedID', 'MedID', 'MedID', 'MedID', 'MedID', 'MedID', 'MedID', 'MedID', 'MedID', 'MedID', 'PreMedAdmin', 'PreMedAdmin', 'PreMedAdmin', 'PreMedAdmin']"
PROCINFO,"['ProcedureStartDateTime', 'ProcedureEndDateTime', 'Anesthesia', 'Anesthesia', 'Anesthesia', 'Anesthesia', 'GuidanceMethodID', 'GuidanceMethodID', 'GuidanceMethodID', 'GuidanceMethodID', 'FluoroDoseKerm', 'FluoroDoseKerm', 'ContrastVol', 'IntraProcAnticoag', 'Warfarin', 'ProcedureLocation', 'ProcedureLocation', 'ProcedureLocation', 'ProcedureLocation', 'ProcedureLocation', 'FluoroDoseDAP2', 'FluoroDoseDAP2', 'FluoroDoseDAP2', 'FluoroDoseDAP2', 'FluoroDoseDAP2', 'SDM_Proc', 'SDM_Tool', 'SDM_Tool_Name', 'TEEPerfLAAO', 'TEEDateLAAO', 'LAAO_OrWid', 'ProcAborted', 'ProcAbortedReason', 'ProcAbortedReason', 'ProcAbortedReason', 'ProcAbortedReason', 'ProcAbortedReason', 'ProcAbortedReason', 'ProcAbortedReason', 'ProcAbortedReason', 'ProcAbortedReason', 'ProcAbortedReason', 'ProcAbortedReason', 'ProcAbortedReason', 'ProcAbortedReason', 'ProcCanceledReason', 'ProcCanceledReason', 'ProcCanceledReason', 'ProcCanceledReason', 'ProcCanceledReason', 'ProcCanceledReason', 'ProcCanceledReason', 'ProcCanceledReason', 'ProcCanceledReason', 'ProcCanceled', 'ProcLAAOInd', 'ProcLAAOInd', 'ProcLAAOInd', 'ProcLAAOInd', 'ProcLAAOInd', 'ProcLAAOInd', 'ProcLAAOInd', 'ProcAtrialThromDetect', 'OHSConversion', 'OHSConversionReason', 'OHSConversionReason', 'OHSConversionReason', 'OHSConversionReason', 'ResidualLeak', 'ResidualLeakNA', 'ProcHeparinInitAdminTime', 'ProcHeparinInitAdminTime', 'AnticoagReversal', 'ConcomitantProcPerf', 'ConcomitantProcType', 'ConcomitantProcType', 'ConcomitantProcType', 'ConcomitantProcType', 'ConcomitantProcType', 'ConcomitantProcType', 'ConcomitantProcType', 'ConcomitantProcType', 'PostProcCreatND2', 'PostProcPeakCreat', 'PostProcCreat2', 'PostProcPeakCreatND', 'PostProcHgb2', 'PostProcHgbND2', 'ProcOtherAnticoag2', 'ProcOtherAnticoag2', 'ProcHeparin2', 'ProcHeparin2', 'ProcBivalirudin2', 'ProcBivalirudin2']"
RSTUDY,"['StudyName', 'StudyPtID']"
