import os
import json
from cachetools import TTLCache, cached
import pandas as pd

from graphqlclient import GraphQLClient
from ingestion.utils import get_secret

ODIN_API_URL = os.getenv('ODIN_API_URL', 'https://odin.biome.io/')
AUTH_TOKEN_KEY = os.getenv('ODIN_API_AUTH_TOKEN_KEY', 'ingestion-bpn-token')
gqlClient = GraphQLClient(ODIN_API_URL + 'graphql')

ce_ttl_cache = TTLCache(100, ttl=21600)  # refresh every 6 hours


ce_query = """
    query CareEntities {
  careEntities {
    id
    name
    code
    alternateNames
    ids
    tenants {
      id
      code
    }
  }
}
"""


def get_gql_response(query, variables=dict()):
    token = get_secret(AUTH_TOKEN_KEY)
    gqlClient.inject_token('Bearer ' + token)
    response = gqlClient.execute(query, variables=variables)
    return json.loads(response)


@cached(cache=ce_ttl_cache)
def get_all_care_entities(client):
    response = get_gql_response(ce_query)
    ce = response.get('data').get('careEntities')
    ce = pd.DataFrame(ce).explode('tenants')
    ce = ce.dropna(subset=['tenants'])  # drop missing tenants for bad odin records

    ce['tenant_id'] = ce['tenants'].apply(lambda x: x['id'] if x else None)
    ce['tenant_code'] = ce['tenants'].apply(lambda x: x['code'] if x else None)

    ce = ce[ce['tenant_code'].str.lower() == client.lower()]

    ce2 = ce.explode('alternateNames').drop('name', axis=1).rename(columns={'alternateNames': 'name'})
    ce = pd.concat([ce.drop('alternateNames', axis=1), ce2],
                   ignore_index=True).dropna(subset=['name']).drop_duplicates(subset=['name'])
    # ING-860: Fix for missing 'ids' field
    # the ce['ids'] field is `None` for some records for WellSpan which were manually added via query, not UI
    ce['ids'] = ce['ids'].apply(lambda x: x if x else [])

    return ce


def get_care_entities(name, _id, client):
    """ Get care entities by part name or part id """
    ce = get_all_care_entities(client)
    cond = (ce['name'].str.lower().str.contains(name.lower())) | (ce['ids'].apply(lambda x: _id in x))
    return ce[cond]


def get_care_entity_code_map(client):
    ce = get_all_care_entities(client)
    ce_dict = ce.explode('ids').set_index('ids')[['code', 'id', 'tenant_id']].to_dict()
    return ce_dict
