from assimcli import APIClient
import random
import yaml
import os
import re
from hashlib import blake2b

import logging
from ingestion.utils.odin import get_all_care_entities


def infer_id_fields(df, key_field='EpisodeKey', visit_field='VisitId'):
    """
    Determine the unique id fields for the dataset based on episode<PERSON>ey and visitId.
    If episodeKey is 0, then the unique id is ncdrPatientId.
    If episodeKey is not 0, then the unique id is ncdrPatientId + episodeKey
    If visitId is not 0, then the unique id is ncdrPatientId + episodeKey + visitId
    """
    id_fields = ['NCDRPatientId']
    if key_field in df.columns:
        if (df[key_field].astype(str) == '0').all():
            df.drop(key_field, axis=1, inplace=True)
        else:
            id_fields.append(key_field)

        if visit_field in df.columns:
            if (df[visit_field].astype(str) == '0').all():
                df.drop(visit_field, axis=1, inplace=True)
            else:
                id_fields.append(visit_field)
    return id_fields


def get_file_name_from_path(file_path):
    """
    Get file name from a file path
    """
    if '\\' in file_path:
        return file_path.split('\\')[-1].rstrip('.xml')
    else:
        return file_path.split('/')[-1].rstrip('.xml')


def get_file_info(file_id, client):
    """
    Gets file info from the assimilation API
    """
    logging.info(f"Getting file info for file_id: {file_id}")
    api_client = APIClient().get_instance(client)
    cdf = api_client.get_client_file(file_id=file_id)
    return cdf


def create_table_name(file_info, dsm, hospname, period):
    """
    Create a table name with the following format: (yyyymmdd)_(dsm)_(hospname)_(period for anchordate range)_(random)
    """
    logging.info(f"Creating table name for file_id: {file_info['id']}")
    submission_date = file_info['data_received_date']

    random_num = (random.randint(0, 999) + 1) % 1000

    table_name = f"{submission_date}_{dsm}_{hospname}_{period}_{random_num}"
    table_name = table_name.replace(' ', '').replace('-', '').replace(':', '').lower()
    logging.info(f"Table name created: {table_name}")

    return table_name


def get_code_version():
    """
    Get the code version from the gitlab-ci.yml file
    """
    try:
        with open('.gitlab-ci.yml', 'r') as f:
            version = yaml.load(f, Loader=yaml.FullLoader)['variables']['PROJ_VER']
        return version
    except FileNotFoundError:
        logging.error("Unable to find .gitlab-ci.yml file. Trying ingestion3/.gitlab-ci.yml.")
        try:
            with open('ingestion3/.gitlab-ci.yml', 'r') as f:
                version = yaml.load(f, Loader=yaml.FullLoader)['variables']['PROJ_VER']
            return version
        except FileNotFoundError:
            logging.error("Also unable to find ingestion3/.gitlab-ci.yml file.")
            return None


def getenv(env_key, default_value=None):
    value = os.getenv(env_key, default_value)
    logging.info(f"Retrieved {env_key}: {value}")
    return value


def convert_dtypes_and_merge(left_df, right_df, keys, how, dtype=str, left_on=None, right_on=None):
    """
    Convert the columns in left_df and right_df to the specified dtype and merge them on the specified keys.
    """
    if left_on and right_on:
        for col in left_on:
            left_df[col] = left_df[col].astype(dtype)
        for col in right_on:
            right_df[col] = right_df[col].astype(dtype)
        return left_df.merge(right_df, left_on=left_on, right_on=right_on, how=how)
    else:
        for col in keys:
            left_df[col] = left_df[col].astype(dtype)
            right_df[col] = right_df[col].astype(dtype)
        return left_df.merge(right_df, on=keys, how=how)


def smart_merge(left_df, right_df, keys=None, how='left', dtype=str, left_on=None, right_on=None):
    """
    Merge the left_df and right_df on the specified keys.
    If the merge fails due to data type mismatch, convert the data types to string and try again.
    """
    try:
        if left_on and right_on:
            return left_df.merge(right_df, left_on=left_on, right_on=right_on, how=how)
        else:
            return left_df.merge(right_df, on=keys, how=how)
    except ValueError as e:
        logging.info('mismatch in data types for joining columns, converting to string')
        return convert_dtypes_and_merge(left_df, right_df, keys, how, dtype=dtype,
                                        left_on=left_on, right_on=right_on)


def uid(parts, digest_size=6):
    """ Create a very large unique numeric id. """
    # Prepare digest value as a string of hexadecimal digits
    h = blake2b(digest_size=digest_size)
    for part in parts:
        h.update(str(part).encode('ascii'))
    # Convert hexadecimal to base10 int
    return int(h.hexdigest(), 16)


def add_biomeencounterid(df, client, dataset, identifier='ncdrpatientid', mrn='otherid', anchordate='dischargedate'):

    # Prepare a unique key using system, dataset name, registry id, and anchor date
    df['biomeencounterid'] = df.apply(lambda r: uid([
        client.lower(),
        dataset.lower(),
        # Use mrn as a pseudo case-id if case id is empty
        r[identifier] if len(str(r[identifier])) > 0 else 'm:' + str(r[mrn]),
        # Use date part only from the anchor-date
        str(r[anchordate])[:10]
    ]), axis=1)
    return df


def find_hospital(client, filename):
    """add hosp-info in hospital field """
    hospital = None
    ce = get_all_care_entities(client)
    # check if it's a single hospital client
    if len(ce['code'].unique()) == 1:
        hospital = ce['code'][0]
    else:
        tokens = re.split(r"[\s,_\-]", filename)
        for hc in ce['code'].unique():
            if hc.upper() in tokens:
                hospital = hc
    #  todo: terminate ingestion if hospital is not found
    # if hospital is None:
    #     return False
    return hospital
