import pandas as pd
import re
import xlrd


def standardize_time_format(col):
    col = col.apply(lambda x: None if str(x) in ('', 'nan', 'None', 'NaT') else str(x))
    col_idx = col.first_valid_index()
    if col_idx is not None:
        matches = re.match("^\d{3,4}$|\d{3,4}\.0$", str(col[col_idx]))
        if matches is not None:
            # time conversion from army-format to HH:MM:SS format
            col = col.astype("str").str.replace("\.0", "")
            col = col.mask(col == "nan", None)
            col = col.mask(col == "None", None)
            col = col.mask(
                ~col.isna(), col.astype(float).astype('Int64').astype(str).str.pad(4, side="left", fillchar="0")
            )
            col = col.mask(
                ~col.isna(), col.str[:2] + ":" + col.str[2:4] + ":00"
            )
        elif re.match(
                "(\d{4}\-\d{2}\-\d{2}\s)*\d{1,2}:\d{1,2}(:\d{1,2})?\s?(am|pm)$",
                str(col[col_idx])):
            # time conversion from 12 hour 24
            # conversion from HH:MM:SS am|pm format to HH:MM:SS format
            # conversion from YYYY-MM-DD-h:mm:ss am|pm format to HH:MM:SS format
            col = pd.to_datetime(col, errors="coerce").dt.strftime("%H:%M:%S")
            col = col.astype(str).replace({"NaT": None, "nan": None})
        elif re.match("\d{1,2}:\d{1,2}(\s)?$", str(col[col_idx])):
            # time conversion from HH:MM and H:MM format to HH:MM:SS format
            col = col.astype("str").str.replace(" ", "")
            col = col.mask(col == "nan", None)
            col = col.mask(col == "None", None)
            col = col.mask(col != "None", col.str[:5] + ":00")
            col = col.mask(
                ~col.isna(), col.str.pad(8, side="left", fillchar="0"))
        elif re.match("\d{14}", str(col[col_idx])):
            # time conversion from YYYYMMDDhhmmss format to HH:MM:SS format
            col = col.astype("str").str.replace(" ", "")
            col = col.mask(col == "nan", None)
            col = col.mask(col == "None", None)
            col = col.mask(col == "", None)
            col = col.astype(str).str[-6:]
            col = col.mask(
                col != "None",
                col.str[:2] + ":" + col.str[2:4] + ":" + col.str[-2:])
            col = col.mask(col.astype("str") == "None", None)
        elif re.match("\d{12}", str(col[col_idx])):
            # time conversion from YYYYMMDDhhmm format to HH:MM:SS format
            col = col.astype("str").str.replace(" ", "")
            col = col.mask(col == "nan", None)
            col = col.mask(col == "None", None)
            col = col.mask(col == "", None)
            col = col.astype(str).str[-4:]
            col = col.mask(
                col != "None", col.str[:2] + ":" + col.str[2:4] + ":00"
            )
            col = col.mask(col.astype("str") == "None", None)
        elif re.match(
                "^\d{1,2}:\d{2}:\d{2}(\.\d+)*\-\d{1,2}:\d{2}", str(col[col_idx])):
            # time conversion from zone-format to HH:MM:SS format
            col = col.astype("str").str.replace(" ", "")
            col = col.mask(col == "nan", None)
            col = col.mask(col == "None", None)
            col = col.mask(col == "", None)
            col = col.astype(str).str[:8]
            col = col.mask(col.astype("str") == "None", None)

        elif str(pd.to_datetime(col, errors="coerce").dt.strftime("%H:%M:%S")[col_idx]) not in ('NaT', 'nan'):
            # get time from any valid date time format
            col = pd.to_datetime(col, errors="coerce").dt.strftime("%H:%M:%S")
        elif re.match(
                # Trinity ECHO format e.g. 03JAN24:15:51 -> 2024-01-03 15:51 (yyyy-mm-dd HH:MM)
                "^\d{2}[A-Z]{3}\d{2}:\d{2}:\d{2}", str(col[col_idx])):
            col = col.astype("str").str.replace(" ", "")
            col = col.mask(col == "nan", None)
            col = col.mask(col == "None", None)
            col = col.mask(col == "", None)
            col = pd.to_datetime(col, format='%d%b%y:%H:%M', errors='coerce').dt.strftime("%H:%M:%S")
        elif re.match(
                # Trinity ECHO format variation
                "^\d{2}[A-Z]{3}\d{4}:\d{2}:\d{2}:\d{2}\.\d{7}", str(col[col_idx])):
            col = col.astype("str").str.replace(" ", "")
            col = col.mask(col == "nan", None)
            col = col.mask(col == "None", None)
            col = col.mask(col == "", None)
            col = pd.to_datetime(col, format='%d%b%Y:%H:%M:%S.%f', errors='coerce').dt.strftime("%H:%M:%S")
    return col


def standardize_date_format(col, date_only=False):
    col_idx = col.first_valid_index()
    if col_idx is not None:
        matches = re.match("^\d{4,5}$", str(col[col_idx]))
        if matches is not None:
            # converting excel xldate to python datetime
            col = col.apply(lambda s: xlrd.xldate.xldate_as_datetime(s, 0))
        elif re.match(
                "\d{4}\-\d{2}-\d{2}(\s|T)\d{2}:\d{2}:\d{2}(\-|\+)\d{2}:\d{2}",
                str(col[col_idx])):
            # date conversion from zone-format to YYYY-mm-ddTHH:MM:SS format
            col = col.mask(col == "nan", None)
            col = col.mask(col == "None", None)
            col = col.mask(col == "", None)
            col = col.str[:19]
            col = pd.to_datetime(col, errors="coerce")
        elif re.match(
                # Trinity ECHO format e.g. 03JAN24:15:51 -> 2024-01-03 15:51 (yyyy-mm-dd HH:MM)
                "^\d{2}[A-Z]{3}\d{2}:\d{2}:\d{2}", str(col[col_idx])):
            col = col.mask(col == 'nan', None)
            col = col.mask(col == "None", None)
            col = col.mask(col == "", None)
            col = pd.to_datetime(col, format='%d%b%y:%H:%M', errors="coerce")
        elif re.match(
                # Trinity ECHO format variation
                "^\d{2}[A-Z]{3}\d{4}:\d{2}:\d{2}:\d{2}\.\d{7}", str(col[col_idx])):
            col = col.mask(col == 'nan', None)
            col = col.mask(col == "None", None)
            col = col.mask(col == "", None)
            col = pd.to_datetime(col, format='%d%b%Y:%H:%M:%S.%f', errors="coerce")
        elif re.match(r"^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$", str(col[col_idx])):
            # ISO 8601 format (YYYY-MM-DDTHH:MM:SS)
            col = col.mask(col == 'nan', None)
            col = col.mask(col == "None", None)
            col = col.mask(col == "", None)
            col = pd.to_datetime(col, format="%Y-%m-%dT%H:%M:%S", errors="coerce")
        elif re.match(r"^\d{1,2}/\d{1,2}/\d{4} \d{1,2}:\d{2}:\d{2} (AM|PM)$", str(col[col_idx]), re.IGNORECASE):
            # MM/DD/YYYY HH:MM:SS AM/PM format
            col = col.mask(col == 'nan', None)
            col = col.mask(col == "None", None)
            col = col.mask(col == "", None)
            col = pd.to_datetime(col, format="%m/%d/%Y %I:%M:%S %p", errors="coerce")
        elif re.match(r"^\d{1,2}/\d{1,2}/\d{4} \d{1,2}:\d{2} (AM|PM)$", str(col[col_idx]), re.IGNORECASE):
            # MM/DD/YYYY HH:MM AM/PM format
            col = col.mask(col == 'nan', None)
            col = col.mask(col == "None", None)
            col = col.mask(col == "", None)
            col = pd.to_datetime(col, format="%m/%d/%Y %I:%M %p", errors="coerce")
        elif re.match(r"^\d{14}$", str(col[col_idx])):
            # YYYYMMDDHHMMSS format
            col = col.mask(col == 'nan', None)
            col = col.mask(col == "None", None)
            col = col.mask(col == "", None)
            col = pd.to_datetime(col, format="%Y%m%d%H%M%S", errors="coerce")
        elif re.match(r"^\d{12}$", str(col[col_idx])):
            # YYYYMMDDHHMM format
            col = pd.to_datetime(col, format="%Y%m%d%H%M", errors="coerce")

        else:
            col = pd.to_datetime(col, errors="coerce")
        col = col.dt.strftime('%Y-%m-%dT%H:%M:%S')
    return col


def standardize_date_only_format(col):
    col_idx = col.first_valid_index()
    if col_idx is not None:
        matches = re.match("^\d{4,5}$", str(col[col_idx]))
        if matches is not None:
            col = col.apply(lambda s: xlrd.xldate.xldate_as_datetime(s, 0))
        elif re.match(
                # Trinity ECHO format e.g. 03JAN24:15:51 -> 2024-01-03 15:51 (yyyy-mm-dd HH:MM)
                "^\d{2}[A-Z]{3}\d{2}:\d{2}:\d{2}", str(col[col_idx])):
            col = col.mask(col == 'nan', None)
            col = col.mask(col == "None", None)
            col = col.mask(col == "", None)
            col = pd.to_datetime(col, format='%d%b%y:%H:%M', errors="coerce")
        elif re.match(
                # Trinity ECHO format variation
                "^\d{2}[A-Z]{3}\d{4}:\d{2}:\d{2}:\d{2}\.\d{7}", str(col[col_idx])):
            col = col.mask(col == 'nan', None)
            col = col.mask(col == "None", None)
            col = col.mask(col == "", None)
            col = pd.to_datetime(col, format='%d%b%Y:%H:%M:%S.%f', errors="coerce")
        else:
            col = pd.to_datetime(col, errors="coerce")
            col = col.astype(str).str.split(" ", expand=True)[0]
            col = pd.to_datetime(col, errors="coerce")
        col = col.dt.normalize().dt.strftime('%Y-%m-%dT%H:%M:%S')
    return col
