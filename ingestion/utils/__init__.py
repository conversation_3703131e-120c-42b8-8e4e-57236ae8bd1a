import functools
import logging
import os
import time
from azure.identity import DefaultAzureCredential
from azure.keyvault.secrets import SecretClient

logging.basicConfig(
    format='%(asctime)s %(message)s',
    datefmt='%m/%d/%Y %I:%M:%S %p'
)
logging.root.setLevel(logging.INFO)

vault_client = None

AZURE_VAULT_NAME = os.getenv('VAULT_NAME', 'biome-apps-prod')


def logger(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Log function start
        logging.info(f"Function {func.__name__} started")

        # Record the start time
        start_time = time.time()

        # Call the original function
        result = func(*args, **kwargs)

        # Record the end time
        end_time = time.time()

        # Log function end and time taken
        logging.info(f"Function {func.__name__} completed in {end_time - start_time:.4f} seconds")

        return result

    return wrapper


def get_secret(key):
    global vault_client
    if vault_client is None:
        credential = DefaultAzureCredential()
        uri = f"https://{AZURE_VAULT_NAME}.vault.azure.net"
        vault_client = SecretClient(uri, credential=credential)
    return str(vault_client.get_secret(key).value)


def timeit(method):
    def timed(*args, **kw):
        ts = time.time()
        result = method(*args, **kw)
        te = time.time()
        logging.info(f"{method.__name__} took {te - ts} seconds")
        return result
    return timed
