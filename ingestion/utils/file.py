from azure.core.exceptions import ResourceNotFoundError
from azure.identity import DefaultAzureCredential
from azure.storage.blob import BlobClient
import logging
import os
from urllib.parse import urlparse
import xml.etree.ElementTree as et
from assimcli import APIClient


logging.basicConfig(
    format='%(asctime)s %(message)s',
    datefmt='%m/%d/%Y %I:%M:%S %p'
)
logging.root.setLevel(logging.INFO)

FILE_INPUT_LOCATION = './phi/input'
FILE_OUTPUT_LOCATION = './phi/output'
os.makedirs(FILE_INPUT_LOCATION, exist_ok=True)
os.makedirs(FILE_OUTPUT_LOCATION, exist_ok=True)


class File:
    def __init__(self, path, file_id=None):
        self.url = None
        self.local_path = None
        self.file_id = file_id

        parsed_path = urlparse(path)
        if parsed_path.scheme in ["http", "https"]:
            self.url = path
        else:
            if os.path.isfile(path):
                self.local_path = path
            else:
                raise ValueError("The path provided is neither a valid URL nor a local file path.")

        self.file_name = os.path.basename(path)  # this works with an URL or local path
        self.submitted_file_name = ''.join(path.split("/")[-1].split("__")[4::])
        self.extension = path.split(".")[-1].lower()
        self.submission_date = path.split("/")[-1].split("__")[0]
        self.submitter = path.split("/")[-1].split("__")[1]
        self.tenant = path.split("/")[-1].split("__")[2]

        self.dataset_type = None

    def download(self):
        # download the file from Azure Blob Storage
        if self.url is not None:
            try:
                logging.info(f"Downloading file: {self.url}")
                creds = DefaultAzureCredential()
                blob_client = BlobClient.from_blob_url(self.url, credential=creds)
                local_file_path = os.path.join(FILE_INPUT_LOCATION, self.file_name)
                print(os.getcwd())
                with open(local_file_path, "wb") as f:
                    data = blob_client.download_blob()
                    data.readinto(f)
                logging.debug(f'Downloaded {self.url} to {FILE_INPUT_LOCATION} complete.')

                self.local_path = local_file_path
                return local_file_path
            except ResourceNotFoundError:
                logging.error(f"File not found in blob storage: {self.url}")
                return None
        else:
            logging.info("Attempted to download file but it is "
                         f"local, returning local file path: {self.local_path}")
            return self.local_path

    def get_size(self):
        if self.local_path.startswith(('http://', 'https://')):
            return -1
        else:
            try:
                return os.path.getsize(self.local_path)
            except FileNotFoundError:
                return -1

    def get_dataset_type(self):
        if self.extension.lower() == 'xml':
            # Taking registry id from the NCDR files by default
            # Then checking for the coded section that tells if it is Episode of Care or Follow Up Records
            # if the file says it is a follow up file based on the code ********** then verify based on content of file
            try:
                matadata = et.parse(self.local_path)
                self.root = matadata.getroot()
                submission = self.root.find("submission")
                if submission is not None:
                    registry_id = submission.find("./section/element[@code='2.16.840.1.113883.3.3478.4.841']/value")

                    if registry_id is not None:
                        self.dataset_type = registry_id.attrib.get('value', '')

                    element = submission.find(".//element[@code='1000142423']")
                    if element is not None:
                        value_element = element.find(".//value[@code='**********']")
                        if value_element is not None:
                            submission_type = value_element.attrib['code']
                            display_name = value_element.attrib['displayName']
                        else:
                            submission_type = None
                            display_name = None
                    else:
                        submission_type = None
                        display_name = None

                    patient = self.root.find("patient")
                    if patient is not None:
                        episode_tags_exist = patient.find("episode")
                        followup_tags_exist = patient.find("followup")

                        if str(submission_type) == '**********':
                            if episode_tags_exist is not None:
                                pass
                            elif followup_tags_exist is not None:
                                self.dataset_type = f"{self.dataset_type}-FUP"
                        elif followup_tags_exist:
                            self.dataset_type = f"{self.dataset_type}-FUP"
                    else:
                        # TODO:This should raise an error that is handled in case the files only contain submission info
                        pass

                    return self.dataset_type
                else:
                    pass  # TODO: this is for LAAO 1.3 and other old registries
            except et.ParseError:
                logging.error(f"Unable to parse file: {self.local_path}")
                return None

        if self.extension in ('csv', 'txt'):
            from ingestion.readers.csv import CSVMeta
            csv_meta = CSVMeta(path=self.local_path, client=self.tenant, file_id=self.file_id)
            csv_meta.execute()
            self.dataset_type = csv_meta.dataset
            return self.dataset_type

        if self.extension in ('xlsx', 'xls', 'ods'):
            from ingestion.readers.excel import BaseReaderExcel
            reade_excel = BaseReaderExcel(path=self.local_path, client=self.tenant, file_id=self.file_id)
            reade_excel.execute()
            self.dataset_type = reade_excel.dataset
            return self.dataset_type

        # TODO: add more file types


class FileInfo:

    def __init__(self, file_id, client):
        self.file_id = file_id
        self.client = client
        self.file_info = None
        self.api_client = None

    def get_api_client(self):
        if self.api_client is None:
            self.api_client = APIClient.get_instance(self.client)

        return self.api_client

    def get_file_info(self):
        """
        Gets file info from the assimilation API
        """
        logging.info(f"Getting file info for file_id: {self.file_id}")
        cdf = self.get_api_client().get_client_file(file_id=self.file_id)
        self.file_info = cdf
        return cdf

    def add_file_info(self, output, anchor_date_field, target_db, target_table, registry_subset, hosp_field='hospname'):
        if hosp_field in output.columns:
            self.file_info['hospital'] = ','.join(output[hosp_field].astype(str).unique())
        else:
            self.file_info['hospital'] = None

        if anchor_date_field is None:
            self.file_info['data_start_date'] = None
            self.file_info['data_end_date'] = None
        else:
            self.file_info['data_start_date'] = str(output[anchor_date_field].min())
            self.file_info['data_end_date'] = str(output[anchor_date_field].max())

        self.file_info['table_name'] = target_table
        self.file_info['database_name'] = target_db
        self.file_info['row_count'] = output.shape[0]
        self.file_info['registry_subset'] = registry_subset

    def update_client_data_file(self):
        """
        Update the client data file with new attributes. Add the new attributes to the existing file info.
        """
        if os.getenv('UPDATE_CDF', 'no').lower() in ['no', 'false', '0']:
            logging.info(f"UPDATE_CDF is set to {os.getenv('UPDATE_CDF')}, skipping updating client data file.")
        else:
            logging.info(f"Updating client data file for file_id: {self.file_id}")
            self.get_api_client().update_client_file(file_id=self.file_id, data=self.file_info)
            logging.info(f"Client data file updated for file_id: {self.file_id}")

    def find_dataset(self, header_cols):
        header_cols = list(map(lambda x: x.upper().replace('"', '').replace("'", ''), header_cols))
        dataset = self.get_api_client().findsubset(header_cols)[0]
        logging.info(f"Dataset found: {dataset}")
        return dataset


if __name__ == "__main__":
    # file = File('https://biomeclientdataphi.blob.core.windows.net/ingestiondata' +
    #             '/manual-processing/20230919__SutterAuto__Sutter__Multi__SSRRH_CathPCI_23Q2_COMPLETE.xml')
    file = File('./phi/input/20230919__SutterAuto__Sutter__Multi__SSRRH_CathPCI_23Q2_COMPLETE.xml')
    print(file.download())
    print(file.get_size())
    print(file.get_dataset_type())
