import os
import pandas as pd
import sqlalchemy
# from sqlalchemy.orm import sessionmaker
import logging
from urllib.parse import quote_plus
from mysql import connector

from ingestion.utils import get_secret

# TODO: can we just do lowercase everything?
import warnings

# warnings.filterwarnings('ignore', message="The provided table name .* is not found exactly as such in the database")
warnings.filterwarnings('ignore', message="The provided table name .* is not found exactly as such in the database")

logging.basicConfig(
    format='%(asctime)s %(message)s',
    datefmt='%m/%d/%Y %I:%M:%S %p'
)
logging.root.setLevel(logging.INFO)

engine = None


def get_db_credentials(encoded=True):
    assimilation_db_user = os.getenv("ASSIMILATION_DB_USER", None)
    assimilation_db_password = os.getenv("ASSIMILATION_DB_PASSWORD", None)
    assimilation_db_username_key = os.getenv(
        "ASSIMILATION_DB_USERNAME_KEY", "assimilation-db-user-prod"
    )
    assimilation_db_password_key = os.getenv(
        "ASSIMILATION_DB_PASSWORD_KEY", "assimilation-db-password-prod"
    )

    user = assimilation_db_user if assimilation_db_user else get_secret(assimilation_db_username_key)
    pw = assimilation_db_password if assimilation_db_password else get_secret(assimilation_db_password_key)
    host = os.environ.get('DB_HOST', '')

    if encoded:
        return quote_plus(user), quote_plus(pw), host
    return user, pw, host


def create_db_engine(db):
    encoded_user, encoded_pw, host = get_db_credentials(encoded=True)

    connection_string = f'mysql+mysqlconnector://{encoded_user}:{encoded_pw}@{host}/{db}'

    return sqlalchemy.create_engine(connection_string,
                                    connect_args={'connect_timeout': 10000, 'compress': True},
                                    pool_pre_ping=True)


def get_db_engine(db):
    global engine
    if engine:
        if engine.url.database.lower() == db.lower():
            return engine.execution_options(rollback_on_exit=True)
        else:
            engine.dispose()
            engine = create_db_engine(db)
    else:
        engine = create_db_engine(db)
    return engine.execution_options(rollback_on_exit=True)


def insert_data(df, table_name, db, if_exists='append'):
    if hasattr(df, 'to_pandas'):
        df = df.to_pandas()
    if not df.empty:
        logging.info(f'Inserting data into {table_name}')
        logging.info(f'data shape: {df.shape}')
        engine = get_db_engine(db)
        try:
            with engine.begin() as connection:
                df.to_sql(table_name, if_exists=if_exists, schema=db, con=connection, index=False, chunksize=100000)
            logging.info(f'Data inserted into {table_name}')
        except Exception as e:
            logging.error(f'Failed to insert data into {table_name}')
            logging.error(str(e))
            raise
    else:
        logging.info(f'No data to insert into {table_name}')


def read_sql(sql, db='DB_EARASS'):
    db_con = get_db_engine(db).connect()
    df = pd.read_sql(sql, con=db_con)
    db_con.close()
    return df


def create_table(schema, table_name, db='DB_EARASS'):
    logging.info(f'Creating table {table_name}')
    fields = (schema['Field'].apply(lambda x: f'`{x}`') + ' ' + schema['Dtype']).tolist()
    sql = f'Create Table {table_name} ( {", ".join(fields)} )'
    try:
        execute_sql(f'DROP TABLE IF EXISTS {table_name}', db=db)
        execute_sql(sql, db=db)
        logging.info(f'Table {table_name} created')
    except Exception as e:
        logging.error(f'Failed to create table {table_name}')
        logging.error(str(e))
        raise


# def execute_sql(sql, db='DB_EARASS', multi_statements=False):
#     """
#     Given a query, it is executed in the database
#     """
#
#     conn = None
#
#     try:
#         engine = get_db_engine(db)
#         session = sessionmaker(bind=engine)
#
#         if multi_statements:
#
#             session = session()
#             conn = session.connection().connection
#             cursor = conn.cursor()
#             cursor.execute(sql, multi=True)
#
#         else:
#             conn = engine.connect()
#             conn.execute(sql)
#
#     except Exception as error:
#         logging.exception(str(error))
#         raise
#
#     finally:
#         if conn is not None:
#             conn.close()


def delete_existing_data(data, table_name, db):
    """
    Check if the table exists in the database
    Delete data from the NCDR table if the distinct ClientFileId, FileName, and Version are present in the data
    """
    if not data.empty:
        logging.info(f'Deleting existing data from {table_name} for the given ClientFileId, FileName, and Version')

        sql = (f"SELECT TABLE_NAME FROM information_schema.tables "
               f"WHERE table_schema = '{db}' AND table_name = '{table_name}'")
        table_exists = read_sql(sql, db)
        if table_exists.empty:
            logging.info(f'Table {table_name} does not exist in the database')
        else:
            distinct = data[['ClientFileId', 'FileName', 'Version']].drop_duplicates()
            delete_quries = []
            for ind, row in distinct.iterrows():
                sql = (f'DELETE FROM {table_name} '
                       f'WHERE ClientFileId = "{row["ClientFileId"]}" '
                       f'AND FileName = "{row["FileName"]}" AND Version = "{row["Version"]}";')
                delete_quries.append(sql)
            try:
                execute_sql(' '.join(delete_quries), db=db, multi_statements=True)
                logging.info(f'Deleted existing data from {table_name} for the given ClientFileId, FileName, and '
                             f'Version')
            except Exception as e:
                logging.error(f'Failed to delete existing data from {table_name}')
                logging.error(str(e))
                raise


def execute_sql(sql, db='DB_EARASS', multi_statements=True):
    user, password, host = get_db_credentials(encoded=False)
    connection = connector.connect(
        host=host,
        user=user,
        password=password,
        database=db,
        # connect_timeout=60,
        # command_timeout=120,
        # mysql_connection_timeout=300
    )
    cursor = connection.cursor()
    results = []

    try:
        for result in cursor.execute(sql, multi=True):
            results.append(result)
            if result.with_rows:
                logging.info(f"Rows affected: {result.rowcount}")

        connection.commit()

        return results

    except connector.Error as e:
        logging.error(f"MySQL Error: {e}")
        connection.rollback()
        raise
    except Exception as ex:
        logging.error(f"Unexpected error: {ex}")
        connection.rollback()
        raise
    finally:
        cursor.close()
        connection.close()
        logging.info("SQL Execution Complete")


def disable_keys(table_name, db):
    logging.info(f'Disabling keys for table {table_name}')
    sql = f"ALTER TABLE {table_name} DISABLE KEYS;"
    execute_sql(sql, db)


def enable_keys(table_name, db):
    logging.info(f'Enabling keys for table {table_name}')
    sql = f"ALTER TABLE {table_name} ENABLE KEYS;"
    execute_sql(sql, db)
