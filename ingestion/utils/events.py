from azure.identity import EnvironmentCredential
from azure.eventgrid import EventGridPublisherClient
import logging
import os


PUBLISH_EVENT_ENVIRONMENT_KEY = "PUBLISH_EVENT_ENVIRONMENT"
PUBLISH_EVENT_ENVIRONMENT_DEFAULT = "biome.ingestion."
event_type_base = os.getenv(PUBLISH_EVENT_ENVIRONMENT_KEY, PUBLISH_EVENT_ENVIRONMENT_DEFAULT)


def build_event(subject, data, event_type, event_time=None, data_version='0.9', timestamp_format="%Y%m%d-%H%M%S%f"):
    return {
        'id': event_type + '-' + data['client'] + '-' + event_time.strftime(timestamp_format),
        'subject': subject,
        'data': data,
        'eventType': event_type_base + event_type,
        'eventTime': event_time,
        'dataVersion': data_version
    }


def publish_event(publish, topic_endpoint, event_content=None):
    try:
        if publish:
            creds = EnvironmentCredential()
            publisher_client = EventGridPublisherClient(topic_endpoint, creds)
            publisher_client.send([event_content])

            logging.info(f"Event successfully published to {topic_endpoint}")
            logging.info(f'Event content: {event_content}')
        else:
            logging.info('Event not published because azure_publishing is set to False')
            logging.info(f'Event content: {event_content}')
    except Exception as e:
        logging.error(f'Error publishing event: {e}')
