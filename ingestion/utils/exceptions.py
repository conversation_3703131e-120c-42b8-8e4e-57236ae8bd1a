class ValidationError(Exception):
    """
    Exception raised for input data validation errors, for a client and file
    """

    def __init__(self, client, file):
        self.client = client
        self.file = file
        self.message = f"Validation failed for client {self.client} and file {self.file}"
        super().__init__(self.message)


class RequiredFieldsError(Exception):
    """
    Exception raised for missing fields in the input data
    """

    def __init__(self, missing_fields_info):
        self.missing_fields_info = missing_fields_info
        self.message = f"Following required fields are missing: {self.missing_fields_info} "
        super().__init__(self.message)


class HospitalRequiredError(Exception):
    """
    Exception raised for missing hospital information in the input data
    """

    def __init__(self, missing_fields_info):
        self.missing_fields_info = missing_fields_info
        self.message = "Could not find hospital information"
        super().__init__(self.message)
