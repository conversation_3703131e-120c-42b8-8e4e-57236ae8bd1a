import logging

from ingestion.readers import BaseReader
from ingestion.utils.db import insert_data, delete_existing_data
from ingestion.schema.cpmi import SchemaCPMI31, SchemaCPMI30

logging.basicConfig(
    format='%(asctime)s %(message)s',
    datefmt='%m/%d/%Y %I:%M:%S %p'
)
logging.root.setLevel(logging.INFO)


class BaseReaderCPMI(BaseReader):
    """
    Base class for CPMI reader
    """

    def __init__(self, filepath, schema, schema_class, write=False, target_db='DB_EARASS', client=None, file_id=None):
        super().__init__(filepath, schema_class.DATA_DICT, dataset=schema_class.DATASET,
                         dataset_id=schema_class.DATASET_ID, version=schema_class.VERSION,
                         client=client, file_id=file_id)

        if isinstance(schema, schema_class):
            self.schema = schema
        else:
            logging.info('Schema class instance not provided, creating schema from data dictionary')
            self.schema = schema_class()
            self.schema.execute()

        self.write = write
        self.file_id = file_id
        self.ncdr_tables = None
        self.target_db = target_db

    def execute(self):
        BaseReader.execute(self)
        table_prefix = self.schema.TABLE_PREFIX
        if self.write:
            for table_name, data in self.ncdr_tables.items():
                full_table_name = table_prefix + table_name
                delete_existing_data(data, table_name=full_table_name, db=self.target_db)
                insert_data(data, table_name=full_table_name, db=self.target_db, if_exists='append')


class ReaderCPMI30(BaseReaderCPMI):
    """
    Derived class for CPMI30
    """

    def __init__(self, filepath, schema, write=False, target_db='DB_EARASS', client=None, file_id=None):
        super().__init__(filepath, schema, SchemaCPMI30, write, target_db, client, file_id)


class ReaderCPMI31(BaseReaderCPMI):
    """
    Derived class for CPMI31
    """

    def __init__(self, filepath, schema, write=False, target_db='DB_EARASS', client=None, file_id=None):
        super().__init__(filepath, schema, SchemaCPMI31, write, target_db, client, file_id)
