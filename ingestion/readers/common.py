import logging

import pandas as pd

from ingestion.utils.db import read_sql


def replicate_episodes_visits(df):
    """
    Replicate episode and visit information for rows with 0 episode or visit id based on the same patient.
    """
    episodes = df[['ncdrPatientId', 'episodeKey']]
    episodes = episodes[episodes['episodeKey'] != 0]
    episodes = episodes.drop_duplicates()

    zero_epi_cond = df['episodeKey'] == 0
    df_zero_epi = df[zero_epi_cond].drop('episodeKey', axis=1)
    df_other = df[~zero_epi_cond]

    df_zero_epi = df_zero_epi.merge(episodes, on=['ncdrPatientId'])
    df = pd.concat([df_zero_epi, df_other])

    visits = df[['ncdrPatientId', 'episodeKey', 'visitId']]
    visits = visits[visits['visitId'] > 0]
    visits = visits.drop_duplicates()

    zero_visit_cond = df['visitId'] == 0
    df_zero_visit = df[zero_visit_cond].drop('visitId', axis=1)
    df_other = df[~zero_visit_cond]

    df_zero_visit = df_zero_visit.merge(visits, on=['ncdrPatientId', 'episodeKey'])
    return pd.concat([df_zero_visit, df_other])


def convert_columns(df):
    """
    Convert columns to nullable integer type if all non-NaN entries are integers.
    This is to avoid the integer columns being converted to float when there are NaN values.
    :param df:
    :return:
    """
    for column in df.columns:
        if df[column].dtype == float:
            # Check if all non-NaN entries are integers
            if all(df[column].dropna().apply(float.is_integer)):
                # Convert to 'Int64' (nullable integer type)
                df[column] = df[column].astype('Int64')
    return df


class ReadNCDRTables:

    """
    Read NCDR tables from db
    """

    def __init__(self, db, table_prefix, file_id):
        self.db = db
        self.table_prefix = table_prefix
        self.file_id = file_id
        self.ncdr_tables = {}

    def read(self):
        """
        Get all tables from the db where the table name starts with the table_prefix.
        Store the data in a dictionary with the table name as the key and the data as the value.
        """
        logging.info(f"Reading NCDR tables from {self.db}")
        sql = (f"SELECT TABLE_NAME FROM information_schema.tables WHERE table_schema = '{self.db}' "
               f"AND table_name LIKE '{self.table_prefix}%'")
        tables = read_sql(sql, self.db)
        tables = tables['TABLE_NAME'].tolist()
        self.ncdr_tables = {}
        for table in tables:
            logging.info(f"loading table: {table}")
            section_name = table.split('_')[-1].upper()
            sql = f"SELECT * FROM {table} WHERE ClientFileId = '{self.file_id}'"
            df = read_sql(sql, self.db)
            self.ncdr_tables[section_name] = convert_columns(df)
        logging.info("NCDR tables loaded")

    def execute(self):
        self.read()


class ReadRawTable:

    """
    Read raw tables from db
    """

    def __init__(self, db, table_name, file_id):
        self.db = db
        self.table_name = table_name
        self.file_id = file_id
        self.out = pd.DataFrame()

    def read(self):
        logging.info(f"Reading raw table {self.table_name} from {self.db}")
        sql = f"SELECT * FROM {self.table_name} WHERE ClientFileId = '{self.file_id}'"
        self.out = read_sql(sql, self.db)
        logging.info("Raw table loaded")

    def execute(self):
        self.read()
