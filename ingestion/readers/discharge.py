import logging
from ingestion.readers.admin import BaseReaderAdmin
from ingestion.schema.discharge import SchemaDischarge


class ReaderDischarge(BaseReaderAdmin):
    def __init__(self, filepath, schema, write=False, target_db='DB_MBA', client=None, file_id=None):

        if isinstance(schema, SchemaDischarge):
            self.schema = schema
        else:
            logging.info('Schema class instance not provided, creating schema from data dictionary')
            self.schema = SchemaDischarge()
            self.schema.execute()

        super().__init__(filepath, self.schema, target_table=SchemaDischarge.TABLE_PREFIX,
                         dataset_id=SchemaDischarge.DATASET_ID,
                         dataset=SchemaDischarge.DATASET,
                         write=write, target_db=target_db, client=client, file_id=file_id)
