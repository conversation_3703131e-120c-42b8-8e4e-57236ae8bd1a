import pandas as pd
import logging

from ingestion.readers import BaseReader
from ingestion.utils.db import insert_data, delete_existing_data
from ingestion.schema.icd import SchemaICD23, SchemaICD30

logging.basicConfig(
    format='%(asctime)s %(message)s',
    datefmt='%m/%d/%Y %I:%M:%S %p'
)
logging.root.setLevel(logging.INFO)


class BaseReaderICD(BaseReader):
    """
    Base class for ICD reader
    """

    def __init__(self, filepath, schema, schema_class, device_names, write=False, target_db="DB_ASHISH",
                 client=None, file_id=None):
        super().__init__(filepath, schema_class.DATA_DICT, dataset=schema_class.DATASET,
                         dataset_id=schema_class.DATASET_ID, version=schema_class.VERSION,
                         client=client, file_id=file_id)

        if isinstance(schema, schema_class):
            self.schema = schema
        else:
            logging.info('Schema class instance not provided, creating schema from data dictionary')
            self.schema = schema_class()
            self.schema.execute()

        self.df_dates = pd.DataFrame()
        self.write = write
        self.ncdr_tables = None
        self.target_db = target_db
        self.device_names = device_names

    def add_device_name(self):

        implant_dev = self.device_names[0].lower()
        explant_dev = self.device_names[1].lower()

        c1 = self.df_raw['section'].str.lower() == implant_dev
        c2 = self.df_raw['codeField'] == '2.16.840.1.113883.3.3478.6.1.21'
        dev_df1 = self.df_raw[c1 & c2].copy()
        dev_df1['valueDataDict'] = dev_df1['valueDisplayName'].astype(str)
        dev_df1['shortName'] = 'ICDImpModelName'

        c1 = self.df_raw['section'].str.lower() == explant_dev
        c2 = self.df_raw['codeField'] == '2.16.840.1.113883.3.3478.6.1.21'
        dev_df2 = self.df_raw[c1 & c2].copy()
        dev_df2['valueDataDict'] = dev_df2['valueDisplayName'].astype(str)
        dev_df2['shortName'] = 'ExplantDeviceName'

        c1 = self.df_raw['section'].str.lower() == 'leads'
        c2 = self.df_raw['codeField'] == '2.16.840.1.113883.3.3478.6.1.20'
        dev_df3 = self.df_raw[c1 & c2].copy()
        dev_df3['valueDataDict'] = dev_df3['valueDisplayName'].astype(str)
        dev_df3['shortName'] = 'LeadModelName'
        self.df_raw = pd.concat([self.df_raw, dev_df1, dev_df2, dev_df3], ignore_index=True)

    def execute(self):
        if self.schema is None:
            self.gen_schema()
        self.get_submission_info()
        self.gen_parent_child_sections()
        self.get_procedure_section_if_any()
        self.parse_patients()
        self.join_data_dict()
        self.add_device_name()
        self.add_field_suffix()
        self.map_values()
        self.get_file_info()
        self.slice_tables()
        self.update_file_info()

        table_prefix = self.schema.TABLE_PREFIX
        if self.write:
            for table_name, data in self.ncdr_tables.items():
                table_name = table_prefix + table_name
                delete_existing_data(data, table_name=table_name, db=self.target_db)
                insert_data(data, table_name=table_name, db=self.target_db, if_exists='append')


class ReaderICD23(BaseReaderICD):
    """
    Derived class for ICD 23
    """

    def __init__(self, filepath, schema, write=False, target_db='DB_ASHISH', client=None, file_id=None):

        device_names = ['implantdevice', 'explantdevice']

        super().__init__(filepath, schema, SchemaICD23, device_names, write, target_db, client, file_id)


class ReaderICD30(BaseReaderICD):
    """
    Derived class for ICD 30
    """

    def __init__(self, filepath, schema, write=False, target_db='DB_ASHISH', client=None, file_id=None):

        device_names = ['implantdev', 'explantdev']

        super().__init__(filepath, schema, SchemaICD30, device_names, write, target_db, client, file_id)
