import logging
from ingestion.readers.admin import BaseReaderAdmin
from ingestion.schema.procedure import SchemaProcedure


class ReaderProcedure(BaseReaderAdmin):
    def __init__(self, filepath, schema, write=False, target_db='DB_EARASS', client=None, file_id=None):

        if isinstance(schema, SchemaProcedure):
            self.schema = schema
        else:
            logging.info('Schema class instance not provided, creating schema from data dictionary')
            self.schema = SchemaProcedure()
            self.schema.execute()

        super().__init__(filepath, self.schema, target_table=SchemaProcedure.TABLE_PREFIX,
                         dataset_id=SchemaProcedure.DATASET_ID,
                         dataset=SchemaProcedure.DATASET,
                         write=write, target_db=target_db, client=client, file_id=file_id)
