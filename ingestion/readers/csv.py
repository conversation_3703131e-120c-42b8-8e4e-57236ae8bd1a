import polars as pl
import chardet
from io import BytesIO
import logging
from typing import Optional

from ingestion.utils.db import get_db_engine
from ingestion.utils.file import FileInfo


class CSVMeta:
    def __init__(self, path: str, client: Optional[str] = None, file_id: Optional[int] = None,
                 dataset: Optional[str] = None):
        self.path = path
        self.client = client
        self.file_id = file_id
        self.delimiter = None
        self.header = None
        self.dataset = dataset
        self.encoding = None
        self.raw_bytes = None
        self.raw_data = None
        self.raw_bytes_utf8 = None
        self.line_ending = None

    def read_raw_bytes(self):
        with open(self.path, 'rb') as f:
            self.raw_bytes = f.read()

    def detect_encoding_and_convert_utf8(self):
        if not self.encoding:
            result = chardet.detect(self.raw_bytes)
            self.encoding = result['encoding']
        self.raw_data = self.raw_bytes.decode(self.encoding)
        if self.encoding != 'utf-8':
            self.raw_bytes_utf8 = self.raw_data.encode('utf-8')
        else:
            self.raw_bytes_utf8 = self.raw_bytes

        """Detect the line ending in the byte data"""
        sample = self.raw_bytes_utf8[:min(1000, len(self.raw_bytes_utf8))].decode('utf-8')
        if '\r\n' in sample:
            self.line_ending = b'\r\n'
        elif '\r' in sample:
            self.line_ending = b'\r'
        else:
            self.line_ending = b'\n'

        logging.info(f'line ending in file: {self.line_ending}')

    def read_header(self):
        self.header = self.raw_data.splitlines()[0]
        return self.header

    def find_delimiter(self, possible_delimiters=['\t', ';', '|', ':', ','], n_lines=10):
        """
        Guess the delimiter from the file
        :param possible_delimiters: List of possible delimiters to check
        :param n_lines: Number of lines to check for delimiter if not found in header
        :return: The determined delimiter
        """

        def check_delimiter(line, delimiters):
            for delimiter in delimiters:
                if delimiter in str(line):
                    return delimiter
            return None

        if self.delimiter:
            return self.delimiter

        # Check the header first
        self.delimiter = check_delimiter(self.header, possible_delimiters)
        if self.delimiter:
            return self.delimiter

        # If not found in header, check the first n lines from the file at self.path
        lines = self.raw_data.splitlines()
        for i in range(n_lines):
            line = lines[i]
            self.delimiter = check_delimiter(line, possible_delimiters)
            if self.delimiter:
                return self.delimiter

        raise ValueError('Unable to determine delimiter. Please specify manually.')

    def identify_dataset(self):
        if not self.dataset:
            self.dataset = FileInfo(file_id=self.file_id,
                                    client=self.client).find_dataset(self.header.split(self.delimiter))

    def normalize_line_endings(self):
        """Normalize line endings to \n"""
        if self.line_ending != b'\n':
            logging.info('Normalizing line endings')
            self.raw_bytes_utf8 = self.raw_bytes_utf8.replace(self.line_ending, b'\n')

    def execute(self):
        self.read_raw_bytes()
        self.detect_encoding_and_convert_utf8()
        self.normalize_line_endings()
        self.read_header()
        self.find_delimiter()
        self.identify_dataset()


class BaseReaderCSV(CSVMeta, FileInfo):
    def __init__(self, path: str, client: Optional[str] = None, file_id: Optional[int] = None,
                 dataset: Optional[str] = None):
        CSVMeta.__init__(self, path, client, file_id, dataset)
        FileInfo.__init__(self, file_id, client)
        self.path = path
        self.client = client
        self.file_id = file_id
        self.out = None
        self.null_values = ['#N/A', '<NA>', '#NA', '', 'N/A', 'n/a', '-NaN', '-nan', 'NaN', '1.#QNAN', '1.#IND', 'null',
                            'NULL', 'Null', '-1.#IND', '-1.#QNAN', '#N/A N/A',
                            'None', 'NONE', 'none']  # picked from old ingestion code

    def _has_null_bytes_in_header(self, num_lines=3):
        lines = self.raw_bytes_utf8.splitlines()
        num_lines = min(num_lines, len(lines))
        for i in range(num_lines):
            if b'\x00' in lines[i]:
                return True
        return False

    def _clean_null_bytes_in_file(self):
        with open(self.path, 'wb') as fo:
            self.raw_bytes_utf8 = self.raw_bytes_utf8.replace(b'\x00', b'')
            fo.write(self.raw_bytes_utf8)

    def check_and_clean_csv(self):
        if self._has_null_bytes_in_header():
            self._clean_null_bytes_in_file()

    def read(self):
        # Handle the in-memory bytes data as a file
        utf8_file = BytesIO(self.raw_bytes_utf8)

        logging.info(f'delimiter: {self.delimiter}')
        self.out = pl.read_csv(utf8_file,
                               separator=self.delimiter,
                               infer_schema_length=0,  # so the schema is not inferred and dtypes are enforced
                               eol_char='\n',
                               dtypes={"*": pl.Utf8},  # read all cols as text, issue came up with text value in mrn,
                               null_values=self.null_values
                               )

    def write(self):
        self.out.write_database(table_name="cstk", connection=get_db_engine('DB_EARASS'),
                                if_table_exists='append')

    def execute(self):
        super().execute()
        self.check_and_clean_csv()
        self.get_file_info()
        self.read()
