import pandas as pd
import logging

from ingestion.readers import BaseReader
from ingestion.utils.db import insert_data, delete_existing_data
from ingestion.schema.cathpci import SchemaCathPCI5

logging.basicConfig(
    format='%(asctime)s %(message)s',
    datefmt='%m/%d/%Y %I:%M:%S %p'
)
logging.root.setLevel(logging.INFO)


class ReaderCathPCI5(BaseReader):

    def __init__(self, filepath, schema, write=False, target_db='DB_EARASS', client=None, file_id=None):
        super().__init__(filepath, SchemaCathPCI5.DATA_DICT, dataset=SchemaCathPCI5.DATASET,
                         dataset_id=SchemaCathPCI5.DATASET_ID, version=SchemaCathPCI5.VERSION,
                         client=client, file_id=file_id)

        if isinstance(schema, SchemaCathPCI5):
            self.schema = schema
        else:
            logging.info('Schema class instance not provided, creating schema from data dictionary')
            self.schema = SchemaCathPCI5()
            self.schema.execute()

        self.df_dates = pd.DataFrame()
        self.write = write
        self.ncdr_tables = None
        self.target_db = target_db

    def execute(self):
        BaseReader.execute(self)

        table_prefix = SchemaCathPCI5.TABLE_PREFIX
        if self.write:
            for table_name, data in self.ncdr_tables.items():
                table_name = table_prefix + table_name
                delete_existing_data(data, table_name=table_name, db=self.target_db)
                insert_data(data, table_name=table_name, db=self.target_db, if_exists='append')
