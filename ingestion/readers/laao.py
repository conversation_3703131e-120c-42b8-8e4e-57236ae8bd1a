import logging
import numpy as np
import pandas as pd

from ingestion.readers import BaseReader
from ingestion.utils.db import insert_data, delete_existing_data
from ingestion.schema.laao import SchemaLAAO14, SchemaLAAOFU14
from ingestion.config import read_config

logging.basicConfig(
    format='%(asctime)s %(message)s',
    datefmt='%m/%d/%Y %I:%M:%S %p'
)
logging.root.setLevel(logging.INFO)


def add_event_id(df, section, field_code):
    """
    Add event id in the IPPEVENTS section using the code of the value instead of the value itself.
    This is an additional field being added in the ncdr schema to have it available in the translated output
    :return:
    """
    cond = (df['Table'] == section) & (df['codeField'] == field_code)
    event_df = df[cond].copy()
    event_df['valueDataDict'] = event_df['codeValue'].astype(str)
    event_df['shortName'] = 'EventId'
    return pd.concat([df, event_df], ignore_index=True)


def add_parent_id(df, dataset):
    # if IncrementalId is less than ParentIncrementalId, then set IncrementalId to ParentIncrementalId
    df['IncrementalId'] = np.where(df['IncrementalId'] < df['ParentIncrementalId'],
                                   df['ParentIncrementalId'], df['IncrementalId'])
    #
    child_sections = read_config(dataset, 'parent_child_sections')['Child Section'].tolist()
    # if not TABLE in child_sections set ParentIncrementalId to 1
    df['ParentIncrementalId'] = np.where(df['Table'].isin(child_sections), df['ParentIncrementalId'], 1)
    return df


class ReaderLAAO14(BaseReader):

    def __init__(self, filepath, schema, write=False, target_db='DB_EARASS', client=None, file_id=None):
        super().__init__(filepath, SchemaLAAO14.DATA_DICT, dataset=SchemaLAAO14.DATASET,
                         dataset_id=SchemaLAAO14.DATASET_ID, version=SchemaLAAO14.VERSION,
                         client=client, file_id=file_id, keep_parent_id=True)

        if isinstance(schema, SchemaLAAO14):
            self.schema = schema
        else:
            logging.info('Schema class instance not provided, creating schema from data dictionary')
            self.schema = SchemaLAAO14()
            self.schema.execute()

        self.write = write
        self.file_id = file_id
        self.ncdr_tables = None
        self.target_db = target_db

    def execute(self):
        if self.schema is None:
            self.gen_schema()
        self.get_submission_info()
        self.gen_parent_child_sections()
        self.get_procedure_section_if_any()
        self.parse_patients()
        self.join_data_dict()
        self.df_raw = add_event_id(self.df_raw, 'IPPEVENTS', '**********')
        self.add_field_suffix()
        self.df_raw = add_parent_id(self.df_raw, SchemaLAAO14.DATASET + '14')
        self.map_values()
        self.get_file_info()
        self.slice_tables()
        self.update_file_info()
        table_prefix = SchemaLAAO14.TABLE_PREFIX
        if self.write:
            for table_name, data in self.ncdr_tables.items():
                full_table_name = table_prefix + table_name
                delete_existing_data(data, table_name=full_table_name, db=self.target_db)
                insert_data(data, table_name=full_table_name, db=self.target_db, if_exists='append')


class ReaderLAAOFU14(BaseReader):
    """
    Derived class for LAAOFUP14
    """

    def __init__(self, filepath, schema, write=False, target_db='DB_EARASS', client=None, file_id=None):
        super().__init__(filepath, SchemaLAAOFU14.DATA_DICT, dataset=SchemaLAAOFU14.DATASET,
                         dataset_id=SchemaLAAOFU14.DATASET_ID, version=SchemaLAAOFU14.VERSION,
                         client=client, file_id=file_id, is_followup=True, keep_parent_id=True)

        if isinstance(schema, SchemaLAAOFU14):
            self.schema = schema
        else:
            logging.info('Schema class instance not provided, creating schema from data dictionary')
            self.schema = SchemaLAAOFU14()
            self.schema.execute()

        self.write = write
        self.file_id = file_id
        self.ncdr_tables = None
        self.target_db = target_db

    def execute(self):
        if self.schema is None:
            self.gen_schema()
        self.get_submission_info()
        self.gen_parent_child_sections()
        self.get_procedure_section_if_any()
        self.parse_patients()
        self.join_data_dict()
        self.df_raw = add_event_id(self.df_raw, 'FUPEVENTS', '**********')
        self.add_field_suffix()
        self.df_raw = add_parent_id(self.df_raw, SchemaLAAOFU14.DATASET + '14')
        self.map_values()
        self.get_file_info()
        self.slice_tables()
        self.update_file_info()

        table_prefix = SchemaLAAOFU14.TABLE_PREFIX
        if self.write:
            for table_name, data in self.ncdr_tables.items():
                table_name = table_prefix + table_name
                delete_existing_data(data, table_name=table_name, db=self.target_db)
                insert_data(data, table_name=table_name, db=self.target_db, if_exists='append')
