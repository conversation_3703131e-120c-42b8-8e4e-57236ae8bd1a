import pandas as pd
import logging

from ingestion.schema.stroke import SchemaStroke
from ingestion.readers.csv import BaseReaderCSV
from ingestion.utils.db import insert_data, delete_existing_data


class ReaderStroke(BaseReaderCSV):
    def __init__(self, filepath, schema, write=False, target_db='DB_MBA', client=None, file_id=None):
        super().__init__(filepath, client, file_id, SchemaStroke.DATASET)
        if isinstance(schema, SchemaStroke):
            self.schema = schema
        else:
            logging.info('Schema class instance not provided, creating schema from data dictionary')
            self.schema = SchemaStroke()
            self.schema.execute()
        self.write = write
        self.target_db = target_db
        self.target_table = SchemaStroke.TABLE_PREFIX
        self.dataset_id = SchemaStroke.DATASET_ID

    def pre_process(self):
        self.out = self.out.to_pandas()
        schema_cols = self.schema.schema['Name'].str.upper().tolist()
        data_cols = self.out.columns.str.upper().tolist()
        self.out.columns = data_cols
        schema_cols = [col for col in schema_cols if col in data_cols]
        self.out = self.out[schema_cols]

    def append_file_info(self):
        """
        append metadata for the file to the dataframe
        """
        self.out['ClientFileId'] = self.file_info['id']
        self.out['FileName'] = self.file_info['file_name']
        self.out['BiomeImportDt'] = pd.to_datetime(self.file_info['created_on'], unit='s')
        self.out['Version'] = self.file_info['version']
        self.out['DatasetName'] = self.file_info['client']

    def update_file_info(self):
        """
        update metadata for the file to the file_info dictionary
        """
        self.file_info['dataframes'] = [{'name': self.target_table, 'row_count': len(self.out)}]
        self.file_info['num_patients'] = len(self.out['PATIENT_DISPLAY_ID'].unique())

    def execute(self):
        super().execute()
        self.pre_process()
        self.update_file_info()
        self.append_file_info()
        if self.write:
            delete_existing_data(data=self.out, table_name=self.target_table, db=self.target_db)
            insert_data(df=self.out, table_name=self.target_table, db=self.target_db, if_exists='append')
