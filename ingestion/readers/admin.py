import pandas as pd
import logging
import json
import numpy as np

from ingestion.readers.excel import BaseReaderExcel
from ingestion.readers.csv import BaseReaderCSV
from ingestion.utils.db import insert_data, delete_existing_data, disable_keys, enable_keys
from ingestion.config import read_config
from ingestion.utils.exceptions import RequiredFieldsError


def clean_dict_for_json(d):
    cleaned = {}
    for k, v in d.items():
        # Handle NaN, None, and empty values
        if pd.isna(v) or (isinstance(v, float) and np.isnan(v)):
            cleaned[k] = None
        # Handle empty strings
        elif isinstance(v, str) and not v.strip():
            cleaned[k] = None
        # Handle infinite values
        elif isinstance(v, float) and (np.isinf(v)):
            cleaned[k] = str(v)
        else:
            cleaned[k] = v
    return cleaned


class BaseReaderAdmin:
    def __init__(self, filepath, schema, target_table, dataset_id, dataset, write=False,
                 target_db='DB_EARASS', client=None, file_id=None):
        self.is_excel = any(ext in filepath.upper() for ext in ['XLSX', 'XLS', 'ODS'])
        self.base_reader = BaseReaderExcel(
            filepath, client, file_id, dataset
        ) if self.is_excel else BaseReaderCSV(filepath, client, file_id, dataset)

        self.write = write
        self.target_db = target_db
        self.target_table = target_table
        self.dataset_id = dataset_id
        self.dataset = dataset
        self.client = client
        self.out = pd.DataFrame()
        self.schema = schema
        self.additional_info = []
        self.file_info = None
        self.datasetlist = None

    def validate_input_completeness(self):
        logging.info("Validating input fields completeness")
        required_fields = read_config(f'{self.dataset}-{self.client}', 'required_fields', as_df=False)
        required_fields = list(map(str.lower, required_fields))
        missing_fields_info = []
        missing_fields_flag = False

        for data in self.datasetlist:
            received_fields = data['dataframe'].columns.str.lower().tolist()
            missing_required_fields = list(set(required_fields) - set(received_fields))
            additional_fields = list(set(received_fields) - set(required_fields))
            field_match_percentage = 100 - round(len(missing_required_fields) / len(required_fields) * 100, 2)

            validation = {
                'sheet_name': data['sheet_name'],
                'field_match_rate': field_match_percentage
            }

            if missing_required_fields:
                missing_fields_flag = True
                validation['missing_fields'] = missing_required_fields
                missing_fields_info.append(validation)

            elif additional_fields:
                validation['additional_fields'] = additional_fields
                self.additional_info.append(validation)

        if missing_fields_flag:

            logging.error("Required fields are missing!")
            raise RequiredFieldsError(missing_fields_info=missing_fields_info)

    def pre_process(self):
        """
        todo add description
        """
        required_columns = self.schema.fields['Name'].str.lower().tolist()
        for data in self.datasetlist:
            extra_cols = [col.lower() for col in data['dataframe'].columns if col not in required_columns]
            data['dataframe']["additional_fields"] = data['dataframe'][extra_cols].apply(
                lambda row: json.dumps(
                    clean_dict_for_json(row.to_dict()),
                    ensure_ascii=False
                ),
                axis=1
            )

            data['dataframe'].drop(columns=extra_cols, inplace=True)
        self.out = pd.concat([data['dataframe'] for data in self.datasetlist], ignore_index=True)

    def append_file_info(self):
        """
        append metadata for the file to the dataframe
        """
        self.out['ClientFileId'] = self.file_info['id']
        self.out['FileName'] = self.file_info['file_name']
        self.out['BiomeImportDt'] = pd.to_datetime(self.file_info['created_on'], unit='s')
        self.out['Version'] = self.file_info['version']
        self.out['DatasetName'] = self.file_info['client']

    def update_file_info(self):
        """
        update metadata for the file to the file_info dictionary
        """
        self.file_info['dataframes'] = [{'name': self.target_table, 'row_count': len(self.out)}]
        if 'medrecn' in self.out.columns:
            self.file_info['num_patients'] = len(self.out['medrecn'].unique())
        else:
            self.file_info['num_patients'] = len(self.out['encounternumber'].unique())

    def rename_columns(self):
        config = read_config(f'{self.dataset}-{self.client}', 'rename_fields')
        if config.empty:
            logging.info('Reader: No fields to rename')
            return

        logging.info('Reader: Renaming fields')
        for data in self.datasetlist:
            data_cols = data['dataframe'].columns.str.lower().tolist()
            data['dataframe'].columns = data_cols
            config['Old Field'] = config['Old Field'].str.lower()
            config['New Field'] = config['New Field'].str.lower()
            new_fields = config['New Field'].tolist()
            old_fields = config['Old Field'].tolist()
            rename = dict(zip(new_fields, old_fields))
            data['dataframe'] = data['dataframe'].rename(columns=rename)

    def lower_case_fields(self):
        for data in self.datasetlist:
            data['dataframe'].columns = data['dataframe'].columns.str.lower()

    def execute(self):
        self.base_reader.execute()
        self.file_info = self.base_reader.file_info
        if not self.is_excel:
            self.datasetlist = [{'dataframe': self.base_reader.out.to_pandas(), 'sheet_name': 'default'}]
        else:
            self.datasetlist = self.base_reader.datasetlist

        self.rename_columns()
        self.lower_case_fields()
        self.validate_input_completeness()
        self.pre_process()
        self.update_file_info()
        self.append_file_info()

        if self.write:
            delete_existing_data(data=self.out, table_name=self.target_table, db=self.target_db)
            disable_keys(table_name=self.target_table, db=self.target_db)
            insert_data(df=self.out, table_name=self.target_table, db=self.target_db, if_exists='append')
            enable_keys(table_name=self.target_table, db=self.target_db)
