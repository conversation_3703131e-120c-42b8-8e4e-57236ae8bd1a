import pandas as pd
import polars as pl
from openpyxl import load_workbook
from typing import Optional

from ingestion.utils import logger
from ingestion.utils.file import FileInfo


class BaseReaderExcel(FileInfo):
    def __init__(self, path: str, client: Optional[str] = None, file_id: Optional[int] = None,
                 dataset: Optional[str] = None):
        super().__init__(file_id, client)
        self.path = path
        self.file_id = file_id
        self.dataset = dataset
        self.skipsheets = list()
        self.na_values = ['#N/A', '<NA>', '#NA', '', 'N/A', 'n/a', '-NaN', '-nan', 'NaN', '1.#QNAN', '1.#IND',
                          'null', 'NULL', '-1.#IND', '-1.#QNAN', '#N/A N/A', 'nan']

    def identify_dataset(self):
        dataset = list()
        if not self.dataset:
            for data in self.datasetlist:
                dataset.append(FileInfo(file_id=self.file_id,
                                        client=self.client).find_dataset(data['dataframe'].columns))
            self.dataset = dataset

    def skiprows(self, sheet):
        skiprows = 0

        # for i in range(0, min(10, sheet.nrows)):
        #     skiprows = i
        #     """
        #         Sometimes there are empty column in the end causing the actual header row to skip.
        #  Added some tolerance
        #     """
        #     emptycells = [sheet.cell(i, c).value for c in range(max(min(3, sheet.ncols), sheet.ncols - 5)) if
        #                   sheet.cell(i, c).value == ""]
        #     if len(emptycells) == 0:
        #         break
        #     elif len(emptycells) == 1 and sheet.cell(i, 0).value == "" and sheet.cell(i + 1, 0).value == (
        #             sheet.cell(i + 2, 0).value - 1):
        #         """In UCLA elso files the first cell in column headers is usually blank
        #  and the first cell in next row is 1"""
        #         break

        return skiprows

    def filterfields(self, fields, df):
        fields = list(filter(None, fields))
        df = df[fields]  # Pick only valid non blank columns
        return df

    @logger
    def read(self):
        # todo:
        # handel invalid work sheets
        # handel extra blank rows

        datasetlist = list()
        if self.path.split('.')[-1].upper() == 'ODS':
            odsfile = pd.ExcelFile(self.path, engine="odf")
            sheets = odsfile.sheet_names

            for sheet in sheets:
                df = pd.read_excel(odsfile, sheet, keep_default_na=False, na_values=self.na_values)
                fields = map(str.upper, df.columns.to_list())
                df.columns = list(filter(None, fields))
                if len(df.columns.unique()) > 0:
                    # removeblankcolsiftoomany
                    if len(list(df)) > 1100:
                        nullcols = list(df.columns[df.isnull().all()])

                    df.columns = df.columns.str.replace('≥', 'Gte')\
                        .str.replace('≤', 'Lte')\
                        .str.replace('±', 'PlsMns')\
                        .str.replace('≠', 'Neq')
                    df.columns = [name[:255] for name in df.columns]
                    df.columns = df.columns.str.lower()
                    dataset = {}
                    dataset['sheet_name'] = sheet.replace("∕", "/")
                    dataset["dataframes"] = df
                    dataset['columns'] = list(df)
                    datasetlist.append(dataset)
            odsfile = None
        else:
            wb = load_workbook(self.path, read_only=True, data_only=True)
            sheets = wb.sheetnames
            for idx, sheet in enumerate(sheets):  # read only visible sheets, with data and those are not skipped
                worksheet = wb[sheet]
                if (worksheet.sheet_state != 'hidden' and sheet.upper() not in self.skipsheets and
                        worksheet.max_column > 0):
                    rowstoskip = self.skiprows(sheet)
                    fields = list()
                    for cell in worksheet[rowstoskip + 1]:
                        value = str(cell.value or "").upper()
                        fields.append(value)
                    """In UCLA elso files the first cell in column headers is usually
                      blank and the first cell in next row is 1"""
                    if (len(fields) > 100 and
                            fields[0] == "" and
                            isinstance(worksheet.cell(row=rowstoskip + 2, column=1).value, (int, float)) and
                            isinstance(worksheet.cell(row=rowstoskip + 3, column=1).value, (int, float))):
                        first_num = worksheet.cell(row=rowstoskip + 2, column=1).value
                        second_num = worksheet.cell(row=rowstoskip + 3, column=1).value
                        if second_num == first_num + 1:
                            fields[0] = "Index"

                    df = pl.read_excel(source=self.path, sheet_name=sheet, engine="xlsx2csv",
                                       read_csv_options={
                                           "null_values": self.na_values, "skip_rows": rowstoskip,
                                           "dtypes": {"*": pl.Utf8},  # Explicitly set all columns to string
                                           "ignore_errors": True,
                                           "try_parse_dates": False,  # Ensure no automatic date parsing
                                           "infer_schema_length": 0  # Completely disable schema inference
                                       })
                    df = self.filterfields(df.columns, df)
                    clean_fields = list(filter(None, fields))

                    # Rename columns with specific replacements
                    new_columns = []
                    for col in clean_fields:
                        col = col.replace('≥', 'Gte') \
                            .replace('≤', 'Lte') \
                            .replace('±', 'PlsMns') \
                            .replace('≠', 'Neq') \
                            .lower()
                        new_columns.append(col)

                    # Rename columns in the DataFrame
                    df.columns = new_columns
                    dataset = {}
                    dataset['sheet_name'] = sheet.replace("∕", "/")
                    dataset["dataframe"] = df.to_pandas()
                    dataset['columns'] = list(df)
                    datasetlist.append(dataset)
            wb.close()
        self.datasetlist = datasetlist

    def execute(self):
        self.get_file_info()
        self.read()
        self.identify_dataset()
