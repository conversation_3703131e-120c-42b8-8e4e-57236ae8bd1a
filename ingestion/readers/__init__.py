import xml.etree.ElementTree as ET
import pandas as pd
import numpy as np
import logging

from ingestion.schema.schema_gen import BuildSchema
from ingestion.utils.file import FileInfo

logging.basicConfig(
    format='%(asctime)s %(message)s',
    datefmt='%m/%d/%Y %I:%M:%S %p'
)
logging.root.setLevel(logging.INFO)


class BaseReader:
    def __init__(self, filepath, data_dict_filepath, dataset, dataset_id, version, client, file_id, is_followup=False,
                 keep_parent_id=False):
        self.root = ET.parse(filepath).getroot()
        self.filepath = filepath
        self.data_dict_filepath = data_dict_filepath
        self.records = []
        self.df_raw = pd.DataFrame()
        self.parent_child = pd.DataFrame()
        self.schema = None
        self.ncdr_tables = None
        self.dataset = dataset
        self.dataset_id = dataset_id
        self.version = version
        self.client = client
        self.file_id = file_id
        self.sec_counters = {}
        self.parent_sec_counters = {}
        self.is_followup = is_followup
        self.keep_parent_id = keep_parent_id

        self.value_map = {'true': 'Yes', 'false': 'No'}

        self.part_id = None
        self.part_name = None

        self.procedure_section_name = None
        self.file_info = dict()

        if is_followup:
            self.container = 'followup'
            self.key_field = 'FollowupKey'
            self.key_field_xml = 'followupKey'
        else:
            self.container = 'episode'
            self.key_field = 'EpisodeKey'
            self.key_field_xml = 'episodeKey'

    def get_submission_info(self):
        """
        Extracts the submission information from the xml
        """
        submission = self.root.find('submission')
        admin = self.schema.elements[self.schema.elements['Section Code'] == 'ADMIN']
        code = admin[admin['Short Name'] == 'PartID']['Code'].values[0]
        dtype = admin[admin['Short Name'] == 'PartID']['Data Type'].values[0]
        path = f"./section/element[@code='{code}']/value[@{{http://www.w3.org/2001/XMLSchema-instance}}type='{dtype}']"
        self.part_id = submission.find(path).attrib.get("value") if submission.find(path) is not None else None

        code = admin[admin['Short Name'] == 'PartName']['Code'].values[0]
        dtype = admin[admin['Short Name'] == 'PartName']['Data Type'].values[0]
        path = f"./section/element[@code='{code}']/value[@{{http://www.w3.org/2001/XMLSchema-instance}}type='{dtype}']"
        self.part_name = submission.find(path).attrib.get("value") if submission.find(path) is not None else None

    def gen_schema(self):
        """
        Generates the schema for the data dictionary file and stores it in self.sc object for further use in the reader
        """
        self.schema = BuildSchema(data_dict_filepath=self.data_dict_filepath, dataset=self.dataset,
                                  dataset_id=self.dataset_id, version=self.version)
        self.schema.execute()

    def gen_parent_child_sections(self):
        """
        Generates a dataframe with parent and child sections based on the section structure in the data dictionary
        """
        parent_child = self.schema.sections

        sec_codes = parent_child[['Section Code', 'Section Display Name']].drop_duplicates().rename(
            columns={'Section Code': 'Parent Section Code', 'Section Display Name': 'Parent Section'})

        parent_child = parent_child.merge(sec_codes, on=['Parent Section'], how='left')
        parent_child['Parent Section Code'] = parent_child['Parent Section Code'].fillna('Root')
        self.parent_child = parent_child

    def get_parent_section_code(self, child_section):
        """
        Returns the parent section code for the given child section code based on the parent_child dataframe
        """
        return self.parent_child.loc[
            self.parent_child['Section Code'] == child_section, 'Parent Section Code'].values[0]

    @staticmethod
    def extract_data(element):
        """
        Extracts the data from the xml element and returns a dictionary with the data points for the element
        and its values if any
        """
        code_system = element.get("codeSystem")
        code_system_name = element.get("codeSystemName")
        code = element.get("code")
        display_name = element.get("displayName")

        data = {"codeSystem": code_system, "codeSystemName": code_system_name, "codeField": code,
                "displayName": display_name, "valueType": [], "value": [], "codeValue": [], "unit": [],
                "valueDisplayName": []}

        values = element.findall('value')

        for value_element in values:
            if value_element is not None:
                value_type = value_element.get("{http://www.w3.org/2001/XMLSchema-instance}type")
                if value_type == 'CD':
                    value = value_element.get("code", value_element.get('displayName'))
                else:
                    value = value_element.get("value", value_element.get('displayName'))
                code_value = value_element.get('code')
                value_display_name = value_element.get('displayName')
                unit = value_element.get('unit')

                if value_type:
                    data["valueType"].append(value_type)
                if value:
                    data["value"].append(value)
                if code_value:
                    data["codeValue"].append(code_value)
                if unit:
                    data["unit"].append(unit)
                if value_display_name:
                    data["valueDisplayName"].append(value_display_name)

        data['valueLength'] = len(data['value'])
        data["valueType"] = '|'.join(data["valueType"])
        data["value"] = '|'.join(data["value"])
        # joining the code with || for multiple values because for some value the code contained | in itself.
        # e.g. of such scenario is this code '1522000|15825003' for one of the values for element 4050 in LAAO
        data["codeValue"] = '||'.join(data["codeValue"]) if data["codeValue"] else None
        data["unit"] = '|'.join(data["unit"]) if data["unit"] else None
        data["valueDisplayName"] = '|'.join(data["valueDisplayName"]) if data["valueDisplayName"] else None
        return data

    def create_data_point(self, elem, patient, container, visitid, section, sec_counter=1, parent_sec_counter=1):
        """
        Creates a dictionary with the data points for the section and its subsections if any
        """
        data = self.extract_data(elem)
        data['NCDRPatientId'] = patient.get('ncdrPatientId')
        data[self.key_field] = container.get(self.key_field_xml, 0)
        data['VisitId'] = visitid
        data['section'] = section.get('code')
        data['sectionDisplayName'] = section.get('displayName')
        data['sectionCounter'] = sec_counter
        if self.keep_parent_id:
            data['ParentIncrementalId'] = parent_sec_counter
        return data

    def parse_section(self, patient, container, section, visitid, sec_counter=1, parent_sec_counter=1):
        """
        Parses the section and creates a list of dictionaries with the data points for the section
        and its subsections if any
        """
        for elem in section.findall('element'):
            data = self.create_data_point(elem, patient, container, visitid, section, sec_counter, parent_sec_counter)
            self.records.append(data)

        parent_sec_key = (patient.get('ncdrPatientId'), section.get('code'), container.get(self.key_field_xml), visitid)
        self.parent_sec_counters[parent_sec_key] = self.parent_sec_counters.get(parent_sec_key, 0) + 1

        sub_sections = section.findall('section')
        if sub_sections:
            for section in sub_sections:
                sec_key = (patient.get('ncdrPatientId'), section.get('code'), container.get(self.key_field_xml),
                           visitid)
                self.sec_counters[sec_key] = self.sec_counters.get(sec_key, 0) + 1
                self.parse_section(patient, container, section, visitid, self.sec_counters[sec_key],
                                   self.parent_sec_counters[parent_sec_key])
        else:
            return

    def get_procedure_section_if_any(self, proc_section='PROCINFO'):
        """
        check from schema sections if PROCINFO is present and the type is Repeater Section or the cardonality has n
        :return:
        """
        schema = self.schema.sections
        schema = schema[
            (schema['Section Code'] == proc_section
             ) & ((schema['Section Type'] == 'Repeater Section') | (schema['Cardinality'].str.contains('n')))
        ]
        if not schema.empty:
            self.procedure_section_name = proc_section
        else:
            self.procedure_section_name = ''

    def parse_patients(self):
        """
        Parses the xml and creates a list of dictionaries with the data points
        """
        for patient in self.root.iter('patient'):
            demo_graphics = patient.find('section')
            if demo_graphics.get('code') == 'DEMOGRAPHICS':
                for elem in demo_graphics.findall('element'):
                    data = self.create_data_point(elem, patient, container={}, visitid=0, section=demo_graphics)
                    self.records.append(data)

            for container in patient.iter(self.container):
                visitid = 0
                completed_sections = []
                for sec in container.iter("section"):

                    parent_section = self.get_parent_section_code(sec.get('code'))
                    if parent_section == 'Root' or parent_section not in completed_sections:
                        if sec.get('code') != 'DEMOGRAPHICS':
                            procedure_section = sec.get('code') == self.procedure_section_name
                            if procedure_section:
                                visitid = visitid + 1
                            self.parse_section(patient, container, sec, visitid=visitid if procedure_section else 0)
                    completed_sections.append(sec.get('code'))

        self.df_raw = pd.DataFrame(self.records)

    def join_data_dict(self):
        """
        Joining data dictionary to fetch values from there for some categorical fields, like Sex, HIPS etc
        also brings the Table name from the schema
        :return:
        """

        # joining data dictionary to get field names based on field codes
        data_dict = self.schema.output[['Section Code', 'Code Elements', 'Short Name', 'Data Type']].drop_duplicates()
        data_dict['Code Elements'] = data_dict['Code Elements'].astype(str)
        left_on = [
            self.df_raw['section'].str.lower(),
            self.df_raw['codeField'].str.lower(),
            self.df_raw['valueType'].str.lower().str.split('|').str[0]
        ]
        right_on = [
            data_dict['Section Code'].str.lower(),
            data_dict['Code Elements'].str.lower(),
            data_dict['Data Type'].str.lower()
        ]
        self.df_raw = self.df_raw.merge(data_dict, left_on=left_on, right_on=right_on, how='inner').rename(
            columns={'Short Name': 'shortName'}).drop(['Section Code', 'Code Elements', 'Data Type',
                                                       'key_0', 'key_1', 'key_2'], axis=1)

        # joining data dictionary to get value names based on value codes and fields
        data_dict = self.schema.output[
            ['Section Code', 'Table', 'Short Name', 'Data Type', 'Code Selections', 'Selection Name', 'Code Elements',
             'Unit Of Measure Elements']].drop_duplicates()
        left_on = [
            self.df_raw['section'].str.lower(),
            self.df_raw['shortName'].str.lower(),
            self.df_raw['codeValue'].str.lower(),
        ]
        right_on = [
            data_dict['Section Code'].str.lower(),
            data_dict['Short Name'].str.lower(),
            data_dict['Code Selections'].str.lower(),
        ]
        self.df_raw = self.df_raw.merge(data_dict, how='left', left_on=left_on, right_on=right_on)
        self.df_raw['Selection Name'] = self.df_raw['Selection Name'].fillna(self.df_raw['value'])
        self.df_raw = self.df_raw.rename(
            columns={'Selection Name': 'valueDataDict',
                     'Code Selections': 'valueCodeDataDict',
                     'Data Type': 'dataTypeDataDict'}).drop(['Table', 'Section Code'], axis=1)

        # for multi value fields
        value_lookup = data_dict.set_index(right_on)['Selection Name']

        def map_values(row):
            value = [
                value_lookup[(row['section'].lower(),
                              row['shortName'].lower(),
                              v.lower())]
                for v in row['codeValue'].split('||')
            ]
            return '|'.join(value)

        cond = (self.df_raw['valueLength'] > 1) & (self.df_raw['valueType'].str.contains('CD'))
        self.df_raw.loc[cond, 'valueDataDict'] = self.df_raw.loc[cond].apply(map_values, axis=1)

        # append unit fields
        # https://biome-analytics.atlassian.net/browse/ING-568
        # keeping unit fields only that are mentioned in the coders dictionary
        unit_fields = self.df_raw.dropna(subset=['Unit Of Measure Elements']).drop_duplicates()
        unit_fields['shortName'] = unit_fields['shortName'] + '_unit'
        unit_fields['valueDataDict'] = unit_fields['unit']
        unit_fields['value'] = unit_fields['unit']
        drop_fields = ['unit', 'Unit Of Measure Elements']
        self.df_raw = pd.concat([self.df_raw.drop(drop_fields, axis=1), unit_fields.drop(drop_fields, axis=1)])

        self.df_raw = self.df_raw.merge(
            data_dict[['Table', 'Section Code']].drop_duplicates().rename(columns={'Section Code': 'section'}),
            how='inner', on='section')

        # for some clients ncdr patient id is coming in Demographics section as well. Droping this to retain only one
        # self.df_raw = self.df_raw[self.df_raw['shortName'] != 'NCDRPatientID']
        self.df_raw = self.df_raw[~self.df_raw['shortName'].isin(['NCDRPatientID', 'FollowUpKey'])]

    def add_field_suffix(self):
        """
        Adds suffix to the field names based on the number of times the field appears for the same patient in the same
        section and visit id
        """

        # incremental id for fields that appear multiple times for same patient like DC_MedID, PreProcMedID
        self.df_raw['IncrementalId'] = self.df_raw.groupby(
            ['NCDRPatientId', self.key_field, 'VisitId', 'shortName']).cumcount() + 1
        self.df_raw['displayNameSuffix'] = np.where(self.df_raw['IncrementalId'] > 1,
                                                    self.df_raw['shortName'] + '_' + self.df_raw[
                                                        'IncrementalId'].astype('str'),
                                                    self.df_raw['shortName'])

        # creating section counter for repeating sections based on the section display name.
        # e.g. counter for section Discharge Med 17 would be 17
        # self.df_raw['sectionCounter'] = self.df_raw['sectionDisplayName'].str.extract(r'(\d+)$').fillna(1).astype(int)
        pre_cond = self.df_raw['sectionCounter'] == 1
        self.df_raw['sectionCounter'] = np.where(pre_cond, self.df_raw['IncrementalId'], self.df_raw['sectionCounter'])

        self.df_raw['IncrementalId'] = self.df_raw['sectionCounter']

    def slice_tables(self):
        """
        Slices the data into multiple tables based on the section code and the schema
        """
        schema = self.schema.output
        schema = schema.groupby('Table')['Short Name'].agg(list)
        self.ncdr_tables = {}
        index_cols = ['NCDRPatientId', self.key_field, 'VisitId', 'IncrementalId']
        if self.keep_parent_id:
            index_cols.append('ParentIncrementalId')

        for table_name, data in self.df_raw.groupby('Table'):
            data = data[~data['shortName'].isin(index_cols)]
            try:
                piv = data.pivot(index=index_cols,
                                 columns='shortName', values='valueDataDict').reset_index()
            except ValueError as e:
                logging.info(f'Error pivoting {table_name}, attempting to add sectionCounter to pivot index')
                idx_cols_session_counter = index_cols + ['sectionCounter']
                piv = data.pivot(index=idx_cols_session_counter,
                                 columns='shortName', values='valueDataDict').reset_index()
                piv.drop('sectionCounter', axis=1, inplace=True)

            for field in schema[table_name]:
                if field.lower() not in piv.columns.str.lower():
                    piv[field] = None

            if len(self.file_info) > 0:
                piv['ClientFileId'] = self.file_info['id']
                piv['FileName'] = self.file_info['file_name']
                piv['BiomeImportDt'] = pd.to_datetime(self.file_info['created_on'], unit='s')
                piv['Version'] = self.file_info['version']
                piv['DatasetName'] = self.file_info['client']
                piv['PartId'] = self.part_id
                piv['PartName'] = self.part_name

            self.ncdr_tables[table_name.upper()] = piv

    def map_values(self):
        """
        Maps the values based on the value map
        """
        self.df_raw['value'] = self.df_raw['value'].map(self.value_map).fillna(self.df_raw['value'])
        self.df_raw['valueDataDict'] = self.df_raw['valueDataDict'].map(self.value_map).fillna(
            self.df_raw['valueDataDict'])

    def get_file_info(self):
        self.file_info = FileInfo(file_id=self.file_id, client=self.client).get_file_info()

    def update_file_info(self):
        """
        update metadata for the file
        """
        self.file_info['dataframes'] = [{'name': table, 'row_count': len(data)}
                                        for table, data in self.ncdr_tables.items()]
        self.file_info['num_patients'] = len(self.df_raw['NCDRPatientId'].unique())

    def execute(self):
        if self.schema is None:
            self.gen_schema()
        self.get_submission_info()
        self.gen_parent_child_sections()
        self.get_procedure_section_if_any()
        self.parse_patients()
        self.join_data_dict()
        self.add_field_suffix()
        self.map_values()
        self.get_file_info()
        self.slice_tables()
        self.update_file_info()
