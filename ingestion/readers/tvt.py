import logging

from ingestion.readers import BaseReader
from ingestion.utils.db import insert_data, delete_existing_data
from ingestion.schema.tvt import SchemaTVT30, SchemaTVTFU30

logging.basicConfig(
    format='%(asctime)s %(message)s',
    datefmt='%m/%d/%Y %I:%M:%S %p'
)
logging.root.setLevel(logging.INFO)


class ReaderTVT30(BaseReader):

    def __init__(self, filepath, schema, write=False, target_db='MBA_DB', client=None, file_id=None):
        super().__init__(filepath, SchemaTVT30.DATA_DICT, dataset=SchemaTVT30.DATASET,
                         dataset_id=SchemaTVT30.DATASET_ID, version=SchemaTVT30.VERSION,
                         client=client, file_id=file_id, keep_parent_id=False)

        if isinstance(schema, SchemaTVT30):
            self.schema = schema
        else:
            logging.info('Schema class instance not provided, creating schema from data dictionary')
            self.schema = SchemaTVT30()
            self.schema.execute()

        self.write = write
        self.file_id = file_id
        self.ncdr_tables = None
        self.target_db = target_db
        self.procedure_section_name = "labvisit"

    def execute(self):
        if self.schema is None:
            self.gen_schema()
        self.get_submission_info()
        self.gen_parent_child_sections()
        self.get_procedure_section_if_any(proc_section=self.procedure_section_name)
        self.parse_patients()
        self.join_data_dict()
        self.add_field_suffix()
        self.map_values()
        self.get_file_info()
        self.slice_tables()
        self.update_file_info()
        table_prefix = SchemaTVT30.TABLE_PREFIX
        if self.write:
            for table_name, data in self.ncdr_tables.items():
                full_table_name = table_prefix + table_name
                delete_existing_data(data, table_name=full_table_name, db=self.target_db)
                insert_data(data, table_name=full_table_name, db=self.target_db, if_exists='append')


class ReaderTVTFU30(BaseReader):
    """
    Derived class for TVTFU30
    """

    def __init__(self, filepath, schema, write=False, target_db='DB_EARASS', client=None, file_id=None):
        super().__init__(filepath, SchemaTVTFU30.DATA_DICT, dataset=SchemaTVTFU30.DATASET,
                         dataset_id=SchemaTVTFU30.DATASET_ID, version=SchemaTVTFU30.VERSION,
                         client=client, file_id=file_id, is_followup=True, keep_parent_id=False)

        if isinstance(schema, SchemaTVTFU30):
            self.schema = schema
        else:
            logging.info('Schema class instance not provided, creating schema from data dictionary')
            self.schema = SchemaTVTFU30()
            self.schema.execute()

        self.write = write
        self.file_id = file_id
        self.ncdr_tables = None
        self.target_db = target_db

    def execute(self):
        if self.schema is None:
            self.gen_schema()
        self.get_submission_info()
        self.gen_parent_child_sections()
        self.get_procedure_section_if_any()
        self.parse_patients()
        self.join_data_dict()
        self.add_field_suffix()
        self.map_values()
        self.get_file_info()
        self.slice_tables()
        self.update_file_info()

        table_prefix = SchemaTVTFU30.TABLE_PREFIX
        if self.write:
            for table_name, data in self.ncdr_tables.items():
                table_name = table_prefix + table_name
                delete_existing_data(data, table_name=table_name, db=self.target_db)
                insert_data(data, table_name=table_name, db=self.target_db, if_exists='append')
