import logging

from ingestion.readers.admin import BaseReaderAdmin
from ingestion.schema.diagnosis import SchemaDiagnosis


class ReaderDiagnosis(BaseReaderAdmin):
    def __init__(self, filepath, schema, write=False, target_db='DB_EARASS', client=None, file_id=None):

        if isinstance(schema, SchemaDiagnosis):
            self.schema = schema
        else:
            logging.info('Schema class instance not provided, creating schema from data dictionary')
            self.schema = SchemaDiagnosis()
            self.schema.execute()

        super().__init__(filepath, self.schema, target_table=SchemaDiagnosis.TABLE_PREFIX,
                         dataset_id=SchemaDiagnosis.DATASET_ID,
                         dataset=SchemaDiagnosis.DATASET,
                         write=write, target_db=target_db, client=client, file_id=file_id)
