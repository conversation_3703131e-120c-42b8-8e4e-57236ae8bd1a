import pandas as pd
import logging

from ingestion.readers import BaseReader
from ingestion.utils.db import insert_data, delete_existing_data
from ingestion.schema.afib import SchemaAFib10, SchemaAFib20

logging.basicConfig(
    format='%(asctime)s %(message)s',
    datefmt='%m/%d/%Y %I:%M:%S %p'
)
logging.root.setLevel(logging.INFO)


class ReaderAFib10(BaseReader):
    def __init__(self, filepath, schema, write=False, target_db="DB_ASHISH", client=None, file_id=None):
        super().__init__(filepath, SchemaAFib10.DATA_DICT, dataset=SchemaAFib10.DATASET,
                         dataset_id=SchemaAFib10.DATASET_ID, version=SchemaAFib10.VERSION,
                         client=client, file_id=file_id)
        if isinstance(schema, SchemaAFib10):
            self.schema = schema
        else:
            logging.info("Schema class instance not provided, creating schema from data dictionary")
            self.schema = SchemaAFib10()
            self.schema.execute()

        self.df_dates = pd.DataFrame()
        self.write = write
        self.ncdr_tables = None
        self.target_db = target_db

    def add_device_name(self):

        c1 = self.df_raw['section'].str.lower() == 'devicesused'
        c2 = self.df_raw['codeField'] == '2.16.840.1.113883.3.3478.6.1.22'
        dev_df = self.df_raw[c1 & c2].copy()
        dev_df['valueDataDict'] = dev_df['valueDisplayName'].astype(str)
        dev_df['shortName'] = 'DeviceName'

        self.df_raw = pd.concat([self.df_raw, dev_df], ignore_index=True)

    def execute(self):
        if self.schema is None:
            self.gen_schema()
        self.get_submission_info()
        self.gen_parent_child_sections()
        self.get_procedure_section_if_any()
        self.parse_patients()
        self.join_data_dict()
        self.add_device_name()
        self.add_field_suffix()
        self.map_values()
        self.get_file_info()
        self.slice_tables()
        self.update_file_info()

        table_prefix = SchemaAFib10.TABLE_PREFIX
        if self.write:
            for table_name, data in self.ncdr_tables.items():
                table_name = table_prefix + table_name
                delete_existing_data(data, table_name=table_name, db=self.target_db)
                insert_data(data, table_name=table_name, db=self.target_db, if_exists='append')


class ReaderAFib20(BaseReader):
    def __init__(self, filepath, schema, write=False, target_db="DB_ASHISH", client=None, file_id=None):
        super().__init__(filepath, SchemaAFib20.DATA_DICT, dataset=SchemaAFib20.DATASET,
                         dataset_id=SchemaAFib20.DATASET_ID, version=SchemaAFib20.VERSION,
                         client=client, file_id=file_id)
        if isinstance(schema, SchemaAFib20):
            self.schema = schema
        else:
            logging.info("Schema class instance not provided, creating schema from data dictionary")
            self.schema = SchemaAFib20()
            self.schema.execute()

        self.df_dates = pd.DataFrame()
        self.write = write
        self.ncdr_tables = None
        self.target_db = target_db

    def add_device_name(self):

        c1 = self.df_raw['section'].str.upper() == 'CATHABLDEV'
        c2 = self.df_raw['codeField'] == '2.16.840.1.113883.3.3478.6.1.22'
        dev_df = self.df_raw[c1 & c2].copy()
        dev_df['valueDataDict'] = dev_df['valueDisplayName'].astype(str)
        dev_df['shortName'] = 'DeviceName'

        self.df_raw = pd.concat([self.df_raw, dev_df], ignore_index=True)

    def execute(self):
        if self.schema is None:
            self.gen_schema()
        self.get_submission_info()
        self.gen_parent_child_sections()
        self.get_procedure_section_if_any()
        self.parse_patients()
        self.join_data_dict()
        self.add_device_name()
        self.add_field_suffix()
        self.map_values()
        self.get_file_info()
        self.slice_tables()
        self.update_file_info()

        table_prefix = SchemaAFib20.TABLE_PREFIX
        if self.write:
            for table_name, data in self.ncdr_tables.items():
                table_name = table_prefix + table_name
                delete_existing_data(data, table_name=table_name, db=self.target_db)
                insert_data(data, table_name=table_name, db=self.target_db, if_exists='append')
