import logging

from ingestion.readers.admin import BaseReaderAdmin
from ingestion.schema.cost import SchemaCost


class ReaderCost(BaseReaderAdmin):
    """
    Reader class of Cost that reads raw cost files.
    """

    def __init__(self, filepath, schema, write=False, target_db='DB_ASHISH', client=None, file_id=None):

        if isinstance(schema, SchemaCost):
            self.schema = schema
        else:
            logging.info("Schema class instance not provided, creating schema from data dictionary")
            self.schema = SchemaCost()
            self.schema.execute()

        super().__init__(filepath, self.schema, target_table=SchemaCost.TABLE_PREFIX,
                         dataset_id=SchemaCost.DATASET_ID,
                         dataset=SchemaCost.DATASET,
                         write=write, target_db=target_db, client=client, file_id=file_id)
