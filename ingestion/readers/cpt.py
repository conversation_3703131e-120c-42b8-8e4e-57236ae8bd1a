import logging
from ingestion.readers.admin import BaseReaderAdmin
from ingestion.schema.cpt import SchemaCpt


class ReaderCpt(BaseReaderAdmin):
    def __init__(self, filepath, schema, write=False, target_db='db_casey', client=None, file_id=None):

        if isinstance(schema, SchemaCpt):
            self.schema = schema
        else:
            logging.info('Schema class instance not provided, creating schema from data dictionary')
            self.schema = SchemaCpt()
            self.schema.execute()

        super().__init__(filepath, self.schema, target_table=SchemaCpt.TABLE_PREFIX,
                         dataset_id=SchemaCpt.DATASET_ID,
                         dataset=SchemaCpt.DATASET,
                         write=write, target_db=target_db, client=client, file_id=file_id)
