import math
import pandas as pd
import logging
import re

from ingestion.readers.discharge import ReaderDischarge
from ingestion.schema.discharge import SchemaDischarge
from ingestion.config import read_config
from ingestion.translators import TranslatorBase
from ingestion.readers.common import ReadRawTable
from ingestion.utils.common import create_table_name, find_hospital
from ingestion.utils.file import FileInfo
from ingestion.utils.odin import get_care_entities, get_all_care_entities
from ingestion.utils.exceptions import HospitalRequiredError


class TranslateDischargesToBiome(TranslatorBase, FileInfo):
    """
    translates the output of Admin Discharges to Biome v1
    """

    def __init__(self, filepath, schema, reader, file_id, client, write=False, target_db='DB_MBA',
                 db_as_source=False, source_db=None):
        TranslatorBase.__init__(self)
        FileInfo.__init__(self, file_id=file_id, client=client)

        if db_as_source:

            if source_db is None:
                logging.error("Source database must be provided.")
                raise ValueError("Source database must be provided.")

            logging.info('Using client raw database as source for raw data')
            self.reader = ReadRawTable(source_db, SchemaDischarge.TABLE_PREFIX, file_id)
            self.reader.execute()

        else:
            if isinstance(schema, SchemaDischarge):
                self.schema = schema
            else:
                logging.info('Schema class instance not provided, creating schema from data dictionary')
                self.schema = SchemaDischarge()
                self.schema.execute()

            if isinstance(reader, ReaderDischarge):
                self.reader = reader
            else:
                logging.info('Reader class instance not provided, reading from file')
                self.reader = ReaderDischarge(filepath=filepath, schema=self.schema, file_id=file_id, client=client)
                self.reader.execute()

        self.output = pd.DataFrame()
        self._write = write
        self.target_table = None
        self.target_db = target_db

        self.dataset = SchemaDischarge.DATASET
        self.dataset_id = SchemaDischarge.DATASET_ID
        self.target_schema = read_config(self.dataset, 'biome_schema')
        self.filepath = filepath
        self.file_id = file_id
        self.file_info = None
        self.anchor_date_field = SchemaDischarge.ANCHOR_DATE_FIELD
        self.schema_version = SchemaDischarge.VERSION
        self.registry_subset = SchemaDischarge.REGISTRY_SUBSET
        self.hospital_column_list = ['servicesitename', 'hospname', 'servicesitecode']
        self.mrn_cleaner_regex = re.compile("[a-zA-Z\-\s\_\/=\"<>]")

    def denormalize(self):
        self.output = self.reader.out.drop(columns=['additional_fields'])

    def post_process(self):
        """
        Using custom logic to get care entity id, tenant id. The Admin Discharges files contain multiple CEs in the
        same file.
        """
        try:
            self.output['hospital'] = (self.output.
                                       apply(lambda r: get_care_entities(str(r.servicesitename), str(r.servicesitecode),
                                                                         self.client)['code'].values[0], axis=1))
        except IndexError as e:
            logging.error("Translator post processing cannot find hospital by servicesitename or "
                          "servicesitecode attempting to find hospital by hospname field.")
            self.output['hospital'] = None
        try:
            if len(self.output['hospital'].unique()) == 1 and self.output['hospital'].unique()[0] is None:
                self.output['hospital'] = (self.output.
                                           apply(lambda r: get_care_entities(str(r.hospname), str(r.hospname),
                                                                             self.client)['code'].values[0], axis=1))
        except Exception as e:
            logging.error("Translator post processing cannot find hospital by hospname "
                          "attempting to find hospital by file name.")
        if len(self.output['hospital'].unique()) == 1 and self.output['hospital'].unique()[0] is None:
            self.output['hospital'] = find_hospital(self.client, self.output['filename'][0])
        if len(self.output['hospital'].unique()) == 1 and self.output['hospital'].unique()[0] is None:
            raise HospitalRequiredError("hospital")

        self.output['hospname'] = self.output['hospital']
        self.output['servicesitename'] = self.output['hospital']
        ce = get_all_care_entities(self.client)
        self.output['careentityid'] = self.output['hospname'].map(dict(zip(ce['code'], ce['id'])))
        self.output['tenantid'] = self.output['hospname'].astype(str).map(dict(zip(ce['code'], ce['tenant_id'])))
        self.output['medrecn'] = list(map(
            lambda m: re.sub(self.mrn_cleaner_regex, "", m).zfill(8)
            if m is not None and isinstance(m, str)
            else m,
            [
                str(int(i))
                if isinstance(i, float) and not math.isnan(i)
                else str(i)
                for i in list(self.output['medrecn'])
            ],
        )
        )  # This is lifted from Ingestion 1.5 and reformatted for flake8 compliance

        if ('age' in self.output.columns and len(self.output['age'].unique()) == 1 and
                str(self.output['age'].unique()[0]) in ['None', '', '0.0', '0']):
            self.output['age'] = (pd.to_datetime(self.output['admitdate']) - pd.to_datetime(
                self.output['dob'])).dt.days / 365.25

        self.output['yearmonth'] = pd.to_datetime(self.output['dischargedate']).dt.strftime('%Y%m')
        # self.output = add_biomeencounterid(self.output, self.client, 'Admin', identifier='encounternumber',
        #                                    mrn='medrecn', anchordate='dischargedate')

    def create_table_name(self):
        self.output[self.anchor_date_field] = pd.to_datetime(self.output[self.anchor_date_field])
        period = self.output[self.anchor_date_field].dt.to_period('Q').unique().strftime('%yQ%q')
        period = [x for x in period if str(x) != 'nan']
        if len(period) == 1:
            period = period[0]
        else:
            period = '_'.join([period[0], period[-1]])

        dsm = 'ADMIN_' + self.dataset + '_' + self.schema_version.replace('.', '')
        self.target_table = create_table_name(file_info=self.file_info,
                                              dsm=dsm,
                                              hospname=','.join(self.output['datasetname'].astype(str).unique()),
                                              period=period)

    def execute(self):
        self.denormalize()
        self.lower_case_fields()
        self.change_delimiters()
        self.transform_field_values()
        self.lower_case_fields()
        self.value_mapping()
        self.clean_field_values()
        self.post_process()
        self.standardize_datetime()
        self.enforce_schema()
        self.fix_field_lengths()

        FileInfo.get_file_info(self)
        self.create_table_name()
        FileInfo.add_file_info(self, output=self.output, anchor_date_field=self.anchor_date_field,
                               target_db=self.target_db, target_table=self.target_table,
                               registry_subset=self.registry_subset, hosp_field='hospname')
        FileInfo.update_client_data_file(self)
        self.output[self.anchor_date_field] = self.output[self.anchor_date_field].dt.strftime('%Y-%m-%dT%H:%M:%S')

        if self._write:
            self.write_data()
