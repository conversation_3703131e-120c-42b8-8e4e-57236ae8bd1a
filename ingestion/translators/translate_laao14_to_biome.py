import logging

import numpy as np
import pandas as pd

from ingestion.readers.laao import Reader<PERSON><PERSON>O14
from ingestion.schema.laao import SchemaLAAO14
from ingestion.config import read_config
from ingestion.translators import TranslatorBase
from ingestion.readers.common import ReadNCDRTables
from ingestion.utils.common import create_table_name
from ingestion.utils.file import FileInfo


class TranslateLAAO14ToBiome(TranslatorBase, FileInfo):
    """
    translates the output of LAAO 14 to Biome v1
    """

    def __init__(self, filepath, schema, reader, file_id, client, write=False, target_db='DB_EARASS',
                 db_as_source=False, source_db=None):
        TranslatorBase.__init__(self)
        FileInfo.__init__(self, file_id=file_id, client=client)

        if db_as_source:

            if source_db is None:
                logging.error("Source database must be provided.")
                raise ValueError("Source database must be provided.")

            logging.info('Using client raw database as source for NCDR data')
            self.reader = ReadNCDRTables(source_db, SchemaLAAO14.TABLE_PREFIX, file_id)
            self.reader.execute()

        else:
            if isinstance(schema, SchemaLAAO14):
                self.schema = schema
            else:
                logging.info('Schema class instance not provided, creating schema from data dictionary')
                self.schema = SchemaLAAO14()
                self.schema.execute()

            if isinstance(reader, ReaderLAAO14):
                self.reader = reader
            else:
                logging.info('Reader class instance not provided, reading from file')
                self.reader = ReaderLAAO14(filepath=filepath, schema=self.schema)
                self.reader.execute()

        self.output = pd.DataFrame()
        self._write = write
        self.target_table = None
        self.target_db = target_db

        self.dataset = SchemaLAAO14.DATASET + '14'
        self.dataset_id = SchemaLAAO14.DATASET_ID
        self.target_schema = read_config(self.dataset, 'biome_schema')
        self.filepath = filepath
        self.file_id = file_id
        self.file_info = None
        self.anchor_date_field = SchemaLAAO14.ANCHOR_DATE_FIELD
        self.schema_version = SchemaLAAO14.VERSION
        self.registry_subset = SchemaLAAO14.REGISTRY_SUBSET
        self.drop_fields_on_join = ['IncrementalId', 'ClientFileId', 'FileName', 'Version', 'BiomeImportDt',
                                    'DatasetName', 'PartId', 'PartName', 'ParentIncrementalId']

    def post_process(self):
        TranslatorBase.post_process(self)
        # convert procedure start date and time to separate columns
        self.output['procedurestartdate'] = pd.to_datetime(self.output['procedurestartdatetime']).dt.date
        self.output['procedurestarttime'] = pd.to_datetime(self.output['procedurestartdatetime']).dt.time

        self.output['procedurestopdate'] = pd.to_datetime(self.output['procedureenddatetime']).dt.date
        self.output['procedurestoptime'] = pd.to_datetime(self.output['procedureenddatetime']).dt.time

        self.output['yearmonth'] = pd.to_datetime(self.output['dischargedate']).dt.strftime('%Y%m')

        self.output['age'] = (pd.to_datetime(self.output['arrivaldate']) - pd.to_datetime(
            self.output['dob'])).dt.days / 365.25

        # truncate age above 90
        self.output['age'] = np.where(self.output['age'] > 90, 90, self.output['age'])

        self.output['registryver'] = SchemaLAAO14.VERSION
        self.output['registryid'] = 'ACC-NCDR-LAAO'

    def create_table_name(self):
        period = pd.to_datetime(self.output[self.anchor_date_field]).dt.to_period('Q').unique().strftime('%yQ%q')
        if len(period) == 1:
            period = period[0]
        else:
            period = '_'.join([period[0], period[-1]])

        self.target_table = create_table_name(file_info=self.file_info,
                                              dsm=SchemaLAAO14.DATASET,
                                              hospname=','.join(self.output['hospname'].astype(str).unique()),
                                              period=period)

    def execute(self):
        TranslatorBase.execute(self)

        FileInfo.get_file_info(self)
        self.create_table_name()
        FileInfo.add_file_info(self, output=self.output, anchor_date_field=self.anchor_date_field,
                               target_db=self.target_db, target_table=self.target_table,
                               registry_subset=self.registry_subset)
        FileInfo.update_client_data_file(self)

        if self._write:
            self.write_data()
