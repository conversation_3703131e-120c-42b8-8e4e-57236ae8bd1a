import logging

import numpy as np
import pandas as pd

from ingestion.readers.afib import ReaderAFib20
from ingestion.schema.afib import SchemaAFib20
from ingestion.config import read_config
from ingestion.translators import TranslatorBase
from ingestion.readers.common import ReadNCDRTables
from ingestion.utils.common import create_table_name
from ingestion.utils.file import FileInfo


class TranslateAFib20ToBiome(TranslatorBase, FileInfo):
    """
    Translate the output of AFIB V2 to Biome V1
    """

    def __init__(self, filepath, schema, reader, file_id, client, write=False, target_db='DB_ASHISH',
                 db_as_source=False, source_db=None):
        TranslatorBase.__init__(self)
        FileInfo.__init__(self, file_id=file_id, client=client)

        if db_as_source:

            if source_db is None:
                logging.error("Source database must be provided.")
                raise ValueError("Source database must be provided.")

            logging.info('Using client raw database as source for NCDR data')
            self.reader = ReadNCDRTables(source_db, SchemaAFib20.TABLE_PREFIX, file_id)
            self.reader.execute()

        else:
            if isinstance(schema, SchemaAFib20):
                self.schema = schema
            else:
                logging.info('Schema class instance not provided, creating schema from data dictionary')
                self.schema = SchemaAFib20()
                self.schema.execute()

            if isinstance(reader, ReaderAFib20):
                self.reader = reader
            else:
                logging.info('Reader class instance not provided, reading from file')
                self.reader = ReaderAFib20(filepath=filepath, schema=self.schema)
                self.reader.execute()

        self.output = pd.DataFrame()
        self._write = write
        self.target_table = None
        self.target_db = target_db

        self.dataset = SchemaAFib20.DATASET + '20'
        self.dataset_id = SchemaAFib20.DATASET_ID
        self.target_schema = read_config(self.dataset, 'biome_schema')
        self.filepath = filepath
        self.file_id = file_id
        self.file_info = None
        self.anchor_date_field = SchemaAFib20.ANCHOR_DATE_FIELD
        self.schema_version = SchemaAFib20.VERSION
        self.registry_subset = SchemaAFib20.REGISTRY_SUBSET
        self.granular_section = 'PROCINFO'
        self.partname = 'partname'
        self.partid = 'particid'

    def rename_fields(self):
        TranslatorBase.rename_fields(self)

    def post_process(self):
        TranslatorBase.post_process(self)

        self.output['datavrsn'] = SchemaAFib20.VERSION
        self.output['yearmonth'] = pd.to_datetime(self.output['dischargedate']).dt.strftime('%Y%m')
        self.output['age'] = (pd.to_datetime(self.output['arrivaldate']) - pd.to_datetime(
            self.output['dob'])).dt.days / 365.25

        # truncate age above 90
        self.output['age'] = np.where(self.output['age'] > 90, 90, self.output['age'])

        self.output['casesequencenumber'] = self.output.groupby(['dischargedate', 'episodeid']).ngroup() + 1

    def create_table_name(self):
        period = pd.to_datetime(self.output[self.anchor_date_field]).dt.to_period('Q').unique().strftime('%yQ%q')
        if len(period) == 1:
            period = period[0]
        else:
            period = np.sort(period)
            period = '_'.join([period[0], period[-1]])

        dsm = 'AFIB' + self.schema_version.replace('.', '')
        self.target_table = create_table_name(file_info=self.file_info,
                                              dsm=dsm,
                                              hospname=','.join(self.output['hospname'].astype(str).unique()),
                                              period=period)

    def execute(self):
        TranslatorBase.execute(self)

        FileInfo.get_file_info(self)
        self.create_table_name()
        FileInfo.add_file_info(self, output=self.output, anchor_date_field=self.anchor_date_field,
                               target_db=self.target_db, target_table=self.target_table,
                               registry_subset=self.registry_subset)
        FileInfo.update_client_data_file(self)

        if self._write:
            self.write_data()
