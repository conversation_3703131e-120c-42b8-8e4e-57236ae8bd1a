import logging

import pandas as pd
import numpy as np

from ingestion.readers.cpmi import ReaderCPMI30
from ingestion.schema.cpmi import SchemaCPMI30
from ingestion.config import read_config
from ingestion.translators import TranslatorBase
from ingestion.readers.common import ReadNCDRTables
from ingestion.utils.common import create_table_name
from ingestion.utils.file import FileInfo


class TranslateCPMI30ToBiome(TranslatorBase, FileInfo):
    """
    translates the output of CPMI 30 to Biome v1
    """

    def __init__(self, filepath, schema, reader, file_id, client, write=False, target_db='DB_EARASS',
                 db_as_source=False, source_db=None):
        TranslatorBase.__init__(self)
        FileInfo.__init__(self, file_id=file_id, client=client)

        if db_as_source:

            if source_db is None:
                logging.error("Source database must be provided.")
                raise ValueError("Source database must be provided.")

            logging.info('Using client raw database as source for NCDR data')
            self.reader = ReadNCDRTables(source_db, SchemaCPMI30.TABLE_PREFIX, file_id)
            self.reader.execute()

        else:
            if isinstance(schema, SchemaCPMI30):
                self.schema = schema
            else:
                logging.info('Schema class instance not provided, creating schema from data dictionary')
                self.schema = SchemaCPMI30()
                self.schema.execute()

            if isinstance(reader, ReaderCPMI30):
                self.reader = reader
            else:
                logging.info('Reader class instance not provided, reading from file')
                self.reader = ReaderCPMI30(filepath=filepath, schema=self.schema)
                self.reader.execute()

        self.output = pd.DataFrame()
        self._write = write
        self.target_table = None
        self.target_db = target_db

        self.dataset = SchemaCPMI30.DATASET + '30'
        self.dataset_id = SchemaCPMI30.DATASET_ID
        self.target_schema = read_config(self.dataset, 'biome_schema')
        self.filepath = filepath
        self.file_id = file_id
        self.file_info = None
        self.anchor_date_field = SchemaCPMI30.ANCHOR_DATE_FIELD
        self.schema_version = SchemaCPMI30.VERSION
        self.granular_section = 'EPISODEOFCARE'
        self.registry_subset = SchemaCPMI30.REGISTRY_SUBSET

    def post_process(self):
        TranslatorBase.post_process(self)

        # fix the format of symptomtime
        if 'symptomtime' in self.output.columns:
            self.output['symptomtime'] = self.output['symptomtime'].apply(lambda x: str(x).split(' days ')[-1]).replace(
                ['NaT', 'nat', 'nan', 'none'], np.nan)
        self.output['datavrsn'] = '3.0'

    def create_table_name(self):
        period = pd.to_datetime(self.output[self.anchor_date_field]).dt.to_period('Q').unique().strftime('%yQ%q')
        if len(period) == 1:
            period = period[0]
        else:
            period = '_'.join([period[0], period[-1]])

        dsm = 'CPMI'
        self.target_table = create_table_name(file_info=self.file_info,
                                              dsm=dsm,
                                              hospname=','.join(self.output['hospname'].astype(str).unique()),
                                              period=period)

    def execute(self):
        TranslatorBase.execute(self)

        FileInfo.get_file_info(self)
        self.create_table_name()
        FileInfo.add_file_info(self, output=self.output, anchor_date_field=self.anchor_date_field,
                               target_db=self.target_db, target_table=self.target_table,
                               registry_subset=self.registry_subset)
        FileInfo.update_client_data_file(self)

        if self._write:
            self.write_data()
