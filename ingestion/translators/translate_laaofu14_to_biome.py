import logging

import numpy as np
import pandas as pd

from ingestion.readers.laao import ReaderLAAOFU14
from ingestion.schema.laao import SchemaLAAOFU14
from ingestion.config import read_config
from ingestion.translators import TranslatorBase
from ingestion.readers.common import ReadNCDRTables
from ingestion.utils.common import create_table_name
from ingestion.utils.file import FileInfo


class TranslateLAAOFU14ToBiome(TranslatorBase, FileInfo):
    """
    translates the output of LAAO 14 to Biome v1
    """

    def __init__(self, filepath, schema, reader, file_id, client, write=False, target_db='DB_EARASS',
                 db_as_source=False, source_db=None):
        TranslatorBase.__init__(self)
        FileInfo.__init__(self, file_id=file_id, client=client)

        if db_as_source:

            if source_db is None:
                logging.error("Source database must be provided.")
                raise ValueError("Source database must be provided.")

            logging.info('Using client raw database as source for NCDR data')
            self.reader = ReadNCDRTables(source_db, SchemaLAAOFU14.TABLE_PREFIX, file_id)
            self.reader.execute()

        else:
            if isinstance(schema, SchemaLAAOFU14):
                self.schema = schema
            else:
                logging.info('Schema class instance not provided, creating schema from data dictionary')
                self.schema = SchemaLAAOFU14()
                self.schema.execute()

            if isinstance(reader, ReaderLAAOFU14):
                self.reader = reader
            else:
                logging.info('Reader class instance not provided, reading from file')
                self.reader = ReaderLAAOFU14(filepath=filepath, schema=self.schema)
                self.reader.execute()

        self.output = pd.DataFrame()
        self._write = write
        self.target_table = None
        self.target_db = target_db

        self.dataset = SchemaLAAOFU14.DATASET + '14'
        self.dataset_id = SchemaLAAOFU14.DATASET_ID
        self.target_schema = read_config(self.dataset, 'biome_schema')
        self.filepath = filepath
        self.file_id = file_id
        self.file_info = None
        self.anchor_date_field = SchemaLAAOFU14.ANCHOR_DATE_FIELD
        self.schema_version = SchemaLAAOFU14.VERSION
        self.registry_subset = SchemaLAAOFU14.REGISTRY_SUBSET
        self.granular_section = 'FOLLOWUP'
        self.is_followup = True
        self.drop_fields_on_join = ['IncrementalId', 'ClientFileId', 'FileName', 'Version', 'BiomeImportDt',
                                    'DatasetName', 'PartId', 'PartName', 'ParentIncrementalId']

    def post_process(self):
        TranslatorBase.post_process(self)
        self.output['procedurestartdate'] = pd.to_datetime(self.output['procedurestartdatetime'])
        self.output['procedurestarttime'] = pd.to_datetime(self.output['procedurestartdatetime']).dt.time

        self.output['yearmonth'] = pd.to_datetime(self.output['dischargedate']).dt.strftime('%Y%m')

        self.output['registryver'] = SchemaLAAOFU14.VERSION
        self.output['registryid'] = 'ACC-NCDR-LAAO'

    def create_table_name(self):
        period = pd.to_datetime(self.output[self.anchor_date_field]).dt.to_period('Q').unique().strftime('%yQ%q')
        if len(period) == 1:
            period = period[0]
        else:
            period = period.astype(str)
            period = np.sort(period)
            period = '_'.join([period[0], period[-1]])
        self.target_table = create_table_name(file_info=self.file_info,
                                              dsm=SchemaLAAOFU14.DATASET,
                                              hospname=','.join(self.output['hospname'].astype(str).unique()),
                                              period=period)

    def execute(self):
        TranslatorBase.execute(self)

        FileInfo.get_file_info(self)
        self.create_table_name()
        FileInfo.add_file_info(self, output=self.output, anchor_date_field=self.anchor_date_field,
                               target_db=self.target_db, target_table=self.target_table,
                               registry_subset=self.registry_subset)
        FileInfo.update_client_data_file(self)

        if self._write:
            self.write_data()
