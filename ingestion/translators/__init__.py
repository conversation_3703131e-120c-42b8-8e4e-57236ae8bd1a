import pandas as pd
import numpy as np
import re

from ingestion.utils import logging
from ingestion.config import read_config
from ingestion.utils.odin import get_care_entities
from ingestion.utils.db import insert_data, create_table
from ingestion.utils.common import infer_id_fields, smart_merge
from ingestion.utils import datetime as dt


class TranslatorBase:

    def __init__(self):
        self.dataset = None
        self.output = pd.DataFrame()
        self.target_schema = None
        self.filepath = None
        self.schema = None
        self.reader = None
        self.target_table = None
        self.target_db = None
        self.is_followup = False

        self.granular_section = 'PROCINFO'  # the first section to join all sections with while flattening the tables
        self.drop_fields_on_join = ['IncrementalId', 'ClientFileId', 'FileName', 'Version', 'BiomeImportDt',
                                    'DatasetName', 'PartId', 'PartName']  # drop these from raw tables while joining
        self.partname = 'partname'
        self.partid = 'partid'

    def denormalize(self):

        """
        Joins multiple sectioned tables to create one flat table.
        Some sections are skipped because the data from these are added using config
        """
        logging.info('Translator: Denormalize')
        exclude_sections = []
        pivot_sections = read_config(self.dataset, 'pivot_sections', as_df=False)
        parent_child_sections = read_config(self.dataset, 'parent_child_sections')
        if pivot_sections is not None:
            exclude_sections = exclude_sections + pivot_sections
        if not parent_child_sections.empty:
            exclude_sections = exclude_sections + parent_child_sections[
                'Parent Section'].tolist() + parent_child_sections['Child Section'].tolist()
        self.output = self.reader.ncdr_tables[self.granular_section].drop('IncrementalId', axis=1)
        for table_name, data in self.reader.ncdr_tables.items():
            # pivot sections are removed because there are separate columns for each of the values in the fields
            # of these sections like for DISCHMED section, the current ingestion has separate field for each med
            # data from these sections is added in the append_custom_fields function
            if not data.empty and table_name not in [self.granular_section] + exclude_sections:
                logging.info(f"Joining {table_name}")
                keys = infer_id_fields(data, key_field='FollowupKey' if self.is_followup else 'EpisodeKey')
                data.drop(self.drop_fields_on_join, axis=1, inplace=True)
                self.output = smart_merge(self.output, data, keys, how='left').drop_duplicates()
            else:
                logging.info(f"Skipping {table_name}")

        # for sections that need to keep parent child relationship e.g. ACCESSSYS and DEVICES sections in LAAO
        # the data of both sections is in separate raw tables, so to match the row of child section with correct parent
        # section, parent incremental id and incremental id is used
        if not parent_child_sections.empty:
            joined_parents = {}
            for parent, group in parent_child_sections.groupby('Parent Section'):
                parent_table_name = parent
                logging.info(f"Processing parent table {parent_table_name}")
                parent_table = self.reader.ncdr_tables[parent_table_name].copy()
                if not parent_table.empty:
                    parent_keys = infer_id_fields(
                        parent_table, key_field='FollowupKey' if self.is_followup else 'EpisodeKey'
                    )
                    parent_table.drop([f for f in self.drop_fields_on_join if f != 'IncrementalId'], axis=1,
                                      inplace=True)
                    parent_inc_id = f'{parent_table_name}IncrementalId'
                    parent_table.rename(columns={'IncrementalId': parent_inc_id}, inplace=True)

                    if parent not in joined_parents:
                        joined_parents[parent] = parent_table

                    for ind, row in group.iterrows():
                        child_table_name = row['Child Section']
                        if child_table_name not in pivot_sections:
                            logging.info(f"Joining {row['Parent Section']} with {row['Child Section']}")
                            child_table = self.reader.ncdr_tables[child_table_name].copy()
                            child_keys = infer_id_fields(
                                child_table, key_field='FollowupKey' if self.is_followup else 'EpisodeKey'
                            )
                            child_table.drop([f for f in self.drop_fields_on_join if f != 'ParentIncrementalId'],
                                             axis=1, inplace=True)

                            joined_parent_table = joined_parents[parent]
                            joined_parent_table = joined_parent_table.merge(
                                child_table,
                                left_on=parent_keys + [parent_inc_id],
                                right_on=child_keys + ['ParentIncrementalId'],
                                how='left'
                            )

                            joined_parents[parent] = joined_parent_table

                    self.output = smart_merge(self.output, joined_parents[parent],
                                              parent_keys, how='left').drop_duplicates()

    def append_custom_fields(self):
        """
        Creates custom fields as present in the Biome v1 using a configuration. The fields are from sections like
        Medications, Events etc and the fields are separate for each specific Medication or Event. These fields are
        added in the config with the fields and values they are derived from.
        """
        config = read_config(self.dataset, 'custom_fields')
        parent_child_sections = read_config(self.dataset, 'parent_child_sections')
        if config.empty:
            logging.info('Translator: No custom fields to append')
            return
        logging.info('Translator: Appending custom fields')
        for table_name, conf in config.groupby('Section'):
            if table_name in self.reader.ncdr_tables:
                client_data = self.reader.ncdr_tables[table_name].copy()
                if not client_data.empty:
                    keys = infer_id_fields(client_data, key_field='FollowupKey' if self.is_followup else 'EpisodeKey')
                    if not parent_child_sections.empty and table_name in parent_child_sections['Child Section'].tolist(
                    ):
                        join_cols = keys + ['ParentIncrementalId']
                        parent = parent_child_sections[
                            parent_child_sections['Child Section'] == table_name]['Parent Section'].values[0]
                        parent_join_cols = keys + [f'{parent}IncrementalId']
                    else:
                        join_cols = keys
                        parent_join_cols = keys
                    df_new = client_data[join_cols].drop_duplicates().copy()
                    for row, gr in conf.iterrows():
                        new_field = gr['Field']
                        der_field = gr['Derivative Field']
                        der_value = gr['Derivative Value']
                        out_value = gr['Reference Field']
                        value_map = gr['Value Map']
                        exact_match = gr['Exact Match'] == 1
                        delimiter = gr['Delimiter']
                        regex_match = gr.get('Regex Match', 0) == 1
                        case_match = gr.get('Case Match', 0) == 1

                        der_field_ser = client_data[der_field].copy()

                        if str(delimiter) not in ['None', 'nan', '']:
                            der_field_ser = delimiter + der_field_ser.astype(str) + delimiter
                            if not der_value.startswith(delimiter):
                                der_value = delimiter + der_value
                            if not der_value.endswith(delimiter):
                                der_value = der_value + delimiter

                        if exact_match:
                            cond = der_field_ser == der_value
                        else:
                            cond = der_field_ser.str.contains(der_value, na=False, case=case_match, regex=regex_match)

                        df = client_data[cond].copy()

                        df[new_field] = df[out_value]

                        if isinstance(value_map, dict):
                            df[new_field] = df[new_field].map(value_map)
                            if not exact_match and not regex_match:
                                df[new_field] = value_map.get(der_value)

                        elif isinstance(value_map, str) and value_map not in ['None', 'nan', '']:
                            value_map = eval(value_map)
                            df[new_field] = df[new_field].map(value_map)
                            if not exact_match:
                                df[new_field] = value_map.get(der_value)

                        df = df[join_cols + [new_field]].drop_duplicates()
                        df_new = df_new.join(df.set_index(join_cols), on=join_cols, how='left')

                    self.output = smart_merge(self.output, df_new, keys=None, how='left', left_on=parent_join_cols,
                                              right_on=join_cols)
                    if 'ParentIncrementalId' in self.output.columns:
                        self.output.drop('ParentIncrementalId', axis=1, inplace=True)

    def rename_fields(self):
        """
        Renames fields as present in Biome v1
        """
        config = read_config(self.dataset, 'rename_fields')
        if config.empty:
            logging.info('Translator: No fields to rename')
            return
        logging.info('Translator: Renaming fields')
        config['Old Field'] = config['Old Field'].str.lower()
        config['New Field'] = config['New Field'].str.lower()
        # not rename the ones that already exist with same name
        cond = (config['Old Field'].isin(self.output.columns.str.lower().tolist()))
        config = config[~cond]
        additional_fields = config[config.duplicated('New Field', keep='last')].to_dict('records')
        new_fields = config['New Field'].tolist()
        old_fields = config['Old Field'].tolist()
        rename = dict(zip(new_fields, old_fields))
        self.output = self.output.rename(columns=rename)
        for af in additional_fields:
            self.output[af['Old Field']] = self.output[rename[af['New Field']]]

    def enforce_schema(self):
        """
        1. Keeps only the fields that are present in target schema
        2. Creates missing fields as null
        """
        logging.info('Translator: Enforcing schema')
        target_fields = self.target_schema['Field'].str.lower().tolist()
        self.output.columns = self.output.columns.str.lower()

        # add missing fields as null
        missing_fields = set(target_fields) - set(self.output.columns)
        logging.info(f"Adding missing fields: {missing_fields}")
        new_columns = {field: [None] * len(self.output) for field in missing_fields}
        new_columns_df = pd.DataFrame(new_columns)
        self.output = pd.concat([self.output.reset_index(drop=True), new_columns_df.reset_index(drop=True)],
                                axis=1)

        fields = [f for f in self.output.columns if f in target_fields]
        self.output = self.output[fields].drop_duplicates()

    def value_mapping(self):
        """
        Maps values as per the config to match the values in the Biome v1 schema
        """
        config = read_config(self.dataset, 'value_mapping', as_df=False)
        if not config:
            logging.info('Translator: No value mapping required')
            return
        logging.info('Translator: Mapping values')
        for field_map in config:
            field = field_map['Field'].lower()
            value_map = field_map['Value Map']
            replace_substr = field_map.get('Replace Substr', 0) == 1
            if field in self.output.columns:
                if replace_substr:
                    for val, repl in value_map.items():
                        self.output[field] = self.output[field].str.replace(val, repl)
                else:
                    self.output[field] = self.output[field].map(value_map).fillna(self.output[field])

    def change_delimiters(self):
        """
        For some fields where the client data has multiple values e.g. SegmentId, the default delimiters in the reader
        is pipe(|). In Biome v0.5, the delimiters are not consistent. For some fields, it is comma(,) for some other,
        it is pipe(|). So using this config to change delimiters where required. This also encloses values with
        specified string where mentioned
        """
        config = read_config(self.dataset, 'delimiters')
        if config.empty:
            logging.info('Translator: No delimiters to change')
            return
        logging.info('Translator: Changing delimiters')
        na_values = ['None', 'nan']
        for row, gr in config.iterrows():
            field = gr['Field'].lower()
            sep = gr['Delimiter']
            rep = gr['Replace']
            enc = gr['Enclose']
            if field in self.output.columns:
                if str(rep) not in na_values:
                    self.output[field] = self.output[field].apply(
                        lambda x: x.replace(rep, sep) if str(x) not in na_values else x)
                if enc:
                    self.output[field] = self.output[field].apply(
                        lambda x: f'{sep}{x}{sep}' if str(x) not in na_values else x)

    def fix_field_lengths(self):
        """
        Fix lengths for varchar fields
        If the length defined in the biome schema is less than the max length in the column, then the max length
        would be used as the varchar length. This is only for the fields where dtype is varchar
        """
        logging.info('Translator: Fixing field lengths')
        self.output.columns.name = 'Cols'
        max_lengths = pd.DataFrame(
            self.output.apply(lambda col: col.astype(str).apply(len).max()),
            columns=['NewLen']
        ).reset_index().rename(columns={'Cols': 'FieldL'})
        self.target_schema['OldLen'] = self.target_schema['Dtype'].str.extract(r'\((\d+(\.\d+)?)\)').astype(float)[0]
        self.target_schema['FieldL'] = self.target_schema['Field'].str.lower()
        max_lengths['FieldL'] = max_lengths['FieldL'].str.lower()
        self.target_schema = self.target_schema.merge(max_lengths, on='FieldL', how='left')
        cond = (self.target_schema['Dtype'].str.contains('varchar')) & (
            self.target_schema['OldLen'] < self.target_schema['NewLen'])
        self.target_schema['Dtype'] = np.where(
            cond, 'varchar(' + self.target_schema['NewLen'].astype(str) + ')', self.target_schema['Dtype']
        )
        self.target_schema = self.target_schema[['Field', 'Dtype']].drop_duplicates()

    def post_process(self):
        """
        post-processing steps that are common in all translators
        """
        logging.info('Translator: Post processing')

        """ add Odin info """
        ce = get_care_entities(self.output[self.partname].values[0], self.output[self.partid].values[0],
                               self.output['datasetname'].values[0])
        if not ce.empty:
            self.output['careentityid'] = ce['id'].values[0]
            self.output['tenantid'] = ce['tenant_id'].values[0]
            self.output['hospname'] = ce['code'].values[0]

        # convert DOB column to date string
        self.output['dob'] = pd.to_datetime(self.output['dob']).dt.strftime('%Y-%m-%d')

        self.output['originalotherid'] = self.output['otherid']

    def lower_case_fields(self):
        self.output.columns = self.output.columns.str.lower()

    def transform_field_values(self):
        """
        transform field values or compute fields
        """

        def _conditional_or(row, fields, true_val='Yes', false_val=None):
            """Generic OR transformation for multiple fields"""
            return_val = row[false_val] if false_val is not None and false_val in row.index.tolist() else false_val
            if any(any(v.upper() in str(row[f['name']]).upper() for v in f['values']) for f in fields):
                return_val = row[true_val] if (true_val is not None and true_val in row.index.tolist()) else true_val
            return return_val

        def _conditional_and(row, fields, true_val=None, false_val=None):
            """Generic AND transformation for multiple fields"""
            return_val = row[false_val] if false_val is not None and false_val in row.index.tolist() else false_val
            if all(any(v.upper() in str(row[f['name']]).upper() for v in f['values']) for f in fields):
                return_val = row[true_val] if (true_val is not None and true_val in row.index.tolist()) else true_val
            return return_val

        def _conditional_concat(row, field_map, prefix='|', suffix='|'):
            """Generic conditional concatenation of fields"""
            parts = []
            for field, text in field_map.items():
                if str(row[field]).upper() in ('YES', '1', '1.0', 'TRUE'):
                    parts.append(f"{prefix}{text}{suffix}")
            return ''.join(parts).replace(f"{suffix}{prefix}", suffix)

        def _fallback_fields(row, field, fallback_fields, default=None):
            """Handle fallback field transformations"""
            if pd.notna(row[field]):
                return row[field]
            for fallback in fallback_fields:
                if fallback in row and pd.notna(row[fallback]):
                    return row[fallback]
            return default if default is not None else row[field]

        def _transform_simple_clean(row, field, empty_values=['']):
            value = row[field]
            if pd.isna(value):
                return value
            str_value = str(value)
            return None if str_value in empty_values else value

        def _transform_replace(row, field, old_values=[''], new_values=['']):
            return str(row[field]).replace(old_values, new_values) if pd.notna(row[field]) else None

        def _transform_split_join(row, field, split_char='-', extract_part='prefix', source_field=None):
            value = row[field]
            if pd.isna(value):
                return value
            value = row[source_field]
            str_value = str(value)
            if split_char in str_value:
                parts = str_value.split(split_char)
                if extract_part == 'prefix':
                    return parts[0].strip()
                elif extract_part == 'suffix':
                    return split_char.join(parts[1:]).strip()
            return value

        def _transform_scientific_to_decimal(row, field, default_value='0', format_str='.20f'):
            value = row[field] if field in row else row[field]
            if pd.isna(value) or str(value).upper() in ('NONE', 'NAN', ''):
                return default_value
            str_value = str(value).replace('e', 'E')
            if 'E' in str_value:
                base, exponent = str_value.split('E')
                return format(float(base) * (10 ** int(exponent)), format_str)
            return str_value

        def _transform_add(row, fields, format_str='.20f'):
            if str(row[fields[0]]).upper() not in ('NONE', 'NAN', '') and str(row[fields[1]]
                                                                              ).upper() not in ('NONE', 'NAN', ''):
                val1 = str(row[fields[0]]).translate(str.maketrans('', '', '$,')
                                                     ).replace('(', '-').replace(')', '')
                val2 = str(row[fields[1]]).translate(str.maketrans('', '', '$,')
                                                     ).replace('(', '-').replace(')', '')
                return format(float(val1) + float(val2), format_str)
            return None

        def _transform_subtract(row, fields, format_str='.20f'):
            if str(row[fields[0]]).upper() not in ('NONE', 'NAN', '') and str(row[fields[1]]
                                                                              ).upper() not in ('NONE', 'NAN', ''):
                val1 = str(row[fields[0]]).translate(str.maketrans('', '', '$,')
                                                     ).replace('(', '-').replace(')', '')
                val2 = str(row[fields[1]]).translate(str.maketrans('', '', '$,')
                                                     ).replace('(', '-').replace(')', '')
                return format(float(val1) - float(val2), format_str)
            return None

        transform_map = {
            'conditional_concat': _conditional_concat,
            'conditional_or': _conditional_or,
            'conditional_and': _conditional_and,
            'fallback_fields': _fallback_fields,
            'simple_clean': _transform_simple_clean,
            'replace': _transform_replace,
            'split_join': _transform_split_join,
            'scientific_to_decimal': _transform_scientific_to_decimal,
            'add': _transform_add,
            'subtract': _transform_subtract,
            # 'additional_field' is handled separately as it doesn't use .apply with a row
        }

        configs = read_config(self.dataset, 'transform_fields', as_df=False)
        if not configs:
            logging.info('Translator: No transformation required')
            return
        logging.info('Translator: transforming field values')

        for config in configs:
            field = config['Field'].lower()
            transform_type = config['Transform Type']
            args = config.get('Args', {})
            is_computation = config.get('Computation', 0) == 1

            if (is_computation and field in self.output.columns and self.output[field].notna().any() and (
                    self.output[field] != '0').any()):
                continue

            # Ensure the field exists in the DataFrame if it's not a computation
            if not is_computation and field not in self.output.columns:
                self.output[field] = None

            try:
                if transform_type == 'additional_field':
                    is_custom_string = args.get('custom string', 0) == 1
                    source = args['Source']
                    if is_custom_string:
                        self.output[field] = source
                    else:
                        self.output[field] = self.output[source] if source in self.output.columns else None
                elif transform_type in transform_map:
                    # For transformations that require the 'field' argument to the inner function
                    # but it's not explicitly passed in args (like simple_clean, fallback_fields)
                    if transform_type in ['simple_clean', 'fallback_fields', 'split_join', 'replace',
                                          'scientific_to_decimal']:
                        self.output[field] = self.output.apply(
                            lambda row: transform_map[transform_type](row, field, **args), axis=1)
                    # For transformations that don't need the field passed explicitly or handle it in args
                    else:
                        self.output[field] = self.output.apply(
                            lambda row: transform_map[transform_type](row, **args), axis=1)
                else:
                    logging.warning(
                        f"Translator: Unknown transform type '{transform_type}' for field '{field}'. Skipping.")

            except Exception as ex:
                logging.error("Translator: Failed to apply transformation on field {targetfield}, reason: {e}".
                              format(targetfield=field, e=str(ex)))

    def write_data(self):
        logging.info('Translator: Writing data')
        create_table(self.target_schema, table_name=self.target_table, db=self.target_db)
        insert_data(self.output, table_name=self.target_table, db=self.target_db, if_exists='append')

    def standardize_datetime(self):
        """
        ensure that date fileds have 'yyyy-mm-ddThh:mm:ss' ISO format.
        """
        configs = read_config(self.dataset, 'biome_schema', as_df=False)
        logging.info('Translator: standardizing datetime')
        for config in configs:
            field = config['Field'].lower()
            role = str(config['Role']).upper()
            if field in self.output.columns:
                if "DATE_ONLY" in role:
                    self.output[field] = dt.standardize_date_only_format(self.output[field])
                elif "DATE" in role:
                    self.output[field] = dt.standardize_date_format(self.output[field])
                elif "TIME" in role:
                    self.output[field] = dt.standardize_time_format(self.output[field])

    def clean_field_values(self):
        """
        remove unwanted characters based on given regular expression
        """
        configs = read_config(self.dataset, 'cleaner_regex', as_df=False)
        if not configs:
            logging.info('Translator: No fields with cleaner_regex')
            return
        logging.info('Translator: cleaning regex')
        for config in configs:
            field = config['Field'].lower()
            cleaner_regex = config['Regex']
            if cleaner_regex is not None and cleaner_regex != "":
                try:
                    self.output[field] = [
                        re.sub(cleaner_regex, "", str(e), flags=re.IGNORECASE)
                        if str(e).upper() not in ["NAT", "NAN", "NONE"]
                        else e
                        for e in list(self.output[field])
                    ]
                except Exception as e:
                    logging.error(f"Translator: Error while cleaning {field} using {cleaner_regex}. Error: {e}")

    def execute(self):
        self.denormalize()
        self.append_custom_fields()
        self.lower_case_fields()
        self.rename_fields()
        self.change_delimiters()
        self.lower_case_fields()
        self.clean_field_values()
        self.transform_field_values()
        self.value_mapping()
        self.post_process()
        self.standardize_datetime()
        self.enforce_schema()
        self.fix_field_lengths()
