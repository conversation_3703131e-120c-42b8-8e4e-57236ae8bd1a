import pandas as pd
import logging

from ingestion.readers.cstk import ReaderCST<PERSON>
from ingestion.schema.cstk import SchemaCSTK
from ingestion.config import read_config
from ingestion.translators import TranslatorBase
from ingestion.readers.common import ReadRawTable
from ingestion.utils.common import create_table_name
from ingestion.utils.file import FileInfo
from ingestion.utils.odin import get_care_entity_code_map


class TranslateCSTKToBiome(TranslatorBase, FileInfo):
    """
    translates the output of CSTK to Biome v1
    """

    def __init__(self, filepath, schema, reader, file_id, client, write=False, target_db='DB_EARASS',
                 db_as_source=False, source_db=None):
        TranslatorBase.__init__(self)
        FileInfo.__init__(self, file_id=file_id, client=client)

        if db_as_source:

            if source_db is None:
                logging.error("Source database must be provided.")
                raise ValueError("Source database must be provided.")

            logging.info('Using client raw database as source for raw data')
            self.reader = ReadRawTable(source_db, SchemaCSTK.TABLE_PREFIX, file_id)
            self.reader.execute()

        else:
            if isinstance(schema, SchemaCSTK):
                self.schema = schema
            else:
                logging.info('Schema class instance not provided, creating schema from data dictionary')
                self.schema = SchemaCSTK()
                self.schema.execute()

            if isinstance(reader, ReaderCSTK):
                self.reader = reader
            else:
                logging.info('Reader class instance not provided, reading from file')
                self.reader = ReaderCSTK(filepath=filepath, schema=self.schema, file_id=file_id, client=client)
                self.reader.execute()

        self.output = pd.DataFrame()
        self._write = write
        self.target_table = None
        self.target_db = target_db

        self.dataset = SchemaCSTK.DATASET
        self.dataset_id = SchemaCSTK.DATASET_ID
        self.target_schema = read_config(self.dataset, 'biome_schema')
        self.filepath = filepath
        self.file_id = file_id
        self.file_info = None
        self.anchor_date_field = SchemaCSTK.ANCHOR_DATE_FIELD
        self.schema_version = SchemaCSTK.VERSION
        self.registry_subset = SchemaCSTK.REGISTRY_SUBSET

    def denormalize(self):
        self.output = self.reader.out

    def post_process(self):
        """
        Using custom logic to get care entity id, tenant id. The cstk files contain multiple CEs in the same file.
        """
        ce = get_care_entity_code_map(self.client)
        self.output['hospname'] = self.output['servicesitecode'].astype(str).apply(lambda x: ce['code'].get(x))
        self.output['careentityid'] = self.output['servicesitecode'].astype(str).apply(lambda x: ce['id'].get(x))
        self.output['tenantid'] = self.output['servicesitecode'].astype(str).apply(lambda x: ce['tenant_id'].get(x))

        self.output['age'] = (pd.to_datetime(self.output['arrivaldatetime']) - pd.to_datetime(
            self.output['dob'])).dt.days / 365.25

    def create_table_name(self):
        self.output[self.anchor_date_field] = pd.to_datetime(self.output[self.anchor_date_field])
        period = self.output[self.anchor_date_field].dt.to_period('Q').unique().strftime('%yQ%q')
        period = [x for x in period if str(x) != 'nan']
        if len(period) == 1:
            period = period[0]
        else:
            period = '_'.join([period[0], period[-1]])

        dsm = self.dataset + '_' + self.schema_version.replace('.', '')
        self.target_table = create_table_name(file_info=self.file_info,
                                              dsm=dsm,
                                              hospname=','.join(self.output['datasetname'].astype(str).unique()),
                                              period=period)

    def execute(self):
        TranslatorBase.execute(self)

        FileInfo.get_file_info(self)
        self.create_table_name()
        FileInfo.add_file_info(self, output=self.output, anchor_date_field=self.anchor_date_field,
                               target_db=self.target_db, target_table=self.target_table,
                               registry_subset=self.registry_subset, hosp_field='hospname')
        FileInfo.update_client_data_file(self)
        self.output[self.anchor_date_field] = self.output[self.anchor_date_field].dt.strftime('%Y-%m-%dT%H:%M:%S')

        if self._write:
            self.write_data()
