import logging

import numpy as np
import pandas as pd

from ingestion.readers.icd import ReaderICD23
from ingestion.schema.icd import SchemaICD23
from ingestion.config import read_config
from ingestion.translators import TranslatorBase
from ingestion.readers.common import ReadNCDRTables
from ingestion.utils.common import create_table_name
from ingestion.utils.file import FileInfo


class TranslateICD23ToBiome(TranslatorBase, FileInfo):
    """
    translates the output of ICD 23 to Biome v1
    """

    def __init__(self, filepath, schema, reader, file_id, client, write=False, target_db='DB_EARASS',
                 db_as_source=False, source_db=None):
        TranslatorBase.__init__(self)
        FileInfo.__init__(self, file_id=file_id, client=client)

        if db_as_source:

            if source_db is None:
                logging.error("Source database must be provided.")
                raise ValueError("Source database must be provided.")

            logging.info('Using client raw database as source for NCDR data')
            self.reader = ReadNCDRTables(source_db, SchemaICD23.TABLE_PREFIX, file_id)
            self.reader.execute()

        else:
            if isinstance(schema, SchemaICD23):
                self.schema = schema
            else:
                logging.info('Schema class instance not provided, creating schema from data dictionary')
                self.schema = SchemaICD23()
                self.schema.execute()

            if isinstance(reader, ReaderICD23):
                self.reader = reader
            else:
                logging.info('Reader class instance not provided, reading from file')
                self.reader = ReaderICD23(filepath=filepath, schema=self.schema)
                self.reader.execute()

        self.output = pd.DataFrame()
        self._write = write
        self.target_table = None
        self.target_db = target_db

        self.dataset = SchemaICD23.DATASET + '23'
        self.dataset_id = SchemaICD23.DATASET_ID
        self.target_schema = read_config(self.dataset, 'biome_schema')
        self.filepath = filepath
        self.file_id = file_id
        self.file_info = None
        self.anchor_date_field = SchemaICD23.ANCHOR_DATE_FIELD
        self.schema_version = SchemaICD23.VERSION
        self.registry_subset = SchemaICD23.REGISTRY_SUBSET

    def post_process(self):
        TranslatorBase.post_process(self)
        self.output['proceduretime'] = pd.to_datetime(self.output['proceduredate']).dt.time
        self.output['procedureendtime'] = pd.to_datetime(self.output['procedureenddate']).dt.time

        self.output['yearmonth'] = pd.to_datetime(self.output['dischargedate']).dt.strftime('%Y%m')

        self.output['age'] = (pd.to_datetime(self.output['arrivaldate']) - pd.to_datetime(
            self.output['dob'])).dt.days / 365.25

        # truncate age above 90
        self.output['age'] = np.where(self.output['age'] > 90, 90, self.output['age'])

        self.output['datavrsn'] = SchemaICD23.VERSION
        self.output['registryid'] = 'ACC-NCDR-ICD'

    def rename_fields(self):
        TranslatorBase.rename_fields(self)
        # there is a circular dependency on the field RaceNatHaw, which is initially used to create field
        # RaceNatHawPacificIslander
        self.output.rename(columns={'RaceNativeHawaii': 'RaceNatHaw'}, inplace=True)

    def create_table_name(self):
        period = pd.to_datetime(self.output[self.anchor_date_field]).dt.to_period('Q').unique().strftime('%yQ%q')
        if len(period) == 1:
            period = period[0]
        else:
            period = '_'.join([period[0], period[-1]])

        self.target_table = create_table_name(file_info=self.file_info,
                                              dsm=SchemaICD23.DATASET + '_' + self.schema_version.replace('.', ''),
                                              hospname=','.join(self.output['hospname'].astype(str).unique()),
                                              period=period)

    def execute(self):
        TranslatorBase.execute(self)

        FileInfo.get_file_info(self)
        self.create_table_name()
        FileInfo.add_file_info(self, output=self.output, anchor_date_field=self.anchor_date_field,
                               target_db=self.target_db, target_table=self.target_table,
                               registry_subset=self.registry_subset)
        FileInfo.update_client_data_file(self)

        if self._write:
            self.write_data()
