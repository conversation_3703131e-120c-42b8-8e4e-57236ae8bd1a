import logging

import pandas as pd
import numpy as np

from ingestion.schema.cathpci import SchemaCathPCI5
from ingestion.utils.db import read_sql, insert_data
from ingestion.config import read_config


class TranslateBiomeToCathPCIV5:
    """
        translates the existing Biome v1 schema to CathPCI v5
    """

    def __init__(self, db_table_name, write=False, target_db='DB_EARASS'):
        self.db_table_name = db_table_name
        self.schema = None
        self.raw_data = pd.DataFrame()
        self.out_tables = {}
        self.schema_fields = {}
        self.target_db = target_db
        self.write = write

        self.dataset = SchemaCathPCI5.DATASET

        self.id_fields = ['ncdrpatientid', 'arrivaldatetime', 'procedurestartdatetime', 'dcdatetime']

    def gen_schema(self):
        """
        Generates the schema for the CathPCI5 dataset and stores it in self.schema_fields as a dictionary of tables
        """
        if not self.schema:
            self.schema = SchemaCathPCI5()
            self.schema.execute()

        self.schema.output['Short Name'] = self.schema.output['Short Name'].str.lower()
        self.schema_fields = self.schema.output.groupby('Table')['Short Name'].agg(list).to_dict()

    def rename_fields(self):
        """
        Renames the fields to match the CathPCI5 schema
        :return:
        """
        config = read_config(self.dataset, 'rename_fields')
        new_fields = config['New Field'].str.lower().tolist()
        old_fields = config['Old Field'].str.lower().tolist()
        rename = dict(zip(old_fields, new_fields))
        self.raw_data = self.raw_data.rename(columns=rename)

    def normalize(self):
        """
        Normalizes the data to match the schema. Adds missing fields and removes extra fields from the raw data.
        :return:
        """
        for table, fields in self.schema_fields.items():
            fields = list(set(self.id_fields + fields))
            for field in fields:
                if field not in self.raw_data.columns:
                    self.raw_data[field] = None
            self.out_tables[table] = self.raw_data[fields].drop_duplicates()

    def add_custom_fields(self):
        """
        Adds custom fields to the tables based on the config.
        """
        config = read_config(self.dataset, 'custom_fields')
        config = config[config['Section'].isin(['DISCHMED', 'IPPEVENTS', 'PREPROCMED', 'PROCMED'])]
        for table, conf in config.groupby('Section'):
            der_field = conf['Derivative Field'].values[0].lower()
            der_values = conf['Derivative Value'].unique()
            df_conf = pd.DataFrame(der_values, columns=[der_field])
            df_table = self.out_tables[table]

            if der_field in df_table.columns:
                df_table = df_table.drop(der_field, axis=1)

            df_conf['key'] = 1
            df_table['key'] = 1

            merged = df_table.merge(df_conf, on='key').drop('key', axis=1)

            for ind, row in conf.iterrows():
                source_field = row['Field'].lower()
                der_field = row['Derivative Field'].lower()
                der_value = row['Derivative Value']
                out_field = row['Reference Field'].lower()

                cond = merged[der_field] == der_value

                merged = merged.merge(self.raw_data[self.id_fields + [source_field]].drop_duplicates(),
                                      on=self.id_fields, how='inner')

                merged[out_field] = np.where(cond, merged[source_field], merged[out_field])
                merged = merged.drop(source_field, axis=1)

            self.out_tables[table] = merged[list(set(self.id_fields + self.schema_fields[table]))]

    def insert_tables(self):
        for table_name, data in self.out_tables.items():
            try:
                insert_data(data, table_name=table_name, db=self.target_db, if_exists='append')
                logging.info(f'Inserted: {table_name}')
            except Exception as e:
                logging.error(f'Failed insertion: {table_name}')
                logging.error(e)

    def execute(self):
        self.raw_data = read_sql(f'Select * from {self.db_table_name}')
        self.raw_data.columns = self.raw_data.columns.str.lower()

        self.gen_schema()
        self.rename_fields()
        self.normalize()
        self.add_custom_fields()
        if self.write:
            self.insert_tables()
