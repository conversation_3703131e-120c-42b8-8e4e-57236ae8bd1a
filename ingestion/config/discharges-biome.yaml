rename_fields:
  - New Field: secondaryinsurancegroup/secondarypayorplan
    Old Field: secondaryinsurancegroup
  - New Field: primaryinsurancegroup/primarypayorplan
    Old Field: primaryinsurancegroup
  - New Field: prindx9code, prindx10code
    Old Field: prindx10code
  - New Field: principalproceduredate
    Old Field: icd10princprocdate
  - New Field: prinicd9proccode, prinicd10proccode
    Old Field: prinicd10proccode
  - New Field: principlediagnosisicd9poa, principlediagnosisicd9poa
    Old Field: principlediagnosisicd10poa
  - New Field: dischargetime
    Old Field: dctime
  - New Field: patientzip
    Old Field: patzip
  - New Field: encounter facility name
    Old Field: hospname
  - New Field: mrn
    Old Field: medrecn
  - New Field: entity name
    Old Field: servicesitename
  - New Field: ecd number (pe ecd extension)
    Old Field: encounternumber
  - New Field: patient account number
    Old Field: encounternumber
required_fields:
  - admitdate
  - admitsource
  - admittime
  - admittype
  - dctime
  - directcost
  - dischargedate
  - dischargestatus
  - dob
  - drgcode
  - encounternumber
  - expectedpayment
  - financialpayorclass
  - gender
  - icudays
  - indirectcost
  - inoutcode
  - medrecn
  - netpatientrevenue
  - patienttype
  - patzip
  - primarypayor
  - prindx10code
  - prinicd10proccode
  - secondarypayor
  - servicesitename
