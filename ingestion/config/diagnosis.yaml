version: '1.0'
biome_schema:
  - Field: clientfileid
    Dtype: varchar(17)
    Role: null
    PHI: false
  - Field: filename
    Dtype: varchar(98)
    Role: null
    PHI: false
  - Field: version
    Dtype: varchar(22)
    Role: null
    PHI: false
  - Field: poa
    Dtype: varchar(30)
    Role: null
    PHI: false
  - Field: datasetname
    Dtype: varchar(24)
    Role: null
    PHI: false
  - Field: diagtype
    Dtype: varchar(12)
    Role: null
    PHI: false
  - Field: diagseqno
    Dtype: varchar(20)
    Role: null
    PHI: false
  - Field: encounternumber
    Dtype: varchar(30)
    Role: IDENTIFIER
    PHI: true
  - Field: diagcode
    Dtype: varchar(10)
    Role: null
    PHI: false
  - Field: tenantid
    Dtype: varchar(25)
    Role: TENANT
    PHI: false
  - Field: biomeencounterid
    Dtype: bigint unsigned
    Role: null
    PHI: false
  - Field: biomeimportdt
    Dtype: varchar(38)
    Role: null
    PHI: false
value_mapping:
  - Field: poa
    Value Map:
      0 - Not Specified: Not Specified
      '1': Y
      1 - Yes: Y
      '2': N
      2 - No: N
      '3': U
      3 - Unknown: U
      '4': W
      '5': E
      5 - Exempt: E
      '5 - Exempt from POA ': E
      5 - Exempt from POA reporting: E
      E - Condition is exempt from POA reporting (assigned by OSHPD).: E
      E - Exempt: E
      N - Condition was NOT present at time of inpatient admission: N
      N - No: N
      U - Documentation is insufficient to determine if the condition is present at time of admission: U
      U - Unknown: U
      W - Provider is unable to clin: W
      W - Provider is unable to clinically determine whether the condition was present at time of admission: W
      Y - Condition was present at time of inpatient admission: Y
      Y - Yes: Y
cleaner_regex:
  - Field: diagcode
    Regex: '\s.*'
  - Field: encounternumber
    Regex: '\.0*$'
