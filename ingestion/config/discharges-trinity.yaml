client_fields:
  - prin diag poa
  - prin diag code
  - drg code
  - discharge status
  - icu days
  - discharge date and time
  - discharge date
  - admit diagnosis code
  - admit date and time
  - admit date
  - admit source
  - admit type
  - patient type code
  - patient zip
  - gender
  - dob
  - mrn
  - patient account number
  - group ip or op rollup
  - entity name
  - prin proc code
  - prin proc date
  - primary payor
  - financial
  - primary payor plan
  - secondary payor plan
  - expected payment -hq
  - expected payment
  - net patient revenue
  - direct cost
  - indirect cost
  - group ip or op rollup
  - financial class name
  - discharge date & time
  - icd-10-cm admi diag code
  - admit date & time
  - icd-10-cm prin diag code
  - primary payor name
  - expected payment
  - expected payment- hq
  - expected payments
  - estimated net revenue
  - discharge date & time
  - discharge date and time
  - discharge md speciality
  - discharge md name
  - discharge md npi
  - admitting md speciality
  - admitting md name
  - admitting md npi
  - patient race
  - patient last name
  - patient first name
  - ccn
  - prin proc start time
  - prin proc end time
  - total direct cost
  - total indirect cost
  - 'unnamed: 41'
  - icd-10-cm prin diag code
  - icd-10-cm admi diag code
  - financial class name
  - patient med rec no
  - mrn
  - total payment
  - expected reimbursement
  - estimated net revenue
  - patient race
rename_fields:
  - New Field: prin diag poa
    Old Field: principlediagnosisicd10poa
  - New Field: prin diag code
    Old Field: prindx10code
  - New Field: drg code
    Old Field: drgcode
  - New Field: discharge status
    Old Field: dischargestatus
  - New Field: icu days
    Old Field: icudays
  - New Field: discharge date and time
    Old Field: dischargedate_1
  - New Field: discharge date
    Old Field: dischargedate
  - New Field: admit diagnosis code
    Old Field: admitdx10code
  - New Field: admit date and time
    Old Field: admitdate_1
  - New Field: admit date
    Old Field: admitdate
  - New Field: admit source
    Old Field: admitsource
  - New Field: admit type
    Old Field: admittype
  - New Field: patient type code
    Old Field: patienttypecode
  - New Field: patient zip
    Old Field: patzip
  - New Field: mrn
    Old Field: medrecn
  - New Field: patient account number
    Old Field: encounternumber
  - New Field: group ip or op rollup
    Old Field: groupiporoprollup
  - New Field: entity name
    Old Field: servicesitename
  - New Field: prin proc code
    Old Field: prinicd10proccode
  - New Field: prin proc date
    Old Field: icd9princprocdate
  - New Field: primary payor
    Old Field: primarypayor
  - New Field: primary payor plan
    Old Field: primarypayorplan
  - New Field: secondary payor plan
    Old Field: secondarypayor
  - New Field: expected payment -hq
    Old Field: expectedpayment
  - New Field: expected payment
    Old Field: expectedpayment_1
  - New Field: net patient revenue
    Old Field: netpatientrevenue
  - New Field: direct cost
    Old Field: directcost
  - New Field: indirect cost
    Old Field: indirectcost
  - New Field: group ip or op rollup
    Old Field: patienttype
  - New Field: financial class name
    Old Field: financialclassname
  - New Field: discharge date & time
    Old Field: dischargedatetime
  - New Field: icd-10-cm admi diag code
    Old Field: icd10cmadmidiagcode
  - New Field: admit date & time
    Old Field: admitdatetime
  - New Field: icd-10-cm prin diag code
    Old Field: icd10cmprindiagcode
  - New Field: primary payor name
    Old Field: primarypayor_1
  - New Field: expected payment
    Old Field: expectedpayment
  - New Field: expected payment- hq
    Old Field: expectedpaymenthq
  - New Field: expected payments
    Old Field: expectedpayments
  - New Field: estimated net revenue
    Old Field: estimatednetrevenue
  - New Field: discharge date & time
    Old Field: dctime
  - New Field: discharge date and time
    Old Field: dctime
  - New Field: discharge md speciality
    Old Field: dischargemdspeciality
  - New Field: discharge md name
    Old Field: dischargemdname
  - New Field: discharge md npi
    Old Field: dischargemdnpi
  - New Field: admitting md speciality
    Old Field: admittingmdspeciality
  - New Field: admitting md name
    Old Field: admittingmdname
  - New Field: admitting md npi
    Old Field: admittingmdnpi
  - New Field: patient race
    Old Field: patientrace
  - New Field: patient last name
    Old Field: patientlastname
  - New Field: patient first name
    Old Field: patientfirstname
  - New Field: prin proc start time
    Old Field: prinprocstarttime
  - New Field: prin proc end time
    Old Field: prinprocendtime
  - New Field: total direct cost
    Old Field: directcost
  - New Field: total indirect cost
    Old Field: indirectcost
  - New Field: 'unnamed: 41'
    Old Field: unnamed41
  - New Field: icd-10-cm prin diag code
    Old Field: prindx10code
  - New Field: icd-10-cm admi diag code
    Old Field: admitdx10code
  - New Field: financial class name
    Old Field: financialpayorclass
  - New Field: patient med rec no
    Old Field: medrecn
  - New Field: mrn
    Old Field: medrecn_1
  - New Field: total payment
    Old Field: totalpayment
  - New Field: expected reimbursement
    Old Field: expectedreimbursement
  - New Field: estimated net revenue
    Old Field: netpatientrevenue
  - New Field: patient race
    Old Field: race
required_fields:
- admitdate
- admitdx10code
- admitsource
- admittype
- dctime
- directcost
- dischargedate
- dischargestatus
- dob
- drgcode
- encounternumber
- gender
- icudays
- indirectcost
- medrecn
- netpatientrevenue
- patienttype
- patzip
- primarypayor
- prindx10code
- prinicd10proccode
- race
- secondarypayor
- servicesitename