raw-config-version: 20250410
rename_fields:
  - New Field: charge - variable indirect cost
    Old Field: indirectcostvariable
  - New Field: charge - fixed indirect cost
    Old Field: indirectcostfixed
  - New Field: charge - variable direct cost
    Old Field: directcostvariable
  - New Field: charge - fixed direct cost
    Old Field: directcostfixed
  - New Field: unit charge amount
    Old Field: chargeunit
  - New Field: charge - day of stay
    Old Field: dayofstay
  - New Field: charge - cpt-hcpcs code desc
    Old Field: cpt4hcpcsname
  - New Field: charge - cpt-hcpcs code
    Old Field: cpt4hcpcscode
  - New Field: charge - ub revenue code desc
    Old Field: ub92revname
  - New Field: charge - ub revenue code
    Old Field: ub92revcode
  - New Field: number of units
    Old Field: units
  - New Field: charge - service date
    Old Field: svcitemdate
  - New Field: charge - activity code desc
    Old Field: chargecodename
  - New Field: charge - activity code
    Old Field: svcitemchargecode
  - New Field: charge - revenue center desc
    Old Field: departmentname
  - New Field: charge - revenue center
    Old Field: departmentcode
  - New Field: charge - facility desc
    Old Field: servicesitename
  - New Field: total charges
    Old Field: charges
  - New Field: charge - total indirect costs
    Old Field: indirectcost
  - New Field: charge - total direct costs
    Old Field: directcost
  - New Field: enc - patient account
    Old Field: encounternumber
  - New Field: enc - medical record number
    Old Field: medrecn
required_fields:
  - dayofstay
  - directcost
  - medrecn
  - departmentname
  - cpt4hcpcsname
  - encounternumber
  - chargecodename
  - indirectcost
  - ub92revcode
  - ub92revname
  - svcitemchargecode
  - charges
  - svcitemdate
  - cpt4hcpcscode
  - servicesitename
  - units
