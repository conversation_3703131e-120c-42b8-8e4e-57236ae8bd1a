client_fields:
  - encounter number
  - medical record number
  - patient dob
  - patient gender
  - patient race
  - patient ethnicity
  - patient language
  - patient zip code
  - admission type code
  - admission type description
  - admission source code
  - admission source description
  - patient type
  - patient type description
  - discharge disposition code
  - discharge disposition description
  - ub04 patient discharge status code
  - admission date
  - admission time
  - discharge date
  - discharge time
  - length of stay
  - icu days charged
  - admit diagnosis code (icd-9)
  - admit diagnosis description (icd-9)
  - principal diagnosis (icd-9)
  - principal diagnosis description (icd-9)
  - principal diagnosis present on admission (poa) indicator (icd-9)
  - principal procedure code (icd-9)
  - principal procedure description (icd-9)
  - principal procedure performed date (icd-9)
  - admit diagnosis code (icd-10)
  - admit diagnosis description (icd-10)
  - principal diagnosis (icd-10)
  - principal diagnosis description (icd-10)
  - principal diagnosis present on admission (poa) indicator (icd-10)
  - principal procedure code (icd-10)
  - principal procedure description (icd-10)
  - principal procedure performed date (icd-10)
  - apr drg
  - apr drg description
  - apr severity of illness
  - apr risk of mortality
  - discharge ms-drg
  - discharge ms-drg description
  - medicare outlier and/or other outlier
  - site of service code
  - site of service description (facility name)
  - discharge unit
  - financial category code
  - financial category description (medicare, managed care, etc.)
  - jmh ins group code
  - jmh ins group description
  - secondary payer code
  - secondary payer code description
  - attending md upin or identifier
  - attending md name
  - attending md specialty
  - discharge service
  - discharge service description
  - principal procedure md upin or identifier
  - principal procedure md name
  - principal procedure md specialty
  - referring md upin or identifier
  - referring md name
  - referring md specialty
  - total charges
  - net revenue
  - total direct cost
  - total costs
  - encounter location
  - attending md upin or identifier
  - site of service description (facility name)
rename_fields:
  - New Field: encounter number
    Old Field: encounternumber
  - New Field: medical record number
    Old Field: medrecn
  - New Field: patient dob
    Old Field: dob
  - New Field: patient gender
    Old Field: gender
  - New Field: patient race
    Old Field: race
  - New Field: patient ethnicity
    Old Field: ethnicity
  - New Field: patient language
    Old Field: patientlanguage
  - New Field: patient zip code
    Old Field: patzip
  - New Field: admission type code
    Old Field: admissiontypecode
  - New Field: admission type description
    Old Field: admittype
  - New Field: admission source code
    Old Field: admitsourcecode
  - New Field: admission source description
    Old Field: admitsource
  - New Field: patient type
    Old Field: patienttypecode
  - New Field: patient type description
    Old Field: inoutcode
  - New Field: discharge disposition code
    Old Field: dischargestatuscode
  - New Field: discharge disposition description
    Old Field: dischargestatus
  - New Field: ub04 patient discharge status code
    Old Field: ub04dischargestatuscode
  - New Field: admission date
    Old Field: admitdate
  - New Field: admission time
    Old Field: admittime
  - New Field: discharge date
    Old Field: dischargedate
  - New Field: discharge time
    Old Field: dctime
  - New Field: length of stay
    Old Field: los
  - New Field: icu days charged
    Old Field: icudays
  - New Field: admit diagnosis code (icd-9)
    Old Field: admitdx9code
  - New Field: admit diagnosis description (icd-9)
    Old Field: admitdx9name
  - New Field: principal diagnosis (icd-9)
    Old Field: prindx9code
  - New Field: principal diagnosis description (icd-9)
    Old Field: prindx9name
  - New Field: principal diagnosis present on admission (poa) indicator (icd-9)
    Old Field: principlediagnosisicd9poa
  - New Field: principal procedure code (icd-9)
    Old Field: prinicd9proccode
  - New Field: principal procedure description (icd-9)
    Old Field: prinicd9procname
  - New Field: principal procedure performed date (icd-9)
    Old Field: icd9princprocdate
  - New Field: admit diagnosis code (icd-10)
    Old Field: admitdx10code
  - New Field: admit diagnosis description (icd-10)
    Old Field: admitdx10name
  - New Field: principal diagnosis (icd-10)
    Old Field: prindx10code
  - New Field: principal diagnosis description (icd-10)
    Old Field: prindx10name
  - New Field: principal diagnosis present on admission (poa) indicator (icd-10)
    Old Field: principlediagnosisicd10poa
  - New Field: principal procedure code (icd-10)
    Old Field: prinicd10proccode
  - New Field: principal procedure description (icd-10)
    Old Field: prinicd10procname
  - New Field: principal procedure performed date (icd-10)
    Old Field: icd10princprocdate
  - New Field: apr drg
    Old Field: aprdrg
  - New Field: apr drg description
    Old Field: aprdrgdesc
  - New Field: apr severity of illness
    Old Field: aprseverity
  - New Field: apr risk of mortality
    Old Field: aprriskofmortality
  - New Field: discharge ms-drg
    Old Field: drgcode
  - New Field: discharge ms-drg description
    Old Field: drgname
  - New Field: medicare outlier and/or other outlier
    Old Field: medicareoutlierandorotheroutlier
  - New Field: site of service code
    Old Field: servicesitecode
  - New Field: site of service description (facility name)
    Old Field: servicesitename
  - New Field: discharge unit
    Old Field: dischargeunit
  - New Field: financial category code
    Old Field: primarypayorcode
  - New Field: financial category description (medicare, managed care, etc.)
    Old Field: primarypayor
  - New Field: jmh ins group code
    Old Field: primaryinsurancegroupcode
  - New Field: jmh ins group description
    Old Field: primaryinsurancegroup
  - New Field: secondary payer code
    Old Field: secondarypayorcode
  - New Field: secondary payer code description
    Old Field: secondarypayor
  - New Field: attending md upin or identifier
    Old Field: attendingmdid
  - New Field: attending md name
    Old Field: attendingmdname
  - New Field: attending md specialty
    Old Field: attendingmdrole
  - New Field: discharge service
    Old Field: dischargeservicecode
  - New Field: discharge service description
    Old Field: dischargeservicename
  - New Field: principal procedure md upin or identifier
    Old Field: proceduremdid
  - New Field: principal procedure md name
    Old Field: proceduremdname
  - New Field: principal procedure md specialty
    Old Field: proceduremdrole
  - New Field: referring md upin or identifier
    Old Field: referringmdid
  - New Field: referring md name
    Old Field: refferingphyname
  - New Field: referring md specialty
    Old Field: referringmdrole
  - New Field: total charges
    Old Field: charges
  - New Field: net revenue
    Old Field: netpatientrevenue
  - New Field: total direct cost
    Old Field: directcost
  - New Field: total costs
    Old Field: totalcost
  - New Field: encounter location
    Old Field: dischargelocation
  - New Field: attending md upin or identifier
    Old Field: attendingmdnpi

required_fields:
- admitdate
- admitdx10code
- admitdx9code
- admitsource
- admittime
- admittype
- attendingmdname
- attendingmdnpi
- dctime
- directcost
- dischargedate
- dischargestatus
- dob
- drgcode
- encounternumber
- ethnicity
- gender
- icudays
- inoutcode
- los
- medrecn
- netpatientrevenue
- patzip
- primaryinsurancegroup
- primarypayor
- prindx10code
- prindx9code
- prinicd10proccode
- prinicd9proccode
- race
- referringmdrole
- refferingphyname
- secondarypayor
- servicesitename
- totalcost