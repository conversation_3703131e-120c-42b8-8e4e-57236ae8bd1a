raw-config-version: 20250410
rename_fields:
  - New Field: costs - variable indirect - detail
    Old Field: indirectcostvariable
  - New Field: costs - fixed indirect - detail
    Old Field: indirectcostfixed
  - New Field: costs - variable direct - detail
    Old Field: directcostvariable
  - New Field: costs - fixed direct - detail
    Old Field: directcostfixed
  - New Field: charge - day of stay
    Old Field: dayofstay
  - New Field: charge - cpt-hcpcs code desc
    Old Field: cpt4hcpcsname
  - New Field: charge - cpt-hcpcs code
    Old Field: cpt4hcpcscode
  - New Field: charge - ub revenue code desc
    Old Field: ub92revname
  - New Field: charge - ub revenue code
    Old Field: ub92revcode
  - New Field: quantity - detail
    Old Field: units
  - New Field: charge - service date
    Old Field: svcitemdate
  - New Field: charge - activity code desc
    Old Field: chargecodename
  - New Field: charge - activity code
    Old Field: svcitemchargecode
  - New Field: national drug class code
    Old Field: nationaldrugclasscode
  - New Field: charge - revenue center desc
    Old Field: departmentname
  - New Field: charge - revenue center
    Old Field: departmentcode
  - New Field: charges - total - detail
    Old Field: charges
  - New Field: costs - total - detail
    Old Field: totalcost
  - New Field: costs - indirect - detail
    Old Field: indirectcost
  - New Field: costs - direct - detail
    Old Field: directcost
  - New Field: enc - patient account
    Old Field: encounternumber
  - New Field: enc - medical record number
    Old Field: medrecn
required_fields:
  - dayofstay
  - directcost
  - medrecn
  - departmentname
  - cpt4hcpcsname
  - encounternumber
  - chargecodename
  - indirectcost
  - ub92revcode
  - ub92revname
  - nationaldrugclasscode
  - svcitemchargecode
  - charges
  - svcitemdate
  - cpt4hcpcscode
  - totalcost
  - units
