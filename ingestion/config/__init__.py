import pandas as pd
import yaml
import os


def dir_path():
    return os.path.dirname(os.path.abspath(__file__))


def read_config(dataset, config, as_df=True):
    with open(f'{dir_path()}/{dataset.lower()}.yaml', 'r', encoding='utf-8') as f:
        conf = yaml.load(f, Loader=yaml.FullLoader).get(config)

    if as_df:
        if conf:
            return pd.DataFrame(conf)
        else:
            return pd.DataFrame()
    else:
        return conf


def read_all_clients_config(dataset):
    """
    Read all config files in the folder with name <dataset>-<client>.yaml
    """
    config_files = [
        f for f in os.listdir(dir_path()) if f.startswith(dataset) and f.endswith('.yaml') and f != f'{dataset}.yaml'
    ]
    configs = {}
    for config_file in config_files:
        client = config_file.split('-')[1].split('.')[0]
        if client != 'global':
            with open(f'{dir_path()}/{config_file}', 'r', encoding='utf-8') as f:
                configs[client] = yaml.load(f, Loader=yaml.FullLoader)
    return configs
