client_fields:
  - enc - medical record number
  - enc - date of birth
  - enc - patient account
  - enc - gender_desc
  - enc - zip
  - enc - patient type
  - enc - patient type_desc
  - service line rollup
  - enc - facility
  - enc - facility_desc
  - enc - admission type
  - enc - admission type_desc
  - enc - admission source
  - enc - admission source_desc
  - enc - length of stay
  - enc - admission date
  - enc - admission time
  - enc - discharge date
  - enc - discharge time
  - icu days
  - enc - discharge status
  - enc - discharge status_desc
  - payor ar l2 rollup code
  - payor ar l2 rollup description
  - enc - payor plan code
  - enc - payor plan code_desc
  - enc - second payor plan code
  - enc - second payor plan code_desc
  - enc - referring physician
  - enc - referring physician_desc
  - referring npi
  - referring specialty
  - enc - attending physician
  - enc - attending physician_desc
  - attending npi
  - attending specialty
  - enc - surgeon
  - enc - surgeon_desc
  - surgeon npi
  - surgeon specialty
  - ed physician id
  - ed physician name
  - ed physician npi
  - ed physician specialty
  - enc - ms drg
  - enc - ms drg_desc
  - enc - icd10 admission diagnosis
  - enc - icd10 admission diagnosis_desc
  - enc - total direct costs
  - enc - total indirect costs
  - enc - total variable costs
  - enc - total fixed costs
  - enc - fixed direct cost
  - enc - total variable direct cost
  - enc - fixed indirect cost
  - enc - total variable indirect cost
  - enc - total charges
  - enc - estimated net revenue
  - enc - total actual payment
  - contr margin
  - net income
  - ed level
  - 'unnamed: 62'
  - 'unnamed: 63'
  - 'unnamed: 64'
  - ed level indicator
  - enc - facility desc
  - ed level indicator
  - net income
  - enc - surgeon_desc
  - surgeon npi
  - surgeon specialty
  - referring npi
  - enc - attending physician_desc
  - attending npi
  - attending specialty
  - enc - discharge status
  - enc - facility
  - ed level
  - enc - facility_desc
  - attending npi
  - enc - gender desc
  - enc - discharge disposition
  - service line rollup
  - enc - total variable direct cost
  - enc - fixed direct cost
  - enc - total fixed costs
  - enc - total variable costs
  - enc - icd10 admission diagnosis_desc
  - ed physician specialty
  - ed physician npi
  - ed physician name
  - ed physician id
  - enc - surgeon
  - enc - attending physician
  - enc - referring physician
  - enc - admission type
  - enc - attending physician desc
  - enc - referring physician desc
  - enc - second payor plan code desc
  - enc - payor plan code desc
  - payor ar l2 rollup code desc
  - enc - discharge disposition desc
  - enc - admission source desc
  - enc - admission type desc
  - enc - facility desc
  - enc - patient type desc
  - enc - surgeon desc
  - enc - ms drg desc
  - enc - icd10 admission diagnosis desc
  - enc - primary icd10 diagnosis
  - 'lookup '
  - enc - primary icd10 diagnosis desc
  - contr margin
  - enc - total variable indirect cost
  - enc - fixed indirect cost
  - referring npi.1
  - referring specialty.1
  - enc - attending physician.1
  - enc - attending physician_desc.1
  - attending npi.1
  - attending specialty.1
  - enc - surgeon.1
  - enc - surgeon_desc.1
  - surgeon npi.1
  - surgeon specialty.1
  - ed physician id.1
  - ed physician name.1
  - ed physician npi.1
  - ed physician specialty.1
  - 'unnamed: 3'
  - enc - total fixed indirect cost
  - enc - total fixed direct cost
  - enc - total variable cost
  - enc - total fixed cost
  - enc - surgeon desc
  - enc - attending physician desc
  - enc - discharge status desc
rename_fields:
  - New Field: enc - medical record number
    Old Field: medrecn
  - New Field: enc - date of birth
    Old Field: dob
  - New Field: enc - patient account
    Old Field: encounternumber
  - New Field: enc - gender_desc
    Old Field: gender
  - New Field: enc - zip
    Old Field: patzip
  - New Field: enc - patient type
    Old Field: patienttypecode
  - New Field: enc - patient type_desc
    Old Field: inoutcode
  - New Field: service line rollup
    Old Field: servicelinerollup10540
  - New Field: enc - facility
    Old Field: encfacility10539
  - New Field: enc - facility_desc
    Old Field: servicesitename
  - New Field: enc - admission type
    Old Field: encadmissiontype3597
  - New Field: enc - admission type_desc
    Old Field: admittype
  - New Field: enc - admission source
    Old Field: admitsourcecode
  - New Field: enc - admission source_desc
    Old Field: admitsource
  - New Field: enc - length of stay
    Old Field: los
  - New Field: enc - admission date
    Old Field: admitdate
  - New Field: enc - admission time
    Old Field: admittime
  - New Field: enc - discharge date
    Old Field: dischargedate
  - New Field: enc - discharge time
    Old Field: dctime
  - New Field: icu days
    Old Field: icudays
  - New Field: enc - discharge status
    Old Field: encdischargestatus3599
  - New Field: enc - discharge status_desc
    Old Field: dischargestatus
  - New Field: payor ar l2 rollup code
    Old Field: primarypayorcode
  - New Field: payor ar l2 rollup description
    Old Field: primarypayor
  - New Field: enc - payor plan code
    Old Field: primaryinsurancegroupcode
  - New Field: enc - payor plan code_desc
    Old Field: primaryinsurancegroup
  - New Field: enc - second payor plan code
    Old Field: secondaryinsurancegroupcode
  - New Field: enc - second payor plan code_desc
    Old Field: secondaryinsurancegroup
  - New Field: enc - referring physician
    Old Field: encreferringphysician10543
  - New Field: enc - referring physician_desc
    Old Field: refferingphyname
  - New Field: referring npi
    Old Field: referringnpi10544
  - New Field: referring specialty
    Old Field: referringmdrole
  - New Field: enc - attending physician
    Old Field: encattendingphysician3642
  - New Field: enc - attending physician_desc
    Old Field: encattendingphysician_desc3617
  - New Field: attending npi
    Old Field: attendingnpi10545
  - New Field: attending specialty
    Old Field: attendingspecialty3618
  - New Field: enc - surgeon
    Old Field: encsurgeon10547
  - New Field: enc - surgeon_desc
    Old Field: encsurgeon_desc3611
  - New Field: surgeon npi
    Old Field: surgeonnpi10546
  - New Field: surgeon specialty
    Old Field: surgeonspecialty3612
  - New Field: ed physician id
    Old Field: edphysicianid10548
  - New Field: ed physician name
    Old Field: edphysicianname10393
  - New Field: ed physician npi
    Old Field: edphysiciannpi10549
  - New Field: ed physician specialty
    Old Field: edphysicianspecialty3614
  - New Field: enc - ms drg
    Old Field: drgcode
  - New Field: enc - ms drg_desc
    Old Field: drgname
  - New Field: enc - icd10 admission diagnosis
    Old Field: admitdx10code
  - New Field: enc - icd10 admission diagnosis_desc
    Old Field: encicd10admissiondiagnosis_desc10550
  - New Field: enc - total direct costs
    Old Field: directcost
  - New Field: enc - total indirect costs
    Old Field: indirectcost
  - New Field: enc - total variable costs
    Old Field: enctotalvariablecosts3625
  - New Field: enc - total fixed costs
    Old Field: enctotalfixedcosts10443
  - New Field: enc - fixed direct cost
    Old Field: encfixeddirectcost3627
  - New Field: enc - total variable direct cost
    Old Field: enctotalvariabledirectcost3629
  - New Field: enc - fixed indirect cost
    Old Field: encfixedindirectcost3628
  - New Field: enc - total variable indirect cost
    Old Field: enctotalvariableindirectcost3630
  - New Field: enc - total charges
    Old Field: charges
  - New Field: enc - estimated net revenue
    Old Field: expectedpayment
  - New Field: enc - total actual payment
    Old Field: netpatientrevenue
  - New Field: contr margin
    Old Field: contrmargin3634
  - New Field: net income
    Old Field: netincome
  - New Field: ed level
    Old Field: edlevel
  - New Field: 'unnamed: 62'
    Old Field: unnamed62
  - New Field: 'unnamed: 63'
    Old Field: unnamed63
  - New Field: 'unnamed: 64'
    Old Field: unnamed64
  - New Field: ed level indicator
    Old Field: edlevelindicator
  - New Field: enc - facility desc
    Old Field: encfacilitydesc
  - New Field: ed level indicator
    Old Field: edlevelindicator17227
  - New Field: net income
    Old Field: netincome17225
  - New Field: enc - surgeon_desc
    Old Field: proceduremdname
  - New Field: surgeon npi
    Old Field: proceduremdid
  - New Field: surgeon specialty
    Old Field: proceduremdrole
  - New Field: referring npi
    Old Field: refferingphycode
  - New Field: enc - attending physician_desc
    Old Field: attendingmdname
  - New Field: attending npi
    Old Field: attendingmdid
  - New Field: attending specialty
    Old Field: attendingmdrole
  - New Field: enc - discharge status
    Old Field: dischargestatuscode
  - New Field: enc - facility
    Old Field: servicesitecode
  - New Field: ed level
    Old Field: edlevel17226
  - New Field: enc - facility_desc
    Old Field: hospname
  - New Field: attending npi
    Old Field: attendingmdnpi
  - New Field: enc - gender desc
    Old Field: gender
  - New Field: enc - discharge disposition
    Old Field: dischargestatuscode
  - New Field: service line rollup
    Old Field: servicelinerollup
  - New Field: enc - total variable direct cost
    Old Field: enctotalvariabledirectcost
  - New Field: enc - fixed direct cost
    Old Field: encfixeddirectcost
  - New Field: enc - total fixed costs
    Old Field: enctotalfixedcosts
  - New Field: enc - total variable costs
    Old Field: enctotalvariablecosts
  - New Field: enc - icd10 admission diagnosis_desc
    Old Field: encicd10admissiondiagnosis_desc
  - New Field: ed physician specialty
    Old Field: edphysicianspecialty
  - New Field: ed physician npi
    Old Field: edphysiciannpi
  - New Field: ed physician name
    Old Field: edphysicianname
  - New Field: ed physician id
    Old Field: edphysicianid
  - New Field: enc - surgeon
    Old Field: encsurgeon
  - New Field: enc - attending physician
    Old Field: encattendingphysician
  - New Field: enc - referring physician
    Old Field: encreferringphysician
  - New Field: enc - admission type
    Old Field: encadmissiontype
  - New Field: enc - attending physician desc
    Old Field: encattendingphysiciandesc9257
  - New Field: enc - referring physician desc
    Old Field: refferingphyname
  - New Field: enc - second payor plan code desc
    Old Field: secondaryinsurancegroup
  - New Field: enc - payor plan code desc
    Old Field: primaryinsurancegroup
  - New Field: payor ar l2 rollup code desc
    Old Field: primarypayor
  - New Field: enc - discharge disposition desc
    Old Field: encdischargedispositiondesc
  - New Field: enc - admission source desc
    Old Field: admitsource
  - New Field: enc - admission type desc
    Old Field: admittype
  - New Field: enc - facility desc
    Old Field: servicesitename
  - New Field: enc - patient type desc
    Old Field: inoutcode
  - New Field: enc - surgeon desc
    Old Field: encsurgeondesc9258
  - New Field: enc - ms drg desc
    Old Field: drgname
  - New Field: enc - icd10 admission diagnosis desc
    Old Field: encicd10admissiondiagnosisdesc
  - New Field: enc - primary icd10 diagnosis
    Old Field: encprimaryicd10diagnosis
  - New Field: 'lookup '
    Old Field: lookup
  - New Field: enc - primary icd10 diagnosis desc
    Old Field: encprimaryicd10diagnosisdesc
  - New Field: contr margin
    Old Field: contrmargin
  - New Field: enc - total variable indirect cost
    Old Field: enctotalvariableindirectcost
  - New Field: enc - fixed indirect cost
    Old Field: encfixedindirectcost
  - New Field: referring npi.1
    Old Field: referringnpi1
  - New Field: referring specialty.1
    Old Field: referringspecialty1
  - New Field: enc - attending physician.1
    Old Field: encattendingphysician1
  - New Field: enc - attending physician_desc.1
    Old Field: encattendingphysician_desc1
  - New Field: attending npi.1
    Old Field: attendingnpi1
  - New Field: attending specialty.1
    Old Field: attendingspecialty1
  - New Field: enc - surgeon.1
    Old Field: encsurgeon1
  - New Field: enc - surgeon_desc.1
    Old Field: encsurgeon_desc1
  - New Field: surgeon npi.1
    Old Field: surgeonnpi1
  - New Field: surgeon specialty.1
    Old Field: surgeonspecialty1
  - New Field: ed physician id.1
    Old Field: edphysicianid1
  - New Field: ed physician name.1
    Old Field: edphysicianname1
  - New Field: ed physician npi.1
    Old Field: edphysiciannpi1
  - New Field: ed physician specialty.1
    Old Field: edphysicianspecialty1
  - New Field: 'unnamed: 3'
    Old Field: unnamed3
  - New Field: enc - total fixed indirect cost
    Old Field: enctotalfixedindirectcost
  - New Field: enc - total fixed direct cost
    Old Field: enctotalfixeddirectcost
  - New Field: enc - total variable cost
    Old Field: enctotalvariablecost
  - New Field: enc - total fixed cost
    Old Field: enctotalfixedcost
  - New Field: enc - surgeon desc
    Old Field: encsurgeondesc
  - New Field: enc - attending physician desc
    Old Field: encattendingphysiciandesc
  - New Field: enc - discharge status desc
    Old Field: encdischargestatusdesc
required_fields:
- admitdate
- admitdx10code
- admitsource
- admittime
- admittype
- attendingmdname
- attendingmdnpi
- dctime
- directcost
- dischargedate
- dischargestatus
- dob
- drgcode
- encounternumber
- expectedpayment
- gender
- hospname
- icudays
- indirectcost
- inoutcode
- los
- medrecn
- netpatientrevenue
- patzip
- primaryinsurancegroup
- primarypayor
- referringmdrole
- refferingphyname
- servicelinerollup