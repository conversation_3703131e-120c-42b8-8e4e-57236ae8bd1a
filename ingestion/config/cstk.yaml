version: '1.0'
biome_schema:
  - Dtype: varchar(255)
    Field: CaseId
    PHI: true
    Role: ANY_ID
  - Dtype: varchar(255)
    Field: StrokePatientId
    PHI: true
    Role: IDENTIFIER
  - Dtype: text
    Field: MASTER_PATIENT_ID
    PHI: true
    Role: ANY_ID
  - Dtype: varchar(255)
    Field: SCHEDULEDFORMNAME
    PHI: false
    Role: null
  - Dtype: int
    Field: FORM_VERSION
    PHI: false
    Role: null
  - Dtype: int
    Field: ServiceSiteCode
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: ServiceSiteName
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: CreatedDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(30)
    Field: MODIFIED_DT
    PHI: true
    Role: DATE
  - Dtype: varchar(255)
    Field: STATUS
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: CreatedBy
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: UpdatedBy
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: UPLOADED_BY
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: accesscase
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: FACPATID
    PHI: true
    Role: ANY_ID
  - Dtype: varchar(255)
    Field: Gender
    PHI: true
    Role: GENDER
  - Dtype: varchar(255)
    Field: patgenid
    PHI: true
    Role: GENDER
  - Dtype: varchar(255)
    Field: PATGENIDOTH
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: PATORIEN
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PATORIENOTH
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: DOB
    PHI: true
    Role: '|DATE_ONLY||DOB|'
  - Dtype: varchar(255)
    Field: DOBPrecision
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: JC_MEDICARE
    PHI: false
    Role: null
  - Dtype: varchar(55)
    Field: RaceOtherHispanic
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: ArrivalDatetime
    PHI: true
    Role: '|ARRIVAL_DATE||DATE|'
  - Dtype: varchar(255)
    Field: ArrivalDateTimePrecision
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: AdmitDate
    PHI: true
    Role: '|ADMISSION_DATE||DATE_ONLY|'
  - Dtype: varchar(255)
    Field: AdmitDatePrecision
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: DischargeDate
    PHI: true
    Role: '|DISCHARGE_DATE||DATE|'
  - Dtype: varchar(255)
    Field: DischargeDatePrecision
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: DischargeStatus
    PHI: false
    Role: null
  - Dtype: text
    Field: JC_NOTSTROKE
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: ICD10diagnosis
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: i10prindx_description
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: I10PRINDXTAB
    PHI: false
    Role: null
  - Dtype: text
    Field: OtherDiagICD10
    PHI: false
    Role: null
  - Dtype: text
    Field: i10otherdx_description
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: I10OTHERDXTAB
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: I10PCSND
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: I10PRINPCS
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: JC_PrincICD10Procedure
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PCMODIFIERDATE
    PHI: true
    Role: DATE
  - Dtype: varchar(255)
    Field: pcmodifierdate_p
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: I10PRINPCSTAB
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: I10OTHPCSTAB
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: I10ADMITDX
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: ADMITTINGDIAGNOSISICD10
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: I10ADMITDXTAB
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: CIPP
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: EARLIESTCSTK01
    PHI: true
    Role: DATE
  - Dtype: varchar(255)
    Field: earliestcstk01_p
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: EARLIESTCSTK03
    PHI: true
    Role: DATE
  - Dtype: varchar(255)
    Field: earliestcstk03_p
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CLINICALTRIAL
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: CLINICALTRIALTYPE
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: ElectiveCarotidInterventionAdmin
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: MethodPatientArrival
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: EDPATIENT
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: C_DIRECTADM
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: PRESTROKEMRSSCORE
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: C_PRESTROKEMRS
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: C_NIHSSPERF
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: InitialNIHSSscoreDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(255)
    Field: C_NIHSSDT_PRECISION
    PHI: false
    Role: null
  - Dtype: int
    Field: NIHSSOBT
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: C_NIHSSOBTUTD
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: NIHSSOBTLT6
    PHI: false
    Role: null
  - Dtype: int
    Field: C_EMSGCSEYE
    PHI: false
    Role: null
  - Dtype: int
    Field: C_EMSGCSVOIC
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: C_EMSGCSINTUB
    PHI: false
    Role: null
  - Dtype: int
    Field: C_EMSGCSMOT
    PHI: false
    Role: null
  - Dtype: int
    Field: C_EMSGCSTOT
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: EMSGCSND
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: C_SAHNA
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: C_SAHSCALE
    PHI: false
    Role: null
  - Dtype: int
    Field: C_SAHSCALE_VALUE
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: C_SAHSCALEDT
    PHI: true
    Role: DATE
  - Dtype: varchar(255)
    Field: C_SAHSCALEDT_PRECISION
    PHI: false
    Role: null
  - Dtype: int
    Field: C_WFNSSAH
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: ICHSCORE
    PHI: false
    Role: null
  - Dtype: int
    Field: C_ICHSCORE_VALUE
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: ICHSCOREDT
    PHI: true
    Role: DATE
  - Dtype: varchar(255)
    Field: C_ICHSCOREDT_PRECISION
    PHI: false
    Role: null
  - Dtype: int
    Field: C_FUNCSCORE
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: IvThromboInitiatedDT
    PHI: true
    Role: DATE
  - Dtype: varchar(255)
    Field: IVTHRODT_P
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: IAtPAInitiationDatetime
    PHI: true
    Role: DATE
  - Dtype: int
    Field: FastingGlucose
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: BloodGlucoseND
    PHI: false
    Role: null
  - Dtype: int
    Field: C_PLATELET
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: PLATELETUTD
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: C_INRPERF
    PHI: false
    Role: null
  - Dtype: int
    Field: SYSTOLICBP
    PHI: false
    Role: null
  - Dtype: int
    Field: DiastolicBP
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: DischargeBPND
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: DOORTOREP
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: PUNCTOREP
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: C_IAROUTETPA
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: C_IATHROMINIT
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: C_IATHROMINITDT
    PHI: true
    Role: DATE
  - Dtype: varchar(255)
    Field: C_IATHROMINITDT_PRECISION
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: C_DELAYED
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: C_SKIN
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: c_artpuncdt
    PHI: true
    Role: DATE
  - Dtype: varchar(255)
    Field: C_ARTPUNCDT_PRECISION
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: C_IVTPAPRIOR
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: MER_PROC
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: FAILEDTHROM
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: IATYPE
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: C_FIRSTPASS
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: FirstPassDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(255)
    Field: FIRSTPASSDT_P
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: C_PROXDIST
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: c_cartocc
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: c_ticigrade
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: THROMTICIGRADE2
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: THROMTICIDT
    PHI: true
    Role: DATE
  - Dtype: varchar(255)
    Field: THROMTICIDT_P
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: ENDENDODT
    PHI: true
    Role: DATE
  - Dtype: varchar(255)
    Field: ENDENDODT_P
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: C_NEUROIMAGING36H
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: C_POSBrain
    PHI: true
    Role: DATE
  - Dtype: varchar(255)
    Field: PosBrainImgDateTimePrecision
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: C_POSBrain_Result
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: MER_IVSCOREOBT
    PHI: false
    Role: null
  - Dtype: int
    Field: C_NIHSSIVT
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: C_NIHSSIVTUTD
    PHI: false
    Role: null
  - Dtype: int
    Field: C_NIHSS36IV
    PHI: false
    Role: null
  - Dtype: text
    Field: C_NIHSS36IVUTD
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: MER_SCOREOBT
    PHI: false
    Role: null
  - Dtype: int
    Field: C_NIHSSIATPAMER
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: C_NIHSSIATPAUTD
    PHI: false
    Role: null
  - Dtype: int
    Field: C_NIHSS36IA
    PHI: false
    Role: null
  - Dtype: text
    Field: NIHSS36IAUTD
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: C_PROCOAGULANT
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: C_PROCOAGULANT_DT
    PHI: true
    Role: DATE
  - Dtype: varchar(255)
    Field: C_PROCOAGULANT_DT_PRECISION
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: C_REASNOPRO
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: INRDT
    PHI: true
    Role: DATE
  - Dtype: varchar(255)
    Field: C_INR_DT_PRECISION
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: C_INRAFTERTX
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: NIMODIPINE
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: C_NIMODIPINE_DT
    PHI: true
    Role: DATE
  - Dtype: varchar(255)
    Field: C_NIMODIPINE_DT_PRECISION
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: C_REASONNONIM
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: C_SURGICALICH
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: SURGICALICHTYPE
    PHI: false
    Role: null
  - Dtype: int
    Field: C_SURGICALICH_HR
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: CSEXGENDER
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: RaceChinese
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: JC_COMFORTONLY2
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CIVTHROINIT
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: JC_DISCDATETIME
    PHI: true
    Role: DATE
  - Dtype: varchar(255)
    Field: JC_DISCDATETIME_PRECISION
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: C_DISCDTUTD
    PHI: false
    Role: null
  - Dtype: text
    Field: CSTKADDCM
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK01MR
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK01MP
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK01MX
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK01MB
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK01MD
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK01ME
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK03MR
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK03MP
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK03MX
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK03MB
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK03MD
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK03ME
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK03AMR
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK03AMP
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK03AMX
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK03AMB
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK03AMD
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK03AME
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK03BMR
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK03BMP
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK03BMX
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK03BMB
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK03BMD
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK03BME
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK04MR
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK04MP
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK04MX
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK04MB
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK04MD
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK04ME
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK05MR
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK05MP
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK05MX
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK05MB
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK05MD
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK05ME
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK05AMR
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK05AMP
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK05AMX
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK05AMB
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK05AMD
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK05AME
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK05BMR
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK05BMP
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK05BMX
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK05BMB
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK05BMD
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK05BME
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK06MR
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK06MP
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK06MX
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK06MB
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK06MD
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK06ME
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK08MR
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK08MP
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK08MX
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK08MB
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK08MD
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK08ME
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK09MR
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK09MP
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK09MX
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK09MB
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK09MD
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK09MY
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK09AMR
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK09AMP
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK09AMX
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK09AMB
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK09AMD
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK09AMY
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK09BMR
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK09BMP
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK09BMX
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK09BMB
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK09BMD
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK09BMY
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK11MR
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK11MP
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK11MX
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK11MB
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK11MD
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK11ME
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK12MR
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK12MP
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK12MX
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK12MB
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK12MD
    PHI: false
    Role: null
  - Dtype: tinyint(1)
    Field: CSTK12ME
    PHI: false
    Role: null
  - Dtype: text
    Field: ClientFileId
    PHI: false
    Role: null
  - Dtype: text
    Field: FileName
    PHI: false
    Role: null
  - Dtype: text
    Field: BiomeImportDt
    PHI: false
    Role: Date
  - Dtype: int
    Field: Version
    PHI: false
    Role: null
  - Dtype: varchar(64)
    Field: DatasetName
    PHI: false
    Role: null
  - Dtype: varchar(16)
    Field: HospName
    PHI: false
    Role: HOSPITAL
  - Dtype: varchar(34)
    Field: CareEntityId
    PHI: false
    Role: null
  - Dtype: varchar(34)
    Field: TenantID
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: age
    PHI: false
    Role: null
rename_fields:
  - New Field: cdiscdtutd
    Old Field: C_DISCDTUTD
  - New Field: ccomfortonly2
    Old Field: JC_COMFORTONLY2
  - New Field: cdiscdatetime
    Old Field: JC_DISCDATETIME
  - New Field: cdiscdatetime.P
    Old Field: JC_DISCDATETIME_PRECISION
  - New Field: crace
    Old Field: RaceChinese
  - New Field: surgicalichhr
    Old Field: C_SURGICALICH_HR
  - New Field: surgicalich
    Old Field: C_SURGICALICH
  - New Field: reasonnonim
    Old Field: C_REASONNONIM
  - New Field: nimodipinedt.P
    Old Field: C_NIMODIPINE_DT_PRECISION
  - New Field: nimodipinedt
    Old Field: C_NIMODIPINE_DT
  - New Field: inraftertx
    Old Field: C_INRAFTERTX
  - New Field: inrdt.P
    Old Field: C_INR_DT_PRECISION
  - New Field: reasnopro
    Old Field: C_REASNOPRO
  - New Field: procoagulantdt.P
    Old Field: C_PROCOAGULANT_DT_PRECISION
  - New Field: procoagulantdt
    Old Field: C_PROCOAGULANT_DT
  - New Field: procoagulant
    Old Field: C_PROCOAGULANT
  - New Field: nihss36ia
    Old Field: C_NIHSS36IA
  - New Field: nihssiatpautd
    Old Field: C_NIHSSIATPAUTD
  - New Field: nihssiatpamer
    Old Field: C_NIHSSIATPAMER
  - New Field: merscoreobt
    Old Field: MER_SCOREOBT
  - New Field: nihss36ivutd
    Old Field: C_NIHSS36IVUTD
  - New Field: nihss36iv
    Old Field: C_NIHSS36IV
  - New Field: nihssivtutd
    Old Field: C_NIHSSIVTUTD
  - New Field: nihssivt
    Old Field: C_NIHSSIVT
  - New Field: merivscoreobt
    Old Field: MER_IVSCOREOBT
  - New Field: posbrainresult
    Old Field: C_POSBrain_Result
  - New Field: posbrain.P
    Old Field: PosBrainImgDateTimePrecision
  - New Field: posbrain
    Old Field: C_POSBrain
  - New Field: neuroimaging36h
    Old Field: C_NEUROIMAGING36H
  - New Field: endendodt.P
    Old Field: endendodt_p
  - New Field: cartocc
    Old Field: c_cartocc
  - New Field: thromticidt.P
    Old Field: thromticidt_p
  - New Field: thromticigrade
    Old Field: c_ticigrade
  - New Field: proxdist
    Old Field: C_PROXDIST
  - New Field: firstpassdt.P
    Old Field: firstpassdt_p
  - New Field: firstpassdt
    Old Field: FirstPassDateTime
  - New Field: firstpass
    Old Field: C_FIRSTPASS
  - New Field: merproc
    Old Field: MER_PROC
  - New Field: ivtpaprior
    Old Field: C_IVTPAPRIOR
  - New Field: artpuncdt.P
    Old Field: C_ARTPUNCDT_PRECISION
  - New Field: artpuncdt
    Old Field: c_artpuncdt
  - New Field: skin
    Old Field: C_SKIN
  - New Field: iathrominitdt.P
    Old Field: C_IATHROMINITDT_PRECISION
  - New Field: delayed
    Old Field: C_DELAYED
  - New Field: iathrominit
    Old Field: C_IATHROMINIT
  - New Field: iaroutetpa
    Old Field: C_IAROUTETPA
  - New Field: iathrominitdt
    Old Field: C_IATHROMINITDT
  - New Field: diastolic
    Old Field: DiastolicBP
  - New Field: bpnd
    Old Field: DischargeBPND
  - New Field: inrperf
    Old Field: C_INRPERF
  - New Field: systolic
    Old Field: SystolicBP
  - New Field: platelet
    Old Field: C_PLATELET
  - New Field: bloodglucose_nd
    Old Field: BloodGlucoseND
  - New Field: iatpadt.P
    Old Field: iatpadt_p
  - New Field: fastingblood
    Old Field: FastingGlucose
  - New Field: iatpadt
    Old Field: IAtPAInitiationDatetime
  - New Field: ivthrodt.P
    Old Field: ivthrodt_p
  - New Field: ichfuncscore
    Old Field: C_FUNCSCORE
  - New Field: ichscoredt.P
    Old Field: C_ICHSCOREDT_PRECISION
  - New Field: ichscorevalue
    Old Field: C_ICHSCORE_VALUE
  - New Field: wfnssah
    Old Field: C_WFNSSAH
  - New Field: sahscaledt.P
    Old Field: C_SAHSCALEDT_PRECISION
  - New Field: sahscaledt
    Old Field: C_SAHSCALEDT
  - New Field: sahscalevalue
    Old Field: C_SAHSCALE_VALUE
  - New Field: sahscale
    Old Field: C_SAHSCALE
  - New Field: sahna
    Old Field: C_SAHNA
  - New Field: emsgcstot
    Old Field: C_EMSGCSTOT
  - New Field: emsgcsmot
    Old Field: C_EMSGCSMOT
  - New Field: emsgcsintub
    Old Field: C_EMSGCSINTUB
  - New Field: emsgcsvoic
    Old Field: C_EMSGCSVOIC
  - New Field: emsgcseye
    Old Field: C_EMSGCSEYE
  - New Field: nihssobtutd
    Old Field: C_NIHSSOBTUTD
  - New Field: nihssdt.P
    Old Field: C_NIHSSDT_PRECISION
  - New Field: nihssdt
    Old Field: InitialNIHSSscoreDateTime
  - New Field: nihssperf
    Old Field: C_NIHSSPERF
  - New Field: prestrokemrs
    Old Field: C_PRESTROKEMRS
  - New Field: directadmit
    Old Field: C_DIRECTADM
  - New Field: patientarrival
    Old Field: MethodPatientArrival
  - New Field: electivecarotid
    Old Field: ElectiveCarotidInterventionAdmin
  - New Field: earliestcstk03.P
    Old Field: earliestcstk03_p
  - New Field: earliestcstk01.P
    Old Field: earliestcstk01_p
  - New Field: I10admitdx-Description
    Old Field: ADMITTINGDIAGNOSISICD10
  - New Field: pcmodifierdate.P
    Old Field: pcmodifierdate_p
  - New Field: I10prinpcs-Description
    Old Field: JC_PrincICD10Procedure
  - New Field: I10otherdx
    Old Field: OtherDiagICD10
  - New Field: I10prindx
    Old Field: ICD10diagnosis
  - New Field: notstroke
    Old Field: JC_NOTSTROKE
  - New Field: disdate.P
    Old Field: DischargeDatePrecision
  - New Field: disdate
    Old Field: DischargeDate
  - New Field: admdt.P
    Old Field: AdmitDatePrecision
  - New Field: admdt
    Old Field: AdmitDate
  - New Field: arrdt.P
    Old Field: ArrivalDateTimePrecision
  - New Field: arrdt
    Old Field: ArrivalDatetime
  - New Field: medicarejc
    Old Field: JC_MEDICARE
  - New Field: dob.P
    Old Field: DOBPrecision
  - New Field: sex
    Old Field: Gender
  - New Field: UPDATED_BY
    Old Field: UpdatedBy
  - New Field: CREATED_BY
    Old Field: CreatedBy
  - New Field: FORM_STATUS
    Old Field: STATUS
  - New Field: CREATED_DT
    Old Field: CreatedDateTime
  - New Field: PATIENT_DISPLAY_ID
    Old Field: StrokePatientId
  - New Field: HISETHNI
    Old Field: RaceOtherHispanic
  - New Field: ACCESS_CASE
    Old Field: accesscase
  - New Field: SCHEDULED_FORM_NAME
    Old Field: SCHEDULEDFORMNAME
  - New Field: i10otherdx-description
    Old Field: i10otherdx_description
  - New Field: i10prindx-description
    Old Field: i10prindx_description
  - New Field: dschstat
    Old Field: DischargeStatus
  - New Field: case_id
    Old Field: CaseId
  - New Field: FACILITY_NAME
    Old Field: ServiceSiteName
  - New Field: FACILITY_DISPLAY_ID
    Old Field: ServiceSiteCode
  - New Field: ivthrodt
    Old Field: IvThromboInitiatedDT