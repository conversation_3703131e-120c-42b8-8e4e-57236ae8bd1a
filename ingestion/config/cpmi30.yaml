version: '3.0'
biome_schema:
  - Dtype: bigint
    Field: PATIENTROWID
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: NCDRPatientId
    PHI: true
    Role: IDENTIFIER
  - Dtype: varchar(20)
    Field: PartID
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: PartName
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: FirstName
    PHI: true
    Role: NAME
  - Dtype: varchar(30)
    Field: LastName
    PHI: true
    Role: NAME
  - Dtype: varchar(30)
    Field: MidName
    PHI: true
    Role: NAME
  - Dtype: varchar(10)
    Field: SSNNA
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: SSN
    PHI: true
    Role: NAME
  - Dtype: varchar(30)
    Field: OtherId
    PHI: true
    Role: MEDRECNUM
  - Dtype: varchar(30)
    Field: DOB
    PHI: true
    Role: DOB
  - Dtype: varchar(30)
    Field: Gender
    PHI: true
    Role: GENDER
  - Dtype: varchar(14)
    Field: PatZipNA
    PHI: false
    Role: null
  - Dtype: varchar(9)
    Field: PatZip
    PHI: true
    Role: PATIENT_ZIP
  - Dtype: varchar(10)
    Field: RaceWhite
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: RaceBlackAfricanAmerican
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: RaceAmericanIndianAlaskanNative
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceAsian
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceAsianIndian
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceChinese
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceFilipino
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceJapanese
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceKorean
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceVietnamese
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: RaceOtherAsian
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: RaceNatHawPacificIslander
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceNatHaw
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: RaceGuamanianOrChamorro
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceSamoan
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: RaceOtherPacificIslander
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: HispOrig
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: HispanicEthnicityTypeMexicanMexicanAmericanChicano
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: HispanicEthnicityTypePuertoRican
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: HispanicEthnicityTypeCuban
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HispanicEthnicityTypeOtherHisp
    PHI: false
    Role: null
  - Dtype: bigint
    Field: EPISODEROWID
    PHI: false
    Role: null
  - Dtype: text
    Field: EpisodeId
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: ArrivalDatetime
    PHI: true
    Role: ARRIVAL_DATE
  - Dtype: varchar(31)
    Field: AdmitDatetime
    PHI: true
    Role: DATE
  - Dtype: varchar(21)
    Field: AdmFName
    PHI: false
    Role: null
  - Dtype: varchar(26)
    Field: AdmLName
    PHI: false
    Role: null
  - Dtype: varchar(21)
    Field: AdmMidName
    PHI: false
    Role: null
  - Dtype: varchar(22)
    Field: AdmNPI
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HealthIns
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Medicare
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: HIC
    PHI: true
    Role: NAME
  - Dtype: varchar(15)
    Field: MiltaryHealthCare
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: HICNumber
    PHI: false
    Role: null
  - Dtype: varchar(23)
    Field: MedicareBeneficiaryId
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: EnrolledStudy
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PtRestriction2
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: EDFName
    PHI: false
    Role: null
  - Dtype: varchar(27)
    Field: EDLName
    PHI: false
    Role: null
  - Dtype: varchar(21)
    Field: EDMName
    PHI: false
    Role: null
  - Dtype: varchar(22)
    Field: EDNPI
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: AttnLName
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: AttnFName
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: AttnMidName
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: AttnNPI
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: StudyName
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: StudyPatientID
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: Height
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: Weight
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HxAFib
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: AtrialFlutter
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: Hypertension
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: Dyslipidemia
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: CurrentDialysis
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: CANCER
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PriorMI
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: Diabetes
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: PriorHF
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: PriorPCI
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: LastPCIDate
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: PriorCABG
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PriorCABGDate
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: HxCVD
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Stroke
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HxTIA
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PriorPAD
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: TobaccoUse
    PHI: false
    Role: null
  - Dtype: varchar(22)
    Field: TobaccoType
    PHI: false
    Role: null
  - Dtype: varchar(40)
    Field: SmokingAmount
    PHI: false
    Role: null
  - Dtype: varchar(22)
    Field: Walking
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: WalkingUnk
    PHI: false
    Role: null
  - Dtype: varchar(27)
    Field: Cognition
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: CognitionUnk
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: BasicADLsUNK
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: BasicADLs
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HomeMedClopidogrel
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HomeMedASA
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: HomeMedPrasugrel
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HomeMedTicagrelor
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HomeMedWarfarin
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: HomeMedDabigatran
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HomeMedRivaroxaban
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HomeMedApixaban
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: HomeMedEdoxaban
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HomeMedEvolocumab
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: HomeMedAlirocumab
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HomeMedBetaBlocker
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HomeMedACEI
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HomeMedSacubitrilValsartan
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HomeMedEzetimibe
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HomeMedFenofibrate
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HomeMedAngiotensinII
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HomeMedAldosteroneAntagonist
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HomeMedStatin
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HomeMedDPP4inhibitor
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HomeMedGLP1ReceptorAgonist
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HomeMedMetformin
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HomeMedOtherOralHypoglyc
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HomeMedPioglitazone
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HomeMedSGLT2Inhibitor
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HomeMedSulfonylurea
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HomeMedInsulin
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: ThromTherapyFMC
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: ThromDateTimeFMC
    PHI: true
    Role: DATE
  - Dtype: varchar(31)
    Field: EMSDispatchDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(31)
    Field: EMSLeavingSceneDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(22)
    Field: EMSAgencyNumber
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: EMSRunNumber
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: ECGPerfEMS
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: EMSSTEMIAlert
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: TranOutsideFac
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: TransferHospMeansTransfer
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: ArrOutHospDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(31)
    Field: TranOutFacDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(69)
    Field: TranFacName
    PHI: false
    Role: null
  - Dtype: varchar(19)
    Field: TranFacAHANumber
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: TranFacNameUnavail
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LVEFAssessedAD
    PHI: false
    Role: null
  - Dtype: int
    Field: LVEFPercent
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LVEFAfterDischarge
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: DiagCorAngioFirst
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: CathArrivalDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(19)
    Field: DCathLName
    PHI: false
    Role: null
  - Dtype: varchar(19)
    Field: DCathFName
    PHI: false
    Role: null
  - Dtype: varchar(19)
    Field: DCathMName
    PHI: false
    Role: null
  - Dtype: varchar(22)
    Field: DCathNPI
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: DiagCorAngioDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: NVStenosis
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: GVStenosis
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CABGFirst
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: CABGDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: PrivateHealthInsurance
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Medicaid
    PHI: false
    Role: null
  - Dtype: bigint
    Field: CARDIACPRESSROWID
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PatientType
    PHI: false
    Role: null
  - Dtype: varchar(21)
    Field: STEMISet
    PHI: false
    Role: null
  - Dtype: varchar(23)
    Field: FirstFacMeansTrans
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: FirstMedConDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: EMSFirstMedConReasonforDelay
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HFFirstMedCon
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: ShockFirstMedCon
    PHI: false
    Role: null
  - Dtype: int
    Field: HRFirstMedCon
    PHI: false
    Role: null
  - Dtype: int
    Field: SBPFirstMedCon
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CAOutHospital
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CAWitness
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: CAPostEMS
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: BystandCPR
    PHI: false
    Role: null
  - Dtype: varchar(21)
    Field: InitCARhythm
    PHI: false
    Role: null
  - Dtype: varchar(17)
    Field: InitCARhythmUnk
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: ResuscDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: CATransferFac
    PHI: false
    Role: null
  - Dtype: varchar(24)
    Field: LocFirstEval
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: TranOutEDDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(23)
    Field: EDDisposition
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: ObserOrderDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(31)
    Field: SymptomDate
    PHI: true
    Role: DATE
  - Dtype: text
    Field: SymptomTime
    PHI: false
    Role: TIME
  - Dtype: varchar(15)
    Field: RiskScorePerf
    PHI: false
    Role: null
  - Dtype: varchar(29)
    Field: NameRiskScore
    PHI: false
    Role: null
  - Dtype: varchar(13)
    Field: TIMIScore
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: GraceScore
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: IschemSymResolv
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CXRPerf
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: NoninvasPerformed
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: NonInvasPlndDC
    PHI: false
    Role: null
  - Dtype: varchar(27)
    Field: PreProcASA
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PreProcASADose
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PreProcASAStartDate
    PHI: true
    Role: DATE
  - Dtype: varchar(27)
    Field: PreProcBetaBlocker
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PreProcBetaBlockerDose
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PreProcBetaBlockerStartDate
    PHI: true
    Role: DATE
  - Dtype: varchar(27)
    Field: PreProcClopidogrel
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PreProcClopidogrelDose
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: PreProcClopidogrelStartDate
    PHI: true
    Role: DATE
  - Dtype: varchar(27)
    Field: PreProcPrasugrel
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PreProcPrasugrelDose
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PreProcPrasugrelStartDate
    PHI: true
    Role: DATE
  - Dtype: varchar(27)
    Field: PreProcTicagrelor
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PreProcTicagrelorDose
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: PreProcTicagrelorStartDate
    PHI: true
    Role: DATE
  - Dtype: bigint
    Field: ELECTROCARDIOROWID
    PHI: false
    Role: null
  - Dtype: int
    Field: ECGCounter
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: ECGDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: StemiFirstNotedFMC
    PHI: false
    Role: null
  - Dtype: varchar(33)
    Field: STEMIECGFindings
    PHI: false
    Role: null
  - Dtype: varchar(57)
    Field: OtherECGFindings
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: NONINVASIVEROWID
    PHI: false
    Role: null
  - Dtype: varchar(46)
    Field: NonInvasiveTestType
    PHI: false
    Role: null
  - Dtype: varchar(18)
    Field: NonInvasTestMeth
    PHI: false
    Role: null
  - Dtype: bigint
    Field: LABROWID
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: TROPONINROWID
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: TropCount
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: TropCollDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(31)
    Field: TropResDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: TropTestLoc
    PHI: false
    Role: null
  - Dtype: varchar(16)
    Field: LabTropAssay
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TropAssayURL
    PHI: false
    Role: null
  - Dtype: varchar(18)
    Field: InitTropValue
    PHI: false
    Role: null
  - Dtype: bigint
    Field: INITLABROWID
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: FMCCreat
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: FMCCreatND
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: InitHGBValue
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: InitHgbND
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: InitHemoValue
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: InitHemoND
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: InitINRValue
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: InitINRDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: InitINRND
    PHI: false
    Role: null
  - Dtype: bigint
    Field: LOWPKLABSROWID
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: PeakCreat
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: CreatPeakDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: CreatPeakND
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: LowHGBValue
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: LowHgbValueDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: LowHgbND
    PHI: false
    Role: null
  - Dtype: bigint
    Field: LIPIDSROWID
    PHI: false
    Role: null
  - Dtype: int
    Field: LipidsTC6mFMC
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LipidsTCND6mFMC
    PHI: false
    Role: null
  - Dtype: int
    Field: LipidsHDL6mFMC
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LipidsHDLND6mFMC
    PHI: false
    Role: null
  - Dtype: int
    Field: LipidsLDL
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LDLND
    PHI: false
    Role: null
  - Dtype: int
    Field: LipidsTrig
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LipidsTrigND
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: NVESSELROWID
    PHI: false
    Role: null
  - Dtype: varchar(28)
    Field: NVSegmentID
    PHI: false
    Role: null
  - Dtype: int
    Field: NVCoroVesselStenosis
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: GVESSELROWID
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: GraftSegmentID
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: GraftCoroVesselStenosis
    PHI: false
    Role: null
  - Dtype: varchar(16)
    Field: CABGGraftVessel
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: CABGGraftVesselUnk
    PHI: false
    Role: null
  - Dtype: bigint
    Field: PCIPROCROWID
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PCIArrivDC
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PCIFName
    PHI: false
    Role: null
  - Dtype: varchar(19)
    Field: PCILName
    PHI: false
    Role: null
  - Dtype: varchar(19)
    Field: PCIMName
    PHI: false
    Role: null
  - Dtype: varchar(22)
    Field: PCINPI
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: StentsImplanted
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: StentType
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: AccessSite
    PHI: false
    Role: null
  - Dtype: varchar(54)
    Field: PCIIndication
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: ReasonPCINotPerf
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: MechVentSupp
    PHI: false
    Role: null
  - Dtype: varchar(105)
    Field: MVSupportDevice
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CathLabAct
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: CathLabActDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: CathLabActCanc
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: FirstDevActiDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: PtPCIDelayReason
    PHI: false
    Role: null
  - Dtype: varchar(64)
    Field: PCIDelayReason
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: CurrStentTypeUnk
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Procedure_Low_Molecular_Weight_Heparin_any
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: Procedure_Edoxaban
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Procedure_Clopidogrel
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Procedure_Unfractionated_Heparin_any
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: Procedure_Rivaroxaban
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: Procedure_Prasugrel
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: Procedure_Warfarin
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: Procedure_Cangrelor
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Procedure_Ticagrelor
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: Procedure_Vorapaxar
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: Procedure_Bivalirudin
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Procedure_GPInhibitors_Any
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: Procedure_Fondaparinux
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: Procedure_Apixaban
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: Procedure_Dabigatran
    PHI: false
    Role: null
  - Dtype: bigint
    Field: EVENTSROWID
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CERBC
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: CERBCDate
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: CERBCCABG
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: NSAIDAdmin
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: HypothermiaInducedFMCDC
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: HypothermiaInducedDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(12)
    Field: HypoProtLoc
    PHI: false
    Role: null
  - Dtype: varchar(28)
    Field: LocFMCDC
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: NewRequirementforDialysis
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DialysisDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: BleedingOther
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: BleedingOtherDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: BleedingAccessSite
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: BleedingAccessSiteDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: BleedingSurgicalProc
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: BleedingSurgProcDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(30)
    Field: MyocardialInfarction
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: MyocardialInfarctionDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: StrokeHemorrhagic
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: StrokeHemorrhagicDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: StrokeUndetermined
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: StrokeUndeterminedDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: VentTachy
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: VentTachyDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: TransIschemicAttack
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TransIschemicAttackDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: CardiacArrest
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: CardiacArrestDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: BleedingGenitourinary
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: BleedingGenitourinaryDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: StrokeIschemic
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: StrokeIschemicDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: BleedingAtrialFibrillation
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: BleedingAtrialFibDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: Intubation
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: IntubationDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: VentFib
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: VentFibDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: BleedingGastrointestinal
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: BleedingGastrointestinalDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: HeartFailure
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: HeartFailureDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: CardiogenicShock
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: CardiogenicShockDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: BleedingRetroperitoneal
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: BleedingRetroDateTime
    PHI: true
    Role: DATE
  - Dtype: bigint
    Field: DISCHARGEROWID
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: DCDateTime
    PHI: true
    Role: ANCHOR_DATE
  - Dtype: varchar(27)
    Field: DCLName
    PHI: false
    Role: null
  - Dtype: varchar(21)
    Field: DCFName
    PHI: false
    Role: null
  - Dtype: varchar(21)
    Field: DCMName
    PHI: false
    Role: null
  - Dtype: varchar(22)
    Field: DCNPI
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: DischargeComfort
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: DCComfortDatetime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: HospClinicTrial
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TypeClinTrial
    PHI: false
    Role: null
  - Dtype: varchar(60)
    Field: DischargeStatus
    PHI: false
    Role: null
  - Dtype: varchar(53)
    Field: DischargeCardRehab
    PHI: false
    Role: null
  - Dtype: varchar(60)
    Field: DischargeLocation
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TransDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(12)
    Field: TransferPCI
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: CABGTransferDC
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: DCHospice
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: HospCareDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(39)
    Field: DeathCause
    PHI: false
    Role: null
  - Dtype: varchar(43)
    Field: Discharge_ACE_Inhibitor_any
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_ACEInhibitors_Any_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_Warfarin
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_Warfarin_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: DischargeASA
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: DC_ASA_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(43)
    Field: Discharge_AldoAntag
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_AldoAntag_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_ARB_any
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_ARB_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_Non_Statin_any
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_Non_Statin_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_Apixaban
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_Apixaban_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_Dabigatran
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_Dabigatran_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_Edoxaban
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_Edoxaban_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_Prasugrel
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_Prasugrel_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_Ticagrelor
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_Ticagrelor_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(43)
    Field: Discharge_SacubValsar
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_SacubValsar_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: DischargeStatinAny
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: DC_Statin_Any_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_Clopidogrel
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_Clopidogrel_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: DischargeBetaBlocker
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_BetaBlocker_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_Rivaroxaban
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_Rivaroxaban_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: DataVrsn
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: HospClinicTrialType
    PHI: false
    Role: null
  - Dtype: varchar(22)
    Field: OriginalOtherId
    PHI: true
    Role: MEDRECNUM
  - Dtype: varchar(100)
    Field: HospName
    PHI: false
    Role: HOSPITAL
  - Dtype: varchar(69)
    Field: FileName
    PHI: false
    Role: null
  - Dtype: int
    Field: Version
    PHI: false
    Role: null
  - Dtype: varchar(19)
    Field: DatasetName
    PHI: false
    Role: null
  - Dtype: int
    Field: ClientFileId
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: BiomeImportDT
    PHI: false
    Role: null
  - Dtype: varchar(34)
    Field: CareEntityId
    PHI: false
    Role: CAREENTITY
  - Dtype: varchar(34)
    Field: TenantID
    PHI: false
    Role: TENANT
  - Dtype: decimal(8,2)
    Field: DistFromHospital
    PHI: false
    Role: DISTANCE_FROM_HOSPITAL
  - Dtype: varchar(12)
    Field: IndianHealthService
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: StateSpecificPlan
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: NonUSInsurance
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: Procedure_HeparinDerivative
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: VendorVer
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: VendorID
    PHI: false
    Role: null
  - Dtype: bigint
    Field: BiomeEncounterId
    PHI: false
    Role: null
custom_fields:
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin Converting Enzyme Inhibitor
    Derivative Value Code: 41549009
    Exact Match: 1
    Field: Discharge_ACE_Inhibitor_any
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin Converting Enzyme Inhibitor
    Derivative Value Code: 41549009
    Exact Match: 1
    Field: DC_ACEInhibitors_Any_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aldosterone Antagonist
    Derivative Value Code: 372603003
    Exact Match: 1
    Field: Discharge_AldoAntag
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aldosterone Antagonist
    Derivative Value Code: 372603003
    Exact Match: 1
    Field: DC_AldoAntag_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Warfarin
    Derivative Value Code: 11289
    Exact Match: 1
    Field: Discharge_Warfarin
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Warfarin
    Derivative Value Code: 11289
    Exact Match: 1
    Field: DC_Warfarin_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aspirin
    Derivative Value Code: 1191
    Exact Match: 1
    Field: DischargeASA
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aspirin
    Derivative Value Code: 1191
    Exact Match: 1
    Field: DC_ASA_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin II Receptor Blocker
    Derivative Value Code: 372913009
    Exact Match: 1
    Field: Discharge_ARB_any
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin II Receptor Blocker
    Derivative Value Code: 372913009
    Exact Match: 1
    Field: DC_ARB_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Non-Statin
    Derivative Value Code: 100014161
    Exact Match: 1
    Field: Discharge_Non_Statin_any
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Non-Statin
    Derivative Value Code: 100014161
    Exact Match: 1
    Field: DC_Non_Statin_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Apixaban
    Derivative Value Code: 1364430
    Exact Match: 1
    Field: Discharge_Apixaban
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Apixaban
    Derivative Value Code: 1364430
    Exact Match: 1
    Field: DC_Apixaban_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Dabigatran
    Derivative Value Code: 1546356
    Exact Match: 1
    Field: Discharge_Dabigatran
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Dabigatran
    Derivative Value Code: 1546356
    Exact Match: 1
    Field: DC_Dabigatran_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Edoxaban
    Derivative Value Code: 1599538
    Exact Match: 1
    Field: Discharge_Edoxaban
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Edoxaban
    Derivative Value Code: 1599538
    Exact Match: 1
    Field: DC_Edoxaban_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Prasugrel
    Derivative Value Code: 613391
    Exact Match: 1
    Field: Discharge_Prasugrel
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Prasugrel
    Derivative Value Code: 613391
    Exact Match: 1
    Field: DC_Prasugrel_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Ticagrelor
    Derivative Value Code: 1116632
    Exact Match: 1
    Field: Discharge_Ticagrelor
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Ticagrelor
    Derivative Value Code: 1116632
    Exact Match: 1
    Field: DC_Ticagrelor_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Ticlopidine
    Derivative Value Code: 10594
    Exact Match: 1
    Field: Discharge_Ticlopidine
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Ticlopidine
    Derivative Value Code: 10594
    Exact Match: 1
    Field: DC_Ticlopidine_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Alirocumab
    Derivative Value Code: 1659152
    Exact Match: 1
    Field: Discharge_Alirocumab
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Alirocumab
    Derivative Value Code: 1659152
    Exact Match: 1
    Field: DC_Alirocumab_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Evolocumab
    Derivative Value Code: 1665684
    Exact Match: 1
    Field: Discharge_Evolocumab
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Evolocumab
    Derivative Value Code: 1665684
    Exact Match: 1
    Field: DC_Evolocumab_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Statin
    Derivative Value Code: 96302009
    Exact Match: 1
    Field: DischargeStatinAny
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Statin
    Derivative Value Code: 96302009
    Exact Match: 1
    Field: DC_Statin_Any_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Clopidogrel
    Derivative Value Code: 32968
    Exact Match: 1
    Field: Discharge_Clopidogrel
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Clopidogrel
    Derivative Value Code: 32968
    Exact Match: 1
    Field: DC_Clopidogrel_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Beta Blocker
    Derivative Value Code: 33252009
    Exact Match: 1
    Field: DischargeBetaBlocker
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Beta Blocker
    Derivative Value Code: 33252009
    Exact Match: 1
    Field: DC_BetaBlocker_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Rivaroxaban
    Derivative Value Code: 1114195
    Exact Match: 1
    Field: Discharge_Rivaroxaban
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Rivaroxaban
    Derivative Value Code: 1114195
    Exact Match: 1
    Field: DC_Rivaroxaban_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Sacubitril and Valsartan
    Derivative Value Code: 1656341
    Exact Match: 1
    Field: Discharge_SacubValsar
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Sacubitril and Valsartan
    Derivative Value Code: 1656341
    Exact Match: 1
    Field: DC_SacubValsar_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Access Site
    Derivative Value Code: 1000142440
    Exact Match: 1
    Field: BleedingAccessSite
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Access Site
    Derivative Value Code: 1000142440
    Exact Match: 1
    Field: BleedingAccessSiteDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Gastrointestinal
    Derivative Value Code: 74474003
    Exact Match: 1
    Field: BleedingGastrointestinal
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Gastrointestinal
    Derivative Value Code: 74474003
    Exact Match: 1
    Field: BleedingGastrointestinalDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Genitourinary
    Derivative Value Code: 417941003
    Exact Match: 1
    Field: BleedingGenitourinary
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Genitourinary
    Derivative Value Code: 417941003
    Exact Match: 1
    Field: BleedingGenitourinaryDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Atrial Fibrillation
    Derivative Value Code: 49436004
    Exact Match: 1
    Field: BleedingAtrialFibrillation
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Atrial Fibrillation
    Derivative Value Code: 49436004
    Exact Match: 1
    Field: BleedingAtrialFibDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Other
    Derivative Value Code: 1000142371
    Exact Match: 1
    Field: BleedingOther
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Other
    Derivative Value Code: 1000142371
    Exact Match: 1
    Field: BleedingOtherDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Retroperitoneal
    Derivative Value Code: 95549001
    Exact Match: 1
    Field: BleedingRetroperitoneal
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Retroperitoneal
    Derivative Value Code: 95549001
    Exact Match: 1
    Field: BleedingRetroDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Cardiac Arrest
    Derivative Value Code: 410429000
    Exact Match: 1
    Field: CardiacArrest
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Cardiac Arrest
    Derivative Value Code: 410429000
    Exact Match: 1
    Field: CardiacArrestDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Surgical Procedure or Intervention Required for Bleeding
      Event
    Derivative Value Code: 112000000213
    Exact Match: 1
    Field: BleedingSurgicalProc
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Surgical Procedure or Intervention Required for Bleeding
      Event
    Derivative Value Code: 112000000213
    Exact Match: 1
    Field: BleedingSurgProcDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Cardiogenic Shock
    Derivative Value Code: 89138009
    Exact Match: 1
    Field: CardiogenicShock
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Cardiogenic Shock
    Derivative Value Code: 89138009
    Exact Match: 1
    Field: CardiogenicShockDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Heart Failure
    Derivative Value Code: 84114007
    Exact Match: 1
    Field: HeartFailure
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Heart Failure
    Derivative Value Code: 84114007
    Exact Match: 1
    Field: HeartFailureDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Myocardial Infarction
    Derivative Value Code: 22298006
    Exact Match: 1
    Field: MyocardialInfarction
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Myocardial Infarction
    Derivative Value Code: 22298006
    Exact Match: 1
    Field: MyocardialInfarctionDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: New Requirement for Dialysis
    Derivative Value Code: 100014076
    Exact Match: 1
    Field: NewRequirementforDialysis
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: New Requirement for Dialysis
    Derivative Value Code: 100014076
    Exact Match: 1
    Field: DialysisDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Intubation
    Derivative Value Code: 52765003
    Exact Match: 1
    Field: Intubation
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Intubation
    Derivative Value Code: 52765003
    Exact Match: 1
    Field: IntubationDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Stroke - Hemorrhagic
    Derivative Value Code: 230706003
    Exact Match: 1
    Field: StrokeHemorrhagic
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Stroke - Hemorrhagic
    Derivative Value Code: 230706003
    Exact Match: 1
    Field: StrokeHemorrhagicDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Stroke - Ischemic
    Derivative Value Code: 422504002
    Exact Match: 1
    Field: StrokeIschemic
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Stroke - Ischemic
    Derivative Value Code: 422504002
    Exact Match: 1
    Field: StrokeIschemicDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Stroke - Undetermined
    Derivative Value Code: 230713003
    Exact Match: 1
    Field: StrokeUndetermined
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Stroke - Undetermined
    Derivative Value Code: 230713003
    Exact Match: 1
    Field: StrokeUndeterminedDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Transient Ischemic Attack (TIA)
    Derivative Value Code: 266257000
    Exact Match: 1
    Field: TransIschemicAttack
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Transient Ischemic Attack (TIA)
    Derivative Value Code: 266257000
    Exact Match: 1
    Field: TransIschemicAttackDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Ventricular Fibrillation
    Derivative Value Code: 71908006
    Exact Match: 1
    Field: VentFib
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Ventricular Fibrillation
    Derivative Value Code: 71908006
    Exact Match: 1
    Field: VentFibDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Ventricular Tachycardia
    Derivative Value Code: 25569003
    Exact Match: 1
    Field: VentTachy
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Ventricular Tachycardia
    Derivative Value Code: 25569003
    Exact Match: 1
    Field: VentTachyDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: HOSPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: ArriMedCode
    Derivative Field Code: 100013057
    Derivative Value: Clopidogrel
    Derivative Value Code: 32968
    Exact Match: 1
    Field: PreProcClopidogrel
    Reference Field: MedsArrival
    Reference Field Code: 432102000
    Section: ARVLMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ArriMedCode
    Derivative Field Code: 100013057
    Derivative Value: Clopidogrel
    Derivative Value Code: 32968
    Exact Match: 1
    Field: PreProcClopidogrelDose
    Reference Field: MedArrivalDose
    Reference Field Code: 100014233
    Section: ARVLMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ArriMedCode
    Derivative Field Code: 100013057
    Derivative Value: Clopidogrel
    Derivative Value Code: 32968
    Exact Match: 1
    Field: PreProcClopidogrelStartDate
    Reference Field: DrugStartDT
    Reference Field Code: 432102000
    Section: ARVLMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ArriMedCode
    Derivative Field Code: 100013057
    Derivative Value: Aspirin
    Derivative Value Code: 1191
    Exact Match: 1
    Field: PreProcASA
    Reference Field: MedsArrival
    Reference Field Code: 432102000
    Section: ARVLMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ArriMedCode
    Derivative Field Code: 100013057
    Derivative Value: Aspirin
    Derivative Value Code: 1191
    Exact Match: 1
    Field: PreProcASADose
    Reference Field: MedArrivalDose
    Reference Field Code: 100014233
    Section: ARVLMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ArriMedCode
    Derivative Field Code: 100013057
    Derivative Value: Aspirin
    Derivative Value Code: 1191
    Exact Match: 1
    Field: PreProcASAStartDate
    Reference Field: DrugStartDT
    Reference Field Code: 432102000
    Section: ARVLMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ArriMedCode
    Derivative Field Code: 100013057
    Derivative Value: Beta Blocker
    Derivative Value Code: 33252009
    Exact Match: 1
    Field: PreProcBetaBlocker
    Reference Field: MedsArrival
    Reference Field Code: 432102000
    Section: ARVLMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ArriMedCode
    Derivative Field Code: 100013057
    Derivative Value: Beta Blocker
    Derivative Value Code: 33252009
    Exact Match: 1
    Field: PreProcBetaBlockerDose
    Reference Field: MedArrivalDose
    Reference Field Code: 100014233
    Section: ARVLMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ArriMedCode
    Derivative Field Code: 100013057
    Derivative Value: Beta Blocker
    Derivative Value Code: 33252009
    Exact Match: 1
    Field: PreProcBetaBlockerStartDate
    Reference Field: DrugStartDT
    Reference Field Code: 432102000
    Section: ARVLMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ArriMedCode
    Derivative Field Code: 100013057
    Derivative Value: Prasugrel
    Derivative Value Code: 613391
    Exact Match: 1
    Field: PreProcPrasugrel
    Reference Field: MedsArrival
    Reference Field Code: 432102000
    Section: ARVLMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ArriMedCode
    Derivative Field Code: 100013057
    Derivative Value: Prasugrel
    Derivative Value Code: 613391
    Exact Match: 1
    Field: PreProcPrasugrelDose
    Reference Field: MedArrivalDose
    Reference Field Code: 100014233
    Section: ARVLMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ArriMedCode
    Derivative Field Code: 100013057
    Derivative Value: Prasugrel
    Derivative Value Code: 613391
    Exact Match: 1
    Field: PreProcPrasugrelStartDate
    Reference Field: DrugStartDT
    Reference Field Code: 432102000
    Section: ARVLMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ArriMedCode
    Derivative Field Code: 100013057
    Derivative Value: Ticagrelor
    Derivative Value Code: 1116632
    Exact Match: 1
    Field: PreProcTicagrelor
    Reference Field: MedsArrival
    Reference Field Code: 432102000
    Section: ARVLMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ArriMedCode
    Derivative Field Code: 100013057
    Derivative Value: Ticagrelor
    Derivative Value Code: 1116632
    Exact Match: 1
    Field: PreProcTicagrelorDose
    Reference Field: MedArrivalDose
    Reference Field Code: 100014233
    Section: ARVLMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ArriMedCode
    Derivative Field Code: 100013057
    Derivative Value: Ticagrelor
    Derivative Value Code: 1116632
    Exact Match: 1
    Field: PreProcTicagrelorStartDate
    Reference Field: DrugStartDT
    Reference Field Code: 432102000
    Section: ARVLMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Clopidogrel
    Derivative Value Code: 32968
    Exact Match: 1
    Field: HomeMedClopidogrel
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Aspirin
    Derivative Value Code: 1191
    Exact Match: 1
    Field: HomeMedASA
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Prasugrel
    Derivative Value Code: 613391
    Exact Match: 1
    Field: HomeMedPrasugrel
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Ticagrelor
    Derivative Value Code: 1116632
    Exact Match: 1
    Field: HomeMedTicagrelor
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Warfarin
    Derivative Value Code: 11289
    Exact Match: 1
    Field: HomeMedWarfarin
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Dabigatran
    Derivative Value Code: 1546356
    Exact Match: 1
    Field: HomeMedDabigatran
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Rivaroxaban
    Derivative Value Code: 1114195
    Exact Match: 1
    Field: HomeMedRivaroxaban
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Apixaban
    Derivative Value Code: 1364430
    Exact Match: 1
    Field: HomeMedApixaban
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Edoxaban
    Derivative Value Code: 1599538
    Exact Match: 1
    Field: HomeMedEdoxaban
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Evolocumab
    Derivative Value Code: 1665684
    Exact Match: 1
    Field: HomeMedEvolocumab
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Alirocumab
    Derivative Value Code: 1659152
    Exact Match: 1
    Field: HomeMedAlirocumab
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Beta Blocker
    Derivative Value Code: 33252009
    Exact Match: 1
    Field: HomeMedBetaBlocker
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin Converting Enzyme Inhibitor
    Derivative Value Code: 41549009
    Exact Match: 1
    Field: HomeMedACEI
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Sacubitril and Valsartan
    Derivative Value Code: 1656341
    Exact Match: 1
    Field: HomeMedSacubitrilValsartan
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: 'Ezetimibe '
    Derivative Value Code: 341248
    Exact Match: 1
    Field: HomeMedEzetimibe
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Fenofibrate
    Derivative Value Code: 8703
    Exact Match: 1
    Field: HomeMedFenofibrate
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin II Receptor Blocker
    Derivative Value Code: 372913009
    Exact Match: 1
    Field: HomeMedAngiotensinII
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Aldosterone Antagonist
    Derivative Value Code: 372603003
    Exact Match: 1
    Field: HomeMedAldosteroneAntagonist
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Statin
    Derivative Value Code: 96302009
    Exact Match: 1
    Field: HomeMedStatin
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: DPP-4 inhibitor
    Derivative Value Code: 112000000263
    Exact Match: 1
    Field: HomeMedDPP4inhibitor
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: GLP-1 Receptor Agonist
    Derivative Value Code: 112000000264
    Exact Match: 1
    Field: HomeMedGLP1ReceptorAgonist
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Metformin
    Derivative Value Code: 6809
    Exact Match: 1
    Field: HomeMedMetformin
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Other Oral Hypoglycemic
    Derivative Value Code: 112000000265
    Exact Match: 1
    Field: HomeMedOtherOralHypoglyc
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Pioglitazone
    Derivative Value Code: 33738
    Exact Match: 1
    Field: HomeMedPioglitazone
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Sodium glucose cotransporter subtype 2 inhibitor
    Derivative Value Code: 703673007
    Exact Match: 1
    Field: HomeMedSGLT2Inhibitor
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Sulfonylurea
    Derivative Value Code: 372711004
    Exact Match: 1
    Field: HomeMedSulfonylurea
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Insulin
    Derivative Value Code: 67866001
    Exact Match: 1
    Field: HomeMedInsulin
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Bivalirudin
    Derivative Value Code: 400610005
    Exact Match: 1
    Field: Procedure_Bivalirudin
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PCIPROCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Fondaparinux
    Derivative Value Code: 321208
    Exact Match: 1
    Field: Procedure_Fondaparinux
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PCIPROCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Heparin Derivative
    Derivative Value Code: 100000921
    Exact Match: 1
    Field: Procedure_HeparinDerivative
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PCIPROCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Low Molecular Weight Heparin
    Derivative Value Code: 373294004
    Exact Match: 1
    Field: Procedure_Low_Molecular_Weight_Heparin_any
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PCIPROCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Unfractionated Heparin
    Derivative Value Code: 96382006
    Exact Match: 1
    Field: Procedure_Unfractionated_Heparin_any
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PCIPROCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Glycoprotein IIb IIIa Inhibitors
    Derivative Value Code: 1000142427
    Exact Match: 1
    Field: Procedure_GPInhibitors_Any
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PCIPROCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Apixaban
    Derivative Value Code: 1364430
    Exact Match: 1
    Field: Procedure_Apixaban
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PCIPROCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Dabigatran
    Derivative Value Code: 1546356
    Exact Match: 1
    Field: Procedure_Dabigatran
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PCIPROCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Edoxaban
    Derivative Value Code: 1599538
    Exact Match: 1
    Field: Procedure_Edoxaban
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PCIPROCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Rivaroxaban
    Derivative Value Code: 1114195
    Exact Match: 1
    Field: Procedure_Rivaroxaban
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PCIPROCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Cangrelor
    Derivative Value Code: 1656052
    Exact Match: 1
    Field: Procedure_Cangrelor
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PCIPROCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Clopidogrel
    Derivative Value Code: 32968
    Exact Match: 1
    Field: Procedure_Clopidogrel
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PCIPROCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Prasugrel
    Derivative Value Code: 613391
    Exact Match: 1
    Field: Procedure_Prasugrel
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PCIPROCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Ticagrelor
    Derivative Value Code: 1116632
    Exact Match: 1
    Field: Procedure_Ticagrelor
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PCIPROCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Vorapaxar
    Derivative Value Code: 1537034
    Exact Match: 1
    Field: Procedure_Vorapaxar
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PCIPROCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Warfarin
    Derivative Value Code: 11289
    Exact Match: 1
    Field: Procedure_Warfarin
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PCIPROCMEDS
    Value Map: null
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Medicare
    Derivative Value Code: 1
    Exact Match: 0
    Field: Medicare
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Medicare|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Medicaid
    Derivative Value Code: 2
    Exact Match: 0
    Field: Medicaid
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Medicaid|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Private Health Insurance
    Derivative Value Code: 5
    Exact Match: 0
    Field: PrivateHealthInsurance
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Private Health Insurance|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Military Health Care
    Derivative Value Code: 31
    Exact Match: 0
    Field: MiltaryHealthCare
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Military Health Care|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: State-Specific Plan (non-Medicaid)
    Derivative Value Code: 36
    Exact Match: 0
    Field: StateSpecificPlan
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|State-Specific Plan (non-Medicaid)|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Indian Health Service
    Derivative Value Code: 33
    Exact Match: 0
    Field: IndianHealthService
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Indian Health Service|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Non-US Insurance
    Derivative Value Code: 100000812
    Exact Match: 0
    Field: NonUSInsurance
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Non-US Insurance|': 'Yes'
rename_fields:
  - New Field: Sex
    Old Field: Gender
  - New Field: ZipCode
    Old Field: PatZip
  - New Field: ZipCodeNA
    Old Field: PatZipNA
  - New Field: HeightAD
    Old Field: Height
  - New Field: WeightAD
    Old Field: Weight
  - New Field: PCI_Indication
    Old Field: PCIIndication
  - New Field: DCStatus
    Old Field: DischargeStatus
  - New Field: DCLocation
    Old Field: DischargeLocation
  - New Field: DC_Comfort
    Old Field: DischargeComfort
  - New Field: DC_CardRehab
    Old Field: DischargeCardRehab
  - New Field: DiagCorAngio1st
    Old Field: DiagCorAngioFirst
  - New Field: CreatPeak
    Old Field: PeakCreat
  - New Field: CE_RBC
    Old Field: CERBC
  - New Field: CE_RBCDate
    Old Field: CERBCdate
  - New Field: CE_RBCCABG
    Old Field: CERBCCABG
  - New Field: TransPCI
    Old Field: TransferPCI
  - New Field: TransCABG
    Old Field: TransferCABG
  - New Field: RaceBlack
    Old Field: RaceBlackAfricanAmerican
  - New Field: RaceAmIndian
    Old Field: RaceAmericanIndianAlaskanNative
  - New Field: RaceAsianOther
    Old Field: RaceOtherAsian
  - New Field: RaceNativeHawaii
    Old Field: RaceNatHawPacificIslander
  - New Field: RaceGuamChamorro
    Old Field: RaceGuamanianOrChamorro
  - New Field: RacePacificIslandOther
    Old Field: RaceOtherPacificIslander
  - New Field: HispEthnicityMexican
    Old Field: HispanicEthnicityTypeMexicanMexicanAmericanChicano
  - New Field: HispEthnicityPuertoRico
    Old Field: HispanicEthnicityTypePuertoRican
  - New Field: HispEthnicityCuban
    Old Field: HispanicEthnicityTypeCuban
  - New Field: HispEthnicityOtherOrigin
    Old Field: HispanicEthnicityTypeOtherHisp
  - New Field: AdmMName
    Old Field: AdmMidName
  - New Field: AttLName
    Old Field: AttnLName
  - New Field: AttMName
    Old Field: AttnMidName
  - New Field: AttNPI
    Old Field: AttnNPI
  - New Field: AttFName
    Old Field: AttnFName
  - New Field: AFlutterBA
    Old Field: AtrialFlutter
  - New Field: CurrentDialysisonarrival
    Old Field: CurrentDialysis
  - New Field: HxMIFF
    Old Field: PriorMI
  - New Field: DiabetesBA
    Old Field: Diabetes
  - New Field: PriorHFFF
    Old Field: PriorHF
  - New Field: LastPCIDate_BA
    Old Field: LastPCIDate
  - New Field: StrokeBA
    Old Field: Stroke
  - New Field: SmokeAmount
    Old Field: SmokingAmount
  - New Field: LVEFPlanDC
    Old Field: LVEFAfterDischarge
  - New Field: GraftStenosis
    Old Field: GVStenosis
  - New Field: SymptomDate24
    Old Field: SymptomDate
  - New Field: SymptomTime24
    Old Field: SymptomTime
  - New Field: NonInvasTestType
    Old Field: NonInvasiveTestType
  - New Field: POCTropAssay
    Old Field: TropAssayURL
  - New Field: PtType
    Old Field: PatientType
  - New Field: StentTypeFV
    Old Field: StentType
  - New Field: PrimPCINotPerm
    Old Field: ReasonPCINotPerf
  - New Field: EpisodeKey
    Old Field: EpisodeId
  - New Field: LOC_FMCDC
    Old Field: LocFMCDC
  - New Field: MBI
    Old Field: MedicareBeneficiaryId
  - New Field: ComMeasDateTime
    Old Field: DCComfortDatetime
value_mapping:
- Field: diagcorangiofirst
  Value Map:
    No - Pt. Reason: No - Patient Reason
- Field: noninvasperformed
  Value Map:
    No - Pt. Reason: No - Patient Reason
- Field: noninvastestmeth
  Value Map:
    'Rest ': Rest
    'Stress ': Stress
- Field: tobaccouse
  Value Map:
    Smoker - Current Status Unknown: Smoker, current status unknown
pivot_sections:
  - DCMEDS
  - HOSPEVENTS
  - ARVLMEDS
  - HOMEMEDS
  - PCIPROCMEDS
