client_fields:
  - contributionmargin
  - profitloss
  - indirectcost
  - netpatientrevenue
  - directcost
  - expectedpayment
  - secondarypayorplan
  - secondarypayorcode
  - primarypayorplan
  - secondarypayorplancode
  - secondarypayor
  - primarypayor
  - primarypayorcode
  - primarypayorplancode
  - prinicd10proccode
  - principalproceduredate
  - financialpayorclass
  - principaldiagnosispoa
  - prindx10code
  - drgcode
  - edmdspecialty
  - edmdname
  - edmdid
  - principalproceduremdname
  - attendingmdspecialty
  - principalproceduremdspecialty
  - principalproceduremdid
  - attendingmdname
  - attendingmdid
  - referringmdspecialty
  - referringmdname
  - referringmdid
  - patientsubtype
  - dischargeunit
  - los
  - dischargestatus
  - icudays
  - dischargetime
  - dischargedate
  - admitdxcode
  - admittime
  - admitdate
  - admitsource
  - admittype
  - patienttype
  - inoutcode
  - patientzip
  - gender
  - dob
  - encounternumber
  - medrecn
  - encounterfacilityname
  - encounterfacilityname
  - primarypayorplan
rename_fields:
  - New Field: principaldiagnosispoa
    Old Field: principlediagnosisicd9poa
  - New Field: referringmdspecialty
    Old Field: referringmdrole
  - New Field: referringmdname
    Old Field: refferingphyname
  - New Field: dischargetime
    Old Field: dctime
  - New Field: admitdxcode
    Old Field: admitdx9code
  - New Field: patientzip
    Old Field: patzip
  - New Field: encounterfacilityname
    Old Field: hospname
  - New Field: primarypayorplan
    Old Field: primaryinsurancegroup
required_fields:
- admitdate
- admitdx9code
- admitsource
- admittime
- admittype
- attendingmdname
- dctime
- directcost
- dischargedate
- dischargestatus
- dob
- drgcode
- encounternumber
- expectedpayment
- financialpayorclass
- gender
- hospname
- icudays
- indirectcost
- inoutcode
- los
- medrecn
- netpatientrevenue
- patienttype
- patzip
- primaryinsurancegroup
- primarypayor
- prindx10code
- prinicd10proccode
- referringmdrole
- refferingphyname
- secondarypayor