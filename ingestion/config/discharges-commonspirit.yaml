client_fields:
  - primary payor code
  - prin proc date
  - prin proc icd-10-pcs name
  - prin proc icd-10-pcs code
  - prin proc icd-9-cm name
  - prin proc icd-9-cm code
  - icd-10-cm prin diag name
  - icd-10-cm prin diag code
  - icd-9-cm prin diag name
  - icd-9-cm prin diag code
  - drg name
  - drg code
  - discharge status name
  - discharge status code
  - icu days
  - days
  - discharge time
  - discharge date
  - check-in time
  - check-in date
  - check-in source name
  - check-in source code
  - cases
  - emergent flag
  - ed cases
  - pat home zip
  - patient type code
  - group ja_patient type ip/op
  - patient sex
  - patient date of birth
  - patient account number
  - 'med rec #'
  - entity name
  - entity code
  - primary payor name
  - secondary payor code
  - secondary payor name
  - rca sipg name (rca payor groups)
  - chw payor category
  - charges
  - chw netrev
  - total payments
  - total cost
  - variable cost
  - fixed cost
  - total direct
  - total indirect
  - sec icd-9-cm diag code
  - sec icd-9-cm diag name
  - sec icd-10-cm diag code
  - sec icd-10-cm diag name
  - sec icd-9-cm procedure code
  - sec icd-9-cm procedure name
  - sec icd-10-pcs procedure code
  - sec icd-10-pcs procedure name
  - prin proc cpt4/hcpcs code
  - prin proc cpt4/hcpcs name
  - trauma cases
  - procedure flag
  - sec  procedure date
  - rca sipg name (rca payor groups)
  - chw payor category
  - net revenue
  - total payments
  - entity code
  - sec icd-10-cm diag code
  - ' procedure flag '
  - ' trauma cases '
  - encounter level - total actual payments
  - 2nd px service date
  - 2nd icd10 px description
  - 2nd icd10 px code
  - costs - indirect cost
  - costs - direct cost
  - costs - fixed cost
  - costs - variable cost
  - 2nd icd10 dx code
  - 2nd icd10 dx description
  - primary cpt - cpt code
  - ' ed cases '
  - primary cpt - cpt description
  - 'unnamed: 52'
  - 'unnamed: 53'
  - 'unnamed: 54'
  - 'unnamed: 55'
  - 'unnamed: 56'
  - 'unnamed: 57'
  - ms drg code
  - patient type ip/op
  - ' cases '
  - discharge status - discharge status code
  - patient home zip code
  - ' icu days '
  - discharge status - discharge status description
  - ms drg name
  - ' emergent flag '
  - encounter level - total charges
  - icd10 dx primary - icd10 dx description
  - icd10 dx primary - icd10 dx code
  - icd10 px primary - icd10 px code
  - icd10 px primary - icd10 px description
  - service date
  - insurance plan 1 - description
  - insurance plan 1 - code
  - insurance plan 2 - code
  - insurance plan 2 - description
  - rca sipg name
  - encounter level - net revenue
rename_fields:
  - New Field: primary payor code
    Old Field: primarypayorcode
  - New Field: prin proc date
    Old Field: icd9princprocdate
  - New Field: prin proc icd-10-pcs name
    Old Field: prinicd10procname
  - New Field: prin proc icd-10-pcs code
    Old Field: prinicd10proccode
  - New Field: prin proc icd-9-cm name
    Old Field: prinicd9procname
  - New Field: prin proc icd-9-cm code
    Old Field: prinicd9proccode
  - New Field: icd-10-cm prin diag name
    Old Field: prindx10name
  - New Field: icd-10-cm prin diag code
    Old Field: prindx10code
  - New Field: icd-9-cm prin diag name
    Old Field: prindx9name
  - New Field: icd-9-cm prin diag code
    Old Field: prindx9code
  - New Field: drg name
    Old Field: drgname
  - New Field: drg code
    Old Field: drgcode
  - New Field: discharge status name
    Old Field: dischargestatus
  - New Field: discharge status code
    Old Field: dischargestatuscode
  - New Field: icu days
    Old Field: icudays
  - New Field: days
    Old Field: los
  - New Field: discharge time
    Old Field: dctime
  - New Field: discharge date
    Old Field: dischargedate
  - New Field: check-in time
    Old Field: admittime
  - New Field: check-in date
    Old Field: admitdate
  - New Field: check-in source name
    Old Field: admitsource
  - New Field: check-in source code
    Old Field: admitsourcecode
  - New Field: emergent flag
    Old Field: emergentflag
  - New Field: ed cases
    Old Field: edcases
  - New Field: pat home zip
    Old Field: patzip
  - New Field: patient type code
    Old Field: patienttypecode
  - New Field: group ja_patient type ip/op
    Old Field: patienttype
  - New Field: patient sex
    Old Field: gender
  - New Field: patient date of birth
    Old Field: dob
  - New Field: patient account number
    Old Field: encounternumber
  - New Field: 'med rec #'
    Old Field: medrecn
  - New Field: entity name
    Old Field: servicesitename
  - New Field: entity code
    Old Field: servicesitecode
  - New Field: primary payor name
    Old Field: primarypayor
  - New Field: secondary payor code
    Old Field: secondarypayorcode
  - New Field: secondary payor name
    Old Field: secondarypayor
  - New Field: rca sipg name (rca payor groups)
    Old Field: rcasipgnamercapayorgroups
  - New Field: chw payor category
    Old Field: chwpayorcategory
  - New Field: chw netrev
    Old Field: netpatientrevenue
  - New Field: total payments
    Old Field: expectedpayment
  - New Field: total cost
    Old Field: totalcost
  - New Field: variable cost
    Old Field: variablecost
  - New Field: fixed cost
    Old Field: fixedcost
  - New Field: total direct
    Old Field: directcost
  - New Field: total indirect
    Old Field: indirectcost
  - New Field: sec icd-9-cm diag code
    Old Field: secicd9cmdiagcode
  - New Field: sec icd-9-cm diag name
    Old Field: secicd9cmdiagname
  - New Field: sec icd-10-cm diag code
    Old Field: secicd10cmdiagcode
  - New Field: sec icd-10-cm diag name
    Old Field: secicd10cmdiagname
  - New Field: sec icd-9-cm procedure code
    Old Field: secicd9cmprocedurecode
  - New Field: sec icd-9-cm procedure name
    Old Field: secicd9cmprocedurename
  - New Field: sec icd-10-pcs procedure code
    Old Field: secicd10pcsprocedurecode
  - New Field: sec icd-10-pcs procedure name
    Old Field: secicd10pcsprocedurename
  - New Field: prin proc cpt4/hcpcs code
    Old Field: prinproccpt4hcpcscode
  - New Field: prin proc cpt4/hcpcs name
    Old Field: prinproccpt4hcpcsname
  - New Field: trauma cases
    Old Field: traumacases
  - New Field: procedure flag
    Old Field: procedureflag
  - New Field: sec  procedure date
    Old Field: secproceduredate
  - New Field: rca sipg name (rca payor groups)
    Old Field: primarypayor_1
  - New Field: chw payor category
    Old Field: financialpayorclass
  - New Field: net revenue
    Old Field: netpatientrevenue
  - New Field: total payments
    Old Field: totalpayments
  - New Field: entity code
    Old Field: entitycode
  - New Field: sec icd-10-cm diag code
    Old Field: admitdx10code
  - New Field: ' procedure flag '
    Old Field: procedureflag
  - New Field: ' trauma cases '
    Old Field: traumacases
  - New Field: encounter level - total actual payments
    Old Field: encounterleveltotalactualpayments
  - New Field: 2nd px service date
    Old Field: 2ndpxservicedate
  - New Field: 2nd icd10 px description
    Old Field: 2ndicd10pxdescription
  - New Field: 2nd icd10 px code
    Old Field: 2ndicd10pxcode
  - New Field: costs - indirect cost
    Old Field: indirectcost
  - New Field: costs - direct cost
    Old Field: directcost
  - New Field: costs - fixed cost
    Old Field: costsfixedcost
  - New Field: costs - variable cost
    Old Field: costsvariablecost
  - New Field: 2nd icd10 dx code
    Old Field: 2ndicd10dxcode
  - New Field: 2nd icd10 dx description
    Old Field: 2ndicd10dxdescription
  - New Field: primary cpt - cpt code
    Old Field: primarycptcptcode
  - New Field: ' ed cases '
    Old Field: edcases
  - New Field: primary cpt - cpt description
    Old Field: primarycptcptdescription
  - New Field: 'unnamed: 52'
    Old Field: unnamed52
  - New Field: 'unnamed: 53'
    Old Field: unnamed53
  - New Field: 'unnamed: 54'
    Old Field: unnamed54
  - New Field: 'unnamed: 55'
    Old Field: unnamed55
  - New Field: 'unnamed: 56'
    Old Field: unnamed56
  - New Field: 'unnamed: 57'
    Old Field: unnamed57
  - New Field: ms drg code
    Old Field: drgcode
  - New Field: patient type ip/op
    Old Field: patienttype
  - New Field: ' cases '
    Old Field: cases
  - New Field: discharge status - discharge status code
    Old Field: dischargestatuscode
  - New Field: patient home zip code
    Old Field: patzip
  - New Field: ' icu days '
    Old Field: icudays
  - New Field: discharge status - discharge status description
    Old Field: dischargestatus
  - New Field: ms drg name
    Old Field: drgname
  - New Field: ' emergent flag '
    Old Field: emergentflag
  - New Field: encounter level - total charges
    Old Field: encounterleveltotalcharges
  - New Field: icd10 dx primary - icd10 dx description
    Old Field: icd10dxprimaryicd10dxdescription
  - New Field: icd10 dx primary - icd10 dx code
    Old Field: prindx10code
  - New Field: icd10 px primary - icd10 px code
    Old Field: prinicd10proccode
  - New Field: icd10 px primary - icd10 px description
    Old Field: prinicd10procname
  - New Field: service date
    Old Field: servicedate
  - New Field: insurance plan 1 - description
    Old Field: primaryinsurancegroup
  - New Field: insurance plan 1 - code
    Old Field: primaryinsurancegroupcode
  - New Field: insurance plan 2 - code
    Old Field: insuranceplan2code
  - New Field: insurance plan 2 - description
    Old Field: secondaryinsurancegroup
  - New Field: rca sipg name
    Old Field: rcasipgname
  - New Field: encounter level - net revenue
    Old Field: netpatientrevenue
required_fields:
  - admitdate
  - admitsource
  - admittime
  - dctime
  - directcost
  - dischargedate
  - dischargestatus
  - dob
  - drgcode
  - encounternumber
  - financialpayorclass
  - gender
  - icudays
  - indirectcost
  - los
  - medrecn
  - netpatientrevenue
  - patienttype
  - patzip
  - primaryinsurancegroup
  - prindx10code
  - prinicd10proccode
  - servicesitename
  - totalcost