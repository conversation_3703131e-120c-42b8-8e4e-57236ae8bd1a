version: '2.3'
biome_schema:
  - Dtype: bigint
    Field: DEMOGRAPHICROWID
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: HospName
    PHI: false
    Role: HOSPITAL
  - Dtype: varchar(30)
    Field: NCDRPatientId
    PHI: true
    Role: IDENTIFIER
  - Dtype: text
    Field: EpisodeId
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: OtherId
    PHI: true
    Role: MEDRECNUM
  - Dtype: varchar(9)
    Field: PatZip
    PHI: true
    Role: PATIENT_ZIP
  - Dtype: text
    Field: FirstName
    PHI: true
    Role: NAME
  - Dtype: text
    Field: MidName
    PHI: true
    Role: NAME
  - Dtype: text
    Field: LastName
    PHI: true
    Role: NAME
  - Dtype: varchar(30)
    Field: Gender
    PHI: true
    Role: GENDER
  - Dtype: varchar(69)
    Field: DOB
    PHI: true
    Role: DOB
  - Dtype: varchar(53)
    Field: RaceWhite
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: RaceBlack
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: RaceAMIndian
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: RaceAsianIndian
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: RaceChinese
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: RaceFilipino
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: RaceJapanese
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: RaceKorean
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: RaceVietnamese
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: RaceOther
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: RaceNatHawPasIslander
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: RaceNatHaw
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: RaceGuamChamo
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: RaceSamoan
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: RaceOtherIsland
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: RaceAsian
    PHI: false
    Role: null
  - Dtype: varchar(53)
    Field: HispOrig
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: RaceMexicanAmChicano
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: RacePuertoRican
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: RaceCuban
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: RaceOtherHispanic
    PHI: false
    Role: null
  - Dtype: bigint
    Field: ENCCOMBINEDROWID
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: EPISODEUID
    PHI: false
    Role: null
  - Dtype: varchar(69)
    Field: ArrivalDate
    PHI: true
    Role: ADMISSION_DATE
  - Dtype: varchar(69)
    Field: DischargeDate
    PHI: true
    Role: ANCHOR_DATE
  - Dtype: varchar(52)
    Field: CABG
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: CABGDate
    PHI: true
    Role: DATE
  - Dtype: varchar(52)
    Field: PCI
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PCIDate
    PHI: true
    Role: DATE
  - Dtype: varchar(255)
    Field: ReasonForAdmit
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: HealthInsurance
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: InsMedicare
    PHI: false
    Role: null
  - Dtype: varchar(53)
    Field: InsMedicaid
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: InsPrivate
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: InsMilitary
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: InsState
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: InsIHS
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: InsNonUS
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: EnrolledStudy
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: StudyName
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: PatientRestriction
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: HIC
    PHI: false
    Role: null
  - Dtype: varchar(23)
    Field: MBI
    PHI: false
    Role: null
  - Dtype: varchar(55)
    Field: DischargeStatus
    PHI: false
    Role: null
  - Dtype: varchar(200)
    Field: DischargeLocation
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: DISCHMEDASA
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: Warfarin
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Rivaroxaban
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Apixaban
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Dabigatran
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Edoxaban
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: BetaBlocker
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: ACE
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: AntiarrhythmicAgents
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: Statin
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: AntiplateletAgents
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: AldosteroneAntagonist
    PHI: false
    Role: null
  - Dtype: varchar(45)
    Field: ARB
    PHI: false
    Role: null
  - Dtype: varchar(53)
    Field: CoronaryAngiography
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: PriorPCI
    PHI: false
    Role: null
  - Dtype: varchar(69)
    Field: PriorPCIDate
    PHI: true
    Role: DATE
  - Dtype: varchar(52)
    Field: PriorCABG
    PHI: false
    Role: null
  - Dtype: varchar(69)
    Field: PriorCABGDate
    PHI: true
    Role: DATE
  - Dtype: varchar(53)
    Field: EPStudy
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: EPStudyDate
    PHI: true
    Role: DATE
  - Dtype: varchar(53)
    Field: ECG
    PHI: false
    Role: null
  - Dtype: varchar(69)
    Field: ECGDate
    PHI: true
    Role: DATE
  - Dtype: varchar(52)
    Field: HgbNd
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: Hgb
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: SodiumND
    PHI: false
    Role: null
  - Dtype: int
    Field: Sodium
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: BUNND
    PHI: false
    Role: null
  - Dtype: int
    Field: BUN
    PHI: false
    Role: null
  - Dtype: varchar(53)
    Field: ECGNormal
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: VPQRS
    PHI: false
    Role: null
  - Dtype: int
    Field: NVPQRS
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: AbConduction
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: AbConductionType
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: AbConductionLBBB
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: AtrialRhythm
    PHI: false
    Role: null
  - Dtype: varchar(53)
    Field: AtrialRhythmSinusnode
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: Vpace
    PHI: false
    Role: null
  - Dtype: varchar(53)
    Field: HF
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: NYHA
    PHI: false
    Role: null
  - Dtype: varchar(53)
    Field: LVEFAssessed
    PHI: false
    Role: null
  - Dtype: varchar(69)
    Field: MstRecLVEFDate
    PHI: true
    Role: DATE
  - Dtype: int
    Field: LVEF
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: SyndromeRiskDeath
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: FamHxSDeath
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: FamilialHxNICM
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: IschCardio
    PHI: false
    Role: null
  - Dtype: varchar(53)
    Field: NIDCM
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: NIDCMTimeframe
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: NIDCMMxDose
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: OnInotSupport
    PHI: false
    Role: null
  - Dtype: varchar(53)
    Field: CardiacArrest
    PHI: false
    Role: null
  - Dtype: varchar(69)
    Field: CardiacArrestDate
    PHI: true
    Role: DATE
  - Dtype: varchar(52)
    Field: VTArrest
    PHI: false
    Role: null
  - Dtype: varchar(53)
    Field: VFibArrest
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: VFib
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: VFIBDATEBRADYCARDIAARREST
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: VT
    PHI: false
    Role: null
  - Dtype: varchar(69)
    Field: VTDate
    PHI: true
    Role: DATE
  - Dtype: varchar(52)
    Field: PriorMI
    PHI: false
    Role: null
  - Dtype: varchar(69)
    Field: PriorMIDate
    PHI: true
    Role: DATE
  - Dtype: varchar(52)
    Field: Syncope
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: CAD
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: CurrentVAD
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: VADCandidate
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: PriorCIED
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: INDICATIONSFORPERMANENTPACEMAKER
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: TransplantWaitList
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: TransplantCandidate
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: CURRENTLYONLVAD
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: ParoxysmalSVTHistory
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: CANDIDATEFORLVAD
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: AFibFlutter
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: AFibFlutterClass
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: AFibFlutterCardioPlans
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: PrimaryValvularHD
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: OtherStructAbn
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: StructAbnType
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: StructAbnTypeCH
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: PriorCVD
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: Diabetes
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: CurrentDialysis
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: ChronicLungDisease
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: SyndromeRiskType
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: GuidDireMedTherMaxDose
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: IschCardiotimeframe
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: BradyArrest
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: VTType
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: PostCardiacSurgery
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: BradycardiaDependent
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: ReversibleCause
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: HemoInstability
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: VFibDate
    PHI: true
    Role: DATE
  - Dtype: varchar(50)
    Field: PerfAfterRecentCA
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: ResultsOfAngiography
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: RevascularPerformed
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: RevascularizationOutcome
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: PriorPCICardioPresent
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: PriorPCIElective
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: PriorAVProc
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: AVPDate
    PHI: true
    Role: DATE
  - Dtype: varchar(50)
    Field: PriorCABGCardioPresent
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: AVPElective
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: PriorCABGElective
    PHI: false
    Role: null
  - Dtype: int
    Field: VPacedQRS
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: AtrialRhythmAfib
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: DeathCause
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: DischMedARNi
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: DischMedReninInhibitor
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: DischMedSelectiveSinusNodeIfChannelInhibitor
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: AbConductionDelayNS
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: AtrialRhythmAP
    PHI: false
    Role: null
  - Dtype: varchar(1000)
    Field: ReasonforReImplantation
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: AbConductionRBBB
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: AtrialRhythmAflutter
    PHI: false
    Role: null
  - Dtype: bigint
    Field: PROCEDURESESSIONROWID
    PHI: false
    Role: null
  - Dtype: varchar(66)
    Field: ProcedureType
    PHI: false
    Role: null
  - Dtype: varchar(69)
    Field: ProcedureDate
    PHI: true
    Role: PROCEDURE_DATE
  - Dtype: varchar(69)
    Field: ProcedureEndDate
    PHI: true
    Role: DATE
  - Dtype: int
    Field: GenOpNPI
    PHI: false
    Role: null
  - Dtype: varchar(61)
    Field: GenOpFName
    PHI: false
    Role: null
  - Dtype: varchar(59)
    Field: GenOpLName
    PHI: false
    Role: null
  - Dtype: varchar(54)
    Field: GenOpMName
    PHI: false
    Role: null
  - Dtype: varchar(53)
    Field: DeviceImplanted
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: FinalDeviceType
    PHI: false
    Role: null
  - Dtype: varchar(80)
    Field: CSLVLead
    PHI: false
    Role: null
  - Dtype: varchar(60)
    Field: ICDImpSerNo
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: ICDImpID
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: ICDImpModelName
    PHI: false
    Role: null
  - Dtype: int
    Field: LeadOpNPI
    PHI: false
    Role: null
  - Dtype: varchar(61)
    Field: LeadOpFName
    PHI: false
    Role: null
  - Dtype: varchar(59)
    Field: LeadOpLName
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: LeadOpMName
    PHI: false
    Role: null
  - Dtype: varchar(70)
    Field: ICDIndication
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: ClinicalTrial
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: CArrest
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: PostMI
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: CardiacPerf
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: CVDissect
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: Tamponade
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: Stroke
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: PostTIA
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: Hematoma
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: InfectionReqAnti
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: Hemothorax
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: Pneumothorax
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: UrgentSurgery
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: SetScrew
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: LeadDislodge
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: SDMProc
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: SDMTool
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: SDMToolName
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: HisLBundleLead
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: BradIndPres
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: PrimBradIndPres
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: PrimTachIndPres
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: ReasonPacingIndicated
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: ReasonPacIndicSickSinusSyndrome
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: PriPacMode
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: ReImpBattery
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: DeviceExplant
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: ExplantDate
    PHI: true
    Role: DATE
  - Dtype: varchar(100)
    Field: ExplantTreatRecommend
    PHI: false
    Role: null
  - Dtype: varchar(90)
    Field: ExplantDeviceName
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: ICDExpID
    PHI: false
    Role: null
  - Dtype: varchar(61)
    Field: ICDExpSerNo
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: ReasonPacIndicCompleteHeartBlock
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: ReImpUpgrade
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: ReasonPacIndicAnticipatedRequirementOfGt40pRVPacing
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: ReImpLeadRev
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: ReasonPacIndicChronotropicIncompetence
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: ReasonPacIndicHFUnresponsiveToGDMT
    PHI: false
    Role: null
  - Dtype: bigint
    Field: LEADROWID
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: LeadSerNo
    PHI: false
    Role: null
  - Dtype: varchar(60)
    Field: LeadType
    PHI: false
    Role: null
  - Dtype: varchar(64)
    Field: LeadLocation
    PHI: false
    Role: null
  - Dtype: varchar(69)
    Field: ExLeadDate
    PHI: true
    Role: DATE
  - Dtype: varchar(60)
    Field: ExLeadStat
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: LeadID
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: LeadModelName
    PHI: false
    Role: null
  - Dtype: int
    Field: LeadCounter
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: DataVrsn
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: ProcedureTime
    PHI: false
    Role: PROCEDURE_TIME
  - Dtype: varchar(30)
    Field: OriginalOtherId
    PHI: true
    Role: MEDRECNUM
  - Dtype: varchar(68)
    Field: FileName
    PHI: false
    Role: null
  - Dtype: varchar(22)
    Field: Version
    PHI: false
    Role: null
  - Dtype: varchar(16)
    Field: DatasetName
    PHI: false
    Role: null
  - Dtype: int
    Field: ClientFileId
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: BiomeImportDT
    PHI: false
    Role: null
  - Dtype: varchar(34)
    Field: CareEntityId
    PHI: false
    Role: CAREENTITY
  - Dtype: varchar(34)
    Field: TenantID
    PHI: false
    Role: TENANT
  - Dtype: int
    Field: YearMonth
    PHI: false
    Role: ANCHOR_YEAR_MONTH
  - Dtype: int
    Field: CaseSequenceNumber
    PHI: false
    Role: CASE_NUMBER
  - Dtype: decimal(6,3)
    Field: Age
    PHI: true
    Role: AGE
  - Dtype: decimal(8,2)
    Field: DistFromHospital
    PHI: false
    Role: DISTANCE_FROM_HOSPITAL
  - Dtype: varchar(20)
    Field: MIB
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: ReasonPacIndicMobitzTypeII
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: ReasonPacIndic21AVBlock
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: ReasonPacIndicAtrioventricularNodeAblation
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: PatZipNA
    PHI: false
    Role: null
  - Dtype: varchar(66)
    Field: ICDImpMfr
    PHI: false
    Role: null
  - Dtype: varchar(70)
    Field: ICDExpMfr
    PHI: false
    Role: null
  - Dtype: varchar(68)
    Field: PartName
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: ParticID
    PHI: false
    Role: null
  - Dtype: varchar(57)
    Field: VendorID
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: StructAbnType_ARVC
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: StructAbnType_HCM
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: StructAbnType_Infiltrative
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: StructAbnType_LVC
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: RStudyPatientID
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: Class1Class2Brady
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: ReImpInfection
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: ReImpRecall
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: ReImpFaulty
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: ReImpRelocation
    PHI: false
    Role: null
  - Dtype: varchar(53)
    Field: ReImpMalfx
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: LeadDislodgeLoc
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: MineralocorticoidReceptorAntagonist
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: DischMedCode
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: DischMedPrescribed
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: ICDImpUDI
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: LeadIdentification3
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: LeadIdentification2
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: LeadID2
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: LeadID1
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: LeadUDI3
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: LeadSerNo3
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: LeadSerNo2
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: LeadUDI
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: LeadUDI2
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: LeadID3
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: ExLeadDate2
    PHI: true
    Role: DATE
  - Dtype: varchar(50)
    Field: ExLeadStat2
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: DeathProcedure
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: ProcedureEndTime
    PHI: false
    Role: TIME
  - Dtype: varchar(50)
    Field: AtrialRhythmAtrialtach
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: EPStudyDateUnk
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: VentArrythInduced
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: AbConductionRBBBandLBBB
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: AtrialRhythmUndocdatrial
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: AtrialRhythmSinusarrest
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_Aspirin_any
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: PartNPI
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: Timeframe
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: TransmissionNumber
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: StructAbnType_OtherStructAbn
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: RaceAsianOther
    PHI: false
    Role: null
  - Dtype: bigint unsigned
    Field: BiomeEncounterId
    PHI: false
    Role: null
custom_fields:
  - Delimiter: null
    Derivative Field: IntraVentConductionType
    Derivative Field Code: 100001142
    Derivative Value: Left bundle branch block
    Derivative Value Code: 164909002
    Exact Match: 0
    Field: AbConductionLBBB
    Reference Field: AbConduction
    Reference Field Code: 4554005
    Section: EPISODEOFCARE
    Value Map: null
  - Delimiter: null
    Derivative Field: IntraVentConductionType
    Derivative Field Code: 100001142
    Derivative Value: Right bundle branch block
    Derivative Value Code: 164907000
    Exact Match: 0
    Field: AbConductionRBBB
    Reference Field: AbConduction
    Reference Field Code: 4554005
    Section: EPISODEOFCARE
    Value Map: null
  - Delimiter: null
    Derivative Field: IntraVentConductionType
    Derivative Field Code: 100001142
    Derivative Value: Delay, Non-specific
    Derivative Value Code: 698252002
    Exact Match: 0
    Field: AbConductionDelayNS
    Reference Field: AbConduction
    Reference Field Code: 4554005
    Section: EPISODEOFCARE
    Value Map: null
  - Delimiter: null
    Derivative Field: IntraVentConductionType
    Derivative Field Code: 100001142
    Derivative Value: Alternating RBBB and LBBB
    Derivative Value Code: 32758004
    Exact Match: 0
    Field: AbConductionRBBBandLBBB
    Reference Field: AbConduction
    Reference Field Code: 4554005
    Section: EPISODEOFCARE
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin Converting Enzyme Inhibitor
    Derivative Value Code: 41549009
    Exact Match: 1
    Field: ace
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aldosterone Antagonist
    Derivative Value Code: 372603003
    Exact Match: 1
    Field: AldosteroneAntagonist
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin Receptor-Neprilysin Inhibitor
    Derivative Value Code: 112000001832
    Exact Match: 1
    Field: DischMedARNi
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Antiarrhythmic Drug
    Derivative Value Code: 67507000
    Exact Match: 1
    Field: AntiarrhythmicAgents
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Warfarin
    Derivative Value Code: 11289
    Exact Match: 1
    Field: Warfarin
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Antiplatelet agent
    Derivative Value Code: 372560006
    Exact Match: 1
    Field: AntiplateletAgents
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aspirin
    Derivative Value Code: 1191
    Exact Match: 1
    Field: Discharge_Aspirin_any
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aspirin
    Derivative Value Code: 1191
    Exact Match: 1
    Field: DischMedASA
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin II Receptor Blocker
    Derivative Value Code: 372913009
    Exact Match: 1
    Field: ARB
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Beta Blocker
    Derivative Value Code: 33252009
    Exact Match: 1
    Field: BetaBlocker
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Apixaban
    Derivative Value Code: 1364430
    Exact Match: 1
    Field: Apixaban
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Dabigatran
    Derivative Value Code: 1546356
    Exact Match: 1
    Field: Dabigatran
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Edoxaban
    Derivative Value Code: 1599538
    Exact Match: 1
    Field: Edoxaban
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Rivaroxaban
    Derivative Value Code: 1114195
    Exact Match: 1
    Field: Rivaroxaban
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Renin Inhibitor
    Derivative Value Code: 426228001
    Exact Match: 1
    Field: DischMedReninInhibitor
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Selective Sinus Node I/f Channel Inhibitor
    Derivative Value Code: 112000001831
    Exact Match: 1
    Field: DischMedSelectiveSinusNodeIfChannelInhibitor
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Statin
    Derivative Value Code: 96302009
    Exact Match: 1
    Field: Statin
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: AtrialRhythm
    Derivative Field Code: 106068003
    Derivative Value: Sinus node rhythm
    Derivative Value Code: 106067008
    Exact Match: 0
    Field: AtrialRhythmSinusnode
    Reference Field: AtrialRhythm
    Reference Field Code: 106068003
    Section: EPISODEOFCARE
    Value Map:
      Sinus node rhythm: 'Yes'
  - Delimiter: null
    Derivative Field: AtrialRhythm
    Derivative Field Code: 106068003
    Derivative Value: Atrial fibrillation
    Derivative Value Code: 49436004
    Exact Match: 0
    Field: AtrialRhythmAfib
    Reference Field: AtrialRhythm
    Reference Field Code: 106068003
    Section: EPISODEOFCARE
    Value Map:
      Atrial fibrillation: 'Yes'
  - Delimiter: null
    Derivative Field: AtrialRhythm
    Derivative Field Code: 106068003
    Derivative Value: Atrial tachycardia
    Derivative Value Code: 276796006
    Exact Match: 0
    Field: AtrialRhythmAtrialtach
    Reference Field: AtrialRhythm
    Reference Field Code: 106068003
    Section: EPISODEOFCARE
    Value Map:
      Atrial tachycardia: 'Yes'
  - Delimiter: null
    Derivative Field: AtrialRhythm
    Derivative Field Code: 106068003
    Derivative Value: Atrial flutter
    Derivative Value Code: 5370000
    Exact Match: 0
    Field: AtrialRhythmAflutter
    Reference Field: AtrialRhythm
    Reference Field Code: 106068003
    Section: EPISODEOFCARE
    Value Map:
      Atrial flutter: 'Yes'
  - Delimiter: null
    Derivative Field: AtrialRhythm
    Derivative Field Code: 106068003
    Derivative Value: Sinus arrest
    Derivative Value Code: 5609005
    Exact Match: 0
    Field: AtrialRhythmSinusarrest
    Reference Field: AtrialRhythm
    Reference Field Code: 106068003
    Section: EPISODEOFCARE
    Value Map:
      Sinus arrest: 'Yes'
  - Delimiter: null
    Derivative Field: AtrialRhythm
    Derivative Field Code: 106068003
    Derivative Value: Atrial paced
    Derivative Value Code: 251268003
    Exact Match: 0
    Field: AtrialRhythmAP
    Reference Field: AtrialRhythm
    Reference Field Code: 106068003
    Section: EPISODEOFCARE
    Value Map:
      Atrial paced: 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Private Health Insurance
    Derivative Value Code: 5
    Exact Match: 0
    Field: INSPRIVATE
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Private Health Insurance|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Medicare
    Derivative Value Code: 1
    Exact Match: 0
    Field: INSMEDICARE
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Medicare|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Medicaid
    Derivative Value Code: 2
    Exact Match: 0
    Field: INSMEDICAID
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Medicaid|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Military Health Care
    Derivative Value Code: 31
    Exact Match: 0
    Field: INSMILITARY
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Military Health Care|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: State-Specific Plan (non-Medicaid)
    Derivative Value Code: 36
    Exact Match: 0
    Field: INSSTATE
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|State-Specific Plan (non-Medicaid)|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Indian Health Service
    Derivative Value Code: 33
    Exact Match: 0
    Field: INSIHS
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Indian Health Service|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Non-US Insurance
    Derivative Value Code: 100000812
    Exact Match: 0
    Field: INSNONUS
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Non-US Insurance|': 'Yes'
  - Delimiter: null
    Derivative Field: ReasonPacIndic
    Derivative Field Code: 100001097
    Derivative Value: Sick sinus syndrome
    Derivative Value Code: 36083008
    Exact Match: 0
    Field: reasonpacindicsicksinussyndrome
    Reference Field: ReasonPacIndic
    Reference Field Code: 100001097
    Section: PROCINFO
    Value Map:
      Sick sinus syndrome: 'Yes'
  - Delimiter: null
    Derivative Field: ReasonPacIndic
    Derivative Field Code: 100001097
    Derivative Value: Complete heart block
    Derivative Value Code: 27885002
    Exact Match: 0
    Field: reasonpacindiccompleteheartblock
    Reference Field: ReasonPacIndic
    Reference Field Code: 100001097
    Section: PROCINFO
    Value Map:
      Complete heart block: 'Yes'
  - Delimiter: null
    Derivative Field: ReasonPacIndic
    Derivative Field Code: 100001097
    Derivative Value: Chronotropic incompetence
    Derivative Value Code: 427989008
    Exact Match: 0
    Field: reasonpacindicchronotropicincompetence
    Reference Field: ReasonPacIndic
    Reference Field Code: 100001097
    Section: PROCINFO
    Value Map:
      Chronotropic incompetence: 'Yes'
  - Delimiter: null
    Derivative Field: ReasonPacIndic
    Derivative Field Code: 100001097
    Derivative Value: Mobitz Type II
    Derivative Value Code: 28189009
    Exact Match: 0
    Field: reasonpacindicmobitztypeii
    Reference Field: ReasonPacIndic
    Reference Field Code: 100001097
    Section: PROCINFO
    Value Map:
      Mobitz Type II: 'Yes'
  - Delimiter: null
    Derivative Field: ReasonPacIndic
    Derivative Field Code: 100001097
    Derivative Value: 2:1 AV Block
    Derivative Value Code: 54016002
    Exact Match: 0
    Field: reasonpacindic21avblock
    Reference Field: ReasonPacIndic
    Reference Field Code: 100001097
    Section: PROCINFO
    Value Map:
      2:1 AV Block: 'Yes'
  - Delimiter: null
    Derivative Field: ReasonPacIndic
    Derivative Field Code: 100001097
    Derivative Value: Atrioventricular Node Ablation
    Derivative Value Code: 428663009
    Exact Match: 0
    Field: reasonpacindicatrioventricularnodeablation
    Reference Field: ReasonPacIndic
    Reference Field Code: 100001097
    Section: PROCINFO
    Value Map:
      Atrioventricular Node Ablation: 'Yes'
  - Delimiter: null
    Derivative Field: ReasonPacIndic
    Derivative Field Code: 100001097
    Derivative Value: HF Unresponsive to GDMT
    Derivative Value Code: 112000002017
    Exact Match: 0
    Field: reasonpacindichfunresponsivetogdmt
    Reference Field: ReasonPacIndic
    Reference Field Code: 100001097
    Section: PROCINFO
    Value Map:
      HF Unresponsive to GDMT: 'Yes'
  - Delimiter: null
    Derivative Field: ReasonPacIndic
    Derivative Field Code: 100001097
    Derivative Value: Anticipated requirement of > 40% RV pacing
    Derivative Value Code: 100000931
    Exact Match: 0
    Field: reasonpacindicanticipatedrequirementofgt40prvpacing
    Reference Field: ReasonPacIndic
    Reference Field Code: 100001097
    Section: PROCINFO
    Value Map:
      Anticipated requirement of > 40% RV pacing: 'Yes'
  - Delimiter: null
    Derivative Field: ReImplantReason
    Derivative Field Code: 100000991
    Derivative Value: Reimplant Reason - End of Battery Life
    Derivative Value Code: 100001088
    Exact Match: 0
    Field: reimpbattery
    Reference Field: ReImplantReason
    Reference Field Code: 100000991
    Section: PROCINFO
    Value Map:
      Reimplant Reason - End of Battery Life: 'Yes'
  - Delimiter: null
    Derivative Field: ReImplantReason
    Derivative Field Code: 100000991
    Derivative Value: Reimplant Reason - Replaced At Time of Lead Revision
    Derivative Value Code: 100001092
    Exact Match: 0
    Field: reimpleadrev
    Reference Field: ReImplantReason
    Reference Field Code: 100000991
    Section: PROCINFO
    Value Map:
      Reimplant Reason - Replaced At Time of Lead Revision: 'Yes'
  - Delimiter: null
    Derivative Field: ReImplantReason
    Derivative Field Code: 100000991
    Derivative Value: Reimplant Reason - Upgrade
    Derivative Value Code: 100001094
    Exact Match: 0
    Field: reimpupgrade
    Reference Field: ReImplantReason
    Reference Field Code: 100000991
    Section: PROCINFO
    Value Map:
      Reimplant Reason - Upgrade: 'Yes'
  - Delimiter: null
    Derivative Field: ReImplantReason
    Derivative Field Code: 100000991
    Derivative Value: Reimplant Reason - Infection
    Derivative Value Code: 100001091
    Exact Match: 0
    Field: reimpinfection
    Reference Field: ReImplantReason
    Reference Field Code: 100000991
    Section: PROCINFO
    Value Map:
      Reimplant Reason - Infection: 'Yes'
  - Delimiter: null
    Derivative Field: ReImplantReason
    Derivative Field Code: 100000991
    Derivative Value: Reimplant Reason - Under Manufacturer Advisory/Recall
    Derivative Value Code: 100001093
    Exact Match: 0
    Field: reimprecall
    Reference Field: ReImplantReason
    Reference Field Code: 100000991
    Section: PROCINFO
    Value Map:
      Reimplant Reason - Under Manufacturer Advisory/Recall: 'Yes'
  - Delimiter: null
    Derivative Field: ReImplantReason
    Derivative Field Code: 100000991
    Derivative Value: Reimplant Reason - Faulty Connector/Header
    Derivative Value Code: 100001089
    Exact Match: 0
    Field: reimpfaulty
    Reference Field: ReImplantReason
    Reference Field Code: 100000991
    Section: PROCINFO
    Value Map:
      Reimplant Reason - Faulty Connector/Header: 'Yes'
  - Delimiter: null
    Derivative Field: ReImplantReason
    Derivative Field Code: 100000991
    Derivative Value: Reimplant Reason - Device Relocation
    Derivative Value Code: 100001087
    Exact Match: 0
    Field: reimprelocation
    Reference Field: ReImplantReason
    Reference Field Code: 100000991
    Section: PROCINFO
    Value Map:
      Reimplant Reason - Device Relocation: 'Yes'
  - Delimiter: null
    Derivative Field: ReImplantReason
    Derivative Field Code: 100000991
    Derivative Value: Reimplant Reason - Generator Malfunction
    Derivative Value Code: 100001090
    Exact Match: 0
    Field: reimpmalfx
    Reference Field: ReImplantReason
    Reference Field Code: 100000991
    Section: PROCINFO
    Value Map:
      Reimplant Reason - Generator Malfunction: 'Yes'
  - Delimiter: null
    Derivative Field: StructAbnType
    Derivative Field Code: 100000949
    Derivative Value: LV structural abnormality associated with risk for sudden cardiac
      arrest
    Derivative Value Code: 87878005
    Exact Match: 0
    Field: structabntype_lvc
    Reference Field: OtherStructAbn
    Reference Field Code: 100000949
    Section: EPISODEOFCARE
    Value Map: null
  - Delimiter: null
    Derivative Field: StructAbnType
    Derivative Field Code: 100000949
    Derivative Value: Hypertrophic cardiomyopathy (HCM) with high risk features
    Derivative Value Code: 233873004
    Exact Match: 0
    Field: structabntype_hcm
    Reference Field: OtherStructAbn
    Reference Field Code: 100000949
    Section: EPISODEOFCARE
    Value Map: null
  - Delimiter: null
    Derivative Field: StructAbnType
    Derivative Field Code: 100000949
    Derivative Value: Infiltrative
    Derivative Value Code: 100001018
    Exact Match: 0
    Field: structabntype_infiltrative
    Reference Field: OtherStructAbn
    Reference Field Code: 100000949
    Section: EPISODEOFCARE
    Value Map: null
  - Delimiter: null
    Derivative Field: StructAbnType
    Derivative Field Code: 100000949
    Derivative Value: Arrhythmogenic right ventricular cardiomyopathy (ARVC)
    Derivative Value Code: 281170005
    Exact Match: 0
    Field: structabntype_arvc
    Reference Field: OtherStructAbn
    Reference Field Code: 100000949
    Section: EPISODEOFCARE
    Value Map: null
  - Delimiter: null
    Derivative Field: StructAbnType
    Derivative Field Code: 100000949
    Derivative Value: Congenital heart disease associated with sudden cardiac arrest
    Derivative Value Code: 13213009
    Exact Match: 0
    Field: structabntypech
    Reference Field: OtherStructAbn
    Reference Field Code: 100000949
    Section: EPISODEOFCARE
    Value Map: null
rename_fields:
  - New Field: DCDate
    Old Field: DischargeDate
  - New Field: Sex
    Old Field: Gender
  - New Field: HispEthnicityMexican
    Old Field: racemexicanamchicano
  - New Field: IntraVentConductionType
    Old Field: AbConductionType
  - New Field: AFib
    Old Field: afibflutter
  - New Field: AFibClass
    Old Field: afibflutterclass
  - New Field: AVP_Date
    Old Field: AVPDate
  - New Field: AVP_Elective
    Old Field: AVPElective
  - New Field: BradyDependent
    Old Field: BradycardiaDependent
  - New Field: CoronaryAngio
    Old Field: CoronaryAngiography
  - New Field: CurrentlyonVAD
    Old Field: currentvad
  - New Field: DCLocation
    Old Field: dischargelocation
  - New Field: ExplantTreatment
    Old Field: explanttreatrecommend
  - New Field: FamilialSyndSuddenDeath
    Old Field: famhxsdeath
  - New Field: ISCMGDMTDose
    Old Field: guiddiremedthermaxdose
  - New Field: HealthIns
    Old Field: HealthInsurance
  - New Field: ISCM
    Old Field: ischcardio
  - New Field: ISCMTimeframe
    Old Field: ischcardiotimeframe
  - New Field: PriorLVEF
    Old Field: LVEF
  - New Field: PriorLVEFAssessed
    Old Field: LVEFAssessed
  - New Field: PriorLVEFDate
    Old Field: mstreclvefdate
  - New Field: NICM
    Old Field: nidcm
  - New Field: NICMGDMTDose
    Old Field: nidcmmxdose
  - New Field: NICMTimeframe
    Old Field: nidcmtimeframe
  - New Field: InotropicSupport
    Old Field: oninotsupport
  - New Field: ParoxySVTHistory
    Old Field: paroxysmalsvthistory
  - New Field: PtRestriction
    Old Field: patientrestriction
  - New Field: ZipCode
    Old Field: patzip
  - New Field: ZipCodeNA
    Old Field: patzipna
  - New Field: VTPostCardiacSurgery
    Old Field: postcardiacsurgery
  - New Field: Prior_AVProc
    Old Field: prioravproc
  - New Field: ProcedureStartDateTime
    Old Field: proceduredate
  - New Field: ProcedureEndDateTime
    Old Field: procedureenddate
  - New Field: HispEthnicityCuban
    Old Field: racecuban
  - New Field: RaceGuamChamorro
    Old Field: raceguamchamo
  - New Field: RaceNatHaw
    Old Field: racenathawpasislander
  - New Field: RaceAsianOther
    Old Field: RaceOther
  - New Field: HispEthnicityOtherOrigin
    Old Field: raceotherhispanic
  - New Field: RacePacificIslandOther
    Old Field: raceotherisland
  - New Field: HispEthnicityPuertoRico
    Old Field: racepuertorican
  - New Field: ReImplantReason
    Old Field: reasonforreimplantation
  - New Field: ReasonPacIndic
    Old Field: reasonpacingindicated
  - New Field: CoronaryAngioResults
    Old Field: resultsofangiography
  - New Field: RevascOutcome
    Old Field: revascularizationoutcome
  - New Field: RevascPerf
    Old Field: revascularperformed
  - New Field: VTReverseCause
    Old Field: reversiblecause
  - New Field: SDM_Proc
    Old Field: sdmproc
  - New Field: SDM_Tool
    Old Field: sdmtool
  - New Field: SDM_Tool_Name
    Old Field: sdmtoolname
  - New Field: CandidateforVAD
    Old Field: vadcandidate
  - New Field: VPaced
    Old Field: vpace
  - New Field: VTachArrest
    Old Field: vtarrest
  - New Field: dcstatus
    Old Field: dischargestatus
  - New Field: StudyPtID
    Old Field: RStudyPatientID
  - New Field: episodekey
    Old Field: episodeid
delimiters:
  - Enclose: true
    Field: abconductiontype
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: reasonforreimplantation
    Replace: null
    Delimiter: '|'
value_mapping:
- Field: LeadType
  Value Map:
    "New   ": New
