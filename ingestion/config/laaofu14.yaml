version: '1.4'
biome_schema:
  - Dtype: varchar(30)
    Field: NCDRPatientId
    PHI: true
    Role: IDENTIFIER
  - Dtype: varchar(20)
    Field: PartID
    PHI: false
    Role: null
  - Dtype: text
    Field: FollowupId
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: PartName
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: Timeframe
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: RegistryId
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: RegistryVer
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: OtherId
    PHI: true
    Role: MEDRECNUM
  - Dtype: varchar(30)
    Field: Gender
    PHI: true
    Role: GENDER
  - Dtype: varchar(30)
    Field: DOB
    PHI: true
    Role: DOB
  - Dtype: varchar(30)
    Field: ArrivalDate
    PHI: true
    Role: ARRIVAL_DATE
  - Dtype: varchar(30)
    Field: DischargeDate
    PHI: true
    Role: ANCHOR_DATE
  - Dtype: varchar(30)
    Field: ProcedureStartDate
    PHI: true
    Role: PROCEDURE_DATE
  - Dtype: varchar(30)
    Field: ProcedureStartTime
    PHI: false
    Role: TIME
  - Dtype: varchar(31)
    Field: AssessmentDate
    PHI: true
    Role: DATE
  - Dtype: varchar(24)
    Field: FollowupInterval
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Method_Office
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Method_MedRecord
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: Method_MedProvider
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: Method_Phone
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: Method_SSFile
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: Method_Hospital
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Method_Other
    PHI: false
    Role: null
  - Dtype: varchar(60)
    Field: DischargeStatus
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LVEFAssessed
    PHI: false
    Role: null
  - Dtype: int
    Field: LVEF
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: TTEPerf
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: TTEDate
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: F_TEEPerf
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: F_TEEDate
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: F_CardiacCTPerf
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: F_CardiacCTDate
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: F_CardiacMRIPerf
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: F_CardiacMRIDate
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: F_ICEPerformed
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: F_ICEDate
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: F_AtrialThromDetect
    PHI: false
    Role: null
  - Dtype: decimal(4,2)
    Field: ResidualLeak
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: ResidualLeakNA
    PHI: false
    Role: null
  - Dtype: decimal(4,2)
    Field: F_Creat
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: F_CREATNA
    PHI: false
    Role: null
  - Dtype: decimal(3,1)
    Field: F_Hgb
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: F_HGBNA
    PHI: false
    Role: null
  - Dtype: varchar(57)
    Field: F_RankinScale
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: F_RankinScaleNA
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: BIEPerf
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: BIEFeeding
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: BIEBathing
    PHI: false
    Role: null
  - Dtype: varchar(36)
    Field: BIEGrooming
    PHI: false
    Role: null
  - Dtype: varchar(32)
    Field: BIEDressing
    PHI: false
    Role: null
  - Dtype: varchar(27)
    Field: BIEBowels
    PHI: false
    Role: null
  - Dtype: varchar(29)
    Field: BIEBladder
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: BIEToiletUse
    PHI: false
    Role: null
  - Dtype: varchar(43)
    Field: BIETransfers
    PHI: false
    Role: null
  - Dtype: varchar(28)
    Field: BIEMobility
    PHI: false
    Role: null
  - Dtype: varchar(33)
    Field: BIEStairs
    PHI: false
    Role: null
  - Dtype: varchar(33)
    Field: WarfarinDiscontinued
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: WarfarinDiscontinuedDate
    PHI: true
    Role: DATE
  - Dtype: varchar(31)
    Field: WarfarinResumed
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: WarfarinResumedDate
    PHI: true
    Role: DATE
  - Dtype: varchar(33)
    Field: NOACTherapyDiscontinued
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: NOACTherapyDiscontinuedDate
    PHI: true
    Role: DATE
  - Dtype: varchar(31)
    Field: NOACTherapyResumed
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: NOACTherapyResumedDate
    PHI: true
    Role: DATE
  - Dtype: varchar(33)
    Field: F_AspirinDiscontinued
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: F_AspirinDiscontinuedDate
    PHI: true
    Role: DATE
  - Dtype: varchar(31)
    Field: F_AspirinResumed
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: F_AspirinResumedDate
    PHI: true
    Role: DATE
  - Dtype: varchar(33)
    Field: F_P2Y12Discontinued
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: F_P2Y12DiscontinuedDate
    PHI: true
    Role: DATE
  - Dtype: varchar(31)
    Field: F_P2Y12Resumed
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: F_P2Y12Date
    PHI: true
    Role: DATE
  - Dtype: varchar(38)
    Field: MedID_Fondaparinux
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: MedID_Heparin_Derivative
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: MedID_Low_Molecular_Wt_Heparin
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: MedID_Unfractionated_Heparin
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: MedID_Warfain
    PHI: false
    Role: null
  - Dtype: varchar(32)
    Field: MedID_Dose
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: MedID_Aspirin
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: MedID_Aspirin_Dipyridamole
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: MedID_Vorapaxar
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: MedID_Apixaban
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: MedID_Dabigatran
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: MedID_Edoxaban
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: MedID_Rivaroxaban
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: MedID_Cangrelor
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: MedID_Clopidogrel
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: MedID_Other_P2Y12_Inhibitor
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: MedID_Prasugrel
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: MedID_Ticagrelor
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: MedID_Ticlopidine
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_BleedAdjStatus
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_BleedDeathDate
    PHI: true
    Role: DATE
  - Dtype: varchar(12)
    Field: ADJ_BleedInvInter
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_BleedRBCTransfusion
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_BleedRBCUnits
    PHI: false
    Role: null
  - Dtype: decimal(3,1)
    Field: ADJ_BleedPreTransHgb
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_BleedImagePerf
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_BleedEndOrganDamage
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_BleedPrimaryDC
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_BleedMajorSurgery
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_BleedPCI
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_BleedProcRelated
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_BleedDevRelated
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_SysThromboAdjStatus
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_SysThromboDeathDate
    PHI: true
    Role: DATE
  - Dtype: varchar(12)
    Field: ADJ_SysThromboDeathCause
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_SysThromboHypoperfusion
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_SysThromboImagEvidence
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_SysThromboTheraInterv
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_NeuroAdjStatus
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: AJ_DeathDate
    PHI: true
    Role: DATE
  - Dtype: varchar(12)
    Field: ADJ_NeuroSxOnset
    PHI: true
    Role: DATE
  - Dtype: varchar(25)
    Field: AJ_NeuroDef
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_NeuroClinicPresent
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_NeuroDxConfirmed
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: AJ_NeuroImag
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_NeuroIVrTPA
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_NeuroEndoTheraInter
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: AJ_NeuroSxDuration
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_NeuroTrauma
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_RankinScale
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_RankinScaleNA
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_NeuroProcRelated
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_NeuroDevRelated
    PHI: false
    Role: null
  - Dtype: varchar(24)
    Field: EventID
    PHI: false
    Role: null
  - Dtype: varchar(82)
    Field: F_EVENTNAME
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: EventOccurred
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: EventDate
    PHI: true
    Role: DATE
  - Dtype: varchar(20)
    Field: OriginalOtherId
    PHI: true
    Role: MEDRECNUM
  - Dtype: varchar(100)
    Field: HospName
    PHI: false
    Role: HOSPITAL
  - Dtype: varchar(73)
    Field: FileName
    PHI: false
    Role: null
  - Dtype: varchar(22)
    Field: Version
    PHI: false
    Role: null
  - Dtype: varchar(18)
    Field: DatasetName
    PHI: false
    Role: null
  - Dtype: bigint
    Field: ClientFileId
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: BiomeImportDT
    PHI: false
    Role: null
  - Dtype: varchar(34)
    Field: CareEntityId
    PHI: false
    Role: CAREENTITY
  - Dtype: varchar(34)
    Field: TenantID
    PHI: false
    Role: TENANT
  - Dtype: int
    Field: YearMonth
    PHI: false
    Role: ANCHOR_YEAR_MONTH
  - Dtype: int
    Field: CaseSequenceNumber
    PHI: false
    Role: CASE_NUMBER
  - Dtype: varchar(12)
    Field: TheraInterv_Other
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_NeuroDeficitType
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: Hemorrhage_Intracerebral
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: Hemorrhage_Subarachnoid
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: Hemorrhage_Subdural
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ImagingMethod_Angiography
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ImagingMethod_CTScan
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ImagingMethod_MRI
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ImagingMethod_Ultrasound
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ImagingMethod_Other
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TheraInterv_Catheter
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TheraInterv_Pharmacological
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TheraInterv_Surgical
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: EventName
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_MedID_Aspirin_81mg
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_MedID_Aspirin_325mg
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_MedID_Vorapaxar
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_MedID_Clopidogrel
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_MedID_Prasugrel
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_MedID_Ticlopidine
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_MedID_Ticagrelor
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_MedID_Warfain
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_MedID_Apixaban
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_MedID_Dabigatran
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_MedID_Rivaroxaban
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_MedID_Edoxaban
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_MedID_Unfractionated_Heparin
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MedID_Aggrenox
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MedID_Durlaza
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MedID_Aspirin_81mg
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MedID_Aspirin_325mg
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_MedID_Fondaparinux
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_MedID_Low_Molecular_Wt_Heparin
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_MedID_Heparin_Derivative
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_MedID_Aggrenox
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_MedID_Durlaza
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: F_CreatND
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: F_FollowupEventOccurred
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_MEDID_ASPIRIN_101_324MG
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_MEDID_ASPIRIN_81_100_MG
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_MEDID_ASPIRIN_DIPYRIDAMOLE
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_MEDID_CANGRELOR
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ADJ_MEDID_OTHER_P2Y12_INHIBITOR
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DeathCause
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ProcedureStopDate
    PHI: true
    Role: DATE
  - Dtype: varchar(30)
    Field: ProcedureStopTime
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: F_DeathDate
    PHI: true
    Role: DATE
  - Dtype: varchar(12)
    Field: F_HgbND
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: SubmissionType
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: ImagingType_Angiography
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: ImagingType_CTScan
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: ImagingType_MRI
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: ImagingType_Other
    PHI: false
    Role: null
  - Dtype: bigint unsigned
    Field: BiomeEncounterId
    PHI: false
    Role: null
custom_fields:
  - Delimiter: null
    Derivative Field: F_MedID
    Derivative Field Code: 100013057
    Derivative Value: Fondaparinux
    Derivative Value Code: 321208
    Exact Match: 1
    Field: MedID_Fondaparinux
    Reference Field: F_MedAdmin2
    Reference Field Code: 432102000
    Section: FUPMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: F_MedID
    Derivative Field Code: 100013057
    Derivative Value: Heparin Derivative
    Derivative Value Code: 100000921
    Exact Match: 1
    Field: MedID_Heparin_Derivative
    Reference Field: F_MedAdmin2
    Reference Field Code: 432102000
    Section: FUPMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: F_MedID
    Derivative Field Code: 100013057
    Derivative Value: Low Molecular Weight Heparin
    Derivative Value Code: 373294004
    Exact Match: 1
    Field: MedID_Low_Molecular_Wt_Heparin
    Reference Field: F_MedAdmin2
    Reference Field Code: 432102000
    Section: FUPMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: F_MedID
    Derivative Field Code: 100013057
    Derivative Value: Unfractionated Heparin
    Derivative Value Code: 96382006
    Exact Match: 1
    Field: MedID_Unfractionated_Heparin
    Reference Field: F_MedAdmin2
    Reference Field Code: 432102000
    Section: FUPMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: F_MedID
    Derivative Field Code: 100013057
    Derivative Value: Warfarin
    Derivative Value Code: 11289
    Exact Match: 1
    Field: MedID_Warfain
    Reference Field: F_MedAdmin2
    Reference Field Code: 432102000
    Section: FUPMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: F_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aspirin
    Derivative Value Code: 1191
    Exact Match: 1
    Field: medid_aspirin
    Reference Field: F_MedAdmin2
    Reference Field Code: 432102000
    Section: FUPMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: F_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aspirin/Dipyridamole
    Derivative Value Code: 226716
    Exact Match: 1
    Field: MedID_Aspirin_Dipyridamole
    Reference Field: F_MedAdmin2
    Reference Field Code: 432102000
    Section: FUPMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: F_MedID
    Derivative Field Code: 100013057
    Derivative Value: Vorapaxar
    Derivative Value Code: 1537034
    Exact Match: 1
    Field: MedID_Vorapaxar
    Reference Field: F_MedAdmin2
    Reference Field Code: 432102000
    Section: FUPMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: F_MedID
    Derivative Field Code: 100013057
    Derivative Value: Apixaban
    Derivative Value Code: 1364430
    Exact Match: 1
    Field: MedID_Apixaban
    Reference Field: F_MedAdmin2
    Reference Field Code: 432102000
    Section: FUPMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: F_MedID
    Derivative Field Code: 100013057
    Derivative Value: Dabigatran
    Derivative Value Code: 1546356
    Exact Match: 1
    Field: MedID_Dabigatran
    Reference Field: F_MedAdmin2
    Reference Field Code: 432102000
    Section: FUPMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: F_MedID
    Derivative Field Code: 100013057
    Derivative Value: Edoxaban
    Derivative Value Code: 1599538
    Exact Match: 1
    Field: MedID_Edoxaban
    Reference Field: F_MedAdmin2
    Reference Field Code: 432102000
    Section: FUPMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: F_MedID
    Derivative Field Code: 100013057
    Derivative Value: Rivaroxaban
    Derivative Value Code: 1114195
    Exact Match: 1
    Field: MedID_Rivaroxaban
    Reference Field: F_MedAdmin2
    Reference Field Code: 432102000
    Section: FUPMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: F_MedID
    Derivative Field Code: 100013057
    Derivative Value: Cangrelor
    Derivative Value Code: 1656052
    Exact Match: 1
    Field: MedID_Cangrelor
    Reference Field: F_MedAdmin2
    Reference Field Code: 432102000
    Section: FUPMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: F_MedID
    Derivative Field Code: 100013057
    Derivative Value: Clopidogrel
    Derivative Value Code: 32968
    Exact Match: 1
    Field: MedID_Clopidogrel
    Reference Field: F_MedAdmin2
    Reference Field Code: 432102000
    Section: FUPMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: F_MedID
    Derivative Field Code: 100013057
    Derivative Value: Other P2Y12
    Derivative Value Code: 112000001003
    Exact Match: 1
    Field: MedID_Other_P2Y12_Inhibitor
    Reference Field: F_MedAdmin2
    Reference Field Code: 432102000
    Section: FUPMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: F_MedID
    Derivative Field Code: 100013057
    Derivative Value: Prasugrel
    Derivative Value Code: 613391
    Exact Match: 1
    Field: MedID_Prasugrel
    Reference Field: F_MedAdmin2
    Reference Field Code: 432102000
    Section: FUPMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: F_MedID
    Derivative Field Code: 100013057
    Derivative Value: Ticagrelor
    Derivative Value Code: 1116632
    Exact Match: 1
    Field: MedID_Ticagrelor
    Reference Field: F_MedAdmin2
    Reference Field Code: 432102000
    Section: FUPMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: F_MedID
    Derivative Field Code: 100013057
    Derivative Value: Ticlopidine
    Derivative Value Code: 10594
    Exact Match: 1
    Field: MedID_Ticlopidine
    Reference Field: F_MedAdmin2
    Reference Field Code: 432102000
    Section: FUPMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: F_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aspirin
    Derivative Value Code: 1191
    Exact Match: 1
    Field: MedID_Dose
    Reference Field: F_MedDose2
    Reference Field Code: 100014233
    Section: FUPMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_MedID
    Derivative Field Code: 100013057
    Derivative Value: Apixaban
    Derivative Value Code: 321208
    Exact Match: 1
    Field: ADJ_MedID_Apixaban
    Reference Field: FU_ADJ_MedAdmin
    Reference Field Code: 432102000
    Section: FADJMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aspirin 101 to 324 mg
    Derivative Value Code: 100000921
    Exact Match: 1
    Field: ADJ_MEDID_ASPIRIN_101_324MG
    Reference Field: FU_ADJ_MedAdmin
    Reference Field Code: 432102000
    Section: FADJMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aspirin 325 mg
    Derivative Value Code: 373294004
    Exact Match: 1
    Field: ADJ_MedID_Aspirin_325mg
    Reference Field: FU_ADJ_MedAdmin
    Reference Field Code: 432102000
    Section: FADJMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aspirin 81 to 100 mg
    Derivative Value Code: 96382006
    Exact Match: 1
    Field: ADJ_MEDID_ASPIRIN_81_100_MG
    Reference Field: FU_ADJ_MedAdmin
    Reference Field Code: 432102000
    Section: FADJMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aspirin 81 to 100 mg
    Derivative Value Code: 96382006
    Exact Match: 1
    Field: ADJ_MedID_Aspirin_81mg
    Reference Field: FU_ADJ_MedAdmin
    Reference Field Code: 432102000
    Section: FADJMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aspirin/Dipyridamole
    Derivative Value Code: 11289
    Exact Match: 1
    Field: ADJ_MEDID_ASPIRIN_DIPYRIDAMOLE
    Reference Field: FU_ADJ_MedAdmin
    Reference Field Code: 432102000
    Section: FADJMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_MedID
    Derivative Field Code: 100013057
    Derivative Value: Cangrelor
    Derivative Value Code: 112000002080
    Exact Match: 1
    Field: ADJ_MEDID_CANGRELOR
    Reference Field: FU_ADJ_MedAdmin
    Reference Field Code: 432102000
    Section: FADJMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_MedID
    Derivative Field Code: 100013057
    Derivative Value: Clopidogrel
    Derivative Value Code: 112000002081
    Exact Match: 1
    Field: ADJ_MedID_Clopidogrel
    Reference Field: FU_ADJ_MedAdmin
    Reference Field Code: 432102000
    Section: FADJMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_MedID
    Derivative Field Code: 100013057
    Derivative Value: Dabigatran
    Derivative Value Code: 317300
    Exact Match: 1
    Field: ADJ_MedID_Dabigatran
    Reference Field: FU_ADJ_MedAdmin
    Reference Field Code: 432102000
    Section: FADJMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_MedID
    Derivative Field Code: 100013057
    Derivative Value: Edoxaban
    Derivative Value Code: 226716
    Exact Match: 1
    Field: ADJ_MedID_Edoxaban
    Reference Field: FU_ADJ_MedAdmin
    Reference Field Code: 432102000
    Section: FADJMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_MedID
    Derivative Field Code: 100013057
    Derivative Value: Fondaparinux
    Derivative Value Code: 1537034
    Exact Match: 1
    Field: ADJ_MedID_Fondaparinux
    Reference Field: FU_ADJ_MedAdmin
    Reference Field Code: 432102000
    Section: FADJMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_MedID
    Derivative Field Code: 100013057
    Derivative Value: Heparin Derivative
    Derivative Value Code: 1364430
    Exact Match: 1
    Field: ADJ_MedID_Heparin_Derivative
    Reference Field: FU_ADJ_MedAdmin
    Reference Field Code: 432102000
    Section: FADJMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_MedID
    Derivative Field Code: 100013057
    Derivative Value: Low Molecular Weight Heparin
    Derivative Value Code: 1546356
    Exact Match: 1
    Field: ADJ_MedID_Low_Molecular_Wt_Heparin
    Reference Field: FU_ADJ_MedAdmin
    Reference Field Code: 432102000
    Section: FADJMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_MedID
    Derivative Field Code: 100013057
    Derivative Value: Other P2Y12
    Derivative Value Code: 1599538
    Exact Match: 1
    Field: ADJ_MEDID_OTHER_P2Y12_INHIBITOR
    Reference Field: FU_ADJ_MedAdmin
    Reference Field Code: 432102000
    Section: FADJMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_MedID
    Derivative Field Code: 100013057
    Derivative Value: Prasugrel
    Derivative Value Code: 1114195
    Exact Match: 1
    Field: ADJ_MedID_Prasugrel
    Reference Field: FU_ADJ_MedAdmin
    Reference Field Code: 432102000
    Section: FADJMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_MedID
    Derivative Field Code: 100013057
    Derivative Value: Rivaroxaban
    Derivative Value Code: 1656052
    Exact Match: 1
    Field: ADJ_MedID_Rivaroxaban
    Reference Field: FU_ADJ_MedAdmin
    Reference Field Code: 432102000
    Section: FADJMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_MedID
    Derivative Field Code: 100013057
    Derivative Value: Ticagrelor
    Derivative Value Code: 32968
    Exact Match: 1
    Field: ADJ_MedID_Ticagrelor
    Reference Field: FU_ADJ_MedAdmin
    Reference Field Code: 432102000
    Section: FADJMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_MedID
    Derivative Field Code: 100013057
    Derivative Value: Ticlopidine
    Derivative Value Code: 112000001003
    Exact Match: 1
    Field: ADJ_MedID_Ticlopidine
    Reference Field: FU_ADJ_MedAdmin
    Reference Field Code: 432102000
    Section: FADJMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_MedID
    Derivative Field Code: 100013057
    Derivative Value: Unfractionated Heparin
    Derivative Value Code: 613391
    Exact Match: 1
    Field: ADJ_MedID_Unfractionated_Heparin
    Reference Field: FU_ADJ_MedAdmin
    Reference Field Code: 432102000
    Section: FADJMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_MedID
    Derivative Field Code: 100013057
    Derivative Value: Vorapaxar
    Derivative Value Code: 1116632
    Exact Match: 1
    Field: ADJ_MedID_Vorapaxar
    Reference Field: FU_ADJ_MedAdmin
    Reference Field Code: 432102000
    Section: FADJMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_MedID
    Derivative Field Code: 100013057
    Derivative Value: Warfarin
    Derivative Value Code: 10594
    Exact Match: 1
    Field: ADJ_MedID_Warfain
    Reference Field: FU_ADJ_MedAdmin
    Reference Field Code: 432102000
    Section: FADJMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_NeuroIntracranType
    Derivative Field Code: 230706003
    Derivative Value: Intracerebral
    Derivative Value Code: 274100004
    Exact Match: 0
    Field: Hemorrhage_Intracerebral
    Reference Field: FU_ADJ_NeuroIntracranType
    Reference Field Code: 230706003
    Section: FADJ
    Value Map:
      Intracerebral: 'Yes'
  - Delimiter: null
    Derivative Field: FU_ADJ_NeuroIntracranType
    Derivative Field Code: 230706003
    Derivative Value: Subarachnoid
    Derivative Value Code: 21454007
    Exact Match: 0
    Field: Hemorrhage_Subarachnoid
    Reference Field: FU_ADJ_NeuroIntracranType
    Reference Field Code: 230706003
    Section: FADJ
    Value Map:
      Subarachnoid: 'Yes'
  - Delimiter: null
    Derivative Field: FU_ADJ_NeuroIntracranType
    Derivative Field Code: 230706003
    Derivative Value: Subdural
    Derivative Value Code: 35486000
    Exact Match: 0
    Field: Hemorrhage_Subdural
    Reference Field: FU_ADJ_NeuroIntracranType
    Reference Field Code: 230706003
    Section: FADJ
    Value Map:
      Subdural: 'Yes'
  - Delimiter: null
    Derivative Field: FU_ADJ_NeuroBrainImagingType
    Derivative Field Code: 441986001
    Derivative Value: Computed Tomography
    Derivative Value Code: 77477000
    Exact Match: 0
    Field: imagingtype_ctscan
    Reference Field: FU_ADJ_NeuroBrainImaging
    Reference Field Code: 441986001
    Section: FADJ
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_NeuroBrainImagingType
    Derivative Field Code: 441986001
    Derivative Value: Other
    Derivative Value Code: 112000001862
    Exact Match: 0
    Field: imagingtype_other
    Reference Field: FU_ADJ_NeuroBrainImaging
    Reference Field Code: 441986001
    Section: FADJ
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_NeuroBrainImagingType
    Derivative Field Code: 441986001
    Derivative Value: Magnetic Resonance Imaging
    Derivative Value Code: 113091000
    Exact Match: 0
    Field: imagingtype_mri
    Reference Field: FU_ADJ_NeuroBrainImaging
    Reference Field Code: 441986001
    Section: FADJ
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_NeuroBrainImagingType
    Derivative Field Code: 441986001
    Derivative Value: Cerebral Angiography
    Derivative Value Code: 3258003
    Exact Match: 0
    Field: imagingtype_angiography
    Reference Field: FU_ADJ_NeuroBrainImaging
    Reference Field Code: 441986001
    Section: FADJ
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_SysThromboImagMethod
    Derivative Field Code: 112000000960
    Derivative Value: Other Imaging
    Derivative Value Code: 112000001862
    Exact Match: 0
    Field: imagingmethod_other
    Reference Field: FU_ADJ_SysThromboImagEvidence
    Reference Field Code: 365853002
    Section: FADJ
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_SysThromboImagMethod
    Derivative Field Code: 112000000960
    Derivative Value: Magnetic Resonance Imaging
    Derivative Value Code: 113091000
    Exact Match: 0
    Field: imagingmethod_mri
    Reference Field: FU_ADJ_SysThromboImagEvidence
    Reference Field Code: 365853002
    Section: FADJ
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_SysThromboImagMethod
    Derivative Field Code: 112000000960
    Derivative Value: Ultrasound
    Derivative Value Code: 112000001042
    Exact Match: 0
    Field: imagingmethod_ultrasound
    Reference Field: FU_ADJ_SysThromboImagEvidence
    Reference Field Code: 365853002
    Section: FADJ
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_SysThromboImagMethod
    Derivative Field Code: 112000000960
    Derivative Value: Angiography
    Derivative Value Code: 77343006
    Exact Match: 0
    Field: imagingmethod_angiography
    Reference Field: FU_ADJ_SysThromboImagEvidence
    Reference Field Code: 365853002
    Section: FADJ
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_SysThromboImagMethod
    Derivative Field Code: 112000000960
    Derivative Value: Computed Tomography
    Derivative Value Code: 77477000
    Exact Match: 0
    Field: imagingmethod_ctscan
    Reference Field: FU_ADJ_SysThromboImagEvidence
    Reference Field Code: 365853002
    Section: FADJ
    Value Map: null
  - Delimiter: null
    Derivative Field: F_Method
    Derivative Field Code: 100014059
    Derivative Value: Hospitalized
    Derivative Value Code: **********
    Exact Match: 0
    Field: method_hospital
    Reference Field: F_Method
    Reference Field Code: 100014059
    Section: FOLLOWUP
    Value Map:
      Hospitalized: 'Yes'
  - Delimiter: null
    Derivative Field: F_Method
    Derivative Field Code: 100014059
    Derivative Value: Letter from Medical Provider
    Derivative Value Code: 100014061
    Exact Match: 0
    Field: method_medprovider
    Reference Field: F_Method
    Reference Field Code: 100014059
    Section: FOLLOWUP
    Value Map:
      Letter from Medical Provider: 'Yes'
  - Delimiter: null
    Derivative Field: F_Method
    Derivative Field Code: 100014059
    Derivative Value: Medical Records
    Derivative Value Code: 100014060
    Exact Match: 0
    Field: method_medrecord
    Reference Field: F_Method
    Reference Field Code: 100014059
    Section: FOLLOWUP
    Value Map:
      Medical Records: 'Yes'
  - Delimiter: null
    Derivative Field: F_Method
    Derivative Field Code: 100014059
    Derivative Value: Office Visit
    Derivative Value Code: 183654001
    Exact Match: 0
    Field: method_office
    Reference Field: F_Method
    Reference Field Code: 100014059
    Section: FOLLOWUP
    Value Map:
      Office Visit: 'Yes'
  - Delimiter: null
    Derivative Field: F_Method
    Derivative Field Code: 100014059
    Derivative Value: Other
    Derivative Value Code: 100000351
    Exact Match: 0
    Field: method_other
    Reference Field: F_Method
    Reference Field Code: 100014059
    Section: FOLLOWUP
    Value Map:
      Other: 'Yes'
  - Delimiter: null
    Derivative Field: F_Method
    Derivative Field Code: 100014059
    Derivative Value: Phone Call
    Derivative Value Code: 100014062
    Exact Match: 0
    Field: method_phone
    Reference Field: F_Method
    Reference Field Code: 100014059
    Section: FOLLOWUP
    Value Map:
      Phone Call: 'Yes'
  - Delimiter: null
    Derivative Field: F_Method
    Derivative Field Code: 100014059
    Derivative Value: Social Security Death Master File
    Derivative Value Code: 1000142362
    Exact Match: 0
    Field: method_ssfile
    Reference Field: F_Method
    Reference Field Code: 100014059
    Section: FOLLOWUP
    Value Map:
      Social Security Death Master File: 'Yes'
  - Delimiter: null
    Derivative Field: FU_ADJ_SysThromboIntervType
    Derivative Field Code: 100013063
    Derivative Value: Other
    Derivative Value Code: 112000000172
    Exact Match: 0
    Field: therainterv_other
    Reference Field: FU_ADJ_SysThromboTheraInterv
    Reference Field Code: 100013063
    Section: FADJ
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_SysThromboIntervType
    Derivative Field Code: 100013063
    Derivative Value: Catheter
    Derivative Value Code: 276272002
    Exact Match: 0
    Field: therainterv_catheter
    Reference Field: FU_ADJ_SysThromboTheraInterv
    Reference Field Code: 100013063
    Section: FADJ
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_SysThromboIntervType
    Derivative Field Code: 100013063
    Derivative Value: Pharmacological
    Derivative Value Code: 182832007
    Exact Match: 0
    Field: therainterv_pharmacological
    Reference Field: FU_ADJ_SysThromboTheraInterv
    Reference Field Code: 100013063
    Section: FADJ
    Value Map: null
  - Delimiter: null
    Derivative Field: FU_ADJ_SysThromboIntervType
    Derivative Field Code: 100013063
    Derivative Value: Surgical
    Derivative Value Code: 387713003
    Exact Match: 0
    Field: therainterv_surgical
    Reference Field: FU_ADJ_SysThromboTheraInterv
    Reference Field Code: 100013063
    Section: FADJ
    Value Map: null
pivot_sections:
  - FUPMEDS
  - FADJMEDS
rename_fields:
  - New Field: fu_refdischargedate
    Old Field: dischargedate
  - New Field: f_bietransfers
    Old Field: bietransfers
  - New Field: fu_adj_bleedpretranshgb
    Old Field: adj_bleedpretranshgb
  - New Field: fu_adj_rankinscalena
    Old Field: adj_rankinscalena
  - New Field: f_biebowels
    Old Field: biebowels
  - New Field: f_warfarinresumeddate
    Old Field: warfarinresumeddate
  - New Field: fu_adj_bleeddevrelated
    Old Field: adj_bleeddevrelated
  - New Field: f_biestairs
    Old Field: biestairs
  - New Field: fu_adj_systhrombohypoperfusion
    Old Field: adj_systhrombohypoperfusion
  - New Field: residualleakfu
    Old Field: residualleak
  - New Field: furefarrivaldate
    Old Field: arrivaldate
  - New Field: fu_adj_systhromboimagevidence
    Old Field: adj_systhromboimagevidence
  - New Field: fu_adj_neuroprocrelated
    Old Field: adj_neuroprocrelated
  - New Field: f_biedressing
    Old Field: biedressing
  - New Field: fu_adj_neuroivrtpa
    Old Field: adj_neuroivrtpa
  - New Field: f_deathcause
    Old Field: deathcause
  - New Field: f_adj_systhrombodeathdate
    Old Field: adj_systhrombodeathdate
  - New Field: fu_adj_bleedimageperf
    Old Field: adj_bleedimageperf
  - New Field: f_lvef
    Old Field: lvef
  - New Field: fu_lvef
    Old Field: lvefassessed
  - New Field: fu_adj_bleedpci
    Old Field: adj_bleedpci
  - New Field: f_adj_neuroadjstatus
    Old Field: adj_neuroadjstatus
  - New Field: f_warfarindiscontinueddate
    Old Field: warfarindiscontinueddate
  - New Field: f_biegrooming
    Old Field: biegrooming
  - New Field: f_bieperf
    Old Field: bieperf
  - New Field: f_adj_systhrombodeathcause
    Old Field: adj_systhrombodeathcause
  - New Field: fu_adj_neurosxonset
    Old Field: adj_neurosxonset
  - New Field: fu_adj_bleedendorgandamage
    Old Field: adj_bleedendorgandamage
  - New Field: fu_adj_neurotrauma
    Old Field: adj_neurotrauma
  - New Field: fu_adj_neurodxconfirmed
    Old Field: adj_neurodxconfirmed
  - New Field: fu_adj_systhromboadjstatus
    Old Field: adj_systhromboadjstatus
  - New Field: f_biebathing
    Old Field: biebathing
  - New Field: fu_adj_bleedinvinter
    Old Field: adj_bleedinvinter
  - New Field: fu_adj_bleedrbctransfusion
    Old Field: adj_bleedrbctransfusion
  - New Field: fu_adj_neurodeficittype
    Old Field: adj_neurodeficittype
  - New Field: f_biemobility
    Old Field: biemobility
  - New Field: f_biebladder
    Old Field: biebladder
  - New Field: fu_adj_bleedprocrelated
    Old Field: adj_bleedprocrelated
  - New Field: fu_adj_neuroclinicpresent
    Old Field: adj_neuroclinicpresent
  - New Field: fupeventdate
    Old Field: eventdate
  - New Field: f_assessmentdate
    Old Field: assessmentdate
  - New Field: fu_adj_rankinscale
    Old Field: adj_rankinscale
  - New Field: residualleaknafu
    Old Field: residualleakna
  - New Field: f_warfarinresumed
    Old Field: warfarinresumed
  - New Field: creat_fu
    Old Field: f_creat
  - New Field: f_warfarindiscontinued
    Old Field: warfarindiscontinued
  - New Field: fu_adj_bleedmajorsurgery
    Old Field: adj_bleedmajorsurgery
  - New Field: fu_adj_systhrombotherainterv
    Old Field: adj_systhrombotherainterv
  - New Field: fu_adj_bleedrbcunits
    Old Field: adj_bleedrbcunits
  - New Field: ttedate_f
    Old Field: ttedate
  - New Field: fu_adj_bleeddeathdate
    Old Field: adj_bleeddeathdate
  - New Field: f_biefeeding
    Old Field: biefeeding
  - New Field: f_adj_bleedadjstatus
    Old Field: adj_bleedadjstatus
  - New Field: tteperffu
    Old Field: tteperf
  - New Field: fu_adj_neuroendotherainter
    Old Field: adj_neuroendotherainter
  - New Field: f_adj_neurodevrelated
    Old Field: adj_neurodevrelated
  - New Field: refprocstartdatetime
    Old Field: procedurestartdatetime
  - New Field: hgbnd_fu
    Old Field: f_hgbnd
  - New Field: f_adj_deathdate
    Old Field: aj_deathdate
  - New Field: fu_adj_neurodeficit
    Old Field: aj_neurodef
  - New Field: fu_adj_neurosxduration
    Old Field: aj_neurosxduration
  - New Field: F_BIEToilet
    Old Field: bietoiletuse
  - New Field: F_Event
    Old Field: eventname
  - New Field: FupEvOccurred
    Old Field: eventoccurred
  - New Field: F_AspirinTherapyDiscontinued
    Old Field: f_aspirindiscontinued
  - New Field: F_AspirinTherapyDiscontinuedDate
    Old Field: f_aspirindiscontinueddate
  - New Field: F_AspirinTherapyResumed
    Old Field: f_aspirinresumed
  - New Field: F_AspirinTherapyResumedDate
    Old Field: f_aspirinresumeddate
  - New Field: F_CreatND
    Old Field: f_creatna
  - New Field: LowHgbValue_F
    Old Field: F_Hgb
  - New Field: F_P2Y12TherapyResumedDate
    Old Field: f_p2y12date
  - New Field: F_P2Y12TherapyDiscontinued
    Old Field: f_p2y12discontinued
  - New Field: F_P2Y12TherapyDiscontinuedDate
    Old Field: f_p2y12discontinueddate
  - New Field: F_P2Y12TherapyResumed
    Old Field: f_p2y12resumed
  - New Field: F_RankinScore
    Old Field: F_RankinScale
  - New Field: F_mRS_NA
    Old Field: F_RankinScaleNA
  - New Field: Sex
    Old Field: Gender
  - New Field: FUInterv
    Old Field: followupinterval
  - New Field: F_DOACTherapyDiscontinued
    Old Field: noactherapydiscontinued
  - New Field: F_DOACTherapyDiscontinuedDate
    Old Field: noactherapydiscontinueddate
  - New Field: F_DOACTherapyResumed
    Old Field: noactherapyresumed
  - New Field: F_DOACTherapyResumedDate
    Old Field: noactherapyresumeddate
  - New Field: F_Status
    Old Field: dischargestatus
  - New Field: FollowupKey
    Old Field: FollowupId
value_mapping:
  - Field: adj_bleedprocrelated
    Value Map:
      Certain: '1'
      Probable: '2'
      Possible: '3'
      Unlikely: '4'
      Unclassifiable: '5'
  - Field: adj_bleeddevrelated
    Value Map:
      Certain: '1'
      Probable: '2'
      Possible: '3'
      Unlikely: '4'
      Unclassifiable: '5'
parent_child_sections:
  - Parent Section: FADJ
    Child Section: FADJMEDS