version: '1.0'
biome_schema:
- Dtype: int(11)
  Field: version
  PHI: false
  Role: null
- Dtype: text
  Field: filename
  PHI: false
  Role: null
- Dtype: text
  Field: i10dschstkdx_description
  PHI: false
  Role: null
- Dtype: text
  Field: i10prindx_description
  PHI: false
  Role: null
- Dtype: text
  Field: iacomp
  PHI: false
  Role: null
- Dtype: text
  Field: initialexamfindings1
  PHI: false
  Role: null
- Dtype: text
  Field: fluvaccine
  PHI: false
  Role: null
- Dtype: text
  Field: accesscase
  PHI: true
  Role: FREE_TEXT
- Dtype: text
  Field: merreasonsdelay
  PHI: false
  Role: null
- Dtype: text
  Field: rankindschscale
  PHI: false
  Role: null
- Dtype: text
  Field: prestrokemrsscore
  PHI: false
  Role: null
- Dtype: text
  Field: reastransfer
  PHI: false
  Role: null
- Dtype: text
  Field: telestrokeconsultation
  PHI: false
  Role: null
- Dtype: text
  Field: ichetiologydoc
  PHI: false
  Role: null
- Dtype: text
  Field: admitsource
  PHI: false
  Role: null
- Dtype: text
  Field: relexclusion2
  PHI: false
  Role: null
- Dtype: text
  Field: relexclusion
  PHI: false
  Role: null
- Dtype: text
  Field: transferreason
  PHI: false
  Role: null
- Dtype: text
  Field: rehabsvcs
  PHI: false
  Role: null
- Dtype: text
  Field: c_prestrokemrs
  PHI: false
  Role: null
- Dtype: text
  Field: merreasons
  PHI: false
  Role: null
- Dtype: text
  Field: gs_rsdeltra
  PHI: false
  Role: null
- Dtype: text
  Field: ivtpadelaymedreason
  PHI: false
  Role: null
- Dtype: text
  Field: prevmedhistory
  PHI: false
  Role: null
- Dtype: text
  Field: exclusionlessthan3hrc5
  PHI: false
  Role: null
- Dtype: text
  Field: exclusion2
  PHI: false
  Role: null
- Dtype: text
  Field: onsetcomments
  PHI: false
  Role: null
- Dtype: text
  Field: otherdiagicd10
  PHI: false
  Role: null
- Dtype: text
  Field: i10otherdx_description
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: raceother
  PHI: false
  Role: null
- Dtype: Decimal(6,3)
  Field: inr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: priorhf
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: status
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: racewhite
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: gender
  PHI: true
  Role: GENDER
- Dtype: varchar(10)
  Field: insnone
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: insprivate
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: insmedicaid
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: insmedicare
  PHI: false
  Role: null
- Dtype: Decimal(6,3)
  Field: height
  PHI: false
  Role: null
- Dtype: Decimal(6,3)
  Field: weight
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: racenathawpasislander
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: raceotherhispanic
  PHI: false
  Role: null
- Dtype: int
  Field: systolicbp
  PHI: false
  Role: null
- Dtype: int
  Field: diastolicbp
  PHI: false
  Role: null
- Dtype: Decimal(4,1)
  Field: creatinine
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: creatininend
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: clinicaltrial
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: priorstroke
  PHI: false
  Role: null
- Dtype: Decimal(6,2)
  Field: bmi
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: raceamericanindianalaskannative
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: raceblackafricanamerican
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: priorafib
  PHI: false
  Role: null
- Dtype: int
  Field: lipidsldl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dischatrialrhythmafib
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: electivecarotidinterventionadmin
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: locationcare
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: prehospems
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: rankindischargeperformed
  PHI: false
  Role: null
- Dtype: int
  Field: rankindischargescore
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: initialnihss
  PHI: false
  Role: null
- Dtype: int
  Field: nihsstotalscore
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: insnd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: priorcarotidstenosis
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: priorpvd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: pmhnone
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: resolutionofstrokesymptomsonpresentation
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: nomedpriortoadmission
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: antiplateletpreadmission
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: anticoagpreadmission
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: antihypertensivepreadmission
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cholesterolreducerpreadmission
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: diabeticmedpreadmission
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivtwarncontra3hrs
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivtpacontra4p5hrs
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivthrombolyisstarted
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivtpaoutside
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: comfortonlydocumenttiming
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: icd10diagnosis
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ptnpothroughoutstay
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dysphagia
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: propylaxis2ndday
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vteinterventionlduh
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vteinterventionlmwh
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vteinterventionipc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vteinterventiongcs
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vteinterventionfactorxainhib
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vteinterventionwarfarin
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vteinterventionoralfactorxainhib
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otheranticoagdabigatranpradaxa
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otheranticoagargatroban
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otheranticoagdesirudiniprivask
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otheranticoagrivaroxabanxarelto
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otheranticoagapixabaneliquis
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otheranticoaglepirudinrefludan
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: docreasonoralfactorxa
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: docnovteadmission
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: antiplateletadmday2
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: lipidsnd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: nodcantithrom
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: persistentafib
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: whynoatrialmedswarfhep
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: whynoatrialmedsmentalstat
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dischargeantihyper
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: antihyperarb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: antihyperdiuretics
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: antihyperother
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cholreducstatintype
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cholreducstatindose
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cholreduchighstatintx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: eduantismokingtx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: edutlcdient
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: edusodiumdiet
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: edudiabetesteaching
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: edustrokerisk
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: eduusingems
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: edumeds
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: edustrokewarningsigns
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: edufollowup
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: assessedrehab
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherhealthcaredestin
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: firstimageinterpretation
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cpmcunit
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: procedureiatpa
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: iatpaoutsidehosp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: yearmonth
  PHI: false
  Role: ANCHOR_YEAR_MONTH
- Dtype: varchar(10)
  Field: cryptogenicstroke
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: implantcrmonitrng
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: intracranvascularimaging
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: carotidrevascul
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cust_optional11
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cust_optional12
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dyn_stroke
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dyn_stroke_optional
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: formdata_seq
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: additionalrelativeexclusion
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: addiotinalwarnings
  PHI: false
  Role: null
- Dtype: varchar(120)
  Field: transfertohospital
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: transferfromhospitalnotdoc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: transferfromhospitalnotlisted
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: transfertohospitalnotdoc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: transfertohospitalnotlisted
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: antithrombotic1dose
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: antithrombotic2dose
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: antithrombotic3dose
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: antithrombotic3freq
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: antithrombotic4
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: antithrombotic4class
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: antithrombotic4dose
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: antithrombotic4freq
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: bloodglucosend
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: bmind
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cholesterol
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: comments
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: compreperf
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ctcomplete
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ctinittimeprecision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cttype
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: gs_delintra
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: gs_diagdm
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dmdiagbasis
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dischargebpnd
  PHI: false
  Role: null
- Dtype: int
  Field: dischargediastolic
  PHI: false
  Role: null
- Dtype: int
  Field: dischargesystolic
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dmtx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dvtdoc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dysphagiaresult
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: gs_eduyes
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: exclusionlessthan3hr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: exclusiongrtrthan3hr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fastingglucose
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dischargefunctionstatus
  PHI: false
  Role: null
- Dtype: Decimal(3,1)
  Field: gs_globalrisk
  PHI: false
  Role: null
- Dtype: Decimal(4,1)
  Field: hba1c
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: hba1cnd
  PHI: false
  Role: null
- Dtype: int
  Field: hdl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: heightnd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: heightunit
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: homeless
  PHI: false
  Role: null
- Dtype: int
  Field: heartrate
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: icd9nostroke
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: icd10nostroke
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: initexamfindings
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: inrnotdoc
  PHI: false
  Role: null
- Dtype: Decimal(3,1)
  Field: gs_ischemicrisk
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivtpaorderdatena
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivtpaorderdateprecision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: teamarrivetimeprecision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ctna
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ctorderdatetimeprecision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ctinterpretdatetimeprecision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ecgcompldatetimeprecision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ecgna
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ecgorderdatetimeprecision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: assessdatetimeprecision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: assessdatetimena
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: xraycomments
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: labcompdatetimeprecison
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: labna
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: laborderdatetimeprec
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: neurosurgna
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: neurosurgdatetimeprecision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: teamactivedatetimeprecision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: teamactivedatetimena
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: xraycompdatetimeprecision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: xrayorderdatetimeprecision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: xrayna
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: lastknownwellprecision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: lifestylerecomm
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: lipidsnc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: strokemimicdiag
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dysarthrianihss
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: extinctionnihss
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: consciousnihss
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ansquesnihss
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: obeynihss
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: gazenihss
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: visualnihss
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: paresisnihss
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ltarmfuncnihss
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: rtarmfuncnihss
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: rtlegfuncnihss
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ltlegfuncnihss
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: limbataxianihss
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: sensorynihss
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: aphasianihss
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: nihssretro
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: noguideddosereason
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: noguideddosereasonunk
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: tpacontraptt
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: tpacontraptt2
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: hospcontralst3hrs
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: hospcontragrt3hrs
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: gs_noivhosp2hist
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: noivthromreason
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: noivthromreason2
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: noivwarninglst3hrs
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: noivwarninggrt3hrs
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: nothromcomments
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherantithrompres
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otheranticoagdose1
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otheranticoagdose2
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otheranticoagfreq1
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otheranticoagfreq2
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otheranticoagmed1
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otheranticoagmed2
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: admordersetused
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dischargelistused
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: onsetcomm
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: hospacqpneumonia
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: priorantithrom
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: priorantithrom3
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: priorantithrom3class
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ptcontractused
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: receivedreason
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: rankindischarge
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: antithromcontra
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: refhospdischargedatetimeprecison
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: refhosparrivdatetimeprecision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: refexclusionlst3hrs
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: refexclusiongrt3hrs
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: discoversamelastknwntime
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: smokingmedunspec
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ptstrokesymptomdatetimeprecision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: thrombolysisprotocol
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: thrombolysisprotocoltype
  PHI: false
  Role: null
- Dtype: int
  Field: transfernihss
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: transfernihssnd
  PHI: false
  Role: null
- Dtype: int
  Field: triglycerides
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vitalsignsnd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: waist
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: waistunits
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: waistnd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: weightnd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: weightunit
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: whynoatrialmedsalergy
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: zipcodeext
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: strokediagicd9discharge
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: strokediagicd10discharge
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivtpadelay
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivtpadelay30min
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivtpadelay45min
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivtpadelayeligreasoncomment
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivtpadelayhospreason
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivtpadelaymedreasoncomment
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivtpadelayothreason
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: admitdateprecision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: arrivaldatetimeprecision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dobprecision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivtpainitdatetimeprecision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: nostatindischarge
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherdiag1
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherdiag10
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherdiag11
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherdiag12
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherdiag13
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherdiag14
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherdiag15
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherdiag16
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherdiag17
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherdiag18
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherdiag19
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherdiag2
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherdiag20
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherdiag21
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherdiag22
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherdiag23
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherdiag24
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherdiag3
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherdiag4
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherdiag5
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherdiag6
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherdiag7
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherdiag8
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: otherdiag9
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vtedateprecision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: aspirinprophylaxis
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ndprophylaxis
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vfpprophylaxis
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: merica
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: merimagedateprecision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: mermca
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: targetlesionvis
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: imagingdone
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: imagingtype
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: imagingnd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vesocclusite
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: oh_optional1
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: oh_optional10
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: oh_optional13
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: oh_optional13_precision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: oh_optional15
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: oh_optional14_precision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: oh_optional2
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: oh_optional3
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: oh_optional4
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: oh_optional5
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: oh_optional6
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: oh_optional7
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: oh_optional8
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: oh_optional9
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: osi_form_type_bitmap
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: antidepmedpost
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: principaldiagnosis
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: antidepmedprior
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: procstep_id
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: site_os_id
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: sp_ethnic_opt
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: study_id
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: antismokingtx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dischargedateprecision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: laborderdatetimeprecision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: rankinscore
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cholreducothermed
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: priorhypertension
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: priorsmoker
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: posbrainimgdatetimeprecision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: originalmrn
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: clientversion
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doortoneedle
  PHI: false
  Role: null
- Dtype: varchar(100)
  Field: c_ticigrade2
  PHI: false
  Role: null
- Dtype: varchar(100)
  Field: c_ticigrade
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_eligmer
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_lvo
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: tq_disccode
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: tq_em
  PHI: false
  Role: null
- Dtype: text
  Field: jc_princicd10procedure
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proc6
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proc7
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proc8
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proc9
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proc16
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proc18
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proc24
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proc23
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: biomeencounterid
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: timtointravthrp45excl
  PHI: false
  Role: null
- Dtype: varchar(120)
  Field: c_posbrain_result
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dmtype
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: presdmtx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dmtxreason
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dxfupsched
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dxfupscheddt
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dxfupscheddt_precision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dyn_diabetes
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: gs_thrombadmin
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: gs_tenecdosend
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: gs_altedosend
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: gs_altotaldose
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: gs_thrombimg
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: gs_thrombimg_ot
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: noactiveinfec
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: activecoldflu
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: activeother
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: gs_tenectotaldose
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure10
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: mer_delay
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_ichscore
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure_timeutd2
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proctimeutd9
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: tq_encounterdt_precision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proc1
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure14
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_sahna
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proc15
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: mer_imagepdt_precision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_directadm
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure_timeutd12
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_nimodipine_dt_precision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure_timeutd21
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_deteriorate36h
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure20
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: admittingdiagnosis
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure16
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dyn_comprehensive
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_ldlhigh
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure_timeutd19
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_inraftertx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proc14
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherproceduredate20_utd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_sahscale_value
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure_timeutd3
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proc13
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proctimeutd15
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_nihss36ia
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proc21
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proctimeutd10
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proctimeutd7
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure_timeutd23
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10procdate20
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure2
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: mer_reasoth
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proctimeutd4
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_nihss36h
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_sahscale
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_emsgcstot
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure_timeutd20
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_wfnssah
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: gs_uti
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proc11
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proc19
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_nihss36h_nd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure4
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_procoagulant_dt_precision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_surgicalich
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure12
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: admittingdiagnosisicd10
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_emsgcsvoic
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure13
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: mer_ticidt_precision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_emsgcseye
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_ivoriatherapy
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_nihssivtutd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: mer_gradenotach
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_procoagulant
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proctimeutd5
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_sahscaledt_precision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proctimeutd20
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_ivtpaprior
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure_timeutd11
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_iaroutetpa
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_nihssperf
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proc17
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_funcscore
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure19
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_proxdist
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_sahscaledt
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: mer_othreasons
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_nihssiatpautd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: gs_pc_emsgcstot
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_nihss_nd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_platelet
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_nihssiatpamer
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_emsgcsintub
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proc4
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_emsgcsmot
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_noantithrodisc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure_timeutd8
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure8
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_lastknown
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proc10
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure23
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_antithrodisc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure7
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure_timeutd22
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure18
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_princproced_timeutd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure_timeutd7
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_addcm
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure_timeutd14
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: gs_foley
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: mer_ivscoreobt
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proctimeutd6
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: gs_pc_emsgcseye
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_notstroke
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proc12
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10procdateutd16
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_athero
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_nihperf
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proctimeutd23
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_discdtutd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proc20
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: mer_proc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proc3
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_iathrominitdt_precision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_nimodipine
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure_timeutd16
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_inrperf
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure_timeutd24
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure_timeutd15
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_firstpass
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure9
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_antithroday2
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proc2
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure_timeutd5
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_reasonnonim
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_nihssobtutd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proctimeutd12
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proctimeutd19
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_procoagulant_dt
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dyn_strokecm
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_neuroimaging36h
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivtpastarted
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivtnkstarted
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proctimeutd16
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_princicd10proced_dtutd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_nihss36iv
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: gs_jp_periodq
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_surgicalich_hr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_death36hr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure24
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_statindisc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: gs_pc_emsgcsintub
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_skin
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: mer_imnotperf
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_delayed
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proctimeutd3
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proctimeutd8
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_discdatetime_precision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: gs_pc_emsgcsmot
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proc5
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dyn_mer
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure_timeutd1
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proctimeutd2
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: mer_present
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proctimeutd13
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure11
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_nihss36ivutd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_eddepart_precision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure_timeutd18
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_reasnopro
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_medicare
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: mer_notperf
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_artpuncdt_precision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure_timeutd17
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proctimeutd21
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure_timeutd9
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_iathrominitdt
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure_timeutd4
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_nimodipine24h
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_iathrominit
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dyn_coverdell
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proctimeutd18
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure_timeutd13
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure6
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_nihssivt
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proctimeutd1
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_ichscore_value
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proc22
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure_timeutd10
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: mer_scoreobt
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_otherprocedure_timeutd6
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_inr_dt_precision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_prelipidmed
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_noanticoagdisc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_nihss
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_noantithroday2
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: jc_othericd10proctimeutd14
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_ichscoredt_precision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: timtointravthrp45den
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: timtointravthrp45ipp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: timtointravthrp30num
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: timtointravthrp30excl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: timtointravthrp30den
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: timtointravthrp30ipp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivthromarrive2num
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivthromarrive2excl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivthromarrive2den
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivthromarrive2ipp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doorindoornum
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doorindoorexcep
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doorindoorexcl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doorindoorden
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doorindooripp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: timintrventhromthertmnum
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: timintrventhromthertmexcl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: timintrventhromthertmden
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: timintrventhromthertmipp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivthromblyarrvtmnum
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivthromblyarrvtmexcl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivthromblyarrvtmden
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: timintrventhromthernum
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: timintrventhromtherexcl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: timintrventhromtherden
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: timintrventhromtheripp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: strokeducationnum
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: strokeducationexcl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: strokeducationden
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: strokeducationipp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: rehabconsidnum
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: rehabconsidexcl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: rehabconsidden
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: rehabconsidipp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: nihssreportnum
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: nihssreportexcl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: nihssreportden
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: nihssreportipp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ldldocumentednum
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ldldocumentedexcl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ldldocumentedden
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ldldocumentedipp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dysphagscreennum
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dysphagscreenexcl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dysphagscreenden
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vteprophylxnum
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vteprophylxexcl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vteprophylxden
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vteprophylxipp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: smokcessationnum
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: smokcessationexcl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: smokcessationden
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: smokcessationipp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivthromblyarrvnum
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivthromblyarrvexcl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivthromblyarrvden
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivthromblyarrvipp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: intstatthernum
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: intstattherexcep
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: intstattherexcl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: intstattherden
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: intstattheripp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: gs_notcared
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: notappranti
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: anticoagafden
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: anticoagafexcl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: anticoagafnum
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: antithrombdisipp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: antithrombdisden
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: antithrombdisexcl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: antithrombdisexcep
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: antithrombdisnum
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: earlantithrombipp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: earlantithrombden
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: earlantithrombexcl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: earlantithrombnum
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doortopunct
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doortoneuroactiv
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doortoniarriv
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vtepropnd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: novteadmis
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doortodevice
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivtpadelay30
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doortoctintp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doortoctinit
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doortoteam
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doortophys
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: lpa
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: lpand
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: lpaunit
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: imagecdt_precision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vascperfimagend
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vascimage
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vtepropaspirin
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vtepropvenftpump
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vtepropwarfarin
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vtepropfactorxa
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vteproplmwh
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vteproplduh
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: eduall
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: eduems
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: neuroactivdt_precision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: neuroarrdt_precision
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dmteaching
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: teamna
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: compafterivtpa
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_weightloss
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_bmi
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antithrombotic3class
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_returnedforfollowup
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk05mx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: xalevel
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: telethromb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: sameaslastknown
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: apttnd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_glucose
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_repeatswallow
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk12md
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk01me
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_noappreason_other
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antiplatelet
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doortostartipp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk04mp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_aspstopreas
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk05mr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk01mr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doortostartnum
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01amx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_symptoms
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_rehabtype
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk01mx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: othreversalpriorspec
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cantithrodisc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_diabtxifmissed
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: procoagulantprior
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antithrombotic3dose
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: aspectm2
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01amr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk04me
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_datefollowcomp_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: telereqresp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doortostartden
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: nimodipinedt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_holter
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: prehospscreenot
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_whomtocall
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_lvefspecify
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: othreversalspec
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01gmx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: firstpassdt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: civthroext
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_newantithrommeds
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: aspectm4
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_diabtx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_ernohosp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: bplower
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_diedsincedisc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_conducted_other
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk06me
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antithrombotic4freq
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk03amx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_eddate1_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_fupstrokedt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk06mx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01fmx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivtpanc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emssevscore
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antistopoth
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_physicalactivity
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_telemanage
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk06mx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk05mp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01amb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk03bme
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antidepresoth
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_eddate_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_newdiabtx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: firstpass
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_edvisitreason
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doortostartexcep
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsarrivescenedt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ctrepdt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: merothreasons
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk05me
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_medication
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: hypmed
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_transesophogeal
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antithrombotic1
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antithrombotic4dose
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: lobar
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antistopreas
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01emx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: sahscaledt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsscenedepdt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_postdismrsdate_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: newdmdxbasis
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: nihssobtlt6
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_rankindc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsfmctoecg
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsfirstmedcont_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk04mr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: transrechosp_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk05amx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: procoagulanttypeoth
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsptoactv
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_anticoagulant
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: aspectcaudate
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_waistcirc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_calorierestrict
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vtepropgcs
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01fmp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_hr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01dmr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: medhistnone
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_phq5
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk03mp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk01md
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_causedeath
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: telethrombdt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk03bmp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk05amd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_newanticoag
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: televideo_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: aptt
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_monitorbp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: lpatreatoth
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: lpatreat
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antithrombotic2dose
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk08mb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_peripheral
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emssevscorend
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antithromifmissed
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01hmp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk01mp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk12mx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_typeoffice
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: altetotaldose
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emssevscaleot
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_tobaccofreq
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ctcompdt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_lvefpercent
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_ldl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_disconanticoag
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_smokedsincehosp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_bpreport
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_cholredtx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk01mp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_revmed
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk03amr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_signsmi
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: uti
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_specificcause
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01bmb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01emy
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_anticoagstopoth
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01dmd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: referdischarge_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_rankin_reason
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk05me
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk06md
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: civthroinit
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_diagbasis
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antithrombotic2class
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_rankin
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_degreesten
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_statstopreas
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: socdetassess
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk09amp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_sympstroke
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_othmedstop
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_lvefdate_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk08mp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_transthoracic
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antithrombotic4class
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: referarrdt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: interfacilityemsagency
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsarrtodep
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_diabtxmisseddose
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk05mb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: bulkset1
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01imd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01hmx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_phq1
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: bplowerdt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01fmb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk05bmp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk09bmx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doortodeviceden
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_lipid
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_patientdispfirsted
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antithrombotic3
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01gmy
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_noappreasonauto
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk09bmp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsbpd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk04mp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: i10nostkdx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: aspectbasgang
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_anticoagismissed
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: intptoneedle
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: labsordertocomplete
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk06me
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01imx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk03amb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_medadherence
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: npo
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_mrct
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: subfacility
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: reversal90spec
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: modetrans
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk05amp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_postdismrsscale
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsagencyunk
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_rehablocation
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cause90delay
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01dmp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk11mp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_rehabsvcs
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk05md
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: sustsysbpdt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk01mb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: advnotice
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: priorantithromdt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: datealert_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk03mp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_phq8
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk04mb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: transrefhosp_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_anticoagstopreas
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk05bmr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: pcmodifierdate_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_cholreddosing
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: covidvacctrial
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk03ame
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cantithroday2
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01imp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emscallrecdt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: intake
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk09amx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_appprior
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: teleconsultdt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_smokecess
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_sympbreath
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk03me
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_datepostdisc_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: mernotperf
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: strokealert
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk12mp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk01mr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsfmctoevt
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: noantithroday2
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_fallreported
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: plateletutd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk10mp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk02mb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cnoantithrodisc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_actguidelines
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsbpnd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_numberhosp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: scrnd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivthrodt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: transarrdt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsntoarr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: nihssobt
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_tobacmed
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivthromblyarrvtmipp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk10mb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ichvolume
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk11mr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_followconducted
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk02md
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk08mp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk03md
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: othreversal
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstatindisc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dido
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dischchlst
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antihyperdosing
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk05mb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_typicalbpreading
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: posbrain_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: opencounterdt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_phq3
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_postdismrsscore
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_appkept
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk08mr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: nodmtxreason
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_stkafter
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: reversal60delay
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_needmedfollowup
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_phq7
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk12me
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_hemoglobin
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: timtointravthrp45num
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01gmd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_phq6
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsfmctoneed
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: iatpadt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsmsuct_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emssymtoarr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01emr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_source_other
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antithrommisseddose
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: transreqst_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_phq4
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_assessrehab
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_signsstroke
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk03me
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01emp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk09bmb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: reversalot
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antidiastopoth
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_noapp_other
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_source
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk03mx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk10mx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk08mb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ctcompsel
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsfmctoarr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk09amr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_sympcp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk06mp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_admitdate_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cause60delay
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01imb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsbgl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_comprankindate_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk05md
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk04mr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_hdl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_othmedstopreas
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_medcaresystem
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsrunsequnk
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: aspectm1
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsfmctolab
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk10me
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ichetiology
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_cholredismissed
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk09amd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk09mx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: repeatpro
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_diabeducation
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_currentmedauto
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_chemistries
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk02mr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_chol
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk03mr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_creatinine
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk09bmd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01dmy
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antismokingtx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emslkwtoarr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk05bmd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: endendodt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsfmctoctint
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01fmd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: neuroassessdt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk03mx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: othreversalprior
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_comprankinscore
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsfmctoctinit
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_diabtxdosing
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cnoanticoagdisc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: neuroassessnd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_rankin_oth
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01hmd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: procoagulantpriortypeoth
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emslkwdt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: teleexpert
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_monitorweight
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antithrombotic1dose
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivtpaorderedna
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk12mr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: thromticidt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doorvessel
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doortostartexcl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_anticoagtherapy
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk03bmd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: pltrandt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk09amy
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk06mp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: clastknowndttm_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk03mb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_rankin_lack
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cnoivthro
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk09mp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk11me
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: priorantipltdt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk08me
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: pltransfusion
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_heightunits
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dmdxfusched
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_datedeath_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_rankin_dateunk
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: edassessnd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk09my
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_dietcounseling
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ipcdoc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: earliestcstk03_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_quallvdys
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_specificcausedeath_other
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: presantithrombotic
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: i10admitdx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_pillcontainer
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_othmedstopoth
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antithrombotic1freq
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01bmd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_height
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk10mr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: reversal
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01amp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: npi
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antithrombotic2
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: edassessdt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_tobacstopnum
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emssymptomsdt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk06mr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_tobacstopattempt
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk12mb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk03mr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_phq2
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antithromdosing
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01bmp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: currpreg
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: aspectm3
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: aspectinsribb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: aspectm5
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk05mx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ipcdt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: i10pcsnd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cprelipidmed
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: canticoagdisc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: bpnddsch
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: reversal90delay
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_phq9
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: antiplateletadm
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: departeddt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_nosymp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_death
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_carotid
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_newantihypertx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_bloodsincedisc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: lkwtoarr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: telerespdt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antidepresreas
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01imy
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk10md
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01bmr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsrecordavail
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk08mx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk04me
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: reversal60spec
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk05mr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: punctorep
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: nothromcomment
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsprehospemsdt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_anticoagmisseddose
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_noapp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk04md
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: redosedt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_30dayadmit
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: weightmgmt
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_newcholtx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_medsatdc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_outpatsincedisc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doortodeviceexcl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: civoriatherapy
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: reasoncorti
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: sample2
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk09md
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antithrombotic3freq
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cdiscdatetime_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: merdelay
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: anticoagafipp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: eligmer
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_anticoagdosing
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_cholredmisseddose
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk05bmb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk08md
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_monitorbs
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: teleconsultenddt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk02mx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk05amb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_signshf
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_triglyc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk08mx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antihyperifmissed
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk04mx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_newdiagdm
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk03bmr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_numfalls
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk04mb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_waistcircunits
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doortodevicenum
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk11mx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01bmx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsrecordlater
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01dmx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk01md
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk04mx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: tlc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: aspectm6
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: aspecttotal
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01gmr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ichvolumend
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_riskfactors
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: disctoarr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk02mp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_edvisitreasonoth
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: nihsscalc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_discdate_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsbps
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01amd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: covidvaccdate_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_weightunits
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emslkwunk
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk11md
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk09mr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: scdysphagia
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: clastknown
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antithrombotic1class
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: telerecc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_numberer
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk01mx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk03amp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doortodeviceexcep
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01gmb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_modifiedrankin
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk05bme
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk09bmy
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_patientlocation
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsthromcl
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cfibflutter
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: thromexptype
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk05bmx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk09amb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antidiastopreas
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk03bmx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_bpdias
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01hmy
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: aspectintcapsule
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_rankinyn
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk06mb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_currentmed
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_strokerehab
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antithrombotic2freq
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk03md
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antihypermisseddose
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_lvef
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_ptfall
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_barthel
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_aspstopoth
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_followupdate_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01dmb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: bulkset2
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk01me
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_cov
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_hospfor
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cperiodq
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_30dayadmit_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: imagepriordt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk05ame
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: aspectnd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01emb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_tobaccouse
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ichsurgdt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: othalert
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_findings
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: nihsssource
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01imr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01emd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk02me
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: c_rankin_at
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: earliestcstk01_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk08mr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antihypertx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01fmr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_datepostdiscvisit_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsmsutpa_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk03amd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk03bmb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doortodeviceipp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_weight
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk01mb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antithrombotic4
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: dmdxfudt_p
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsbglval
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: lvo
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk03mb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: arrivaltonihss
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk05amr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk04md
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_bpsys
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk06mb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_dietspecify
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: etiologyyn
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_statstopoth
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk06mr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk09bmr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk08md
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: csexgender
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emssymptomunk
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: i10prinpcs
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_noappreason
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cholesterolprior
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: doortorep
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_fupstroketype
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stk06md
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: impagepriornd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: gs_ivtpalatenc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: i10dschstkdx
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_conducted
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01amy
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: bulkset3
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk08me
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk09mb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk05mp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: cstk11mb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01hmb
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01hmr
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ivtpadelay_other
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_antithrom
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_tobedu
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01fmy
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: fu_nomedication
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01bmy
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: stkop01gmp
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: imageprior
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: vtepropipc
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: pneumonia
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: los
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: edpatient
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: ichscore
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: nimodipine
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: emsgcsnd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: failedthrom
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: nihss36iautd
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: clinicaltrialtype
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: patorienoth
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: patgenidoth
  PHI: true
  Role: GENDER
- Dtype: varchar(10)
  Field: facility_display_id
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: form_version
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: ivh
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: mer_mca
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: thrombadmin
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: uploaded_by
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: antithrombotic1class
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: antithrombotic1freq
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: antithrombotic2class
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: antithrombotic2freq
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: antithrombotic3class
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: priorantithrom1class
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: priorantithrom2class
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: meddmtx1
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: meddmtx2
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: meddmtx3
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: meddmtx4
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: dmduration
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: scheduledformname
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: covidvaccboth
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: newdmdx
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: emssuspstroke
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: emssevdest
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: emsdispatch
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: emsposlvo
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: screenoutcome
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: emsbglnd
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: pmtused
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: cstkaddcm
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: emsrunseq
  PHI: false
  Role: null
- Dtype: decimal(6,3)
  Field: age
  PHI: true
  Role: AGE
- Dtype: varchar(30)
  Field: initialadmittingservice
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: intadmitoth
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: telestatus
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: prevstrokehistory
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: updatedby
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: firstimage
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: doac
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: teamarrivetime
  PHI: false
  Role: TIME
- Dtype: varchar(30)
  Field: raceasian
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: tiaduration
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: comfortprior
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: priorantideptype
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: localert
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: caresettingoth
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: antithrombotic2
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: antithrombotic3
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: priorantithrom1
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: priorantithrom2
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: telewhoprovide
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: vitk
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: classdmtx1
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: classdmtx2
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: classdmtx3
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: classdmtx4
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: jc_comfortonly2
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: hawaiian
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: earliestcstk01
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: earliestcstk03
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: emssevscale
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: cholreducstatin
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: imagepriorcomp
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: createdby
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: patorien
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: clientfileid
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: datasetname
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: antithrombotic1
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: emsdestdecoth
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: rankindschtype
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: ivtpadelay_hro
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: npi_att
  PHI: false
  Role: null
- Dtype: text
  Field: cipp
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: antithrom2type
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: gs_tenecreason_ot
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: cholreductx
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: npi_other
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: covidvaccmanu
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: mer_ica
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: lpaobt
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: medrecn
  PHI: true
  Role: MEDRECNUM
- Dtype: varchar(30)
  Field: zipcode
  PHI: true
  Role: PATIENT_ZIP
- Dtype: varchar(60)
  Field: npi_disc
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: telestroketype
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: npi_ed
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: npi_strokenppa
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: i10othpcstab
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: npi_neurosurgeon
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: i10prinpcstab
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: c_cartocc
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: npi_adm
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: npi_int
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: i10admitdxtab
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: i10prindxtab
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: race
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: gs_thrombo
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: sp_race
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: teleconmtd
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: firstcare
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: notadmit
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: opem
  PHI: false
  Role: null
- Dtype: varchar(35)
  Field: dob
  PHI: false
  Role: DOB|DATE
- Dtype: varchar(35)
  Field: admitdate
  PHI: false
  Role: ADMISSION_DATE|DATE
- Dtype: varchar(35)
  Field: arrivaldatetime
  PHI: false
  Role: ARRIVAL_DATE|DATE
- Dtype: varchar(35)
  Field: dcdatetime
  PHI: false
  Role: '|DATE||ANCHOR_DATE|'
- Dtype: varchar(35)
  Field: ptlastknownwelldt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: ptstrokesymptomdatetime
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: ivthromboinitiateddt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: initvteprophylaxisdatetime
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: reperfusiondatetime
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: createddatetime
  PHI: true
  Role: DATE
- Dtype: varchar(100)
  Field: antidepmed
  PHI: false
  Role: null
- Dtype: varchar(35)
  Field: ctcompletedate
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: iatpainitiationdatetime
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: ivtpaorderdate
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: ctorderdatetime
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: ctinterpretdatetime
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: ecgcompldatetime
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: ecgorderdatetime
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: assessdatetime
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: labcompdatetime
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: laborderdatetime
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: neurosurgdatetime
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: teamactivedatetime
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: xraycompdatetime
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: xrayorderdatetime
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: refhospdischargedatetime
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: refhosparrivdatetime
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: merimagedate
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: updateddatetime
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: firstpassdatetime
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: initialnihssscoredatetime
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: c_artpuncdt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: jc_discdatetime
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: mer_ticidt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: tq_encounterdt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: jc_othericd10procdate2
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: jc_othericd10procdate19
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: c_posbrain
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: jc_othericd10procdate7
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: c_nihssdt_precision
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: jc_otherproceduredate5_utd
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdate15
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdate14
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate23_utd
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdateutd17
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate1_utd
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate22
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdate5
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdateutd11
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate9_utd
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdateutd18
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_princicd10procedurdt
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdate16
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate12_utd
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdate24
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdateutd13
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate9
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate15
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdateutd21
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdateutd6
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate14_utd
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdate9
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate24_utd
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdateutd3
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdateutd14
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate20
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate19_utd
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate16
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate2
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdate1
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate8
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate8_utd
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate23
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: c_nimodipine_dt
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdate23
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdate3
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdate21
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdate10
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_princproced_dtutd
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdate12
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate3
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate21
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdate4
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdate17
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate7_utd
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate19
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdate11
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate3_utd
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdate6
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdateutd7
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdate8
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate14
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdateutd9
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate10
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate11_utd
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdateutd4
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate12
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdateutd20
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate24
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate15_utd
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdate22
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdate18
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate22_utd
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdateutd15
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate18
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate13
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdateutd2
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate11
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate18_utd
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate13_utd
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate17_utd
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdate13
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate6
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdateutd24
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate6_utd
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate5
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_otherproceduredate1
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdateutd10
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: jc_othericd10procdateutd22
  PHI: false
  Role: '|DATE|'
- Dtype: varchar(35)
  Field: imagecdt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: neuroactivdt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: neuroarrdt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: modified_dt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: priorantithromdt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: teleconsultdt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: emssymptomsdt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: departeddt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: transreqst
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: emslkwdt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: transrefhosp
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: fu_postdismrsdate
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: emsmsutpa
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: inrdt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: fu_eddate1
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: ichsurgdt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: emsmsuct
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: pltrandt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: emsarrivescenedt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: emsfirstmedcont
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: redosedt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: imagepriordt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: dmdxfudt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: fu_datedeath
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: fu_datepostdisc
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: fu_fupstrokedt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: ichscoredt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: covidvaccdate
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: teleconsultenddt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: fu_lvefdate
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: fu_eddate
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: fu_datefollowcomp
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: priorantipltdt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: bplowerdt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: fu_followupdate
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: televideo
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: fu_comprankindate
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: emsscenedepdt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: transarrdt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: emscallrecdt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: clastknowndttm
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: telerespdt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: sustsysbpdt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: ipcdt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: endendodt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: telethrombdt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: thromticidt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: referarrdt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: pcmodifierdate
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: transrechosp
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: fu_datepostdiscvisit
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: emsprehospemsdt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: opencounterdt
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: fu_admitdate
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: datealert
  PHI: true
  Role: DATE
- Dtype: varchar(35)
  Field: fu_discdate
  PHI: true
  Role: DATE
- Dtype: varchar(90)
  Field: occlusionsite
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: cholesterolreducertype_1
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: hxinfectopt
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: iatype
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: strokerelateddiagnosis
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: npi_neurologist
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: prehdata
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: dschothfac
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: etiologydocmntd
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: otheranticoag
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: initscdysphagia
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: ivtpadelay_mrspec
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: ethnicys
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: i10otherdxtab
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: methodpatientarrival
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: dischargestatus
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: dischargedisposition
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: ivcomp
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: telecomment
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: foley
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: crace
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: vascperfimage
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: emsdestdec
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: corti
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: patgenid
  PHI: true
  Role: GENDER
- Dtype: varchar(90)
  Field: dischargecodestk
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: prehospscreen
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: reasonnoantithrom
  PHI: false
  Role: null
- Dtype: varchar(50)
  Field: hospname
  PHI: false
  Role: HOSPITAL
- Dtype: varchar(90)
  Field: merreasoth
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: noivhosp2
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: noivhosp
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: addrelexcl
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: cardiacmon
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: hypercoagtest
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: carotidimaging
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: cardiacrhythmmonitoring
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: cardiacultrasound
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: gs_tenecreason
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: icmilr
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: intracranvasc
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: cardrevasc
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: addlcomment
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: otheretiology
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: ivtpadelayeligiblityreason
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: comptransf
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: surgicalichtype
  PHI: false
  Role: null
- Dtype: varchar(90)
  Field: ivtpadelay_erspec
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: strokepatientid
  PHI: true
  Role: IDENTIFIER
- Dtype: varchar(60)
  Field: uuid
  PHI: true
  Role: ANY_ID
- Dtype: varchar(60)
  Field: case_id
  PHI: true
  Role: ANY_ID
- Dtype: varchar(60)
  Field: master_patient_id
  PHI: true
  Role: ANY_ID
- Dtype: varchar(60)
  Field: facpatid
  PHI: true
  Role: ANY_ID
- Dtype: varchar(120)
  Field: locationofstrokesymptoms
  PHI: false
  Role: null
- Dtype: varchar(120)
  Field: caddstroke
  PHI: false
  Role: null
- Dtype: varchar(120)
  Field: emsagencylist
  PHI: false
  Role: null
- Dtype: varchar(120)
  Field: transferhospitalname
  PHI: false
  Role: null
- Dtype: varchar(120)
  Field: noivthromoth
  PHI: false
  Role: null
- Dtype: varchar(120)
  Field: dmmedspecadm
  PHI: false
  Role: null
- Dtype: varchar(120)
  Field: noivthromoth2
  PHI: false
  Role: null
- Dtype: varchar(120)
  Field: caresetting
  PHI: false
  Role: null
- Dtype: varchar(120)
  Field: socdetareas
  PHI: false
  Role: null
- Dtype: varchar(120)
  Field: biomeimportdt
  PHI: false
  Role: null
- Dtype: varchar(120)
  Field: activeinfec
  PHI: false
  Role: null
- Dtype: varchar(120)
  Field: smokingmed
  PHI: false
  Role: null
- Dtype: varchar(120)
  Field: payor
  PHI: false
  Role: null
- Dtype: varchar(120)
  Field: ichetiologyother
  PHI: false
  Role: null
- Dtype: varchar(120)
  Field: teledest
  PHI: false
  Role: null
- Dtype: varchar(120)
  Field: ambulatadmit
  PHI: false
  Role: null
- Dtype: varchar(120)
  Field: ambulatorystatusprior
  PHI: false
  Role: null
- Dtype: varchar(120)
  Field: funcstatus
  PHI: false
  Role: null
- Dtype: varchar(80)
  Field: careentityid
  PHI: false
  Role: CAREENTITY
- Dtype: varchar(80)
  Field: tenantid
  PHI: false
  Role: TENANT
- Dtype: text
  Field: covidvaccdoc
  PHI: false
  Role: null
- Dtype: text
  Field: noguideddosereasonintoltostatin
  PHI: false
  Role: null
- Dtype: text
  Field: strokeetiology
  PHI: false
  Role: null
- Dtype: text
  Field: notadmitreason
  PHI: false
  Role: null
- Dtype: text
  Field: whynoatrialmeds
  PHI: false
  Role: null
- Dtype: text
  Field: htntx
  PHI: false
  Role: null
- Dtype: text
  Field: emsaddcm
  PHI: false
  Role: null
- Dtype: text
  Field: emsalert
  PHI: false
  Role: null
- Dtype: text
  Field: fu_comprankin
  PHI: false
  Role: null
- Dtype: text
  Field: procoagulanttype
  PHI: false
  Role: null
- Dtype: text
  Field: procoagulantpriortype
  PHI: false
  Role: null
rename_fields:
  - New Field: transferhosp
    Old Field: transfertohospital
  - New Field: FORM_STATUS
    Old Field: Status
  - New Field: thromticigrade2
    Old Field: c_ticigrade2
  - New Field: thromticigrade
    Old Field: c_ticigrade
  - New Field: merproc
    Old Field: mer_proc
  - New Field: HEIGHT
    Old Field: Height
  - New Field: BPND
    Old Field: DischargeBPND
  - New Field: DIASTOLIC
    Old Field: DiastolicBP
  - New Field: SYSTOLIC
    Old Field: SystolicBP
  - New Field: HEARTRATE
    Old Field: HeartRate
  - New Field: INRND
    Old Field: INRNotDoc
  - New Field: INR
    Old Field: Inr
  - New Field: SCR
    Old Field: Creatinine
  - New Field: BLOODGLUCOSE_ND
    Old Field: BloodGlucoseND
  - New Field: FASTINGBLOOD
    Old Field: FastingGlucose
  - New Field: HBA1CND
    Old Field: Hba1cND
  - New Field: HBA1C
    Old Field: HBa1c
  - New Field: LPAND
    Old Field: LPAND
  - New Field: LPAUNIT
    Old Field: LPAUnit
  - New Field: LPA
    Old Field: LPA
  - New Field: LIPIDSNC
    Old Field: LipidsNC
  - New Field: LIPIDSND
    Old Field: LipidsND
  - New Field: LDL
    Old Field: LipidsLDL
  - New Field: HDL
    Old Field: HDL
  - New Field: TRIGLYCERIDES
    Old Field: Triglycerides
  - New Field: CHOLESTEROL
    Old Field: Cholesterol
  - New Field: ANTITHROM2TYPE
    Old Field: Antithrom2Type
  - New Field: DVTDOC
    Old Field: DVTdoc
  - New Field: OTHERANTICOAG
    Old Field: OtherAntiCoag
  - New Field: REASONORALXA
    Old Field: DocReasonOralFactorXa
  - New Field: NOVTEADMIS
    Old Field: NOVTEADMIS
  - New Field: VTEDATE.P
    Old Field: VTEDatePrecision
  - New Field: VTEDATE
    Old Field: InitVTEProphylaxisDateTime
  - New Field: VTEPROPND
    Old Field: VTEPROPND
  - New Field: VTEPROPASPIRIN
    Old Field: VTEPROPASPIRIN
  - New Field: VTEPROPORALFACTORXA
    Old Field: VTEInterventionOralFactorXaInhib
  - New Field: VTEPROPVENFTPUMP
    Old Field: VTEPROPVENFTPUMP
  - New Field: VTEPROPWARFARIN
    Old Field: VTEPROPWARFARIN
  - New Field: VTEPROPFACTORXA
    Old Field: VTEPROPFACTORXA
  - New Field: VTEPROPLMWH
    Old Field: VTEPROPLMWH
  - New Field: VTEPROPLDUH
    Old Field: VTEPROPLDUH
  - New Field: DYSPHAGIARESULT
    Old Field: DysphagiaResult
  - New Field: COMPTRANSF
    Old Field: comptransf
  - New Field: IACOMP
    Old Field: IAComp
  - New Field: IVCOMP
    Old Field: IVComp
  - New Field: THROMTHERCOMP
    Old Field: CompReperf
  - New Field: IATPAOUTSIDE
    Old Field: IAtPAOutsideHosp
  - New Field: IATPADT
    Old Field: IAtPAInitiationDatetime
  - New Field: IATPA
    Old Field: ProcedureIAtPA
  - New Field: THROMEXP
    Old Field: ThrombolysisProtocol
  - New Field: IVTPAOUTSIDE
    Old Field: IVtPAOutside
  - New Field: IVTPADELAY_MR
    Old Field: IVtPADelayMedReason
  - New Field: IVTPADELAY_ER
    Old Field: IVtPADelayEligiblityReason
  - New Field: IVTPADELAY30
    Old Field: ivtpadelay30
  - New Field: IVTPADELAY45
    Old Field: IVtPADelay45min
  - New Field: IVTPADELAY
    Old Field: IVtPADelay
  - New Field: EXCLUSION
    Old Field: ExclusionLessThan3hrC5
  - New Field: OTHTHROMBIMG
    Old Field: GS_THROMBIMG_OT
  - New Field: THROMBIMG
    Old Field: GS_THROMBIMG
  - New Field: OTHTENECREASON
    Old Field: GS_TENECREASON_OT
  - New Field: TENECREASON
    Old Field: GS_TENECREASON
  - New Field: TENECDOSEND
    Old Field: GS_TENECDOSEND
  - New Field: TENECTOTALDOSE
    Old Field: GS_TENECTOTALDOSE
  - New Field: ALTEDOSEND
    Old Field: GS_ALTEDOSEND
  - New Field: THROMBOUSED
    Old Field: GS_THROMBO
  - New Field: IVTHRODT
    Old Field: IvThromboInitiatedDT
  - New Field: IVTHROINIT
    Old Field: IvThrombolyisStarted
  - New Field: TARGLES
    Old Field: TargetLesionVis
  - New Field: VASCPERFIMAGEND
    Old Field: VASCPERFIMAGEND
  - New Field: IMAGECDT.P
    Old Field: IMAGECDT_PRECISION
  - New Field: IMAGECDT
    Old Field: IMAGECDT
  - New Field: VASCIMAGE
    Old Field: VASCIMAGE
  - New Field: FIRSTIMAGE
    Old Field: FIRSTIMAGE
  - New Field: CTREPDT
    Old Field: CTInterpretDateTime
  - New Field: CTCOMPDT
    Old Field: CTCompleteDate
  - New Field: CTCOMP
    Old Field: CTComplete
  - New Field: SYMPTOMDT.P
    Old Field: PTStrokeSymptomDateTimePrecision
  - New Field: SYMPTOMDT
    Old Field: PTStrokeSymptomDateTime
  - New Field: LASTKNOWNWELL.P
    Old Field: LastKnownWellPrecision
  - New Field: LASTKNOWNWELL
    Old Field: PTLastKnownWellDT
  - New Field: FLUVACC
    Old Field: FLUVACCINE
  - New Field: COVIDVACC
    Old Field: covidvaccdoc
  - New Field: PRIORANTIDEPMED
    Old Field: AntiDepMedPrior
  - New Field: DMMEDPRIOR
    Old Field: DiabeticMedPreAdmission
  - New Field: CHOLREDTYPE
    Old Field: cholesterolreducertype_1
  - New Field: HTNTXPRIOR
    Old Field: PriorHypertension
  - New Field: PRIORANTITHROM3
    Old Field: PriorAntithrom3
  - New Field: PRIORANTITHROM3CLASS
    Old Field: PriorAntithrom3Class
  - New Field: PRIORANTITHROM2
    Old Field: PriorAntithrom2
  - New Field: PRIORANTITHROM2CLASS
    Old Field: PriorAntithrom2Class
  - New Field: PRIORANTITHROM1
    Old Field: PriorAntithrom1
  - New Field: PRIORANTITHROM1CLASS
    Old Field: PriorAntithrom1Class
  - New Field: PRIORANTITHROM
    Old Field: PriorAntithrom
  - New Field: NOPRIORMEDS
    Old Field: NoMedPriorToAdmission
  - New Field: AMBULATADMIT
    Old Field: AmbulatAdmit
  - New Field: INITIALEXAM
    Old Field: InitialExamFindings1
  - New Field: TRANSFERNIHSSND
    Old Field: TransferNIHSSND
  - New Field: TRANSFERNIHSS
    Old Field: TransferNIHSS
  - New Field: NIHSS11
    Old Field: ExtinctionNIHSS
  - New Field: NIHSS10
    Old Field: DysarthriaNIHSS
  - New Field: NIHSS9
    Old Field: AphasiaNIHSS
  - New Field: NIHSS8
    Old Field: SensoryNIHSS
  - New Field: NIHSS7
    Old Field: LimbAtaxiaNIHSS
  - New Field: NIHSS6R
    Old Field: RtLegFuncNIHSS
  - New Field: NIHSS6L
    Old Field: LtLegFuncNIHSS
  - New Field: NIHSS5R
    Old Field: RtArmFuncNIHSS
  - New Field: NIHSS5L
    Old Field: LtArmFuncNIHSS
  - New Field: NIHSS4
    Old Field: ParesisNIHSS
  - New Field: NIHSS3
    Old Field: VisualNIHSS
  - New Field: NIHSS2
    Old Field: GazeNIHSS
  - New Field: NIHSS1C
    Old Field: C_NIHSS
  - New Field: NIHSS1B
    Old Field: AnsQuesNIHSS
  - New Field: NIHSS1A
    Old Field: ConsciousNIHSS
  - New Field: NIHSSSCORE
    Old Field: NIHSSTotalScore
  - New Field: NIHSSDT.P
    Old Field: C_NIHSSDT_PRECISION
  - New Field: NIHSSDT
    Old Field: InitialNIHSSscoreDateTime
  - New Field: NIHSSPERF
    Old Field: C_NIHSSPERF
  - New Field: SYMPTRESOLVE
    Old Field: ResolutionofStrokeSymptomsOnPresentation
  - New Field: TIADURATION
    Old Field: TIADuration
  - New Field: PRESTROKEMRS
    Old Field: C_PRESTROKEMRS
  - New Field: AMBULATPRIOR
    Old Field: AmbulatoryStatusPrior
  - New Field: MEDHISTSTROKE
    Old Field: PrevStrokeHistory
  - New Field: DMDURATION
    Old Field: DMDURATION
  - New Field: DMTYPE
    Old Field: DMTYPE
  - New Field: MEDHIST
    Old Field: PrevMedHistory
  - New Field: TELESTROKECONSULT
    Old Field: TelestrokeConsultation
  - New Field: NOTCARED
    Old Field: gs_notcared
  - New Field: INTADMIT
    Old Field: InitialAdmittingService
  - New Field: TRANSFERREASON
    Old Field: TransferReason
  - New Field: TRANSFERND
    Old Field: TransferFromHospitalNotDoc
  - New Field: TRANSFERNL
    Old Field: TransferFromHospitalNotListed
  - New Field: TRANSFERNAME
    Old Field: TransferHospitalName
  - New Field: REFERDISCHARGE
    Old Field: RefHospDischargeDateTime
  - New Field: PATIENTARRIVAL
    Old Field: MethodPatientArrival
  - New Field: PATIENTLOCATION
    Old Field: LocationOfStrokeSymptoms
  - New Field: ADMITSOURCE
    Old Field: AdmitSource
  - New Field: ELECTIVECAROTID
    Old Field: ElectiveCarotidInterventionAdmin
  - New Field: CLINICALTRIAL
    Old Field: ClinicalTrial
  - New Field: I10OTHERDX
    Old Field: OtherDiagICD10
  - New Field: I10PRINDX
    Old Field: ICD10diagnosis
  - New Field: DSCHSTAT
    Old Field: DischargeStatus
  - New Field: SPECREASONDELAY
    Old Field: GS_RSDELTRA
  - New Field: REASONDELAYYN
    Old Field: GS_DELINTRA
  - New Field: DISDATE.P
    Old Field: DischargeDatePrecision
  - New Field: DISDATE
    Old Field: DCDateTime
  - New Field: REASTRANSFER
    Old Field: reastransfer
  - New Field: TRANSFERHOSPND
    Old Field: TransferToHospitalNotDoc
  - New Field: TRANSFERHOSPNL
    Old Field: TransferToHospitalNotListed
  - New Field: NOTADMREAS
    Old Field: NotAdmitReason
  - New Field: ADMDT.P
    Old Field: AdmitDatePrecision
  - New Field: ADMDT
    Old Field: AdmitDate
  - New Field: NOTADM
    Old Field: NotAdmit
  - New Field: ARRDT.P
    Old Field: ArrivalDateTimePrecision
  - New Field: ARRDT
    Old Field: ArrivalDatetime
  - New Field: COMFORTONLY
    Old Field: ComfortOnlyDocumentTiming
  - New Field: ETIOLOGYCRYPT
    Old Field: EtiologyDocmntd
  - New Field: ETIOLOGYOTHER
    Old Field: OtherEtiology
  - New Field: ETIOLOGY
    Old Field: StrokeEtiology
  - New Field: STROKEMIMICS
    Old Field: StrokeMimicDiag
  - New Field: STROKETYPE
    Old Field: StrokeRelatedDiagnosis
  - New Field: HISETHNI
    Old Field: RaceOtherHispanic
  - New Field: ASIAN
    Old Field: RaceAsian
  - New Field: RACE
    Old Field: SP_RACE
  - New Field: PAYSOURCE
    Old Field: Payor
  - New Field: ZIP
    Old Field: ZipCode
  - New Field: HOMELESS
    Old Field: Homeless
  - New Field: AGE
    Old Field: Age
  - New Field: DOB.P
    Old Field: DOBPrecision
  - New Field: DOB
    Old Field: DOB
  - New Field: SEX
    Old Field: Gender
  - New Field: UPDATED_BY
    Old Field: UpdatedBy
  - New Field: CREATED_BY
    Old Field: CreatedBy
  - New Field: MODIFIED_DT
    Old Field: MODIFIED_DT
  - New Field: CREATED_DT
    Old Field: CreatedDateTime
  - New Field: FACILITY_NAME
    Old Field: facility_name
  - New Field: SCHEDULED_FORM_NAME
    Old Field: SCHEDULEDFORMNAME
  - New Field: PATIENT_DISPLAY_ID
    Old Field: StrokePatientId
  - New Field: HEIGHTU
    Old Field: HeightUnit
  - New Field: HEIGHTND
    Old Field: HeightND
  - New Field: WEIGHT
    Old Field: Weight
  - New Field: WEIGHTU
    Old Field: WeightUnit
  - New Field: WEIGHTND
    Old Field: WeightND
  - New Field: WAIST
    Old Field: Waist
  - New Field: WAISTU
    Old Field: WaistUnits
  - New Field: WAISTND
    Old Field: WaistND
  - New Field: BMI
    Old Field: BMI
  - New Field: BMIND
    Old Field: BMIND
  - New Field: TEAMACTIVATEDT
    Old Field: TeamActiveDateTime
  - New Field: TEAMACTIVATEDT.P
    Old Field: TeamActiveDateTimePrecision
  - New Field: TEAMARRIVEDT
    Old Field: TeamArriveTime
  - New Field: TEAMARRIVEDT.P
    Old Field: TeamArriveTimePrecision
  - New Field: TEAMNA
    Old Field: TEAMNA
  - New Field: EDASSESSDT
    Old Field: AssessDateTime
  - New Field: NEUROASSESSDT
    Old Field: NeuroSurgDateTime
  - New Field: CTORDDT
    Old Field: CTorderDateTime
  - New Field: CTORDDT.P
    Old Field: CTorderDateTimePrecision
  - New Field: CTNA
    Old Field: CTNA
  - New Field: IVTPAORDEREDDT
    Old Field: IVtPAOrderDate
  - New Field: IVTPAORDEREDDT.P
    Old Field: IVtPAOrderDatePrecision
  - New Field: LABORDDT
    Old Field: LabOrderDateTime
  - New Field: LABORDDT.P
    Old Field: LabOrderDateTimePrec
  - New Field: LABCOMPDT
    Old Field: LabCompDateTime
  - New Field: LABCOMPDT.P
    Old Field: LabCompDateTimePrecison
  - New Field: LABNA
    Old Field: LabNA
  - New Field: ECGORDDT
    Old Field: ECGorderDateTime
  - New Field: ECGORDDT.P
    Old Field: ECGorderDateTimePrecision
  - New Field: ECGCOMPDT
    Old Field: ECGcomplDateTime
  - New Field: ECGCOMPDT.P
    Old Field: ECGcomplDateTimePrecision
  - New Field: ECGNA
    Old Field: ECGNA
  - New Field: CXRORDERDT
    Old Field: XRayOrderDateTime
  - New Field: CXRORDERDT.P
    Old Field: XRayOrderDateTimePrecision
  - New Field: CXRCOMPDT
    Old Field: XRayCompDateTime
  - New Field: CXRCOMPDT.P
    Old Field: XRayCompDateTimePrecision
  - New Field: CXRNA
    Old Field: XRayNA
  - New Field: NEUROACTIVDT
    Old Field: NEUROACTIVDT
  - New Field: NEUROACTIVDT.P
    Old Field: NEUROACTIVDT_PRECISION
  - New Field: NEUROARRDT
    Old Field: NEUROARRDT
  - New Field: NEUROARRDT.P
    Old Field: NEUROARRDT_PRECISION
  - New Field: DOORTOPHYS
    Old Field: DOORTOPHYS
  - New Field: DOORTOTEAM
    Old Field: DOORTOTEAM
  - New Field: DOORTOCTINIT
    Old Field: DOORTOCTINIT
  - New Field: DOORTOCTINTP
    Old Field: DOORTOCTINTP
  - New Field: DOORTONEEDLE
    Old Field: DOORTONEEDLE
  - New Field: DOORTONEUROACTIV
    Old Field: DOORTONEUROACTIV
  - New Field: DOORTONIARRIV
    Old Field: DOORTONIARRIV
  - New Field: DOORTOPUNCT
    Old Field: DOORTOPUNCT
  - New Field: DOORTODEVICE
    Old Field: DOORTODEVICE
  - New Field: ISCHEMICRISK
    Old Field: GS_ISCHEMICRISK
  - New Field: GLOBALRISK
    Old Field: GS_GLOBALRISK
  - New Field: RANKINDSCH
    Old Field: RankinDischargePerformed
  - New Field: RANKINDSCHTOTAL
    Old Field: RankinDischargeScore
  - New Field: FUNCSTATUS
    Old Field: FUNCSTATUS
  - New Field: SYSDSCH
    Old Field: DischargeSystolic
  - New Field: DIASDSCH
    Old Field: DischargeDiastolic
  - New Field: CLASSANTITHROMBOTIC1
    Old Field: Antithrombotic1Class
  - New Field: MEDANTITHROMBOTIC1
    Old Field: Antithrombotic1
  - New Field: DOSEANTITHROMBOTIC1
    Old Field: Antithrombotic1Dose
  - New Field: FREQANTITHROMBOTIC1
    Old Field: Antithrombotic1Freq
  - New Field: CLASSANTITHROMBOTIC2
    Old Field: Antithrombotic2Class
  - New Field: MEDANTITHROMBOTIC2
    Old Field: Antithrombotic2
  - New Field: DOSEANTITHROMBOTIC2
    Old Field: Antithrombotic2Dose
  - New Field: FREQANTITHROMBOTIC2
    Old Field: Antithrombotic2Freq
  - New Field: CLASSANTITHROMBOTIC3
    Old Field: Antithrombotic3Class
  - New Field: MEDANTITHROMBOTIC3
    Old Field: Antithrombotic3
  - New Field: DOSEANTITHROMBOTIC3
    Old Field: Antithrombotic3Dose
  - New Field: FREQANTITHROMBOTIC3
    Old Field: Antithrombotic3Freq
  - New Field: CLASSANTITHROMBOTIC4
    Old Field: Antithrombotic4Class
  - New Field: MEDANTITHROMBOTIC4
    Old Field: Antithrombotic4
  - New Field: DOSEANTITHROMBOTIC4
    Old Field: Antithrombotic4Dose
  - New Field: FREQANTITHROMBOTIC4
    Old Field: Antithrombotic4Freq
  - New Field: NOTAPPRANTI
    Old Field: notappranti
  - New Field: PERSISTENTAFIB
    Old Field: PersistentAFib
  - New Field: AFIBDISCHARGE
    Old Field: DischAtrialRhythmAFib
  - New Field: CHOLREDTX
    Old Field: CholReducTx
  - New Field: STATINMED
    Old Field: CholReducStatin
  - New Field: STATINDOSE
    Old Field: CholReducStatinDose
  - New Field: NOGUIDEDOSE
    Old Field: NoGuidedDoseReasonIntolToStatin
  - New Field: NOSTATINDISC
    Old Field: CholReducOthermed
  - New Field: PRESDMTX
    Old Field: PRESDMTX
  - New Field: CLASSDMTX1
    Old Field: CLASSDMTX1
  - New Field: MEDDMTX1
    Old Field: MEDDMTX1
  - New Field: CLASSDMTX2
    Old Field: CLASSDMTX2
  - New Field: MEDDMTX2
    Old Field: MEDDMTX2
  - New Field: CLASSDMTX3
    Old Field: CLASSDMTX3
  - New Field: MEDDMTX3
    Old Field: MEDDMTX3
  - New Field: CLASSDMTX4
    Old Field: CLASSDMTX4
  - New Field: MEDDMTX4
    Old Field: MEDDMTX4
  - New Field: ANTISMOKINGTX
    Old Field: AntiSmokingTx
  - New Field: PRESANTIDEPMED
    Old Field: AntiDepMed
  - New Field: SODIUMDIET
    Old Field: EduSodiumDiet
  - New Field: DMTEACHING
    Old Field: DMTEACHING
  - New Field: EDUALL
    Old Field: EDUALL
  - New Field: EDUSTRK
    Old Field: EduStrokeRisk
  - New Field: EDUWARNING
    Old Field: EduStrokeWarningSigns
  - New Field: EDUEMS
    Old Field: EDUEMS
  - New Field: EDUFOLLOWUP
    Old Field: EduFollowup
  - New Field: EDUDISMED
    Old Field: EduMeds
  - New Field: ASSESSREHAB
    Old Field: AssessedRehab
  - New Field: REHABSVCS
    Old Field: RehabSvcs
  - New Field: CARDULTRASOUND
    Old Field: CardiacUltrasound
  - New Field: CAROTIDIMAGING
    Old Field: CarotidImaging
  - New Field: HYPERCOAGTEST
    Old Field: HypercoagTest
  - New Field: EXTCARDRHYTHMMON
    Old Field: CardiacRhythmMonitoring
  - New Field: SHORTCARDRHYTHMMON
    Old Field: CardiacMon
  - New Field: PMTUSED
    Old Field: PMTUsed
  - New Field: ADMORDER
    Old Field: AdmOrderSetUsed
  - New Field: PTCONTRACT
    Old Field: PtContractUsed
  - New Field: ANTICOAGAFDEN
    Old Field: anticoagafden
  - New Field: ANTICOAGAFEXCL
    Old Field: anticoagafexcl
  - New Field: ANTICOAGAFNUM
    Old Field: anticoagafnum
  - New Field: ANTITHROMBDISIPP
    Old Field: antithrombdisipp
  - New Field: ANTITHROMBDISDEN
    Old Field: antithrombdisden
  - New Field: ANTITHROMBDISEXCL
    Old Field: antithrombdisexcl
  - New Field: ANTITHROMBDISEXCEP
    Old Field: antithrombdisexcep
  - New Field: ANTITHROMBDISNUM
    Old Field: antithrombdisnum
  - New Field: EARLANTITHROMBIPP
    Old Field: earlantithrombipp
  - New Field: EARLANTITHROMBDEN
    Old Field: earlantithrombden
  - New Field: EARLANTITHROMBEXCL
    Old Field: earlantithrombexcl
  - New Field: EARLANTITHROMBNUM
    Old Field: earlantithrombnum
  - New Field: INTSTATTHERIPP
    Old Field: intstattheripp
  - New Field: INTSTATTHERDEN
    Old Field: intstattherden
  - New Field: INTSTATTHEREXCL
    Old Field: intstattherexcl
  - New Field: INTSTATTHEREXCEP
    Old Field: intstattherexcep
  - New Field: INTSTATTHERNUM
    Old Field: intstatthernum
  - New Field: IVTHROMBLYARRVIPP
    Old Field: ivthromblyarrvipp
  - New Field: IVTHROMBLYARRVDEN
    Old Field: ivthromblyarrvden
  - New Field: IVTHROMBLYARRVEXCL
    Old Field: ivthromblyarrvexcl
  - New Field: IVTHROMBLYARRVNUM
    Old Field: ivthromblyarrvnum
  - New Field: SMOKCESSATIONIPP
    Old Field: SMOKCESSATIONIPP
  - New Field: SMOKCESSATIONDEN
    Old Field: SMOKCESSATIONDEN
  - New Field: SMOKCESSATIONEXCL
    Old Field: SMOKCESSATIONEXCL
  - New Field: SMOKCESSATIONNUM
    Old Field: SMOKCESSATIONNUM
  - New Field: VTEPROPHYLXIPP
    Old Field: VTEPROPHYLXIPP
  - New Field: VTEPROPHYLXDEN
    Old Field: VTEPROPHYLXDEN
  - New Field: VTEPROPHYLXEXCL
    Old Field: VTEPROPHYLXEXCL
  - New Field: VTEPROPHYLXNUM
    Old Field: VTEPROPHYLXNUM
  - New Field: DYSPHAGSCREENIPP
    Old Field: DYSPHAGSCREENEXCL
  - New Field: DYSPHAGSCREENDEN
    Old Field: DYSPHAGSCREENDEN
  - New Field: DYSPHAGSCREENNUM
    Old Field: DYSPHAGSCREENNUM
  - New Field: LDLDOCUMENTEDIPP
    Old Field: ldldocumentedipp
  - New Field: LDLDOCUMENTEDDEN
    Old Field: ldldocumentedden
  - New Field: LDLDOCUMENTEDEXCL
    Old Field: ldldocumentedexcl
  - New Field: LDLDOCUMENTEDNUM
    Old Field: ldldocumentednum
  - New Field: NIHSSREPORTIPP
    Old Field: nihssreportipp
  - New Field: NIHSSREPORTDEN
    Old Field: nihssreportden
  - New Field: NIHSSREPORTEXCL
    Old Field: nihssreportexcl
  - New Field: NIHSSREPORTNUM
    Old Field: nihssreportnum
  - New Field: REHABCONSIDIPP
    Old Field: REHABCONSIDIPP
  - New Field: REHABCONSIDDEN
    Old Field: REHABCONSIDDEN
  - New Field: REHABCONSIDEXCL
    Old Field: REHABCONSIDEXCL
  - New Field: REHABCONSIDNUM
    Old Field: REHABCONSIDNUM
  - New Field: STROKEDUCATIONIPP
    Old Field: strokeducationipp
  - New Field: STROKEDUCATIONDEN
    Old Field: strokeducationden
  - New Field: STROKEDUCATIONEXCL
    Old Field: strokeducationexcl
  - New Field: STROKEDUCATIONNUM
    Old Field: strokeducationnum
  - New Field: TIMINTRVENTHROMTHERIPP
    Old Field: timintrventhromtheripp
  - New Field: TIMINTRVENTHROMTHERDEN
    Old Field: timintrventhromtherden
  - New Field: TIMINTRVENTHROMTHEREXCL
    Old Field: timintrventhromtherexcl
  - New Field: TIMINTRVENTHROMTHERNUM
    Old Field: timintrventhromthernum
  - New Field: IVTHROMBLYARRVTMDEN
    Old Field: ivthromblyarrvtmden
  - New Field: IVTHROMBLYARRVTMEXCL
    Old Field: ivthromblyarrvtmexcl
  - New Field: IVTHROMBLYARRVTMNUM
    Old Field: ivthromblyarrvtmnum
  - New Field: TIMINTRVENTHROMTHERTMIPP
    Old Field: timintrventhromthertmipp
  - New Field: TIMINTRVENTHROMTHERTMDEN
    Old Field: TIMINTRVENTHROMTHERTMDEN
  - New Field: TIMINTRVENTHROMTHERTMEXCL
    Old Field: timintrventhromthertmexcl
  - New Field: TIMINTRVENTHROMTHERTMNUM
    Old Field: timintrventhromthertmnum
  - New Field: DOORINDOORIPP
    Old Field: DOORINDOORIPP
  - New Field: DOORINDOORDEN
    Old Field: DOORINDOORDEN
  - New Field: DOORINDOOREXCL
    Old Field: DOORINDOOREXCL
  - New Field: DOORINDOOREXCEP
    Old Field: DOORINDOOREXCEP
  - New Field: DOORINDOORNUM
    Old Field: DOORINDOORNUM
  - New Field: IVTHROMARRIVE2IPP
    Old Field: IVTHROMARRIVE2IPP
  - New Field: IVTHROMARRIVE2DEN
    Old Field: IVTHROMARRIVE2DEN
  - New Field: IVTHROMARRIVE2EXCL
    Old Field: IVTHROMARRIVE2EXCL
  - New Field: IVTHROMARRIVE2NUM
    Old Field: IVTHROMARRIVE2NUM
  - New Field: TIMTOINTRAVTHRP30IPP
    Old Field: TIMTOINTRAVTHRP30IPP
  - New Field: TIMTOINTRAVTHRP30DEN
    Old Field: TIMTOINTRAVTHRP30DEN
  - New Field: TIMTOINTRAVTHRP30EXCL
    Old Field: TIMTOINTRAVTHRP30EXCL
  - New Field: TIMTOINTRAVTHRP30NUM
    Old Field: TIMTOINTRAVTHRP30NUM
  - New Field: TIMTOINTRAVTHRP45IPP
    Old Field: TIMTOINTRAVTHRP45IPP
  - New Field: TIMTOINTRAVTHRP45DEN
    Old Field: TIMTOINTRAVTHRP45DEN
  - New Field: TIMTOINTRAVTHRP45EXCL
    Old Field: TIMTOINTRAVTHRP45EXCL
  - New Field: fu_fupstrokedt.p
    Old Field: fu_fupstrokedt_p
  - New Field: artpuncdt.p
    Old Field: C_ARTPUNCDT_PRECISION
  - New Field: artpuncdt
    Old Field: C_Artpuncdt
  - New Field: fu_datepostdiscvisit.p
    Old Field: fu_datepostdiscvisit_p
  - New Field: thromticidt.p
    Old Field: thromticidt_p
  - New Field: priorantipltdt.p
    Old Field: priorantipltdt_p
  - New Field: lastknownwell.p
    Old Field: lastknownwell_p
  - New Field: emsprehospemsdt.p
    Old Field: emsprehospemsdt_p
  - New Field: endendodt.p
    Old Field: endendodt_p
  - New Field: teamarrivedt.p
    Old Field: teamarrivedt_p
  - New Field: dmdxfudt.p
    Old Field: dmdxfudt_p
  - New Field: clastknowndttm.p
    Old Field: clastknowndttm_p
  - New Field: departeddt.p
    Old Field: departeddt_p
  - New Field: emscallrecdt.p
    Old Field: emscallrecdt_p
  - New Field: teleconsultdt.p
    Old Field: teleconsultdt_p
  - New Field: posbrain.p
    Old Field: posbrain_p
  - New Field: datealert.p
    Old Field: datealert_p
  - New Field: iatpadt.p
    Old Field: iatpadt_p
  - New Field: fu_eddate.p
    Old Field: fu_eddate_p
  - New Field: fu_lvefdate.p
    Old Field: fu_lvefdate_p
  - New Field: earliestcstk01.p
    Old Field: earliestcstk01_p
  - New Field: sustsysbpdt.p
    Old Field: sustsysbpdt_p
  - New Field: ctorddt.p
    Old Field: ctorddt_p
  - New Field: access case
    Old Field: accesscase
  - New Field: fu_datedeath.p
    Old Field: fu_datedeath_p
  - New Field: ichsurgdt.p
    Old Field: ichsurgdt_p
  - New Field: imagepriordt.p
    Old Field: imagepriordt_p
  - New Field: bplowerdt.p
    Old Field: bplowerdt_p
  - New Field: ivthrodt.p
    Old Field: ivthrodt_p
  - New Field: ecgorddt.p
    Old Field: ecgorddt_p
  - New Field: edassessdt.p
    Old Field: edassessdt_p
  - New Field: labcompdt.p
    Old Field: labcompdt_p
  - New Field: ctcompdt.p
    Old Field: ctcompdt_p
  - New Field: emsarrivescenedt.p
    Old Field: emsarrivescenedt_p
  - New Field: fu_datefollowcomp.p
    Old Field: fu_datefollowcomp_p
  - New Field: cxrcompdt.p
    Old Field: cxrcompdt_p
  - New Field: priorantithromdt.p
    Old Field: priorantithromdt_p
  - New Field: referarrdt.p
    Old Field: referarrdt_p
  - New Field: fu_30dayadmit.p
    Old Field: fu_30dayadmit_p
  - New Field: earliestcstk03.p
    Old Field: earliestcstk03_p
  - New Field: emsmsuct.p
    Old Field: emsmsuct_p
  - New Field: vtedate.p
    Old Field: vtedate_p
  - New Field: neuroarrdt.p
    Old Field: neuroarrdt_p
  - New Field: ecgcompdt.p
    Old Field: ecgcompdt_p
  - New Field: transreqst.p
    Old Field: transreqst_p
  - New Field: imagecdt.p
    Old Field: imagecdt_p
  - New Field: i10dschstkdx-description
    Old Field: i10dschstkdx_description
  - New Field: pcmodifierdate.p
    Old Field: pcmodifierdate_p
  - New Field: teleconsultenddt.p
    Old Field: teleconsultenddt_p
  - New Field: neuroassessdt.p
    Old Field: neuroassessdt_p
  - New Field: transarrdt.p
    Old Field: transarrdt_p
  - New Field: fu_discdate.p
    Old Field: fu_discdate_p
  - New Field: pltrandt.p
    Old Field: pltrandt_p
  - New Field: ipcdt.p
    Old Field: ipcdt_p
  - New Field: telethrombdt.p
    Old Field: telethrombdt_p
  - New Field: symptomdt.p
    Old Field: symptomdt_p
  - New Field: redosedt.p
    Old Field: redosedt_p
  - New Field: covidvaccdate.p
    Old Field: covidvaccdate_p
  - New Field: emslkwdt.p
    Old Field: emslkwdt_p
  - New Field: emsmsutpa.p
    Old Field: emsmsutpa_p
  - New Field: telerespdt.p
    Old Field: telerespdt_p
  - New Field: emssymptomsdt.p
    Old Field: emssymptomsdt_p
  - New Field: referdischarge.p
    Old Field: referdischarge_p
  - New Field: emsfirstmedcont.p
    Old Field: emsfirstmedcont_p
  - New Field: i10otherdx-description
    Old Field: i10otherdx_description
  - New Field: emsscenedepdt.p
    Old Field: emsscenedepdt_p
  - New Field: firstpassdt.p
    Old Field: firstpassdt_p
  - New Field: firstpassdt
    Old Field: FirstPassDateTime
  - New Field: fu_datepostdisc.p
    Old Field: fu_datepostdisc_p
  - New Field: opencounterdt.p
    Old Field: opencounterdt_p
  - New Field: laborddt.p
    Old Field: laborddt_p
  - New Field: ivtpaordereddt.p
    Old Field: ivtpaordereddt_p
  - New Field: teamactivatedt.p
    Old Field: teamactivatedt_p
  - New Field: fu_comprankindate.p
    Old Field: fu_comprankindate_p
  - New Field: cxrorderdt.p
    Old Field: cxrorderdt_p
  - New Field: neuroactivdt.p
    Old Field: neuroactivdt_p
  - New Field: transrefhosp.p
    Old Field: transrefhosp_p
  - New Field: fu_admitdate.p
    Old Field: fu_admitdate_p
  - New Field: televideo.p
    Old Field: televideo_p
  - New Field: fu_followupdate.p
    Old Field: fu_followupdate_p
  - New Field: fu_postdismrsdate.p
    Old Field: fu_postdismrsdate_p
  - New Field: transrechosp.p
    Old Field: transrechosp_p
  - New Field: fu_eddate1.p
    Old Field: fu_eddate1_p
  - New Field: i10prindx-description
    Old Field: i10prindx_description
  - New Field: ctrepdt.p
    Old Field: ctrepdt_p
  - New Field: procoagulantdt.P
    Old Field: C_PROCOAGULANT_DT_PRECISION
  - New Field: procoagulantdt
    Old Field: C_PROCOAGULANT_DT
  - New Field: procoagulant
    Old Field: C_PROCOAGULANT
  - New Field: nihss36iautd
    Old Field: NIHSS36IAutd
  - New Field: nihss36ia
    Old Field: C_NIHSS36IA
  - New Field: nihssiatpautd
    Old Field: C_NIHSSIATPAUTD
  - New Field: nihssiatpamer
    Old Field: C_NIHSSIATPAMER
  - New Field: merscoreobt
    Old Field: MER_SCOREOBT
  - New Field: nihss36ivutd
    Old Field: C_NIHSS36IVUTD
  - New Field: nihss36iv
    Old Field: C_NIHSS36IV
  - New Field: nihssivtutd
    Old Field: C_NIHSSIVTUTD
  - New Field: nihssivt
    Old Field: C_NIHSSIVT
  - New Field: merivscoreobt
    Old Field: MER_IVSCOREOBT
  - New Field: posbrainresult
    Old Field: C_POSBrain_Result
  - New Field: posbrain
    Old Field: C_POSBrain
  - New Field: neuroimaging36h
    Old Field: C_NEUROIMAGING36H
  - New Field: cartocc
    Old Field: c_cartocc
  - New Field: proxdist
    Old Field: C_PROXDIST
  - New Field: failedthrom
    Old Field: FailedThrom
  - New Field: ivtpaprior
    Old Field: C_IVTPAPRIOR
  - New Field: skin
    Old Field: C_SKIN
  - New Field: delayed
    Old Field: C_DELAYED
  - New Field: iathrominitdt.P
    Old Field: C_IATHROMINITDT_PRECISION
  - New Field: iathrominitdt
    Old Field: C_IATHROMINITDT
  - New Field: iathrominit
    Old Field: C_IATHROMINIT
  - New Field: iaroutetpa
    Old Field: C_IAROUTETPA
  - New Field: inrperf
    Old Field: C_INRPERF
  - New Field: platelet
    Old Field: C_PLATELET
  - New Field: ichfuncscore
    Old Field: C_FUNCSCORE
  - New Field: ichscoredt.P
    Old Field: C_ICHSCOREDT_PRECISION
  - New Field: ichscorevalue
    Old Field: C_ICHSCORE_VALUE
  - New Field: ichscore
    Old Field: ICHScore
  - New Field: wfnssah
    Old Field: C_WFNSSAH
  - New Field: sahscaledt.P
    Old Field: C_SAHSCALEDT_PRECISION
  - New Field: sahscaledt
    Old Field: C_SAHSCALEDT
  - New Field: sahscalevalue
    Old Field: C_SAHSCALE_VALUE
  - New Field: sahscale
    Old Field: C_SAHSCALE
  - New Field: sahna
    Old Field: C_SAHNA
  - New Field: emsgcsnd
    Old Field: EmsGCSND
  - New Field: emsgcstot
    Old Field: C_EMSGCSTOT
  - New Field: emsgcsmot
    Old Field: C_EMSGCSMOT
  - New Field: emsgcsintub
    Old Field: C_EMSGCSINTUB
  - New Field: emsgcsvoic
    Old Field: C_EMSGCSVOIC
  - New Field: emsgcseye
    Old Field: C_EMSGCSEYE
  - New Field: nihssobtutd
    Old Field: C_NIHSSOBTUTD
  - New Field: directadmit
    Old Field: C_DIRECTADM
  - New Field: edpatient
    Old Field: EDPatient
  - New Field: clinicaltrial
    Old Field: ClinicalTrial
  - New Field: I10admitdxtab
    Old Field: i10admitdxtab
  - New Field: I10admitdx-Description
    Old Field: ADMITTINGDIAGNOSISICD10
  - New Field: I10admitdx
    Old Field: i10admitdx
  - New Field: I10othpcstab
    Old Field: i10othpcstab
  - New Field: I10prinpcstab
    Old Field: i10prinpcstab
  - New Field: I10prinpcs-Description
    Old Field: JC_PrincICD10Procedure
  - New Field: I10prinpcs
    Old Field: i10prinpcs
  - New Field: I10pcsnd
    Old Field: i10pcsnd
  - New Field: I10otherdxtab
    Old Field: i10otherdxtab
  - New Field: I10prindxtab
    Old Field: i10prindxtab
  - New Field: notstroke
    Old Field: JC_NOTSTROKE
  - New Field: medicarejc
    Old Field: JC_MEDICARE
  - New Field: UPLOADED_BY
    Old Field: uploaded_by
  - New Field: FACILITY_DISPLAY_ID
    Old Field: facility_display_id
  - New Field: FORM_VERSION
    Old Field: form_version
  - New Field: MASTER_PATIENT_ID
    Old Field: master_patient_id
  - New Field: "\u02DC?CASE_ID"
    Old Field: Case_Id
  - New Field: reasnopro
    Old Field: C_REASNOPRO
  - New Field: inrdt.P
    Old Field: C_INR_DT_PRECISION
  - New Field: inraftertx
    Old Field: C_INRAFTERTX
  - New Field: nimodipine
    Old Field: NIMODIPINE
  - New Field: nimodipinedt
    Old Field: C_NIMODIPINE_DT
  - New Field: nimodipinedt.P
    Old Field: C_NIMODIPINE_DT_PRECISION
  - New Field: reasonnonim
    Old Field: C_REASONNONIM
  - New Field: surgicalich
    Old Field: C_SURGICALICH
  - New Field: surgicalichhr
    Old Field: C_SURGICALICH_HR
  - New Field: ccomfortonly2
    Old Field: JC_COMFORTONLY2
  - New Field: cdiscdatetime
    Old Field: JC_DISCDATETIME
  - New Field: cdiscdatetime.P
    Old Field: JC_DISCDATETIME_PRECISION
  - New Field: cdiscdtutd
    Old Field: C_DISCDTUTD
value_mapping:
- Field: heightunit
  Value Map:
    '1': in
    '2': cm
- Field: admordersetused
  Value Map:
    '1': 'Yes'
    '2': 'No'
- Field: notadmit
  Value Map:
    '1': Yes, not admitted
    '2': No, patient admitted as inpatient
- Field: ltlegfuncnihss
  Value Map:
    U-Untestablee (Joint fused or limb amputated): U-Untestable (Joint fused or limb amputated)
- Field: strokemimicdiag
  Value Map:
    '1': Migraine
    '2': Seizure
    '3': Delirium
    '4': Electrolyte or metabolic imbalance
    '5': Functional disorder
    '6': Other
    '7': Uncertain
- Field: lipidsnc
  Value Map:
    '1': Checked
    'True': Checked
- Field: xrayna
  Value Map:
    '1': Checked
- Field: labna
  Value Map:
    '1': Checked
- Field: ecgna
  Value Map:
    '1': Checked
    'True': Checked
- Field: ctna
  Value Map:
    '1': Checked
- Field: iatpainitiationdatetime
  Value Map:
    '5': MM/DD/YYYY HH24:MM
    '3': MM/DD/YYYY
    '0': Unknown
- Field: homeless
  Value Map:
    '1': Checked
- Field: pmtused
  Value Map:
    '1': Concurrently
    '2': Retrospectively
    '3': Combination
- Field: heightnd
  Value Map:
    '1': Checked
    'True': Checked
- Field: hba1cnd
  Value Map:
    '1': Checked
    'True': Checked
- Field: dysphagiaresult
  Value Map:
    '1': Pass
    '2': Fail
    '3': ND
- Field: dvtdoc
  Value Map:
    '1': 'Yes'
    '2': No/ND
- Field: dischargebpnd
  Value Map:
    '1': Checked
    'True': Checked
- Field: ctcomplete
  Value Map:
    '1': 'Yes'
    '2': No/ND
    '3': NC
- Field: bmind
  Value Map:
    '1': Checked
    'True': Checked
- Field: bloodglucosend
  Value Map:
    '1': ND
    '2': Too Low
    '3': Too High
- Field: antithrombotic3freq
  Value Map:
    '2': Every Day
    '3': 4 times a day
    '4': 5 times a day
    '6': Other
    '7': Unknown
- Field: antithrombotic3dose
  Value Map:
    '300': 2 mg
    '301': 2.5 mg
    '302': 3 mg
    '303': 4 mg
    '304': 5 mg
    '305': 6 mg
    '306': 7.5 mg
    '307': Other
    '308': Unknown
- Field: antithrombotic3class
  Value Map:
    '1': Antiplatelet
    '2': Anticoagulant
- Field: weightunit
  Value Map:
    '1': lb
    '2': kg
- Field: noguideddosereasonintoltostatin
  Value Map:
    Intolerant to moderate (greater than 75yr) or high (less than or equal to 75yr) intensity statin: 'Yes'
    '1': 'Yes'
- Field: priorhypertension
  Value Map:
    Hypertension: 'Yes'
    '1': 'Yes'
- Field: exclusionlessthan3hrc5
  Value Map:
    'C5: Acute bleeding diathesis (low platelet count, increased PTT, INR >= 1.7 or use of NOAC)': 'Yes'
    '1': 'Yes'
- Field: cholreducothermed
  Value Map:
    Other med: 'Yes'
    '1': 'Yes'
- Field: cholreducstatin
  Value Map:
    Statin: 'Yes'
    '1': 'Yes'
- Field: antismokingtx
  Value Map:
    '1': 'Yes'
    '2': No/ND
    '3': NC
- Field: antidepmedprior
  Value Map:
    '1': 'Yes'
    '2': No/ND
- Field: targetlesionvis
  Value Map:
    '1': 'Yes'
    '2': No/ND
- Field: vtedateprecision
  Value Map:
    '5': MM/DD/YYYY HH24:MM
    '3': MM/DD/YYYY
    '0': Unknown
- Field: ivtpadelay45min
  Value Map:
    '1': 'Yes'
    '2': 'No'
- Field: ivtpadelay
  Value Map:
    '1': 'Yes'
    '2': 'No'
- Field: antithrombotic3
  Value Map:
    '3': aspirin
    '4': ASA/dipyridamole (Aggrenox)
    '6': clopidogrel (Plavix)
    '7': ticlopidine (Ticlid)
    '8': Unfractionated heparin IV
    '9': full dose LMW heparin
    '5': 'warfarin (Coumadin) '
    '16': dabigatran (Pradaxa)
    '15': argatroban
    '12': fondaparinux (Arixtra)
    '14': rivaroxaban (Xarelto)
    '19': apixaban (Eliquis)
    '18': lepirudin (Refludan)
    dipyridamole/aspirin (Aggrenox): ASA/dipyridamole (Aggrenox)
    prasugrel (Effient) *contraindication in stroke and TIA: prasugrel (Effient)
    Other anticoagulant: Other Anticoagulant
- Field: weightnd
  Value Map:
    '1': Checked
    'True': Checked
- Field: waistnd
  Value Map:
    '1': Checked
    'True': Checked
- Field: transfernihssnd
  Value Map:
    '1': Checked
    'True': Checked
- Field: tiaduration
  Value Map:
    '1': Less then 10 minutes
    '2': "10 \u2013 59 minutes"
    '3': '>= 60 minutes'
    '4': ND
- Field: thrombolysisprotocol
  Value Map:
    '1': 'Yes'
    '2': 'No'
- Field: ptcontractused
  Value Map:
    '1': 'Yes'
    '2': 'No'
- Field: priorantithrom3class
  Value Map:
    '1': Antiplatelet
    '2': Anticoagulant
- Field: priorantithrom3
  Value Map:
    '3': 'aspirin '
    '4': 'ASA/dipyridamole (Aggrenox) '
    '6': 'clopidogrel (Plavix) '
    '20': 'prasugrel (Effient) '
    '21': 'ticagrelor (Brilinta) '
    '7': 'ticlopidine (Ticlid) '
    '11': 'Other Antiplatelet '
    '8': 'Unfractionated heparin IV '
    '9': 'full dose LMW heparin '
    '5': 'warfarin (Coumadin) '
    '16': 'dabigatran (Pradaxa) '
    '15': 'argatroban '
    '17': 'desirudin (Iprivask) '
    '12': 'fondaparinux (Arixtra) '
    '14': 'rivaroxaban (Xarelto) '
    '19': 'apixaban (Eliquis) '
    '18': 'lepirudin (Refludan) '
    '13': 'Other Anticoagulant '
- Field: priorantithrom2class
  Value Map:
    '1': Antiplatelet
    '2': Anticoagulant
- Field: priorantithrom1class
  Value Map:
    '1': Antiplatelet
    '2': Anticoagulant
- Field: priorantithrom
  Value Map:
    '1': 'Yes'
    '2': No/ND
- Field: diabeticmedpreadmission
  Value Map:
    '1': 'Yes'
    '2': No/ND
- Field: edumeds
  Value Map:
    '1': 'Yes'
    '2': 'No'
- Field: edustrokerisk
  Value Map:
    '1': 'Yes'
    '2': 'No'
- Field: edusodiumdiet
  Value Map:
    '1': 'Yes'
    '2': No/ND
    '3': NC
- Field: cholreducstatindose
  Value Map:
    '100': 20/500 mg
    '101': 20/750 mg
    '102': 20/1000 mg
    '103': 40/1000 mg
    '105': Unknown
    '150': 20 mg
    '151': 40 mg
    '152': 60 mg
    '154': Unknown
    '200': 2.5/10 mg
    '201': 2.5/20 mg
    '202': 2.5/40 mg
    '203': 5/10 mg
    '204': 5/20 mg
    '205': 5/40 mg
    '206': 5/80 mg
    '207': 10/10 mg
    '208': 10/20 mg
    '209': 10/40 mg
    '210': 10/80 mg
    '212': Unknown
    '300': 5 mg
    '301': 10 mg
    '302': '>= 20 mg'
    '304': Unknown
    '400': 20 mg
    '401': 40 mg
    '402': 80 mg
    '404': Unknown
    '500': 10 mg
    '501': 20 mg
    '502': '>= 40 mg'
    '504': Unknown
    '650': 1 mg
    '651': 2 mg
    '652': 4 mg
    '656': Unknown
    '704': 10 mg
    '700': 20 mg
    '701': 40 mg
    '703': Unknown
    '800': 10 mg
    '801': 20 mg
    '802': 40 mg
    '803': 80 mg
    '805': Unknown
    '900': 20/500 mg
    '901': 20/750 mg
    '902': 20/1000 mg
    '903': 40/500 mg
    '904': 40/1000 mg
    '905': 40/2000 mg (2 x 20/1000 mg)
    '907': Unknown
    '1000': 10/10 mg
    '1001': 10/20 mg
    '1002': 10/40 mg
    '1003': 10/80 mg
    '1005': Unknown
    '1100': 5 mg
    '1101': 10 mg
    '1102': 20 mg
    '1103': 40 mg
    '1104': 80 mg
    '1106': Unknown
- Field: persistentafib
  Value Map:
    '1': 'Yes'
    '2': 'No'
- Field: lipidsnd
  Value Map:
    '1': Checked
    'True': Checked
- Field: docreasonoralfactorxa
  Value Map:
    '1': 'Yes'
    '2': 'No'
- Field: vteinterventionoralfactorxainhib
  Value Map:
    '1': Checked
    'True': Checked
- Field: ambulatadmit
  Value Map:
    '1': Able to ambulate independently (no help from another person) w/ or w/o device
    '2': With assistance (from person)
    '3': Unable to ambulate
    '4': ND
- Field: ivtpaoutside
  Value Map:
    '1': 'Yes'
    '2': 'No'
- Field: ivthrombolyisstarted
  Value Map:
    '1': 'Yes'
    '2': 'No'
- Field: edustrokewarningsigns
  Value Map:
    '1': 'Yes'
    '2': 'No'
- Field: nomedpriortoadmission
  Value Map:
    '1': Checked
    'True': Checked
- Field: resolutionofstrokesymptomsonpresentation
  Value Map:
    '1': 'Yes'
    '2': 'No'
    '3': ND
- Field: strokerelateddiagnosis
  Value Map:
    '2': Ischemic Stroke
    '3': Transient Ischemic Attack (< 24 hours)
    '4': Subarachnoid Hemorrhage
    '5': Intracerebral Hemorrhage
    '6': Stroke Not Otherwise Specified
    '7': No Stroke Related Diagnosis
    '8': Elective Carotid Intervention Only
    Elective Carotid Intervention only: Elective Carotid Intervention Only
    Ischemic stroke: Ischemic Stroke
    No stroke related diagnosis: No Stroke Related Diagnosis
    Stroke not otherwise specified: Stroke Not Otherwise Specified
- Field: rankindischargeperformed
  Value Map:
    '1': 'Yes'
    '2': No/ND
- Field: locationofstrokesymptoms
  Value Map:
    '1': Not in a healthcare setting
    '2': Another acute care facility
    '3': Chronic health care facility
    '5': Outpatient healthcare setting
    '4': Stroke occurred after hospital arrival (in ED/Obs/inpatient)
    '9': ND or Cannot be determined
    ND or Cannot be Determined: ND or Cannot be determined
- Field: methodpatientarrival
  Value Map:
    '1': EMS from home/scene
    '10': Mobile Stroke Unit
    '2': Private transport/taxi/other from home/scene
    '3': Transfer from other hospital
    '9': ND or unknown
- Field: electivecarotidinterventionadmin
  Value Map:
    '1': 'Yes'
    '2': 'No'
- Field: dischatrialrhythmafib
  Value Map:
    '1': 'Yes'
    '2': No/ND
    '3': NC
- Field: clinicaltrial
  Value Map:
    '1': 'Yes'
    '2': 'No'
- Field: raceotherhispanic
  Value Map:
    '1': 'Yes'
    '2': No/UTD
- Field: gender
  Value Map:
    '1': Male
    '2': Female
    M - Male: Male
    F - Female: Female
    U - Unknown: Unknown
- Field: strokeetiology
  Value Map:
    '1': '1: Large-artery atherosclerosis (e.g., carotid or basilar artery stenosis)'
    '2': '2: Cardioembolism (e.g., atrial fibrillation/flutter, prosthetic heart valve, recent MI)'
    '3': '3: Small-vessel disease (e.g., Subcortical or brain stem lacunar infarction <1.5 cm)'
    '4': '4: Stroke of other determined etiology'
    '5': '5: Cryptogenic Stroke'
- Field: antithrombotic2freq
  Value Map:
    '2': Every Day
    '3': 2 times a day
    '4': 3 times a day
    '5': 4 times a day
    '6': Other
    '7': Unknown
- Field: antithrombotic2class
  Value Map:
    '1': Antiplatelet
    '2': Anticoagulant
- Field: antithrombotic2
  Value Map:
    '3': aspirin
    '4': ASA/dipyridamole (Aggrenox)
    '6': clopidogrel (Plavix)
    '7': ticlopidine (Ticlid)
    '8': Unfractionated heparin IV
    '9': full dose LMW heparin
    '5': 'warfarin (Coumadin) '
    '16': dabigatran (Pradaxa)
    '15': argatroban
    '12': fondaparinux (Arixtra)
    '14': rivaroxaban (Xarelto)
    '19': apixaban (Eliquis)
    '18': lepirudin (Refludan)
    dipyridamole/aspirin (Aggrenox): ASA/dipyridamole (Aggrenox)
    prasugrel (Effient) *contraindication in stroke and TIA: prasugrel (Effient)
    Other anticoagulant: Other Anticoagulant
- Field: antithrombotic1freq
  Value Map:
    '2': Every Day
    '3': 2 times a day
    '4': 3 times a day
    '5': 4 times a day
    '6': Other
    '7': Unknown
- Field: antithrombotic1class
  Value Map:
    '1': Antiplatelet
    '2': Anticoagulant
- Field: antidepmed
  Value Map:
    '1': Yes, SSRI
    '2': Yes, any other antidepressant class
    '3': No/ND
- Field: ambulatorystatusprior
  Value Map:
    '1': Able to ambulate independently (no help from another person) w/ or w/o device
    '2': With assistance (from person)
    '3': Unable to ambulate
    '9': ND
- Field: transfertohospitalnotlisted
  Value Map:
    '1': Checked
- Field: transfertohospitalnotdoc
  Value Map:
    '1': Checked
- Field: transferfromhospitalnotlisted
  Value Map:
    '1': Checked
- Field: transferfromhospitalnotdoc
  Value Map:
    '1': Checked
- Field: cardiacultrasound
  Value Map:
    '1': Performed during this admission or in the 3 months prior
    '2': Planned post discharge
    '3': Not performed or planned
- Field: raceasian
  Value Map:
    '1': 'Yes'
- Field: cardiacrhythmmonitoring
  Value Map:
    '1': Performed during this admission or in the 3 months prior
    '2': Planned post discharge
    '3': Not performed or planned
- Field: otheretiology
  Value Map:
    '1': Dissection
    '2': Hypercoagualability
    '3': Other (e.g., vasculopathy or other hematologic disorders)
- Field: carotidimaging
  Value Map:
    '1': Performed during this admission or in the 3 months prior
    '2': Planned post discharge
    '3': Not performed or planned
- Field: hypercoagtest
  Value Map:
    '1': Performed during this admission or in the 3 months prior
    '2': Planned post discharge
    '3': Not performed or planned
- Field: etiologydocmntd
  Value Map:
    '1': 'Yes'
    '2': 'No'
- Field: cardiacmon
  Value Map:
    '1': Performed during this admission or in the 3 months prior
    '2': Planned post discharge
    '3': Not performed or planned
- Field: iatpaoutsidehosp
  Value Map:
    '1': 'Yes'
    '2': 'No'
- Field: procedureiatpa
  Value Map:
    '1': 'Yes'
    '2': 'No'
- Field: assessedrehab
  Value Map:
    '1': 'Yes'
    '2': 'No'
- Field: edufollowup
  Value Map:
    '1': 'Yes'
    '2': 'No'
- Field: transferreason
  Value Map:
    Advanced Stroke care (non-time critical therapy): Advanced stroke care (e.g., Neurocritical care, surgical or other time critical therapy)
    Post Management of IV Thrombolytics (e.g. Drip and Ship): Post Management of IV alteplase (e.g. Drip and Ship)
    Evaluation for IV Thrombolytics up to 4.5 hours: Evaluation for IV alteplase up to 4.5 hours
- Field: i10pcsnd
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk01mp
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk05bmr
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk05amp
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk06md
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk08mp
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk08mr
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk05bme
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk09bmd
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk05mp
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk03mb
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk09amb
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk05amr
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk05mr
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk01md
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk05amd
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk09mp
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk09bmr
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk03mp
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk06mr
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk09amd
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: c_emsgcsintub
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk01me
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk12mp
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk05amx
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk03ame
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk09mb
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk05bmb
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk01mx
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk03bmd
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk08mx
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk03amb
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk05ame
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk03bmx
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk03bmp
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk12mx
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk03md
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk05mb
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk09mx
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk09bmx
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk06mb
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk04mr
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk01mb
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk09mr
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk03mx
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk03bmr
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk12md
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk05bmd
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk05mx
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk05me
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk11mb
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk04mp
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk11mp
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk05bmp
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk08md
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk05bmx
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk04mb
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk03me
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk05amb
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk03bme
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk03amx
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk03bmb
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk11mr
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk03amr
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk03mr
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk05md
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk08mb
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk09md
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: plateletutd
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk03amd
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk03amp
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: c_nihssobtutd
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk11md
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk11me
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk09amp
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk09bmp
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk12mb
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk11mx
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk12me
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk08me
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk01mr
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk06mp
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk12mr
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk09bmb
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk06me
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk09amr
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: cstk06mx
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'
- Field: c_ivtpaprior
  Value Map:
    'Yes': '1'
    'No': '0'
    'true': '1'

cleaner_regex:
- Field: Height
  Regex: '[%a-zA-Z\/\s]*'
- Field: EncounterNumber
  Regex: '[\u00a0\u2000-\u200b\u202f\u205f\u3000]'
- Field: weight
  Regex: '[%a-zA-Z\/\s]*'
- Field: zipcode
  Regex: '((?<=\d{5}).*|\-)|[a-z="]+'
- Field: antithrom2type
  Regex: ':$'
- Field: prevmedhistory
  Regex: ':$'
- Field: transferreason
  Regex: ':$'
- Field: ivtpadelaymedreason
  Regex: ':$'
- Field: ivtpadelayeligiblityreason
  Regex: ':$'
- Field: inrnotdoc
  Regex: ':$'
- Field: prevstrokehistory
  Regex: ':$'
- Field: cholreductx
  Regex: ':$'
- Field: gs_rsdeltra
  Regex: ':$'
- Field: rehabsvcs
  Regex: ':$'
- Field: payor
  Regex: ':$'
- Field: compreperf
  Regex: ':$'
- Field: otheranticoag
  Regex: ':$'
transform_fields:
  - Field: locationcare
    Transform Type: conditional_and
    Args:
      true_val: 'Emergency Department/Urgent Care'
      false_val: null
      fields:
        - name: admitsource
          values: ['Emergency Room']
  - Field: etiologydocmntd
    Transform Type: conditional_and
    Args:
      true_val: null
      false_val: etiologydocmntd
      fields:
        - name: etiologydocmntd
          values: ['Multiple potential etiologies identified', 'Stroke of undetermined 
  etiology', 'Unspecified']
  - Field: ndprophylaxis
    Transform Type: conditional_and
    Args:
      true_val: 'Checked'
      false_val: null
      fields:
        - name: vtepropnd
          values: ['1']
  - Field: prevmedhistory
    Transform Type: replace
    Args:
      old_values: ';'
      new_values: ':'
  - Field: ivtpadelaymedreason
    Transform Type: replace
    Args:
      old_values: ';'
      new_values: ":"
  - Field: ivtpadelaymedreason
    Transform Type: replace
    Args:
      old_values: 'medications,Management'
      new_values: 'medications:Management'
  - Field: ivtpadelaymedreason
    Transform Type: replace
    Args:
      old_values: 'disorders,Management'
      new_values: 'disorders:Management'
  - Field: ivtpadelaymedreason
    Transform Type: replace
    Args:
      old_values: 'disorders,Need'
      new_values: 'disorders:Need'
  - Field: priorsmoker
    Transform Type: conditional_and
    Computation: true
    Args:
      true_val: 'Yes'
      false_val: prevmedhistory
      fields:
        - name: prevmedhistory
          values: [ 'SMOKER' ]
  - Field: priorsmoker
    Transform Type: replace
    Args:
      old_values: 'nan'
      new_values: ''
  - Field: ivtpastarted
    Transform Type: conditional_and
    Computation: true
    Args:
      true_val: 'Yes'
      false_val: null
      fields:
        - name: gs_thrombo
          values: [ 'Alteplase (Class 1 evidence)' ]
        - name: ivthrombolyisstarted
          values: [ 'YES' ]
  - Field: ivtnkstarted
    Transform Type: conditional_and
    Computation: true
    Args:
      true_val: 'Yes'
      false_val: null
      fields:
        - name: gs_thrombo
          values: [ 'Tenecteplase (Class 2b evidence)' ]
        - name: ivthrombolyisstarted
          values: [ 'YES' ]
  - Field: Race
    Transform Type: additional_field
    Args:
      Source: sp_race
    Computation: true
  - Field: dischargedisposition
    Transform Type: additional_field
    Args:
      Source: dischargestatus
    Computation: true
  - Field: eduusingems
    Transform Type: additional_field
    Args:
      Source: eduems
    Computation: true
  - Field: site_os_id
    Transform Type: additional_field
    Args:
      Source: hospname
    Computation: true
  - Field: reperfusiondatetime
    Transform Type: additional_field
    Args:
      Source: iatpainitiationdatetime
    Computation: true