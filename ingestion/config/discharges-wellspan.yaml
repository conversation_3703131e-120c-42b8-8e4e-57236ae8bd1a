client_fields:
  - IndirectCost
  - DirectCost
  - NetRevenue
  - ExpectedPayment
  - SecondaryInsuranceGroup/SecondaryPayorPlan
  - PrimaryInsuranceGroup/PrimaryPayorPlan
  - FinancialPayorClass
  - SecondaryPayor
  - PrimaryPayor
  - PrincipleProcedureStartTime
  - PrincipleProcedureEndTime
  - PrincipalProcedureDate
  - PrinICDProcCode
  - PrinICDDxCode
  - PrincipleDiagnosisICDPOA
  - DrgCode
  - DischargeStatus
  - ICUDays
  - DischargeUnit/DischargeDepartment
  - DischargeService
  - DischargeTime
  - DischargeDate
  - PrimaryAdmissionICDCode
  - AdmitSource
  - TransferringFacility
  - AdmitType
  - AdmitService
  - PatientType
  - AdmissionUnit/AdmissionDepartment
  - AdmitTime
  - AdmitDate
  - DischargeMDSpecialty
  - DischargeMDName
  - DischargeMDNPI
  - AdmittingMDName
  - AdmittingMDSpecialty
  - PatientZip
  - AdmittingMDNPI
  - Ethnicity
  - Gender
  - Race
  - DOB
  - PatientLastName
  - PatientFirstName
  - EncounterNumber
  - EMPI
  - MedRecN
  - FacilityMedicareID (CCN)
  - Encounter Facility Name
rename_fields:
  - New Field: netrevenue
    Old Field: netpatientrevenue
  - New Field: secondaryinsurancegroup/secondarypayorplan
    Old Field: secondaryinsurancegroup
  - New Field: primaryinsurancegroup/primarypayorplan
    Old Field: primaryinsurancegroup
  - New Field: prinicdproccode
    Old Field: prinicd10proccode
  - New Field: prinicddxcode
    Old Field: prindx10code
  - New Field: dischargetime
    Old Field: dctime
  - New Field: patientzip
    Old Field: patzip
  - New Field: encounter facility name
    Old Field: hospname
required_fields:
- admitdate
- admitsource
- admittime
- admittype
- dctime
- directcost
- dischargedate
- dischargestatus
- dob
- drgcode
- encounternumber
- ethnicity
- expectedpayment
- financialpayorclass
- gender
- hospname
- icudays
- indirectcost
- medrecn
- netpatientrevenue
- patienttype
- patzip
- primaryinsurancegroup
- primarypayor
- prindx10code
- prinicd10proccode
- race
- secondarypayor