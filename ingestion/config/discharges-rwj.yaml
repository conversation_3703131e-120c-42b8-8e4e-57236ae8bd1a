rename_fields:
  - New Field: XPR Direct Physician Revenue
    Old Field: XPRDirPhysicianRevenue
  - New Field: APR Indirect Physician Revenue
    Old Field: APRIndPhysicianRevenue
  - New Field: Costs - Total Cost
    Old Field: totalcost
  - New Field: grants and other revenue
    Old Field: GrantsRevenue
  - New Field: total subsidy and other operating revenue
    Old Field: totalsubsidyandotheroperatingrevenue
  - New Field: other system revenue
    Old Field: othernetrevenue
  - New Field: Cost Report Pass Thru Revenue
    Old Field: CostReportRevenue
  - New Field: State Subsidies
    Old Field: statesubsidyamount
  - New Field: net income
    Old Field: netincome
  - New Field: Payments and Adjustments - Payment Amount
    Old Field: actualpayment
  - New Field: Reimbursement - Historic Expected Payment
    Old Field: expectedpayment
  - New Field: Net Patient Service Revenue
    Old Field: netpatientrevenue
  - New Field: Total Indirect Costs
    Old Field: indirectcost
  - New Field: Charge Level - Charge
    Old Field: grosspatientrevenue
  - New Field: Indirect Costs A&G
    Old Field: indirectagcost
  - New Field: Indirect PPE Costs
    Old Field: indirectppecost
  - New Field: Indirect Interest and Malpractice Costs
    Old Field: indirectinterestmalpcost
  - New Field: indirect department overhead costs
    Old Field: indirectdepartmentoverheadcost
  - New Field: indirect physician costs
    Old Field: indirectphysiciancost
  - New Field: direct other costs
    Old Field: directothercost
  - New Field: total direct costs
    Old Field: directcost
  - New Field: direct physician costs
    Old Field: directphysiciancost
  - New Field: direct labor costs
    Old Field: directlaborcost
  - New Field: direct benefits
    Old Field: directbenefitscost
  - New Field: direct supply costs
    Old Field: directsupplycost
  - New Field: ICD10 PX Primary - ICD10 PX
    Old Field: PrinICD10ProcCode
  - New Field: Present on Admission - Present on Admission
    Old Field: principlediagnosisicd9poa
  - New Field: Admit ICD10 Diagnosis - ICD10 Diagnosis
    Old Field: AdmitDx10Code
  - New Field: ICD10 DX Primary - ICD10 Diagnosis
    Old Field: PrinDx10Code
  - New Field: Primary Performing Physician - Specialty
    Old Field: PrinProcMDRole
  - New Field: Reporting DRG - Code
    Old Field: drgcode
  - New Field: Attend Physician - Specialty
    Old Field: attendingmdrole
  - New Field: Primary Performing Physician - NPI
    Old Field: PrinProcMDID
  - New Field: Primary Performing Physician - Physician Name
    Old Field: PrinProcMDName
  - New Field: Attend Physician - Physician Name
    Old Field: attendingmdname
  - New Field: Attend Physician - NPI
    Old Field: attendingmdnpi
  - New Field: Refer Physician - Specialty
    Old Field: referringmdrole
  - New Field: Refer Physician - NPI
    Old Field: referringmdid
  - New Field: Refer Physician - Physician Name
    Old Field: refferingphyname
  - New Field: Insurance Plan 2 - Insurance Plan
    Old Field: primaryinsurancegroupcode
  - New Field: Insurance Plan 1 - Insurance Plan
    Old Field: primaryinsurancegroup
  - New Field: Insurance Plan 1 - Financial Class
    Old Field: financialpayorclass
  - New Field: Discharge Status - Discharge Status Description
    Old Field: dischargestatus
  - New Field: Discharge Status - Discharge Status Code
    Old Field: dischargestatuscode
  - New Field: Discharge Time - Time
    Old Field: dctime
  - New Field: icu days
    Old Field: icudays
  - New Field: Discharge Date - Date
    Old Field: dischargedate
  - New Field: Claim Admit Time - Time
    Old Field: admittime
  - New Field: Claim Admit Date - Date
    Old Field: admitdate
  - New Field: Admit Source - Description
    Old Field: admitsource
  - New Field: Inpatient Length of Stay
    Old Field: los
  - New Field: Admit Source - Code
    Old Field: admitsourcecode
  - New Field: Admit Type - Description
    Old Field: admittype
  - New Field: Patient Type - DSSRollup
    Old Field: patienttype
  - New Field: Admit Type - Code
    Old Field: admissiontypecode
  - New Field: Gender - Gender Description
    Old Field: gender
  - New Field: Zip Code - Zip Code
    Old Field: patzip
  - New Field: Patient Encounter - Date Of Birth
    Old Field: dob
  - New Field: Patient Encounter - Encounter Record Number
    Old Field: encounternumber
  - New Field: DSS Entity - Entity Description
    Old Field: servicesitename
  - New Field: Patient Encounter - Medical Record Number Source
    Old Field: medrecn
  - New Field: DSS Entity - Entity Code
    Old Field: servicesitecode
required_fields:
  - admitdate
  - admitdx10code
  - admitsource
  - admittime
  - admittype
  - attendingmdname
  - dctime
  - directcost
  - dischargedate
  - dischargestatus
  - dob
  - drgcode
  - encounternumber
  - expectedpayment
  - financialpayorclass
  - gender
  - icudays
  - indirectcost
  - los
  - medrecn
  - netpatientrevenue
  - patienttype
  - patzip
  - primaryinsurancegroup
  - prindx10code
  - prinicd10proccode
  - referringmdrole
  - refferingphyname
  - servicesitename
  - totalcost