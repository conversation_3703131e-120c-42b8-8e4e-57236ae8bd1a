version: '3.0'
biome_schema:
  - Dtype: varchar(30)
    Field: NCDRPatientId
    PHI: true
    Role: IDENTIFIER
  - Dtype: varchar(20)
    Field: ParticID
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: PartName
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: FirstName
    PHI: true
    Role: NAME
  - Dtype: varchar(30)
    Field: LastName
    PHI: true
    Role: NAME
  - Dtype: varchar(30)
    Field: MidName
    PHI: true
    Role: NAME
  - Dtype: varchar(30)
    Field: DOB
    PHI: true
    Role: 'DOB|DATE_ONLY'
  - Dtype: varchar(22)
    Field: SSNNA
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: SSN
    PHI: true
    Role: NAME
  - Dtype: varchar(30)
    Field: OtherId
    PHI: true
    Role: MEDRECNUM
  - Dtype: varchar(30)
    Field: Gender
    PHI: true
    Role: GENDER
  - Dtype: varchar(10)
    Field: PatZipNA
    PHI: false
    Role: null
  - Dtype: varchar(9)
    Field: PatZip
    PHI: true
    Role: PATIENT_ZIP
  - Dtype: varchar(10)
    Field: RaceWhite
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceBlack
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceAMIndian
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceAsian
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceAsianIndian
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceChinese
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceFilipino
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceJapanese
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceKorean
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceVietnamese
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceAsianOther
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceNatHaw
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceNativeHawaii
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceGuamChamo
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceSamoan
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: RaceOtherIsland
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: HispOrig
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: HispEthnicityMexican
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: HispEthnicityPuertoRico
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: HispEthnicityCuban
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: HispEthnicityOtherOrigin
    PHI: false
    Role: null
  - Dtype: varchar(48)
    Field: EPISODEID
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: ArrivalDate
    PHI: true
    Role: '|ARRIVAL_DATE||DATE_ONLY|'
  - Dtype: varchar(20)
    Field: ArrivalTime
    PHI: false
    Role: ARRIVAL_TIME
  - Dtype: varchar(15)
    Field: HealthIns
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: InsPrivate
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: InsMedicare
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Residence
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: ResidenceND
    PHI: false
    Role: null
  - Dtype: varchar(23)
    Field: MBI
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: EnrolledStudy
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PtRestriction2
    PHI: false
    Role: null
  - Dtype: varchar(37)
    Field: TVTPathway
    PHI: false
    Role: null
  - Dtype: varchar(23)
    Field: AdmFName
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: AdmLName
    PHI: false
    Role: null
  - Dtype: varchar(19)
    Field: AdmMidName
    PHI: false
    Role: null
  - Dtype: varchar(22)
    Field: AdmNPI
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: Height
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: Weight
    PHI: false
    Role: null
  - Dtype: int(11)
    Field: NumPrevCardSurg
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PriorHFAdmit
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PriorHFAdmit1YearND
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LifeLessThan1yrND
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: HMO2
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: IMMSUPP
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: CurrentDialysis
    PHI: false
    Role: null
  - Dtype: varchar(24)
    Field: TobaccoType
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: TobaccoUse
    PHI: false
    Role: null
  - Dtype: varchar(40)
    Field: SmokeAmount
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: PriorMed_ACE_I_OR_ARB_Any
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: PriorMed_Aldosterone_Antagonists
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: PriorMed_AngioRecepNeprilInhib
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: PriorMed_Anticoagulants_Any
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: PriorMed_Aspirin
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: PriorMedAngiotensinIIReceptorBlocker
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: PriorMed_Beta_Blocker_Any
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: PriorMed_Diuretics_Other
    PHI: false
    Role: null
  - Dtype: varchar(28)
    Field: PriorMed_Loop_Diuretic
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: PriorMed_Thiazides
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: PriorMed_P2Y12Antagonist
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: PriorMed_SelectiveSinusNode
    PHI: false
    Role: null
  - Dtype: varchar(36)
    Field: AFibClass
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: AFib30Days
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: DiabetesControl
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: MIWhen
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PriorAtrialFib
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: AFlutter
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PriorCarotidStenosis
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PriorStroke
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: PriorStrokeDate
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: PriorCVD
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: ChronicLungDisease
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: ConductionDefect
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PriorDementia
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: Diabetes
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: INFENDO
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: PriorHF
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: HostileChest
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: Hypertension
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PriorLiverDisease
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PriorMI
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PriorPAD
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PorcelainAorta
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: CVDTIA
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PriorAorticValve
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: PriorAorticValveDate
    PHI: true
    Role: DATE
  - Dtype: varchar(20)
    Field: PrevProcAVBall
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: PrevProcAVRepair
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: PrevProcAVReplace
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: PrevProcTCVRep
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PrevProcTCVInt
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PriorCABG
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PriorCABGDate
    PHI: true
    Role: DaATE
  - Dtype: varchar(30)
    Field: PrevICD
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PriorMVProc
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PriorMVProcDate
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: PriorMARingSurg
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: PrevProcMVRepair
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: PrevProcMVReplace
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PriorTMVR
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: PriorPCI
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PriorPCIDate
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: Pacemaker
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: PriorPacerDate
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: PriorPulmonicProc
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PriorTricuspidProc
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TricuspidValveProcedureDate
    PHI: true
    Role: DATE
  - Dtype: varchar(30)
    Field: DischargeDate
    PHI: true
    Role: '|DISCHARGE_DATE||ANCHOR_DATE|'
  - Dtype: varchar(20)
    Field: DCLName
    PHI: false
    Role: null
  - Dtype: varchar(23)
    Field: DCFName
    PHI: false
    Role: null
  - Dtype: varchar(19)
    Field: DCMName
    PHI: false
    Role: null
  - Dtype: varchar(22)
    Field: DCNPI
    PHI: false
    Role: null
  - Dtype: varchar(60)
    Field: DischargeStatus
    PHI: false
    Role: null
  - Dtype: varchar(42)
    Field: DischargeCardRehab
    PHI: false
    Role: null
  - Dtype: varchar(60)
    Field: DischargeLocation
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: DCHospice
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DeathLocation
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DeathCause
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: DC_RBC
    PHI: false
    Role: null
  - Dtype: varchar(13)
    Field: DC_RBCUnit
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: Discharge_Direct_thrombin_inhibitor
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: Discharge_Warfarin
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_Aspirin_any
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: Discharge_Factor_Xa_Inhibitor
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: Discharge_P2Y12_any
    PHI: false
    Role: null
  - Dtype: varchar(16)
    Field: SAVRImplantID
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: SAVRImplantDia
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PrevProcAVType
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: AVReplacementTypeND
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PrevProcTCVModelID
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TAVRImplantDia
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: CRTD
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PriorMVRingSurg
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PriorMVRingSurgND
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MVRingImplantID
    PHI: false
    Role: null
  - Dtype: int(11)
    Field: MVRingImplantDia
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PrevProcMVReplaceType
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PrevMVReplaceTypeND
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: SMVRImplantID
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: SMVRImplantDia
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: CRT
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PreTVARing
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PreTTVIType
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: STVRImplantID
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: STVRImplantDia
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TTVRImplantID
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TTVRImplantDia
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: MedicareAdvantage
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: InsState
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: AFlutter30days
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PriorMedDose_Home
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: Cardiomyopathy
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PriorCardiomyopathy
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: Discharge_ACE_Inhibitor_any
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: Discharge_Aldosterone_Antagonists
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: Discharge_ARB_any
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: Discharge_Diuretics_Other
    PHI: false
    Role: null
  - Dtype: varchar(28)
    Field: Discharge_Loop_Diuretic
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MedDose_Discharge
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: Discharge_Thiazides
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: ChrLungD
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: ChronLungDisSeverityND
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: InsMedicaid
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CurrendCAS
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: CVDCARSTEN
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: CVDCarSteLocND
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PriorTricuspidValveRepairSurgery
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PriorTricuspidValveReplaceSurgery
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PriorTricuspidValveReplaceTranscatheter
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PriorTricuspidValveTranscathInterven
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ATTLNAME
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ATTFNAME
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ATTMNAME
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ATTNPI
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: TVTProType
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: ProcTAVR
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: ProcTMVrepair
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ProcTMVR
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: ProcTTVP
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: TVTPRocedureEntryTime
    PHI: true
    Role: DATE
  - Dtype: varchar(30)
    Field: ProcedureStartDate
    PHI: true
    Role: '|PROCEDURE_DATE||DATE|'
  - Dtype: varchar(30)
    Field: ProcedureStartTime
    PHI: false
    Role: '|PROCEDURE_TIME||TIME|'
  - Dtype: varchar(30)
    Field: TVTProcedureStopDate
    PHI: true
    Role: DATE_ONLY
  - Dtype: varchar(30)
    Field: TVTProcedureStopTime
    PHI: false
    Role: TIME
  - Dtype: varchar(31)
    Field: TVTPRocedureExitTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: ProcLeafClip
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: CADPresentation
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Prior2WeekSHF
    PHI: false
    Role: null
  - Dtype: varchar(21)
    Field: Prior2WeekNYHA
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PriorCardioShock
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PriorCardiacArrest
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: SxAS
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: SxASND
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: FiveMWalkTest
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: STSRiskScore
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: KCCQ12_Performed
    PHI: false
    Role: null
  - Dtype: varchar(68)
    Field: KCCQ12_1a
    PHI: false
    Role: null
  - Dtype: varchar(68)
    Field: KCCQ12_1b
    PHI: false
    Role: null
  - Dtype: varchar(64)
    Field: KCCQ12_1c
    PHI: false
    Role: null
  - Dtype: varchar(39)
    Field: KCCQ12_2
    PHI: false
    Role: null
  - Dtype: varchar(53)
    Field: KCCQ12_3
    PHI: false
    Role: null
  - Dtype: varchar(53)
    Field: KCCQ12_4
    PHI: false
    Role: null
  - Dtype: varchar(53)
    Field: KCCQ12_5
    PHI: false
    Role: null
  - Dtype: varchar(59)
    Field: KCCQ12_6
    PHI: false
    Role: null
  - Dtype: varchar(32)
    Field: KCCQ12_7
    PHI: false
    Role: null
  - Dtype: varchar(58)
    Field: KCCQ12_8a
    PHI: false
    Role: null
  - Dtype: varchar(58)
    Field: KCCQ12_8b
    PHI: false
    Role: null
  - Dtype: varchar(58)
    Field: KCCQ12_8c
    PHI: false
    Role: null
  - Dtype: decimal(5,2)
    Field: KCCQ12_Overall
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PreProcHGBND
    PHI: false
    Role: null
  - Dtype: int(11)
    Field: Sodium
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: SodiumND
    PHI: false
    Role: null
  - Dtype: varchar(16)
    Field: PreProcCreat
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PreProcCreatND
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: TotBLRBN
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: TotBlrbnND
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: TotalBUMIN
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: TotAlbuminND
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: Platelets
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PlateletND
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: Inr
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: InrNd
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: BNP
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: BNPND
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: NTProBNP
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PreProcNTBNPNotDrawn
    PHI: false
    Role: null
  - Dtype: int(10)
    Field: FEV1
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: FEV1NA
    PHI: false
    Role: null
  - Dtype: int(10)
    Field: DLCOPRED
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: DLCONA
    PHI: false
    Role: null
  - Dtype: int(16)
    Field: NVPQRS
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: VPQRS
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Pre_Procedure_Anticoagulants_Any
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Pre_Procedure_Inotrope_positive
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: DxCathPer
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: DxCathDt
    PHI: true
    Role: DATE
  - Dtype: varchar(30)
    Field: NumDisV
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: NumDisVND
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: LMainDis
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LMainDisND
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: ProxLAD
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: ProxLADND
    PHI: false
    Role: null
  - Dtype: varchar(34)
    Field: Syntax
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: SyntaxND
    PHI: false
    Role: null
  - Dtype: decimal(3,1)
    Field: CardiacOutput
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CONA
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PCWP
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PCWPNM
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PAPMean
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PAPMeanNM
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PAPSys
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PAPSysNM
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: PVR
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PVRND
    PHI: false
    Role: null
  - Dtype: int(64)
    Field: RAPMean
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: RAPNM
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: RVSYS
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: RVSYSND
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: AVDAnnulusSizeMethod
    PHI: false
    Role: null
  - Dtype: varchar(16)
    Field: AVAnnulusDia
    PHI: false
    Role: null
  - Dtype: varchar(16)
    Field: AVAnnulusMaxDia
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: AVAnnulusArea
    PHI: false
    Role: null
  - Dtype: varchar(16)
    Field: AVAnnulusPeri
    PHI: false
    Role: null
  - Dtype: varchar(27)
    Field: AVCalc
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: AVCalcND
    PHI: false
    Role: null
  - Dtype: int(16)
    Field: LVEF
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: LVEFNA
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: VDAOET
    PHI: false
    Role: null
  - Dtype: varchar(21)
    Field: AVDMorphology
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: AASize
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: AASizeND
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: AVDAnnularCalc
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: VDINSUFA
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: VDSTENA
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: VDAOVA
    PHI: false
    Role: null
  - Dtype: decimal(3,0)
    Field: VDGRADA
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: SVI
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: SVI_ND
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: AVDStenosisPeakGradient
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: AVDPeakVelocity
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: VDMIT
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: VDINSUFM
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: VDInsufMPara
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: VDInsufMParaND
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: VDInsuffMCentral
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: VDInsuffMCentralND
    PHI: false
    Role: null
  - Dtype: decimal(3,2)
    Field: VDMitEOA
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: VDMitEOA_MoA
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: VDSTENM
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: VDMVA
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: VDGRADM
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MVDEtioDMR
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MVDEtioEndoc
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MVDEtioInflam
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MVDEtioFMR
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MVDEtioOther
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MVDEtioNone
    PHI: false
    Role: null
  - Dtype: varchar(47)
    Field: FMRType
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: FMRTypeND
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MVDLeafPro
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: MVDLeafProND
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: MVDLeafFlail
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MVDLeafFlailND
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: InflamType
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: InflamTypeND
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MVDLeafTeth
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: MVDLeafTethND
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MAnnCalc
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: MVCalcND
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MLeafCalc
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: MLeafCalcND
    PHI: false
    Role: null
  - Dtype: varchar(19)
    Field: TVDisEtio
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: VDINSUFT
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TVDGrad
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: TVDGradND
    PHI: false
    Role: null
  - Dtype: varchar(13)
    Field: TVAnnulus
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: TVAnnulusND
    PHI: false
    Role: null
  - Dtype: decimal(4,2)
    Field: MidRVDia
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: MidRVDiaND
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: BasalDia
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: BasalDiaND
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: DobutChal
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: FlowRes
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ASType
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: ASTypeND
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: OtherProc
    PHI: false
    Role: null
  - Dtype: varchar(37)
    Field: ConcomProcType
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: Status
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: EvalAVRSuit
    PHI: false
    Role: null
  - Dtype: varchar(27)
    Field: TVTLocation
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: AnesthesiaType
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: ProcedureAbort
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ProcedureAbortReason
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ProcedureAbortAction
    PHI: false
    Role: null
  - Dtype: varchar(29)
    Field: OperatorReason
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: ConvSurgAccess
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ConvSurgAccessReason
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MVSupport
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MVSupportDevice
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MVSupportTiming
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: CPB
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: CPBStatus
    PHI: false
    Role: null
  - Dtype: int(11)
    Field: PERFUSTM
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: DeliveryRemoved
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Intra_Procedure_Inotrope_positive
    PHI: false
    Role: null
  - Dtype: varchar(23)
    Field: TVTOpA_FirstName
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: TVTOpA_LastName
    PHI: false
    Role: null
  - Dtype: varchar(19)
    Field: TVTOpA_MidName
    PHI: false
    Role: null
  - Dtype: int(10)
    Field: TVTOpA_NPI
    PHI: false
    Role: null
  - Dtype: varchar(23)
    Field: TVTOpB_FirstName
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: TVTOpB_LastName
    PHI: false
    Role: null
  - Dtype: varchar(19)
    Field: TVTOpB_MidName
    PHI: false
    Role: null
  - Dtype: int(10)
    Field: TVTOpB_NPI
    PHI: false
    Role: null
  - Dtype: varchar(17)
    Field: FluoroDoseDAP
    PHI: false
    Role: null
  - Dtype: varchar(19)
    Field: FluoroDoseDAPUnit
    PHI: false
    Role: null
  - Dtype: int(16)
    Field: FluoroDoseKerm
    PHI: false
    Role: null
  - Dtype: decimal(4,1)
    Field: FluroTime
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: ContrastVol_AllProcs
    PHI: false
    Role: null
  - Dtype: varchar(40)
    Field: TVTProcedureIndication
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: ValveInValve
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: BVFAttempt
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: BVFTiming
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: ValveFractured
    PHI: false
    Role: null
  - Dtype: varchar(19)
    Field: TVTAccessSite
    PHI: false
    Role: null
  - Dtype: varchar(24)
    Field: TVTAccessMethod
    PHI: false
    Role: null
  - Dtype: int(16)
    Field: ValveSheathDelivery
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: EmbProt
    PHI: false
    Role: null
  - Dtype: varchar(16)
    Field: EmbProtDevice
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: AVRPostAR
    PHI: false
    Role: null
  - Dtype: int(16)
    Field: Post_MeanAVGrad
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: PostProcHGB
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PostProcHGBND
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PoPEKGPerf
    PHI: false
    Role: null
  - Dtype: varchar(40)
    Field: POPEKG
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: DC_Creat
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: DC_CreatND
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: PostProcCreat
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PostProcCreatND
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: POPTTECH
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: EchoND
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: POpTTEchDate
    PHI: true
    Role: DATE
  - Dtype: decimal(6,3)
    Field: Post_AorticValveArea
    PHI: false
    Role: null
  - Dtype: int(16)
    Field: Post_AorticValveMeanGradient
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: POPTTAR
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: POPTTMR
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: PPTR
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: Post_MVEOA
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: Post_EOAmethod
    PHI: false
    Role: null
  - Dtype: int(16)
    Field: Post_MVMeanGrad
    PHI: false
    Role: null
  - Dtype: decimal(4,2)
    Field: Post_Mvarea
    PHI: false
    Role: null
  - Dtype: decimal(4,2)
    Field: Post_LVOT
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: Post_SAM
    PHI: false
    Role: null
  - Dtype: int(16)
    Field: PPTVDGrad
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: PPTVDGradND
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PPTVAnnulus
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PPTVAnnulusND
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PPMidRVDia
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PPMidRVDiaND
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PPBasalRVDia
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PPBasalRVDiaND
    PHI: false
    Role: null
  - Dtype: int(16)
    Field: PPRVSP
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PPRVSYSND
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: FiveMWalk1
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: FiveMWalk2
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: FiveMWalk3
    PHI: false
    Role: null
  - Dtype: varchar(18)
    Field: TVTOpC_FIRSTNAME
    PHI: false
    Role: null
  - Dtype: varchar(18)
    Field: TVTOpC_LASTNAME
    PHI: false
    Role: null
  - Dtype: varchar(19)
    Field: TVTOpC_MIDNAME
    PHI: false
    Role: null
  - Dtype: int(10)
    Field: TVTOpC_NPI
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: STSRiskMVRepair
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: STSRiskMVReplace
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: SixMinWalkDate
    PHI: true
    Role: DATE
  - Dtype: varchar(12)
    Field: SixMinWalkDist
    PHI: false
    Role: null
  - Dtype: varchar(39)
    Field: SixMinWalkReason
    PHI: false
    Role: null
  - Dtype: varchar(55)
    Field: SixMinWalkPerf
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LVIDs
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LVIDsNM
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LVIDd
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LVIDdNM
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LVESV
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LVESVNM
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LVEDV
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LVEDVNM
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: LAVol
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LAVolNM
    PHI: false
    Role: null
  - Dtype: int(11)
    Field: LAVolIndex
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LAVolIndexNM
    PHI: false
    Role: null
  - Dtype: varchar(16)
    Field: MVR_Post_MR
    PHI: false
    Role: null
  - Dtype: int(16)
    Field: MVR_Post_MeanMVGrad
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: MRRIndOptimalMedicalTherapy
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MRRIndFrail
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MRRIndHostile
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MRRIndSeverePulmonaryHypertension
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MRRIndPA
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MRRIndOpM6
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MRRIndOpM8
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MRRIndRVDysfx
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MRRIndBleed
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MRRIndChemo
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MRRIndAIDS
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MRRIndAspir
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MRRIndDem
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MRRIndIMA
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MRRIndOther
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MRRIndImmob
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MRRIndLiver
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: LeafAccess
    PHI: false
    Role: null
  - Dtype: varchar(16)
    Field: SteerableGuideUsed
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: MRR_GuideSerNo
    PHI: true
    Role: FREE_TEXT
  - Dtype: varchar(26)
    Field: TVProcType
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TVLocation
    PHI: false
    Role: null
  - Dtype: varchar(41)
    Field: TVProcedureInd
    PHI: false
    Role: null
  - Dtype: varchar(24)
    Field: TVAccess
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: RVLead
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: RVLeadStrat
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: RVLeadFx
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: SVDPre
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: SVDPreND
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: IVCPre
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: IVCPreND
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: RAPPre
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: RAPPreND
    PHI: false
    Role: null
  - Dtype: int(16)
    Field: RVSPPre
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: RVSPPreND
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: TVDGradPre
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: TVDGradPreND
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: SVDPost
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: SVDPostND
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: IVCPost
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: IVCPostND
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: RAPPost
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: RAPPostND
    PHI: false
    Role: null
  - Dtype: int(11)
    Field: RVSPPost
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: RVSPPostND
    PHI: false
    Role: null
  - Dtype: int(11)
    Field: TVDGradPost
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: TVDGradPostND
    PHI: false
    Role: null
  - Dtype: int(11)
    Field: TVTDeviceCounter
    PHI: false
    Role: null
  - Dtype: varchar(16)
    Field: TVTDeviceID
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: TAVRDeviceDia
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: TVTDeviceRepositioning
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: TVTDeviceRepositioningNA
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: DeviceImplantSuccessful
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TAVR_Unsuccessful
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: TVTDeviceSerNo
    PHI: true
    Role: FREE_TEXT
  - Dtype: varchar(12)
    Field: Valve_UDIDirectID
    PHI: true
    Role: FREE_TEXT
  - Dtype: int(11)
    Field: MRR_Counter
    PHI: false
    Role: null
  - Dtype: varchar(16)
    Field: MVR_DeviceID
    PHI: false
    Role: null
  - Dtype: varchar(21)
    Field: MRR_LeafletClipNum
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MRR_UDIDirectID
    PHI: true
    Role: FREE_TEXT
  - Dtype: varchar(16)
    Field: MRR_Loc
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: MRR_LeafletClipDeploy
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MRR_LeafletClipReasonNotDeploy
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MRR_ClipRemoved
    PHI: false
    Role: null
  - Dtype: int(16)
    Field: TVDevCounter
    PHI: false
    Role: null
  - Dtype: varchar(16)
    Field: TTVDeviceID
    PHI: false
    Role: null
  - Dtype: int(11)
    Field: TTVDeviceDia
    PHI: false
    Role: null
  - Dtype: varchar(22)
    Field: TVDeviceSN
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TTVUDI
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: TVDeviceImplantSuccessful
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TVUnsuccessful
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CE_EventOccurred
    PHI: false
    Role: null
  - Dtype: varchar(24)
    Field: CE_EventID
    PHI: false
    Role: null
  - Dtype: varchar(65)
    Field: CE_EventName
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: CE_EventDate
    PHI: true
    Role: DATE
  - Dtype: varchar(29)
    Field: AJ_AdjudEvent
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: AJ_EventDate
    PHI: true
    Role: DATE
  - Dtype: varchar(20)
    Field: AJ_Status
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: AJ_DeathDate
    PHI: true
    Role: DATE
  - Dtype: varchar(12)
    Field: AJCommentsInHosp
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: AJ_SxOnset
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: AJ_NeuroDef
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: AJ_NeuroClinPresent
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: AJ_NeuroSxDuration
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: AJBrainImag
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: AJBrainImageType
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: BIFind
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: AdjERS
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: AJDLAE
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: AJPriorLiving
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: AJAutDxStroke
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: DataVrsn
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PriorMed_Aspirin_Alone
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: Smoker
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PriorMed_Aspirin_Dual_Antiplatelet
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: AFibFlutter
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: Discharge_ACE_OR_ARB_any
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Discharge_Aspirin_Alone
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Discharge_Aspirin_dual_antiplatelet_therapy
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: Post_ValvMR
    PHI: false
    Role: null
  - Dtype: int(9)
    Field: ContrastVol
    PHI: false
    Role: null
  - Dtype: int(16)
    Field: MVR_ContrastVol
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: MRR_ProcRmArrivalTime
    PHI: false
    Role: TIME
  - Dtype: varchar(25)
    Field: VDInsufMV
    PHI: false
    Role: null
  - Dtype: varchar(23)
    Field: HIC
    PHI: true
    Role: NAME
  - Dtype: varchar(12)
    Field: SupportType
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PreProcMechAssist
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MRR_ConvSurgAccess
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MRR_ConvSurgAccessReason
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: MRR_MVSupport
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MRR_SupportType
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MRR_SupportTiming
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: MRR_ProcRmArrivalDate
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: MRR_ProcedureAbort
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MRR_ProcedureAbortReason
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MRR_ProcedureAbortAction
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MVR_ConvSurgMitral
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MVR_ConvSurgAccess
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MVR_MVSupport
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MVR_MVSupportType
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MVR_MVSupportTiming
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MVR_OperatorReason
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MVR_ProcedureAbort
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MVR_ProcedureAbortAction
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MVR_ProcedureAbortReason
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: TTVP_ConvSurgMitral
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TTVP_ConvSurgAccessReason
    PHI: false
    Role: null
  - Dtype: varchar(21)
    Field: TTVP_OperatorReason
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: TTVP_ProcedureAbort
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TTVP_ProcedureAbortReason
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TTVP_ProcedureAbortAction
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TTVP_MVSupport
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TTVP_SupportTiming
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: OriginalOtherId
    PHI: true
    Role: MEDRECNUM
  - Dtype: varchar(100)
    Field: HospName
    PHI: false
    Role: HOSPITAL
  - Dtype: varchar(82)
    Field: FileName
    PHI: false
    Role: null
  - Dtype: varchar(22)
    Field: Version
    PHI: false
    Role: null
  - Dtype: varchar(18)
    Field: DatasetName
    PHI: false
    Role: null
  - Dtype: bigint(20)
    Field: ClientFileId
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: BiomeImportDT
    PHI: false
    Role: DATE
  - Dtype: varchar(34)
    Field: CareEntityId
    PHI: false
    Role: CAREENTITY
  - Dtype: varchar(34)
    Field: TenantID
    PHI: false
    Role: TENANT
  - Dtype: decimal(8,2)
    Field: DistFromHospital
    PHI: false
    Role: DISTANCE_FROM_HOSPITAL
  - Dtype: bigint(20) unsigned
    Field: BiomeEncounterId
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: Age
    PHI: true
    Role: AGE
  - Dtype: varchar(30)
    Field: INFENDTY
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: VendorID
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: InsMilitary
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: InsIHS
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: InsNonUS
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PreProcHGB
    PHI: false
    Role: null
  - Dtype: varchar(24)
    Field: DischargeBetaBlockerAny
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: Timeframe
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: VendorVer
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: RegistryId
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: StudyName
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: AJ_AISev
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: AJ_CenSev
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: AJ_MVInd
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: AJ_PrimaryInd
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: AJ_PVSev
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: AJ_ReIntType
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MVAccessSite
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MVR_DeviceImplantSuccessful
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MVR_MVHemDet
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MVR_MVPostBalloon
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MVR_MVPreBalloon
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: Post_AorticValveInsuffCent
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: Post_AorticValveInsuffPeri
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: Post_ParaMR
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PrevProcAVModelID
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PriorTMVRType
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: StudyName2
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: StudyName3
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: StudyName4
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: StudyName5
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: StudyPtID
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: StudyPtID2
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: StudyPtID3
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: StudyPtID4
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: StudyPtID5
    PHI: false
    Role: null
  - Dtype: int(11)
    Field: MVR_DeviceCounter
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MVR_DeviceSerNo
    PHI: true
    Role: FREE_TEXT
  - Dtype: varchar(12)
    Field: MVR_Valve_UDIDirectID
    PHI: true
    Role: FREE_TEXT
  - Dtype: varchar(12)
    Field: SubmissionType
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ArrivalDatetime
    PHI: true
    Role: ARRIVAL_DATE
  - Dtype: varchar(12)
    Field: TTVP_SupportType
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TMVRDeviceID
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: SDMProc
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: SDMTool
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: SDMToolName
    PHI: false
    Role: null
custom_fields:
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: '|Indian Health Service|'
    Derivative Value Code: 33
    Exact Match: 0
    Field: INSIHS
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Indian Health Service|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: '|Medicaid|'
    Derivative Value Code: 2
    Exact Match: 0
    Field: INSMEDICAID
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Medicaid|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: '|Medicare Fee-For-Service|'
    Derivative Value Code: 1
    Exact Match: 0
    Field: INSMEDICARE
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Medicare Fee-For-Service|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: '|Military Health Care|'
    Derivative Value Code: 31
    Exact Match: 0
    Field: INSMILITARY
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Military Health Care|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: '|Non-US Insurance|'
    Derivative Value Code: 100000812
    Exact Match: 0
    Field: INSNONUS
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Non-US Insurance|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: '|Private Health Insurance|'
    Derivative Value Code: 5
    Exact Match: 0
    Field: INSPRIVATE
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Private Health Insurance|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: '|State-Specific Plan (non-Medicaid)|'
    Derivative Value Code: 36
    Exact Match: 0
    Field: INSSTATE
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|State-Specific Plan (non-Medicaid)|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: '|Medicare Advantage|'
    Derivative Value Code: 112000002025
    Exact Match: 0
    Field: MEDICAREADVANTAGE
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Medicare Advantage|': 'Yes'
  - Delimiter: '|'
    Derivative Field: MRRIndication
    Derivative Field Code: 112000000482
    Derivative Value: '|AIDS|'
    Derivative Value Code: 62479008
    Exact Match: 0
    Field: MRRIndAIDS
    Reference Field: MRRIndication
    Reference Field Code: 112000000000
    Section: LABVISIT
    Value Map:
      '|AIDS|': 'Yes'
  - Delimiter: '|'
    Derivative Field: MRRIndication
    Derivative Field Code: 112000000482
    Derivative Value: '|High Risk of Aspiration|'
    Derivative Value Code: 112000001488
    Exact Match: 0
    Field: MRRIndAspir
    Reference Field: MRRIndication
    Reference Field Code: 112000000000
    Section: LABVISIT
    Value Map:
      '|High Risk of Aspiration|': 'Yes'
  - Delimiter: '|'
    Derivative Field: MRRIndication
    Derivative Field Code: 112000000482
    Derivative Value: '|Major Bleeding Diathesis|'
    Derivative Value Code: 112000001487
    Exact Match: 0
    Field: MRRIndBleed
    Reference Field: MRRIndication
    Reference Field Code: 112000000000
    Section: LABVISIT
    Value Map:
      '|Major Bleeding Diathesis|': 'Yes'
  - Delimiter: '|'
    Derivative Field: MRRIndication
    Derivative Field Code: 112000000482
    Derivative Value: '|Chemotherapy for Malignancy|'
    Derivative Value Code: 112000001491
    Exact Match: 0
    Field: MRRIndChemo
    Reference Field: MRRIndication
    Reference Field Code: 112000000000
    Section: LABVISIT
    Value Map:
      '|Chemotherapy for Malignancy|': 'Yes'
  - Delimiter: '|'
    Derivative Field: MRRIndication
    Derivative Field Code: 112000000482
    Derivative Value: '|Severe Dementia|'
    Derivative Value Code: 112000001914
    Exact Match: 0
    Field: MRRIndDem
    Reference Field: MRRIndication
    Reference Field Code: 112000000000
    Section: LABVISIT
    Value Map:
      '|Severe Dementia|': 'Yes'
  - Delimiter: '|'
    Derivative Field: MRRIndication
    Derivative Field Code: 112000000482
    Derivative Value: '|Frailty|'
    Derivative Value Code: 248279007
    Exact Match: 0
    Field: MRRIndFrail
    Reference Field: MRRIndication
    Reference Field Code: 112000000000
    Section: LABVISIT
    Value Map:
      '|Frailty|': 'Yes'
  - Delimiter: '|'
    Derivative Field: MRRIndication
    Derivative Field Code: 112000000482
    Derivative Value: '|Hostile Chest|'
    Derivative Value Code: 112000001489
    Exact Match: 0
    Field: MRRIndHostile
    Reference Field: MRRIndication
    Reference Field Code: 112000000000
    Section: LABVISIT
    Value Map:
      '|Hostile Chest|': 'Yes'
  - Delimiter: '|'
    Derivative Field: MRRIndication
    Derivative Field Code: 112000000482
    Derivative Value: '|IMA at High Risk of Injury|'
    Derivative Value Code: 112000001494
    Exact Match: 0
    Field: MRRIndIMA
    Reference Field: MRRIndication
    Reference Field Code: 112000000000
    Section: LABVISIT
    Value Map:
      '|IMA at High Risk of Injury|': 'Yes'
  - Delimiter: '|'
    Derivative Field: MRRIndication
    Derivative Field Code: 112000000482
    Derivative Value: '|Immobility|'
    Derivative Value Code: 112000001492
    Exact Match: 0
    Field: MRRIndImmob
    Reference Field: MRRIndication
    Reference Field Code: 112000000000
    Section: LABVISIT
    Value Map:
      '|Immobility|': 'Yes'
  - Delimiter: '|'
    Derivative Field: MRRIndication
    Derivative Field Code: 112000000482
    Derivative Value: '|Severe Liver Disease (Cirrhosis or MELD score >12)|'
    Derivative Value Code: 112000001482
    Exact Match: 0
    Field: MRRIndLiver
    Reference Field: MRRIndication
    Reference Field Code: 112000000000
    Section: LABVISIT
    Value Map:
      '|Severe Liver Disease (Cirrhosis or MELD score >12)|': 'Yes'
  - Delimiter: '|'
    Derivative Field: MRRIndication
    Derivative Field Code: 112000000482
    Derivative Value: '|Predicted STS MV Repair ROM Greater than or Equal to 6 Percent|'
    Derivative Value Code: 112000001483
    Exact Match: 0
    Field: MRRIndOpM6
    Reference Field: MRRIndication
    Reference Field Code: 112000000000
    Section: LABVISIT
    Value Map:
      '|Predicted STS MV Repair ROM Greater than or Equal to 6 Percent|': 'Yes'
  - Delimiter: '|'
    Derivative Field: MRRIndication
    Derivative Field Code: 112000000482
    Derivative Value: '|Predicted STS MV Replacement ROM Greater than or Equal to 8
    Percent|'
    Derivative Value Code: 112000001484
    Exact Match: 0
    Field: MRRIndOpM8
    Reference Field: MRRIndication
    Reference Field Code: 112000000000
    Section: LABVISIT
    Value Map:
      '|Predicted STS MV Replacement ROM Greater than or Equal to 8 Percent|': 'Yes'
  - Delimiter: '|'
    Derivative Field: MRRIndication
    Derivative Field Code: 112000000482
    Derivative Value: '|Refractory to Guideline Determined Optimal Medical Therapy|'
    Derivative Value Code: 112000001944
    Exact Match: 0
    Field: MRRIndOptimalMedicalTherapy
    Reference Field: MRRIndication
    Reference Field Code: 112000000000
    Section: LABVISIT
    Value Map:
      '|Refractory to Guideline Determined Optimal Medical Therapy|': 'Yes'
  - Delimiter: '|'
    Derivative Field: MRRIndication
    Derivative Field Code: 112000000482
    Derivative Value: '|Other|'
    Derivative Value Code: 100000351
    Exact Match: 0
    Field: MRRIndOther
    Reference Field: MRRIndication
    Reference Field Code: 112000000000
    Section: LABVISIT
    Value Map:
      '|Other|': 'Yes'
  - Delimiter: '|'
    Derivative Field: MRRIndication
    Derivative Field Code: 112000000482
    Derivative Value: '|Porcelain Aorta|'
    Derivative Value Code: 112000001175
    Exact Match: 0
    Field: MRRIndPA
    Reference Field: MRRIndication
    Reference Field Code: 112000000000
    Section: LABVISIT
    Value Map:
      '|Porcelain Aorta|': 'Yes'
  - Delimiter: '|'
    Derivative Field: MRRIndication
    Derivative Field Code: 112000000482
    Derivative Value: '|RVD with Severe TR|'
    Derivative Value Code: 112000001486
    Exact Match: 0
    Field: MRRIndRVDysfx
    Reference Field: MRRIndication
    Reference Field Code: 112000000000
    Section: LABVISIT
    Value Map:
      '|RVD with Severe TR|': 'Yes'
  - Delimiter: '|'
    Derivative Field: MRRIndication
    Derivative Field Code: 112000000482
    Derivative Value: '|Severe Pulmonary Hypertension|'
    Derivative Value Code: 112000001490
    Exact Match: 0
    Field: MRRIndSeverePulmonaryHypertension
    Reference Field: MRRIndication
    Reference Field Code: 112000000000
    Section: LABVISIT
    Value Map:
      '|Severe Pulmonary Hypertension|': 'Yes'
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Implantable Cardioverter Defibrillator
    Derivative Value Code: 447365002
    Exact Match: 1
    Field: PrevICD
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Aortic Valve Balloon Valvuloplasty
    Derivative Value Code: 77166000
    Exact Match: 1
    Field: PrevProcAVBall
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Aortic Valve Repair Surgery
    Derivative Value Code: 112816004
    Exact Match: 1
    Field: PrevProcAVRepair
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Aortic Valve Replacement Surgery
    Derivative Value Code: 725351001
    Exact Match: 1
    Field: PrevProcAVReplace
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Mitral Valve Repair Surgery
    Derivative Value Code: 384641003
    Exact Match: 1
    Field: PrevProcMVRepair
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Mitral Valve Replacement Surgery
    Derivative Value Code: 53059001
    Exact Match: 1
    Field: PrevProcMVReplace
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Aortic Valve Transcatheter Intervention
    Derivative Value Code: 112000001768
    Exact Match: 1
    Field: PrevProcTCVInt
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Aortic Valve Replacement - Transcatheter
    Derivative Value Code: 41873006
    Exact Match: 1
    Field: PrevProcTCVRep
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Aortic Valve Procedure
    Derivative Value Code: 112000001755
    Exact Match: 1
    Field: PriorAorticValve
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Mitral Valve Annuloplasty Ring Surgery
    Derivative Value Code: 232744004
    Exact Match: 1
    Field: PriorMARingSurg
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Mitral Valve Procedure
    Derivative Value Code: 112000001940
    Exact Match: 1
    Field: PriorMVProc
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Mitral Valve Procedure
    Derivative Value Code: 112000001940
    Exact Match: 1
    Field: PriorMVProcDate
    Reference Field: ProcHistDate
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Permanent Pacemaker
    Derivative Value Code: 449397007
    Exact Match: 1
    Field: Pacemaker
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Permanent Pacemaker
    Derivative Value Code: 449397007
    Exact Match: 1
    Field: PriorPacerDate
    Reference Field: ProcHistDate
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: PCI
    Derivative Value Code: 415070008
    Exact Match: 1
    Field: PriorPCI
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: PCI
    Derivative Value Code: 415070008
    Exact Match: 1
    Field: PriorPCIDate
    Reference Field: ProcHistDate
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Pulmonic Valve Procedure
    Derivative Value Code: 112000001769
    Exact Match: 1
    Field: PriorPulmonicProc
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Mitral Valve Transcatheter Intervention
    Derivative Value Code: 112000001773
    Exact Match: 1
    Field: PriorTMVR
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Tricuspid Valve Procedure
    Derivative Value Code: 112000001941
    Exact Match: 1
    Field: PriorTricuspidProc
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Tricuspid Valve Procedure
    Derivative Value Code: 112000001941
    Exact Match: 1
    Field: TricuspidValveProcedureDate
    Reference Field: ProcHistDate
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Tricuspid Valve Repair Surgery
    Derivative Value Code: 384643000
    Exact Match: 1
    Field: PriorTricuspidValveRepairSurgery
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Tricuspid Valve Replacement Surgery
    Derivative Value Code: 25236004
    Exact Match: 1
    Field: PriorTricuspidValveReplaceSurgery
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Tricuspid Valve Replacement - Transcatheter
    Derivative Value Code: 112000001977
    Exact Match: 1
    Field: PriorTricuspidValveReplaceTranscatheter
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Tricuspid Valve Transcatheter Intervention
    Derivative Value Code: 112000001779
    Exact Match: 1
    Field: PriorTricuspidValveTranscathInterven
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: '|'
    Derivative Field: TVTProType
    Derivative Field Code: 112000001167
    Derivative Value: '|TAVR|'
    Derivative Value Code: 41873006
    Exact Match: 0
    Field: ProcTAVR
    Reference Field: TVTProType
    Reference Field Code: 112000001167
    Section: LABVISIT
    Value Map:
      '|TAVR|': 'Yes'
  - Delimiter: '|'
    Derivative Field: TVTProType
    Derivative Field Code: 112000001167
    Derivative Value: '|TMVR|'
    Derivative Value Code: 112000001458
    Exact Match: 0
    Case Match: 1
    Field: ProcTMVR
    Reference Field: TVTProType
    Reference Field Code: 112000001167
    Section: LABVISIT
    Value Map:
      '|TMVR|': 'Yes'
  - Delimiter: '|'
    Derivative Field: TVTProType
    Derivative Field Code: 112000001167
    Derivative Value: '|TMVr|'
    Derivative Value Code: 112000001801
    Exact Match: 0
    Case Match: 1
    Field: ProcTMVrepair
    Reference Field: TVTProType
    Reference Field Code: 112000001167
    Section: LABVISIT
    Value Map:
      '|TMVr|': 'Yes'
  - Delimiter: '|'
    Derivative Field: TVTProType
    Derivative Field Code: 112000001167
    Derivative Value: '|Tricuspid Valve Procedure|'
    Derivative Value Code: 112000001977
    Exact Match: 0
    Field: ProcTTVP
    Reference Field: TVTProType
    Reference Field Code: 112000001167
    Section: LABVISIT
    Value Map:
      '|Tricuspid Valve Procedure|': 'Yes'
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Myocardial Infarction
    Derivative Value Code: 22298006
    Exact Match: 0
    Field: PriorMI
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHIS
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Peripheral Arterial Disease
    Derivative Value Code: 399957001
    Exact Match: 0
    Field: PriorPAD
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHIS
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Cerebrovascular Accident
    Derivative Value Code: 230690007
    Exact Match: 0
    Field: PriorStroke
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHIS
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Cerebrovascular Accident
    Derivative Value Code: 230690007
    Exact Match: 0
    Field: PriorStrokeDate
    Reference Field: CondHistDate
    Reference Field Code: 312850006
    Section: CONDHIS
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcLeafClip
    Derivative Field Code: 112000000208
    Derivative Value: 'Yes'
    Derivative Value Code: null
    Exact Match: 1
    Field: MRR_ConvSurgAccess
    Reference Field: ConvSurgAccess
    Reference Field Code: 112000001327
    Section: LABVISIT
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcLeafClip
    Derivative Field Code: 112000000208
    Derivative Value: 'Yes'
    Derivative Value Code: null
    Exact Match: 1
    Field: MRR_ConvSurgAccessReason
    Reference Field: ConvSurgAccessReason
    Reference Field Code: 112000001327
    Section: LABVISIT
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcLeafClip
    Derivative Field Code: 112000000208
    Derivative Value: 'Yes'
    Derivative Value Code: null
    Exact Match: 1
    Field: MRR_MVSupport
    Reference Field: MechVentSupp
    Reference Field Code: 100014009
    Section: LABVISIT
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcLeafClip
    Derivative Field Code: 112000000208
    Derivative Value: 'Yes'
    Derivative Value Code: null
    Exact Match: 1
    Field: MRR_SupportType
    Reference Field: MVSupportDevice
    Reference Field Code: 100001278
    Section: LABVISIT
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcLeafClip
    Derivative Field Code: 112000000208
    Derivative Value: 'Yes'
    Derivative Value Code: null
    Exact Match: 1
    Field: MRR_SupportTiming
    Reference Field: MVSupportTiming
    Reference Field Code: 100014009
    Section: LABVISIT
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcLeafClip
    Derivative Field Code: 112000000208
    Derivative Value: 'Yes'
    Derivative Value Code: null
    Exact Match: 1
    Field: MRR_ProcRmArrivalDate
    Reference Field: TVTPRocedureEntryTime
    Reference Field Code: 112000001197
    Section: LABVISIT
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcLeafClip
    Derivative Field Code: 112000000208
    Derivative Value: 'Yes'
    Derivative Value Code: null
    Exact Match: 1
    Field: MRR_ProcRmArrivalTime
    Reference Field: TVTPRocedureEntryTime
    Reference Field Code: 112000001197
    Section: LABVISIT
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcLeafClip
    Derivative Field Code: 112000000208
    Derivative Value: 'Yes'
    Derivative Value Code: null
    Exact Match: 1
    Field: MRR_ProcedureAbort
    Reference Field: TVTProcedureAbort
    Reference Field Code: 112000000515
    Section: LABVISIT
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcLeafClip
    Derivative Field Code: 112000000208
    Derivative Value: 'Yes'
    Derivative Value Code: null
    Exact Match: 1
    Field: MRR_ProcedureAbortReason
    Reference Field: ProcedureAbortReason
    Reference Field Code: 112000001292
    Section: LABVISIT
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcLeafClip
    Derivative Field Code: 112000000208
    Derivative Value: 'Yes'
    Derivative Value Code: null
    Exact Match: 1
    Field: MRR_ProcedureAbortAction
    Reference Field: ProcedureAbortAction
    Reference Field Code: 112000001468
    Section: LABVISIT
    Value Map: null
  - Delimiter: null
    Derivative Field: TVTProType
    Derivative Field Code: 112000001167
    Derivative Value: Tricuspid Valve Procedure
    Derivative Value Code: null
    Exact Match: 0
    Field: TTVP_ConvSurgMitral
    Reference Field: ConvSurgAccess
    Reference Field Code: 112000001327
    Section: LABVISIT
    Value Map: null
  - Delimiter: null
    Derivative Field: TVTProType
    Derivative Field Code: 112000001167
    Derivative Value: Tricuspid Valve Procedure
    Derivative Value Code: null
    Exact Match: 0
    Field: TTVP_ConvSurgAccessReason
    Reference Field: ConvSurgAccessReason
    Reference Field Code: 112000001327
    Section: LABVISIT
    Value Map: null
  - Delimiter: null
    Derivative Field: TVTProType
    Derivative Field Code: 112000001167
    Derivative Value: Tricuspid Valve Procedure
    Derivative Value Code: null
    Exact Match: 0
    Field: TTVP_MVSupport
    Reference Field: MechVentSupp
    Reference Field Code: 100014009
    Section: LABVISIT
    Value Map: null
  - Delimiter: null
    Derivative Field: TVTProType
    Derivative Field Code: 112000001167
    Derivative Value: Tricuspid Valve Procedure
    Derivative Value Code: null
    Exact Match: 0
    Field: TTVP_SupportType
    Reference Field: MVSupportDevice
    Reference Field Code: 100001278
    Section: LABVISIT
    Value Map: null
  - Delimiter: null
    Derivative Field: TVTProType
    Derivative Field Code: 112000001167
    Derivative Value: Tricuspid Valve Procedure
    Derivative Value Code: null
    Exact Match: 0
    Field: TTVP_SupportTiming
    Reference Field: MVSupportTiming
    Reference Field Code: 100014009
    Section: LABVISIT
    Value Map: null
  - Delimiter: null
    Derivative Field: TVTProType
    Derivative Field Code: 112000001167
    Derivative Value: Tricuspid Valve Procedure
    Derivative Value Code: null
    Exact Match: 0
    Field: TTVP_ProcedureAbort
    Reference Field: TVTProcedureAbort
    Reference Field Code: 112000000515
    Section: LABVISIT
    Value Map: null
  - Delimiter: null
    Derivative Field: TVTProType
    Derivative Field Code: 112000001167
    Derivative Value: Tricuspid Valve Procedure
    Derivative Value Code: null
    Exact Match: 0
    Field: TTVP_ProcedureAbortReason
    Reference Field: ProcedureAbortReason
    Reference Field Code: 112000001292
    Section: LABVISIT
    Value Map: null
  - Delimiter: null
    Derivative Field: TVTProType
    Derivative Field Code: 112000001167
    Derivative Value: Tricuspid Valve Procedure
    Derivative Value Code: null
    Exact Match: 0
    Field: TTVP_ProcedureAbortAction
    Reference Field: ProcedureAbortAction
    Reference Field Code: 112000001468
    Section: LABVISIT
    Value Map: null
  - Delimiter: null
    Derivative Field: TVTProType
    Derivative Field Code: 112000001167
    Derivative Value: Tricuspid Valve Procedure
    Derivative Value Code: null
    Exact Match: 0
    Field: TTVP_OperatorReason
    Reference Field: OperatorReason
    Reference Field Code: 112000001281
    Section: LABVISIT
    Value Map: null
  - Delimiter: null
    Derivative Field: MVSupportTiming
    Derivative Field Code: 100014009
    Derivative Value: In place at start of procedure
    Derivative Value Code: 100001280
    Exact Match: 1
    Field: PreProcMechAssist
    Reference Field: MVSupportDevice
    Reference Field Code: 100001278
    Section: LABVISIT
    Value Map:
      'Cardiopulmonary Support (CPS)': 'Yes - Cardiopulmonary Support (CPS)'
      'Extracorporeal membrane oxygenation (ECMO)': 'Yes - Extracorporeal membrane oxygenation (ECMO)'
      'Impella: Left Ventricular Support': 'Yes - Impella: Left Ventricular Support'
      'Impella: Right Ventricular Support': 'Yes - Impella: Right Ventricular Support'
      'Intra-aortic balloon pump (IABP)': 'Yes - IABP'
      'Isolated Right Ventricular Support': 'Yes - Isolated Right Ventricular Support'
      'Left ventricular assist device (LVAD)': 'Yes - Left ventricular assist device (LVAD)'
      'Right Ventricular Assist Device (RVAD)': 'Yes - Right Ventricular Assist Device (RVAD)'
      'Percutaneous Heart Pump (PHP)': 'Yes - Percutaneous Heart Pump (PHP)'
      'TandemHeart': 'Yes - TandemHeart'
      'Other': 'Yes - Other'

  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Atrial Flutter
    Derivative Value Code: 5370000
    Exact Match: 0
    Field: aflutter
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHIS
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Chronic Lung Disease
    Derivative Value Code: 413839001
    Exact Match: 0
    Field: chroniclungdisease
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHIS
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Conduction Defect
    Derivative Value Code: 44808001
    Exact Match: 0
    Field: conductiondefect
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHIS
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Transient Ischemic Attack (TIA)
    Derivative Value Code: 266257000
    Exact Match: 0
    Field: cvdtia
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHIS
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Diabetes Mellitus
    Derivative Value Code: 73211009
    Exact Match: 0
    Field: diabetes
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHIS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin Converting Enzyme Inhibitor
    Derivative Value Code: 41549009
    Exact Match: 0
    Field: discharge_ace_inhibitor_any
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aldosterone Antagonist
    Derivative Value Code: 372603003
    Exact Match: 0
    Field: discharge_aldosterone_antagonists
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin II Receptor Blocker
    Derivative Value Code: 372913009
    Exact Match: 0
    Field: discharge_arb_any
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aspirin
    Derivative Value Code: 1191
    Exact Match: 0
    Field: discharge_aspirin_any
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Direct thrombin inhibitor
    Derivative Value Code: 414010005
    Exact Match: 0
    Field: discharge_direct_thrombin_inhibitor
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Diuretics Not Otherwise Specified
    Derivative Value Code: 112000001417
    Exact Match: 0
    Field: discharge_diuretics_other
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Direct Factor Xa Inhibitor
    Derivative Value Code: 112000000696
    Exact Match: 0
    Field: discharge_factor_xa_inhibitor
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Loop Diuretics
    Derivative Value Code: 29051009
    Exact Match: 0
    Field: discharge_loop_diuretic
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Loop Diuretics
    Derivative Value Code: 29051009
    Exact Match: 0
    Field: meddose_discharge
    Reference Field: DischMed_LoopDiureticDose
    Reference Field Code: 112000001975
    Section: DISCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: P2Y12 Antagonist
    Derivative Value Code: 112000001003
    Exact Match: 0
    Field: discharge_p2y12_any
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Thiazides
    Derivative Value Code: 372747003
    Exact Match: 0
    Field: discharge_thiazides
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Warfarin
    Derivative Value Code: 11289
    Exact Match: 0
    Field: discharge_warfarin
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Beta Blocker
    Derivative Value Code: 33252009
    Exact Match: 0
    Field: dischargebetablockerany
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Aortic Valve Procedure
    Derivative Value Code: 112000001755
    Exact Match: 0
    Field: prioraorticvalvedate
    Reference Field: ProcHistDate
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Atrial Fibrillation
    Derivative Value Code: 49436004
    Exact Match: 0
    Field: prioratrialfib
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHIS
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Coronary Artery Bypass Graft
    Derivative Value Code: 232717009
    Exact Match: 0
    Field: PriorCABG
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Coronary Artery Bypass Graft
    Derivative Value Code: 232717009
    Exact Match: 0
    Field: priorcabgdate
    Reference Field: ProcHistDate
    Reference Field Code: 416940007
    Section: PROCHIST
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Cardiomyopathy
    Derivative Value Code: 85898001
    Exact Match: 0
    Field: priorcardiomyopathy
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHIS
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Carotid Artery Stenosis
    Derivative Value Code: 64586002
    Exact Match: 0
    Field: priorcarotidstenosis
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHIS
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Cerebrovascular Disease
    Derivative Value Code: 62914000
    Exact Match: 0
    Field: priorcvd
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHIS
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Dementia - Moderate to Severe
    Derivative Value Code: 112000001493
    Exact Match: 0
    Field: priordementia
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHIS
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Heart Failure
    Derivative Value Code: 84114007
    Exact Match: 0
    Field: priorhf
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHIS
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Liver Disease
    Derivative Value Code: 235856003
    Exact Match: 0
    Field: priorliverdisease
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHIS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin Converting Enzyme Inhibitor
    Derivative Value Code: 41549009
    Exact Match: 1
    Field: priormed_ace_i_or_arb_any
    Reference Field: PriorMedAdmin_Hom
    Reference Field Code: 33633005
    Section: HOMEMEDS
    Value Map:
      Not Prescribed - No Reason: Not Prescribed - No Reason
      'Yes': Yes - Prescribed
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Aldosterone Antagonist
    Derivative Value Code: 372603003
    Exact Match: 1
    Field: priormed_aldosterone_antagonists
    Reference Field: PriorMedAdmin_Hom
    Reference Field Code: 33633005
    Section: HOMEMEDS
    Value Map:
      Not Prescribed - No Reason: Not Prescribed - No Reason
      'Yes': Yes - Prescribed
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin Receptor-Neprilysin Inhibitor
    Derivative Value Code: 112000001832
    Exact Match: 1
    Field: priormed_angiorecepneprilinhib
    Reference Field: PriorMedAdmin_Hom
    Reference Field Code: 33633005
    Section: HOMEMEDS
    Value Map:
      Not Prescribed - No Reason: Not Prescribed - No Reason
      'Yes': Yes - Prescribed
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Anticoagulant
    Derivative Value Code: 112000001416
    Exact Match: 1
    Field: priormed_anticoagulants_any
    Reference Field: PriorMedAdmin_Hom
    Reference Field Code: 33633005
    Section: HOMEMEDS
    Value Map:
      Not Prescribed - No Reason: Not Prescribed - No Reason
      'Yes': Yes - Prescribed
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Aspirin
    Derivative Value Code: 1191
    Exact Match: 1
    Field: priormed_aspirin
    Reference Field: PriorMedAdmin_Hom
    Reference Field Code: 33633005
    Section: HOMEMEDS
    Value Map:
      Not Prescribed - No Reason: Not Prescribed - No Reason
      'Yes': Yes - Prescribed
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Beta Blocker
    Derivative Value Code: 33252009
    Exact Match: 1
    Field: priormed_beta_blocker_any
    Reference Field: PriorMedAdmin_Hom
    Reference Field Code: 33633005
    Section: HOMEMEDS
    Value Map:
      Not Prescribed - No Reason: Not Prescribed - No Reason
      'Yes': Yes - Prescribed
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Diuretics Not Otherwise Specified
    Derivative Value Code: 112000001417
    Exact Match: 1
    Field: priormed_diuretics_other
    Reference Field: PriorMedAdmin_Hom
    Reference Field Code: 33633005
    Section: HOMEMEDS
    Value Map:
      Not Prescribed - No Reason: Not Prescribed - No Reason
      'Yes': Yes - Prescribed
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Loop Diuretics
    Derivative Value Code: 29051009
    Exact Match: 1
    Field: priormed_loop_diuretic
    Reference Field: PriorMedAdmin_Hom
    Reference Field Code: 33633005
    Section: HOMEMEDS
    Value Map:
      Not Prescribed - No Reason: Not Prescribed - No Reason
      'Yes': Yes - Prescribed
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: P2Y12 Antagonist
    Derivative Value Code: 112000001003
    Exact Match: 1
    Field: priormed_p2y12antagonist
    Reference Field: PriorMedAdmin_Hom
    Reference Field Code: 33633005
    Section: HOMEMEDS
    Value Map:
      Not Prescribed - No Reason: Not Prescribed - No Reason
      'Yes': Yes - Prescribed
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Selective Sinus Node I/f Channel Inhibitor
    Derivative Value Code: 112000001831
    Exact Match: 1
    Field: priormed_selectivesinusnode
    Reference Field: PriorMedAdmin_Hom
    Reference Field Code: 33633005
    Section: HOMEMEDS
    Value Map:
      Not Prescribed - No Reason: Not Prescribed - No Reason
      'Yes': Yes - Prescribed
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Thiazides
    Derivative Value Code: 372747003
    Exact Match: 1
    Field: priormed_thiazides
    Reference Field: PriorMedAdmin_Hom
    Reference Field Code: 33633005
    Section: HOMEMEDS
    Value Map:
      Not Prescribed - No Reason: Not Prescribed - No Reason
      'Yes': Yes - Prescribed
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin II Receptor Blocker
    Derivative Value Code: 372913009
    Exact Match: 1
    Field: priormedangiotensiniireceptorblocker
    Reference Field: PriorMedAdmin_Hom
    Reference Field Code: 33633005
    Section: HOMEMEDS
    Value Map:
      Not Prescribed - No Reason: Not Prescribed - No Reason
      'Yes': Yes - Prescribed
  - Delimiter: null
    Derivative Field: STSRiskScoreType
    Derivative Field Code: 112000001412
    Derivative Value: Society of Thoracic Surgeons Risk Score for Mitral Valve Repair
    Derivative Value Code: 112000001795
    Exact Match: 0
    Field: stsriskmvrepair
    Reference Field: STSRiskScoreValue
    Reference Field Code: 112000001797
    Section: STSRISK
    Value Map: null
  - Delimiter: null
    Derivative Field: STSRiskScoreType
    Derivative Field Code: 112000001412
    Derivative Value: Society of Thoracic Surgeons Risk Score for Mitral Valve Replacement
    Derivative Value Code: 112000001793
    Exact Match: 0
    Field: stsriskmvreplace
    Reference Field: STSRiskScoreValue
    Reference Field Code: 112000001797
    Section: STSRISK
    Value Map: null
  - Delimiter: null
    Derivative Field: STSRiskScoreType
    Derivative Field Code: 112000001412
    Derivative Value: Society of Thoracic Surgeons Risk Score for Aortic Valve Replacement
    Derivative Value Code: 112000001796
    Exact Match: 0
    Field: stsriskscore
    Reference Field: STSRiskScoreValue
    Reference Field Code: 112000001797
    Section: STSRISK
    Value Map: null
  - Delimiter: null
    Derivative Field: TobaccoUse
    Derivative Field Code: 110483000
    Derivative Value: CURRENT - SOME DAYS|CURRENT - EVERY DAY
    Derivative Value Code: null
    Exact Match: 0
    Regex Match: 1
    Field: Smoker
    Reference Field: TobaccoUse
    Reference Field Code: 110483000
    Section: EPISODEOFCARE
    Value Map:
      'Current - Every Day': 'Yes'
      'Current - Some Days': 'Yes'
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Loop Diuretics
    Derivative Value Code: null
    Exact Match: 0
    Field: priormeddose_home
    Reference Field: HomeMed_LoopDiureticDose
    Reference Field Code: 112000001975
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Hostile Chest
    Derivative Value Code: 112000001489
    Exact Match: 0
    Field: hostilechest
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHIS
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Hypertension
    Derivative Value Code: 38341003
    Exact Match: 0
    Field: hypertension
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHIS
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Endocarditis
    Derivative Value Code: 56819008
    Exact Match: 0
    Field: infendo
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHIS
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Porcelain Aorta
    Derivative Value Code: 112000001175
    Exact Match: 0
    Field: porcelainaorta
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHIS
    Value Map: null
  - Delimiter: null
    Derivative Field: MVDEtio
    Derivative Field Code: 11851006
    Derivative Value: Degenerative MR (Primary)
    Derivative Value Code: 112000001277
    Exact Match: 1
    Field: MVDEtioDMR
    Reference Field: MVDEtio
    Reference Field Code: 11851006
    Section: LABVISIT
    Value Map:
      Degenerative MR (Primary): 'Yes'
  - Delimiter: null
    Derivative Field: MVDEtio
    Derivative Field Code: 11851006
    Derivative Value: Endocarditis
    Derivative Value Code: 56819008
    Exact Match: 1
    Field: MVDEtioEndoc
    Reference Field: MVDEtio
    Reference Field Code: 11851006
    Section: LABVISIT
    Value Map:
      Endocarditis: 'Yes'
  - Delimiter: null
    Derivative Field: MVDEtio
    Derivative Field Code: 11851006
    Derivative Value: Functional MR (Secondary)
    Derivative Value Code: 112000001276
    Exact Match: 1
    Field: MVDEtioFMR
    Reference Field: MVDEtio
    Reference Field Code: 11851006
    Section: LABVISIT
    Value Map:
      Functional MR (Secondary): 'Yes'
  - Delimiter: null
    Derivative Field: MVDEtio
    Derivative Field Code: 11851006
    Derivative Value: Post Inflammatory
    Derivative Value Code: 112000001441
    Exact Match: 1
    Field: MVDEtioInflam
    Reference Field: MVDEtio
    Reference Field Code: 11851006
    Section: LABVISIT
    Value Map:
      Post Inflammatory: 'Yes'
  - Delimiter: null
    Derivative Field: MVDEtio
    Derivative Field Code: 11851006
    Derivative Value: None
    Derivative Value Code: 100001231
    Exact Match: 1
    Field: MVDEtioNone
    Reference Field: MVDEtio
    Reference Field Code: 11851006
    Section: LABVISIT
    Value Map:
      None: 'Yes'
  - Delimiter: null
    Derivative Field: MVDEtio
    Derivative Field Code: 11851006
    Derivative Value: Other
    Derivative Value Code: 100000351
    Exact Match: 1
    Field: MVDEtioOther
    Reference Field: MVDEtio
    Reference Field Code: 11851006
    Section: LABVISIT
    Value Map:
      Other: 'Yes'
  - Delimiter: '|'
    Derivative Field: TVTProType
    Derivative Field Code: 112000001167
    Derivative Value: '|TMVR|'
    Derivative Value Code: null
    Exact Match: 0
    Case Match: 0
    Field: MVR_ContrastVol
    Reference Field: ContrastVol
    Reference Field Code: 80242-1
    Section: LABVISIT
    Value Map: null
  - Delimiter: '|'
    Derivative Field: TVTProType
    Derivative Field Code: 112000001167
    Derivative Value: '|TMVR|'
    Derivative Value Code: null
    Exact Match: 0
    Case Match: 1
    Field: MVR_ConvSurgAccess
    Reference Field: ConvSurgAccessReason
    Reference Field Code: 112000001327
    Section: LABVISIT
    Value Map: null
  - Delimiter: '|'
    Derivative Field: TVTProType
    Derivative Field Code: 112000001167
    Derivative Value: '|TMVR|'
    Derivative Value Code: null
    Exact Match: 0
    Case Match: 1
    Field: MVR_ConvSurgMitral
    Reference Field: ConvSurgAccess
    Reference Field Code: 112000001327
    Section: LABVISIT
    Value Map: null
  - Delimiter: '|'
    Derivative Field: TVTProType
    Derivative Field Code: 112000001167
    Derivative Value: '|TMVR|'
    Derivative Value Code: null
    Exact Match: 0
    Case Match: 1
    Field: MVR_MVSupport
    Reference Field: MechVentSupp
    Reference Field Code: 100014009
    Section: LABVISIT
    Value Map: null
  - Delimiter: '|'
    Derivative Field: TVTProType
    Derivative Field Code: 112000001167
    Derivative Value: '|TMVR|'
    Derivative Value Code: null
    Exact Match: 0
    Case Match: 1
    Field: MVR_MVSupportTiming
    Reference Field: MVSupportTiming
    Reference Field Code: 100014009
    Section: LABVISIT
    Value Map: null
  - Delimiter: '|'
    Derivative Field: TVTProType
    Derivative Field Code: 112000001167
    Derivative Value: '|TMVR|'
    Derivative Value Code: null
    Exact Match: 0
    Case Match: 1
    Field: MVR_MVSupportType
    Reference Field: MVSupportDevice
    Reference Field Code: 100001278
    Section: LABVISIT
    Value Map: null
  - Delimiter: '|'
    Derivative Field: TVTProType
    Derivative Field Code: 112000001167
    Derivative Value: '|TMVR|'
    Derivative Value Code: null
    Exact Match: 0
    Case Match: 1
    Field: MVR_OperatorReason
    Reference Field: OperatorReason
    Reference Field Code: 112000001281
    Section: LABVISIT
    Value Map: null
  - Delimiter: '|'
    Derivative Field: TVTProType
    Derivative Field Code: 112000001167
    Derivative Value: '|TMVR|'
    Derivative Value Code: null
    Exact Match: 0
    Case Match: 1
    Field: MVR_ProcedureAbort
    Reference Field: TVTProcedureAbort
    Reference Field Code: 112000000515
    Section: LABVISIT
    Value Map: null
  - Delimiter: '|'
    Derivative Field: TVTProType
    Derivative Field Code: 112000001167
    Derivative Value: '|TMVR|'
    Derivative Value Code: null
    Exact Match: 0
    Case Match: 1
    Field: MVR_ProcedureAbortAction
    Reference Field: ProcedureAbortAction
    Reference Field Code: 112000001468
    Section: LABVISIT
    Value Map: null
  - Delimiter: '|'
    Derivative Field: TVTProType
    Derivative Field Code: 112000001167
    Derivative Value: '|TMVR|'
    Derivative Value Code: null
    Exact Match: 0
    Case Match: 1
    Field: MVR_ProcedureAbortReason
    Reference Field: ProcedureAbortReason
    Reference Field Code: 112000001292
    Section: LABVISIT
    Value Map: null
  - Delimiter: '|'
    Derivative Field: TVTProType
    Derivative Field Code: 112000001167
    Derivative Value: '|TAVR|'
    Derivative Value Code: null
    Exact Match: 0
    Case Match: 0
    Field: ContrastVol
    Reference Field: ContrastVol
    Reference Field Code: 80242-1
    Section: LABVISIT
    Value Map: null
  - Delimiter: null
    Derivative Field: IncrementalId
    Derivative Field Code: 112000001955
    Derivative Value: 1
    Derivative Value Code: null
    Exact Match: 1
    Field: tvtopa_firstname
    Reference Field: TVT_Oper_FirstName
    Reference Field Code: 112000001955
    Section: OPRTRINFO
    Value Map: null
  - Delimiter: null
    Derivative Field: IncrementalId
    Derivative Field Code: 112000001955
    Derivative Value: 1
    Derivative Value Code: null
    Exact Match: 1
    Field: tvtopa_midname
    Reference Field: TVT_Oper_MidName
    Reference Field Code: 112000001955
    Section: OPRTRINFO
    Value Map: null
  - Delimiter: null
    Derivative Field: IncrementalId
    Derivative Field Code: 112000001955
    Derivative Value: 1
    Derivative Value Code: null
    Exact Match: 1
    Field: tvtopa_lastname
    Reference Field: TVT_Oper_LastName
    Reference Field Code: 112000001955
    Section: OPRTRINFO
    Value Map: null
  - Delimiter: null
    Derivative Field: IncrementalId
    Derivative Field Code: 112000001955
    Derivative Value: 1
    Derivative Value Code: null
    Exact Match: 1
    Field: tvtopa_npi
    Reference Field: TVT_Oper_NPI
    Reference Field Code: 112000001955
    Section: OPRTRINFO
    Value Map: null
  - Delimiter: null
    Derivative Field: IncrementalId
    Derivative Field Code: 112000001955
    Derivative Value: 2
    Derivative Value Code: null
    Exact Match: 1
    Field: tvtopb_firstname
    Reference Field: TVT_Oper_FirstName
    Reference Field Code: 112000001955
    Section: OPRTRINFO
    Value Map: null
  - Delimiter: null
    Derivative Field: IncrementalId
    Derivative Field Code: 112000001955
    Derivative Value: 2
    Derivative Value Code: null
    Exact Match: 1
    Field: tvtopb_midname
    Reference Field: TVT_Oper_MidName
    Reference Field Code: 112000001955
    Section: OPRTRINFO
    Value Map: null
  - Delimiter: null
    Derivative Field: IncrementalId
    Derivative Field Code: 112000001955
    Derivative Value: 2
    Derivative Value Code: null
    Exact Match: 1
    Field: tvtopb_lastname
    Reference Field: TVT_Oper_LastName
    Reference Field Code: 112000001955
    Section: OPRTRINFO
    Value Map: null
  - Delimiter: null
    Derivative Field: IncrementalId
    Derivative Field Code: 112000001955
    Derivative Value: 2
    Derivative Value Code: null
    Exact Match: 1
    Field: tvtopb_npi
    Reference Field: TVT_Oper_NPI
    Reference Field Code: 112000001955
    Section: OPRTRINFO
    Value Map: null
  - Delimiter: null
    Derivative Field: IncrementalId
    Derivative Field Code: 112000001955
    Derivative Value: 3
    Derivative Value Code: null
    Exact Match: 1
    Field: tvtopc_firstname
    Reference Field: TVT_Oper_FirstName
    Reference Field Code: 112000001955
    Section: OPRTRINFO
    Value Map: null
  - Delimiter: null
    Derivative Field: IncrementalId
    Derivative Field Code: 112000001955
    Derivative Value: 3
    Derivative Value Code: null
    Exact Match: 1
    Field: tvtopc_midname
    Reference Field: TVT_Oper_MidName
    Reference Field Code: 112000001955
    Section: OPRTRINFO
    Value Map: null
  - Delimiter: null
    Derivative Field: IncrementalId
    Derivative Field Code: 112000001955
    Derivative Value: 3
    Derivative Value Code: null
    Exact Match: 1
    Field: tvtopc_lastname
    Reference Field: TVT_Oper_LastName
    Reference Field Code: 112000001955
    Section: OPRTRINFO
    Value Map: null
  - Delimiter: null
    Derivative Field: IncrementalId
    Derivative Field Code: 112000001955
    Derivative Value: 3
    Derivative Value Code: null
    Exact Match: 1
    Field: tvtopc_npi
    Reference Field: TVT_Oper_NPI
    Reference Field Code: 112000001955
    Section: OPRTRINFO
    Value Map: null
  - Delimiter: null
    Derivative Field: IncrementalId
    Derivative Field Code: 2.16.840.1.113883.3.3478.4.852
    Derivative Value: 1
    Derivative Value Code: null
    Exact Match: 1
    Field: studyptid
    Reference Field: studyptid
    Reference Field Code: 2.16.840.1.113883.3.3478.4.852
    Section: RSTUDY
    Value Map: null
  - Delimiter: null
    Derivative Field: IncrementalId
    Derivative Field Code: 100001096
    Derivative Value: 1
    Derivative Value Code: null
    Exact Match: 1
    Field: studyname
    Reference Field: studyname
    Reference Field Code: 100001096
    Section: RSTUDY
    Value Map: null
  - Delimiter: null
    Derivative Field: IncrementalId
    Derivative Field Code: 2.16.840.1.113883.3.3478.4.852
    Derivative Value: 2
    Derivative Value Code: null
    Exact Match: 1
    Field: studyptid2
    Reference Field: studyptid
    Reference Field Code: 2.16.840.1.113883.3.3478.4.852
    Section: RSTUDY
    Value Map: null
  - Delimiter: null
    Derivative Field: IncrementalId
    Derivative Field Code: 100001096
    Derivative Value: 2
    Derivative Value Code: null
    Exact Match: 1
    Field: studyname2
    Reference Field: studyname
    Reference Field Code: 100001096
    Section: RSTUDY
    Value Map: null
  - Delimiter: null
    Derivative Field: IncrementalId
    Derivative Field Code: 2.16.840.1.113883.3.3478.4.852
    Derivative Value: 3
    Derivative Value Code: null
    Exact Match: 1
    Field: studyptid3
    Reference Field: studyptid
    Reference Field Code: 2.16.840.1.113883.3.3478.4.852
    Section: RSTUDY
    Value Map: null
  - Delimiter: null
    Derivative Field: IncrementalId
    Derivative Field Code: 100001096
    Derivative Value: 3
    Derivative Value Code: null
    Exact Match: 1
    Field: studyname3
    Reference Field: studyname
    Reference Field Code: 100001096
    Section: RSTUDY
    Value Map: null
  - Delimiter: null
    Derivative Field: IncrementalId
    Derivative Field Code: 2.16.840.1.113883.3.3478.4.852
    Derivative Value: 4
    Derivative Value Code: null
    Exact Match: 1
    Field: studyptid4
    Reference Field: studyptid
    Reference Field Code: 2.16.840.1.113883.3.3478.4.852
    Section: RSTUDY
    Value Map: null
  - Delimiter: null
    Derivative Field: IncrementalId
    Derivative Field Code: 100001096
    Derivative Value: 4
    Derivative Value Code: null
    Exact Match: 1
    Field: studyname4
    Reference Field: studyname
    Reference Field Code: 100001096
    Section: RSTUDY
    Value Map: null
  - Delimiter: null
    Derivative Field: IncrementalId
    Derivative Field Code: 2.16.840.1.113883.3.3478.4.852
    Derivative Value: 5
    Derivative Value Code: null
    Exact Match: 1
    Field: studyptid5
    Reference Field: studyptid
    Reference Field Code: 2.16.840.1.113883.3.3478.4.852
    Section: RSTUDY
    Value Map: null
  - Delimiter: null
    Derivative Field: IncrementalId
    Derivative Field Code: 100001096
    Derivative Value: 5
    Derivative Value Code: null
    Exact Match: 1
    Field: studyname5
    Reference Field: studyname
    Reference Field Code: 100001096
    Section: RSTUDY
    Value Map: null
  - Delimiter: null
    Derivative Field: FiveMWTCounter
    Derivative Field Code: 112000002003
    Derivative Value: '1'
    Derivative Value Code: null
    Exact Match: 1
    Field: FiveMWalk1
    Reference Field: FiveMWTTime
    Reference Field Code: 112000001184
    Section: FIVEMWT
    Value Map: null
  - Delimiter: null
    Derivative Field: FiveMWTCounter
    Derivative Field Code: 112000002003
    Derivative Value: '2'
    Derivative Value Code: null
    Exact Match: 1
    Field: FiveMWalk2
    Reference Field: FiveMWTTime
    Reference Field Code: 112000001184
    Section: FIVEMWT
    Value Map: null
  - Delimiter: null
    Derivative Field: FiveMWTCounter
    Derivative Field Code: 112000002003
    Derivative Value: '3'
    Derivative Value Code: null
    Exact Match: 1
    Field: FiveMWalk3
    Reference Field: FiveMWTTime
    Reference Field Code: 112000001184
    Section: FIVEMWT
    Value Map: null
  - Delimiter: null
    Derivative Field: SixMinWalkPerf
    Derivative Field Code: 252478000
    Derivative Value: 'No'
    Derivative Value Code: null
    Exact Match: 1
    Field: SixMinWalkPerf
    Reference Field: SixMinWalkReason
    Reference Field Code: 252478000
    Section: LABVISIT
    Value Map:
      Non-Cardiac Reason: Not performed - Non Cardiac Reason
      Cardiac Reason: Not performed - Cardiac Reason
      Patient Not Willing to Walk: Not performed - Patient Not Willing to Walk
      Not Performed by Site: Not performed by site
pivot_sections:
  - HOMEMEDS
  - DISCMED
  - CONDHIS
  - PROCHIST
  - STSRISK
  - RSTUDY
  - FIVEMWT
  - OPRTRINFO
rename_fields:
  - New Field: DCDate
    Old Field: DischargeDate
  - New Field: Sex
    Old Field: Gender
  - New Field: ProcInotropesAdmin
    Old Field: Intra_Procedure_Inotrope_positive
  - New Field: LAVolIndex_NM
    Old Field: LAVolIndexNM
  - New Field: LAVol_NM
    Old Field: LAVolNM
  - New Field: LVEDV_NM
    Old Field: LVEDVNM
  - New Field: LVEFMeasure
    Old Field: LVEF
  - New Field: LVESV_NM
    Old Field: LVESVNM
  - New Field: LVIDd_NM
    Old Field: LVIDdNM
  - New Field: LVIDs_NM
    Old Field: LVIDsNM
  - New Field: MVDAnnular
    Old Field: MAnnCalc
  - New Field: MLeafCalc_ND
    Old Field: MLeafCalcND
  - New Field: MRepairDevCounter
    Old Field: MRR_Counter
  - New Field: MRepairNum
    Old Field: MRR_LeafletClipNum
  - New Field: MRepair_UDI
    Old Field: MRR_UDIDirectID
  - New Field: PP_AVArea
    Old Field: Post_AorticValveArea
  - New Field: PP_CentralAR
    Old Field: Post_AorticValveInsuffCent
  - New Field: PP_ParaAR
    Old Field: Post_AorticValveInsuffPeri
  - New Field: PP_AVMeanGradient
    Old Field: Post_AorticValveMeanGradient
  - New Field: PP_MV_EOA_MOA
    Old Field: Post_EOAmethod
  - New Field: PP_lvot
    Old Field: Post_LVOT
  - New Field: PostImplant_AVMeanGrad
    Old Field: Post_MeanAVGrad
  - New Field: PP_MVArea
    Old Field: Post_Mvarea
  - New Field: PP_MV_EOA
    Old Field: Post_MVEOA
  - New Field: PP_MVMeanGradient
    Old Field: Post_MVMeanGrad
  - New Field: PP_ParaMR
    Old Field: Post_ParaMR
  - New Field: PP_SAM
    Old Field: Post_SAM
  - New Field: PoProc_Creat
    Old Field: PostProcCreat
  - New Field: HighCrea_ND
    Old Field: PostProcCreatND
  - New Field: PostProcHgb1
    Old Field: PostProcHGB
  - New Field: PProcHgbND
    Old Field: PostProcHGBND
  - New Field: PreProcAnticoag
    Old Field: Pre_Procedure_Anticoagulants_Any
  - New Field: PreOpInotropes
    Old Field: Pre_Procedure_Inotrope_positive
  - New Field: HGBND
    Old Field: PreProcHGBND
  - New Field: HGB
    Old Field: PreProcHGB
  - New Field: TVTProcedureAbort
    Old Field: ProcedureAbort
  - New Field: Albumin
    Old Field: TotalBUMIN
  - New Field: Albumin_ND
    Old Field: TotAlbuminND
  - New Field: Bilirubin
    Old Field: TotBLRBN
  - New Field: BilirubinND
    Old Field: TotBlrbnND
  - New Field: PP_MR
    Old Field: Post_ValvMR
  - New Field: PrevMVReplaceType
    Old Field: PrevProcMVReplaceType
  - New Field: TAVRImplantID
    Old Field: PrevProcTCVModelID
  - New Field: PtRestriction
    Old Field: ptrestriction2
  - New Field: RaceGuamChamorro
    Old Field: raceguamchamo
  - New Field: RacePacificIslandOther
    Old Field: raceotherisland
  - New Field: RAP
    Old Field: rapmean
  - New Field: MeanRAP_ND
    Old Field: rapnm
  - New Field: RVSP
    Old Field: rvsys
  - New Field: ProcStatus
    Old Field: status
  - New Field: SGCDeviceID
    Old Field: steerableguideused
  - New Field: TTV_UDI
    Old Field: ttvudi
  - New Field: TAVRDevCounter
    Old Field: tvtdevicecounter
  - New Field: TAVRDeviceID
    Old Field: tvtdeviceid
  - New Field: TAVRDeviceSN
    Old Field: tvtdeviceserno
  - New Field: ProcedureLocation
    Old Field: tvtlocation
  - New Field: PrimTAVRProcInd
    Old Field: tvtprocedureindication
  - New Field: ProcedureEndDateTime
    Old Field: TVTProcedureStopDate
  - New Field: TV_Unsuccessful
    Old Field: tvunsuccessful
  - New Field: TAV_UDI
    Old Field: Valve_UDIDirectID
  - New Field: VDInsuffMCentral_ND
    Old Field: vdinsuffmcentralnd
  - New Field: PreprocMR
    Old Field: vdinsufm
  - New Field: VDInsufMPara_ND
    Old Field: vdinsufmparand
  - New Field: PreprocTR
    Old Field: vdinsuft
  - New Field: MVD
    Old Field: vdmit
  - New Field: PriorHFAdmit1Year
    Old Field: priorhfadmit
  - New Field: PriorCardArrest
    Old Field: priorcardiacarrest
  - New Field: Prior2WksHF
    Old Field: prior2weekshf
  - New Field: DCStatus
    Old Field: dischargestatus
  - New Field: DCLocation
    Old Field: dischargelocation
  - New Field: DC_CardRehab
    Old Field: dischargecardrehab
  - New Field: DiabControl
    Old Field: diabetescontrol
  - New Field: TAVRDeviceImplantSuccessful
    Old Field: deviceimplantsuccessful
  - New Field: DeathProcedure
    Old Field: deathlocation
  - New Field: PostTransfusion
    Old Field: dc_rbc
  - New Field: DCCreatinineND
    Old Field: dc_creatnd
  - New Field: DCCreatinine
    Old Field: dc_creat
  - New Field: CurrentlyonDialysis
    Old Field: currentdialysis
  - New Field: CardiacOutput_ND
    Old Field: cona
  - New Field: ChronLungDisSeverity_ND
    Old Field: chronlungdisseveritynd
  - New Field: ChronLungDisSeverity
    Old Field: chrlungd
  - New Field: PostProcOccurred
    Old Field: ce_eventoccurred
  - New Field: ProcEvents
    Old Field: ce_eventname
  - New Field: ProcEvents
    Old Field: ce_eventid
  - New Field: IntraPostProcEventDate
    Old Field: ce_eventdate
  - New Field: PriorCMType
    Old Field: cardiomyopathy
  - New Field: PreProcBNPNotDrawn
    Old Field: bnpnd
  - New Field: PreProc_BNPValue
    Old Field: bnp
  - New Field: BI_Find
    Old Field: bifind
  - New Field: AVR_Post_AR
    Old Field: avrpostar
  - New Field: AVPeakGrad
    Old Field: avdstenosispeakgradient
  - New Field: AVMorphology
    Old Field: avdmorphology
  - New Field: AVAnnularCalc
    Old Field: avdannularcalc
  - New Field: ArrivalDateTime
    Old Field: arrivaltime
  - New Field: ArrivalDateTime
    Old Field: arrivaldate
  - New Field: AJ_PriorLiving
    Old Field: ajpriorliving
  - New Field: AJ_DLAE
    Old Field: ajdlae
  - New Field: AJ_CommentsInHosp
    Old Field: ajcommentsinhosp
  - New Field: AJ_BrainImageType
    Old Field: ajbrainimagetype
  - New Field: AJ_BrainImag
    Old Field: ajbrainimag
  - New Field: AJ_AutDxStroke
    Old Field: ajautdxstroke
  - New Field: AJ_NeuroSymptDuration
    Old Field: aj_neurosxduration
  - New Field: MVReintInd
    Old Field: aj_mvind
  - New Field: AFibClassification
    Old Field: afibclass
  - New Field: AdmMName
    Old Field: admmidname
  - New Field: Adj_ERS
    Old Field: adjers
  - New Field: DLCOND
    Old Field: DLCONA
  - New Field: FEV1ND
    Old Field: FEV1NA
  - New Field: FluoroTime
    Old Field: FluroTime
  - New Field: FMRType_ND
    Old Field: FMRTypeND
  - New Field: InflamType_ND
    Old Field: InflamTypeND
  - New Field: INRtvt
    Old Field: Inr
  - New Field: MVDLeafFlail_ND
    Old Field: MVDLeafFlailND
  - New Field: MVDLeafPro_ND
    Old Field: MVDLeafProND
  - New Field: MVDLeafTeth_ND
    Old Field: MVDLeafTethND
  - New Field: MRepairDeviceID
    Old Field: MVR_DeviceID
  - New Field: Intraproc_Post_MR
    Old Field: MVR_Post_MR
  - New Field: PreProcedureNTBNP
    Old Field: NTProBNP
  - New Field: ConcomProc
    Old Field: OtherProc
  - New Field: PAPMean_ND
    Old Field: PAPMeanNM
  - New Field: PAPSys_ND
    Old Field: PAPSysNM
  - New Field: PartID
    Old Field: ParticID
  - New Field: ZipCode
    Old Field: PatZip
  - New Field: ZipCodeNA
    Old Field: PatZipNA
  - New Field: PCWP_ND
    Old Field: PCWPNM
  - New Field: PlateletCtND
    Old Field: PlateletND
  - New Field: PlateletCt
    Old Field: Platelets
  - New Field: POpEKG
    Old Field: PoPEKGPerf
  - New Field: PoP_EKGChange
    Old Field: POpEKG
  - New Field: PP_AR
    Old Field: POPTTAR
  - New Field: PP_MR
    Old Field: POPTTMR
  - New Field: PP_BasalRVDia
    Old Field: PPBasalRVDia
  - New Field: PP_BasalRVDiaND
    Old Field: PPBasalRVDiaND
  - New Field: PP_MidRVDia
    Old Field: PPMidRVDia
  - New Field: PP_MidRVDiaND
    Old Field: PPMidRVDiaND
  - New Field: PP_RVSP
    Old Field: PPRVSP
  - New Field: PP_RVSYSND
    Old Field: PPRVSYSND
  - New Field: PP_TR
    Old Field: PPTR
  - New Field: PP_TVAnnulus
    Old Field: PPTVAnnulus
  - New Field: PP_TVAnnulusND
    Old Field: PPTVAnnulusND
  - New Field: PP_TVDGrad
    Old Field: PPTVDGrad
  - New Field: PP_TVDGradND
    Old Field: PPTVDGradND
  - New Field: TMVReplacementDeviceSN
    Old Field: MVR_DeviceSerNo
  - New Field: MVDevCounter
    Old Field: MVR_DeviceCounter
  - New Field: MV_Unsuccessful
    Old Field: MVR_DeviceImplantSuccessful
  - New Field: TMV_UDI
    Old Field: MVR_Valve_UDIDirectID
  - New Field: MechVentSupp
    Old Field: MVSupport
  - New Field: FluoroDoseDAP2
    Old Field: FluoroDoseDAP
  - New Field: FluoroDoseDAP2_unit
    Old Field: FluoroDoseDAPUnit
  - New Field: ContrastVol_y
    Old Field: ContrastVol
  - New Field: ContrastVol_x
    Old Field: ContrastVol_AllProcs
  - New Field: ProcedureStartDateTime
    Old Field: ProcedureStartDate
  - New Field: TVTProcedureStopTime
    Old Field: TVTPRocedureExitTime
  - New Field: SixMinWalkPerf_y
    Old Field: SixMinWalkPerf
  - New Field: SDM_Proc
    Old Field: sdmproc
  - New Field: SDM_Tool
    Old Field: sdmtool
  - New Field: SDM_Tool_Name
    Old Field: sdmtoolname
value_mapping:
  - Field: ce_eventid
    Value Map:
      Annular Rupture: E007
      Aortic Dissection: E008
      ASD Defect Closure due to Transseptal Catheterization: E054
      Atrial Fibrillation: E006
      Bleeding - Access Site: E017
      Bleeding - Gastrointestinal: E020
      Bleeding - Genitourinary: E021
      Bleeding - Hematoma at Access Site: E018
      Bleeding - Other: E022
      Bleeding - Retroperitoneal: E019
      Cardiac Arrest: E005
      Cardiac Perforation: E009
      Cardiac Surgery or Intervention - Other Unplanned: E031
      Complete Leaflet Clip Detachment: E051
      Coronary Artery Compression: E002
      Delivery System Component Embolization: E058
      Device Embolization: E050
      Device Migration: E023
      Device Related Event - Other: E028
      Device Thrombosis: E027
      Dialysis (New Requirement): E029
      Endocarditis: E003
      ICD: E040
      Left Ventricular Outflow Tract Obstruction: 253546004
      Mitral Leaflet or Subvalvular Injury: 112000001886
      Myocardial Infarction: E059
      Pacemaker Lead Dislodgement or Dysfunction: 112000001884
      Percutaneous Coronary Intervention: E033
      Permanent Pacemaker: E039
      Pulmonary Embolism: 59282003
      Reintervention - Aortic Valve: E030
      Reintervention - Mitral Valve: E053
      Reintervention - Tricuspid Valve: 112000001820
      Single Leaflet Device Attachment: E049
      Stroke - Hemorrhagic: E012
      Stroke - Ischemic: E011
      Stroke - Undetermined: E013
      Transient Ischemic Attack (TIA): E010
      Transseptal Complication: E052
      Vascular Complication - Major: E041
      Vascular Complication - Minor: E042
      Vascular Surgery or Intervention - Unplanned: E032
      Readmission - Cardiac (Not Heart Failure): E056
      Readmission - Heart Failure: E055
      Readmission - Non-Cardiac: E057
      COVID-19 Positive: 112000001982
      Deep Vein Thrombosis: 128053003
  - Field: ce_eventname
    Value Map:
      COVID-19 Positive: COVID-19
  - Field: tvtlocation
    Value Map:
      Cardiac Catheterization Laboratory: Cath Lab
      Hybrid Catheterization Laboratory Suite: Hybrid Cath Lab Suite
      Hybrid Operating Room Suite: Hybrid OR Suite
  - Field: tvtprocedureindication
    Value Map:
      Aortic Regurgitation: Primary Aortic Regurgitation
      Aortic Stenosis: Primary Aortic Stenosis
  - Field: chrlungd
    Value Map:
      Mild Lung Disease: Mild
      Moderate Lung Disease: Moderate
      Severe Lung Disease: Severe
  - Field: cardiomyopathy
    Value Map:
      Ischemic cardiomyopathy: Yes - Ischemic
      Non-ischemic cardiomyopathy: Yes - Non-ischemic
  - Field: avdmorphology
    Value Map:
      Bicuspid: Bicuspid Aortic Valve
      Tricuspid: Tricuspid Valve
  - Field: residence
    Value Map:
      Home with No Health Aid: Home with No Health-Aid
  - Field: smokeamount
    Value Map:
      Heavy tobacco use (>= 10/day): Heavy tobacco use (>=10 day)
  - Field: miwhen
    Value Map:
      Prior Myocardial Infarction Less than 30 days: < 30 Days
      Prior Myocardial Infarction Greater than or Equal to 30 days: '>= 30 Days'
  - Field: cvdcarsten
    Value Map:
      Right Carotid Artery Stenosis: Right
      Left Carotid Artery Stenosis: Left
      Bilateral Carotid Artery Stenosis: Both
  - Field: cadpresentation
    Value Map:
      No Symptoms, No Angina: No Sxs, no angina
      Symptoms Unlikely to be Ischemic: Sx unlikely to be ischemic
  - Field: fivemwalktest
    Value Map:
      Test Not Performed: Not Performed
      Test Performed: 'Yes'
  - Field: KCCQ12_1a
    Value Map:
      1 - Extremely Limited: Extremely Limited
      2 - Quite a Bit Limited: Quite a Bit Limited
      3 - Moderately Limited: Moderately Limited
      4 - Slightly Limited: Slightly Limited
      5 - Not at All Limited: Not at All Limited
      6 - Limited for Other Reasons or Did Not Do These Activities: Limited for Other Reasons or Did Not Do These Activities

  - Field: KCCQ12_1b
    Value Map:
      1 - Extremely Limited: Extremely Limited
      2 - Quite a Bit Limited: Quite a Bit Limited
      3 - Moderately Limited: Moderately Limited
      4 - Slightly Limited: Slightly Limited
      5 - Not at All Limited: Not at All Limited
      6 - Limited for Other Reasons or Did Not Do These Activities: Limited for Other Reasons or Did Not Do These Activities
  - Field: KCCQ12_1c
    Value Map:
      1 - Extremely Limited: Extremely Limited
      2 - Quite a Bit Limited: Quite a Bit Limited
      3 - Moderately Limited: Moderately Limited
      4 - Slightly Limited: Slightly Limited
      5 - Not at All Limited: Not at All Limited
      6 - Limited for Other Reasons or Did Not Do These Activities: Limited for other reasons or did not do the activity
  - Field: KCCQ12_2
    Value Map:
      1 - Every Morning: Every Morning
      2 - Three or More Times Per Week But Not Everyday: 3 or More Times Per Week But Not Everyday
      3 - One to Two Times Per Week: 1-2 Times Per Week
      4 - Less Than Once a Week: Less Than Once a Week
      5 - Never Over the Past Two Weeks: Never Over the Past 2 Weeks
  - Field: KCCQ12_3
    Value Map:
      1 - All the Time: All the Time
      2 - Several Times Per Day: Several Times Per Day
      3 - At Least Once Per Day: At Least Once Per Day
      4 - Three or More Times Per Week But Not Everyday: 3 or More Times Per Week But Not Everyday
      5 - One to Two Times Per Week: 1-2 Times Per Week
      6 - Less Than Once a Week: Less Than Once a Week
      7 - Never Over the Past Two Weeks: Never Over the Past 2 Weeks
  - Field: KCCQ12_4
    Value Map:
      1 - All the Time: All the Time
      2 - Several Times Per Day: Several Times Per Day
      3 - At Least Once Per Day: At Least Once Per Day
      4 - Three or More Times Per Week But Not Everyday: 3 or More Times Per Week But Not Everyday
      5 - One to Two Times Per Week: 1-2 Times Per Week
      6 - Less Than Once a Week: Less Than Once a Week
      7 - Never Over the Past Two Weeks: Never Over the Past 2 Weeks
  - Field: KCCQ12_5
    Value Map:
      1 - Every Night: Every Night
      2 - Three or More Times Per Week But Not Everyday: 3 or More Times Per Week But Not Everyday
      3 - One to Two Times Per Week: 1-2 Times Per Week
      4 - Less Than Once a Week: Less Than Once a Week
      5 - Never Over the Past Two Weeks: Never Over the Past 2 Weeks
  - Field: KCCQ12_6
    Value Map:
      1 - It Has Extremely Limited My Enjoyment of Life: It Has Extremely Limited My Enjoyment of Life
      2 - It Has Limited My Enjoyment of Life Quite a Bit: It Has Limited My Enjoyment of Life Quite a Bit
      3 - It Has Moderately Limited My Enjoyment of Life: It Has Moderately Limited My Enjoyment of Life
      4 - It Has Slightly Limited My Enjoyment of Life: It Has Slightly Limited My Enjoyment of Life
      5 - It Has Not Limited My Enjoyment of Life at All: It Has Not Limited My Enjoyment of Life at All
  - Field: KCCQ12_7
    Value Map:
      1 - Not At All Satisfied: Not At All Satisfied
      2 - Mostly Dissatisfied: Mostly Dissatisfied
      3 - Somewhat Satisfied: Somewhat Satisfied
      4 - Mostly Satisfied: Mostly Satisfied
      5 - Completely Satisfied: Completely Satisfied
  - Field: KCCQ12_8a
    Value Map:
      1 - Severely Limited: Severely Limited
      2 - Limited Quite a Bit: Limited Quite a Bit
      3 - Moderately Limited: Moderately Limited
      4 - Slightly Limited: Slightly Limited
      5 - Did Not Limit at All: Did Not Limit at All
      6 - Does Not Apply or Did Not Do for Other Reasons: Does Not Apply or Did Not Do for Other Reasons
  - Field: KCCQ12_8b
    Value Map:
      1 - Severely Limited: Severely Limited
      2 - Limited Quite a Bit: Limited Quite a Bit
      3 - Moderately Limited: Moderately Limited
      4 - Slightly Limited: Slightly Limited
      5 - Did Not Limit at All: Did Not Limit at All
      6 - Does Not Apply or Did Not Do for Other Reasons: Does Not Apply or Did Not Do for Other Reasons
  - Field: KCCQ12_8c
    Value Map:
      1 - Severely Limited: Severely Limited
      2 - Limited Quite a Bit: Limited Quite a Bit
      3 - Moderately Limited: Moderately Limited
      4 - Slightly Limited: Slightly Limited
      5 - Did Not Limit at All: Did Not Limit at All
      6 - Does Not Apply or Did Not Do for Other Reasons: Does Not Apply or Did Not Do for Other Reasons
  - Field: numdisv
    Value Map:
      Two: "2"
      One: "1"
      Three: "3"
  - Field: avdannulussizemethod
    Value Map:
      Computed Tomography Angiography: CTA
      Transthoracic Echo (TTE): TTE
      Transesophageal Echocardiogram (TEE): TEE
  - Field: avdmorphology
    Value Map:
      Bicuspid Aortic Valve: Bicuspid
      Tricuspid Valve: Tricuspid
  - Field: status
    Value Map:
      Elective Procedure: Elective
      Urgent Procedure: Urgent
      Emergency Procedure: Emergency
      Salvage Procedure: Salvage
  - Field: mvdleafflail
    Value Map:
      Anterior Leaflet: Anterior
      Posterior Leaflet: Posterior
      Bileaflet: Bi-leaflet
  - Field: anesthesiatype
    Value Map:
      General Anesthesia: General anesthesia
      Moderate Sedation/Analgesia (Conscious Sedation): Moderate sedation
  - Field: LeafAccess
    Value Map:
      Right Femoral Vein: Right femoral vein
      Left Femoral Vein: Left femoral vein
      Jugular Vein: Jugular vein
      Other Vein: Other vein
  - Field: POPTTECH
    Value Map:
      Transesophageal Echocardiogram (TEE): Yes - TEE
      Transthoracic Echo (TTE): Yes - TTE
  - Field: OperatorReason
    Value Map:
      Extreme Risk: Inoperable/Extreme Risk
  - Field: TVTAccessSite
    Value Map:
      Axillary Artery: Axillary
      Carotid: Transcarotid
      Transaortic: Direct Aortic
      Femoral Artery: Femoral
      Transiliac: Iliac
      Subclavian Artery: Subclavian
      Transseptal via Femoral Vein: Transeptal
  - Field: MVSupportDevice
    Value Map:
      Intra-aortic balloon pump (IABP): IABP
  - Field: AVMorphology
    Value Map:
      Bicuspid Aortic Valve: Bicuspid
      Tricuspid Valve: Tricuspid
  - Field: DischargeLocation
    Value Map:
      Left Against Medical Advice (AMA): Left Against Medical Advice
  - Field: Post_EOAmethod
    Value Map:
      Proximal Isovelocity Surface Area: PISA
  - Field: VDMitEOA_MoA
    Value Map:
      Proximal Isovelocity Surface Area: PISA
  - Field: TVTAccessMethod
    Value Map:
      Percutaneous Approach: Percutaneous
      Mini sternotomy: Mini Sternotomy
      Mini thoracotomy: Mini Thoracotomy
  - Field: FMRType
    Value Map:
      Ischemic-Chronic: Ischemic Chronic
      Pure Annular Dilation (with Normal Left Ventricular Systolic Function): Pure Annular Dilation with Normal Left Ventricular Systolic Function
  - Field: MVDLeafPro
    Value Map:
      Anterior Leaflet: Anterior
      Posterior Leaflet: Posterior
      Bileaflet: Bi-leaflet
  - Field: PrevProcAVType
    Value Map:
      Stented Valve Replacement: Bioprosthetic stented
      Stentless Valve Replacement: Bioprosthetic stentless
  - Field: VDAOET
    Value Map:
      Rheumatic: Rheumatic fever
  - Field: mrr_loc
    Value Map:
      A1/P1: A1P1
      A2/P2: A2P2
      A3/P3: A3P3
  - Field: SixMinWalkReason
    Value Map:
      Non-Cardiac Reason: Non Cardiac Reason
  - Field: concomproctype
    Value Map:
      PCI: "Percutaneous Coronary Intervention (PCI)"
delimiters:
  - Enclose: true
    Field: tobacotype
    Replace: null
    Delimiter: '|'
transform_fields:
  - Field: Discharge_ACE_OR_ARB_any
    Transform Type: conditional_or
    Args:
      fields:
        - name: discharge_ace_inhibitor_any
          values: ['YES']
        - name: discharge_arb_any
          values: ['YES']
  - Field: AFibFlutter
    Transform Type: conditional_or
    Args:
      fields:
        - name: prioratrialfib
          values: ['YES']
        - name: aflutter
          values: ['YES']
  - Field: Discharge_Aspirin_Alone
    Transform Type: conditional_and
    Args:
      true_val: "Yes"
      false_val: null
      fields:
        - name: discharge_aspirin_any
          values: ['YES']
        - name: discharge_p2y12_any
          values: [ 'NO' ]
  - Field: Discharge_Aspirin_dual_antiplatelet_therapy
    Transform Type: conditional_and
    Args:
      true_val: "Yes"
      false_val: null
      fields:
        - name: discharge_aspirin_any
          values: [ 'YES' ]
        - name: discharge_p2y12_any
          values: [ 'YES' ]
  - Field: PriorMed_Aspirin_Alone
    Transform Type: conditional_and
    Args:
      true_val: "Yes - Prescribed"
      false_val: null
      fields:
        - name: priormed_aspirin
          values: [ 'YES' ]
        - name: priormed_p2y12antagonist
          values: [ 'No' ]
  - Field: PriorMed_Aspirin_Dual_Antiplatelet
    Transform Type: conditional_and
    Args:
      true_val: "Yes - Prescribed"
      false_val: null
      fields:
        - name: priormed_aspirin
          values: [ 'YES' ]
        - name: priormed_p2y12antagonist
          values: [ 'YES' ]
  - Field: mrrindaids
    Transform Type: conditional_and
    Args:
      true_val: "No"
      false_val: mrrindaids
      fields:
        - name: mrrindaids
          values: ['None', 'nan']
        - name: leafaccess
          values: ['Other Vein', 'Jugular Vein', 'Left Femoral Vein', 'Right Femoral Vein']
  - Field: mrrindaspir
    Transform Type: conditional_and
    Args:
      true_val: "No"
      false_val: mrrindaspir
      fields:
        - name: mrrindaspir
          values: ['None', 'nan']
        - name: leafaccess
          values: ['Other Vein', 'Jugular Vein', 'Left Femoral Vein', 'Right Femoral Vein']
  - Field: mrrindbleed
    Transform Type: conditional_and
    Args:
      true_val: "No"
      false_val: mrrindbleed
      fields:
        - name: mrrindbleed
          values: ['None', 'nan']
        - name: leafaccess
          values: ['Other Vein', 'Jugular Vein', 'Left Femoral Vein', 'Right Femoral Vein']
  - Field: mrrindchemo
    Transform Type: conditional_and
    Args:
      true_val: "No"
      false_val: mrrindchemo
      fields:
        - name: mrrindchemo
          values: ['None', 'nan']
        - name: leafaccess
          values: ['Other Vein', 'Jugular Vein', 'Left Femoral Vein', 'Right Femoral Vein']
  - Field: mrrinddem
    Transform Type: conditional_and
    Args:
      true_val: "No"
      false_val: mrrinddem
      fields:
        - name: mrrinddem
          values: ['None', 'nan']
        - name: leafaccess
          values: ['Other Vein', 'Jugular Vein', 'Left Femoral Vein', 'Right Femoral Vein']
  - Field: mrrindfrail
    Transform Type: conditional_and
    Args:
      true_val: "No"
      false_val: mrrindfrail
      fields:
        - name: mrrindfrail
          values: ['None', 'nan']
        - name: leafaccess
          values: ['Other Vein', 'Jugular Vein', 'Left Femoral Vein', 'Right Femoral Vein']
  - Field: mrrindhostile
    Transform Type: conditional_and
    Args:
      true_val: "No"
      false_val: mrrindhostile
      fields:
        - name: mrrindhostile
          values: ['None', 'nan']
        - name: leafaccess
          values: ['Other Vein', 'Jugular Vein', 'Left Femoral Vein', 'Right Femoral Vein']
  - Field: mrrindima
    Transform Type: conditional_and
    Args:
      true_val: "No"
      false_val: mrrindima
      fields:
        - name: mrrindima
          values: ['None', 'nan']
        - name: leafaccess
          values: ['Other Vein', 'Jugular Vein', 'Left Femoral Vein', 'Right Femoral Vein']
  - Field: mrrindimmob
    Transform Type: conditional_and
    Args:
      true_val: "No"
      false_val: mrrindimmob
      fields:
        - name: mrrindimmob
          values: ['None', 'nan']
        - name: leafaccess
          values: ['Other Vein', 'Jugular Vein', 'Left Femoral Vein', 'Right Femoral Vein']
  - Field: mrrindliver
    Transform Type: conditional_and
    Args:
      true_val: "No"
      false_val: mrrindliver
      fields:
        - name: mrrindliver
          values: ['None', 'nan']
        - name: leafaccess
          values: ['Other Vein', 'Jugular Vein', 'Left Femoral Vein', 'Right Femoral Vein']
  - Field: mrrindopm6
    Transform Type: conditional_and
    Args:
      true_val: "No"
      false_val: mrrindopm6
      fields:
        - name: mrrindopm6
          values: ['None', 'nan']
        - name: leafaccess
          values: ['Other Vein', 'Jugular Vein', 'Left Femoral Vein', 'Right Femoral Vein']
  - Field: mrrindopm8
    Transform Type: conditional_and
    Args:
      true_val: "No"
      false_val: mrrindopm8
      fields:
        - name: mrrindopm8
          values: [ 'None', 'nan' ]
        - name: leafaccess
          values: [ 'Other Vein', 'Jugular Vein', 'Left Femoral Vein', 'Right Femoral Vein' ]
  - Field: mrrindother
    Transform Type: conditional_and
    Args:
      true_val: "No"
      false_val: mrrindother
      fields:
        - name: mrrindother
          values: [ 'None', 'nan' ]
        - name: leafaccess
          values: [ 'Other Vein', 'Jugular Vein', 'Left Femoral Vein', 'Right Femoral Vein' ]
  - Field: mrrindpa
    Transform Type: conditional_and
    Args:
      true_val: "No"
      false_val: mrrindpa
      fields:
        - name: mrrindpa
          values: [ 'None', 'nan' ]
        - name: leafaccess
          values: [ 'Other Vein', 'Jugular Vein', 'Left Femoral Vein', 'Right Femoral Vein' ]
  - Field: mrrindrvdysfx
    Transform Type: conditional_and
    Args:
      true_val: "No"
      false_val: mrrindrvdysfx
      fields:
        - name: mrrindrvdysfx
          values: [ 'None', 'nan' ]
        - name: leafaccess
          values: [ 'Other Vein', 'Jugular Vein', 'Left Femoral Vein', 'Right Femoral Vein' ]
  - Field: mrrindoptimalmedicaltherapy
    Transform Type: conditional_and
    Args:
      true_val: "No"
      false_val: mrrindoptimalmedicaltherapy
      fields:
        - name: mrrindoptimalmedicaltherapy
          values: [ 'None', 'nan' ]
        - name: leafaccess
          values: [ 'Other Vein', 'Jugular Vein', 'Left Femoral Vein', 'Right Femoral Vein' ]
  - Field: mrrindseverepulmonaryhypertension
    Transform Type: conditional_and
    Args:
      true_val: "No"
      false_val: mrrindseverepulmonaryhypertension
      fields:
        - name: mrrindseverepulmonaryhypertension
          values: [ 'None', 'nan' ]
        - name: leafaccess
          values: [ 'Other Vein', 'Jugular Vein', 'Left Femoral Vein', 'Right Femoral Vein' ]
  - Field: sixminwalkperf
    Transform Type: conditional_and
    Args:
      true_val: "Performed"
      false_val: sixminwalkperf
      fields:
        - name: sixminwalkperf_x
          values: ['Yes']
  - Field: procedureabortreason
    Transform Type: conditional_or
    Args:
      true_val: null
      false_val: procedureabortreason
      fields:
        - name: procleafclip
          values: ['YES']
        - name: procttvp
          values: ['YES']
        - name: proctmvr
          values: ['YES']
  - Field: procedureabortaction
    Transform Type: conditional_or
    Args:
      true_val: null
      false_val: procedureabortaction
      fields:
        - name: procleafclip
          values: ['YES']
        - name: procttvp
          values: ['YES']
        - name: proctmvr
          values: ['YES']
  - Field: convsurgaccess
    Transform Type: conditional_or
    Args:
      true_val: null
      false_val: convsurgaccess
      fields:
        - name: procleafclip
          values: ['YES']
        - name: procttvp
          values: ['YES']
        - name: proctmvr
          values: ['YES']
  - Field: mvsupport
    Transform Type: conditional_or
    Args:
      true_val: null
      false_val: mvsupport
      fields:
        - name: procleafclip
          values: ['YES']
        - name: procttvp
          values: ['YES']
        - name: proctmvr
          values: ['YES']

  - Field: procedureabort
    Transform Type: conditional_or
    Args:
      true_val: null
      false_val: procedureabort
      fields:
        - name: procleafclip
          values: ['YES']
        - name: procttvp
          values: ['YES']
        - name: proctmvr
          values: ['YES']

  - Field: convsurgaccessreason
    Transform Type: conditional_or
    Args:
      true_val: null
      false_val: convsurgaccessreason
      fields:
        - name: procleafclip
          values: ['YES']
        - name: procttvp
          values: ['YES']
        - name: proctmvr
          values: ['YES']

  - Field: mvsupportdevice
    Transform Type: conditional_or
    Args:
      true_val: null
      false_val: mvsupportdevice
      fields:
        - name: procleafclip
          values: ['YES']
        - name: procttvp
          values: ['YES']
        - name: proctmvr
          values: ['YES']

  - Field: mvsupporttiming
    Transform Type: conditional_or
    Args:
      true_val: null
      false_val: mvsupporttiming
      fields:
        - name: procleafclip
          values: ['YES']
        - name: procttvp
          values: ['YES']
        - name: proctmvr
          values: ['YES']

  - Field: operatorreason
    Transform Type: conditional_or
    Args:
      true_val: null
      false_val: operatorreason
      fields:
        - name: procttvp
          values: ['YES']
        - name: proctmvr
          values: ['YES']

  - Field: tvtprocedureentrytime
    Transform Type: conditional_or
    Args:
      true_val: null
      false_val: tvtprocedureentrytime
      fields:
        - name: procleafclip
          values: ['YES']
  - Field: numdisv
    Transform Type: replace
    Args:
      old_values: '.0'
      new_values: ''
  - Field: hic
    Transform Type: additional_field
    Args:
      Source: mbi
  - Field: Post_ValvMR
    Transform Type: additional_field
    Args:
      Source: popttmr
  - Field: VDInsufMV
    Transform Type: additional_field
    Args:
     Source: vdinsufm
  - Field: supporttype
    Transform Type: additional_field
    Args:
      Source: mvsupportdevice
  - Field: originalotherid
    Transform Type: additional_field
    Args:
      Source: otherid
  - Field: ssn
    Transform Type: additional_field
    Args:
      Source: null
      custom string: 1
    Computation: false
