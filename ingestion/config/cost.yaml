version: '1.0'
biome_schema:
  - Dtype: bigint unsigned
    Field: biomeencounterid
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: biomeimportdt
    PHI: false
    Role: DATE
  - Dtype: varchar(150)
    Field: chargecodename
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: charges
    PHI: false
    Role: null
  - Dtype: int
    Field: clientfileid
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: cpt4hcpcscode
    PHI: false
    Role: null
  - Dtype: varchar(150)
    Field: cpt4hcpcsname
    PHI: false
    Role: null
  - Dtype: varchar(18)
    Field: datasetname
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: dayofstay
    PHI: false
    Role: null
  - Dtype: varchar(80)
    Field: departmentname
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: directcost
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: directcostperunit
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: encounternumber
    PHI: true
    Role: IDENTIFIER
  - Dtype: varchar(106)
    Field: filename
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: indirectcost
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: medrecn
    PHI: true
    Role: MEDRECNUM
  - Dtype: varchar(30)
    Field: nationaldrugclasscode
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: originalmrn
    PHI: true
    Role: MEDRECNUM
  - Dtype: varchar(100)
    Field: servicesitename
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: svcitemchargecode
    PHI: false
    Role: null
  - Dtype: varchar(40)
    Field: svcitemdate
    PHI: true
    Role: DATE
  - Dtype: varchar(34)
    Field: tenantid
    PHI: false
    Role: TENANT
  - Dtype: varchar(30)
    Field: totalcost
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: ub92revcode
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: ub92revname
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: units
    PHI: false
    Role: null
  - Dtype: varchar(22)
    Field: version
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: chargeunit
    PHI: false
    Role: null
cleaner_regex:
  - Field: encounternumber
    Regex: '\.0*$'
  - Field: charges
    Regex: '[\s\$\n,]*'
  - Field: dayofstay
    Regex: '\.0|[a-zA-Z\s]'
  - Field: directcost
    Regex: '[\s\$\n,"]*'
  - Field: directcostperunit
    Regex: '[\s\$\n,"]*'
  - Field: indirectcost
    Regex: '[\s\$\n,"]*'
  - Field: nationaldrugclasscode
    Regex: '\.0+'
  - Field: svcitemchargecode
    Regex: '\s(.*)'
  - Field: totalcost
    Regex: '[\s\$\n,"]*'
  - Field: ub92revcode
    Regex: '\.0*$'
  - Field: units
    Regex: '[\s\$\n,]*'
transform_fields:
  - Field: cpt4hcpcscode
    Transform Type: split_join
    Args:
      source_field: cpt4hcpcscode
      split_char: "-"
      extract_part: prefix
# Scientific notation conversions
  - Field: directcost
    Transform Type: scientific_to_decimal
    Args:
      default: "0"
      format_str: ".20f"
  - Field: indirectcost
    Transform Type: scientific_to_decimal
    Args:
      default: "0"
      format_str: ".20f"
  - Field: totalcost
    Transform Type: scientific_to_decimal
    Args:
      default: "0"
      format_str: ".20f"
  # Cost calculations (must come after individual field conversions)
  - Field: totalcost
    Transform Type: add
    Computation: true
    Args:
      fields: [directcost, indirectcost]
      format_str: ".6f"
  - Field: directcost
    Transform Type: subtract
    Computation: true
    Args:
      fields: [totalcost, indirectcost]
      format_str: ".6f"
  - Field: indirectcost
    Transform Type: subtract
    Computation: true
    Args:
      fields: [totalcost, directcost]
      format_str: ".6f"

  - Field: svcitemchargecode
    Transform Type: additional_field
    Args:
      Source: chargecodename
    Computation: true