#translator-config-version: '20240101'
version: '1.0'
biome_schema:
  - Dtype: varchar(60)
    Field: dischargestatus
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: careentityid
    PHI: false
    Role: CAREENTITY
  - Dtype: varchar(30)
    Field: los
    PHI: false
    Role: null
  - Dtype: varchar(24)
    Field: datasetname
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: prindx9code
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: tenantid
    PHI: false
    Role: TENANT
  - Dtype: decimal(18,6)
    Field: indirectcost
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: gender
    PHI: true
    Role: GENDER
  - Dtype: varchar(30)
    Field: originalmrn
    PHI: true
    Role: MEDRECNUM
  - Dtype: varchar(30)
    Field: medrecn
    PHI: true
    Role: MEDRECNUM
  - Dtype: varchar(100)
    Field: secondarypayor
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: prindx10code
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: admitdx9code
    PHI: false
    Role: null
  - Dtype: bigint unsigned
    Field: biomeencounterid
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: dob
    PHI: true
    Role: DOB
  - Dtype: varchar(30)
    Field: inoutcode
    PHI: false
    Role: INOUTCODE
  - Dtype: varchar(50)
    Field: admittype
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: primaryinsurancegroup
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: dctime
    PHI: false
    Role: TIME
  - Dtype: varchar(30)
    Field: admitdate
    PHI: true
    Role: ADMISSION_DATE
  - Dtype: varchar(30)
    Field: icudays
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: directcost
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: age
    PHI: true
    Role: AGE
  - Dtype: varchar(30)
    Field: encounternumber
    PHI: true
    Role: IDENTIFIER
  - Dtype: varchar(9)
    Field: patzip
    PHI: true
    Role: PATIENT_ZIP
  - Dtype: varchar(30)
    Field: patienttype
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: race
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: admittime
    PHI: false
    Role: TIME
  - Dtype: varchar(30)
    Field: dischargedate
    PHI: true
    Role: ANCHOR_DATE
  - Dtype: varchar(10)
    Field: drgcode
    PHI: false
    Role: null
  - Dtype: decimal(18,6)
    Field: totalcost
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: admitdx10code
    PHI: false
    Role: null
  - Dtype: varchar(91)
    Field: filename
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: expectedpayment
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: prinicd10proccode
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: netpatientrevenue
    PHI: false
    Role: null
  - Dtype: varchar(22)
    Field: hospname
    PHI: false
    Role: HOSPITAL
  - Dtype: varchar(22)
    Field: version
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: admitsource
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: primarypayor
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: prinicd9proccode
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: referringmdrole
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: financialpayorclass
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: servicesitename
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: ethnicity
    PHI: false
    Role: null
  - Dtype: varchar(64)
    Field: attendingmdname
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: refferingphyname
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: attendingmdnpi
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: servicelinerollup
    PHI: false
    Role: null
  - Dtype: varchar(17)
    Field: clientfileid
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: biomeimportdt
    PHI: false
    Role: DATE
  - Dtype: varchar(25) 
    Field: distressscore
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: distressquintile
    PHI: false
    Role: null
  - Dtype: int
    Field: yearmonth
    PHI: false
    Role: ANCHOR_YEAR_MONTH
  - Dtype: decimal(8,2)
    Field: distfromhospital
    PHI: false
    Role: DISTANCE_FROM_HOSPITAL
  - Dtype: decimal(12,3)
    Field: charges
    PHI: false
    Role: null
value_mapping:
  - Field: admittype
    Value Map:
      Delivery C Sec/IOL Scheduled: Delivery
      Delivery C Sec/IOL Unsched: Delivery
      ELECTIVE: Elective
      NEWBORN: Newborn
      Routine/Elective: Elective
      Transplant/Donor: Transplant
      Transplant/Donor (All < 24hrs): Transplant
      TRAUMA: Trauma
      TRAUMA CENTER: Trauma
      Unknown: Not Specified
      UNSCHEDULED ADMIT EMERGENT: Emergent
      URGENT: Urgent
      Involuntary Emergent NPH: Emergency
      Involuntary Urgent NPH: Urgent
      Obstetrics - Elective: Elective
      Voluntary Elective NPH: Elective
      Info Not Available: Information Not Available
      Unscheduled Admit Emergent: Emergent
      Trauma Center--Elective: Trauma--Elective
      Trauma Center--Emergency: Trauma--Emergency
      Emergency--Trauma Center--Elective: Emergency--Trauma--Elective
      Trauma Center: Trauma
      Newborn--Trauma Center: Trauma--Newborn
      Urgent--Trauma Center--Elective: Urgent--Trauma--Elective
      Trauma Center--Information Not Available: Trauma--Information Not Available
      Urgent--Trauma Center: Urgent--Trauma
      Trauma Center--Elective--Information Not Available: Trauma--Elective--Information Not Available
      Newborn--Trauma Center--Elective: Trauma--Newborn--Elective

  - Field: dischargestatus
    Value Map:
      STEMI - Unstable (>12 hrs from Sx): STEMI - Unstable (> 12 hrs from Sx)
  - Field: ethnicity
    Value Map:
      other Asian/Mideastern: Other Asian/Mideastern
      Unreported (Patient refused or chose not to disclose): Unknown (Patient cannot or refuses to declare ethnicity)
      Other Hispanic Latino(a) or Spanish origin: Hispanic
      Not Specified: Unknown
      Not Hispanic Latino-a or Spanish origin: Not Hispanic or Latino
  - Field: financialpayorclass
    Value Map:
      COMMERCIAL/HMO/PPO/UCSD: Commercial
      MEDI-CAL & MEDI-CAL MANAGED CARE: Medical
      MEDICARE: Medicare
      SELF PAY & CHARITY: Self Pay
      Blue Cross Blue Shield: Commercial
      Medicaid Managed Care: Medicaid
      Medicaid Out of State: Medicaid
      Medicare Replacement: Medicare
      Self-Pay: Self Pay
      Tricare: Commercial
      Worker's Comp: Other
  - Field: gender
    Value Map:
      '1': Male
      '2': Female
      F: Female
      M: Male
      U: Unknown
      MALE: Male
      FEMALE: Female
      UNKNOWN: Unknown
      N: Unknown
  - Field: inoutcode
    Value Map:
      I: Inpatient
      O: Outpatient
      CCU: Inpatient
      SNF: Unknown
      MICU: Inpatient
      SICU: Inpatient
      OP-ER: Outpatient
      Other: Unknown
      Trauma: Unknown
      Private: Inpatient
      '[Blank]': Unknown
      Oncology: Unknown
      EMERGENCY: Unknown
      INPATIENT: Inpatient
      Inpatient: Inpatient
      Isolation: Unknown
      Burn Acute: Unknown
      IP REGULAR: Inpatient
      OUTPATIENT: Outpatient
      Outpatient: Outpatient
      Acute Rehab: Unknown
      GIP Hospice: Unknown
      IP-DECEASED: Inpatient
      Medical ICU: Inpatient
      Observation: Unknown
      Psych Adult: Unknown
      EMERGENCY-IP: Inpatient
      EMERGENCY-OP: Outpatient
      OP-RECURRING: Outpatient
      REHAB IN BED: Unknown
      Semi-Private: Inpatient
      Surgical ICU: Inpatient
      Trauma Acute: Unknown
      IP-DISCHARGED: Inpatient
      OP-NONPATIENT: Outpatient
      OP-OUTPATIENT: Outpatient
      SURGERY ADMIT: Inpatient
      TRANSPLANT-IP: Inpatient
      Cardiology ICU: Inpatient
      Emergency Room: Unknown
      Monitored Care: Unknown
      EXP ORGAN DONOR: Inpatient
      REHAB INPATIENT: Inpatient
      IP MENTAL HEALTH: Inpatient
      Same Day Surgery: Unknown
      Medical -Surgical: Inpatient
      Ambulatory Surgery: Unknown
      IP SKILLED NURSING: Inpatient
      OBS IN BED/CHARGED: Unknown
      OUT PATIENT IN BED: Outpatient
      OUTPATIENT SURGERY: Outpatient
      Inpatient Admission: Inpatient
      OP SURGERY (NO BED): Outpatient
      Step-Down-Monitored: Inpatient
      Higher Nursing Ratio: Inpatient
      Recurring Outpatient: Outpatient
      EXT RECOV/OBS IN BED/NC: Unknown
      Burn Inpatient Admission: Inpatient
      OBS NOT IN BED/NOT CHARG: Unknown
      Trauma Inpatient Admission: Inpatient
      Inpatients: Inpatient
      Outpatients: Outpatient
      inp: Inpatient
      out: Outpatient
      ER: Outpatient
      Emergency Department - Encount: ED
      Hospital Outpatient Lab: Outpatient
      Hospital Outpatient Surgery: Outpatient
      Lab Referred Specimen: Outpatient
      Outpatient Burn Clinic Visit -: Outpatient
      Outpatient Burn Therapy - Seri: Outpatient
      Outpatient Cardiac Rehab - Ser: Outpatient
      Outpatient Clinic Visit - Enco: Outpatient
      Outpatient Infusion - Series: Outpatient
      Outpatient Labor and Delivery: Outpatient
      Outpatient Occupational Therap: Outpatient
      Outpatient Other - Encounter: Outpatient
      'Outpatient Radiation Oncology': Outpatient
      Outpatient Radiology - Encount: Outpatient
      Outpatient Speech Therapy - Se: Outpatient
      Unknown: Unknown
      'Outpatient Nuclear Medicine - ': Outpatient
      'Outpatient Physical Therapy - ': Outpatient
      Emergency Department - Encounter: ED
      Outpatient Burn Therapy - Series: Outpatient
      Outpatient Cardiac Rehab - Series: Outpatient
      Outpatient Clinic Visit - Encounter: Outpatient
      Outpatient Nuclear Medicine - Series: Outpatient
      Outpatient Occupational Therapy - Series: Outpatient
      Outpatient Physical Therapy - Series: Outpatient
      Outpatient Radiation Oncology - Series: Outpatient
      Outpatient Radiology - Encounter: Outpatient
      INPATIENTS: Inpatient
      OUTPATIENTS: Outpatient
      OUT: Outpatient
      INP: Inpatient
  - Field: patienttype
    Value Map:
      I: Inpatient
      O: OutPatient
  - Field: primarypayor
    Value Map:
      Medicare - FFS: Medicare
      Medicare - Risk: Medicare
      County: Medicare
      Commercial - FFS: Commercial
      Exchange - FFS: Commercial
      Commercial - Risk: Commercial
      Medi-Cal - FFS: MediCal
      Medi-Cal - Risk: MediCal
      Other (excludes County): Other
      ALAMEDA ALLIANCE COVERED CALIFORNIA MANAGED MEDI-C: MediCal
      ALAMEDA ALLIANCE MANAGED MEDI-CAL: MediCal
      COMMERCIAL: Commercial
      COVERED CALIFORNIA MEDI-CAL: MediCal
      MEDI-CAL MANAGED CARE: MediCal
      MEDI-CAL STANDARD: MediCal
      MEDICAID/MIA/CMSP: Medicaid
      MEDICARE: Medicare
      MEDICARE ADVANTAGE: Medicare
      MEDICARE ADVANTAGE HMO/SENIOR: Medicare
      OTHER GOVERNMENT: Other
      PARTNERSHIP COVERED CALIFORNIA MANAGED MEDI-CAL: MediCal
      PARTNERSHIP MANAGED MEDI-CAL: MediCal
      SELF-PAY: Self Pay
      AETNA: Commercial
      BLUE CROSS: Commercial
      BLUE SHIELD: Commercial
      CAPITATION: Commercial
      CAPITATION SENIOR: Commercial
      CCS/GHPP: Commercial
      CIGNA: Commercial
      COVERED CALIFORNIA: Commercial
      HEALTHNET: Commercial
      KAISER: Commercial
      UNITED HEALTH CARE: Commercial
      WORKERS COMP: Commercial
      INSTITUTIONAL: Commercial
      RESEARCH: Commercial
      Subcap: Commercial
      Covered CA: Other
      Capitated Contracts: Commercial
      Domestic: Commercial
      Public Aid: Medicaid
      Uninsured: Self Pay
      Auto: Other
      HMO SENIOR: Commercial
      INDEMNITY: Other
      PPO: Commercial
      TRICARE: Other
      AETNA HMO/PPO: Commercial
      BC ALLIANCE: Commercial
      BJC EMPLOYEE HEALTH PLANS: Commercial
      CIGNA HMO/PPO: Commercial
      GLOBAL/TRANSPLANT: Commercial
      HEALTHCARE EXCHANGE: Commercial
      HEALTHLINK HMO/PPO: Commercial
      ORGANIZATIONAL BILLING: Other
      SECURE HORIZONS: Commercial
      UHC HMO/PPO: Commercial
      WORKERS COMPENSATION: Other
  - Field: race
    Value Map:
      Asian: Asian
      Black: Black or African American
      OTHER: Other
      White: White
      KOREAN: Asian
      SAMOAN: Hawaiian or Pacific Islander
      CHINESE: Asian
      LAOTIAN: Asian
      Unknown: Unknown
      Declined: Unknown
      FILIPINO: Asian
      HAWAIIAN: Hawaiian or Pacific Islander
      JAPANESE: Asian
      CAMBODIAN: Asian
      CAUCASIAN: White
      GUAMANIAN: Hawaiian or Pacific Islander
      PAKISTANI: Asian/Indian
      Other Race: Other
      VIETNAMESE: Asian
      Asian Other: Asian
      'Asian: Thai': Asian
      ASIAN INDIAN: Asian/Indian
      Asian Indian: Asian/Indian
      'Asian: Other': Asian
      Alaska Native: American Indian or Alaska Native
      'Asian: Korean': Asian
      LAOTIAN/HMONG: Asian
      NOT AVAILABLE: Unknown
      'Asian: Chinese': Asian
      MIDDLE EASTERN: Asian/ME
      OTHER HISPANIC: Hawaiian or Pacific Islander
      OTHER SE ASIAN: Asian
      American Indian: American Indian or Alaska Native
      'Asian: Filipino': Asian
      'Asian: Japanese': Asian
      Pacific-Hawaiin: Hawaiian or Pacific Islander
      Pacific/Hawaiin: Hawaiian or Pacific Islander
      Patient Refused: Unknown
      'Asian: Pakistani': Asian/Indian
      'Asian: Taiwanese': Asian
      DECLINE TO STATE: Unknown
      MEXICAN AMERICAN: American Indian or Alaska Native
      PACIFIC ISLANDER: Hawaiian or Pacific Islander
      Unknown/Declined: Unknown
      'Asian: Indonesian': Asian
      'Asian: Vietnamese': Asian
      White or Caucasian: White
      'Asian: Asian Indian': Asian/Indian
      Declined to Specify: Unknown
      OTHER ASIAN/MIDEAST: Asian/ME
      Guamanian or Chamorro: Hawaiian or Pacific Islander
      BLACK/AFRICAN AMERICAN: Black or African American
      Pacific Islander Other: Hawaiian or Pacific Islander
      UNAVAILABLE OR UNKNOWN: Unknown
      AMERICAN INDIAN/ALASKAN: American Indian or Alaska Native
      'Black: African American': Black or African American
      'Pacific Islander: Other': Hawaiian or Pacific Islander
      OTHER RACE OR MIXED RACE: Other
      'Pacific Islander: Samoan': Hawaiian or Pacific Islander
      AFRICAN AMERICAN OR BLACK: Black or African American
      Black or African American: Black or African American
      PACIFIC ISLANDER/HAWAIIAN: Hawaiian or Pacific Islander
      AMERICAN INDIAN OR ALASKAN: American Indian or Alaska Native
      Native Hawaiian or Other Pacif: Hawaiian or Pacific Islander
      Unknown (Patient cannot or ref: Unknown
      AMERICAN INDIAN OR ALASKA NATIVE: American Indian or Alaska Native
      American Indian or Alaska Native: American Indian or Alaska Native
      'Pacific Islander: Native Hawaiian': Hawaiian or Pacific Islander
      'Pacific Islander: Guamanian or Chamorro': Hawaiian or Pacific Islander
      NATIVE HAWAIIAN OR OTHER PACIFIC ISLANDER: Hawaiian or Pacific Islander
      UNKNOWN (PATIENT CANNOT OR REFUSES TO DECLARE RACE: Unknown
      CHINESE (INACTIVE): Asian
      FILIPINO (INACTIVE): Asian
      IRANIAN (INACTIVE): Other
      JAPANESE (INACTIVE): Asian
      LAOTIAN/HMONG (INACTIVE): Asian
      OTHER HISPANIC (INACTIVE): Other Hispanic
      PAKISTANI (INACTIVE): Asian/ME
      UNABLE TO RESPOND: Unknown(Patient cannot or refuses to declare race)
      UNKNOWN (INACTIVE): Unknown
      VIETNAMESE (INACTIVE): Asian
cleaner_regex:
  - Field: admitdx10code
    Regex: '\s.*'
  - Field: admitdx9code
    Regex: '\s.*'
  - Field: admittime
    Regex: '(\d{0,2}\/){2}\d{0,4}\s'
  - Field: dctime
    Regex: '(\d{0,2}\/){2}\d{0,4}\s'
  - Field: directcost
    Regex: '[\s\$\n,"]*'
  - Field: drgcode
    Regex: '\.0*|[A-Za-z\s]|\s*\-.*'
  - Field: encounternumber
    Regex: '\.0*$'
  - Field: expectedpayment
    Regex: '[\s\$\n,]*'
  - Field: indirectcost
    Regex: '[\s\$\n,"]*'
  - Field: los
    Regex: ','
  - Field: netpatientrevenue
    Regex: '[\s\$\n,"]*'
  - Field: patzip
    Regex: '((?<=\d{5}).*|\-)|[a-z="]+'
  - Field: prindx10code
    Regex: '\s.*'
  - Field: prindx9code
    Regex: '\s.*'
  - Field: prinicd10proccode
    Regex: \s.*'
  - Field: prinicd9proccode
    Regex: \s.*'
  - Field: totalcost
    Regex: '[\s\$\n,"]*'
transform_fields:
  - Field: admittype
    Transform Type: fallback_fields
    Computation: true
    Args:
     fallback_fields: [ admittypecode ]
  - Field: inoutcode
    Transform Type: fallback_fields
    Computation: true
    Args:
     fallback_fields: [ patienttype, patienttypecode ]
     default: "Outpatient"
  - Field: patienttype
    Transform Type: fallback_fields
    Computation: true
    Args:
     fallback_fields: [ inoutcode, patienttypecode ]
     default: "Outpatient"
  - Field: FinancialPayorClass
    Transform Type: split_join
    Computation: true
    Args:
      source_field: financialpayorclasscode
      split_char: "-"
      extract_part: suffix
  #  transformations
  - Field: financialpayorclasscode
    Transform Type: split_join
    Args:
      source_field: financialpayorclasscode
      split_char: "-"
      extract_part: prefix
  - Field: gender
    Transform Type: simple_clean
    Args:
      empty_values: [ "" ]
# Scientific notation conversions
  - Field: directcost
    Transform Type: scientific_to_decimal
    Args:
      default: "0"
      format_str: ".20f"
  - Field: indirectcost
    Transform Type: scientific_to_decimal
    Args:
      default: "0"
      format_str: ".20f"
  - Field: totalcost
    Transform Type: scientific_to_decimal
    Args:
      default: "0"
      format_str: ".20f"
  # Cost calculations (must come after individual field conversions)
  - Field: totalcost
    Transform Type: add
    Computation: true
    Args:
      fields: [directcost, indirectcost]
      format_str: ".20f"
  - Field: directcost
    Transform Type: subtract
    Computation: true
    Args:
      fields: [totalcost, indirectcost]
      format_str: ".20f"
  - Field: indirectcost
    Transform Type: subtract
    Computation: true
    Args:
      fields: [totalcost, directcost]
      format_str: ".20f"
  - Field: originalmrn
    Transform Type: additional_field
    Args:
      Source: medrecn
    Computation: true
