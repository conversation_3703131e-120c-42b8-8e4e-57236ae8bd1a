rename_fields:
  - New Field: procedure practitioner upin
    Old Field: proceduremdid
  - New Field: patient encounter - medical record number source
    Old Field: medrecn
  - New Field: procedure practitioner name
    Old Field: proceduremdname
  - New Field: icd10 px sequence number - sequence number
    Old Field: procedureseqno
  - New Field: icd10 px - icd10 px
    Old Field: procedurecode
  - New Field: icd10 px service date - date
    Old Field: proceduredate
  - New Field: entity name
    Old Field: servicesitename
  - New Field: patient encounter - encounter record number
    Old Field: encounternumber
required_fields:
  - procedurecode
  - encounternumber
  - proceduredate
  - procedureseqno