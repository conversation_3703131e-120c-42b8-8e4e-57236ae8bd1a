client_fields:
  - discharge_time
  - icu_days
  - discharge_date
  - admit_type_code
  - admit_type_desc
  - admit_source_code
  - admit_source_ desc
  - ed_arrival_time
  - ed_arrival_date
  - ed_discharge_order_time
  - md_referring_specialty
  - md_attending_id
  - admit_time
  - discharge_status_desc
  - discharge_status_code
  - md_referring_name
  - discharge_unit_desc
  - discharge_unit
  - payor_category_desc
  - md_referring_id
  - admit_date
  - los
  - service_site_ desc
  - mrn
  - encounter_number
  - dob
  - gender
  - patient_zip
  - patient_type_desc
  - service_site_code
  - md_attending_name
  - md_attending_specialty
  - md_prin_proc_id
  - md_prin_proc_name
  - md_prin_proc_specialty
  - md_ed_id
  - md_ed_name
  - md_ed_specialty
  - discharge_ms_drg
  - admit_diagnosis_icd
  - principal_diagnosis_icd
  - principal_diagnosis_poa
  - principal_procedure_code
  - total_direct_cost
  - total_indirect_cost
  - total_variable_cost
  - total_fixed_cost
  - total_direct_ fixed_cost
  - total_direct_variable_cost
  - total_indirect_fixed_cost
  - gross_patient_revenue
  - expected_payment
  - actual_payment
  - net_income
  - contribution_margin
  - ed_affival_time
rename_fields:
  - New Field: discharge_time
    Old Field: dctime
  - New Field: icu_days
    Old Field: icudays
  - New Field: discharge_date
    Old Field: dischargedate
  - New Field: admit_type_code
    Old Field: admittypecode
  - New Field: admit_type_desc
    Old Field: admittype
  - New Field: admit_source_code
    Old Field: admitsourcecode
  - New Field: admit_source_ desc
    Old Field: admitsource
  - New Field: ed_discharge_order_time
    Old Field: ed_discharge_order_time4250
  - New Field: md_referring_specialty
    Old Field: referringmdrole
  - New Field: md_attending_id
    Old Field: attendingmdid
  - New Field: admit_time
    Old Field: admittime
  - New Field: discharge_status_desc
    Old Field: dischargestatus
  - New Field: discharge_status_code
    Old Field: dischargestatuscode
  - New Field: md_referring_name
    Old Field: refferingphyname
  - New Field: discharge_unit_desc
    Old Field: dischargeunit
  - New Field: discharge_unit
    Old Field: dischargeunitcode
  - New Field: payor_category_desc
    Old Field: primarypayor
  - New Field: md_referring_id
    Old Field: referringmdid
  - New Field: admit_date
    Old Field: admitdate
  - New Field: service_site_ desc
    Old Field: servicesitename
  - New Field: mrn
    Old Field: medrecn
  - New Field: encounter_number
    Old Field: encounternumber
  - New Field: patient_zip
    Old Field: patzip
  - New Field: patient_type_desc
    Old Field: patienttype
  - New Field: service_site_code
    Old Field: servicesitecode
  - New Field: md_attending_name
    Old Field: attendingmdname
  - New Field: md_attending_specialty
    Old Field: attendingmdrole
  - New Field: md_prin_proc_id
    Old Field: md_prin_proc_id4254
  - New Field: md_prin_proc_name
    Old Field: md_prin_proc_name4221
  - New Field: md_prin_proc_specialty
    Old Field: md_prin_proc_specialty4222
  - New Field: md_ed_id
    Old Field: md_ed_id4255
  - New Field: md_ed_name
    Old Field: md_ed_name10396
  - New Field: md_ed_specialty
    Old Field: md_ed_specialty4224
  - New Field: discharge_ms_drg
    Old Field: drgcode
  - New Field: admit_diagnosis_icd
    Old Field: admitdx9code
  - New Field: principal_diagnosis_icd
    Old Field: prindx9code
  - New Field: principal_diagnosis_poa
    Old Field: principlediagnosisicd9poa
  - New Field: principal_procedure_code
    Old Field: prinicd9proccode
  - New Field: total_direct_cost
    Old Field: directcost
  - New Field: total_indirect_cost
    Old Field: indirectcost
  - New Field: total_variable_cost
    Old Field: total_variable_cost4236
  - New Field: total_fixed_cost
    Old Field: total_fixed_cost10446
  - New Field: total_direct_ fixed_cost
    Old Field: total_direct_fixed_cost4238
  - New Field: gross_patient_revenue
    Old Field: gross_patient_revenue4241
  - New Field: expected_payment
    Old Field: expectedpayment
  - New Field: actual_payment
    Old Field: actualpayment
required_fields:
  - admitdate
  - admitdx9code
  - admitsource
  - admittime
  - admittype
  - attendingmdname
  - dctime
  - directcost
  - dischargedate
  - dischargestatus
  - dob
  - drgcode
  - encounternumber
  - expectedpayment
  - gender
  - icudays
  - indirectcost
  - los
  - medrecn
  - patienttype
  - patzip
  - primarypayor
  - prindx9code
  - prinicd9proccode
  - referringmdrole
  - refferingphyname
  - servicesitename