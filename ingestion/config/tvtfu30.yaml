version: '3.0'
biome_schema:
- Dtype: varchar(30)
  Field: ncdrpatientid
  PHI: true
  Role: IDENTIFIER
- Dtype: varchar(20)
  Field: partid
  PHI: false
  Role: null
- Dtype: varchar(100)
  Field: partname
  PHI: false
  Role: null
- Dtype: text
  Field: firstname
  PHI: true
  Role: NAME
- Dtype: text
  Field: lastname
  PHI: true
  Role: NAME
- Dtype: text
  Field: midname
  PHI: true
  Role: NAME
- Dtype: varchar(30)
  Field: dob
  PHI: true
  Role: 'DOB|DATE_ONLY'
- Dtype: varchar(10)
  Field: ssnna
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: ssn
  PHI: true
  Role: NAME
- Dtype: varchar(30)
  Field: otherid
  PHI: true
  Role: MEDRECNUM
- Dtype: varchar(30)
  Field: gender
  PHI: trur
  Role: GENDER
- Dtype: varchar(10)
  Field: patzipna
  PHI: false
  Role: null
- Dtype: varchar(9)
  Field: patzip
  PHI: true
  Role: PATIENT_ZIP
- Dtype: varchar(10)
  Field: racewhite
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: raceblack
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: raceamindian
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: raceasian
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: raceasianindian
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: racechinese
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: racefilipino
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: racejapanese
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: racekorean
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: racevietnamese
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: raceotherasian
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: racenathaw
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: racenativehawaii
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: raceguamchamorro
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: racesamoan
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: raceotherpacificislander
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: hisporig
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: hispethnicitymexican
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: hispethnicitypuertorico
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: hispethnicitycuban
  PHI: false
  Role: null
- Dtype: varchar(10)
  Field: hispethnicityotherorigin
  PHI: false
  Role: null
- Dtype: varchar(48)
  Field: followupkey
  PHI: false
  Role: null
- Dtype: varchar(31)
  Field: assessmentdate
  PHI: true
  Role: DATE
- Dtype: varchar(30)
  Field: refarrivaldatetime
  PHI: true
  Role: DATE
- Dtype: varchar(30)
  Field: dischargedate
  PHI: true
  Role: '|DISCHARGE_DATE|ANCHOR_DATE|'
- Dtype: varchar(31)
  Field: refprocstartdatetime
  PHI: true
  Role: DATE
- Dtype: varchar(16)
  Field: f_refprotype
  PHI: false
  Role: null
- Dtype: varchar(27)
  Field: f_assessmentmethod
  PHI: false
  Role: null
- Dtype: varchar(29)
  Field: f_status
  PHI: false
  Role: null
- Dtype: varchar(43)
  Field: f_deathcause
  PHI: false
  Role: null
- Dtype: varchar(31)
  Field: f_deathdate
  PHI: trur
  Role: DATE
- Dtype: varchar(35)
  Field: f_residence
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: f_residencend
  PHI: false
  Role: null
- Dtype: varchar(16)
  Field: f_hgb
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: f_hgbnd
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: f_cr
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: f_crnd
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: f_nyha
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: f_nyhand
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: f_12leadekgflag
  PHI: false
  Role: null
- Dtype: varchar(40)
  Field: f_ekgchange
  PHI: false
  Role: null
- Dtype: varchar(21)
  Field: f_popttech
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: f_echond
  PHI: false
  Role: null
- Dtype: varchar(31)
  Field: f_popttechdate
  PHI: true
  Role: DATE
- Dtype: varchar(14)
  Field: f_lvef
  PHI: false
  Role: null
- Dtype: varchar(14)
  Field: f_lvefna
  PHI: false
  Role: null
- Dtype: varchar(14)
  Field: f_post_aorticvalvemeangradient
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: f_avarea
  PHI: false
  Role: null
- Dtype: varchar(25)
  Field: f_popttar
  PHI: false
  Role: null
- Dtype: varchar(16)
  Field: f_post_aiperiseverity
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: f_post_aiperiseveritynd
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_post_aicentralseverity
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: f_post_aicentralseveritynd
  PHI: false
  Role: null
- Dtype: varchar(25)
  Field: f_post_tr
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_paratr
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_paratrnd
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_centr
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_centrnd
  PHI: false
  Role: null
- Dtype: varchar(14)
  Field: f_4dct
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_4dctdate
  PHI: true
  Role: DATE
- Dtype: varchar(12)
  Field: f_vthromb
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_leafdysfx
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: f_kccq12_performed
  PHI: false
  Role: null
- Dtype: varchar(31)
  Field: f_kccq12_date
  PHI: true
  Role: DATE
- Dtype: varchar(68)
  Field: f_kccq12_1a
  PHI: false
  Role: null
- Dtype: varchar(68)
  Field: f_kccq12_1b
  PHI: false
  Role: null
- Dtype: varchar(68)
  Field: f_kccq12_1c
  PHI: false
  Role: null
- Dtype: varchar(53)
  Field: f_kccq12_2
  PHI: false
  Role: null
- Dtype: varchar(53)
  Field: f_kccq12_3
  PHI: false
  Role: null
- Dtype: varchar(53)
  Field: f_kccq12_4
  PHI: false
  Role: null
- Dtype: varchar(53)
  Field: f_kccq12_5
  PHI: false
  Role: null
- Dtype: varchar(59)
  Field: f_kccq12_6
  PHI: false
  Role: null
- Dtype: varchar(32)
  Field: f_kccq12_7
  PHI: false
  Role: null
- Dtype: varchar(58)
  Field: f_kccq12_8a
  PHI: false
  Role: null
- Dtype: varchar(58)
  Field: f_kccq12_8b
  PHI: false
  Role: null
- Dtype: varchar(58)
  Field: f_kccq12_8c
  PHI: false
  Role: null
- Dtype: varchar(17)
  Field: f_kccq12_overall
  PHI: false
  Role: null
- Dtype: varchar(43)
  Field: followup_direct_thrombin_inhibitor
  PHI: false
  Role: null
- Dtype: varchar(43)
  Field: followup_warfarin
  PHI: false
  Role: null
- Dtype: varchar(43)
  Field: followup_aspirin_any
  PHI: false
  Role: null
- Dtype: varchar(43)
  Field: followup_factor_xa_inhibitor
  PHI: false
  Role: null
- Dtype: varchar(43)
  Field: followup_p2y12_any
  PHI: false
  Role: null
- Dtype: varchar(14)
  Field: f_post_mvmeangrad
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_post_mveoa
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_post_mveoamethod
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_post_mvarea
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_post_lvot
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_post_sam
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: f_post_lvisd
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: f_post_lvisdnm
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: f_post_lvidd
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: f_post_lviddnm
  PHI: false
  Role: null
- Dtype: varchar(14)
  Field: f_post_lvesv
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: f_post_lvesvnm
  PHI: false
  Role: null
- Dtype: int
  Field: f_post_lvedv
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: f_post_lvedvnm
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: f_post_lavol
  PHI: false
  Role: null
- Dtype: varchar(17)
  Field: f_lavol_nm
  PHI: false
  Role: null
- Dtype: varchar(14)
  Field: f_post_lavolindex
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: f_lavolindex_nm
  PHI: false
  Role: null
- Dtype: varchar(25)
  Field: f_post_mr
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_post_mrpara
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_paramrnd
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_centralmr
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_centralmrnd
  PHI: false
  Role: null
- Dtype: varchar(14)
  Field: f_sixminwalkperf
  PHI: false
  Role: null
- Dtype: varchar(33)
  Field: f_sixminwalkperfreason
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_sixminwalkdate
  PHI: true
  Role: DATE
- Dtype: varchar(12)
  Field: f_sixminwalkdist
  PHI: false
  Role: null
- Dtype: varchar(38)
  Field: followup_ace_inhibitor_any
  PHI: false
  Role: null
- Dtype: varchar(38)
  Field: followup_aldosterone_antagonists
  PHI: false
  Role: null
- Dtype: varchar(38)
  Field: followup_arb_any
  PHI: false
  Role: null
- Dtype: varchar(38)
  Field: followup_beta_blocker_any
  PHI: false
  Role: null
- Dtype: varchar(38)
  Field: followup_diuretics_other
  PHI: false
  Role: null
- Dtype: varchar(38)
  Field: followup_loop_diuretic
  PHI: false
  Role: null
- Dtype: varchar(38)
  Field: followup_thiazides
  PHI: false
  Role: null
- Dtype: varchar(32)
  Field: aj_mvreinttype
  PHI: false
  Role: null
- Dtype: varchar(25)
  Field: aj_mvind
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: aj_hospitalization
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: aj_sshf
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: aj_hftreatment
  PHI: false
  Role: null
- Dtype: varchar(14)
  Field: f_meddose
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: f_eventname
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: f_eventoccurred
  PHI: false
  Role: null
- Dtype: varchar(31)
  Field: f_eventdate
  PHI: true
  Role: DATE
- Dtype: varchar(41)
  Field: aj_adjudevent
  PHI: false
  Role: null
- Dtype: varchar(31)
  Field: aj_eventdate
  PHI: true
  Role: DATE
- Dtype: varchar(17)
  Field: aj_status
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: aj_deathdate
  PHI: true
  Role: DATE
- Dtype: varchar(12)
  Field: aj_mvcommentsreint
  PHI: false
  Role: null
- Dtype: varchar(20)
  Field: arrivaltime
  PHI: false
  Role: TIME
- Dtype: varchar(30)
  Field: arrivaldate
  PHI: true
  Role: '|ARRIVAL_DATE||DATE_ONLY|'
- Dtype: varchar(30)
  Field: procedurestarttime
  PHI: false
  Role: '|PROCEDURE_TIME||TIME|'
- Dtype: varchar(30)
  Field: procedurestartdate
  PHI: true
  Role: '|PROCEDURE_DATE||DATE_ONLY|'
- Dtype: varchar(30)
  Field: datavrsn
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_refprotypetricuspidvalvproc
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: f_refprotypetavr
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: f_refprotypetmvrepair
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: f_refprotypetmvreplace
  PHI: false
  Role: null
- Dtype: varchar(34)
  Field: f_12leadekg
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: followup_ace_i_or_arb_any
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: followup_aspirin_alone
  PHI: false
  Role: null
- Dtype: varchar(15)
  Field: followup_aspirin_dual_antiplatelet
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: aj_commentsreint
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: aj_commentsstroketia
  PHI: false
  Role: null
- Dtype: varchar(60)
  Field: f_eventid
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: originalotherid
  PHI: true
  Role: MEDRECNUM
- Dtype: varchar(100)
  Field: hospname
  PHI: false
  Role: HOSPITAL
- Dtype: varchar(72)
  Field: filename
  PHI: false
  Role: null
- Dtype: varchar(22)
  Field: version
  PHI: false
  Role: null
- Dtype: varchar(18)
  Field: datasetname
  PHI: false
  Role: null
- Dtype: bigint
  Field: clientfileid
  PHI: false
  Role: null
- Dtype: varchar(31)
  Field: biomeimportdt
  PHI: false
  Role: DATE
- Dtype: varchar(34)
  Field: careentityid
  PHI: false
  Role: CAREENTITY
- Dtype: varchar(34)
  Field: tenantid
  PHI: false
  Role: TENANT
- Dtype: int
  Field: yearmonth
  PHI: false
  Role: ANCHOR_YEAR_MONTH
- Dtype: varchar(12)
  Field: aj_aisev
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: aj_censev
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_tvdgrad
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_tvdgradnd
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_tvannulus
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_tvannulusnd
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_midrvdia
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_midrvdiand
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_basalrvdia
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_basaldiand
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_rvsp
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_rvspnd
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_aj_brainimage
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_aj_brainimagetype
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_bi_find
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_adj_ers_death
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_adj_ers_pervegestate
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_adj_ers_altconscious
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_adj_ers_blind
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_adj_ers_aphasia
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_adj_ers_motorfuncloss
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_adj_ers_sensoryfuncloss
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_adj_ers_facialparalysis
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_adj_ers_prolongedlos
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_adj_ers_other
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_aj_dlae
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_aj_priorliving
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_aj_autdxstroke
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_aj_ava
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_aj_avg
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_aj_tvrein
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_aj_tvind
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_aj_tr
  PHI: false
  Role: null
- Dtype: int
  Field: casesequencenumber
  PHI: false
  Role: CASE_NUMBER
- Dtype: varchar(12)
  Field: aj_neuroclinpresent
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: aj_neurodef
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: aj_neurosxduration
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: aj_primaryind
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: aj_pvsev
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: aj_reinttype
  PHI: false
  Role: null
- Dtype: varchar(30)
  Field: aj_sxonset
  PHI: true
  Role: DATE
- Dtype: varchar(12)
  Field: f_medadmin
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: f_medid
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: followup_antiarrhythmic_any
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: followup_anticoagulants_any
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: followup_dabigatran
  PHI: false
  Role: null
- Dtype: varchar(12)
  Field: procedureenddatetime
  PHI: true
  Role: DATE
- Dtype: bigint unsigned
  Field: biomeencounterid
  PHI: false
  Role: null
custom_fields:
- Derivative Field: F_MedID
  Derivative Field Code: 100013057
  Derivative Value: Loop Diuretics
  Derivative Value Code: 29051009
  Exact Match: 1
  Field: f_meddose
  Reference Field: FUMed_LoopDiureticDose
  Reference Field Code: 112000001975
  Section: FUPMEDS
  Value Map: null
- Delimiter: null
  Derivative Field: F_Adj_ERS
  Derivative Field Code: 362977000
  Derivative Value: Death
  Derivative Value Code: 419620001
  Exact Match: 0
  Field: F_Adj_ERS_Death
  Reference Field: F_Adj_ERS
  Reference Field Code: 362977000
  Section: FADJ
  Value Map:
    Death: 'Yes'
- Delimiter: null
  Derivative Field: F_Adj_ERS
  Derivative Field Code: 362977000
  Derivative Value: Permanent Vegetative State
  Derivative Value Code: 723151005
  Exact Match: 0
  Field: F_Adj_ERS_PerVegeState
  Reference Field: F_Adj_ERS
  Reference Field Code: 362977000
  Section: FADJ
  Value Map:
    Permanent Vegetative State: 'Yes'
- Delimiter: null
  Derivative Field: F_Adj_ERS
  Derivative Field Code: 362977000
  Derivative Value: Altered Consciousness
  Derivative Value Code: 3006004
  Exact Match: 0
  Field: F_Adj_ERS_AltConscious
  Reference Field: F_Adj_ERS
  Reference Field Code: 362977000
  Section: FADJ
  Value Map:
    Altered Consciousness: 'Yes'
- Delimiter: null
  Derivative Field: F_Adj_ERS
  Derivative Field Code: 362977000
  Derivative Value: Blindness
  Derivative Value Code: 193699007
  Exact Match: 0
  Field: F_Adj_ERS_Blind
  Reference Field: F_Adj_ERS
  Reference Field Code: 362977000
  Section: FADJ
  Value Map:
    Blindness: 'Yes'
- Delimiter: null
  Derivative Field: F_Adj_ERS
  Derivative Field Code: 362977000
  Derivative Value: Aphasia
  Derivative Value Code: 87486003
  Exact Match: 0
  Field: F_Adj_ERS_Aphasia
  Reference Field: F_Adj_ERS
  Reference Field Code: 362977000
  Section: FADJ
  Value Map:
    Aphasia: 'Yes'
- Delimiter: null
  Derivative Field: F_Adj_ERS
  Derivative Field Code: 362977000
  Derivative Value: Loss of Motor Function
  Derivative Value Code: 112000001936
  Exact Match: 0
  Field: F_Adj_ERS_MotorFuncLoss
  Reference Field: F_Adj_ERS
  Reference Field Code: 362977000
  Section: FADJ
  Value Map:
    Loss of Motor Function: 'Yes'
- Delimiter: null
  Derivative Field: F_Adj_ERS
  Derivative Field Code: 362977000
  Derivative Value: Loss of Sensory Function
  Derivative Value Code: 33653009
  Exact Match: 0
  Field: F_Adj_ERS_SensoryFuncLoss
  Reference Field: F_Adj_ERS
  Reference Field Code: 362977000
  Section: FADJ
  Value Map:
    Loss of Sensory Function: 'Yes'
- Delimiter: null
  Derivative Field: F_Adj_ERS
  Derivative Field Code: 362977000
  Derivative Value: Facial Paralysis
  Derivative Value Code: 280816001
  Exact Match: 0
  Field: F_Adj_ERS_FacialParalysis
  Reference Field: F_Adj_ERS
  Reference Field Code: 362977000
  Section: FADJ
  Value Map:
    Facial Paralysis: 'Yes'
- Delimiter: null
  Derivative Field: F_Adj_ERS
  Derivative Field Code: 362977000
  Derivative Value: Prolonged Length of Stay
  Derivative Value Code: 112000001937
  Exact Match: 0
  Field: F_Adj_ERS_ProlongedLOS
  Reference Field: F_Adj_ERS
  Reference Field Code: 362977000
  Section: FADJ
  Value Map:
    Prolonged Length of Stay: 'Yes'
- Delimiter: null
  Derivative Field: F_Adj_ERS
  Derivative Field Code: 362977000
  Derivative Value: Other
  Derivative Value Code: 100000351
  Exact Match: 0
  Field: F_Adj_ERS_Other
  Reference Field: F_Adj_ERS
  Reference Field Code: 362977000
  Section: FADJ
  Value Map:
    Other: 'Yes'
- Delimiter: null
  Derivative Field: F_RefProType
  Derivative Field Code: 112000001167
  Derivative Value: TAVR
  Derivative Value Code: 41873006
  Exact Match: 0
  Field: F_RefProTypeTAVR
  Reference Field: F_RefProType
  Reference Field Code: 112000001167
  Section: FOLLOWUP
  Value Map:
    TAVR: 'Yes'
- Delimiter: null
  Derivative Field: F_RefProType
  Derivative Field Code: 112000001167
  Derivative Value: TMVr
  Derivative Value Code: 112000001801
  Exact Match: 0
  Case Match: 1
  Field: F_RefProTypeTMVRepair
  Reference Field: F_RefProType
  Reference Field Code: 112000001167
  Section: FOLLOWUP
  Value Map:
    TMVr: 'Yes'
- Delimiter: null
  Derivative Field: F_RefProType
  Derivative Field Code: 112000001167
  Derivative Value: TMVR
  Derivative Value Code: 112000001458
  Exact Match: 0
  Case Match: 1
  Field: F_RefProTypeTMVReplace
  Reference Field: F_RefProType
  Reference Field Code: 112000001167
  Section: FOLLOWUP
  Value Map:
    TMVR: 'Yes'
- Delimiter: null
  Derivative Field: F_RefProType
  Derivative Field Code: 112000001167
  Derivative Value: Tricuspid Valve Procedure
  Derivative Value Code: 112000001977
  Exact Match: 0
  Field: F_RefProTypeTricuspidValvProc
  Reference Field: F_RefProType
  Reference Field Code: 112000001167
  Section: FOLLOWUP
  Value Map:
    Tricuspid Valve Procedure: 'Yes'
- Delimiter: null
  Derivative Field: F_MedID
  Derivative Field Code: 100013057
  Derivative Value: Angiotensin Converting Enzyme Inhibitor
  Derivative Value Code: 41549009
  Exact Match: 1
  Field: FollowUp_ACE_Inhibitor_any
  Reference Field: F_MedAdmin1
  Reference Field Code: 432102000
  Section: FUPMEDS
  Value Map: null
- Delimiter: null
  Derivative Field: F_MedID
  Derivative Field Code: 100013057
  Derivative Value: Aldosterone Antagonist
  Derivative Value Code: 372603003
  Exact Match: 1
  Field: FollowUp_Aldosterone_Antagonists
  Reference Field: F_MedAdmin1
  Reference Field Code: 432102000
  Section: FUPMEDS
  Value Map: null
- Delimiter: null
  Derivative Field: F_MedID
  Derivative Field Code: 100013057
  Derivative Value: Direct thrombin inhibitor
  Derivative Value Code: 414010005
  Exact Match: 1
  Field: FollowUp_Direct_Thrombin_Inhibitor
  Reference Field: F_MedAdmin1
  Reference Field Code: 432102000
  Section: FUPMEDS
  Value Map: null
- Delimiter: null
  Derivative Field: F_MedID
  Derivative Field Code: 100013057
  Derivative Value: Warfarin
  Derivative Value Code: 11289
  Exact Match: 1
  Field: FollowUp_Warfarin
  Reference Field: F_MedAdmin1
  Reference Field Code: 432102000
  Section: FUPMEDS
  Value Map: null
- Delimiter: null
  Derivative Field: F_MedID
  Derivative Field Code: 100013057
  Derivative Value: Aspirin
  Derivative Value Code: 1191
  Exact Match: 1
  Field: FollowUp_Aspirin_any
  Reference Field: F_MedAdmin1
  Reference Field Code: 432102000
  Section: FUPMEDS
  Value Map: null
- Delimiter: null
  Derivative Field: F_MedID
  Derivative Field Code: 100013057
  Derivative Value: Angiotensin II Receptor Blocker
  Derivative Value Code: 372913009
  Exact Match: 1
  Field: FollowUp_ARB_any
  Reference Field: F_MedAdmin1
  Reference Field Code: 432102000
  Section: FUPMEDS
  Value Map: null
- Delimiter: null
  Derivative Field: F_MedID
  Derivative Field Code: 100013057
  Derivative Value: Beta Blocker
  Derivative Value Code: 33252009
  Exact Match: 1
  Field: FollowUp_Beta_Blocker_any
  Reference Field: F_MedAdmin1
  Reference Field Code: 432102000
  Section: FUPMEDS
  Value Map: null
- Delimiter: null
  Derivative Field: F_MedID
  Derivative Field Code: 100013057
  Derivative Value: Diuretics Not Otherwise Specified
  Derivative Value Code: 112000001417
  Exact Match: 1
  Field: FollowUp_Diuretics_Other
  Reference Field: F_MedAdmin1
  Reference Field Code: 432102000
  Section: FUPMEDS
  Value Map: null
- Delimiter: null
  Derivative Field: F_MedID
  Derivative Field Code: 100013057
  Derivative Value: Loop Diuretics
  Derivative Value Code: 29051009
  Exact Match: 1
  Field: FollowUp_Loop_Diuretic
  Reference Field: F_MedAdmin1
  Reference Field Code: 432102000
  Section: FUPMEDS
  Value Map: null
- Delimiter: null
  Derivative Field: F_MedID
  Derivative Field Code: 100013057
  Derivative Value: Thiazides
  Derivative Value Code: 372747003
  Exact Match: 1
  Field: FollowUp_Thiazides
  Reference Field: F_MedAdmin1
  Reference Field Code: 432102000
  Section: FUPMEDS
  Value Map: null
- Delimiter: null
  Derivative Field: F_MedID
  Derivative Field Code: 100013057
  Derivative Value: Direct Factor Xa Inhibitor
  Derivative Value Code: 112000000696
  Exact Match: 1
  Field: FollowUp_Factor_Xa_Inhibitor
  Reference Field: F_MedAdmin1
  Reference Field Code: 432102000
  Section: FUPMEDS
  Value Map: null
- Delimiter: null
  Derivative Field: F_MedID
  Derivative Field Code: 100013057
  Derivative Value: P2Y12 Antagonist
  Derivative Value Code: 112000001003
  Exact Match: 1
  Field: FollowUp_P2Y12_any
  Reference Field: F_MedAdmin1
  Reference Field Code: 432102000
  Section: FUPMEDS
  Value Map: null
pivot_sections:
- FUPMEDS
rename_fields:
- New Field: ZipCode
  Old Field: patzip
- New Field: ZipCodeNA
  Old Field: patzipna
- New Field: F_Method
  Old Field: f_assessmentmethod
- New Field: Sex
  Old Field: gender
- New Field: F_MedAdmin1
  Old Field: f_medadmin
- New Field: F_AssessmentDate
  Old Field: assessmentdate
- New Field: F_LVIDs
  Old Field: f_post_lvisd
- New Field: F_AJ_BrainImag
  Old Field: f_aj_brainimage
- New Field: F_AVMeanGradient
  Old Field: f_post_aorticvalvemeangradient
- New Field: F_MVA
  Old Field: f_post_mvarea
- New Field: F_LVOT
  Old Field: f_post_lvot
- New Field: F_SAM
  Old Field: f_post_sam
- New Field: FOLLOW_CREAT
  Old Field: f_cr
- New Field: F_AJ_NeuroClinPresent
  Old Field: aj_neuroclinpresent
- New Field: F_LVESV_NM
  Old Field: f_post_lvesvnm
- New Field: FUMed_LoopDiureticDose
  Old Field: f_meddose
- New Field: F_Condition_Event
  Old Field: f_eventname
- New Field: F_LVESV
  Old Field: f_post_lvesv
- New Field: F_LAVol
  Old Field: f_post_lavol
- New Field: F_AJ_PVSev
  Old Field: aj_pvsev
- New Field: F_LVEDV_NM
  Old Field: f_post_lvedvnm
- New Field: F_LAVolIndex
  Old Field: f_post_lavolindex
- New Field: F_LVIDd_NM
  Old Field: f_post_lviddnm
- New Field: F_AJ_PrimaryInd
  Old Field: aj_primaryind
- New Field: FollowCreatinineNotDrawn
  Old Field: f_crnd
- New Field: FU_ProcHgb1
  Old Field: f_hgb
- New Field: F_AJ_MVReintInd
  Old Field: aj_mvind
- New Field: FU_RefDischargeDate
  Old Field: dischargedate
- New Field: F_AJ_ReIntType
  Old Field: aj_reinttype
- New Field: F_LVIDs_NM
  Old Field: f_post_lvisdnm
- New Field: F_AJ_NeuroSymptDuration
  Old Field: aj_neurosxduration
- New Field: F_AJ_EventDate
  Old Field: aj_eventdate
- New Field: F_MV_EOA_MOA
  Old Field: f_post_mveoamethod
- New Field: F_AJ_Status
  Old Field: aj_status
- New Field: F_LVEDV
  Old Field: f_post_lvedv
- New Field: F_AJ_AdjudEvent
  Old Field: aj_adjudevent
- New Field: F_ParaARND
  Old Field: f_post_aiperiseveritynd
- New Field: F_AJ_SSHF
  Old Field: aj_sshf
- New Field: F_CentARND
  Old Field: f_post_aicentralseveritynd
- New Field: F_ParaAR
  Old Field: f_post_aiperiseverity
- New Field: F_AJ_CenSev
  Old Field: aj_censev
- New Field: F_ParaMR
  Old Field: f_post_mrpara
- New Field: F_12LeadEKG
  Old Field: f_12leadekgflag
- New Field: FupEvOccurred
  Old Field: f_eventoccurred
- New Field: F_MR
  Old Field: f_post_mr
- New Field: F_AJ_MVReinType
  Old Field: aj_mvreinttype
- New Field: F_AJ_NeuroDef
  Old Field: aj_neurodef
- New Field: F_MeanMVGrad
  Old Field: f_post_mvmeangrad
- New Field: F_AJ_DeathDate
  Old Field: aj_deathdate
- New Field: F_AR
  Old Field: f_popttar
- New Field: F_AJ_AISev
  Old Field: aj_aisev
- New Field: F_CentAR
  Old Field: f_post_aicentralseverity
- New Field: FupEventDate
  Old Field: f_eventdate
- New Field: F_MV_EOA
  Old Field: f_post_mveoa
- New Field: FUHgbND
  Old Field: f_hgbnd
- New Field: F_Condition_Event
  Old Field: f_eventid
- New Field: AJ_CommentsFU
  Old Field: aj_mvcommentsreint
- New Field: F_AJ_HFTreatment
  Old Field: aj_hftreatment
- New Field: F_AJ_Hospital
  Old Field: aj_hospitalization
- New Field: F_LVIDd
  Old Field: f_post_lvidd
- New Field: F_AJ_SxOnset
  Old Field: aj_sxonset
- New Field: RefArrivalDateTime
  Old Field: arrivaltime
- New Field: RefProcStartDateTime
  Old Field: procedurestarttime
- New Field: RefArrivalDateTime
  Old Field: arrivaldate
- New Field: RefProcStartDateTime
  Old Field: procedurestartdate
value_mapping:
  - Field: f_eventid
    Value Map:
      Annular Rupture: E007
      Aortic Dissection: E008
      ASD Defect Closure due to Transseptal Catheterization: E054
      Atrial Fibrillation: E006
      Bleeding - Access Site: E017
      Bleeding - Gastrointestinal: E020
      Bleeding - Genitourinary: E021
      Bleeding - Hematoma at Access Site: E018
      Bleeding - Other: E022
      Bleeding - Retroperitoneal: E019
      Cardiac Arrest: E005
      Cardiac Perforation: E009
      Cardiac Surgery or Intervention - Other Unplanned: E031
      Complete Leaflet Clip Detachment: E051
      Coronary Artery Compression: E002
      Delivery System Component Embolization: E058
      Device Embolization: E050
      Device Migration: E023
      Device Related Event - Other: E028
      Device Thrombosis: E027
      Dialysis (New Requirement): E029
      Endocarditis: E003
      ICD: E040
      Left Ventricular Outflow Tract Obstruction: 253546004
      Mitral Leaflet or Subvalvular Injury: 112000001886
      Myocardial Infarction: E059
      Pacemaker Lead Dislodgement or Dysfunction: 112000001884
      Percutaneous Coronary Intervention: E033
      Permanent Pacemaker: E039
      Pulmonary Embolism: 59282003
      Reintervention - Aortic Valve: E030
      Reintervention - Mitral Valve: E053
      Reintervention - Tricuspid Valve: 112000001820
      Single Leaflet Device Attachment: E049
      Stroke - Hemorrhagic: E012
      Stroke - Ischemic: E011
      Stroke - Undetermined: E013
      Transient Ischemic Attack (TIA): E010
      Transseptal Complication: E052
      Vascular Complication - Major: E041
      Vascular Complication - Minor: E042
      Vascular Surgery or Intervention - Unplanned: E032
      Readmission - Cardiac (Not Heart Failure): E056
      Readmission - Heart Failure: E055
      Readmission - Non-Cardiac: E057
      COVID-19 Positive: 112000001982
      Deep Vein Thrombosis: 128053003
      Device Fracture: E038
      Bleeding - Life Threatening: E037
      Bleeding - Major: E043
      Readmission (Valve Related): E034
      Readmission - (Non-Valve Related): E035
  - Field: f_eventname
    Value Map:
      COVID-19 Positive: COVID-19
  - Field: f_popttech
    Value Map:
      Transthoracic Echo (TTE): Yes - TTE
      Transesophageal Echocardiogram (TEE): Yes - TEE
  - Field: F_KCCQ12_1a
    Value Map:
      1 - Extremely Limited: Extremely Limited
      2 - Quite a Bit Limited: Quite a Bit Limited
      3 - Moderately Limited: Moderately Limited
      4 - Slightly Limited: Slightly Limited
      5 - Not at All Limited: Not at All Limited
      6 - Limited for Other Reasons or Did Not Do These Activities: Limited for Other Reasons or Did Not Do These Activities
  - Field: F_KCCQ12_1b
    Value Map:
      1 - Extremely Limited: Extremely Limited
      2 - Quite a Bit Limited: Quite a Bit Limited
      3 - Moderately Limited: Moderately Limited
      4 - Slightly Limited: Slightly Limited
      5 - Not at All Limited: Not at All Limited
      6 - Limited for Other Reasons or Did Not Do These Activities: Limited for Other Reasons or Did Not Do These Activities
  - Field: F_KCCQ12_1c
    Value Map:
      1 - Extremely Limited: Extremely Limited
      2 - Quite a Bit Limited: Quite a Bit Limited
      3 - Moderately Limited: Moderately Limited
      4 - Slightly Limited: Slightly Limited
      5 - Not at All Limited: Not at All Limited
      6 - Limited for Other Reasons or Did Not Do These Activities: Limited for Other Reasons or Did Not Do These Activities
  - Field: F_KCCQ12_2
    Value Map:
      1 - Every Morning: Every Morning
      2 - Three or More Times Per Week But Not Everyday: 3 or More Times Per Week But Not Everyday
      3 - One to Two Times Per Week: 1-2 Times Per Week
      4 - Less Than Once a Week: Less Than Once a Week
      5 - Never Over the Past Two Weeks: Never Over the Past 2 Weeks
  - Field: F_KCCQ12_3
    Value Map:
      1 - All the Time: All the Time
      2 - Several Times Per Day: Several Times Per Day
      3 - At Least Once Per Day: At Least Once Per Day
      4 - Three or More Times Per Week But Not Everyday: 3 or More Times Per Week But Not Everyday
      5 - One to Two Times Per Week: 1-2 Times Per Week
      6 - Less Than Once a Week: Less Than Once a Week
      7 - Never Over the Past Two Weeks: Never Over the Past 2 Weeks
  - Field: F_KCCQ12_4
    Value Map:
      1 - All the Time: All the Time
      2 - Several Times Per Day: Several Times Per Day
      3 - At Least Once Per Day: At Least Once Per Day
      4 - Three or More Times Per Week But Not Everyday: 3 or More Times Per Week But Not Everyday
      5 - One to Two Times Per Week: 1-2 Times Per Week
      6 - Less Than Once a Week: Less Than Once a Week
      7 - Never Over the Past Two Weeks: Never Over the Past 2 Weeks
  - Field: F_KCCQ12_5
    Value Map:
      1 - Every Night: Every Night
      2 - Three or More Times Per Week But Not Everyday: 3 or More Times Per Week But Not Everyday
      3 - One to Two Times Per Week: 1-2 Times Per Week
      4 - Less Than Once a Week: Less Than Once a Week
      5 - Never Over the Past Two Weeks: Never Over the Past 2 Weeks
  - Field: F_KCCQ12_6
    Value Map:
      1 - It Has Extremely Limited My Enjoyment of Life: It Has Extremely Limited My Enjoyment of Life
      2 - It Has Limited My Enjoyment of Life Quite a Bit: It Has Limited My Enjoyment of Life Quite a Bit
      3 - It Has Moderately Limited My Enjoyment of Life: It Has Moderately Limited My Enjoyment of Life
      4 - It Has Slightly Limited My Enjoyment of Life: It Has Slightly Limited My Enjoyment of Life
      5 - It Has Not Limited My Enjoyment of Life at All: It Has Not Limited My Enjoyment of Life at All
  - Field: F_KCCQ12_7
    Value Map:
      1 - Not At All Satisfied: Not At All Satisfied
      2 - Mostly Dissatisfied: Mostly Dissatisfied
      3 - Somewhat Satisfied: Somewhat Satisfied
      4 - Mostly Satisfied: Mostly Satisfied
      5 - Completely Satisfied: Completely Satisfied
  - Field: F_KCCQ12_8a
    Value Map:
      1 - Severely Limited: Severely Limited
      2 - Limited Quite a Bit: Limited Quite a Bit
      3 - Moderately Limited: Moderately Limited
      4 - Slightly Limited: Slightly Limited
      5 - Did Not Limit at All: Did Not Limit at All
      6 - Does Not Apply or Did Not Do for Other Reasons: Does Not Apply or Did Not Do for Other Reasons
  - Field: F_KCCQ12_8b
    Value Map:
      1 - Severely Limited: Severely Limited
      2 - Limited Quite a Bit: Limited Quite a Bit
      3 - Moderately Limited: Moderately Limited
      4 - Slightly Limited: Slightly Limited
      5 - Did Not Limit at All: Did Not Limit at All
      6 - Does Not Apply or Did Not Do for Other Reasons: Does Not Apply or Did Not Do for Other Reasons
  - Field: F_KCCQ12_8c
    Value Map:
      1 - Severely Limited: Severely Limited
      2 - Limited Quite a Bit: Limited Quite a Bit
      3 - Moderately Limited: Moderately Limited
      4 - Slightly Limited: Slightly Limited
      5 - Did Not Limit at All: Did Not Limit at All
      6 - Does Not Apply or Did Not Do for Other Reasons: Does Not Apply or Did Not Do for Other Reasons
  - Field: F_NYHA
    Value Map:
      Class I: I
      Class II: II
      Class III: III
      Class IV: IV
transform_fields:
  - Field: FollowUp_Aspirin_dual_antiplatelet
    Transform Type: conditional_and
    Args:
      true_val: "Yes"
      false_val: null
      fields:
        - name: followup_aspirin_any
          values: ['Yes']
        - name: followup_p2y12_any
          values: ['Yes']
  - Field: FollowUp_Aspirin_Alone
    Transform Type: conditional_and
    Args:
      true_val: "Yes"
      false_val: null
      fields:
        - name: followup_aspirin_any
          values: ['YES']
        - name: followup_p2y12_any
          values: ['NOT']

  - Field: FollowUp_ACE_I_OR_ARB_Any
    Transform Type: conditional_or
    Args:
      true_val: "Yes"
      false_val: null
      fields:
        - name: followup_arb_any
          values: ['YES']
        - name: followup_ace_inhibitor_any
          values: ['YES']
  - Field: f_12leadekg
    Transform Type: conditional_and
    Args:
      true_val: 'Not performed'
      false_val: null
      fields:
        - name: f_12leadekgflag
          values: ['NO']
  - Field: f_12leadekg
    Transform Type: conditional_and
    Args:
      true_val: "No significant changes"
      false_val: f_12leadekg
      fields:
        - name: f_ekgchange
          values: ['NO SIGNIFICANT CHANGE']
  - Field: f_12leadekg
    Transform Type: conditional_and
    Args:
      true_val: "New changes noted"
      false_val: f_12leadekg
      fields:
        - name: f_12leadekgflag
          values: ['YES']
        - name: f_ekgchange
          values: ['Cardiac Arrhythmia','New Left Bundle Branch Block','Pathological Q Wave']
  - Field: originalotherid
    Transform Type: additional_field
    Args:
      Source: otherid
  - Field: refarrivaldatetime
    Transform Type: additional_field
    Args:
      Source: arrivaltime
  - Field: refprocstartdatetime
    Transform Type: additional_field
    Args:
      Source: procedurestarttime
  - Field: aj_commentsreint
    Transform Type: additional_field
    Args:
      Source: aj_mvcommentsreint
  - Field: aj_commentsstroketia
    Transform Type: additional_field
    Args:
      Source: aj_mvcommentsreint
  - Field: ssn
    Transform Type: additional_field
    Args:
      Source: null
      custom string: 1
    Computation: false