version: '5.0'
biome_schema:
  - Dtype: varchar(30)
    Field: NCDRPatientId
    PHI: true
    Role: IDENTIFIER
  - Dtype: int(11)
    Field: PartID
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: PartName
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: FirstName
    PHI: true
    Role: NAME
  - Dtype: varchar(30)
    Field: LastName
    PHI: true
    Role: NAME
  - Dtype: varchar(30)
    Field: MidName
    PHI: true
    Role: NAME
  - Dtype: varchar(10)
    Field: SSNNA
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: SSN
    PHI: true
    Role: NAME
  - Dtype: varchar(30)
    Field: OtherId
    PHI: true
    Role: MEDRECNUM
  - Dtype: varchar(30)
    Field: DOB
    PHI: true
    Role: DOB
  - Dtype: varchar(30)
    Field: Gender
    PHI: true
    Role: GENDER
  - Dtype: varchar(25)
    Field: PatZipNA
    PHI: false
    Role: null
  - Dtype: varchar(9)
    Field: PatZip
    PHI: true
    Role: PATIENT_ZIP
  - Dtype: varchar(10)
    Field: RaceWhite
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: RaceBlackAfricanAmerican
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: RaceAmericanIndianAlaskanNative
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceAsian
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceAsianIndian
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceChinese
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceFilipino
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceJapanese
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceKorean
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceVietnamese
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: RaceOtherAsian
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: RaceNatHawPacificIslander
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceNatHaw
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: RaceGuamanianOrChamorro
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceSamoan
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: RaceOtherPacificIslander
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: HispOrig
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HispanicEthnicityTypeMexicanMexicanAmericanChicano
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HispanicEthnicityTypePuertoRican
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HispanicEthnicityTypeCuban
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HispanicEthnicityTypeOtherHisp
    PHI: false
    Role: null
  - Dtype: text
    Field: EpisodeId
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: ArrivalDatetime
    PHI: true
    Role: ADMISSION_DATE
  - Dtype: varchar(25)
    Field: AdmFName
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: AdmLName
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: AdmMidName
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: AdmNPI
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HealthIns
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Medicare
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Medicaid
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PrivateHealthInsurance
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: MiltaryHealthCare
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: StateSpecificPlan
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: IndianHealthService
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: NonUSInsurance
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HIC
    PHI: true
    Role: NAME
  - Dtype: varchar(50)
    Field: EnrolledStudy
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: PtRestriction2
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: AttnLName
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: AttnFName
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: AttnMidName
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: AttnNPI
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: StudyName
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: StudyPatientID
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: Hypertension
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: Dyslipidemia
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PriorMI
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PriorMIDate
    PHI: true
    Role: DATE
  - Dtype: varchar(10)
    Field: PriorPCI
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PriorPCIDate
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: LMPCI
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LMPCIUnk
    PHI: false
    Role: null
  - Dtype: decimal(5,2)
    Field: Height
    PHI: false
    Role: null
  - Dtype: decimal(5,2)
    Field: Weight
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: FamilyHXCAD
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HxCVD
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PriorPAD
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HxChronicLungDisease
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HxPriorCABG
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: HxCABGDate
    PHI: true
    Role: DATE
  - Dtype: varchar(255)
    Field: TobaccoUse
    PHI: false
    Role: null
  - Dtype: varchar(24)
    Field: TobaccoType
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: SmokingAmount
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CAOutHospital
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CAWitness
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CAPostEMS
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: InitCARhythmUnk
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: InitCARhythm
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CATransferFac
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: Diabetes
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: CurrentDialysis
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: CSHAScale
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: ProcedureDate
    PHI: true
    Role: PROCEDURE_DATE
  - Dtype: varchar(30)
    Field: ProcedureEndDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: DiagCorAngio
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: DCathLName
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: DCathFName
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: DCathMidName
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: DCathNPI
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PCIProc
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: PCILName
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PCIFName
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PCIMidName
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: PCINPI
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LeftHeartCath
    PHI: false
    Role: null
  - Dtype: decimal(2,0)
    Field: PrePCILVEF
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: ConcomProc
    PHI: false
    Role: null
  - Dtype: varchar(59)
    Field: ConcomProcType
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: AccessSite
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Crossover
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: ClosureMethodNA
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: VenousAccess
    PHI: false
    Role: null
  - Dtype: decimal(3,0)
    Field: ProcSystolicBP
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CAInHosp
    PHI: false
    Role: null
  - Dtype: decimal(4,1)
    Field: FluoroTime
    PHI: false
    Role: null
  - Dtype: decimal(3,0)
    Field: ContrastVol
    PHI: false
    Role: null
  - Dtype: decimal(5,0)
    Field: FluoroDoseKerm
    PHI: false
    Role: null
  - Dtype: decimal(7,0)
    Field: FluoroDoseDAP
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HxHF
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: PriorNYHA
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HFNewDiag
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: HFType
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HFTypeUnk
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PreProcAntiarrhythmicAgentOther
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PreProcASA
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PreProcBetaBlocker
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PreProcCalciumChannelBlocking
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PreProcLongActingNitrate
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PreProcNonStatin
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PreProcRanolazine
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PreProcStatinAny
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PreProcPCSK9
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: ECAssessMethod
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: ECGResults
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: AntiArrhyTherapy
    PHI: false
    Role: null
  - Dtype: varchar(108)
    Field: ECGFindings
    PHI: false
    Role: null
  - Dtype: decimal(3,0)
    Field: HR
    PHI: false
    Role: null
  - Dtype: varchar(29)
    Field: NonSustainedVTType
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: StressPerformed
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CardiacCTA
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: CardiacCTADate
    PHI: true
    Role: DATE
  - Dtype: varchar(49)
    Field: CardiacCTAResults
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CardiacCTAResultsUnk
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CalciumScoreAssessed
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: CalciumScore
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: CalciumScoreDate
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: PreProcLVEFAssessed
    PHI: false
    Role: null
  - Dtype: decimal(2,0)
    Field: PreProcLVEF
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PriorDxAngioProc
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PriorDxAngioDate
    PHI: true
    Role: DATE
  - Dtype: varchar(68)
    Field: PriorDxAngioResults
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: PriorDxCathResultsUnk
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: ExerciseStressTest
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: StressEcho
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: StressNuclear
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: CMRStressTest
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: ExerciseTestResult
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: ExerciseTestResultDate
    PHI: true
    Role: DATE
  - Dtype: varchar(25)
    Field: ExerciseTestRisk
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: StressEchoResult
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: StressEchoResultDate
    PHI: true
    Role: DATE
  - Dtype: varchar(25)
    Field: StressEchoRisk
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: StressNuclearResult
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: StressNuclearResultDate
    PHI: true
    Role: DATE
  - Dtype: varchar(25)
    Field: StressNuclearRisk
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: CMRStressResults
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: CMRStressTestResultDate
    PHI: true
    Role: DATE
  - Dtype: varchar(20)
    Field: CMRStressRisk
    PHI: false
    Role: null
  - Dtype: int(25)
    Field: ClosureCounter
    PHI: false
    Role: null
  - Dtype: varchar(75)
    Field: ClosureDevId
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: ClosureUDI
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PreProcTnIND
    PHI: false
    Role: null
  - Dtype: decimal(6,2)
    Field: PreProcTnI
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PreProcTNTND
    PHI: false
    Role: null
  - Dtype: decimal(6,2)
    Field: PreProcTNT
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PreProcCreatND
    PHI: false
    Role: null
  - Dtype: decimal(4,2)
    Field: PreProcCreat
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HgbNd
    PHI: false
    Role: null
  - Dtype: decimal(4,2)
    Field: Hgb
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LipidsTCND
    PHI: false
    Role: null
  - Dtype: decimal(4,0)
    Field: LipidsTC
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LipidsHDLND
    PHI: false
    Role: null
  - Dtype: decimal(3,0)
    Field: LipidsHDL
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PostProcTnIND
    PHI: false
    Role: null
  - Dtype: decimal(6,2)
    Field: PostProcTnI
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PostProcTNTND
    PHI: false
    Role: null
  - Dtype: decimal(6,2)
    Field: PostProcTNT
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PostProcCreatND
    PHI: false
    Role: null
  - Dtype: decimal(4,2)
    Field: PostProcCreat
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PostProcHGBND
    PHI: false
    Role: null
  - Dtype: decimal(4,2)
    Field: PostProcHGB
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: ACS_gt_24_hrs
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LV_Dysfunction
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: New_Onset_Angina_lte_2months
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Suspected_CAD
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: CPSxAssess
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CVInstability
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: CVInstabilityType
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: VSupport
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PharmVasoSupp
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: MechVentSupp
    PHI: false
    Role: null
  - Dtype: varchar(75)
    Field: MVSupportTiming
    PHI: false
    Role: null
  - Dtype: varchar(75)
    Field: MVSupportDevice
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: PreOPEvalForSurgType
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: FuncCapacityNA
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: FuncCapacity
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: SurgRisk
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: OrganTransplantSurg
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: OrganTransplantDonor
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: OrganTransplantType
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Cardiac_Arrhythmia
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CardioLVSD
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: ACS_lte_24_hrs
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Worsening_Angina
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PERIOPEVAL
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: Syncope
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Stable_Known_CAD
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PostCardiacTransplant
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Valvular_Disease
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Resuscitated_Cardiac_Arrest
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Other
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: ValvularDzStenosisType
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: ValvularDzStenosisSev
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: ValvularDzRegurgType
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: ValvularDzRegSev
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: Dominance
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: NVStenosis
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: GraftStenosis
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: NVSegmentID
    PHI: false
    Role: null
  - Dtype: decimal(3,0)
    Field: NVCoroVesselStenosis
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: NVAdjuncMeasObtained
    PHI: false
    Role: null
  - Dtype: decimal(3,2)
    Field: FFRNative
    PHI: false
    Role: null
  - Dtype: decimal(3,2)
    Field: NV_IFR_Native
    PHI: false
    Role: null
  - Dtype: decimal(4,2)
    Field: IVUSNative
    PHI: false
    Role: null
  - Dtype: decimal(4,2)
    Field: NV_OCT_Native
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: GraftSegmentID
    PHI: false
    Role: null
  - Dtype: decimal(3,0)
    Field: GraftCoroVesselStenosis
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: CABGGraftVesselUnk
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: CABGGraftVessel
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: GraftAdjuncMeasObtained
    PHI: false
    Role: null
  - Dtype: decimal(3,2)
    Field: FFRGraft
    PHI: false
    Role: null
  - Dtype: decimal(3,2)
    Field: GT_IFR
    PHI: false
    Role: null
  - Dtype: decimal(4,2)
    Field: IVUSGraft
    PHI: false
    Role: null
  - Dtype: decimal(4,2)
    Field: GT_OCT
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: PCIStatus
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HypothermiaInduced
    PHI: false
    Role: null
  - Dtype: varchar(75)
    Field: HypothermiaInducedTiming
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: Proc_LOC
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PCIDecision
    PHI: false
    Role: null
  - Dtype: varchar(75)
    Field: CVTxDecision
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: MultiVesselDz
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: MultiVessProcType
    PHI: false
    Role: null
  - Dtype: varchar(75)
    Field: PCIIndication
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: SymptomDate
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: SymptomTimeUnk
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: SymptomTime
    PHI: false
    Role: TIME
  - Dtype: varchar(25)
    Field: Thrombolytics
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: ThrombolyticTherapyDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: SyntaxScoreUnk
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: SyntaxScore
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: StemiFirstNoted
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: SubECGDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: SubECGED
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PatientTransPCI
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: EDPresentDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(30)
    Field: FirstDevActiDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: PtPCIDelayReason
    PHI: false
    Role: null
  - Dtype: varchar(75)
    Field: PCIDelayReason
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Procedure_Bivalirudin
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Procedure_Fondaparinux
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Procedure_Low_Molecular_Weight_Heparin_any
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Procedure_Unfractionated_Heparin_any
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Procedure_Warfarin
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Procedure_Vorapaxar
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Procedure_GPInhibitors_Any
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Procedure_Apixaban
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Procedure_Dabigatran
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Procedure_Edoxaban
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Procedure_Rivaroxaban
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Procedure_Cangrelor
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Procedure_Clopidogrel
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Procedure_Prasugrel
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Procedure_Ticagrelor
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Procedure_Argatroban
    PHI: false
    Role: null
  - Dtype: int(8)
    Field: LesionCounter
    PHI: false
    Role: null
  - Dtype: varchar(70)
    Field: SegmentId
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CulpritArtery
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CulpritArteryUnk
    PHI: false
    Role: null
  - Dtype: decimal(3,0)
    Field: StenosisPriorTreat
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: ChronicOcclusion
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: ChronicOcclusionUnk
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: PreProcTIMI
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PrevTreatedLesion
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PrevTreatedLesionDate
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: PreviousStent
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: INREStenosis
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: InThrombosis
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: StentType
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: StentTypeUnk
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LesionGraft
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: LesionGraftType
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: LocGraft
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: NavGraftNatLes
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: LesionComplexity
    PHI: false
    Role: null
  - Dtype: decimal(3,0)
    Field: LesionLength
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: SevereCalcification
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: BifurcationLesion
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: GuideWireLesion
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: DeviceDeployed
    PHI: false
    Role: null
  - Dtype: decimal(3,0)
    Field: StenosisPostProc
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: PostProcTIMI
    PHI: false
    Role: null
  - Dtype: int(8)
    Field: ICDevCounter
    PHI: false
    Role: null
  - Dtype: varchar(75)
    Field: ICDevId
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: ICUniDevID
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: ICDevCounterAssn
    PHI: false
    Role: null
  - Dtype: decimal(4,2)
    Field: DeviceDiameter
    PHI: false
    Role: null
  - Dtype: decimal(3,0)
    Field: DeviceLength
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PerfSeg
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: DissectionSEG
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PostTransfusion
    PHI: false
    Role: null
  - Dtype: decimal(3,0)
    Field: PRBCUnits
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: TransfusPostPCI
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: TransfusionPostSurg
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: BleedingAccessSite
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: BleedingAccessSiteDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: BleedingGastrointestinal
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: BleedingGastrointestinalDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(25)
    Field: BleedingGenitourinary
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: BleedingGenitourinaryDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: BleedingHematomaAccessSite
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: BleedingHematomaASDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: BleedingOther
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: BleedingOtherDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: BleedingRetroperitoneal
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: BleedingRetroDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: CardiacArrest
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: CardiacArrestDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: CardiacTamponade
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: CardiacTamponadeDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: CardiogenicShock
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: CardiogenicShockDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: HeartFailure
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: HeartFailureDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: MyocardialInfarction
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: MyocardialInfarctionDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: NewRequirementforDialysis
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: DialysisDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: OtherVasCompReqTreatment
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: OtherVasCompReqTreatmentDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: StrokeHemorrhagic
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: StrokeHemorrhagicDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: StrokeIschemic
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: StrokeIschemicDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: StrokeUndetermined
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: StrokeUndeterminedDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: HospIntervention
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CABG
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Valvular_Intervention
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Cardiac_Surgery_non_CABG
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Structural_Heart_Intervention_non_valvular
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Non_Cardiac_Surgery
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: EP_Study
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HospInterventionType_Other
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: CABGStatus
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: CABGIndication
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: CABGDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: DCCreatinineND
    PHI: false
    Role: null
  - Dtype: decimal(4,2)
    Field: DCCreatinine
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: DCHgbND
    PHI: false
    Role: null
  - Dtype: decimal(4,2)
    Field: DCHgb
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: DCDateTime
    PHI: true
    Role: '|ANCHOR_DATE||DISCHARGE_DATE||DATE|'
  - Dtype: varchar(25)
    Field: DCLName
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: DCFName
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: DCMName
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: DCNPI
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: DischargeStatus
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: DischargeComfort
    PHI: false
    Role: null
  - Dtype: varchar(75)
    Field: DischargeLocation
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CABGTransferDC
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CABGPlannedDC
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: DCHospice
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: DischargeCardRehab
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: DC_LOC
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: DeathProcedure
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: DeathCause
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: DC_MedReconCompleted
    PHI: false
    Role: null
  - Dtype: varchar(135)
    Field: DC_MedReconciled
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_ACE_Inhibitor_any
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: DC_ACEInhibitors_Any_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: DC_ACEInhibitors_Any_Rationale
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_Warfarin
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: DC_Warfarin_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: DC_Warfarin_Rationale
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_Aspirin_any
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: DC_ASPIRIN_MEDDOSE
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: DC_ASPIRIN_RATIONALE
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_Vorapaxar
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: DC_Vorapaxar_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: DC_Vorapaxar_Rationale
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_ARB_any
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: DC_ARB_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: DC_ARB_Rationale
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_Non_Statin_any
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: DC_Non_Statin_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: DC_Non_Statin_Rationale
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_Apixaban
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: DC_Apixaban_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: DC_Apixaban_Rationale
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_Dabigatran
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: DC_Dabigatran_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: DC_Dabigatran_Rationale
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_Edoxaban
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: DC_Edoxaban_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: DC_Edoxaban_Rationale
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_Prasugrel
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: DC_Prasugrel_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: DC_Prasugrel_Rationale
    PHI: false
    Role: null
  - Dtype: varchar(43)
    Field: Discharge_Ticagrelor
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: DC_Ticagrelor_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: DC_Ticagrelor_Rationale
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_Ticlopidine
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: DC_Ticlopidine_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: DC_Ticlopidine_Rationale
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_Alirocumab
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: DC_Alirocumab_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: DC_Alirocumab_Rationale
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_Evolocumab
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: DC_Evolocumab_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: DC_Evolocumab_Rationale
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: DischargeStatinAny
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: DC_Statin_Any_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: DC_Statin_Any_Rationale
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_Clopidogrel
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: DC_Clopidogrel_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: DC_Clopidogrel_Rationale
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: DischargeBetaBlocker
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: DC_BetaBlocker_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: DC_BetaBlocker_Rationale
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: Discharge_Rivaroxaban
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: DC_Rivaroxaban_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: DC_Rivaroxaban_Rationale
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: DataVrsn
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: ProcedureStartDateTime
    PHI: true
    Role: PROCEDURE_DATE
  - Dtype: varchar(30)
    Field: ArrivalDate
    PHI: true
    Role: ARRIVAL_DATE
  - Dtype: varchar(30)
    Field: DischargeDate
    PHI: true
    Role: DISCHARGE_DATE
  - Dtype: varchar(20)
    Field: OriginalOtherId
    PHI: true
    Role: MEDRECNUM
  - Dtype: varchar(16)
    Field: HospName
    PHI: false
    Role: HOSPITAL
  - Dtype: text
    Field: FileName
    PHI: false
    Role: null
  - Dtype: varchar(22)
    Field: Version
    PHI: false
    Role: null
  - Dtype: varchar(16)
    Field: DatasetName
    PHI: false
    Role: null
  - Dtype: int(11)
    Field: ClientFileId
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: BiomeImportDT
    PHI: false
    Role: null
  - Dtype: int(11)
    Field: YearMonth
    PHI: false
    Role: ANCHOR_YEAR_MONTH
  - Dtype: int(11)
    Field: CaseSequenceNumber
    PHI: false
    Role: CASE_NUMBER
  - Dtype: decimal(18,6)
    Field: Age
    PHI: true
    Role: AGE
  - Dtype: decimal(18,6)
    Field: DistFromHospital
    PHI: false
    Role: DISTANCE_FROM_HOSPITAL
  - Dtype: varchar(25)
    Field: Pericardial_Disease
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: Procedure_HeparinDerivative
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: EvalExerciseClearance
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PreProcSacubitrilValsartan
    PHI: false
    Role: null
  - Dtype: bigint(20) unsigned
    Field: BiomeEncounterId
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: tenantId
    PHI: false
    Role: TENANT
  - Dtype: varchar(25)
    Field: careEntityId
    PHI: false
    Role: CAREENTITY
custom_fields:
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin Converting Enzyme Inhibitor
    Derivative Value Code: 41549009
    Exact Match: 1
    Field: Discharge_ACE_Inhibitor_any
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin Converting Enzyme Inhibitor
    Derivative Value Code: 41549009
    Exact Match: 1
    Field: DC_ACEInhibitors_Any_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin Converting Enzyme Inhibitor
    Derivative Value Code: 41549009
    Exact Match: 1
    Field: DC_ACEInhibitors_Any_Rationale
    Reference Field: DC_PtRationale
    Reference Field Code: 100013080
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Warfarin
    Derivative Value Code: 11289
    Exact Match: 1
    Field: Discharge_Warfarin
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Warfarin
    Derivative Value Code: 11289
    Exact Match: 1
    Field: DC_Warfarin_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Warfarin
    Derivative Value Code: 11289
    Exact Match: 1
    Field: DC_Warfarin_Rationale
    Reference Field: DC_PtRationale
    Reference Field Code: 100013080
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aspirin
    Derivative Value Code: 1191
    Exact Match: 1
    Field: Discharge_Aspirin_any
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aspirin
    Derivative Value Code: 1191
    Exact Match: 1
    Field: DC_Aspirin_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aspirin
    Derivative Value Code: 1191
    Exact Match: 1
    Field: DC_Aspirin_Rationale
    Reference Field: DC_PtRationale
    Reference Field Code: 100013080
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Vorapaxar
    Derivative Value Code: 1537034
    Exact Match: 1
    Field: Discharge_Vorapaxar
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Vorapaxar
    Derivative Value Code: 1537034
    Exact Match: 1
    Field: DC_Vorapaxar_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Vorapaxar
    Derivative Value Code: 1537034
    Exact Match: 1
    Field: DC_Vorapaxar_Rationale
    Reference Field: DC_PtRationale
    Reference Field Code: 100013080
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin II Receptor Blocker
    Derivative Value Code: 372913009
    Exact Match: 1
    Field: Discharge_ARB_any
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin II Receptor Blocker
    Derivative Value Code: 372913009
    Exact Match: 1
    Field: DC_ARB_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin II Receptor Blocker
    Derivative Value Code: 372913009
    Exact Match: 1
    Field: DC_ARB_Rationale
    Reference Field: DC_PtRationale
    Reference Field Code: 100013080
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Non-Statin
    Derivative Value Code: 100014161
    Exact Match: 1
    Field: Discharge_Non_Statin_any
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Non-Statin
    Derivative Value Code: 100014161
    Exact Match: 1
    Field: DC_Non_Statin_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Non-Statin
    Derivative Value Code: 100014161
    Exact Match: 1
    Field: DC_Non_Statin_Rationale
    Reference Field: DC_PtRationale
    Reference Field Code: 100013080
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Apixaban
    Derivative Value Code: 1364430
    Exact Match: 1
    Field: Discharge_Apixaban
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Apixaban
    Derivative Value Code: 1364430
    Exact Match: 1
    Field: DC_Apixaban_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Apixaban
    Derivative Value Code: 1364430
    Exact Match: 1
    Field: DC_Apixaban_Rationale
    Reference Field: DC_PtRationale
    Reference Field Code: 100013080
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Dabigatran
    Derivative Value Code: 1546356
    Exact Match: 1
    Field: Discharge_Dabigatran
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Dabigatran
    Derivative Value Code: 1546356
    Exact Match: 1
    Field: DC_Dabigatran_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Dabigatran
    Derivative Value Code: 1546356
    Exact Match: 1
    Field: DC_Dabigatran_Rationale
    Reference Field: DC_PtRationale
    Reference Field Code: 100013080
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Edoxaban
    Derivative Value Code: 1599538
    Exact Match: 1
    Field: Discharge_Edoxaban
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Edoxaban
    Derivative Value Code: 1599538
    Exact Match: 1
    Field: DC_Edoxaban_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Edoxaban
    Derivative Value Code: 1599538
    Exact Match: 1
    Field: DC_Edoxaban_Rationale
    Reference Field: DC_PtRationale
    Reference Field Code: 100013080
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Prasugrel
    Derivative Value Code: 613391
    Exact Match: 1
    Field: Discharge_Prasugrel
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Prasugrel
    Derivative Value Code: 613391
    Exact Match: 1
    Field: DC_Prasugrel_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Prasugrel
    Derivative Value Code: 613391
    Exact Match: 1
    Field: DC_Prasugrel_Rationale
    Reference Field: DC_PtRationale
    Reference Field Code: 100013080
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Ticagrelor
    Derivative Value Code: 1116632
    Exact Match: 1
    Field: Discharge_Ticagrelor
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Ticagrelor
    Derivative Value Code: 1116632
    Exact Match: 1
    Field: DC_Ticagrelor_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Ticagrelor
    Derivative Value Code: 1116632
    Exact Match: 1
    Field: DC_Ticagrelor_Rationale
    Reference Field: DC_PtRationale
    Reference Field Code: 100013080
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Ticlopidine
    Derivative Value Code: 10594
    Exact Match: 1
    Field: Discharge_Ticlopidine
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Ticlopidine
    Derivative Value Code: 10594
    Exact Match: 1
    Field: DC_Ticlopidine_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Ticlopidine
    Derivative Value Code: 10594
    Exact Match: 1
    Field: DC_Ticlopidine_Rationale
    Reference Field: DC_PtRationale
    Reference Field Code: 100013080
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Alirocumab
    Derivative Value Code: 1659152
    Exact Match: 1
    Field: Discharge_Alirocumab
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Alirocumab
    Derivative Value Code: 1659152
    Exact Match: 1
    Field: DC_Alirocumab_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Alirocumab
    Derivative Value Code: 1659152
    Exact Match: 1
    Field: DC_Alirocumab_Rationale
    Reference Field: DC_PtRationale
    Reference Field Code: 100013080
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Evolocumab
    Derivative Value Code: 1665684
    Exact Match: 1
    Field: Discharge_Evolocumab
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Evolocumab
    Derivative Value Code: 1665684
    Exact Match: 1
    Field: DC_Evolocumab_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Evolocumab
    Derivative Value Code: 1665684
    Exact Match: 1
    Field: DC_Evolocumab_Rationale
    Reference Field: DC_PtRationale
    Reference Field Code: 100013080
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Statin
    Derivative Value Code: 96302009
    Exact Match: 1
    Field: DischargeStatinAny
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Statin
    Derivative Value Code: 96302009
    Exact Match: 1
    Field: DC_Statin_Any_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Statin
    Derivative Value Code: 96302009
    Exact Match: 1
    Field: DC_Statin_Any_Rationale
    Reference Field: DC_PtRationale
    Reference Field Code: 100013080
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Clopidogrel
    Derivative Value Code: 32968
    Exact Match: 1
    Field: Discharge_Clopidogrel
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Clopidogrel
    Derivative Value Code: 32968
    Exact Match: 1
    Field: DC_Clopidogrel_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Clopidogrel
    Derivative Value Code: 32968
    Exact Match: 1
    Field: DC_Clopidogrel_Rationale
    Reference Field: DC_PtRationale
    Reference Field Code: 100013080
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Beta Blocker
    Derivative Value Code: 33252009
    Exact Match: 1
    Field: DischargeBetaBlocker
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Beta Blocker
    Derivative Value Code: 33252009
    Exact Match: 1
    Field: DC_BetaBlocker_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Beta Blocker
    Derivative Value Code: 33252009
    Exact Match: 1
    Field: DC_BetaBlocker_Rationale
    Reference Field: DC_PtRationale
    Reference Field Code: 100013080
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Rivaroxaban
    Derivative Value Code: 1114195
    Exact Match: 1
    Field: Discharge_Rivaroxaban
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Rivaroxaban
    Derivative Value Code: 1114195
    Exact Match: 1
    Field: DC_Rivaroxaban_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Rivaroxaban
    Derivative Value Code: 1114195
    Exact Match: 1
    Field: DC_Rivaroxaban_Rationale
    Reference Field: DC_PtRationale
    Reference Field Code: 100013080
    Section: DISCHMED
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Access Site
    Derivative Value Code: 1000142440
    Exact Match: 1
    Field: BleedingAccessSite
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Access Site
    Derivative Value Code: 1000142440
    Exact Match: 1
    Field: BleedingAccessSiteDateTime
    Reference Field: PostProcDateTime
    Reference Field Code: 10001424780
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Gastrointestinal
    Derivative Value Code: 74474003
    Exact Match: 1
    Field: BleedingGastrointestinal
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Gastrointestinal
    Derivative Value Code: 74474003
    Exact Match: 1
    Field: BleedingGastrointestinalDateTime
    Reference Field: PostProcDateTime
    Reference Field Code: 10001424780
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Genitourinary
    Derivative Value Code: 417941003
    Exact Match: 1
    Field: BleedingGenitourinary
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Genitourinary
    Derivative Value Code: 417941003
    Exact Match: 1
    Field: BleedingGenitourinaryDateTime
    Reference Field: PostProcDateTime
    Reference Field Code: 10001424780
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Hematoma at Access Site
    Derivative Value Code: 385494008
    Exact Match: 1
    Field: BleedingHematomaAccessSite
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Hematoma at Access Site
    Derivative Value Code: 385494008
    Exact Match: 1
    Field: BleedingHematomaASDateTime
    Reference Field: PostProcDateTime
    Reference Field Code: 10001424780
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Other
    Derivative Value Code: 1000142371
    Exact Match: 1
    Field: BleedingOther
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Other
    Derivative Value Code: 1000142371
    Exact Match: 1
    Field: BleedingOtherDateTime
    Reference Field: PostProcDateTime
    Reference Field Code: 10001424780
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Retroperitoneal
    Derivative Value Code: 95549001
    Exact Match: 1
    Field: BleedingRetroperitoneal
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Retroperitoneal
    Derivative Value Code: 95549001
    Exact Match: 1
    Field: BleedingRetroDateTime
    Reference Field: PostProcDateTime
    Reference Field Code: 10001424780
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Cardiac Arrest
    Derivative Value Code: 410429000
    Exact Match: 1
    Field: CardiacArrest
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Cardiac Arrest
    Derivative Value Code: 410429000
    Exact Match: 1
    Field: CardiacArrestDateTime
    Reference Field: PostProcDateTime
    Reference Field Code: 10001424780
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Cardiac Tamponade
    Derivative Value Code: 35304003
    Exact Match: 1
    Field: CardiacTamponade
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Cardiac Tamponade
    Derivative Value Code: 35304003
    Exact Match: 1
    Field: CardiacTamponadeDateTime
    Reference Field: PostProcDateTime
    Reference Field Code: 10001424780
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Cardiogenic Shock
    Derivative Value Code: 89138009
    Exact Match: 1
    Field: CardiogenicShock
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Cardiogenic Shock
    Derivative Value Code: 89138009
    Exact Match: 1
    Field: CardiogenicShockDateTime
    Reference Field: PostProcDateTime
    Reference Field Code: 10001424780
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Heart Failure
    Derivative Value Code: 84114007
    Exact Match: 1
    Field: HeartFailure
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Heart Failure
    Derivative Value Code: 84114007
    Exact Match: 1
    Field: HeartFailureDateTime
    Reference Field: PostProcDateTime
    Reference Field Code: 10001424780
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Myocardial Infarction
    Derivative Value Code: 22298006
    Exact Match: 1
    Field: MyocardialInfarction
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Myocardial Infarction
    Derivative Value Code: 22298006
    Exact Match: 1
    Field: MyocardialInfarctionDateTime
    Reference Field: PostProcDateTime
    Reference Field Code: 10001424780
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: New Requirement for Dialysis
    Derivative Value Code: 100014076
    Exact Match: 1
    Field: NewRequirementforDialysis
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: New Requirement for Dialysis
    Derivative Value Code: 100014076
    Exact Match: 1
    Field: DialysisDateTime
    Reference Field: PostProcDateTime
    Reference Field Code: 10001424780
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Other Vascular Complications Requiring Treatment
    Derivative Value Code: 1000142419
    Exact Match: 1
    Field: OtherVasCompReqTreatment
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Other Vascular Complications Requiring Treatment
    Derivative Value Code: 1000142419
    Exact Match: 1
    Field: OtherVasCompReqTreatmentDateTime
    Reference Field: PostProcDateTime
    Reference Field Code: 10001424780
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Stroke - Hemorrhagic
    Derivative Value Code: 230706003
    Exact Match: 1
    Field: StrokeHemorrhagic
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Stroke - Hemorrhagic
    Derivative Value Code: 230706003
    Exact Match: 1
    Field: StrokeHemorrhagicDateTime
    Reference Field: PostProcDateTime
    Reference Field Code: 10001424780
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Stroke - Ischemic
    Derivative Value Code: 422504002
    Exact Match: 1
    Field: StrokeIschemic
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Stroke - Ischemic
    Derivative Value Code: 422504002
    Exact Match: 1
    Field: StrokeIschemicDateTime
    Reference Field: PostProcDateTime
    Reference Field Code: 10001424780
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Stroke - Undetermined
    Derivative Value Code: 230713003
    Exact Match: 1
    Field: StrokeUndetermined
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Stroke - Undetermined
    Derivative Value Code: 230713003
    Exact Match: 1
    Field: StrokeUndeterminedDateTime
    Reference Field: PostProcDateTime
    Reference Field Code: 10001424780
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PreProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Antiarrhythmic Agent Other
    Derivative Value Code: 100014162
    Exact Match: 1
    Field: PreProcAntiarrhythmicAgentOther
    Reference Field: PreProcMedAdmin
    Reference Field Code: 432102000
    Section: PREPROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: PreProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Proprotein Convertase Subtilisin Kexin Type 9 Inhibitor
    Derivative Value Code: 112000000694
    Exact Match: 1
    Field: PreProcPCSK9
    Reference Field: PreProcMedAdmin
    Reference Field Code: 432102000
    Section: PREPROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: PreProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Aspirin
    Derivative Value Code: 1191
    Exact Match: 1
    Field: PreProcASA
    Reference Field: PreProcMedAdmin
    Reference Field Code: 432102000
    Section: PREPROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: PreProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Beta Blocker
    Derivative Value Code: 33252009
    Exact Match: 1
    Field: PreProcBetaBlocker
    Reference Field: PreProcMedAdmin
    Reference Field Code: 432102000
    Section: PREPROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: PreProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Calcium Channel Blocking Agent
    Derivative Value Code: 48698004
    Exact Match: 1
    Field: PreProcCalciumChannelBlocking
    Reference Field: PreProcMedAdmin
    Reference Field Code: 432102000
    Section: PREPROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: PreProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Long Acting Nitrate
    Derivative Value Code: 31970009
    Exact Match: 1
    Field: PreProcLongActingNitrate
    Reference Field: PreProcMedAdmin
    Reference Field Code: 432102000
    Section: PREPROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: PreProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Non-Statin
    Derivative Value Code: 100014161
    Exact Match: 1
    Field: PreProcNonStatin
    Reference Field: PreProcMedAdmin
    Reference Field Code: 432102000
    Section: PREPROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: PreProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Ranolazine
    Derivative Value Code: 35829
    Exact Match: 1
    Field: PreProcRanolazine
    Reference Field: PreProcMedAdmin
    Reference Field Code: 432102000
    Section: PREPROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: PreProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Statin
    Derivative Value Code: 96302009
    Exact Match: 1
    Field: PreProcStatinAny
    Reference Field: PreProcMedAdmin
    Reference Field Code: 432102000
    Section: PREPROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: PreProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Sacubitril and Valsartan
    Derivative Value Code: 1656341
    Exact Match: 1
    Field: PreProcSacubitrilValsartan
    Reference Field: PreProcMedAdmin
    Reference Field Code: 432102000
    Section: PREPROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Bivalirudin
    Derivative Value Code: 400610005
    Exact Match: 1
    Field: Procedure_Bivalirudin
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Fondaparinux
    Derivative Value Code: 321208
    Exact Match: 1
    Field: Procedure_Fondaparinux
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Heparin Derivative
    Derivative Value Code: 100000921
    Exact Match: 1
    Field: Procedure_HeparinDerivative
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Low Molecular Weight Heparin
    Derivative Value Code: 373294004
    Exact Match: 1
    Field: Procedure_Low_Molecular_Weight_Heparin_any
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Unfractionated Heparin
    Derivative Value Code: 96382006
    Exact Match: 1
    Field: Procedure_Unfractionated_Heparin_any
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Glycoprotein IIb IIIa Inhibitors
    Derivative Value Code: 1000142427
    Exact Match: 1
    Field: Procedure_GPInhibitors_Any
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Apixaban
    Derivative Value Code: 1364430
    Exact Match: 1
    Field: Procedure_Apixaban
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Dabigatran
    Derivative Value Code: 1546356
    Exact Match: 1
    Field: Procedure_Dabigatran
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Edoxaban
    Derivative Value Code: 1599538
    Exact Match: 1
    Field: Procedure_Edoxaban
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Rivaroxaban
    Derivative Value Code: 1114195
    Exact Match: 1
    Field: Procedure_Rivaroxaban
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Cangrelor
    Derivative Value Code: 1656052
    Exact Match: 1
    Field: Procedure_Cangrelor
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Clopidogrel
    Derivative Value Code: 32968
    Exact Match: 1
    Field: Procedure_Clopidogrel
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Prasugrel
    Derivative Value Code: 613391
    Exact Match: 1
    Field: Procedure_Prasugrel
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Ticagrelor
    Derivative Value Code: 1116632
    Exact Match: 1
    Field: Procedure_Ticagrelor
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Vorapaxar
    Derivative Value Code: 1537034
    Exact Match: 1
    Field: Procedure_Vorapaxar
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Warfarin
    Derivative Value Code: 11289
    Exact Match: 1
    Field: Procedure_Warfarin
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcMedID
    Derivative Field Code: 100013057
    Derivative Value: Argatroban
    Derivative Value Code: 15202
    Exact Match: 1
    Field: Procedure_Argatroban
    Reference Field: ProcMedAdmin
    Reference Field Code: 432102000
    Section: PROCMED
    Value Map: null
  - Delimiter: null
    Derivative Field: CathLabVisitIndication
    Derivative Field Code: 100014000
    Derivative Value: Cardiomyopathy
    Derivative Value Code: 85898001
    Exact Match: 0
    Field: CardioLVSD
    Reference Field: CathLabVisitIndication
    Reference Field Code: 100014000
    Section: PROCINFO
    Value Map:
      Cardiomyopathy: 'Yes'
  - Delimiter: null
    Derivative Field: CathLabVisitIndication
    Derivative Field Code: 100014000
    Derivative Value: LV Dysfunction
    Derivative Value Code: 134401001
    Exact Match: 0
    Field: LV_Dysfunction
    Reference Field: CathLabVisitIndication
    Reference Field Code: 100014000
    Section: PROCINFO
    Value Map:
      LV Dysfunction: 'Yes'
  - Delimiter: null
    Derivative Field: CathLabVisitIndication
    Derivative Field Code: 100014000
    Derivative Value: Stable Known CAD
    Derivative Value Code: 100014001
    Exact Match: 0
    Field: Stable_Known_CAD
    Reference Field: CathLabVisitIndication
    Reference Field Code: 100014000
    Section: PROCINFO
    Value Map:
      Stable Known CAD: 'Yes'
  - Delimiter: null
    Derivative Field: CathLabVisitIndication
    Derivative Field Code: 100014000
    Derivative Value: Worsening Angina
    Derivative Value Code: 10001424790
    Exact Match: 0
    Field: Worsening_Angina
    Reference Field: CathLabVisitIndication
    Reference Field Code: 100014000
    Section: PROCINFO
    Value Map:
      Worsening Angina: 'Yes'
  - Delimiter: null
    Derivative Field: CathLabVisitIndication
    Derivative Field Code: 100014000
    Derivative Value: Cardiac arrhythmia
    Derivative Value Code: 698247007
    Exact Match: 0
    Field: Cardiac_Arrhythmia
    Reference Field: CathLabVisitIndication
    Reference Field Code: 100014000
    Section: PROCINFO
    Value Map:
      Cardiac arrhythmia: 'Yes'
  - Delimiter: null
    Derivative Field: CathLabVisitIndication
    Derivative Field Code: 100014000
    Derivative Value: Suspected CAD
    Derivative Value Code: 100014003
    Exact Match: 0
    Field: Suspected_CAD
    Reference Field: CathLabVisitIndication
    Reference Field Code: 100014000
    Section: PROCINFO
    Value Map:
      Suspected CAD: 'Yes'
  - Delimiter: null
    Derivative Field: CathLabVisitIndication
    Derivative Field Code: 100014000
    Derivative Value: Valvular Disease
    Derivative Value Code: 368009
    Exact Match: 0
    Field: Valvular_Disease
    Reference Field: CathLabVisitIndication
    Reference Field Code: 100014000
    Section: PROCINFO
    Value Map:
      Valvular Disease: 'Yes'
  - Delimiter: null
    Derivative Field: CathLabVisitIndication
    Derivative Field Code: 100014000
    Derivative Value: Resuscitated Cardiac Arrest
    Derivative Value Code: 233927002
    Exact Match: 0
    Field: Resuscitated_Cardiac_Arrest
    Reference Field: CathLabVisitIndication
    Reference Field Code: 100014000
    Section: PROCINFO
    Value Map:
      Resuscitated Cardiac Arrest: 'Yes'
  - Delimiter: null
    Derivative Field: CathLabVisitIndication
    Derivative Field Code: 100014000
    Derivative Value: Pre-operative Evaluation
    Derivative Value Code: 1000142360
    Exact Match: 0
    Field: PeriopEval
    Reference Field: CathLabVisitIndication
    Reference Field Code: 100014000
    Section: PROCINFO
    Value Map:
      Pre-operative Evaluation: 'Yes'
  - Delimiter: null
    Derivative Field: CathLabVisitIndication
    Derivative Field Code: 100014000
    Derivative Value: Evaluation for Exercise Clearance
    Derivative Value Code: 10001424791
    Exact Match: 0
    Field: EvalExerciseClearance
    Reference Field: CathLabVisitIndication
    Reference Field Code: 100014000
    Section: PROCINFO
    Value Map:
      Evaluation for Exercise Clearance: 'Yes'
  - Delimiter: null
    Derivative Field: CathLabVisitIndication
    Derivative Field Code: 100014000
    Derivative Value: Syncope
    Derivative Value Code: 271594007
    Exact Match: 0
    Field: Syncope
    Reference Field: CathLabVisitIndication
    Reference Field Code: 100014000
    Section: PROCINFO
    Value Map:
      Syncope: 'Yes'
  - Delimiter: null
    Derivative Field: CathLabVisitIndication
    Derivative Field Code: 100014000
    Derivative Value: Post Cardiac Transplant
    Derivative Value Code: 100014002
    Exact Match: 0
    Field: PostCardiacTransplant
    Reference Field: CathLabVisitIndication
    Reference Field Code: 100014000
    Section: PROCINFO
    Value Map:
      Post Cardiac Transplant: 'Yes'
  - Delimiter: null
    Derivative Field: CathLabVisitIndication
    Derivative Field Code: 100014000
    Derivative Value: Pericardial Disease
    Derivative Value Code: 100014002
    Exact Match: 0
    Field: Pericardial_Disease
    Reference Field: CathLabVisitIndication
    Reference Field Code: 100014000
    Section: PROCINFO
    Value Map:
      Pericardial Disease: 'Yes'
  - Delimiter: null
    Derivative Field: CathLabVisitIndication
    Derivative Field Code: 100014000
    Derivative Value: ACS > 24 hrs
    Derivative Value Code: 1000142359
    Exact Match: 0
    Field: ACS_gt_24_hrs
    Reference Field: CathLabVisitIndication
    Reference Field Code: 100014000
    Section: PROCINFO
    Value Map:
      ACS > 24 hrs: 'Yes'
  - Delimiter: null
    Derivative Field: CathLabVisitIndication
    Derivative Field Code: 100014000
    Derivative Value: ACS <= 24 hrs
    Derivative Value Code: 1000142358
    Exact Match: 0
    Field: ACS_lte_24_hrs
    Reference Field: CathLabVisitIndication
    Reference Field Code: 100014000
    Section: PROCINFO
    Value Map:
      ACS <= 24 hrs: 'Yes'
  - Delimiter: null
    Derivative Field: CathLabVisitIndication
    Derivative Field Code: 100014000
    Derivative Value: New Onset Angina <= 2 months
    Derivative Value Code: 233821000
    Exact Match: 0
    Field: New_Onset_Angina_lte_2months
    Reference Field: CathLabVisitIndication
    Reference Field Code: 100014000
    Section: PROCINFO
    Value Map:
      New Onset Angina <= 2 months: 'Yes'
  - Delimiter: null
    Derivative Field: CathLabVisitIndication
    Derivative Field Code: 100014000
    Derivative Value: Other
    Derivative Value Code: 100000351
    Exact Match: 0
    Field: Other
    Reference Field: CathLabVisitIndication
    Reference Field Code: 100014000
    Section: PROCINFO
    Value Map:
      Other: 'Yes'
  - Delimiter: null
    Derivative Field: StressTestType
    Derivative Field Code: 1000142432
    Derivative Value: Exercise Stress Test (w/o imaging)
    Derivative Value Code: 18752-6
    Exact Match: 1
    Field: ExerciseStressTest
    Reference Field: StressTestType
    Reference Field Code: 1000142432
    Section: STRESSTEST
    Value Map:
      Exercise Stress Test (w/o imaging): 'Yes'
  - Delimiter: null
    Derivative Field: StressTestType
    Derivative Field Code: 1000142432
    Derivative Value: Exercise Stress Test (w/o imaging)
    Derivative Value Code: 18752-6
    Exact Match: 1
    Field: ExerciseTestResult
    Reference Field: StressTestResult
    Reference Field Code: 10001424303
    Section: STRESSTEST
    Value Map: null
  - Delimiter: null
    Derivative Field: StressTestType
    Derivative Field Code: 1000142432
    Derivative Value: Exercise Stress Test (w/o imaging)
    Derivative Value Code: 18752-6
    Exact Match: 1
    Field: ExerciseTestResultDate
    Reference Field: StressTestDate
    Reference Field Code: 1000142431
    Section: STRESSTEST
    Value Map: null
  - Delimiter: null
    Derivative Field: StressTestType
    Derivative Field Code: 1000142432
    Derivative Value: Exercise Stress Test (w/o imaging)
    Derivative Value Code: 18752-6
    Exact Match: 1
    Field: ExerciseTestRisk
    Reference Field: StressTestRisk
    Reference Field Code: 1000142434
    Section: STRESSTEST
    Value Map: null
  - Delimiter: null
    Derivative Field: StressTestType
    Derivative Field Code: 1000142432
    Derivative Value: Stress Echocardiogram
    Derivative Value Code: 18107-3
    Exact Match: 1
    Field: StressEcho
    Reference Field: StressTestType
    Reference Field Code: 1000142432
    Section: STRESSTEST
    Value Map:
      Stress Echocardiogram: 'Yes'
  - Delimiter: null
    Derivative Field: StressTestType
    Derivative Field Code: 1000142432
    Derivative Value: Stress Echocardiogram
    Derivative Value Code: 18107-3
    Exact Match: 1
    Field: StressEchoResult
    Reference Field: StressTestResult
    Reference Field Code: 10001424303
    Section: STRESSTEST
    Value Map: null
  - Delimiter: null
    Derivative Field: StressTestType
    Derivative Field Code: 1000142432
    Derivative Value: Stress Echocardiogram
    Derivative Value Code: 18107-3
    Exact Match: 1
    Field: StressEchoResultDate
    Reference Field: StressTestDate
    Reference Field Code: 1000142431
    Section: STRESSTEST
    Value Map: null
  - Delimiter: null
    Derivative Field: StressTestType
    Derivative Field Code: 1000142432
    Derivative Value: Stress Echocardiogram
    Derivative Value Code: 18107-3
    Exact Match: 1
    Field: StressEchoRisk
    Reference Field: StressTestRisk
    Reference Field Code: 1000142434
    Section: STRESSTEST
    Value Map: null
  - Delimiter: null
    Derivative Field: StressTestType
    Derivative Field Code: 1000142432
    Derivative Value: Stress Nuclear
    Derivative Value Code: 49569-7
    Exact Match: 1
    Field: StressNuclear
    Reference Field: StressTestType
    Reference Field Code: 1000142432
    Section: STRESSTEST
    Value Map:
      Stress Nuclear: 'Yes'
  - Delimiter: null
    Derivative Field: StressTestType
    Derivative Field Code: 1000142432
    Derivative Value: Stress Nuclear
    Derivative Value Code: 49569-7
    Exact Match: 1
    Field: StressNuclearResult
    Reference Field: StressTestResult
    Reference Field Code: 10001424303
    Section: STRESSTEST
    Value Map: null
  - Delimiter: null
    Derivative Field: StressTestType
    Derivative Field Code: 1000142432
    Derivative Value: Stress Nuclear
    Derivative Value Code: 49569-7
    Exact Match: 1
    Field: StressNuclearResultDate
    Reference Field: StressTestDate
    Reference Field Code: 1000142431
    Section: STRESSTEST
    Value Map: null
  - Delimiter: null
    Derivative Field: StressTestType
    Derivative Field Code: 1000142432
    Derivative Value: Stress Nuclear
    Derivative Value Code: 49569-7
    Exact Match: 1
    Field: StressNuclearRisk
    Reference Field: StressTestRisk
    Reference Field Code: 1000142434
    Section: STRESSTEST
    Value Map: null
  - Delimiter: null
    Derivative Field: StressTestType
    Derivative Field Code: 1000142432
    Derivative Value: Stress Imaging with CMR
    Derivative Value Code: 58750-1
    Exact Match: 1
    Field: CMRStressTest
    Reference Field: StressTestType
    Reference Field Code: 1000142432
    Section: STRESSTEST
    Value Map:
      Stress Imaging with CMR: 'Yes'
  - Delimiter: null
    Derivative Field: StressTestType
    Derivative Field Code: 1000142432
    Derivative Value: Stress Imaging with CMR
    Derivative Value Code: 58750-1
    Exact Match: 1
    Field: CMRStressResults
    Reference Field: StressTestResult
    Reference Field Code: 10001424303
    Section: STRESSTEST
    Value Map: null
  - Delimiter: null
    Derivative Field: StressTestType
    Derivative Field Code: 1000142432
    Derivative Value: Stress Imaging with CMR
    Derivative Value Code: 58750-1
    Exact Match: 1
    Field: CMRStressTestResultDate
    Reference Field: StressTestDate
    Reference Field Code: 1000142431
    Section: STRESSTEST
    Value Map: null
  - Delimiter: null
    Derivative Field: StressTestType
    Derivative Field Code: 1000142432
    Derivative Value: Stress Imaging with CMR
    Derivative Value Code: 58750-1
    Exact Match: 1
    Field: CMRStressRisk
    Reference Field: StressTestRisk
    Reference Field Code: 1000142434
    Section: STRESSTEST
    Value Map: null
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Medicare
    Derivative Value Code: 1
    Exact Match: 0
    Field: Medicare
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Medicare|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Medicaid
    Derivative Value Code: 2
    Exact Match: 0
    Field: Medicaid
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Medicaid|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Private Health Insurance
    Derivative Value Code: 5
    Exact Match: 0
    Field: PrivateHealthInsurance
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Private Health Insurance|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Military Health Care
    Derivative Value Code: 31
    Exact Match: 0
    Field: MiltaryHealthCare
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Military Health Care|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: State-Specific Plan (non-Medicaid)
    Derivative Value Code: 36
    Exact Match: 0
    Field: StateSpecificPlan
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|State-Specific Plan (non-Medicaid)|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Indian Health Service
    Derivative Value Code: 33
    Exact Match: 0
    Field: IndianHealthService
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Indian Health Service|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Non-US Insurance
    Derivative Value Code: '100000812'
    Exact Match: 0
    Field: NonUSInsurance
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Non-US Insurance|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HospInterventionType
    Derivative Field Code: 100001284
    Derivative Value: '|CABG|'
    Derivative Value Code: '232717009'
    Exact Match: 0
    Field: CABG
    Reference Field: HospInterventionType
    Reference Field Code: 100001284
    Section: EPISODEOFCARE
    Value Map:
      '|CABG|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HospInterventionType
    Derivative Field Code: 100001284
    Derivative Value: '|Valvular Intervention|'
    Derivative Value Code: '100014071'
    Exact Match: 0
    Field: Valvular_Intervention
    Reference Field: HospInterventionType
    Reference Field Code: 100001284
    Section: EPISODEOFCARE
    Value Map:
      '|Valvular Intervention|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HospInterventionType
    Derivative Field Code: 100001284
    Derivative Value: '|Cardiac Surgery (non CABG)|'
    Derivative Value Code: '100014068'
    Exact Match: 0
    Field: Cardiac_Surgery_non_CABG
    Reference Field: HospInterventionType
    Reference Field Code: 100001284
    Section: EPISODEOFCARE
    Value Map:
      '|Cardiac Surgery (non CABG)|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HospInterventionType
    Derivative Field Code: 100001284
    Derivative Value: '|Structural Heart Intervention (non-valvular)|'
    Derivative Value Code: '100014072'
    Exact Match: 0
    Field: Structural_Heart_Intervention_non_valvular
    Reference Field: HospInterventionType
    Reference Field Code: 100001284
    Section: EPISODEOFCARE
    Value Map:
      '|Structural Heart Intervention (non-valvular)|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HospInterventionType
    Derivative Field Code: 100001284
    Derivative Value: '|Surgery (Non Cardiac)|'
    Derivative Value Code: '100014022'
    Exact Match: 0
    Field: Non_Cardiac_Surgery
    Reference Field: HospInterventionType
    Reference Field Code: 100001284
    Section: EPISODEOFCARE
    Value Map:
      '|Surgery (Non Cardiac)|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HospInterventionType
    Derivative Field Code: 100001284
    Derivative Value: '|EP Study|'
    Derivative Value Code: '252425004'
    Exact Match: 0
    Field: EP_Study
    Reference Field: HospInterventionType
    Reference Field Code: 100001284
    Section: EPISODEOFCARE
    Value Map:
      '|EP Study|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HospInterventionType
    Derivative Field Code: 100001284
    Derivative Value: '|Other|'
    Derivative Value Code: '10001424811'
    Exact Match: 0
    Field: HospInterventionType_Other
    Reference Field: HospInterventionType
    Reference Field Code: 100001284
    Section: EPISODEOFCARE
    Value Map:
      '|Other|': 'Yes'
rename_fields:
  - New Field: RaceBlack
    Old Field: RaceBlackAfricanAmerican
  - New Field: RaceAmIndian
    Old Field: RaceAmericanIndianAlaskanNative
  - New Field: RaceAsianOther
    Old Field: RaceOtherAsian
  - New Field: RaceNatHaw
    Old Field: RaceNatHawPacificIslander
  - New Field: RaceNativeHawaii
    Old Field: RaceNatHaw
  - New Field: RaceGuamChamorro
    Old Field: RaceGuamanianOrChamorro
  - New Field: RacePacificIslandOther
    Old Field: RaceOtherPacificIslander
  - New Field: HispEthnicityMexican
    Old Field: HispanicEthnicityTypeMexicanMexicanAmericanChicano
  - New Field: HispEthnicityPuertoRico
    Old Field: HispanicEthnicityTypePuertoRican
  - New Field: HispEthnicityCuban
    Old Field: HispanicEthnicityTypeCuban
  - New Field: HispEthnicityOtherOrigin
    Old Field: HispanicEthnicityTypeOtherHisp
  - New Field: HxMI
    Old Field: PriorMI
  - New Field: HxMIDate
    Old Field: PriorMIDate
  - New Field: HxPCIDate
    Old Field: PriorPCIDate
  - New Field: PriorCABG
    Old Field: HxPriorCABG
  - New Field: PreOPEval
    Old Field: PreOPEvalForSurgType
  - New Field: AdmMName
    Old Field: AdmMidName
  - New Field: Sex
    Old Field: Gender
  - New Field: StudyName
    Old Field: StudyName
  - New Field: StudyPtID
    Old Field: StudyPatientID
  - New Field: SmokeAmount
    Old Field: SmokingAmount
  - New Field: DCathMName
    Old Field: DCathMidName
  - New Field: PCIMName
    Old Field: PCIMidName
  - New Field: NSVTType
    Old Field: NonSustainedVTType
  - New Field: NV_IFR
    Old Field: NV_IFR_NATIVE
  - New Field: NV_IVUS
    Old Field: IVUSNative
  - New Field: NV_FFR
    Old Field: FFRNATIVE
  - New Field: NV_OCT
    Old Field: NV_OCT_NATIVE
  - New Field: GRAFT_FFR
    Old Field: FFRGraft
  - New Field: Graft_IFR
    Old Field: GT_IFR
  - New Field: Graft_IVUS
    Old Field: IVUSGraft
  - New Field: Graft_OCT
    Old Field: GT_OCT
  - New Field: LOCProc
    Old Field: Proc_LOC
  - New Field: ThromTherapy
    Old Field: Thrombolytics
  - New Field: ThromDateTime
    Old Field: ThrombolyticTherapyDateTime
  - New Field: CABGTransfer
    Old Field: CABGTransferDC
  - New Field: ValvularDzStenosisType
    Old Field: ValvularDzStenosisType
  - New Field: ValvularDzStenosisSev
    Old Field: ValvularDzStenosisSev
  - New Field: RegurgSeverity
    Old Field: ValvularDzRegSev
  - New Field: EpisodeKey
    Old Field: EpisodeId
  - New Field: ZipCode
    Old Field: PatZip
  - New Field: ZipCodeNA
    Old Field: PatZipNA
  - New Field: DCStatus
    Old Field: DischargeStatus
  - New Field: DCLocation
    Old Field: DischargeLocation
  - New Field: DC_Comfort
    Old Field: DischargeComfort
  - New Field: DC_CardRehab
    Old Field: DischargeCardRehab
  - New Field: ICDevUDI
    Old Field: ICUniDevID
  - New Field: AttLName
    Old Field: AttnLName
  - New Field: AttFName
    Old Field: AttnFName
  - New Field: AttMName
    Old Field: AttnMidName
  - New Field: AttNPI
    Old Field: AttnNPI
delimiters:
  - Enclose: true
    Field: TobaccoType
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: ECGFindings
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: CardiacCTAResults
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: PriorDxAngioResults
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: CVInstabilityType
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: DC_MedReconciled
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: DC_ACEInhibitors_Any_Rationale
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: DC_Warfarin_Rationale
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: DC_Aspirin_Rationale
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: DC_Vorapaxar_Rationale
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: DC_ARB_Rationale
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: DC_Non_Statin_Rationale
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: DC_Apixaban_Rationale
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: DC_Dabigatran_Rationale
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: DC_Edoxaban_Rationale
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: DC_Prasugrel_Rationale
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: DC_Ticagrelor_Rationale
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: DC_Ticlopidine_Rationale
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: DC_Alirocumab_Rationale
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: DC_Evolocumab_Rationale
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: DC_Statin_Any_Rationale
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: DC_Clopidogrel_Rationale
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: DC_BetaBlocker_Rationale
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: DC_Rivaroxaban_Rationale
    Replace: null
    Delimiter: '|'
  - Enclose: false
    Field: SegmentID
    Replace: '|'
    Delimiter: ', '
  - Enclose: false
    Field: ConcomProcType
    Replace: '|'
    Delimiter: ', '
  - Enclose: true
    Field: NonSustainedVTType
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: HospInterventionType
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: OrganTransplantType
    Replace: null
    Delimiter: '|'
  - Enclose: false
    Field: ICDevCounterAssn
    Replace: '|'
    Delimiter: ', '
value_mapping:
- Field: dischargecardrehab
  Value Map:
    No - Medical Reason Documented: No -Medical Reason Documented
    No - Health Care System Reason Documented: No -Health Care System Reason Documented
  Replace Substr: 0
- Field: graftsegmentid
  Value Map:
    11a - Ostial LM: 11a - Ostial L
    11b-  Mid-LM: 11b- Mid-LM
    11c -  Distal LM: 11c - Distal LM
  Replace Substr: 1
- Field: hypothermiainducedtiming
  Value Map:
    Initiated Pre-PCI, <=  6 hrs post cardiac arrest: Initiated Pre-PCI, <= 6 hrs
      post cardiac arrest
    Initiated Pre-PCI,  >  6 hrs post cardiac arrest: Initiated Pre-PCI, > 6 hrs post
      cardiac arrest
  Replace Substr: 0
- Field: nvsegmentid
  Value Map:
    11b-  Mid-LM: 11b- Mid-LM
    11c -  Distal LM: 11c - Distal LM
  Replace Substr: 1
- Field: organtransplanttype
  Value Map:
    '|Other Organ|': '| Other Organ|'
  Replace Substr: 0
- Field: pciindication
  Value Map:
    CAD (without ischemic Sx): CAD (without Ischemic Sx)
    STEMI - Rescue (After unsuccessful lytics): STEMI - Rescue (after unsuccessful
      lytics)
  Replace Substr: 0
- Field: segmentid
  Value Map:
    11a - Ostial LM: 11a - Ostial L
    11b-  Mid-LM: 11b- Mid-LM
    11c -  Distal LM: 11c - Distal LM
  Replace Substr: 1
- Field: stenttype
  Value Map:
    BMS: Bare Metal Stent (BMS)
  Replace Substr: 0
- Field: valvulardzregsev
  Value Map:
    Severe (4+): Severe
    Moderate (2+): Moderate
    Mild (1+): Mild
    Moderately Severe (3+): Moderately Severe
  Replace Substr: 0
pivot_sections:
  - DISCHMED
  - IPPEVENTS
  - PREPROCMED
  - PROCMED
