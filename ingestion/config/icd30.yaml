version: '3.0'
biome_schema:
  - Dtype: decimal(6,3)
    Field: age
    PHI: true
    Role: AGE
  - Dtype: bigint unsigned
    Field: biomeencounterid
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: biomeimportdt
    PHI: false
    Role: null
  - Dtype: varchar(34)
    Field: careentityid
    PHI: false
    Role: CAREENTITY
  - Dtype: int
    Field: casesequencenumber
    PHI: false
    Role: CASE_NUMBER
  - Dtype: int
    Field: clientfileid
    PHI: false
    Role: null
  - Dtype: varchar(16)
    Field: datasetname
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: datavrsn
    PHI: false
    Role: null
  - Dtype: decimal(8,2)
    Field: distfromhospital
    PHI: false
    Role: DISTANCE_FROM_HOSPITAL
  - Dtype: varchar(90)
    Field: explantdevicename
    PHI: false
    Role: null
  - Dtype: varchar(68)
    Field: filename
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: hospname
    PHI: false
    Role: HOSPITAL
  - Dtype: varchar(100)
    Field: icdimpmodelname
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: leadmodelname
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: originalotherid
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: particid
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: procedureendtime
    PHI: false
    Role: TIME
  - Dtype: varchar(30)
    Field: proceduretime
    PHI: false
    Role: PROCEDURE_TIME|TIME
  - Dtype: varchar(34)
    Field: tenantid
    PHI: false
    Role: TENANT
  - Dtype: varchar(22)
    Field: version
    PHI: false
    Role: null
  - Dtype: int
    Field: yearmonth
    PHI: false
    Role: ANCHOR_YEAR_MONTH
  - Dtype: varchar(68)
    Field: partname
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: timeframe
    PHI: false
    Role: null
  - Dtype: varchar(57)
    Field: vendorid
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: registryid
    PHI: false
    Role: null
  - Dtype: text
    Field: lastname
    PHI: true
    Role: NAME
  - Dtype: text
    Field: firstname
    PHI: true
    Role: NAME
  - Dtype: text
    Field: midname
    PHI: true
    Role: NAME
  - Dtype: varchar(30)
    Field: ncdrpatientid
    PHI: true
    Role: IDENTIFIER
  - Dtype: varchar(30)
    Field: otherid
    PHI: true
    Role: MEDRECNUM
  - Dtype: varchar(69)
    Field: dob
    PHI: true
    Role: DOB
  - Dtype: varchar(10)
    Field: Gender
    PHI: true
    Role: GENDER
  - Dtype: varchar(9)
    Field: patzip
    PHI: true
    Role: PATIENT_ZIP
  - Dtype: varchar(5)
    Field: patzipna
    PHI: false
    Role: null
  - Dtype: varchar(53)
    Field: racewhite
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: raceblack
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: raceasian
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: raceamindian
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: racenathawpasislander
    PHI: false
    Role: null
  - Dtype: varchar(53)
    Field: hisporig
    PHI: false
    Role: null
  - Dtype: text
    Field: episodeid
    PHI: true
    Role: ANY_ID
  - Dtype: varchar(69)
    Field: arrivaldate
    PHI: true
    Role: ADMISSION_DATE
  - Dtype: varchar(10)
    Field: HealthInsurance
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: insihs
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: insmedicaid
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: insmedicare
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: insmedicareadvantage
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: insmilitary
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: insnonus
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: insprivate
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: insstate
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: enrolledstudy
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: studyname
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: RStudyPatientID
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: reasonforadmit
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: nyha
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: LVEFAssessed
    PHI: false
    Role: null
  - Dtype: varchar(69)
    Field: mstreclvefdate
    PHI: true
    Role: DATE
  - Dtype: int
    Field: LVEF
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: syndromerisktype
    PHI: false
    Role: null
  - Dtype: varchar(40)
    Field: ischcardiotimeframe
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: guiddiremedthermaxdose
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: nidcmtimeframe
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: nidcmmxdose
    PHI: false
    Role: null
  - Dtype: varchar(69)
    Field: cardiacarrestdate
    PHI: true
    Role: DATE
  - Dtype: varchar(52)
    Field: vtarrest
    PHI: false
    Role: null
  - Dtype: varchar(53)
    Field: vfibarrest
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: bradyarrest
    PHI: false
    Role: null
  - Dtype: varchar(69)
    Field: vtdate
    PHI: true
    Role: DATE
  - Dtype: varchar(51)
    Field: postcardiacsurgery
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: BradycardiaDependent
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: reversiblecause
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: hemoinstability
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: vttype
    PHI: false
    Role: null
  - Dtype: varchar(69)
    Field: priormidate
    PHI: true
    Role: DATE
  - Dtype: varchar(50)
    Field: perfafterrecentca
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: resultsofangiography
    PHI: false
    Role: null
  - Dtype: varchar(51)
    Field: revascularperformed
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: revascularizationoutcome
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: afibflutterclass
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: afibfluttercardioplans
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: priorpcicardiopresent
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: StructAbnType
    PHI: false
    Role: null
  - Dtype: varchar(53)
    Field: epstudy
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: epstudydate
    PHI: true
    Role: DATE
  - Dtype: varchar(20)
    Field: epstudydateunk
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: ventarrythinduced
    PHI: false
    Role: null
  - Dtype: varchar(53)
    Field: ecg
    PHI: false
    Role: null
  - Dtype: varchar(53)
    Field: ecgnormal
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: vpqrs
    PHI: false
    Role: null
  - Dtype: int
    Field: vpacedqrs
    PHI: false
    Role: null
  - Dtype: int
    Field: nvpqrs
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: AbConductionType
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: abconduction
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: abconductiondelayns
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: abconductionlbbb
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: abconductionrbbb
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: abconductionrbbbandlbbb
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: atrialrhythm
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: atrialrhythmafib
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: atrialrhythmaflutter
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: atrialrhythmap
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: atrialrhythmatrialtach
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: atrialrhythmsinusarrest
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: atrialrhythmsinusnode
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: vpace
    PHI: false
    Role: null
  - Dtype: int
    Field: bun
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: bunnd
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: hgb
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: hgbnd
    PHI: false
    Role: null
  - Dtype: int
    Field: sodium
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: sodiumnd
    PHI: false
    Role: null
  - Dtype: varchar(69)
    Field: proceduredate
    PHI: true
    Role: PROCEDURE_DATE|DATE
  - Dtype: varchar(69)
    Field: procedureenddate
    PHI: true
    Role: DATE
  - Dtype: varchar(66)
    Field: proceduretype
    PHI: false
    Role: null
  - Dtype: varchar(70)
    Field: icdindication
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: clinicaltrial
    PHI: false
    Role: null
  - Dtype: varchar(59)
    Field: genoplname
    PHI: false
    Role: null
  - Dtype: varchar(61)
    Field: genopfname
    PHI: false
    Role: null
  - Dtype: varchar(54)
    Field: genopmname
    PHI: false
    Role: null
  - Dtype: int
    Field: genopnpi
    PHI: false
    Role: null
  - Dtype: varchar(53)
    Field: deviceimplanted
    PHI: false
    Role: null
  - Dtype: varchar(80)
    Field: cslvlead
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: icdimpid
    PHI: false
    Role: null
  - Dtype: text
    Field: icdimpserno
    PHI: true
    Role: FREE_TEXT
  - Dtype: text
    Field: icdimpudi
    PHI: true
    Role: FREE_TEXT
  - Dtype: varchar(310)
    Field: reasonforreimplantation
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: reimpbattery
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: reimpfaulty
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: reimpinfection
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: reimpleadrev
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: reimpmalfx
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: reimpother
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: reimprecall
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: reimprelocation
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: reimpupgrade
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: deviceexplant
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: explanttreatrecommend
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: icdexpid
    PHI: false
    Role: null
  - Dtype: text
    Field: icdexpserno
    PHI: true
    Role: FREE_TEXT
  - Dtype: varchar(59)
    Field: leadoplname
    PHI: false
    Role: null
  - Dtype: varchar(61)
    Field: leadopfname
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: leadopmname
    PHI: false
    Role: null
  - Dtype: int
    Field: leadopnpi
    PHI: false
    Role: null
  - Dtype: int
    Field: leadcounter
    PHI: false
    Role: null
  - Dtype: varchar(60)
    Field: leadtype
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: leadid
    PHI: false
    Role: null
  - Dtype: text
    Field: leadserno
    PHI: true
    Role: FREE_TEXT
  - Dtype: text
    Field: leadudi
    PHI: true
    Role: FREE_TEXT
  - Dtype: varchar(64)
    Field: leadlocation
    PHI: false
    Role: null
  - Dtype: varchar(69)
    Field: exleaddate
    PHI: true
    Role: DATE
  - Dtype: varchar(60)
    Field: exleadstat
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: setscrew
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: leaddislodge
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: leaddislodgeloc
    PHI: false
    Role: null
  - Dtype: varchar(52)
    Field: cabg
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: cabgdate
    PHI: true
    Role: DATE
  - Dtype: varchar(52)
    Field: pci
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: pcidate
    PHI: true
    Role: DATE
  - Dtype: varchar(69)
    Field: DischargeDate
    PHI: true
    Role: ANCHOR_DATE|DATE
  - Dtype: varchar(10)
    Field: dischargestatus
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: dischargelocation
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: deathprocedure
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: deathcause
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: ace
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: aldosteroneantagonist
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: antiarrhythmicagents
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: antiplateletagents
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: apixaban
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: arb
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: betablocker
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: betrixaban
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: dabigatran
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: discharge_aspirin_any
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: dischmedarni
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: dischmedrenininhibitor
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: edoxaban
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: rivaroxaban
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: warfarin
    PHI: false
    Role: null
  - Dtype: varchar(23)
    Field: mbi
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: vfibdate
    PHI: true
    Role: DATE
  - Dtype: varchar(10)
    Field: bradindpres
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: reasonpacingindicated
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: reasonpacindic21avblock
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: reasonpacindicanticipatedrequirementofgt40prvpacing
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: reasonpacindicatrioventricularnodeablation
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: reasonpacindicchronotropicincompetence
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: reasonpacindiccompleteheartblock
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: reasonpacindichfunresponsivetogdmt
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: reasonpacindicmobitztypeii
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: reasonpacindicother
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: reasonpacindicsicksinussyndrome
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: sdmproc
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: sdmtool
    PHI: false
    Role: null
  - Dtype: varchar(40)
    Field: sdmtoolname
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: finaldevicetype
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: baccesssite
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: bgastrointestinal
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: bretroperitoneal
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: carrest
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: cardiacperf
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: tamponade
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: cvdissect
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: deviceembolization
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: hematoma
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: hemothorax
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: infectionreqanti
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: postmi
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: pericardialeffusion
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: pneumothorax
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: stroke
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: transfusion
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: posttia
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: urgentsurgery
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: vascularcomplications
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: afibflutter
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: cardiacarrest
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: ischcardio
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: nidcm
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: priorcvd
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: chroniclungdisease
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: cad
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: currentdialysis
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: diabetes
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: familialhxnicm
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: famhxsdeath
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: hf
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: oninotsupport
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: priormi
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: paroxysmalsvthistory
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: structuralabnormalities
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: syncope
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: syndromeriskdeath
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: primaryvalvularhd
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: vfib
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: vt
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: prioravproc
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: AVPDate
    PHI: true
    Role: DATE
  - Dtype: varchar(5)
    Field: transplantcandidate
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: vadcandidate
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: CoronaryAngiography
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: priorangiographydate
    PHI: true
    Role: DATE
  - Dtype: varchar(5)
    Field: currentvad
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: priorcied
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: priorcieddate
    PHI: true
    Role: DATE
  - Dtype: varchar(5)
    Field: transplantwaitlist
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: priorcabg
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: priorcabgdate
    PHI: true
    Role: DATE
  - Dtype: varchar(5)
    Field: priorpci
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: priorpcidate
    PHI: true
    Role: DATE
  - Dtype: decimal(4,2)
    Field: inr
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: inrnd
    PHI: false
    Role: null
  - Dtype: decimal(6,3)
    Field: preproccreat
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: preproccreatnd
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: perieffusioninterv
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: hemothoraxreqdrng
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: fitprogid
    PHI: false
    Role: null
  - Dtype: varchar(70)
    Field: fit_lastname
    PHI: false
    Role: null
  - Dtype: varchar(70)
    Field: fit_firstname
    PHI: false
    Role: null
  - Dtype: varchar(70)
    Field: fit_midname
    PHI: false
    Role: null
  - Dtype: int
    Field: fit_npi
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: procedureentrytime
    PHI: true
    Role: DATE
  - Dtype: varchar(30)
    Field: procedurestoptime
    PHI: true
    Role: DATE
  - Dtype: varchar(140)
    Field: aftpr
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: blcodev
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: vascomint
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: vascominttyp
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: vascomloc
    PHI: false
    Role: null
  - Dtype: varchar(180)
    Field: infiltrastruct
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: postmarsur
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: unipacqrsmorph
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: tampinttyp
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: pneureqint
    PHI: false
    Role: null
  - Dtype: int
    Field: finpacedqrsdur
    PHI: false
    Role: null
  - Dtype: int
    Field: rwavpeaktimdur
    PHI: false
    Role: null
  - Dtype: varchar(400)
    Field: pricieddevtyp
    PHI: false
    Role: null
  - Dtype: varchar(115)
    Field: elecdevimppath
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: hisbunlead
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: leftbunlead
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: finpacedqrsdurnotass
    PHI: false
    Role: null
  - Dtype: varchar(5)
    Field: rwavpeaktimdurnotass
    PHI: false
    Role: null
custom_fields:
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Indian Health Service
    Derivative Value Code: '33'
    Exact Match: 0
    Field: insihs
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Indian Health Service|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Medicaid
    Derivative Value Code: '2'
    Exact Match: 0
    Field: insmedicaid
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Medicaid|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Medicare (Part A or B)
    Derivative Value Code: '1'
    Exact Match: 0
    Field: insmedicare
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Medicare (Part A or B)|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Medicare Advantage (Part C)
    Derivative Value Code: '112000002025'
    Exact Match: 0
    Field: insmedicareadvantage
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Medicare Advantage (Part C)|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Military health care
    Derivative Value Code: '31'
    Exact Match: 0
    Field: insmilitary
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Military health care|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Non-US insurance
    Derivative Value Code: '100000812'
    Exact Match: 0
    Field: insnonus
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Non-US insurance|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Private health insurance
    Derivative Value Code: '5'
    Exact Match: 0
    Field: insprivate
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Private health insurance|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: State-specific plan (non-Medicaid)
    Derivative Value Code: '36'
    Exact Match: 0
    Field: insstate
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|State-specific plan (non-Medicaid)|': 'Yes'
  - Delimiter: null
    Derivative Field: IntraVentConductionType
    Derivative Field Code: 100001142
    Derivative Value: Delay, nonspecific
    Derivative Value Code: '698252002'
    Exact Match: 0
    Field: abconductiondelayns
    Reference Field: AbConduction
    Reference Field Code: 4554005
    Section: EPISODEOFCARE
    Value Map: null
  - Delimiter: null
    Derivative Field: IntraVentConductionType
    Derivative Field Code: 100001142
    Derivative Value: Left bundle branch block (LBBB)
    Derivative Value Code: '164909002'
    Exact Match: 0
    Field: abconductionlbbb
    Reference Field: AbConduction
    Reference Field Code: 4554005
    Section: EPISODEOFCARE
    Value Map: null
  - Delimiter: null
    Derivative Field: IntraVentConductionType
    Derivative Field Code: 100001142
    Derivative Value: Right bundle branch block (RBBB)
    Derivative Value Code: '164907000'
    Exact Match: 0
    Field: abconductionrbbb
    Reference Field: AbConduction
    Reference Field Code: 4554005
    Section: EPISODEOFCARE
    Value Map: null
  - Delimiter: null
    Derivative Field: IntraVentConductionType
    Derivative Field Code: 100001142
    Derivative Value: Alternating RBBB and LBBB
    Derivative Value Code: '32758004'
    Exact Match: 0
    Field: abconductionrbbbandlbbb
    Reference Field: AbConduction
    Reference Field Code: 4554005
    Section: EPISODEOFCARE
    Value Map: null
  - Delimiter: null
    Derivative Field: AtrialRhythm
    Derivative Field Code: 106068003
    Derivative Value: Atrial fibrillation
    Derivative Value Code: '49436004'
    Exact Match: 0
    Field: atrialrhythmafib
    Reference Field: AtrialRhythm
    Reference Field Code: 106068003
    Section: EPISODEOFCARE
    Value Map:
      Atrial fibrillation: 'Yes'
  - Delimiter: null
    Derivative Field: AtrialRhythm
    Derivative Field Code: 106068003
    Derivative Value: Atrial flutter
    Derivative Value Code: '5370000'
    Exact Match: 0
    Field: atrialrhythmaflutter
    Reference Field: AtrialRhythm
    Reference Field Code: 106068003
    Section: EPISODEOFCARE
    Value Map:
      Atrial flutter: 'Yes'
  - Delimiter: null
    Derivative Field: AtrialRhythm
    Derivative Field Code: 106068003
    Derivative Value: Atrial paced
    Derivative Value Code: '251268003'
    Exact Match: 0
    Field: atrialrhythmap
    Reference Field: AtrialRhythm
    Reference Field Code: 106068003
    Section: EPISODEOFCARE
    Value Map:
      Atrial paced: 'Yes'
  - Delimiter: null
    Derivative Field: AtrialRhythm
    Derivative Field Code: 106068003
    Derivative Value: Atrial tachycardia
    Derivative Value Code: '276796006'
    Exact Match: 0
    Field: atrialrhythmatrialtach
    Reference Field: AtrialRhythm
    Reference Field Code: 106068003
    Section: EPISODEOFCARE
    Value Map:
      Atrial fibrillation: 'Yes'
  - Delimiter: null
    Derivative Field: AtrialRhythm
    Derivative Field Code: 106068003
    Derivative Value: Sinus arrest
    Derivative Value Code: '5609005'
    Exact Match: 0
    Field: atrialrhythmsinusarrest
    Reference Field: AtrialRhythm
    Reference Field Code: 106068003
    Section: EPISODEOFCARE
    Value Map:
      Sinus arrest: 'Yes'
  - Delimiter: null
    Derivative Field: AtrialRhythm
    Derivative Field Code: 106068003
    Derivative Value: Sinus
    Derivative Value Code: '106067008'
    Exact Match: 0
    Field: atrialrhythmsinusnode
    Reference Field: AtrialRhythm
    Reference Field Code: 106068003
    Section: EPISODEOFCARE
    Value Map:
      Sinus: 'Yes'
  - Delimiter: null
    Derivative Field: ReImplantReason
    Derivative Field Code: 100000991
    Derivative Value: End of expected battery life
    Derivative Value Code: 100001088
    Exact Match: 0
    Field: reimpbattery
    Reference Field: ReImplantReason
    Reference Field Code: 100000991
    Section: PROCINFO
    Value Map:
      End of expected battery life: 'Yes'
  - Delimiter: null
    Derivative Field: ReImplantReason
    Derivative Field Code: 100000991
    Derivative Value: Faulty connector/header
    Derivative Value Code: 100001089
    Exact Match: 0
    Field: reimpfaulty
    Reference Field: ReImplantReason
    Reference Field Code: 100000991
    Section: PROCINFO
    Value Map:
      Faulty connector/header: 'Yes'
  - Delimiter: null
    Derivative Field: ReImplantReason
    Derivative Field Code: 100000991
    Derivative Value: Infection
    Derivative Value Code: 100001091
    Exact Match: 0
    Field: reimpinfection
    Reference Field: ReImplantReason
    Reference Field Code: 100000991
    Section: PROCINFO
    Value Map:
      Infection: 'Yes'
  - Delimiter: null
    Derivative Field: ReImplantReason
    Derivative Field Code: 100000991
    Derivative Value: Replaced at time of lead revision
    Derivative Value Code: 100001092
    Exact Match: 0
    Field: reimpleadrev
    Reference Field: ReImplantReason
    Reference Field Code: 100000991
    Section: PROCINFO
    Value Map:
      Replaced at time of lead revision: 'Yes'
  - Delimiter: null
    Derivative Field: ReImplantReason
    Derivative Field Code: 100000991
    Derivative Value: Malfunction
    Derivative Value Code: 100001090
    Exact Match: 0
    Field: reimpmalfx
    Reference Field: ReImplantReason
    Reference Field Code: 100000991
    Section: PROCINFO
    Value Map:
      Malfunction: 'Yes'
  - Delimiter: null
    Derivative Field: ReImplantReason
    Derivative Field Code: 100000991
    Derivative Value: Other
    Derivative Value Code: 112000003710
    Exact Match: 0
    Field: reimpother
    Reference Field: ReImplantReason
    Reference Field Code: 100000991
    Section: PROCINFO
    Value Map:
      Other: 'Yes'
  - Delimiter: null
    Derivative Field: ReImplantReason
    Derivative Field Code: 100000991
    Derivative Value: Under manufacturer advisory/recall
    Derivative Value Code: 100001093
    Exact Match: 0
    Field: reimprecall
    Reference Field: ReImplantReason
    Reference Field Code: 100000991
    Section: PROCINFO
    Value Map:
      Under manufacturer advisory/recall: 'Yes'
  - Delimiter: null
    Derivative Field: ReImplantReason
    Derivative Field Code: 100000991
    Derivative Value: Device relocation
    Derivative Value Code: 100001087
    Exact Match: 0
    Field: reimprelocation
    Reference Field: ReImplantReason
    Reference Field Code: 100000991
    Section: PROCINFO
    Value Map:
      Device relocation: 'Yes'
  - Delimiter: null
    Derivative Field: ReImplantReason
    Derivative Field Code: 100000991
    Derivative Value: Upgrade
    Derivative Value Code: 100001094
    Exact Match: 0
    Field: reimpupgrade
    Reference Field: ReImplantReason
    Reference Field Code: 100000991
    Section: PROCINFO
    Value Map:
      Upgrade: 'Yes'
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin Converting Enzyme Inhibitor
    Derivative Value Code: 41549009
    Exact Match: 1
    Field: ace
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aldosterone Antagonist
    Derivative Value Code: 372603003
    Exact Match: 1
    Field: aldosteroneantagonist
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Antiarrhythmic Drug
    Derivative Value Code: 67507000
    Exact Match: 1
    Field: antiarrhythmicagents
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Antiplatelet Agent
    Derivative Value Code: 372560006
    Exact Match: 1
    Field: antiplateletagents
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Apixaban
    Derivative Value Code: 1364430
    Exact Match: 1
    Field: apixaban
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin II Receptor Blocker
    Derivative Value Code: 372913009
    Exact Match: 1
    Field: arb
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Beta Blocker
    Derivative Value Code: 33252009
    Exact Match: 1
    Field: betablocker
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Betrixaban
    Derivative Value Code: 1927851
    Exact Match: 1
    Field: betrixaban
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Dabigatran
    Derivative Value Code: 1546356
    Exact Match: 1
    Field: dabigatran
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aspirin
    Derivative Value Code: 1191
    Exact Match: 1
    Field: discharge_aspirin_any
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin Receptor-Neprilysin Inhibitor
    Derivative Value Code: 112000001832
    Exact Match: 1
    Field: dischmedarni
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Renin Inhibitor
    Derivative Value Code: 426228001
    Exact Match: 1
    Field: dischmedrenininhibitor
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Edoxaban
    Derivative Value Code: 1599538
    Exact Match: 1
    Field: edoxaban
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Rivaroxaban
    Derivative Value Code: 1114195
    Exact Match: 1
    Field: rivaroxaban
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Warfarin
    Derivative Value Code: 11289
    Exact Match: 1
    Field: warfarin
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DISCHARGEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ReasonPacIndic
    Derivative Field Code: 100001097
    Derivative Value: 2:1 AV Block
    Derivative Value Code: '54016002'
    Exact Match: 0
    Field: reasonpacindic21avblock
    Reference Field: ReasonPacIndic
    Reference Field Code: 100001097
    Section: PROCINFO
    Value Map:
      2:1 AV Block: 'Yes'
  - Delimiter: null
    Derivative Field: ReasonPacIndic
    Derivative Field Code: 100001097
    Derivative Value: Anticipated requirement of > 40% RV pacing
    Derivative Value Code: '100000931'
    Exact Match: 0
    Field: reasonpacindicanticipatedrequirementofgt40prvpacing
    Reference Field: ReasonPacIndic
    Reference Field Code: 100001097
    Section: PROCINFO
    Value Map:
      Anticipated requirement of > 40% RV pacing: 'Yes'
  - Delimiter: null
    Derivative Field: ReasonPacIndic
    Derivative Field Code: 100001097
    Derivative Value: Atrioventricular Node Ablation
    Derivative Value Code: '428663009'
    Exact Match: 0
    Field: reasonpacindicatrioventricularnodeablation
    Reference Field: ReasonPacIndic
    Reference Field Code: 100001097
    Section: PROCINFO
    Value Map:
      Atrioventricular Node Ablation: 'Yes'
  - Delimiter: null
    Derivative Field: ReasonPacIndic
    Derivative Field Code: 100001097
    Derivative Value: Chronotropic incompetence
    Derivative Value Code: '427989008'
    Exact Match: 0
    Field: reasonpacindicchronotropicincompetence
    Reference Field: ReasonPacIndic
    Reference Field Code: 100001097
    Section: PROCINFO
    Value Map:
      Chronotropic incompetence: 'Yes'
  - Delimiter: null
    Derivative Field: ReasonPacIndic
    Derivative Field Code: 100001097
    Derivative Value: Complete heart block
    Derivative Value Code: '27885002'
    Exact Match: 0
    Field: reasonpacindiccompleteheartblock
    Reference Field: ReasonPacIndic
    Reference Field Code: 100001097
    Section: PROCINFO
    Value Map:
      Complete heart block: 'Yes'
  - Delimiter: null
    Derivative Field: ReasonPacIndic
    Derivative Field Code: 100001097
    Derivative Value: HF unresponsive to GDMT
    Derivative Value Code: '112000002017'
    Exact Match: 0
    Field: reasonpacindichfunresponsivetogdmt
    Reference Field: ReasonPacIndic
    Reference Field Code: 100001097
    Section: PROCINFO
    Value Map:
      HF unresponsive to GDMT: 'Yes'
  - Delimiter: null
    Derivative Field: ReasonPacIndic
    Derivative Field Code: 100001097
    Derivative Value: Mobitz Type II
    Derivative Value Code: '28189009'
    Exact Match: 0
    Field: reasonpacindicmobitztypeii
    Reference Field: ReasonPacIndic
    Reference Field Code: 100001097
    Section: PROCINFO
    Value Map:
      Mobitz Type II: 'Yes'
  - Delimiter: null
    Derivative Field: ReasonPacIndic
    Derivative Field Code: 100001097
    Derivative Value: Other
    Derivative Value Code: '100000351'
    Exact Match: 0
    Field: reasonpacindicother
    Reference Field: ReasonPacIndic
    Reference Field Code: 100001097
    Section: PROCINFO
    Value Map:
      Other: 'Yes'
  - Delimiter: null
    Derivative Field: ReasonPacIndic
    Derivative Field Code: 100001097
    Derivative Value: Sick sinus syndrome
    Derivative Value Code: '36083008'
    Exact Match: 0
    Field: reasonpacindicsicksinussyndrome
    Reference Field: ReasonPacIndic
    Reference Field Code: 100001097
    Section: PROCINFO
    Value Map:
      Sick sinus syndrome: 'Yes'
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Access Site
    Derivative Value Code: '1000142440'
    Exact Match: 1
    Field: baccesssite
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Gastrointestinal
    Derivative Value Code: '74474003'
    Exact Match: 1
    Field: bgastrointestinal
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Retroperitoneal
    Derivative Value Code: '95549001'
    Exact Match: 1
    Field: bretroperitoneal
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Cardiac arrest
    Derivative Value Code: '410429000'
    Exact Match: 1
    Field: carrest
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Cardiac perforation
    Derivative Value Code: 36191001:123005000=302509004
    Exact Match: 1
    Field: cardiacperf
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Cardiac tamponade
    Derivative Value Code: '35304003'
    Exact Match: 1
    Field: tamponade
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Coronary venous dissection
    Derivative Value Code: '100000029'
    Exact Match: 1
    Field: cvdissect
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Device embolization
    Derivative Value Code: '112000001324'
    Exact Match: 1
    Field: deviceembolization
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Hematoma (Re-op, evac, or transfusion)
    Derivative Value Code: '385494008'
    Exact Match: 1
    Field: hematoma
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Hemothorax
    Derivative Value Code: '31892009'
    Exact Match: 1
    Field: hemothorax
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Infection requiring antibiotics
    Derivative Value Code: '100001017'
    Exact Match: 1
    Field: infectionreqanti
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Myocardial infarction
    Derivative Value Code: '22298006'
    Exact Match: 1
    Field: postmi
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Pericardial effusion
    Derivative Value Code: '373945007'
    Exact Match: 1
    Field: pericardialeffusion
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Pneumothorax
    Derivative Value Code: '36118008'
    Exact Match: 1
    Field: pneumothorax
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Stroke (Any)
    Derivative Value Code: '100000977'
    Exact Match: 1
    Field: stroke
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Transfusion
    Derivative Value Code: '5447007'
    Exact Match: 1
    Field: transfusion
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Transient ischemic attack (TIA)
    Derivative Value Code: '266257000'
    Exact Match: 1
    Field: posttia
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Urgent cardiac surgery
    Derivative Value Code: 64915003:260870009=103391001
    Exact Match: 1
    Field: urgentsurgery
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: PostProcEvent
    Derivative Field Code: 1000142478
    Derivative Value: Vascular complications
    Derivative Value Code: '213217008'
    Exact Match: 1
    Field: vascularcomplications
    Reference Field: PostProcOccurred
    Reference Field Code: 1000142479
    Section: IPPEVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Atrial fibrillation
    Derivative Value Code: '49436004'
    Exact Match: 1
    Field: afibflutter
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Cardiac arrest
    Derivative Value Code: '410429000'
    Exact Match: 1
    Field: cardiacarrest
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Cardiomyopathy - ischemic
    Derivative Value Code: '426856002'
    Exact Match: 1
    Field: ischcardio
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Cardiomyopathy - non-ischemic
    Derivative Value Code: '111000119104'
    Exact Match: 1
    Field: nidcm
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Cerebrovascular disease
    Derivative Value Code: '62914000'
    Exact Match: 1
    Field: priorcvd
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Chronic lung disease
    Derivative Value Code: '413839001'
    Exact Match: 1
    Field: chroniclungdisease
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Coronary artery disease
    Derivative Value Code: '53741008'
    Exact Match: 1
    Field: cad
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Currently on dialysis
    Derivative Value Code: '108241001'
    Exact Match: 1
    Field: currentdialysis
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Diabetes mellitus
    Derivative Value Code: '73211009'
    Exact Match: 1
    Field: diabetes
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Familial history of non-ischemic cardiomyopathy
    Derivative Value Code: 281666001:246090004=399020009
    Exact Match: 1
    Field: familialhxnicm
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Familial syndrome-risk of sudden death
    Derivative Value Code: '100001006'
    Exact Match: 1
    Field: famhxsdeath
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Heart failure
    Derivative Value Code: '84114007'
    Exact Match: 1
    Field: hf
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Inotropic support
    Derivative Value Code: '100001061'
    Exact Match: 1
    Field: oninotsupport
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Myocardial infarction
    Derivative Value Code: '22298006'
    Exact Match: 1
    Field: priormi
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Paroxysmal SVT history
    Derivative Value Code: '67198005'
    Exact Match: 1
    Field: paroxysmalsvthistory
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Structural abnormalities
    Derivative Value Code: '100000949'
    Exact Match: 1
    Field: structuralabnormalities
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Syncope
    Derivative Value Code: '271594007'
    Exact Match: 1
    Field: syncope
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Syndromes of sudden death
    Derivative Value Code: '100001202'
    Exact Match: 1
    Field: syndromeriskdeath
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Valvular heart disease
    Derivative Value Code: '368009'
    Exact Match: 1
    Field: primaryvalvularhd
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Ventricular fibrillation (not due to reversible cause)
    Derivative Value Code: '71908006'
    Exact Match: 1
    Field: vfib
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Ventricular tachycardia
    Derivative Value Code: '25569003'
    Exact Match: 1
    Field: vt
    Reference Field: ConditionHxOccurence
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Aortic valve procedure
    Derivative Value Code: '112000001755'
    Exact Match: 1
    Field: prioravproc
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Aortic valve procedure
    Derivative Value Code: '112000001755'
    Exact Match: 1
    Field: AVPDate
    Reference Field: ProcHistDate
    Reference Field Code: 416940007
    Section: PROCHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Candidate for transplant
    Derivative Value Code: '100000821'
    Exact Match: 1
    Field: transplantcandidate
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Candidate for VAD
    Derivative Value Code: '112000002045'
    Exact Match: 1
    Field: vadcandidate
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Coronary angiography
    Derivative Value Code: '33367005'
    Exact Match: 1
    Field: CoronaryAngiography
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Coronary angiography
    Derivative Value Code: '33367005'
    Exact Match: 1
    Field: priorangiographydate
    Reference Field: ProcHistDate
    Reference Field Code: 416940007
    Section: PROCHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Currently on VAD
    Derivative Value Code: '112000002046'
    Exact Match: 1
    Field: currentvad
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: CV implantable electronic device
    Derivative Value Code: '100000954'
    Exact Match: 1
    Field: priorcied
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: CV implantable electronic device
    Derivative Value Code: '100000954'
    Exact Match: 1
    Field: priorcieddate
    Reference Field: ProcHistDate
    Reference Field Code: 416940007
    Section: PROCHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: On Heart Transplant Waiting List
    Derivative Value Code: '471300007'
    Exact Match: 1
    Field: transplantwaitlist
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Prior coronary artery bypass graft
    Derivative Value Code: '232717009'
    Exact Match: 1
    Field: priorcabg
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Prior coronary artery bypass graft
    Derivative Value Code: '232717009'
    Exact Match: 1
    Field: priorcabgdate
    Reference Field: ProcHistDate
    Reference Field Code: 416940007
    Section: PROCHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Prior PCI
    Derivative Value Code: '415070008'
    Exact Match: 1
    Field: priorpci
    Reference Field: ProcHxOccur
    Reference Field Code: 416940007
    Section: PROCHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Prior PCI
    Derivative Value Code: '415070008'
    Exact Match: 1
    Field: priorpcidate
    Reference Field: ProcHistDate
    Reference Field Code: 416940007
    Section: PROCHX
    Value Map: null
rename_fields:
  - New Field: partid
    Old Field: ParticID
  - New Field: sex
    Old Field: Gender
  - New Field: zipcode
    Old Field: patzip
  - New Field: zipcodena
    Old Field: patzipna
  - New Field: racenathaw
    Old Field: racenathawpasislander
  - New Field: episodekey
    Old Field: episodeid
  - New Field: healthins
    Old Field: HealthInsurance
  - New Field: studyptid
    Old Field: RStudyPatientID
  - New Field: priorlvefassessed
    Old Field: LVEFAssessed
  - New Field: priorlvefdate
    Old Field: mstreclvefdate
  - New Field: priorlvef
    Old Field: LVEF
  - New Field: iscmtimeframe
    Old Field: ischcardiotimeframe
  - New Field: iscmgdmtdose
    Old Field: guiddiremedthermaxdose
  - New Field: nicmtimeframe
    Old Field: nidcmtimeframe
  - New Field: nicmgdmtdose
    Old Field: nidcmmxdose
  - New Field: vtacharrest
    Old Field: vtarrest
  - New Field: vtpostcardiacsurgery
    Old Field: postcardiacsurgery
  - New Field: bradydependent
    Old Field: BradycardiaDependent
  - New Field: vtreversecause
    Old Field: reversiblecause
  - New Field: coronaryangioresults
    Old Field: resultsofangiography
  - New Field: revascperf
    Old Field: revascularperformed
  - New Field: revascoutcome
    Old Field: revascularizationoutcome
  - New Field: afibclass
    Old Field: afibflutterclass
  - New Field: intraventconductiontype
    Old Field: AbConductionType
  - New Field: vpaced
    Old Field: vpace
  - New Field: procedurestartdatetime
    Old Field: proceduredate
  - New Field: procedureenddatetime
    Old Field: procedureenddate
  - New Field: reimplantreason
    Old Field: reasonforreimplantation
  - New Field: explanttreatment
    Old Field: explanttreatrecommend
  - New Field: dcdate
    Old Field: DischargeDate
  - New Field: dcstatus
    Old Field: dischargestatus
  - New Field: dclocation
    Old Field: dischargelocation
  - New Field: reasonpacindic
    Old Field: reasonpacingindicated
  - New Field: sdm_proc
    Old Field: sdmproc
  - New Field: sdm_tool
    Old Field: sdmtool
  - New Field: sdm_tool_name
    Old Field: sdmtoolname
  - New Field: final_device_type
    Old Field: finaldevicetype
delimiters:
  - Enclose: true
    Field: abconductiontype
    Replace: null
    Delimiter: '|'
  - Enclose: true
    Field: reasonforreimplantation
    Replace: null
    Delimiter: '|'
pivot_sections:
  - CONDHX
  - PROCHX
  - DISCHARGEMEDS
transform_fields:
  - Field: proceduretime
    Transform Type: additional_field
    Args:
      Source: proceduredate
    Computation: true
  - Field: procedureendtime
    Transform Type: additional_field
    Args:
      Source: procedureenddate
    Computation: true