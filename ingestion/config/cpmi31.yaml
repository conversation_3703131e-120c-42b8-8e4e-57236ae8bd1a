version: '3.1'
biome_schema:
  - Dtype: bigint
    Field: PATIENTROWID
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: NCDRPatientId
    PHI: true
    Role: IDENTIFIER
  - Dtype: varchar(20)
    Field: PartID
    PHI: false
    Role: null
  - Dtype: varchar(100)
    Field: PartName
    PHI: false
    Role: null
  - Dtype: text
    Field: LastName
    PHI: true
    Role: NAME
  - Dtype: text
    Field: FirstName
    PHI: true
    Role: NAME
  - Dtype: text
    Field: MidName
    PHI: true
    Role: NAME
  - Dtype: varchar(30)
    Field: DOB
    PHI: true
    Role: DOB
  - Dtype: text
    Field: SSN
    PHI: true
    Role: NAME
  - Dtype: varchar(10)
    Field: SSNNA
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: OtherId
    PHI: true
    Role: MEDRECNUM
  - Dtype: varchar(30)
    Field: Gender
    PHI: true
    Role: GENDER
  - Dtype: varchar(9)
    Field: PatZip
    PHI: true
    Role: PATIENT_ZIP
  - Dtype: varchar(12)
    Field: PatZipNA
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceWhite
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceBlack
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceAMIndian
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceAsian
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: RaceNatHaw
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: HispOrig
    PHI: false
    Role: null
  - Dtype: bigint
    Field: EPISODEROWID
    PHI: false
    Role: null
  - Dtype: text
    Field: EpisodeId
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: ArrivalDatetime
    PHI: true
    Role: ARRIVAL_DATE
  - Dtype: varchar(31)
    Field: AdmitDatetime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: HealthIns
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: InsPrivate
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: InsMedicare
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PatientType
    PHI: false
    Role: null
  - Dtype: varchar(21)
    Field: STEMISet
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: AdmittingDx
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: NonatheroMI
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MIMechanism
    PHI: false
    Role: null
  - Dtype: varchar(23)
    Field: FirstFacMeansTrans
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: EMS911CallDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(31)
    Field: EMSDispatchDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(31)
    Field: FirstMedConDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(31)
    Field: EMSLeavingSceneDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: EMSFirstMedConReasonforDelay
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: EMSSTEMIAlert
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: EMSSTEMIAlertDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(22)
    Field: EMSNPI
    PHI: false
    Role: null
  - Dtype: varchar(22)
    Field: EMSRunNumber
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: TranOutsideFac
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: ArrOutHospDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(31)
    Field: TranOutFacDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: PatientReasonforDelayXfer
    PHI: false
    Role: null
  - Dtype: varchar(67)
    Field: TranFacName
    PHI: false
    Role: null
  - Dtype: varchar(19)
    Field: TranFacAHANumber
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: FacNameSameParent
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: TranFacNameUnavail
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: CAOutHospital
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: CAWitness
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: BystandCPR
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: CAPostEMS
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: InitCARhythm
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: InitCARhythmUnk
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ResuscDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(12)
    Field: ResuscitationTimeUnknown
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: CATransferFac
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: Uncon
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: Height
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: Weight
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HxCVD
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: STROKEBA
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HxTIA
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: DIABETESBA
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CURRENTDIALYSISONARRIVAL
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PRIORHFFF
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: Hypertension
    PHI: false
    Role: null
  - Dtype: varchar(255)
    Field: TobaccoUse
    PHI: false
    Role: null
  - Dtype: varchar(19)
    Field: eCigarette
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HxAFib
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: AtrialFlutter
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: CANCER
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: Dyslipidemia
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PriorMI
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PriorPAD
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: PriorCABG
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PriorCABGDate
    PHI: true
    Role: DATE
  - Dtype: varchar(10)
    Field: PriorPCI
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: LastPCIDate
    PHI: true
    Role: DATE
  - Dtype: varchar(21)
    Field: LocFirstEval
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HRFirstMedCon
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: SBPFirstMedCon
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: ShockFirstMedCon
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HFFirstMedCon
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: CSHAScaleArrival
    PHI: false
    Role: null
  - Dtype: varchar(28)
    Field: ChestPainSymp
    PHI: false
    Role: null
  - Dtype: varchar(22)
    Field: SYMPTOMDATE24
    PHI: false
    Role: null
  - Dtype: varchar(34)
    Field: SYMPTOMTIME24
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: TimeUnknownSymptomPriorArrival
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DateChestPainSymptomAfterArrival
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TimeChestPainSymptomAfterArrival
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TimeUnknownSymptomAfterArrival
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: TropNotDrawn
    PHI: false
    Role: null
  - Dtype: varchar(21)
    Field: TroponinProtocol
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CathLabAct
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: CathLabActDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(30)
    Field: CathLabActBy
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PCIOpArrDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(12)
    Field: CathLabStaffArrDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: CathLabActCanc
    PHI: false
    Role: null
  - Dtype: varchar(22)
    Field: CathLabCancelledBy
    PHI: false
    Role: null
  - Dtype: varchar(24)
    Field: RiskStratification
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: RiskStraNotDocumented
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: RiskStratPerfTransferFacility
    PHI: false
    Role: null
  - Dtype: varchar(28)
    Field: RiskAssessmentTool
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: RiskAssessmentToolNotDocumented
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: FuncTestResult
    PHI: false
    Role: null
  - Dtype: varchar(25)
    Field: AnatomicalImagingResult
    PHI: false
    Role: null
  - Dtype: varchar(23)
    Field: CADtype
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: SDMTool
    PHI: false
    Role: null
  - Dtype: varchar(26)
    Field: IschemiaEval
    PHI: false
    Role: null
  - Dtype: varchar(37)
    Field: IschemiaEvalMethod
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: IschemiaEvalOrdDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(31)
    Field: IschemiaEvalPerfDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(20)
    Field: IschemiaAssessmentResult
    PHI: false
    Role: null
  - Dtype: varchar(26)
    Field: CTAPerformed
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: CTAOrdDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(12)
    Field: CTAPerfDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(12)
    Field: HomeMedACEI
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: HomeMedAngiotensinII
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: HomeMedARNI
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: HomeMedBetaBlocker
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: HomeMedPrasugrel
    PHI: false
    Role: null
  - Dtype: varchar(27)
    Field: PreProcASA
    PHI: false
    Role: null
  - Dtype: varchar(16)
    Field: FMCCreat
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: CreatNotDrawn
    PHI: false
    Role: null
  - Dtype: varchar(16)
    Field: PeakCreat
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: CreatPeakNotDrawn
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: CreatPeakDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(16)
    Field: InitHGBValue
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: HgbNotDrawn
    PHI: false
    Role: null
  - Dtype: varchar(16)
    Field: LowHGBValue
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: LowHgbNotDrawn
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: LowHgbValueDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(16)
    Field: HbA1cValue
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HbA1cNotDrawn
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: InitINRValue
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: INRNOTDRAWN
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LipidsTC6mFMC
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: TotalCholNotDrawn
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LipidsHDL6mFMC
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: HDLNotDrawn
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LipidsLDL
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LDLNotDrawn
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LipidsTrig
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: TRIGLYCNOTDRAWN
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: DiagCorAngioFirst
    PHI: false
    Role: null
  - Dtype: varchar(19)
    Field: DCathLName
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: DCathFName
    PHI: false
    Role: null
  - Dtype: varchar(18)
    Field: DCathMName
    PHI: false
    Role: null
  - Dtype: varchar(22)
    Field: DCathNPI
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: CathArrivalDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(31)
    Field: DiagCorAngioDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: AngioDelayPtReason
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: AngioDelayResuscitatedPtReason
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CardiacAngioResults
    PHI: false
    Role: null
  - Dtype: varchar(23)
    Field: CADtype2
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: ThromTherapyFMC
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: ThromDateTimeFMC
    PHI: true
    Role: DATE
  - Dtype: varchar(12)
    Field: MedReaDelay
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: PatReaDelay
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: PCIArrivDC2
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: CABGFirst2
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: CABGDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(31)
    Field: PCIDATETIME
    PHI: false
    Role: null
  - Dtype: varchar(19)
    Field: PCILName
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: PCIFName
    PHI: false
    Role: null
  - Dtype: varchar(18)
    Field: PCIMName
    PHI: false
    Role: null
  - Dtype: varchar(22)
    Field: PCINPI
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: FIT_LASTNAME
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: FIT_FIRSTNAME
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: FIT_MIDNAME
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: FIT_NPI
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: FITPROGID
    PHI: false
    Role: null
  - Dtype: varchar(47)
    Field: PCIIndication
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: MechVentSupp
    PHI: false
    Role: null
  - Dtype: varchar(105)
    Field: MVSupportDevice
    PHI: false
    Role: null
  - Dtype: varchar(19)
    Field: AccessSite
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: StentsImplanted
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: StentType
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: CurrStentTypeUnk
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: CERBC
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: CERBCDate
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: CERBCCABG
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: NSAIDAdmin
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MEDREANOTADMINNSAID
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: DCDateTime
    PHI: true
    Role: '|DISCHARGE_DATE||DATE||ANCHOR_DATE|'
  - Dtype: varchar(60)
    Field: DischargeStatus
    PHI: false
    Role: null
  - Dtype: varchar(19)
    Field: DeathCause
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: DCLName
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: DCFName
    PHI: false
    Role: null
  - Dtype: varchar(13)
    Field: DCMName
    PHI: false
    Role: null
  - Dtype: varchar(22)
    Field: DCNPI
    PHI: false
    Role: null
  - Dtype: varchar(50)
    Field: EnrolledStudy
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: DC_LVEF
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: LVEFPercent
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: LVEFPLANDC
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: LVEFPlanDCnotIndicated
    PHI: false
    Role: null
  - Dtype: varchar(41)
    Field: CPCscore
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: HospClinicTrial
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TypeClinTrial
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: DischargeComfort
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DCComfortDatetime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: DCHospice
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: HospCareDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(42)
    Field: DischargeCardRehab
    PHI: false
    Role: null
  - Dtype: varchar(60)
    Field: DischargeLocation
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TransDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(12)
    Field: TransDelayPtCenteredReason
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: CardiacEvaluation
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TransferPCI
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: CABGTransferDC
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: CSHAScaleDC
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: DischargeASA
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_ASA_MEDDOSE
    PHI: false
    Role: null
  - Dtype: varchar(43)
    Field: Discharge_Clopidogrel
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_Clopidogrel_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(43)
    Field: Discharge_Prasugrel
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_Prasugrel_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(43)
    Field: Discharge_Ticagrelor
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_Ticagrelor_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(28)
    Field: DischargeBetaBlocker
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_BetaBlocker_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(43)
    Field: Discharge_ACE_Inhibitor_any
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_ACEInhibitors_Any_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(43)
    Field: Discharge_ARB_any
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_ARB_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(43)
    Field: Discharge_ARNI
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_ARNI_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(43)
    Field: Discharge_ARA_Any
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_ARA_Any_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: Discharge_DOAC_Any
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_DOAC_Any_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(38)
    Field: Discharge_Warfarin
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_Warfarin_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(43)
    Field: DischargeStatinAny
    PHI: false
    Role: null
  - Dtype: varchar(35)
    Field: DC_Statin_Any_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: DC_Aspirin100MGplus
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: ReasonNoHighDose
    PHI: false
    Role: null
  - Dtype: varchar(21)
    Field: EDDisposition
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: TranOutEDDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(12)
    Field: ObserOrderDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: STEMIFIRSTNOTEDFMC1STECG
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: FirstDevActiDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: PtPCIDelayReason
    PHI: false
    Role: null
  - Dtype: varchar(64)
    Field: PCIDelayReason
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: InsMedicaid
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: HypothermiaInducedFMCDC
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: HypothermiaInducedDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(12)
    Field: PtLocTempManagement
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: InitialTargetTempGoal
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TargetTempAchievedDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(12)
    Field: RewarmingInitiatedDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: NonUSInsurance
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: BleedingAtrialFibrillation
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: BleedingAtrialFibDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: BleedingAccessSite
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: BleedingAccessSiteDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: BleedingGastrointestinal
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: BleedingGastrointestinalDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: BleedingGenitourinary
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: BleedingGenitourinaryDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: BleedingHematomaAtSite
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: BleedingHematomaAtSiteDateTime
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: BleedingOther
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: BleedingOtherDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: BleedingRetroperitoneal
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: BleedingRetroDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: BleedingSurgicalProc
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: BleedingSurgProcDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: CardiacArrest
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: CardiacArrestDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: CardiogenicShock
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: CardiogenicShockDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: HeartFailure
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: HeartFailureDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: MyocardialInfarction
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MyocardialInfarctionDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: NewRequirementforDialysis
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DialysisDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: BiPAP
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: BiPAPDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: HighFlowOxygen
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: HighFlowOxygenDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: Intubation
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: IntubationDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: StrokeHemorrhagic
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: StrokeHemorrhagicDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: StrokeIschemic
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: StrokeIschemicDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: StrokeUndetermined
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: StrokeUndeterminedDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(14)
    Field: TransIschemicAttack
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TransIschemicAttackDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: VentFib
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: VentFibDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: VentTachy
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: VentTachyDateTime
    PHI: true
    Role: DATE
  - Dtype: bigint
    Field: ELECTROCARDIOROWID
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: ECGCounter
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: ECGDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: StemiFirstNotedFMC
    PHI: false
    Role: null
  - Dtype: bigint
    Field: TROPONINROWID
    PHI: false
    Role: null
  - Dtype: varchar(13)
    Field: TropCount
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: TropCollDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(31)
    Field: TropResDateTime
    PHI: true
    Role: DATE
  - Dtype: varchar(15)
    Field: TropTestLoc
    PHI: false
    Role: null
  - Dtype: varchar(16)
    Field: LabTropAssay
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: POCTROPASSAY
    PHI: false
    Role: null
  - Dtype: varchar(18)
    Field: TroponinValue
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: DataVrsn
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: OriginalOtherId
    PHI: true
    Role: MEDRECNUM
  - Dtype: varchar(100)
    Field: HospName
    PHI: false
    Role: HOSPITAL
  - Dtype: varchar(70)
    Field: FileName
    PHI: false
    Role: null
  - Dtype: varchar(22)
    Field: Version
    PHI: false
    Role: null
  - Dtype: varchar(18)
    Field: DatasetName
    PHI: false
    Role: null
  - Dtype: bigint
    Field: ClientFileId
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: BiomeImportDT
    PHI: false
    Role: null
  - Dtype: varchar(34)
    Field: CareEntityId
    PHI: false
    Role: CAREENTITY
  - Dtype: varchar(34)
    Field: TenantID
    PHI: false
    Role: TENANT
  - Dtype: varchar(20)
    Field: Diabetes
    PHI: false
    Role: null
  - Dtype: varchar(20)
    Field: ArrivalTime
    PHI: false
    Role: TIME
  - Dtype: varchar(20)
    Field: PriorHF
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: CardiacCTAresult
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: EDNPI
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: StateSpecificPlan
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: SymptomDate
    PHI: true
    Role: DATE
  - Dtype: varchar(12)
    Field: SymptomTime
    PHI: false
    Role: TIME
  - Dtype: varchar(12)
    Field: PrivateHealthInsurance
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: RaceAmericanIndianAlaskanNative
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: RaceBlackAfricanAmerican
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: IndianHealthService
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: Medicaid
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: Medicare
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: MiltaryHealthCare
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TropAssayURL
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: InitINRND
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: LipidsTrigND
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: DC_AldoAntag_MedDose
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: EDFName
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: EDLName
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: EDMName
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: LVEFAfterDischarge
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: TRANSFERCABG
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: CAUSEOFDEATH
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: LVEFPLANDAFTERDC
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: CurrentDialysis
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: InsIHS
    PHI: false
    Role: null
  - Dtype: varchar(12)
    Field: Stroke
    PHI: false
    Role: null
  - Dtype: varchar(64)
    Field: CancerRx
    PHI: false
    Role: null
  - Dtype: bigint unsigned
    Field: BiomeEncounterId
    PHI: false
    Role: null
custom_fields:
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Access site
    Derivative Value Code: 1000142440
    Exact Match: 1
    Field: BleedingAccessSite
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Access site
    Derivative Value Code: 1000142440
    Exact Match: 1
    Field: BleedingAccessSiteDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Gastrointestinal
    Derivative Value Code: 74474003
    Exact Match: 1
    Field: BleedingGastrointestinal
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Gastrointestinal
    Derivative Value Code: 74474003
    Exact Match: 1
    Field: BleedingGastrointestinalDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Genitourinary
    Derivative Value Code: 417941003
    Exact Match: 1
    Field: BleedingGenitourinary
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Genitourinary
    Derivative Value Code: 417941003
    Exact Match: 1
    Field: BleedingGenitourinaryDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Atrial fibrillation
    Derivative Value Code: 49436004
    Exact Match: 1
    Field: BleedingAtrialFibrillation
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Atrial fibrillation
    Derivative Value Code: 49436004
    Exact Match: 1
    Field: BleedingAtrialFibDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Other
    Derivative Value Code: 1000142371
    Exact Match: 1
    Field: BleedingOther
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Other
    Derivative Value Code: 1000142371
    Exact Match: 1
    Field: BleedingOtherDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Retroperitoneal
    Derivative Value Code: 95549001
    Exact Match: 1
    Field: BleedingRetroperitoneal
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Retroperitoneal
    Derivative Value Code: 95549001
    Exact Match: 1
    Field: BleedingRetroDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Cardiac arrest
    Derivative Value Code: 410429000
    Exact Match: 1
    Field: CardiacArrest
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Cardiac arrest
    Derivative Value Code: 410429000
    Exact Match: 1
    Field: CardiacArrestDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Surgical procedure or intervention required
    Derivative Value Code: 112000000213
    Exact Match: 1
    Field: BleedingSurgicalProc
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Surgical procedure or intervention required
    Derivative Value Code: 112000000213
    Exact Match: 1
    Field: BleedingSurgProcDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Cardiogenic shock
    Derivative Value Code: 89138009
    Exact Match: 1
    Field: CardiogenicShock
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Cardiogenic shock
    Derivative Value Code: 89138009
    Exact Match: 1
    Field: CardiogenicShockDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Heart failure
    Derivative Value Code: 84114007
    Exact Match: 1
    Field: HeartFailure
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Heart failure
    Derivative Value Code: 84114007
    Exact Match: 1
    Field: HeartFailureDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Myocardial infarction
    Derivative Value Code: 22298006
    Exact Match: 1
    Field: MyocardialInfarction
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Myocardial infarction
    Derivative Value Code: 22298006
    Exact Match: 1
    Field: MyocardialInfarctionDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: New requirement for dialysis
    Derivative Value Code: 100014076
    Exact Match: 1
    Field: NewRequirementforDialysis
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: New requirement for dialysis
    Derivative Value Code: 100014076
    Exact Match: 1
    Field: DialysisDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Respiratory support - Intubation
    Derivative Value Code: 52765003
    Exact Match: 1
    Field: Intubation
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Respiratory support - Intubation
    Derivative Value Code: 52765003
    Exact Match: 1
    Field: IntubationDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Stroke - Hemorrhagic
    Derivative Value Code: 230706003
    Exact Match: 1
    Field: StrokeHemorrhagic
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Stroke - Hemorrhagic
    Derivative Value Code: 230706003
    Exact Match: 1
    Field: StrokeHemorrhagicDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Stroke - Ischemic
    Derivative Value Code: 422504002
    Exact Match: 1
    Field: StrokeIschemic
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Stroke - Ischemic
    Derivative Value Code: 422504002
    Exact Match: 1
    Field: StrokeIschemicDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Stroke - Undetermined
    Derivative Value Code: 230713003
    Exact Match: 1
    Field: StrokeUndetermined
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Stroke - Undetermined
    Derivative Value Code: 230713003
    Exact Match: 1
    Field: StrokeUndeterminedDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Transient ischemic attack (TIA)
    Derivative Value Code: 266257000
    Exact Match: 1
    Field: TransIschemicAttack
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Transient ischemic attack (TIA)
    Derivative Value Code: 266257000
    Exact Match: 1
    Field: TransIschemicAttackDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Ventricular fibrillation
    Derivative Value Code: 71908006
    Exact Match: 1
    Field: VentFib
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Ventricular fibrillation
    Derivative Value Code: 71908006
    Exact Match: 1
    Field: VentFibDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Sustained ventricular tachycardia
    Derivative Value Code: 25569003
    Exact Match: 1
    Field: VentTachy
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Sustained ventricular tachycardia
    Derivative Value Code: 25569003
    Exact Match: 1
    Field: VentTachyDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Hematoma at access site
    Derivative Value Code: 385494008
    Exact Match: 1
    Field: BleedingHematomaAtSite
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Bleeding - Hematoma at access site
    Derivative Value Code: 385494008
    Exact Match: 1
    Field: BleedingHematomaAtSiteDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Respiratory support - Bi-PAP
    Derivative Value Code: 243142003
    Exact Match: 1
    Field: BiPAP
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Respiratory support - Bi-PAP
    Derivative Value Code: 243142003
    Exact Match: 1
    Field: BiPAPDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Respiratory support - High-flow oxygen
    Derivative Value Code: 426854004
    Exact Match: 1
    Field: HighFlowOxygen
    Reference Field: EpiEventOccurred
    Reference Field Code: 1000142479
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: EpiEvent
    Derivative Field Code: 1000142478
    Derivative Value: Respiratory support - High-flow oxygen
    Derivative Value Code: 426854004
    Exact Match: 1
    Field: HighFlowOxygenDateTime
    Reference Field: EpiEventDateTime
    Reference Field Code: 10001424780
    Section: EVENTS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin converting enzyme inhibitor (ACE-I) (Any)
    Derivative Value Code: 41549009
    Exact Match: 1
    Field: Discharge_ACE_Inhibitor_any
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin converting enzyme inhibitor (ACE-I) (Any)
    Derivative Value Code: 41549009
    Exact Match: 1
    Field: DC_ACEInhibitors_Any_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Warfarin
    Derivative Value Code: 11289
    Exact Match: 1
    Field: Discharge_Warfarin
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Warfarin
    Derivative Value Code: 11289
    Exact Match: 1
    Field: DC_Warfarin_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aspirin (Any)
    Derivative Value Code: 1191
    Exact Match: 1
    Field: DischargeASA
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aspirin (Any)
    Derivative Value Code: 1191
    Exact Match: 1
    Field: DC_ASA_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin receptor blocker (ARB) (Any)
    Derivative Value Code: 372913009
    Exact Match: 1
    Field: Discharge_ARB_any
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin receptor blocker (ARB) (Any)
    Derivative Value Code: 372913009
    Exact Match: 1
    Field: DC_ARB_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Prasugrel
    Derivative Value Code: 613391
    Exact Match: 1
    Field: Discharge_Prasugrel
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Prasugrel
    Derivative Value Code: 613391
    Exact Match: 1
    Field: DC_Prasugrel_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Ticagrelor
    Derivative Value Code: 1116632
    Exact Match: 1
    Field: Discharge_Ticagrelor
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Ticagrelor
    Derivative Value Code: 1116632
    Exact Match: 1
    Field: DC_Ticagrelor_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Clopidogrel
    Derivative Value Code: 32968
    Exact Match: 1
    Field: Discharge_Clopidogrel
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Clopidogrel
    Derivative Value Code: 32968
    Exact Match: 1
    Field: DC_Clopidogrel_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Beta blocker (Any)
    Derivative Value Code: 33252009
    Exact Match: 1
    Field: DischargeBetaBlocker
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Beta blocker (Any)
    Derivative Value Code: 33252009
    Exact Match: 1
    Field: DC_BetaBlocker_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin II receptor blocker neprilysin inhibitor (ARNI)
    Derivative Value Code: 112000001832
    Exact Match: 1
    Field: Discharge_ARNI
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Angiotensin II receptor blocker neprilysin inhibitor (ARNI)
    Derivative Value Code: 112000001832
    Exact Match: 1
    Field: DC_ARNI_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aldosterone receptor antagonist (Any)
    Derivative Value Code: 372603003
    Exact Match: 1
    Field: Discharge_ARA_Any
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Aldosterone receptor antagonist (Any)
    Derivative Value Code: 372603003
    Exact Match: 1
    Field: DC_ARA_Any_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Direct oral anticoagulants (DOAC) (Any)
    Derivative Value Code: 112000001416
    Exact Match: 1
    Field: Discharge_DOAC_Any
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Direct oral anticoagulants (DOAC) (Any)
    Derivative Value Code: 112000001416
    Exact Match: 1
    Field: DC_DOAC_Any_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Statin (Any)
    Derivative Value Code: 96302009
    Exact Match: 1
    Field: DischargeStatinAny
    Reference Field: DC_MedAdmin
    Reference Field Code: 432102000
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: DC_MedID
    Derivative Field Code: 100013057
    Derivative Value: Statin (Any)
    Derivative Value Code: 96302009
    Exact Match: 1
    Field: DC_Statin_Any_MedDose
    Reference Field: DC_MedDose
    Reference Field Code: 100014233
    Section: DCMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Atrial Fibrillation
    Derivative Value Code: 49436004
    Exact Match: 1
    Field: HxAFib
    Reference Field: ConditionHxOccurenceArrival
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Atrial Flutter
    Derivative Value Code: 5370000
    Exact Match: 1
    Field: AtrialFlutter
    Reference Field: ConditionHxOccurenceArrival
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Cancer
    Derivative Value Code: 363346000
    Exact Match: 1
    Field: Cancer
    Reference Field: ConditionHxOccurenceArrival
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Dyslipidemia
    Derivative Value Code: 370992007
    Exact Match: 1
    Field: Dyslipidemia
    Reference Field: ConditionHxOccurenceArrival
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Myocardial Infarction
    Derivative Value Code: 22298006
    Exact Match: 1
    Field: PriorMI
    Reference Field: ConditionHxOccurenceArrival
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ConditionHx
    Derivative Field Code: 312850006
    Derivative Value: Peripheral Arterial Disease
    Derivative Value Code: 399957001
    Exact Match: 1
    Field: PriorPAD
    Reference Field: ConditionHxOccurenceArrival
    Reference Field Code: 312850006
    Section: CONDHX
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: ACE Inhibitors
    Derivative Value Code: 41549009
    Exact Match: 1
    Field: HomeMedACEI
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: ARB (Angiotensin Receptor Blockers)
    Derivative Value Code: 372913009
    Exact Match: 1
    Field: HomeMedAngiotensinII
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: ARNI
    Derivative Value Code: 112000001832
    Exact Match: 1
    Field: HomeMedARNI
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Beta Blocker
    Derivative Value Code: 33252009
    Exact Match: 1
    Field: HomeMedBetaBlocker
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: HomeMeds
    Derivative Field Code: 100013057
    Derivative Value: Prasugrel
    Derivative Value Code: 613391
    Exact Match: 1
    Field: HomeMedPrasugrel
    Reference Field: HomeMedPrescrib
    Reference Field Code: 432102000
    Section: HOMEMEDS
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Coronary Artery Bypass Graft
    Derivative Value Code: 232717009
    Exact Match: 1
    Field: PriorCABG
    Reference Field: ProcHxOccurrenceArrival
    Reference Field Code: 416940007
    Section: PROCHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Coronary Artery Bypass Graft
    Derivative Value Code: 232717009
    Exact Match: 1
    Field: PriorCABGDate
    Reference Field: ProcHistDateArrival
    Reference Field Code: 416940007
    Section: PROCHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Percutaneous Coronary Intervention
    Derivative Value Code: 415070008
    Exact Match: 1
    Field: PriorPCI
    Reference Field: ProcHxOccurrenceArrival
    Reference Field Code: 416940007
    Section: PROCHX
    Value Map: null
  - Delimiter: null
    Derivative Field: ProcedHxName
    Derivative Field Code: 416940007
    Derivative Value: Percutaneous Coronary Intervention
    Derivative Value Code: 415070008
    Exact Match: 1
    Field: LastPCIDate
    Reference Field: ProcHistDateArrival
    Reference Field Code: 416940007
    Section: PROCHX
    Value Map: null
  - Delimiter: null
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Medicare
    Derivative Value Code: 1
    Exact Match: 0
    Field: InsMedicare
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      Medicare: 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Medicaid
    Derivative Value Code: 2
    Exact Match: 0
    Field: InsMedicaid
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Medicaid|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Private Health Insurance
    Derivative Value Code: 5
    Exact Match: 0
    Field: PrivateHealthInsurance
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Private Health Insurance|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Private Health Insurance
    Derivative Value Code: 5
    Exact Match: 0
    Field: InsPrivate
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Private Health Insurance|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Military Health Care
    Derivative Value Code: 31
    Exact Match: 0
    Field: MiltaryHealthCare
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Military Health Care|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: State-Specific Plan (non-Medicaid)
    Derivative Value Code: 36
    Exact Match: 0
    Field: StateSpecificPlan
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|State-Specific Plan (non-Medicaid)|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Indian Health Service
    Derivative Value Code: 33
    Exact Match: 0
    Field: IndianHealthService
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Indian Health Service|': 'Yes'
  - Delimiter: '|'
    Derivative Field: HIPS
    Derivative Field Code: 100001072
    Derivative Value: Non-US Insurance
    Derivative Value Code: 100000812
    Exact Match: 0
    Field: NonUSInsurance
    Reference Field: HIPS
    Reference Field Code: 100001072
    Section: EPISODEOFCARE
    Value Map:
      '|Non-US Insurance|': 'Yes'
rename_fields:
  - New Field: Sex
    Old Field: Gender
  - New Field: ZipCode
    Old Field: PatZip
  - New Field: ZipCodeNA
    Old Field: PatZipNA
  - New Field: HeightAD
    Old Field: Height
  - New Field: WeightAD
    Old Field: Weight
  - New Field: MedsArrival
    Old Field: PreProcASA
  - New Field: PCI_Indication
    Old Field: PCIIndication
  - New Field: DCStatus
    Old Field: DischargeStatus
  - New Field: DCLocation
    Old Field: DischargeLocation
  - New Field: DC_Comfort
    Old Field: DischargeComfort
  - New Field: ComMeasDateTime
    Old Field: DCComfortDatetime
  - New Field: DC_CardRehab
    Old Field: DischargeCardRehab
  - New Field: DiagCorAngio1st
    Old Field: DiagCorAngioFirst
  - New Field: CreatPeak
    Old Field: PeakCreat
  - New Field: CE_RBC
    Old Field: CERBC
  - New Field: CE_RBCDate
    Old Field: CERBCdate
  - New Field: CE_RBCCABG
    Old Field: CERBCCABG
  - New Field: TransPCI
    Old Field: TransferPCI
  - New Field: TransCABG
    Old Field: CABGTransferDC
  - New Field: StentTypeFV
    Old Field: StentType
  - New Field: EpisodeKey
    Old Field: EpisodeId
  - New Field: PtType
    Old Field: PatientType
value_mapping:
- Field: cabgfirst2
  Value Map:
    No - No reason: No - No Reason
    No - Medical reason: No - Medical Reason
    No - Patient reason: No - Patient Reason
- Field: cshascalearrival
  Value Map:
    '7: Severely frail': ' 7: Severely frail'
- Field: cshascaledc
  Value Map:
    ' 7: Severely frail': '7: Severely frail'
- Field: ctaperformed
  Value Map:
    No - No reason: No - No Reason
    No - Medical reason: No - Medical Reason
    No - Patient reason: No - Patient Reason
- Field: dc_lvef
  Value Map:
    No - No reason: No - No Reason
    No - Medical reason: No - Medical Reason
    No - Patient reason: No - Patient Reason
- Field: dc_meddose
  Value Map:
    Low: Low Intensity Dose
    Moderate: Moderate Intensity Dose
    High: High Intensity Dose
- Field: dc_statin_any_meddose
  Value Map:
    High: High Intensity Dose
    Moderate: Moderate Intensity Dose
    Low: Low Intensity Dose
- Field: diagcorangiofirst
  Value Map:
    No - No reason: No - No Reason
    No - Medical reason: No - Medical Reason
    No - Patient reason: No - Patient Reason
    No - System reason: No - System Reason
- Field: discharge_ace_inhibitor_any
  Value Map:
    No - No Reason: Not Prescribed - No Reason
    'Yes': Yes - Prescribed
    No - Medical Reason: Not Prescribed - Medical Reason
- Field: discharge_ara_any
  Value Map:
    No - No Reason: Not Prescribed - No Reason
    'Yes': Yes - Prescribed
- Field: discharge_arb_any
  Value Map:
    'Yes': Yes - Prescribed
    No - No Reason: Not Prescribed - No Reason
    No - Medical Reason: Not Prescribed - Medical Reason
- Field: discharge_arni
  Value Map:
    No - No Reason: Not Prescribed - No Reason
    No - Medical Reason: Not Prescribed - Medical Reason
    'Yes': Yes - Prescribed
- Field: discharge_clopidogrel
  Value Map:
    'Yes': Yes - Prescribed
    No - No Reason: Not Prescribed - No Reason
    No - Medical Reason: Not Prescribed - Medical Reason
- Field: discharge_doac_any
  Value Map:
    No - No Reason: Not Prescribed - No Reason
    'Yes': Yes - Prescribed
- Field: discharge_prasugrel
  Value Map:
    No - No Reason: Not Prescribed - No Reason
    No - Medical Reason: Not Prescribed - Medical Reason
    'Yes': Yes - Prescribed
- Field: discharge_ticagrelor
  Value Map:
    No - No Reason: Not Prescribed - No Reason
    'Yes': Yes - Prescribed
    No - Medical Reason: Not Prescribed - Medical Reason
- Field: discharge_warfarin
  Value Map:
    No - No Reason: Not Prescribed - No Reason
    'Yes': Yes - Prescribed
- Field: dischargeasa
  Value Map:
    'Yes': Yes - Prescribed
    No - No Reason: Not Prescribed - No Reason
- Field: dischargebetablocker
  Value Map:
    'Yes': Yes - Prescribed
    No - Medical Reason: Not Prescribed - Medical Reason
- Field: dischargecardrehab
  Value Map:
    No - Reason not documented: No - Reason Not Documented
    No - Medical reason documented: No - Medical Reason Documented
    No - Health care system reason documented: No - Health Care System Reason Documented
    No - Patient-oriented reason: No - Patient - Oriented Reason
- Field: dischargelocation
  Value Map:
    Skilled nursing facility: Skilled Nursing facility
    Extended care/transitional care unit/Rehab: Discharged/transferred to an Extended
      care/TCU/rehab
    Other: Other Discharge Location
- Field: dischargestatinany
  Value Map:
    'Yes': Yes - Prescribed
    No - Medical Reason: Not Prescribed - Medical Reason
- Field: firstfacmeanstrans
  Value Map:
    EMS - Ambulance: Ambulance
    EMS - Air: Air
- Field: initcarhythm
  Value Map:
    Not shockable: Not Shockable
- Field: ischemiaeval
  Value Map:
    No - No reason: No - No Reason
    No - Medical reason: No - Medical Reason
    No - Patient reason: No - Patient Reason
- Field: locfirsteval
  Value Map:
    Emergency department (ED): ED
    Cath lab: Cath Lab
    Observation unit: Observation
- Field: patienttype
  Value Map:
    Unstable angina: Unstable Angina
    Low-risk chest pain: Low-Risk Chest Pain
- Field: pciarrivdc2
  Value Map:
    No - No reason: No - No Reason
    No - Medical reason: No - Medical Reason
    No - Patient reason: No - Patient Reason
- Field: pcidelayreason
  Value Map:
    Difficult vascular access: Difficult Vascular Access
    Emergent placement of left ventricular support device: Emergent placement of LV
      support device before PCI
    Cardiac arrest and/or need for intubation: Cardiac Arrest and/or need for intubation
      before PCI
- Field: pciindication
  Value Map:
    STEMI - Immediate PCI for acute STEMI: "STEMI – Primary PCI for Acute STEMI"
    Unstable angina: Unstable Angina
- Field: ptloctempmanagement
  Value Map:
    Emergency Department: ED
- Field: stenttype
  Value Map:
    Bare Metal Stent: BMS
    Drug-Eluting Stent: DES
- Field: thromtherapyfmc
  Value Map:
    'Yes ': 'Yes'
    No - No reason: No - No Reason
    No - Medical reason: No - Medical Reason
    No - Patient reason: No - Patient Reason
- Field: tobaccouse
  Value Map:
    Unknown: Unknown if ever smoked
pivot_sections:
  - DCMEDS
  - EVENTS
  - CONDHX
  - HOMEMEDS
  - PROCHX
