version: '1.0'
biome_schema:
  - Dtype: varchar(69)
    Field: filename
    PHI: false
    Role: null
  - Dtype: varchar(22)
    Field: version
    PHI: false
    Role: null
  - Dtype: bigint
    Field: clientfileid
    PHI: false
    Role: null
  - Dtype: varchar(34)
    Field: tenantid
    PHI: false
    Role: TENANT
  - Dtype: varchar(30)
    Field: encounternumber
    PHI: true
    Role: IDENTIFIER
  - Dtype: varchar(30)
    Field: cpt4hcpcscode
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: cptseqno
    PHI: false
    Role: null
  - Dtype: varchar(18)
    Field: datasetname
    PHI: false
    Role: null
  - Dtype: varchar(31)
    Field: cptdate
    PHI: true
    Role: '|DATE||ANCHOR_DATE|'
  - Dtype: varchar(15)
    Field: cptmodifier1
    PHI: false
    Role: null
  - Dtype: varchar(14)
    Field: cptmodifier3
    PHI: false
    Role: null
  - Dtype: varchar(15)
    Field: cptmodifier2
    PHI: false
    Role: null
  - Dtype: bigint unsigned
    Field: biomeencounterid
    PHI: false
    Role: null
  - Field: biomeimportdt
    Dtype: varchar(38)
    Role: null
    PHI: false
  - Dtype: varchar(12)
    Field: batchnumber
    PHI: false
    Role: null
cleaner_regex:
    - Field: encounternumber
      Regex: '\.0*$'