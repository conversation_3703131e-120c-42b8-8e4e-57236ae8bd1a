client_fields:
  - prinicd10proccode
  - principlediagnosisicd10poa
  - prindx10code
  - drgcode
  - dischargestatus
  - admittime.1
  - dischargedate
  - admittime
  - admitdate
  - admitsource
  - admittype
  - patienttype
  - patientzip
  - gender
  - dob
  - encounternumber
  - medrecn
  - hospname
  - principalproceduredate
  - primarypayor
  - financialpayorclass
  - primarypayorplan
  - secondarypayorplan
  - admittingphysician
  - admittingphysicianname
  - attendingphysician
  - attendingphysicianname
  - primarycarephysician
  - primarycarephysicianname
  - anesthesiologist
  - anesthesiologistname
  - los
  - dischargeunit
  - directcost
  - indirectcost,,,,
  - dischargetime
  - encounterfacilityname
  - indirectcost
  - netrevenue
rename_fields:
  - New Field: admittime.1
    Old Field: admittime1
  - New Field: patientzip
    Old Field: patzip
  - New Field: indirectcost,,,,
    Old Field: indirectcost
  - New Field: dischargetime
    Old Field: dctime
  - New Field: encounterfacilityname
    Old Field: hospname
  - New Field: netrevenue
    Old Field: netpatientrevenue
required_fields:
  - admitdate
  - admitsource
  - admittime
  - admittype
  - dctime
  - directcost
  - dischargedate
  - dischargestatus
  - dob
  - drgcode
  - encounternumber
  - financialpayorclass
  - gender
  - hospname
  - indirectcost
  - los
  - medrecn
  - netpatientrevenue
  - patienttype
  - patzip
  - primarypayor
  - prindx10code
  - prinicd10proccode