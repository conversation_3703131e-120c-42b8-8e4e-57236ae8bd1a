client_fields:
  - prim_enc_csn_id
  - indirectcost
  - hsp_account_id
  - directcost
  - netrevenue
  - expectedpayment
  - secondarypayorplan
  - primarypayorplan
  - financialpayorclass
  - secondarypayor
  - primarypayor
  - principleprocedureendtime
  - principalproceduredate
  - principleprocedurestarttime
  - prinicdproccode
  - principlediagnosisicdpoa
  - drgcode
  - prinicddxcode
  - icudays
  - dischargestatus
  - dischargeservice
  - dischargeunit
  - dischargetime
  - admitsource
  - primaryadmissionicdcode
  - dischargedate
  - transferringfacility
  - admittype
  - admitservice
  - admissionunit
  - patienttype
  - dischargemdspecialty
  - admitdate
  - admittime
  - dischargemdname
  - dischargemdnpi
  - admittingmdspecialty
  - admittingmdnpi
  - admittingmdname
  - ethnicity
  - patientzip
  - race
  - gender
  - patientlastname
  - dob
  - patientfirstname
  - encounternumber
  - empi
  - medrecn
  - facilitymeicareid (ccn)
  - encounter facility name
  - facilitymedicareid (ccn)
rename_fields:
  - New Field: netrevenue
    Old Field: netpatientrevenue
  - New Field: prinicdproccode
    Old Field: prinicd10proccode
  - New Field: principlediagnosisicdpoa
    Old Field: principlediagnosisicd10poa
  - New Field: prinicddxcode
    Old Field: prindx10code
  - New Field: dischargetime
    Old Field: dctime
  - New Field: primaryadmissionicdcode
    Old Field: admitdx10code
  - New Field: patientzip
    Old Field: patzip
  - New Field: facilitymeicareid (ccn)
    Old Field: facilitymeicareidccn
  - New Field: encounter facility name
    Old Field: hospname
  - New Field: facilitymedicareid (ccn)
    Old Field: facilitymedicareidccn
required_fields:
- admitdate
- admitdx10code
- admitsource
- admittime
- admittype
- dctime
- directcost
- dischargedate
- dischargestatus
- dob
- drgcode
- encounternumber
- ethnicity
- expectedpayment
- financialpayorclass
- gender
- hospname
- icudays
- indirectcost
- medrecn
- netpatientrevenue
- patienttype
- patzip
- primarypayor
- prindx10code
- prinicd10proccode
- race
- secondarypayor