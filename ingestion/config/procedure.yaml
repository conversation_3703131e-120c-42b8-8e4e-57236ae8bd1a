version: '1.0'
biome_schema:
  - Dtype: varchar(75)
    Field: filename
    PHI: false
    Role: null
  - Dtype: varchar(34)
    Field: tenantid
    PHI: false
    Role: TENANT
  - Dtype: varchar(22)
    Field: version
    PHI: false
    Role: null
  - Dtype: int
    Field: clientfileid
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: encounternumber
    PHI: true
    Role: IDENTIFIER
  - Dtype: varchar(20)
    Field: procedureseqno
    PHI: false
    Role: null
  - Dtype: varchar(30)
    Field: proceduredate
    PHI: true
    Role: DATE
  - Dtype: varchar(18)
    Field: datasetname
    PHI: false
    Role: null
  - Dtype: varchar(10)
    Field: procedurecode
    PHI: false
    Role: null
  - Field: biomeencounterid
    Dtype: bigint unsigned
    Role: null
    PHI: false
  - Field: biomeimportdt
    Dtype: varchar(38)
    Role: null
    PHI: false
cleaner_regex:
  - Field: procedurecode
    Regex: '\s.*'
  - Field: encounternumber
    Regex: '\.0*$'