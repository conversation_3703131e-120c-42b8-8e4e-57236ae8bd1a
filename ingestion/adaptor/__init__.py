from abc import ABCMeta, abstractmethod


class Ing(metaclass=ABCMeta):
    """
    Facade pattern for the ingestion process
    """
    def __init__(self):
        self.schema = None
        self.reader = None
        self.translator = None

    @abstractmethod
    def build(self):
        """
        Builds the schema for the dataset to be ingested
        """
        pass

    @abstractmethod
    def read(self):
        """ Reads the xml and creates the tables for the dataset to be ingested """
        pass

    @abstractmethod
    def translate(self):
        """ Translates the dataset to the Biome v1 schema using the schema and the reader """
        pass

    @abstractmethod
    def run(self):
        """ Runs the build, read and translate methods """
        pass
