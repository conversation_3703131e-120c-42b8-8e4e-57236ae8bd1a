import logging

from ingestion.adaptor import Ing
from ingestion.schema.tvt import SchemaTVT30, SchemaTVTFU30
from ingestion.readers.tvt import ReaderTVT30, ReaderTVTFU30
from ingestion.translators.translate_tvt30_to_biome import TranslateTVT30ToBiome
from ingestion.translators.translate_tvtfu30_to_biome import TranslateTVTFU30ToBiome

from ingestion.utils import logger


class TVT30(Ing):
    def __init__(self,
                 client,
                 filepath=None,
                 client_file_id=None,
                 write=False,
                 export_schema=False,
                 rebuild_schema=False,
                 db_as_ncdr_source=False,
                 testing=False):

        Ing.__init__(self)
        self.filepath = filepath
        self.write = write
        self.rebuild_schema = rebuild_schema
        if testing:
            self.db_raw = 'ingestion3_raw'
            self.db_master = 'ingestion3_master'
        else:
            self.db_raw = f'{client}_RAW'
            self.db_master = f'{client}_MASTER'
        self.export_schema = export_schema
        self.schema = None
        self.reader = None
        self.translator = None
        self.db_as_ncdr_source = db_as_ncdr_source
        self.file_id = client_file_id
        self.client = client

    @logger
    def build(self):
        self.schema = SchemaTVT30(rebuild_schema=self.rebuild_schema, target_db=self.db_raw,
                                  export_schema=self.export_schema)
        self.schema.execute()

    @logger
    def read(self):

        if self.filepath is None or self.file_id is None or self.client is None:
            logging.error("Filepath, file_id, and client must be provided to read the file.")
            raise ValueError("Filepath, file_id, and client must be provided to read the file.")

        self.reader = ReaderTVT30(filepath=self.filepath, schema=self.schema, write=self.write,
                                  target_db=self.db_raw, file_id=self.file_id, client=self.client)
        self.reader.execute()

    @logger
    def translate(self):

        if self.filepath is None and not self.db_as_ncdr_source:
            logging.error("Either a filepath or a database as source must be provided.")
            raise ValueError("Either a filepath or a database as source must be provided. "
                             "Set db_as_ncdr_source=True to use the database as source.")

        if self.file_id is None:
            logging.error("A file_id must be provided.")
            raise ValueError("A file_id must be provided.")

        self.translator = TranslateTVT30ToBiome(filepath=self.filepath, schema=self.schema, reader=self.reader,
                                                file_id=self.file_id, client=self.client, write=self.write,
                                                target_db=self.db_master, db_as_source=self.db_as_ncdr_source,
                                                source_db=self.db_raw)
        self.translator.execute()

    @logger
    def run(self):
        self.build()
        self.read()
        self.translate()


class TVTFU30(Ing):
    def __init__(self,
                 client,
                 filepath=None,
                 client_file_id=None,
                 write=False,
                 export_schema=False,
                 rebuild_schema=False,
                 db_as_ncdr_source=False,
                 testing=False):

        Ing.__init__(self)
        self.filepath = filepath
        self.write = write
        self.rebuild_schema = rebuild_schema
        if testing:
            self.db_raw = 'ingestion3_raw'
            self.db_master = 'ingestion3_master'
        else:
            self.db_raw = f'{client}_RAW'
            self.db_master = f'{client}_MASTER'
        self.export_schema = export_schema
        self.schema = None
        self.reader = None
        self.translator = None
        self.db_as_ncdr_source = db_as_ncdr_source
        self.file_id = client_file_id
        self.client = client

    @logger
    def build(self):
        self.schema = SchemaTVTFU30(rebuild_schema=self.rebuild_schema, target_db=self.db_raw,
                                    export_schema=self.export_schema)
        self.schema.execute()

    @logger
    def read(self):

        if self.filepath is None or self.file_id is None or self.client is None:
            logging.error("Filepath, file_id, and client must be provided to read the file.")
            raise ValueError("Filepath, file_id, and client must be provided to read the file.")

        self.reader = ReaderTVTFU30(filepath=self.filepath, schema=self.schema, write=self.write,
                                    target_db=self.db_raw, file_id=self.file_id, client=self.client)
        self.reader.execute()

    @logger
    def translate(self):

        if self.filepath is None and not self.db_as_ncdr_source:
            logging.error("Either a filepath or a database as source must be provided.")
            raise ValueError("Either a filepath or a database as source must be provided. "
                             "Set db_as_ncdr_source=True to use the database as source.")

        if self.file_id is None:
            logging.error("A file_id must be provided.")
            raise ValueError("A file_id must be provided.")

        self.translator = TranslateTVTFU30ToBiome(filepath=self.filepath, schema=self.schema, reader=self.reader,
                                                  file_id=self.file_id, client=self.client, write=self.write,
                                                  target_db=self.db_master, db_as_source=self.db_as_ncdr_source,
                                                  source_db=self.db_raw)
        self.translator.execute()

    @logger
    def run(self):
        self.build()
        self.read()
        self.translate()
