{"cells": [{"cell_type": "code", "execution_count": 34, "id": "9495796c", "metadata": {"scrolled": true}, "outputs": [], "source": ["# !pip install pandas openpyxl"]}, {"cell_type": "code", "execution_count": 35, "id": "d952f9fb-d483-425f-9626-0310efd72034", "metadata": {}, "outputs": [], "source": ["from enum import Enum\n", "import pandas as pd\n", "\n", "def import_excel_to_dataframe(file_path, sheet_name=None):\n", "    \"\"\"\n", "    Import an Excel file into a Pandas DataFrame.\n", "    \n", "    :param file_path: Path to the Excel file.\n", "    :param sheet_name: Name of the sheet to import. If None, it imports the first sheet.\n", "    :return: DataFrame with the imported data.\n", "    \"\"\"\n", "    df = pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl')\n", "    return df\n", "\n", "# Test\n", "file_path = 'CathPCI5.xlsx'\n", "df = import_excel_to_dataframe(file_path)"]}, {"cell_type": "code", "execution_count": 36, "id": "ea5c5861-6651-4129-91a8-9ad4ff417c41", "metadata": {}, "outputs": [], "source": ["section_structure_df = df['Section Containment Structure']\n", "elements_df = df['Elements']\n", "supporting_definitions_df = df['Supporting Definitions']"]}, {"cell_type": "code", "execution_count": 37, "id": "22e6becb-0e67-42dc-8c29-94662935b9eb", "metadata": {}, "outputs": [], "source": ["def get_enum_by_value(enum_class, enum_value):\n", "    for member in enum_class:\n", "        if member.value == enum_value:\n", "            return member\n", "        return None"]}, {"cell_type": "code", "execution_count": 38, "id": "b6ae721b-22e3-4c2b-9db1-138e08cda945", "metadata": {}, "outputs": [], "source": ["section_code_map = {}\n", "section_display_name_map = {}\n", "class SectionType(Enum):\n", "    SECTION = \"Section\"\n", "    REPEATER_SECTION = \"Repeater Section\"\n", "\n", "class Section:\n", "    def __init__(self, name, code, section_type, parent=None):\n", "        \"\"\"\n", "        Initialize a section with a given name, code, and field type.\n", "\n", "        :param name: Section display name.\n", "        :param code: Section code.\n", "        :param section_type: SectionType.SECTION or SectionType.REPEATER_SECTION\n", "        :param parent: Link to the parent node (default is None).\n", "        \"\"\"\n", "        self.name = name\n", "        section_display_name_map[name] = self\n", "        self.code = code\n", "        section_code_map[code] = self\n", "        self.section_type = SectionType.REPEATER_SECTION if section_type == \"Repeater Section\" else SectionType.SECTION\n", "        self.parent = parent\n", "\n", "    def __repr__(self):\n", "        return f\"Node(name={self.name}, code={self.code}, section_type={self.section_type}, parent={self.parent.name} ({self.parent.code}))\"\n", "\n", "    def _get_enclosing_table(self):\n", "        node = self\n", "        parent_path = [self.code]\n", "        while node != section_code_map['ROOT']:\n", "            if node.section_type == SectionType.REPEATER_SECTION:\n", "                return parent_path\n", "            parent_path.append(node.parent.code)\n", "            node = node.parent\n", "        return parent_path\n", "\n", "    def get_enclosing_table_path(self):\n", "        return \" -> \".join(self._get_enclosing_table())\n", "\n", "    def get_enclosing_table(self):\n", "        return self._get_enclosing_table()[-1]\n", "    "]}, {"cell_type": "code", "execution_count": 39, "id": "f6a14ea3-6ec4-42bc-afb4-33f57da49888", "metadata": {}, "outputs": [], "source": ["class SelectionType(Enum):\n", "    SINGLE = 'Single'\n", "    MULTIPLE = 'Multiple'\n", "\n", "class DataSource(Enum):\n", "    USER = 'User'\n", "    AUTOMATIC = 'Automatic'\n", "\n", "class DataType(Enum):\n", "    LAST_NAME = 'LN'\n", "    MIDDLE_NAME = 'MN'\n", "    FIRST_NAME = 'FN'\n", "    PHYSICAL_QUANTITY = 'PQ'\n", "    NUMBER = 'NUM'\n", "    STRING = 'ST'\n", "    BOOLEAN = 'BL'\n", "    CODE = 'CD'\n", "    TIMESTAMP = 'TS'\n", "    DATETIME = 'DT'\n", "    COUNTER = 'CTR'\n", "    TIME = 'TM'\n", "\n", "class MissingDataAction(Enum):\n", "    NO_ACTION = 'No Action'\n", "    REPORT = 'Report'\n", "    ILLEGAL = 'Illegal'\n", "\n", "class CathPCI50_Element:\n", "    def __init__(self,\n", "                 seq_no,\n", "                 name,\n", "                 sect_display_name,\n", "                 sect_code,\n", "                 coding_instructions,\n", "                 target_value,\n", "                 short_name,\n", "                 data_type,\n", "                 precision,\n", "                 unit_of_measure,\n", "                 selection_type,\n", "                 default_value,\n", "                 is_dynamic_list,\n", "                 missing_data,\n", "                 is_harvested,\n", "                 dataset,\n", "                 data_source,\n", "                 is_identifier,\n", "                 is_base_element,\n", "                 is_followup_element,\n", "                 code,\n", "                 code_system,\n", "                 code_system_name,\n", "                 vendor_instruction):\n", "        \"\"\"\n", "        Initialize a CathPCI50_Element.\n", "\n", "        :param seq_no: Element CathPCI 5.0 sequence number.\n", "        :param name: Element display name.\n", "        :param sect_display_name: Element containing section display name.\n", "        :param sect_code: Element containing section code.\n", "        :param coding_instructions: Element coding instructions.\n", "        :param target_value: Element target value.\n", "        :param short_name: <PERSON><PERSON> short name.\n", "        :param data_type: Element data type.\n", "        :param precision: Element precision. \n", "            For a 'string' (ST) data type, this is the max string length. \n", "            For a 'physical quantity' (PQ) data type, this is two comma-separated numbers. The first number is\n", "                the number of digits in the value, and the second number is the the number of digits to the right\n", "                of the decimal point in the number. For example, the number 123.45 would have a precision of (5,2).\n", "            For a 'counter' (CTR) data type, unclear.\n", "            For a 'number' (NUM) data type, this is the number of digits in the value.\n", "            For a 'last name' (LN) data type, this is the max string length.\n", "            For a 'middle name' (MN) data type, this is the max string length.\n", "            For a 'first name' (FN) data type, this is the max string length.\n", "            For other data types, this is not relevant.\n", "        :param unit_of_measure: The unit of measure for an element of 'physical quantity' (PQ) data type.\n", "        :param selection_type: Element selection type, either SINGLE or MULTIPLE\n", "        :param default_value: Element default value.\n", "        :param is_dynamic_list: True if the element is a dynamic list, otherwise False. Unclear what 'dynamic list' means.\n", "        :param missing_data: Action to take if this element's data is missing: REPORT, ILLEGAL, or NO_ACTION\n", "        :param is_harvested: True if the element is harvested, otherwise Flase. Unclear what this means.\n", "        :param dataset: Unclear what this means.\n", "        :param data_source: Source of data, either USER if this value was sent to NCDR or AUTOMATIC if NCDR produced the value (primarily for counters).\n", "        :param is_identifier: True if this element is an identifier for the record, otherwise False.\n", "        :param is_base_element: True if this element is in the base file, otherwise False.\n", "        :param is_followup_element: True if this element is in the followup file, otherwise False.\n", "        :param code: For elements of data type 'code' (CD), the code for the element.\n", "        :param code_system: For elements of data type 'code' (CD), the code system for the element code.\n", "        :param code_system_name: For elements of data type 'code' (CD), the display name for the code system of the element code.\n", "        :param vendor_instruction: NCDR-provided vendor instruction.\n", "        \"\"\"\n", "        self.seq_no = seq_no\n", "        self.name = name\n", "        self.sect_display_name = sect_display_name\n", "        self.sect_code = sect_code\n", "        self.section = section_code_map[sect_code]\n", "        self.coding_instructions = coding_instructions\n", "        self.target_value = target_value\n", "        self.short_name = short_name\n", "        \n", "        if type(data_type) == DataType:\n", "            self.data_type = data_type\n", "        else:\n", "            self.data_type = get_enum_by_value(DataType, data_type)\n", "        \n", "        self.precision = precision\n", "        self.unit_of_measure = unit_of_measure\n", "\n", "        if type(selection_type) == SelectionType:\n", "            self.selection_type = selection_type\n", "        else:\n", "            self.selection_type = get_enum_by_value(SelectionType, selection_type)\n", "\n", "        if default_value == \"Null\":\n", "            self.default_value = None\n", "        else:\n", "            self.default_value = default_value\n", "\n", "        self.is_dynamic_list = True if is_dynamic_list == \"Yes\" else False\n", "\n", "        if type(missing_data) == MissingDataAction:\n", "            self.missing_data = missing_data\n", "        else:\n", "            self.missing_data = get_enum_by_value(MissingDataAction, missing_data)\n", "\n", "        self.is_harvested = True if is_harvested == \"Yes\" else False\n", "        \n", "        self.dataset = dataset\n", "\n", "        if type(data_source) == DataSource:\n", "            self.data_source = data_source\n", "        else:\n", "            self.data_source = get_enum_by_value(DataSource, data_source)\n", "\n", "        self.is_identifier = True if is_identifier == \"Yes\" else False\n", "        self.is_base_element = True if is_base_element == \"Yes\" else False\n", "        self.is_followup_element = True if is_followup_element == \"Yes\" else False\n", "        self.code = code\n", "        self.code_system = code_system\n", "        self.code_system_name = code_system_name\n", "        self.vendor_instruction = vendor_instruction\n", "\n", "        self.supporting_definition = None\n", "        self.supporting_definition_source = None\n", "        supporting_definitions = supporting_definitions_df[supporting_definitions_df['Element Reference'] == seq_no]\n", "        if not supporting_definitions.empty:\n", "            self.supporting_definition = supporting_definitions.iloc[0]['Definition']\n", "            self.supporting_definition_source = supporting_definitions.iloc[0]['Source']\n", "\n", "    def __repr__(self):\n", "        return f\"Element(seq_no={self.seq_no}, name={self.name}, coding_instructions={self.coding_instructions}, supporting_definition={self.supporting_definition}, sect_code={self.sect_code}, enclosing_table={self.get_enclosing_table()})\"\n", "\n", "    def get_enclosing_table(self):\n", "        return self.section.get_enclosing_table()\n", "\n", "    def get_enclosing_table_path(self):\n", "        return self.section.get_enclosing_table_path()"]}, {"cell_type": "code", "execution_count": 40, "id": "7448c078-4c37-42df-916d-3901ce43495a", "metadata": {}, "outputs": [], "source": ["section_code_map = {}\n", "section_display_name_map = {}\n", "\n", "Section('Root', 'ROOT', 'Section', None)\n", "\n", "for idx, row in section_structure_df.iterrows():\n", "    # if row['Container Class'] == 'followupContainer':\n", "    #     continue\n", "    display_name = row['Section Display Name']\n", "    code = row['Section Code']\n", "    section_type = row['Section Type']\n", "    parent_section_name = row['Parent Section']\n", "    parent_section = section_display_name_map[parent_section_name]\n", "    section = Section(display_name, code, section_type, parent_section)\n", "    "]}, {"cell_type": "code", "execution_count": 41, "id": "bc5e3ca7-fe74-409d-8179-fc6c446b8854", "metadata": {}, "outputs": [], "source": ["elements_by_seq_no = {}\n", "\n", "for elem_idx, elem_vals in elements_df.iterrows():\n", "    sect_code = elem_vals['Section Code']\n", "    section = section_code_map[sect_code]\n", "    seq_no = elem_vals['Element Reference']\n", "    element = CathPCI50_Element(\n", "        seq_no,\n", "        elem_vals['Name'],\n", "        elem_vals['Section Display Name'],\n", "        elem_vals['Section Code'],\n", "        elem_vals['Coding Instructions'],\n", "        elem_vals['Target Value'],\n", "        elem_vals['Short Name'],\n", "        elem_vals['Data Type'],\n", "        elem_vals['Precision'],\n", "        elem_vals['Unit Of Measure'],\n", "        elem_vals['Selection Type'],\n", "        elem_vals['Default Value'],\n", "        elem_vals['Is Dynamic List'],\n", "        elem_vals['Missing Data'],\n", "        elem_vals['Is Harvested'],\n", "        elem_vals['Dataset'],\n", "        elem_vals['Data Source'],\n", "        elem_vals['Is Identifier'],\n", "        elem_vals['Is Base Element'],\n", "        elem_vals['Is Followup Element'],\n", "        elem_vals['Code'],\n", "        elem_vals['Code System'],\n", "        elem_vals['Code System Name'],\n", "        elem_vals['Vendor Instruction'])\n", "    elements_by_seq_no[seq_no] = element"]}, {"cell_type": "code", "execution_count": 42, "id": "1f1769c5-8a4f-4d9e-873a-b452f3ed5882", "metadata": {}, "outputs": [{"data": {"text/plain": ["Element(seq_no=2082, name=Race - Filipino, coding_instructions=Indicate if the patient is Filipino as determined by the patient/family.\n", "\n", "Note(s):\n", "If the patient has multiple race origins, specify them using the other race selections in addition to this one., supporting_definition=Having origins in any of the original peoples of the Philippines., sect_code=DEMOGRAPHICS, enclosing_table=ROOT)"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["elements_by_seq_no[2082]"]}, {"cell_type": "code", "execution_count": 43, "id": "017f3a7c-da40-4459-9888-6d1575f68ff8", "metadata": {}, "outputs": [], "source": ["for sect_idx, sect in section_structure_df.iterrows():\n", "    sect_code = sect['Section Code']\n", "    section_elements = elements_df[elements_df['Section Code'] == sect_code]\n", "    for elem_idx, elem_vals in section_elements.iterrows():\n", "        # print(f\"{sect_code}: \\\"{elem_vals['Name']}\\\" ({elem_vals['Element Reference']}) -> {section_code_map[sect_code].get_enclosing_table_path()}\")\n", "        pass"]}, {"cell_type": "code", "execution_count": 45, "id": "42f8bce7-9a84-4d9a-bfec-060a04346023", "metadata": {}, "outputs": [], "source": ["schema_sheets = {'ROOT': []}\n", "\n", "for sect_idx, sect in section_structure_df.iterrows():\n", "    sect_code = sect['Section Code']\n", "    section_elements = elements_df[elements_df['Section Code'] == sect_code]\n", "    section_node = section_code_map[sect_code]\n", "    enclosing_table = section_node.get_enclosing_table()\n", "    if sect_code not in schema_sheets:\n", "        schema_sheets[sect_code] = []\n", "    for elem_idx, element in section_elements.iterrows():\n", "        schema_sheets[sect_code].append(f\"{element['Name']} ({element['Element Reference']})\")"]}, {"cell_type": "code", "execution_count": 47, "id": "8f48760d-9750-476b-bcef-dedbe3ae90dc", "metadata": {}, "outputs": [{"data": {"text/plain": ["['Native Lesion Segment Number (7507)',\n", " 'Native Coronary Vess<PERSON> (7508)',\n", " 'Native Vessel Adjunctive Measurements Obtained (7511)',\n", " 'Native Vessel Fractional Flow Reserve Ratio (7512)',\n", " 'Native Vessel Instantaneous Wave-<PERSON> Ratio (7513)',\n", " 'Native Vessel Intravascular Ultrasonography (7514)',\n", " 'Native Vessel Optical Coherence Tomography (7515)']"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["schema_sheets['NVESSEL']"]}, {"cell_type": "code", "execution_count": 49, "id": "03da97ba-a6d1-46b2-afc8-f06917d43521", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'ROOT': [],\n", " 'DEMOGRAPHICS': ['Last Name (2000)',\n", "  'First Name (2010)',\n", "  'Middle Name (2020)',\n", "  'SSN (2030)',\n", "  'SSN N/A (2031)',\n", "  'Patient ID (2040)',\n", "  'Other ID (2045)',\n", "  'Birth Date (2050)',\n", "  'Sex (2060)',\n", "  'Patient Zip Code (2065)',\n", "  'Zip Code N/A (2066)',\n", "  'Race - White (2070)',\n", "  'Race - Black/African American (2071)',\n", "  'Race - American Indian/Alaskan Native (2073)',\n", "  'Race - Asian (2072)',\n", "  'Race - Asian Indian (2080)',\n", "  'Race - Chinese (2081)',\n", "  'Race - Filipino (2082)',\n", "  'Race - Japanese (2083)',\n", "  'Race - Korean (2084)',\n", "  'Race - Vietnamese (2085)',\n", "  'Race - Other Asian (2086)',\n", "  'Race - Native Hawaiian/Pacific Islander (2074)',\n", "  'Race - Native Hawaiian (2090)',\n", "  'Race - Guamanian or Chamorro (2091)',\n", "  'Race - <PERSON>n (2092)',\n", "  'Race - Other Pacific Islander (2093)',\n", "  'Hispanic or Latino Ethnicity (2076)',\n", "  'Hispanic Ethnicity Type - Mexican, Mexican-American, Chicano (2100)',\n", "  'Hispanic Ethnicity Type - Puerto Rican (2101)',\n", "  'Hispanic Ethnicity Type - Cuban (2102)',\n", "  'Hispanic Ethnicity Type - Other Hispanic, Latino or Spanish Origin (2103)'],\n", " 'EPISODEOFCARE': [],\n", " 'EOCINFO': ['Episode Unique Key (2999)',\n", "  'Arrival Date and Time (3001)',\n", "  'Admitting Provider Last Name (3050)',\n", "  'Admitting Provider First Name (3051)',\n", "  'Admitting Provider Middle Name (3052)',\n", "  'Admitting Provider NPI (3053)',\n", "  'Health Insurance (3005)',\n", "  'Health Insurance Payment Source (3010)',\n", "  'Health Insurance Claim Number (HIC) (3015)',\n", "  'Patient Enrolled in Research Study (3020)',\n", "  'Patient Restriction (3036)'],\n", " 'ATTPROVIDERS': ['Attending Provider Last Name (3055)',\n", "  'Attending Provider First Name (3056)',\n", "  'Attending Provider Middle Name (3057)',\n", "  'Attending Provider NPI (3058)'],\n", " 'RSTUDY': ['Research Study Name (3025)', 'Research Study Patient ID (3030)'],\n", " 'HXANDRISKFACTORS': ['Hypertension (4615)',\n", "  'Dyslipidemia (4620)',\n", "  'Prior Myocardial Infarction (4291)',\n", "  'Most Recent MI Date (4296)',\n", "  'Prior Percutaneous Coronary Intervention (4495)',\n", "  'Most Recent Percutaneous Coronary Intervention Date (4503)',\n", "  'Percutaneous Coronary Intervention of the Left Main Coronary Artery (4501)',\n", "  'Percutaneous Coronary Intervention of the Left Main Coronary Artery Unknown (4502)',\n", "  'Height (6000)',\n", "  'Weight (6005)',\n", "  'Family History of Premature Coronary Artery Disease (4287)',\n", "  'Cerebrovascular Disease (4551)',\n", "  'Peripheral Arterial Disease (4610)',\n", "  'Chronic Lung Disease (4576)',\n", "  'Prior Coronary Artery Bypass <PERSON> (4515)',\n", "  'Most Recent Coronary Artery Bypass Graft Date (4521)',\n", "  'Tobacco Use (4625)',\n", "  'Tobacco Type (4626)',\n", "  'Smoking Amount (4627)',\n", "  'Cardiac Arrest Out of Healthcare Facility (4630)',\n", "  'Cardiac Arrest Witnessed (4631)',\n", "  'Cardiac Arrest After Arrival of Emergency Medical Services (4632)',\n", "  'First Cardiac Arrest Rhythm (4633)',\n", "  'First Cardiac Arrest Rhythm Unknown (4634)',\n", "  'Cardiac Arrest at Transferring Healthcare Facility (4635)',\n", "  '<PERSON><PERSON><PERSON> (4555)',\n", "  'Currently on Dialysis (4560)',\n", "  'Canadian Study of Health and Aging (CSHA) Clinical Frailty Scale (4561)'],\n", " 'PROCINFO': ['Procedure Start Date and Time (7000)',\n", "  'Procedure End Date and Time (7005)',\n", "  'Diagnostic Coronary Angiography Procedure (7045)',\n", "  'Diagnostic Catheterization Operator Last Name (7046)',\n", "  'Diagnostic Catheterization Operator First Name (7047)',\n", "  'Diagnostic Catheterization Operator Middle Name (7048)',\n", "  'Diagnostic Catheterization Operator NPI (7049)',\n", "  'Percutaneous Coronary Intervention (PCI) (7050)',\n", "  'PCI Operator Last Name (7051)',\n", "  'PCI Operator First Name (7052)',\n", "  'PCI Operator Middle Name (7053)',\n", "  'PCI Operator NPI (7054)',\n", "  'Diagnostic Left Heart Cath (7060)',\n", "  'LVEF % (Diagnostic Left Heart Cath) (7061)',\n", "  'Concomitant Procedures Performed (7065)',\n", "  'Concomitant Procedures Performed Type (7066)',\n", "  'Arterial Access Site (7320)',\n", "  'Arterial Cross Over (7325)',\n", "  'Closure Method Not Documented (7332)',\n", "  'Venous Access (7335)',\n", "  'Systolic Blood Pressure (6016)',\n", "  'Cardiac Arrest at this Facility (7340)',\n", "  'Fluoroscopy Time (7214)',\n", "  'Contrast Volume (7215)',\n", "  'Cumulative Air Kerma (7210)',\n", "  'Dose Area Product (7220)'],\n", " 'PREPROCINFO': ['Heart Failure (4001)',\n", "  'New York Heart Association Classification (4011)',\n", "  'Heart Failure Newly Diagnosed (4012)',\n", "  'Heart Failure Type (4013)',\n", "  'Heart Failure Type Unknown (4014)'],\n", " 'DIAGNOSTICTEST': ['Electrocardiac Assessment Method (5037)',\n", "  'Electrocardiac Assessment Results (5032)',\n", "  'New Antiarrhythmic Therapy Initiated Prior to Cath Lab (5033)',\n", "  'Electrocardiac Abnormality Type (5034)',\n", "  'Heart Rate (6011)',\n", "  'Non-Sustained Ventricular Tachycardia Type (5036)',\n", "  'Stress Test Performed (5200)',\n", "  'Cardiac CTA Performed (5220)',\n", "  'Cardiac CTA Date (5226)',\n", "  'Cardiac CTA Results (5227)',\n", "  'Cardiac CTA Results Unknown (5228)',\n", "  'Agatston Calcium Score Assessed (5256)',\n", "  'Agatston Calcium Score (5255)',\n", "  'Agatston Calcium Score Date (5257)',\n", "  'LVEF Assessed (Pre-Procedure) (5111)',\n", "  'LVEF % (Pre-Procedure) (5116)',\n", "  'Prior Diagnostic Coronary Angiography Procedure without intervention (5263)',\n", "  'Prior Diagnostic Coronary Angiography Procedure Date (5264)',\n", "  'Prior Diagnostic Coronary Angiography Procedure Results (5265)',\n", "  'Prior Diagnostic Coronary Angiography Procedure Results Unknown (5266)'],\n", " 'STRESSTEST': ['Stress Test Performed Type (5201)',\n", "  'Stress Test Date (5204)',\n", "  'Stress Test Results (5202)',\n", "  'Stress Test Risk/Extent of Ischemia (5203)'],\n", " 'PREPROCMED': ['PreProcedure Medication Code (6986)',\n", "  'PreProcedure Medication Administered (6991)'],\n", " 'SAQ': ['Q1a: Difficulty walking indoors on level ground (5301)',\n", "  'Q1b: Difficulty gardening, vacuuming or carrying groceries (5302)',\n", "  'Q1c: Difficulty lifting or moving heavy objects (e.g. furniture, children) (5303)',\n", "  'Q2: Had chest pain, chest tightness, or angina (5305)',\n", "  'Q3: Had to take nitroglycerin (Tablets or spray) for  your chest pain, chest tightness or angina (5310)',\n", "  'Q4: Chest pain, chest tightness or angina limited your enjoyment of life (5315)',\n", "  'Q5: How would you feel about this (5320)'],\n", " 'ROSE': ['Rose Dyspnea Scale Question 1 (5330)',\n", "  'Rose Dyspnea Scale Question 2 (5335)',\n", "  'Rose Dyspnea Scale Question 3 (5340)',\n", "  'Rose Dyspnea Scale Question 4 (5345)'],\n", " 'CLMETHOD': ['Closure Device Counter (7330)',\n", "  'Arterial Access Closure Method (7331)',\n", "  'Closure Method Unique Device Identifier (7333)'],\n", " 'LABS': [],\n", " 'PREPROCLABS': ['PreProcedure Troponin I  (6090)',\n", "  'PreProcedure Troponin I Not Drawn  (6091)',\n", "  'Troponin T (Pre-Procedure) (6095)',\n", "  'Troponin T Not Drawn (Pre-Procedure) (6096)',\n", "  'C<PERSON><PERSON><PERSON> (6050)',\n", "  '<PERSON><PERSON><PERSON><PERSON> (6051)',\n", "  'Hemoglobin (6030)',\n", "  'Hemoglobin Not Drawn (6031)',\n", "  'Total Cholesterol (6100)',\n", "  'Total Cholesterol Not Drawn (6101)',\n", "  'High-density Lipoprotein (6105)',\n", "  'High-density Lipoprotein Not Drawn (6106)'],\n", " 'POSTPROCLABS': ['PostProcedure Troponin I  (8515)',\n", "  'PostProcedure Troponin I Not Drawn (8516)',\n", "  'Troponin T (Post-Procedure) (8520)',\n", "  'Troponin T Not Drawn (Post-Procedure) (8521)',\n", "  '<PERSON><PERSON><PERSON><PERSON> (8510)',\n", "  '<PERSON><PERSON><PERSON><PERSON> (8511)',\n", "  'Hemoglobin (8505)',\n", "  'Hemoglobin Not Drawn (8506)'],\n", " 'LABVISIT': ['Indications for Cath Lab Visit (7400)',\n", "  'Chest Pain Symptom Assessment (7405)',\n", "  'Cardiovascular Instability (7410)',\n", "  'Cardiovascular Instability Type (7415)',\n", "  'Ventricular Support (7420)',\n", "  'Pharmacologic Vasopressor Support (7421)',\n", "  'Mechanical Ventricular Support (7422)',\n", "  'Mechanical Ventricular Support Device (7423)',\n", "  'Mechanical Ventricular Support Timing (7424)',\n", "  'Evaluation for Surgery Type (7465)',\n", "  'Functional Capacity (7466)',\n", "  'Functional Capacity Unknown (7467)',\n", "  'Surgical Risk (7468)',\n", "  'Solid Organ Transplant Surgery (7469)',\n", "  'Solid Organ Transplant Donor (7470)',\n", "  'Solid Organ Transplant Type (7471)'],\n", " 'VALVULARDZSTEN': ['Valvular Disease Stenosis Type (7450)',\n", "  'Valvular Disease Stenosis Severity (7451)'],\n", " 'VALVULARDZREGURG': ['Valvular Disease Regurgitation Type (7455)',\n", "  'Valvular Disease Regurgitation Severity (7456)'],\n", " 'CORANATOMY': ['Coronary Circulation Dominance (7500)',\n", "  'Native Vessel with Stenosis >= 50% (7505)',\n", "  '<PERSON>t Vessel with Stenosis >= 50% (7525)'],\n", " 'NVESSEL': ['Native Lesion Segment Number (7507)',\n", "  'Native Coronary Vess<PERSON> (7508)',\n", "  'Native Vessel Adjunctive Measurements Obtained (7511)',\n", "  'Native Vessel Fractional Flow Reserve Ratio (7512)',\n", "  'Native Vessel Instantaneous Wave-<PERSON> Ratio (7513)',\n", "  'Native Vessel Intravascular Ultrasonography (7514)',\n", "  'Native Vessel Optical Coherence Tomography (7515)'],\n", " 'GVESSEL': ['Graft Lesion Segment Number (7527)',\n", "  'Graft Coronary Vessel <PERSON> (7528)',\n", "  'CAB<PERSON> (7529)',\n", "  'CABG Graft Vessel Unknown (7530)',\n", "  'Graft Vessel Adjunctive Measurements Obtained (7531)',\n", "  'Graft Vessel Fractional Flow Reserve Ratio (7532)',\n", "  'Graft Vessel Instantaneous Wave-Free Ratio (7533)',\n", "  'Graft Vessel Intravascular Ultrasonography (7534)',\n", "  'Graft Vessel Optical Coherence Tomography (7535)'],\n", " 'PCIPROC': ['PCI Status (7800)',\n", "  'Hypothermia Induced (7806)',\n", "  'Hypothermia Induced Tim<PERSON> (7807)',\n", "  'Level of Consciousness (PCI Procedure) (7810)',\n", "  'Decision for PCI with Surgical Consult (7815)',\n", "  'Cardiovascular Treatment Decision (7816)',\n", "  'PCI for MultiVessel Disease (7820)',\n", "  'Multi-vessel Procedure Type (7821)',\n", "  'Percutaneous Coronary Intervention Indication (7825)',\n", "  'Acute Coronary Syndrome Symptom Date (7826)',\n", "  'Acute Coronary Syndrome Symptom Time (7827)',\n", "  'Acute Coronary Syndrome Symptom Time Unknown (7828)',\n", "  'Thrombolytics (7829)',\n", "  'Thrombolytic Therapy Date and Time (7830)',\n", "  'Syntax Score (7831)',\n", "  'Syntax Score Unknown (7832)',\n", "  'STEMI or STEMI Equivalent First Noted (7835)',\n", "  'Subsequent ECG with STEMI or STEMI Equivalent Date and Time (7836)',\n", "  'Subsequent ECG obtained in Emergency Department (7840)',\n", "  'Patient Transferred In for Immediate PCI for STEMI (7841)',\n", "  'Emergency Department Presentation at Referring Facility Date and Time (7842)',\n", "  'First Device Activation Date and Time (7845)',\n", "  'Patient Centered Reason for Delay in PCI (7850)',\n", "  'Patient Centered Reason for Delay in PCI Reason (7851)'],\n", " 'PROCMED': ['PCI Procedure Medication Code (7990)',\n", "  'Procedure Medications Administered (7995)'],\n", " 'LESIONDEV': ['Lesion Counter (8000)',\n", "  'Native Lesion Segment Number (8001)',\n", "  '<PERSON><PERSON><PERSON> (8002)',\n", "  '<PERSON><PERSON><PERSON> Unknown (8003)',\n", "  'Stenosis Immediately Prior to Treatment (8004)',\n", "  'Chronic Total Occlusion (8005)',\n", "  'Chronic Total Occlusion Unknown (8006)',\n", "  'TIMI Flow (Pre-Intervention) (8007)',\n", "  'Previously Treated Lesion (8008)',\n", "  'Previously Treated Lesion Date (8009)',\n", "  'Treated with <PERSON><PERSON> (8010)',\n", "  'In-stent <PERSON> (8011)',\n", "  'In-stent Thrombosis (8012)',\n", "  'Stent Type (8013)',\n", "  'Stent Type Unknown (8014)',\n", "  'Lesion In Graft (8015)',\n", "  'Type of CABG Graft (8016)',\n", "  'Location in Graft (8017)',\n", "  'Navigate through Graft to Native Lesion (8018)',\n", "  'Lesion Complexity (8019)',\n", "  'Lesion Length (8020)',\n", "  'Severe Calcification (8021)',\n", "  'Bifurcation Lesion (8022)',\n", "  'Guidewire Across Lesion (8023)',\n", "  'Devi<PERSON> Deployed (8024)',\n", "  'Stenosis (Post-Intervention) (8025)',\n", "  'TIMI Flow (Post-Intervention) (8026)'],\n", " 'DEVICES': ['Intracoronary Device Counter (8027)',\n", "  'Intracoronary Device(s) Used (8028)',\n", "  'Intracoronary Unique Device Identifier (8029)',\n", "  'Intracoronary Device Associated Lesion (8030)',\n", "  'Intracoronary <PERSON> (8031)',\n", "  'Intracoronary Device Length (8032)'],\n", " 'INTPOSTEVENT': ['Coronary Artery Perforation (9145)',\n", "  'Significant Coronary Artery Dissection (9146)',\n", "  'Packed Red Blood Cell Transfusion (9275)',\n", "  'Number of units of PRBCs transfused (9276)',\n", "  'Transfusion PCI (9277)',\n", "  'Transfusion Surgery (9278)'],\n", " 'IPPEVENTS': ['Intra/Post-Procedure Events (9001)',\n", "  'Intra/Post-Procedure Events Occurred (9002)',\n", "  'Intra/Post-Procedure Event Date and Time (9003)'],\n", " 'DISCHARGE': ['Interventions this Hospitalization (10030)',\n", "  'Intervention Type this Hospitalization (10031)',\n", "  'CABG Status (10035)',\n", "  'CABG Indication (10036)',\n", "  'Coronary Artery Bypass Graft Date and Time (10011)',\n", "  'C<PERSON><PERSON><PERSON> (10060)',\n", "  '<PERSON><PERSON><PERSON><PERSON> (10061)',\n", "  'Hemoglobin (10065)',\n", "  'Hemoglobin Not Drawn (10066)',\n", "  'Discharge Date and Time (10101)',\n", "  'Discharge Provider Last Name (10070)',\n", "  'Discharge Provider First Name (10071)',\n", "  'Discharge Provider Middle Name (10072)',\n", "  'Discharge Provider NPI (10073)',\n", "  'Comfort Measures Only (10075)',\n", "  'Discharge Status (10105)',\n", "  'Discharge Location (10110)',\n", "  'Transferred for CABG (10111)',\n", "  'CABG Planned after <PERSON>harge (10112)',\n", "  'Hospice Care (10115)',\n", "  'Cardiac Rehabilitation Referral (10116)',\n", "  'Level of Consciousness (Discharge) (10117)',\n", "  'Death During the Procedure (10120)',\n", "  'Cause of Death (10125)',\n", "  'Discharge Medication Reconciliation Completed (10220)',\n", "  'Discharge Medications Reconciled (10221)'],\n", " 'DISCHMED': ['Discharge Medication Code (10200)',\n", "  'Discharge Medication Prescribed (10205)',\n", "  'Discharge Medication Dose (10207)',\n", "  '<PERSON><PERSON> Rationale for not taking medication (10206)'],\n", " 'FUP': ['Follow-Up Unique Key (10999)',\n", "  'Follow-Up Assessment Date (11000)',\n", "  'Follow-Up Reference Procedure Start Date and Time (11001)',\n", "  'Follow-Up Reference Episode Arrival Date and Time (11002)',\n", "  'Follow-Up Reference Episode Discharge Date and Time (11015)',\n", "  'Method to Determine Follow-Up Status (11003)',\n", "  'Follow-Up Status (11004)',\n", "  'Chest Pain Symptom Assessment (11005)',\n", "  'Follow-Up Date of Death (11006)',\n", "  'Cause of Death (11007)',\n", "  'Patient Enrolled in Research Study (11008)'],\n", " 'FUP-RSTUDY': ['Research Study Name (11009)',\n", "  'Research Study Patient ID (11010)'],\n", " 'FUP-EVENT': ['Follow-Up Events (11011)',\n", "  'Follow-Up Events Occurred (11012)',\n", "  'Follow-Up Devices Event Occurred In (11013)',\n", "  'Follow-Up Event Dates (11014)'],\n", " 'FUP-MEDPRES': ['Follow-Up Medications Code (11990)',\n", "  'Follow-Up Medications Prescribed (11995)',\n", "  'Follow-Up Medication Dose (11996)'],\n", " 'FUP-SAQ': ['Q1a: Difficulty walking indoors on level ground (11301)',\n", "  'Q1b: Difficulty gardening, vacuuming or carrying groceries (11302)',\n", "  'Q1c: Difficulty lifting or moving heavy objects (e.g. furniture, children) (11303)',\n", "  'Q2: Had chest pain, chest tightness, or angina (11305)',\n", "  'Q3: Had to take nitroglycerin (Tablets or spray) for  your chest pain, chest tightness or angina (11310)',\n", "  'Q4: Chest pain, chest tightness or angina limited your enjoyment of life (11315)',\n", "  'Q5: How would you feel about this (11320)'],\n", " 'FUP-ROSE': ['Rose Dyspnea Scale Question 1 (11330)',\n", "  'Rose Dyspnea Scale Question 2 (11335)',\n", "  'Rose Dyspnea Scale Question 3 (11340)',\n", "  'Rose Dyspnea Scale Question 4 (11345)'],\n", " 'ADMIN': ['Participant ID (1000)',\n", "  'Participant Name (1010)',\n", "  'Time Frame of Data Submission (1020)',\n", "  'Transmission Number (1040)',\n", "  'Vendor Identifier (1050)',\n", "  'Vendor Software Version (1060)',\n", "  'Registry Identifier (1070)',\n", "  'Registry Schema Version (1071)',\n", "  'Submission Type (1085)']}"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["schema_sheets"]}, {"cell_type": "code", "execution_count": 51, "id": "dbede2ef", "metadata": {}, "outputs": [{"data": {"text/plain": ["'LABVISIT -> PROCINFO'"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["elements_by_seq_no[7424].get_enclosing_table_path()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 5}