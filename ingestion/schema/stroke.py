import pandas as pd
from ingestion.utils import logging

from ingestion.data_dictionaries import read_data_dict
from ingestion.utils.db import execute_sql


class SchemaStroke:

    DATA_DICT = 'Stroke.xlsx'
    DATASET = 'Stroke'
    DATASET_ID = 125
    VERSION = '1'
    TABLE_PREFIX = 'Stroke'
    REGISTRY_SUBSET = 'STROKE__DEFAULT'
    ANCHOR_DATE_FIELD = 'dcdatetime'

    FILE_INFO_COLS = [
        {'Name': 'ClientFileId', 'Data Type': 'varchar(20)'},
        {'Name': 'FileName', 'Data Type': 'text'},
        {'Name': 'BiomeImportDt', 'Data Type': 'varchar(64)'},
        {'Name': 'Version', 'Data Type': 'int'},
        {'Name': 'DatasetName', 'Data Type': 'varchar(64)'},
    ]

    def __init__(self, rebuild_schema=False, target_db='DB_EARASS', export_schema=False):
        self.rebuild_schema = rebuild_schema
        self.target_db = target_db
        self.export_schema = export_schema
        self.schema = read_data_dict(filename=SchemaStroke.DATA_DICT, sheet_name='Fields')

    def pre_process(self):
        self.schema['Name'] = self.schema['Name'].str.upper()
        self.schema['Data Type'] = self.schema['Data Type'].fillna('text')

        self.schema = pd.concat([self.schema, pd.DataFrame(SchemaStroke.FILE_INFO_COLS)], ignore_index=True)

    def create_table_sql(self):
        cols = []
        for _, row in self.schema.iterrows():
            col = f'`{row["Name"]}` {row["Data Type"]} DEFAULT NULL'
            cols.append(col)
        return f'DROP TABLE IF EXISTS {self.TABLE_PREFIX};' \
               f'CREATE TABLE {self.TABLE_PREFIX} ({",".join(cols)}) ENGINE=MyISAM DEFAULT CHARSET=latin1;'

    def execute(self):
        self.pre_process()
        create_table_sql = self.create_table_sql()
        if self.rebuild_schema:
            logging.info(f'Dropping and creating table {self.TABLE_PREFIX}')
            logging.info(create_table_sql)
            execute_sql(create_table_sql, db=self.target_db, multi_statements=True)
