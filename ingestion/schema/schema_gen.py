import json
import warnings
import pandas as pd
import numpy as np

from ingestion.data_dictionaries import read_data_dict
from ingestion.data_dictionaries_json import write_data_dict
from ingestion.utils.db import execute_sql


def create_grouped_list(data, name):
    return pd.DataFrame(data.groupby(data.index).apply(lambda group: group.to_dict(orient='records')), columns=[name])


class BuildSchema:

    def __init__(self, data_dict_filepath, dataset, dataset_id, version, rebuild_schema=False, target_db='DB_EARASS',
                 export_schema=False, table_prefix=None, custom_dtype=None, is_followup=False, custom_fields=None):

        if rebuild_schema:
            warnings.warn('Rebuilding schema will drop existing tables and create new ones. '
                          'Please make sure you have taken necessary backups.', UserWarning)

        self.data_dict_filepath = data_dict_filepath
        self.dataset = dataset
        self.dataset_id = dataset_id
        self.version = version
        self.output = pd.DataFrame()
        self.sections = pd.DataFrame()
        self.elements = pd.DataFrame()
        self.selections = pd.DataFrame()
        self.definitions = pd.DataFrame()
        self.ranges = pd.DataFrame()
        self.parent_child_valid = pd.DataFrame()
        self.rebuild_schema = rebuild_schema
        self.target_db = target_db
        self.export_schema = export_schema
        self.table_prefix = table_prefix
        self.is_followup = is_followup
        self.custom_dtype = custom_dtype  # To specify dtype for a specific field that does not align with data dict
        self.custom_fields = custom_fields  # To specify custom fields to be added to the schema

        self.dtype_map = {
            'LN': 'varchar',
            'FN': 'varchar',
            'MN': 'varchar',
            'ST': 'varchar',
            'BL': 'varchar',
            'NUM': 'int',
            'DT': 'date',
            'CD': 'varchar',
            'TS': 'datetime',
            'PQ': 'decimal',
            'CTR': 'varchar',
            'TM': 'time',
        }
        self.id_cols = pd.DataFrame(
            [
                {'name': 'NCDRPatientId', 'dtype': 'bigint'},
                {'name': 'EpisodeKey', 'dtype': 'text'},
                {'name': 'VisitId', 'dtype': 'tinyint'},
                {'name': 'IncrementalId', 'dtype': 'tinyint'},
                {'name': 'ClientFileId', 'dtype': 'text'},
                {'name': 'FileName', 'dtype': 'text'},
                {'name': 'Version', 'dtype': 'int'},
                {'name': 'BiomeImportDt', 'dtype': 'text'},
                {'name': 'DatasetName', 'dtype': 'varchar(64)'},
                {'name': 'PartId', 'dtype': 'text'},
                {'name': 'PartName', 'dtype': 'text'}
            ]
        )

    def create_section_tables(self):
        """
        Creates a table for each section based on the data dictionary information and following the rules below:

        1. If a section is repeating or has a cardinality of n, it will have its own table
        2. If a section is not repeating and has a parent section, it will be part of the parent section's table
        3. If a section is not repeating and has no parent section, it will be part of the base section's table
        4. DEMOGRAPHICS section will have its own table
        5. ADMIN section will have its own table
        """
        self.sections['Table'] = ''

        base_sec = 'EPISODEOFCARE' if not self.is_followup else 'FOLLOWUP'

        repeating_sections = self.sections[
            (self.sections['Cardinality'].str.contains('n')) | (self.sections['Section Type'] == 'Repeater Section')]

        self.sections.loc[repeating_sections.index, 'Table'] = repeating_sections['Section Display Name']

        root_non_repeating_sections = self.sections[
            (~self.sections['Section Display Name'].isin(repeating_sections['Section Display Name'])) & (
                self.sections['Parent Section'] == 'Root')]

        self.sections.loc[root_non_repeating_sections.index, 'Table'] = base_sec

        non_root_non_repeating_sections = self.sections[
            (~self.sections['Section Display Name'].isin(repeating_sections['Section Display Name'])) & (
                self.sections['Parent Section'].isin(self.sections['Section Display Name']))]

        # to map sections to the parent section's table
        # if the parent section comes down in the iteration, the while loop is there to reiterate
        # until all sections are mapped
        sections_to_map = non_root_non_repeating_sections['Section Display Name'].tolist()
        if 'ADMIN' in sections_to_map:
            sections_to_map.remove('ADMIN')
        while len(sections_to_map) > 0:
            non_root_non_repeating_sections = non_root_non_repeating_sections[
                non_root_non_repeating_sections['Section Display Name'].isin(sections_to_map)
            ]
            for idx, row in non_root_non_repeating_sections.iterrows():
                parent_section = row['Parent Section']
                section_display_name = row['Section Display Name']
                parent_table = self.sections.loc[
                    self.sections['Section Display Name'] == parent_section, 'Table'].iloc[0]
                self.sections.at[idx, 'Table'] = parent_table
                if parent_table not in ['nan', '', 'None']:
                    # remove the section from the list if it has been mapped to a table
                    sections_to_map.remove(section_display_name)

        display_name_to_code = self.sections.set_index('Section Display Name')['Section Code'].to_dict()
        self.sections['Table'] = self.sections['Table'].map(display_name_to_code).fillna(self.sections['Table'])

        # set table name DEMOGRAPHICS for the section with code DEMOGRAPHICS
        self.sections.loc[self.sections['Section Code'] == 'DEMOGRAPHICS', 'Table'] = 'DEMOGRAPHICS'

        # set table name ADMIN for the section with code ADMIN
        self.sections.loc[self.sections['Section Code'] == 'ADMIN', 'Table'] = 'ADMIN'

    def join_info(self):
        """
        Joins the data dictionary information to create a single dataframe.
        :return:
        """
        admin_sec = self.sections.loc[
            self.sections['Container Class'] == 'submissionInfoContainer', 'Section Code'].iloc[0]

        df_section_struct = self.sections[self.sections['Section Code'] != admin_sec]

        df = df_section_struct.merge(self.elements,
                                     on='Section Code', how='inner').set_index('Element Reference')

        self.output = df.join(self.selections, lsuffix=' Elements', rsuffix=' Selections').join(
            self.definitions).join(self.ranges, lsuffix=' Elements', rsuffix=' Ranges')

        self.output.drop(['Section Display Name_x'], axis=1, inplace=True)
        self.output = self.output.rename(columns={'Section Display Name_y': 'Section Display Name'})

    def create_dtypes(self, data):
        """
        Creates sql data types for the fields based on the data type and precision of the field from the data dictionary
        """
        cond = [
            (data['Data Type'].isin(['LN', 'FN', 'MN', 'ST', 'CD', 'CTR', 'BL'])) & (data['Precision'].isna()),
            data['Data Type'].isin(['DT', 'TS', 'TM'])
        ]
        choices = ['(128)', '']
        data['precision_new'] = np.select(cond, choices, default='(' + data['Precision'] + ')')

        data['dtypes'] = data['Data Type'].map(self.dtype_map) + data['precision_new'].fillna('')

        # if custom_dtype is provided, replace the dtype for the specific field
        if self.custom_dtype:
            for field, dtype in self.custom_dtype.items():
                data.loc[data['Short Name'] == field, 'dtypes'] = dtype

        data.drop(['precision_new'], axis=1)
        return data['dtypes']

    def get_unit_fields_if_any(self, data):
        """
        Returns the fields for units if any
        """
        df = data.dropna(subset=['Unit Of Measure Elements'])
        if df.empty:
            return []
        else:
            comments = 'comment "Units for ' + self.create_field_comment(df) + '"'
            return (df['Short Name'] + '_unit' + ' varchar(15) CHARACTER SET utf8mb4 ' + comments).tolist()

    def get_custom_fields(self, table):
        """
        Returns the custom fields to be added to the schema
        """
        fields = []
        if self.custom_fields:
            for field in self.custom_fields:
                if field['table'].lower() == table.lower().replace(self.table_prefix.lower(), ''):
                    fields.append(field['name'] + ' ' + field['dtype'])
            return fields
        else:
            return []

    @staticmethod
    def create_field_comment(data):
        comments = 'Reference: ' + data['Element Reference'].astype(str)
        return comments

    def create_table_stmt(self, table, data):
        """
        Creates sql statement for creating table based on the fields from the data dictionary
        """
        if self.table_prefix:
            table = self.table_prefix + table
        table = table.replace(' ', '').replace('.', '').replace('-', '')
        data = data.drop_duplicates(subset=['Short Name']).reset_index()
        data = data[~data['Short Name'].str.lower().isin(self.id_cols['name'].str.lower())]
        dtype = self.create_dtypes(data)
        comments = self.create_field_comment(data)
        data['columns'] = data['Short Name'] + ' ' + dtype + ' comment "' + comments + '"'
        id_cols = (self.id_cols['name'] + ' ' + self.id_cols['dtype']).tolist()
        cols = '\n,'.join(
            id_cols + data['columns'].tolist() + self.get_unit_fields_if_any(data) + self.get_custom_fields(table)
        )
        sql = f"DROP TABLE IF EXISTS {table}; Create table if not exists {table} (\n{cols}\n);"
        return sql

    def create_table_stmts(self):
        return '\n'.join([self.create_table_stmt(table, data) for table, data in self.output.groupby('Table')])

    def export_schema_json(self, is_test=False):
        """
        Exports the schema to a json file with the dataset and version as the name of the file in the current directory
        """
        selections = create_grouped_list(data=self.selections, name='Selections')
        ranges = create_grouped_list(data=self.ranges, name='Ranges')

        definitions = create_grouped_list(data=self.definitions, name='Definition')
        # assuming definitions have one-to-one relation with elements, so keeping first dict only
        definitions['Definition'] = definitions['Definition'].str[0]

        parent_child_valid = create_grouped_list(data=self.parent_child_valid, name='Parent Child Validations')

        elements = self.elements.set_index('Element Reference')
        elements = elements.join(selections).join(ranges).join(definitions).join(parent_child_valid).reset_index()

        elements = create_grouped_list(data=elements.set_index('Section Code'), name='Elements')
        df = self.sections.join(elements, on='Section Code')
        data = {
            'Dictionary': {
                'Dataset': {
                    'Code': self.dataset,
                    'Id': self.dataset_id
                },
                'Version': self.version,
                'Type': 'Vendor',
                'Sections': json.loads(df.to_json(orient='records'))
            }
        }
        if not is_test:
            write_data_dict(f'{self.dataset}_{self.version}.json', data)
        else:
            return data

    def execute(self):
        self.sections = read_data_dict(self.data_dict_filepath, sheet_name='Section Containment Structure')

        # drop followup containers from sections if not followup, otherwise drop episode containers
        if not self.is_followup:
            self.sections = self.sections[self.sections['Container Class'] != 'followupContainer']
        else:
            self.sections = self.sections[self.sections['Container Class'] != 'episodeContainer']

        self.elements = read_data_dict(self.data_dict_filepath, sheet_name='Elements')
        self.selections = read_data_dict(self.data_dict_filepath, sheet_name='Selections').set_index(
            'Element Reference')
        self.definitions = read_data_dict(self.data_dict_filepath,
                                          sheet_name='Supporting Definitions').set_index(
            'Element Reference')
        self.definitions.drop(['Display Order'], axis=1, inplace=True)  # Display Order is constant i.e. 1
        self.ranges = read_data_dict(self.data_dict_filepath, sheet_name='Element Ranges').set_index(
            'Element Reference')
        self.parent_child_valid = read_data_dict(
            self.data_dict_filepath, sheet_name='Parent Child Validations').set_index('Element Reference')

        self.create_section_tables()
        self.join_info()

        if self.export_schema:
            self.export_schema_json()

        sql = self.create_table_stmts()
        if self.rebuild_schema:
            execute_sql(sql, db=self.target_db, multi_statements=True)
        return sql
