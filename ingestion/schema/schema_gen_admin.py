from dataclasses import dataclass
import pandas as pd
import numpy as np

from ingestion.utils.db import execute_sql
from ingestion.utils import logging


@dataclass
class BuildSchemaAdmin:

    """
    Base class to build schema for admin tables
    """

    fields: pd.DataFrame
    file_info_cols: list
    table_prefix: str
    rebuild: bool = False
    target_db: str = 'DB_EARASS'
    export: bool = False

    def pre_process(self):
        self.fields['Data Type'] = self.fields['Data Type'].fillna('text')
        self.fields['Precision'] = np.where(self.fields['Data Type'].str.contains('varchar', case=False), '(255)', '')

        self.fields = pd.concat([self.fields, pd.DataFrame(self.file_info_cols)], ignore_index=True)
        self.fields['Name'] = self.fields['Name'].str.upper()
        self.fields = self.fields.drop_duplicates(subset='Name')

    def create_table_sql(self):
        cols = []
        for _, row in self.fields.iterrows():
            col = f'`{row["Name"].lower()}` {row["Data Type"]} DEFAULT NULL'
            cols.append(col)
        idx = 'INDEX idx_file (`ClientFileId`, `FileName`, `Version`)'
        return f'DROP TABLE IF EXISTS `{self.table_prefix}`;' \
               f'CREATE TABLE `{self.table_prefix}` ({",".join(cols)}, {idx}) ENGINE=InnoDB DEFAULT CHARSET=latin1;'

    def execute(self):
        self.pre_process()
        create_table_sql = self.create_table_sql()
        if self.rebuild:
            logging.info(f'Dropping and creating table {self.table_prefix}')
            logging.info(create_table_sql)
            execute_sql(create_table_sql, db=self.target_db, multi_statements=True)
