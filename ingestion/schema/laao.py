import pandas as pd
from ingestion.schema.schema_gen import BuildSchema


class SchemaLAAO14(BuildSchema):

    DATA_DICT = 'LAAO14.xlsx'
    DATASET = 'LAAO'
    DATASET_ID = 110
    VERSION = '1.4'
    TABLE_PREFIX = 'LAAO_14_'
    REGISTRY_SUBSET = 'LAAO__DEFAULT'
    ANCHOR_DATE_FIELD = 'dischargedate'

    def __init__(self, rebuild_schema=False, target_db='DB_EARASS', export_schema=False):

        # adding EventId field in the IPPEVENTS table to have it available in the translated output
        custom_fields = [
            {'name': 'EventId', 'dtype': 'text', 'table': 'IPPEVENTS'},
        ]

        super().__init__(data_dict_filepath=SchemaLAAO14.DATA_DICT,
                         dataset=SchemaLAAO14.DATASET,
                         dataset_id=SchemaLAAO14.DATASET_ID,
                         version=SchemaLAAO14.VERSION,
                         rebuild_schema=rebuild_schema,
                         target_db=target_db,
                         export_schema=export_schema,
                         table_prefix=SchemaLAAO14.TABLE_PREFIX,
                         custom_fields=custom_fields)

        self.id_cols = pd.DataFrame(
            [
                {'name': 'NCDRPatientId', 'dtype': 'bigint'},
                {'name': 'EpisodeKey', 'dtype': 'text'},
                {'name': 'VisitId', 'dtype': 'tinyint'},
                {'name': 'IncrementalId', 'dtype': 'tinyint'},
                {'name': 'ParentIncrementalId', 'dtype': 'tinyint'},
                {'name': 'ClientFileId', 'dtype': 'text'},
                {'name': 'FileName', 'dtype': 'text'},
                {'name': 'Version', 'dtype': 'int'},
                {'name': 'BiomeImportDt', 'dtype': 'text'},
                {'name': 'DatasetName', 'dtype': 'varchar(64)'},
                {'name': 'PartId', 'dtype': 'text'},
                {'name': 'PartName', 'dtype': 'text'}
            ]
        )


class SchemaLAAOFU14(BuildSchema):

    DATA_DICT = 'LAAO14.xlsx'
    DATASET = 'LAAOFU'
    DATASET_ID = 164
    VERSION = '1.4'
    TABLE_PREFIX = 'LAAOFU_14_'
    REGISTRY_SUBSET = 'LAAOFU__DEFAULT'
    ANCHOR_DATE_FIELD = 'dischargedate'

    def __init__(self, rebuild_schema=False, target_db='DB_EARASS', export_schema=False):

        custom_fields = [
            {'name': 'EventId', 'dtype': 'text', 'table': 'FUPEVENTS'},
        ]

        super().__init__(data_dict_filepath=SchemaLAAOFU14.DATA_DICT,
                         dataset=SchemaLAAOFU14.DATASET,
                         dataset_id=SchemaLAAOFU14.DATASET_ID,
                         version=SchemaLAAOFU14.VERSION,
                         rebuild_schema=rebuild_schema,
                         target_db=target_db,
                         export_schema=export_schema,
                         table_prefix=SchemaLAAOFU14.TABLE_PREFIX,
                         is_followup=True,
                         custom_fields=custom_fields)

        self.id_cols = pd.DataFrame(
            [
                {'name': 'NCDRPatientId', 'dtype': 'bigint'},
                {'name': 'FollowupKey', 'dtype': 'text'},
                {'name': 'VisitId', 'dtype': 'tinyint'},
                {'name': 'IncrementalId', 'dtype': 'tinyint'},
                {'name': 'ParentIncrementalId', 'dtype': 'tinyint'},
                {'name': 'ClientFileId', 'dtype': 'text'},
                {'name': 'FileName', 'dtype': 'text'},
                {'name': 'Version', 'dtype': 'int'},
                {'name': 'BiomeImportDt', 'dtype': 'text'},
                {'name': 'DatasetName', 'dtype': 'varchar(64)'},
                {'name': 'PartId', 'dtype': 'text'},
                {'name': 'PartName', 'dtype': 'text'}
            ]
        )
