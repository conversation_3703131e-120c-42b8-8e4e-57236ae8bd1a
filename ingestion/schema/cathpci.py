from ingestion.schema.schema_gen import BuildSchema


class SchemaCathPCI5(BuildSchema):

    DATA_DICT = 'CathPCI5.xlsx'
    DATASET = 'CathPCI'
    DATASET_ID = 100
    VERSION = '5.0'
    REGISTRY_SUBSET = 'CATHPCI_50__DEFAULT'
    TABLE_PREFIX = 'CathPCI_50_'
    ANCHOR_DATE_FIELD = 'dischargedate'

    def __init__(self, rebuild_schema=False, target_db='DB_EARASS', export_schema=False):

        # this field has len = 3 in the data dictionary,
        # but it has multiple values that are joined with pipe and the length exceeds 3, so providing custom dtype
        custom_dtype = {'ICDevCounterAssn': 'varchar(32)',
                        'SymptomTime': 'varchar(64)'}

        super().__init__(data_dict_filepath=SchemaCathPCI5.DATA_DICT,
                         dataset=SchemaCathPCI5.DATASET,
                         dataset_id=SchemaCathPCI5.DATASET_ID,
                         version=SchemaCathPCI5.VERSION,
                         rebuild_schema=rebuild_schema,
                         target_db=target_db,
                         export_schema=export_schema,
                         table_prefix=SchemaCathPCI5.TABLE_PREFIX,
                         custom_dtype=custom_dtype)
