from ingestion.schema.schema_gen import BuildSchema


class SchemaAFib10(BuildSchema):
    DATA_DICT = 'AFIB10.xlsx'
    DATASET = 'AFib'
    DATASET_ID = 117
    VERSION = '1.0'
    REGISTRY_SUBSET = 'AFIB__DEFAULT'
    TABLE_PREFIX = 'AFib_'
    ANCHOR_DATE_FIELD = 'dischargedate'

    def __init__(self, rebuild_schema=False, target_db='DB_ASHISH', export_schema=False):
        custom_fields = [
            {'name': 'DeviceName', 'dtype': 'text', 'table': 'DEVICESUSED'}
        ]

        custom_dtype = {'AblStrategyCode': 'text'}

        super().__init__(data_dict_filepath=SchemaAFib10.DATA_DICT,
                         dataset=SchemaAFib10.DATASET,
                         dataset_id=SchemaAFib10.DATASET_ID,
                         version=SchemaAFib10.VERSION,
                         rebuild_schema=rebuild_schema,
                         target_db=target_db,
                         export_schema=export_schema,
                         table_prefix=SchemaAFib10.TABLE_PREFIX,
                         custom_fields=custom_fields,
                         custom_dtype=custom_dtype)


class SchemaAFib20(BuildSchema):
    DATA_DICT = 'AFIB20.xlsx'
    DATASET = 'AFib'
    DATASET_ID = 117
    VERSION = '2.0'
    REGISTRY_SUBSET = 'AFIB20__DEFAULT'
    TABLE_PREFIX = 'AFib20_'
    ANCHOR_DATE_FIELD = 'dischargedate'

    def __init__(self, rebuild_schema=False, target_db='DB_ASHISH', export_schema=False):
        custom_fields = [
            {'name': 'DeviceName', 'dtype': 'text', 'table': 'DEVICESUSED'}
        ]

        custom_dtype = {'AblStrategyCode': 'text'}

        super().__init__(data_dict_filepath=SchemaAFib20.DATA_DICT,
                         dataset=SchemaAFib20.DATASET,
                         dataset_id=SchemaAFib20.DATASET_ID,
                         version=SchemaAFib20.VERSION,
                         rebuild_schema=rebuild_schema,
                         target_db=target_db,
                         export_schema=export_schema,
                         table_prefix=SchemaAFib20.TABLE_PREFIX,
                         custom_fields=custom_fields,
                         custom_dtype=custom_dtype)
