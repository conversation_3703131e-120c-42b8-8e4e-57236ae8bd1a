from ingestion.schema.schema_gen import BuildSchema


class SchemaCPMI31(BuildSchema):

    DATA_DICT = 'CPMI31.xlsx'
    DATASET = 'CPMI'
    DATASET_ID = 148
    VERSION = '3.1'
    REGISTRY_SUBSET = 'CPMI_31__DEFAULT'
    TABLE_PREFIX = 'CPMI_31_'
    ANCHOR_DATE_FIELD = 'dcdatetime'

    def __init__(self, rebuild_schema=False, target_db='DB_EARASS', export_schema=False):
        # This field has other than usual time format, but the dtype in dict is time. so keeping this varchar
        custom_dtype = {'SymptomTime24': 'varchar(64)'}

        super().__init__(data_dict_filepath=SchemaCPMI31.DATA_DICT,
                         dataset=SchemaCPMI31.DATASET,
                         dataset_id=SchemaCPMI31.DATASET_ID,
                         version=SchemaCPMI31.VERSION,
                         rebuild_schema=rebuild_schema,
                         target_db=target_db,
                         export_schema=export_schema,
                         table_prefix=SchemaCPMI31.TABLE_PREFIX,
                         custom_dtype=custom_dtype)


class SchemaCPMI30(BuildSchema):

    DATA_DICT = 'CPMI30.xlsx'
    DATASET = 'CPMI'
    DATASET_ID = 148
    VERSION = '3.0'
    REGISTRY_SUBSET = 'CPMI__DEFAULT'
    TABLE_PREFIX = 'CPMI_30_'
    ANCHOR_DATE_FIELD = 'dcdatetime'

    def __init__(self, rebuild_schema=False, target_db='DB_EARASS', export_schema=False):
        # This field has other than usual time format, but the dtype in dict is time. so keeping this varchar
        custom_dtype = {'SymptomTime24': 'varchar(64)'}

        super().__init__(data_dict_filepath=SchemaCPMI30.DATA_DICT,
                         dataset=SchemaCPMI30.DATASET,
                         dataset_id=SchemaCPMI30.DATASET_ID,
                         version=SchemaCPMI30.VERSION,
                         rebuild_schema=rebuild_schema,
                         target_db=target_db,
                         export_schema=export_schema,
                         table_prefix=SchemaCPMI30.TABLE_PREFIX,
                         custom_dtype=custom_dtype)
