from ingestion.schema.schema_gen import BuildSchema


class SchemaICD23(BuildSchema):

    DATA_DICT = 'ICD23.xlsx'
    DATASET = 'ICD'
    DATASET_ID = 107
    VERSION = '2.3'
    REGISTRY_SUBSET = 'ICD_23__DEFAULT'
    TABLE_PREFIX = 'ICD_23_'
    ANCHOR_DATE_FIELD = 'dischargedate'

    def __init__(self, rebuild_schema=False, target_db='DB_EARASS', export_schema=False):
        custom_fields = [
            {'name': 'ICDImpModelName', 'dtype': 'text', 'table': 'PROCINFO'},
            {'name': 'ExplantDeviceName', 'dtype': 'text', 'table': 'PROCINFO'},
            {'name': 'LeadModelName', 'dtype': 'text', 'table': 'Leads'},
        ]

        super().__init__(data_dict_filepath=SchemaICD23.DATA_DICT,
                         dataset=SchemaICD23.DATASET,
                         dataset_id=SchemaICD23.DATASET_ID,
                         version=SchemaICD23.VERSION,
                         rebuild_schema=rebuild_schema,
                         target_db=target_db,
                         export_schema=export_schema,
                         table_prefix=SchemaICD23.TABLE_PREFIX,
                         custom_fields=custom_fields)


class SchemaICD30(BuildSchema):

    DATA_DICT = 'ICD30.xlsx'
    DATASET = 'ICD'
    DATASET_ID = 107
    VERSION = '3.0'
    REGISTRY_SUBSET = 'ICD_30__DEFAULT'
    TABLE_PREFIX = 'ICD_30_'
    ANCHOR_DATE_FIELD = 'dischargedate'

    def __init__(self, rebuild_schema=False, target_db='DB_ASHISH', export_schema=False):
        custom_fields = [
            {'name': 'ICDImpModelName', 'dtype': 'text', 'table': 'IMPLANTDEV'},
            {'name': 'ExplantDeviceName', 'dtype': 'text', 'table': 'EXPLANTDEV'},
            {'name': 'LeadModelName', 'dtype': 'text', 'table': 'Leads'},
        ]

        super().__init__(data_dict_filepath=SchemaICD30.DATA_DICT,
                         dataset=SchemaICD30.DATASET,
                         dataset_id=SchemaICD30.DATASET_ID,
                         version=SchemaICD30.VERSION,
                         rebuild_schema=rebuild_schema,
                         target_db=target_db,
                         export_schema=export_schema,
                         table_prefix=SchemaICD30.TABLE_PREFIX,
                         custom_fields=custom_fields)
