from typing import Optional
from ingestion.schema.schema_gen_admin import BuildSchemaAdmin

from ingestion.data_dictionaries import read_data_dict


class SchemaDiagnosis(BuildSchemaAdmin):
    DATA_DICT: str = 'Diagnosis.xlsx'
    DATASET: str = 'DIAGNOSIS'
    DATASET_ID: int = 163
    VERSION: str = '1'
    TABLE_PREFIX: str = 'Admin_Diagnosis'
    REGISTRY_SUBSET: str = 'ADMIN__DIAGNOSIS'
    ANCHOR_DATE_FIELD: Optional[str] = None
    FILE_INFO_COLS = [
        {'Name': 'ClientFileId', 'Data Type': 'varchar(255)', 'Precision': ''},
        {'Name': 'FileName', 'Data Type': 'varchar(255)', 'Precision': ''},
        {'Name': 'BiomeImportDt', 'Data Type': 'text', 'Precision': ''},
        {'Name': 'Version', 'Data Type': 'int', 'Precision': ''},
        {'Name': 'DatasetName', 'Data Type': 'varchar(32)', 'Precision': ''},
        {'Name': 'additional_fields', 'Data Type': 'json', 'Precision': ''},
    ]

    def __init__(self, rebuild_schema=False, target_db='DB_EARASS', export_schema=False):
        self.rebuild_schema = rebuild_schema
        self.target_db = target_db
        self.export_schema = export_schema
        self.fields = read_data_dict(filename=SchemaDiagnosis.DATA_DICT, sheet_name='Fields')

        super().__init__(fields=self.fields, file_info_cols=SchemaDiagnosis.FILE_INFO_COLS,
                         table_prefix=SchemaDiagnosis.TABLE_PREFIX, rebuild=rebuild_schema, target_db=target_db,
                         export=export_schema)
