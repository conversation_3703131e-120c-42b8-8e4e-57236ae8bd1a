import numpy as np
import pandas as pd
from ingestion.utils import logging

from ingestion.data_dictionaries import read_data_dict
from ingestion.utils.db import execute_sql


class SchemaCSTK:

    DATA_DICT = 'CSTK.xlsx'
    DATASET = 'CSTK'
    DATASET_ID = 183
    VERSION = '1'
    TABLE_PREFIX = 'CSTK'
    REGISTRY_SUBSET = 'CSTK__DEFAULT'
    ANCHOR_DATE_FIELD = 'dischargedate'

    FILE_INFO_COLS = [
        {'Name': 'ClientFileId', 'Data Type': 'text', 'Precision': ''},
        {'Name': 'FileName', 'Data Type': 'text', 'Precision': ''},
        {'Name': 'BiomeImportDt', 'Data Type': 'text', 'Precision': ''},
        {'Name': 'Version', 'Data Type': 'int', 'Precision': ''},
        {'Name': 'DatasetName', 'Data Type': 'varchar', 'Precision': '(64)'},
    ]

    def __init__(self, rebuild_schema=False, target_db='DB_EARASS', export_schema=False):
        self.rebuild_schema = rebuild_schema
        self.target_db = target_db
        self.export_schema = export_schema
        self.schema = read_data_dict(filename=SchemaCSTK.DATA_DICT, sheet_name='Fields')

    def pre_process(self):
        self.schema['Name'] = self.schema['Name'].str.upper()
        self.schema['Data Type'] = self.schema['Data Type'].fillna('text')
        self.schema['Precision'] = np.where(self.schema['Data Type'].str.contains('varchar', case=False), '(255)', '')

        self.schema = pd.concat([self.schema, pd.DataFrame(SchemaCSTK.FILE_INFO_COLS)], ignore_index=True)

    def create_table_sql(self):
        cols = []
        for _, row in self.schema.iterrows():
            col = f'`{row["Name"]}` {row["Data Type"]}{row["Precision"]} DEFAULT NULL'
            cols.append(col)
        return f'DROP TABLE IF EXISTS {self.TABLE_PREFIX};' \
               f'CREATE TABLE {self.TABLE_PREFIX} ({",".join(cols)}) ENGINE=InnoDB DEFAULT CHARSET=latin1;'

    def execute(self):
        self.pre_process()
        create_table_sql = self.create_table_sql()
        if self.rebuild_schema:
            logging.info(f'Dropping and creating table {self.TABLE_PREFIX}')
            logging.info(create_table_sql)
            execute_sql(create_table_sql, db=self.target_db, multi_statements=True)
