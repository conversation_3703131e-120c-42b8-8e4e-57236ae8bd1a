from typing import ClassVar, List, Dict

from ingestion.schema.schema_gen_admin import BuildSchemaAdmin
from ingestion.data_dictionaries import read_data_dict


class SchemaCost(BuildSchemaAdmin):
    """
    Schema class of cost that generates schema table(s).
    """

    DATA_DICT: ClassVar[str] = 'Cost.xlsx'
    DATASET: ClassVar[str] = 'Cost'
    DATASET_ID: ClassVar[int] = 157
    VERSION: ClassVar[str] = '1'
    TABLE_PREFIX: ClassVar[str] = 'Admin_Cost'
    REGISTRY_SUBSET: ClassVar[str] = 'ADMIN__COST'
    ANCHOR_DATE_FIELD: ClassVar[str] = None
    FILE_INFO_COLS: ClassVar[List[Dict[str, str]]] = [
        {'Name': 'ClientFileId', 'Data Type': 'varchar(255)', 'Precision': ''},
        {'Name': 'FileName', 'Data Type': 'varchar(255)', 'Precision': ''},
        {'Name': 'BiomeImportDt', 'Data Type': 'text', 'Precision': ''},
        {'Name': 'Version', 'Data Type': 'int', 'Precision': ''},
        {'Name': 'DatasetName', 'Data Type': 'varchar(32)', 'Precision': ''},
        {'Name': 'additional_fields', 'Data Type': 'json', 'Precision': ''},

    ]

    def __init__(self, rebuild_schema=False, target_db='DB_ASHISH', export_schema=False):
        self.fields = read_data_dict(filename=SchemaCost.DATA_DICT, sheet_name='Fields')

        super().__init__(fields=self.fields,
                         file_info_cols=SchemaCost.FILE_INFO_COLS,
                         table_prefix=SchemaCost.TABLE_PREFIX,
                         rebuild=rebuild_schema,
                         target_db=target_db,
                         export=export_schema)
