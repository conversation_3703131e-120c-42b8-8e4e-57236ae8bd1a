import pandas as pd
from ingestion.schema.schema_gen import BuildSchema


class SchemaTVT30(BuildSchema):

    DATA_DICT = 'TVT30.xlsx'
    DATASET = 'TVT'
    DATASET_ID = 105
    VERSION = '3.0'
    TABLE_PREFIX = 'TVT_30_'
    REGISTRY_SUBSET = 'TVT__DEFAULT'
    ANCHOR_DATE_FIELD = 'dischargedate'

    def __init__(self, rebuild_schema=False, target_db='MBA_DB', export_schema=False):
        custom_dtype = {'NumPrevCardSurg': 'int', 'KCCQ12_Overall': 'decimal(5,2)', 'MRRIndication': 'Varchar(475)'}

        super().__init__(data_dict_filepath=SchemaTVT30.DATA_DICT,
                         dataset=SchemaTVT30.DATASET,
                         dataset_id=SchemaTVT30.DATASET_ID,
                         version=SchemaTVT30.VERSION,
                         rebuild_schema=rebuild_schema,
                         target_db=target_db,
                         export_schema=export_schema,
                         table_prefix=SchemaTVT30.TABLE_PREFIX,
                         custom_dtype=custom_dtype)

        self.id_cols = pd.DataFrame(
            [
                {'name': 'NCDRPatientId', 'dtype': 'bigint'},
                {'name': 'EpisodeKey', 'dtype': 'text'},
                {'name': 'VisitId', 'dtype': 'tinyint'},
                {'name': 'IncrementalId', 'dtype': 'tinyint'},
                {'name': 'ClientFileId', 'dtype': 'text'},
                {'name': 'FileName', 'dtype': 'text'},
                {'name': 'Version', 'dtype': 'int'},
                {'name': 'BiomeImportDt', 'dtype': 'text'},
                {'name': 'DatasetName', 'dtype': 'varchar(64)'},
                {'name': 'PartId', 'dtype': 'text'},
                {'name': 'PartName', 'dtype': 'text'}
            ]
        )


class SchemaTVTFU30(BuildSchema):

    DATA_DICT = 'TVT30.xlsx'
    DATASET = 'TVTFU'
    DATASET_ID = 160
    VERSION = '3.0'
    TABLE_PREFIX = 'TVTFU_30_'
    REGISTRY_SUBSET = 'TVTFU__DEFAULT'
    ANCHOR_DATE_FIELD = 'dischargedate'

    def __init__(self, rebuild_schema=False, target_db='MBA_DB', export_schema=False):
        custom_dtype = {'F_KCCQ12_Overall': 'decimal(5,2)'}

        super().__init__(data_dict_filepath=SchemaTVTFU30.DATA_DICT,
                         dataset=SchemaTVTFU30.DATASET,
                         dataset_id=SchemaTVTFU30.DATASET_ID,
                         version=SchemaTVTFU30.VERSION,
                         rebuild_schema=rebuild_schema,
                         target_db=target_db,
                         export_schema=export_schema,
                         table_prefix=SchemaTVTFU30.TABLE_PREFIX,
                         is_followup=True,
                         custom_dtype=custom_dtype)

        self.id_cols = pd.DataFrame(
            [
                {'name': 'NCDRPatientId', 'dtype': 'bigint'},
                {'name': 'FollowupKey', 'dtype': 'text'},
                {'name': 'VisitId', 'dtype': 'tinyint'},
                {'name': 'IncrementalId', 'dtype': 'tinyint'},
                {'name': 'ClientFileId', 'dtype': 'text'},
                {'name': 'FileName', 'dtype': 'text'},
                {'name': 'Version', 'dtype': 'int'},
                {'name': 'BiomeImportDt', 'dtype': 'text'},
                {'name': 'DatasetName', 'dtype': 'varchar(64)'},
                {'name': 'PartId', 'dtype': 'text'},
                {'name': 'PartName', 'dtype': 'text'}
            ]
        )
