from ingestion.schema.schema_gen_admin import BuildSchemaAdmin
from ingestion.data_dictionaries import read_data_dict


class SchemaDischarge(BuildSchemaAdmin):

    DATA_DICT = 'Discharge.xlsx'
    DATASET = 'Discharges'
    DATASET_ID = 108
    VERSION = '1'
    TABLE_PREFIX = 'Admin_Discharges'
    REGISTRY_SUBSET = 'ADMIN__DISCHARGES'
    ANCHOR_DATE_FIELD = 'dischargedate'
    FILE_INFO_COLS = [
        {'Name': 'ClientFileId', 'Data Type': 'varchar(255)', 'Precision': ''},
        {'Name': 'FileName', 'Data Type': 'varchar(255)', 'Precision': ''},
        {'Name': 'BiomeImportDt', 'Data Type': 'text', 'Precision': ''},
        {'Name': 'Version', 'Data Type': 'int', 'Precision': ''},
        {'Name': 'DatasetName', 'Data Type': 'varchar(32)', 'Precision': ''},
        {'Name': 'additional_fields', 'Data Type': 'json', 'Precision': ''},

    ]

    def __init__(self, rebuild_schema=False, target_db='DB_MBA', export_schema=False):
        self.rebuild_schema = rebuild_schema
        self.target_db = target_db
        self.export_schema = export_schema
        self.fields = read_data_dict(filename=SchemaDischarge.DATA_DICT, sheet_name='Fields')

        super().__init__(fields=self.fields, file_info_cols=SchemaDischarge.FILE_INFO_COLS,
                         table_prefix=SchemaDischarge.TABLE_PREFIX, rebuild=rebuild_schema, target_db=target_db,
                         export=export_schema)
