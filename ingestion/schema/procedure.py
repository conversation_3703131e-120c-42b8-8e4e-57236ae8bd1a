from ingestion.schema.schema_gen_admin import BuildSchemaAdmin

from ingestion.data_dictionaries import read_data_dict


class SchemaProcedure(BuildSchemaAdmin):
    DATA_DICT: str = 'Procedure.xlsx'
    DATASET: str = 'PROCEDURE'
    DATASET_ID: int = 162
    VERSION: str = '1'
    TABLE_PREFIX: str = 'Admin_Procedure'
    REGISTRY_SUBSET: str = 'ADMIN__PROCEDURE'
    ANCHOR_DATE_FIELD: str = 'proceduredate'
    FILE_INFO_COLS = [
        {'Name': 'ClientFileId', 'Data Type': 'varchar(255)', 'Precision': ''},
        {'Name': 'FileName', 'Data Type': 'varchar(255)', 'Precision': ''},
        {'Name': 'BiomeImportDt', 'Data Type': 'text', 'Precision': ''},
        {'Name': 'Version', 'Data Type': 'int', 'Precision': ''},
        {'Name': 'DatasetName', 'Data Type': 'varchar(32)', 'Precision': ''},
        {'Name': 'additional_fields', 'Data Type': 'json', 'Precision': ''},
    ]

    def __init__(self, rebuild_schema=False, target_db='DB_EARASS', export_schema=False):
        self.rebuild_schema = rebuild_schema
        self.target_db = target_db
        self.export_schema = export_schema
        self.fields = read_data_dict(filename=SchemaProcedure.DATA_DICT, sheet_name='Fields')

        super().__init__(fields=self.fields, file_info_cols=SchemaProcedure.FILE_INFO_COLS,
                         table_prefix=SchemaProcedure.TABLE_PREFIX, rebuild=rebuild_schema, target_db=target_db,
                         export=export_schema)
