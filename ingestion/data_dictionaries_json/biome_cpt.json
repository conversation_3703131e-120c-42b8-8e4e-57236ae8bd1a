{"Dictionary": {"Dataset": {"Code": "cpt", "Id": 179}, "Version": "1.0", "Type": "Biome", "Sections": [{"Container Class": "defaultContainer", "Parent Section": "Root", "Section Display Name": "<PERSON><PERSON><PERSON>", "Section Code": "DEFAULT", "Section Type": "Section", "Cardinality": "1..1", "Table": "RAW", "Elements": [{"Element Reference": 1213110851, "Short Name": "filename", "DB Data Type": "<PERSON><PERSON><PERSON>(69)", "Role": null, "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "filename", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 1442714507, "Short Name": "batchnumber", "DB Data Type": "<PERSON><PERSON><PERSON>(12)", "Role": null, "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "batchnumber", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 2670785740, "Short Name": "version", "DB Data Type": "<PERSON><PERSON><PERSON>(22)", "Role": null, "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "version", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 3584077987, "Short Name": "cpt4hcpcscode", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "cpt4hcpcscode", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 4055794023, "Short Name": "cptseqno", "DB Data Type": "<PERSON><PERSON><PERSON>(15)", "Role": null, "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "cptseqno", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 743086825, "Short Name": "datasetname", "DB Data Type": "<PERSON><PERSON><PERSON>(18)", "Role": null, "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "datasetname", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 934113661, "Short Name": "cptdate", "DB Data Type": "<PERSON><PERSON><PERSON>(31)", "Role": "|DATE||ANCHOR_DATE|", "PHI": true, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "cptdate", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 1398750117, "Short Name": "cptmodifier1", "DB Data Type": "<PERSON><PERSON><PERSON>(15)", "Role": null, "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "cptmodifier1", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 1076784334, "Short Name": "hospname", "DB Data Type": "<PERSON><PERSON><PERSON>(18)", "Role": null, "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "hospname", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 3545824584, "Short Name": "cptmodifier3", "DB Data Type": "<PERSON><PERSON><PERSON>(14)", "Role": null, "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "cptmodifier3", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 2613064618, "Short Name": "cptmodifier2", "DB Data Type": "<PERSON><PERSON><PERSON>(15)", "Role": null, "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "cptmodifier2", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 1614028513, "Short Name": "biomeencounterid", "DB Data Type": "bigint unsigned", "Role": null, "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "biomeencounterid", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 4090799199, "Short Name": "clientfileid", "DB Data Type": "bigint", "Role": null, "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "clientfileid", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 3699837234, "Short Name": "encounternumber", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": "IDENTIFIER", "PHI": true, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "encounternumber", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 4035169011, "Short Name": "tenantid", "DB Data Type": "<PERSON><PERSON><PERSON>(34)", "Role": "TENANT", "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "tenantid", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 176123314, "Short Name": "facilityid", "DB Data Type": "<PERSON><PERSON><PERSON>(12)", "Role": null, "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "facilityid", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}]}]}}