{"Dictionary": {"Dataset": {"Code": "discharges", "Id": 102}, "Version": "1.0", "Type": "<PERSON><PERSON><PERSON>", "Sections": [{"Container Class": "defaultContainer", "Parent Section": "Root", "Section Display Name": "<PERSON><PERSON><PERSON>", "Section Code": "DEFAULT", "Section Type": "Section", "Cardinality": "1..1", "Table": "RAW", "Elements": [{"Short Name": "dischargestatus", "DB Data Type": "<PERSON><PERSON><PERSON>(255)", "Role": null, "PHI": 0, "Element Reference": 1873700935, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "dischargestatus", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["discharge status - description", "enc___discharge_disposition_desc"], "ucd": ["dischdispdesc", "dischargedisposition", "dischargesource"], "genesis": ["dischargestatus"], "ech": ["dischargedispositiondesc"], "sutter": ["enc - discharge status_desc"], "osf": ["discharge_status_desc"], "sluh": ["dischargedisposition"], "biome": ["dischargestatus"], "ucsd": ["enc - discharge disposition desc"], "bjc": ["dischargestatus"], "uva": ["dischargestatus"], "bellin": ["dischargestatus"], "montefiore": ["discharge status", "discharge status"], "rwj": ["Discharge Status - Discharge Status Description"], "prisma": ["dischargestatus"], "ucsf": ["discharge disposition", "discharge_disposition_desc"], "sharp": ["discharge_disp_desc"], "northshore": ["dischargestatus"], "trinity": ["discharge status"], "umms": ["dischargestatus"], "ucla": ["discharge disposition description"], "commonspirit": ["discharge status name", "discharge status - discharge status description"], "wellspan": ["dischargestatus"]}}, {"Short Name": "prindx9code", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": 0, "Element Reference": 3223988868, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "prindx9code", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["primary icd9 diagnosis - code", "enc___primary_diagnosis"], "ucd": [], "genesis": [], "ech": ["principaldiagnosiscode"], "sutter": [], "osf": ["principal_diagnosis_icd"], "sluh": ["principaldiagnosisicd"], "biome": [], "ucsd": ["enc - primary icd9 diagnosis"], "bjc": [], "uva": [], "bellin": [], "montefiore": [], "rwj": [], "prisma": [], "ucsf": [], "sharp": ["principal_diagnosis_icd"], "northshore": ["prindx9code/prindx10code"], "trinity": [], "umms": [], "ucla": ["principal diagnosis (icd-9)"], "commonspirit": [], "wellspan": []}}, {"Short Name": "indirectcost", "DB Data Type": "<PERSON><PERSON><PERSON>(15)", "Role": null, "PHI": 0, "Element Reference": 225185267, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "indirectcost", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["indirect cost", "enc___total_indirect_costs"], "ucd": ["totalindirectcosts", "fixexpenses", "fixcosts"], "genesis": ["indirectcost"], "ech": ["totalindirectcosts"], "sutter": ["enc - total indirect costs"], "osf": ["total_indirect_cost"], "sluh": ["totalindirectcost"], "biome": ["indirectcost"], "ucsd": ["costs - indirect"], "bjc": ["indirectcost"], "uva": ["indirectcost"], "bellin": ["indirectcost"], "montefiore": ["indirect cost", "indirect cost"], "rwj": ["Total Indirect Costs"], "prisma": ["indirectcost,,,,"], "ucsf": ["total_indirect_cost"], "sharp": [], "northshore": ["indirectcost"], "trinity": ["indirect cost", "total indirect cost"], "umms": ["indirectcost"], "ucla": [], "commonspirit": ["total indirect", "costs - indirect cost"], "wellspan": ["indirectcost"]}}, {"Short Name": "gender", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": "GENDER", "PHI": 1, "Element Reference": **********, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "gender", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["gender - description", "enc___gender_desc"], "ucd": ["genderdesc"], "genesis": ["gender"], "ech": ["patientgender"], "sutter": ["enc - gender_desc", "enc - gender desc"], "osf": ["gender"], "sluh": ["gender"], "biome": ["gender"], "ucsd": ["enc - gender desc"], "bjc": ["gender"], "uva": ["gender"], "bellin": ["gender"], "montefiore": ["gender"], "rwj": ["Gender - Gender Description"], "prisma": ["gender"], "ucsf": ["gender"], "sharp": ["gender"], "northshore": ["gender"], "trinity": ["gender"], "umms": ["gender"], "ucla": ["patient gender"], "commonspirit": ["patient sex"], "wellspan": ["gender"]}}, {"Short Name": "medrecn", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": "MEDRECNUM", "PHI": 1, "Element Reference": **********, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "medrecn", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["patient encounter - facility medical record number", "enc___medical_record_number"], "ucd": ["medicalrecordnumber", "medicalrecnubmber"], "genesis": ["cmrn"], "ech": ["medicalrecordnumber"], "sutter": ["enc - medical record number"], "osf": ["mrn"], "sluh": ["mrn", "ï»¿mrn"], "biome": ["mrn"], "ucsd": ["enc - medical record number"], "bjc": ["mrn"], "uva": ["medrecn"], "bellin": ["medrecn"], "montefiore": ["patient encounter - medical record number", "patient encounter - medical record number"], "rwj": ["Patient Encounter - Medical Record Number Source"], "prisma": ["medrecn"], "ucsf": ["pat_mrn_id", "mrn"], "sharp": ["shc"], "northshore": ["medrecnumber"], "trinity": ["mrn", "patient med rec no"], "umms": ["medrecn"], "ucla": ["medical record number"], "commonspirit": ["med rec #"], "wellspan": ["medrecn"]}}, {"Short Name": "secondarypayor", "DB Data Type": "<PERSON><PERSON><PERSON>(100)", "Role": null, "PHI": 0, "Element Reference": **********, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "secondarypayor", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["enc___second_payor_plan_code_desc"], "ucd": [], "genesis": ["secondarypayor"], "ech": [], "sutter": [], "osf": [], "sluh": [], "biome": ["secondarypayor"], "ucsd": [], "bjc": ["secondarypayor"], "uva": ["secondarypayor"], "bellin": ["secondarypayor"], "montefiore": [], "rwj": [], "prisma": [], "ucsf": ["second_payor_plan"], "sharp": [], "northshore": ["secondarypayor"], "trinity": ["secondary payor plan"], "umms": ["secondarypayor"], "ucla": ["secondary payer code description"], "commonspirit": [], "wellspan": ["secondarypayor"]}}, {"Short Name": "prindx10code", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": 0, "Element Reference": 3867833841, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "prindx10code", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": [], "ucd": [], "genesis": [], "ech": [], "sutter": [], "osf": [], "sluh": [], "biome": ["prindx9code, prindx10code"], "ucsd": ["enc - primary icd10 diagnosis"], "bjc": ["prindx9code, prindx10code"], "uva": ["prindx10code"], "bellin": ["prinicddxcode"], "montefiore": ["primary icd10 diagnosis - code", "primary icd10 diagnosis - code"], "rwj": ["ICD10 DX Primary - ICD10 Diagnosis"], "prisma": ["prindx10code"], "ucsf": ["primary icd10 dx code"], "sharp": [], "northshore": [], "trinity": ["prin diag code", "icd-10-cm prin diag code"], "umms": ["prinicddxcode"], "ucla": ["principal diagnosis (icd-10)"], "commonspirit": ["icd-10-cm prin diag code", "icd10 dx primary - icd10 dx code"], "wellspan": ["prinicddxcode"]}}, {"Short Name": "admitdx9code", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": 0, "Element Reference": 932495030, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "admitdx9code", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["admit icd9 diagnosis - code", "enc___admission_diagnosis"], "ucd": [], "genesis": [], "ech": ["admitdiagnosiscode"], "sutter": [], "osf": ["admit_diagnosis_icd"], "sluh": ["admitdiagnosisicd"], "biome": [], "ucsd": ["enc - icd9 admission diagnosis"], "bjc": [], "uva": ["admitdxcode"], "bellin": [], "montefiore": [], "rwj": [], "prisma": [], "ucsf": [], "sharp": [], "northshore": [], "trinity": [], "umms": [], "ucla": ["admit diagnosis code (icd-9)"], "commonspirit": [], "wellspan": []}}, {"Short Name": "dob", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": "DOB", "PHI": 1, "Element Reference": 182991122, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "dob", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["patient encounter - date of birth", "enc___date_of_birth"], "ucd": ["dateofbirth", "birthdate"], "genesis": ["dob"], "ech": ["birthdate"], "sutter": ["enc - date of birth"], "osf": ["dob"], "sluh": ["dob"], "biome": ["dob"], "ucsd": ["enc - date of birth"], "bjc": ["dob"], "uva": ["dob"], "bellin": ["dob"], "montefiore": ["patient encounter - date of birth", "patient encounter - date of birth"], "rwj": ["Patient Encounter - Date Of Birth"], "prisma": ["dob"], "ucsf": ["date of birth", "date_of_birth"], "sharp": ["dob"], "northshore": ["dob"], "trinity": ["dob"], "umms": ["dob"], "ucla": ["patient dob"], "commonspirit": ["patient date of birth"], "wellspan": ["dob"]}}, {"Short Name": "inoutcode", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": "INOUTCODE", "PHI": 0, "Element Reference": **********, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "inoutcode", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": [], "ucd": [], "genesis": ["inoutcode"], "ech": ["admittypedesc"], "sutter": ["enc - patient type_desc", "enc - patient type desc"], "osf": [], "sluh": ["patienttype"], "biome": ["inoutcode"], "ucsd": ["enc - patient type desc"], "bjc": ["inoutcode"], "uva": ["inoutcode"], "bellin": [], "montefiore": [], "rwj": [], "prisma": [], "ucsf": [], "sharp": [], "northshore": ["inoutcode"], "trinity": [], "umms": [], "ucla": ["patient type description"], "commonspirit": [], "wellspan": []}}, {"Short Name": "admittype", "DB Data Type": "<PERSON><PERSON><PERSON>(50)", "Role": null, "PHI": 0, "Element Reference": **********, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "admittype", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["admit type - description", "enc___admission_type_desc"], "ucd": [], "genesis": ["admittype"], "ech": ["patientprioritydesc"], "sutter": ["enc - admission type_desc", "enc - admission type desc"], "osf": ["admit_type_desc"], "sluh": ["admitstatus"], "biome": ["admittype"], "ucsd": ["enc - admission type desc"], "bjc": ["admittype"], "uva": ["admittype"], "bellin": ["admittype"], "montefiore": ["admit type", "admit type"], "rwj": ["Admit Type - Description"], "prisma": ["admittype"], "ucsf": ["admit type"], "sharp": ["admit_type_desc"], "northshore": ["admittype"], "trinity": ["admit type"], "umms": ["admittype"], "ucla": ["admission type description"], "commonspirit": [], "wellspan": ["admittype"]}}, {"Short Name": "primaryinsurancegroup", "DB Data Type": "<PERSON><PERSON><PERSON>(100)", "Role": null, "PHI": 0, "Element Reference": **********, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "primaryinsurancegroup", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": [], "ucd": [], "genesis": [], "ech": ["primaryinsgroupdesc"], "sutter": ["enc - payor plan code_desc", "enc - payor plan code desc"], "osf": [], "sluh": [], "biome": [], "ucsd": ["enc - payor plan code desc"], "bjc": [], "uva": ["primarypayorplan"], "bellin": ["primaryinsurancegroup/primarypayorplan"], "montefiore": ["insurance plan 1 - payor", "insurance plan 1 - payor"], "rwj": ["Insurance Plan 1 - Insurance Plan"], "prisma": [], "ucsf": ["primary_payor_plan_desc"], "sharp": ["primary_ins_group_desc"], "northshore": ["primaryinsurancegroup/primarypayorplan"], "trinity": [], "umms": [], "ucla": ["jmh ins group description"], "commonspirit": ["insurance plan 1 - description"], "wellspan": ["primaryinsurancegroup/primarypayorplan"]}}, {"Short Name": "dctime", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": "TIME", "PHI": 0, "Element Reference": 3060211418, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "dctime", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["discharge time - time", "enc___discharge_time"], "ucd": ["dischargetime"], "genesis": ["dischargetime"], "ech": ["dischargetime"], "sutter": ["enc - discharge time"], "osf": ["discharge_time"], "sluh": ["dischargetime"], "biome": ["dischargetime"], "ucsd": ["enc - discharge time"], "bjc": ["dischargetime"], "uva": ["dischargetime"], "bellin": ["dischargetime"], "montefiore": [], "rwj": ["Discharge Time - Time"], "prisma": ["dischargetime"], "ucsf": ["discharge_time"], "sharp": ["discharge_time"], "northshore": ["dischargetime"], "trinity": ["discharge date & time", "discharge date and time"], "umms": ["dischargetime"], "ucla": ["discharge time"], "commonspirit": ["discharge time"], "wellspan": ["dischargetime"]}}, {"Short Name": "admitdate", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": "ADMISSION_DATE", "PHI": 1, "Element Reference": 3358798491, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "admitdate", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["admit date - date", "enc___admission_date"], "ucd": ["admissiondate"], "genesis": ["admitdate"], "ech": ["admitdate"], "sutter": ["enc - admission date"], "osf": ["admit_date"], "sluh": ["admitdate"], "biome": ["admitdate"], "ucsd": ["enc - admission date"], "bjc": ["admitdate"], "uva": ["admitdate"], "bellin": ["admitdate"], "montefiore": ["admit date - date", "admit date - date"], "rwj": ["Claim Admit Date - Date"], "prisma": ["admitdate"], "ucsf": ["admit date", "admit_date"], "sharp": ["admit_date"], "northshore": ["admitdate"], "trinity": ["admit date"], "umms": ["admitdate"], "ucla": ["admission date"], "commonspirit": ["check-in date"], "wellspan": ["admitdate"]}}, {"Short Name": "icudays", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": 0, "Element Reference": 1686880744, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "icudays", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["days i - special care days", "days_i___special_care_days"], "ucd": [], "genesis": ["icu days"], "ech": ["icudays"], "sutter": ["icu days"], "osf": ["icu_days"], "sluh": ["icudays"], "biome": ["icudays"], "ucsd": ["days - icu - detail"], "bjc": ["icudays"], "uva": ["icudays"], "bellin": ["icudays"], "montefiore": ["icu days", "icu days"], "rwj": ["icu days"], "prisma": [], "ucsf": ["icu_days"], "sharp": [], "northshore": ["icudays"], "trinity": ["icu days"], "umms": ["icudays"], "ucla": ["icu days charged"], "commonspirit": ["icu days", " icu days "], "wellspan": ["icudays"]}}, {"Short Name": "directcost", "DB Data Type": "<PERSON><PERSON><PERSON>(15)", "Role": null, "PHI": 0, "Element Reference": 3373283960, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "directcost", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["direct cost", "enc___total_direct_costs"], "ucd": ["totaldirectcosts", "variableexpenses", "variablecosts"], "genesis": ["directcost"], "ech": ["totaldirectcosts"], "sutter": ["enc - total direct costs"], "osf": ["total_direct_cost"], "sluh": ["totaldirectcost"], "biome": ["directcost"], "ucsd": ["costs - direct"], "bjc": ["directcost"], "uva": ["directcost"], "bellin": ["directcost"], "montefiore": ["direct cost", "direct cost"], "rwj": ["total direct costs"], "prisma": ["directcost"], "ucsf": ["total_direct_cost"], "sharp": [], "northshore": ["directcost"], "trinity": ["direct cost", "total direct cost"], "umms": ["directcost"], "ucla": ["total direct cost"], "commonspirit": ["total direct", "costs - direct cost"], "wellspan": ["directcost"]}}, {"Short Name": "age", "DB Data Type": "decimal(6,3)", "Role": "AGE", "PHI": 1, "Element Reference": 3807611514, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "age", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": [], "ucd": [], "genesis": [], "ech": [], "sutter": [], "osf": [], "sluh": [], "biome": [], "ucsd": [], "bjc": [], "uva": [], "bellin": [], "montefiore": [], "rwj": [], "prisma": [], "ucsf": [], "sharp": [], "northshore": [], "trinity": [], "umms": [], "ucla": [], "commonspirit": [], "wellspan": []}}, {"Short Name": "encounternumber", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": "IDENTIFIER", "PHI": 1, "Element Reference": **********, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "encounternumber", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["patient encounter - encounter record number", "enc___patient_account"], "ucd": ["patientaccount"], "genesis": ["encounternumber"], "ech": ["encounternumber"], "sutter": ["enc - patient account"], "osf": ["encounter_number"], "sluh": ["encounternumber"], "biome": ["ecd number (pe ecd extension)", "patient account number"], "ucsd": ["enc - patient account"], "bjc": ["ecd number (pe ecd extension)", "patient account number"], "uva": ["encounternumber"], "bellin": ["encounternumber"], "montefiore": ["patient encounter - encounter record number", "patient encounter - encounter record number"], "rwj": ["Patient Encounter - Encounter Record Number"], "prisma": ["encounternumber"], "ucsf": ["patient_account"], "sharp": ["encounter_number"], "northshore": ["encounternumber"], "trinity": ["patient account number"], "umms": ["encounternumber"], "ucla": ["encounter number"], "commonspirit": ["patient account number"], "wellspan": ["encounternumber"]}}, {"Short Name": "patzip", "DB Data Type": "<PERSON><PERSON><PERSON>(15)", "Role": "PATIENT_ZIP", "PHI": 1, "Element Reference": *********, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "patzip", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["zip code", "enc___zip"], "ucd": ["zip", "zipcode"], "genesis": ["patientzip"], "ech": ["patientzipcode"], "sutter": ["enc - zip"], "osf": ["patient_zip"], "sluh": ["patientzip"], "biome": ["patientzip"], "ucsd": ["enc - zip"], "bjc": ["patientzip"], "uva": ["patientzip"], "bellin": ["patientzip"], "montefiore": ["zip code", "zip code"], "rwj": ["Zip Code - Zip Code"], "prisma": ["patientzip"], "ucsf": ["zip_code"], "sharp": ["patient_zip"], "northshore": ["patientzip"], "trinity": ["patient zip"], "umms": ["patientzip"], "ucla": ["patient zip code"], "commonspirit": ["pat home zip", "patient home zip code"], "wellspan": ["patientzip"]}}, {"Short Name": "patienttype", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": 0, "Element Reference": 521579640, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "patienttype", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["patient type - description", "enc___custom_patient_type_desc"], "ucd": [], "genesis": ["patient type"], "ech": ["admittypedesc", "patienttypedesc"], "sutter": [], "osf": ["patient_type_desc"], "sluh": ["patientsubtype"], "biome": ["patienttype"], "ucsd": ["enc - custom patient type desc"], "bjc": ["patienttype"], "uva": ["patienttype"], "bellin": ["patienttype"], "montefiore": [], "rwj": ["Patient Type - DSSRollup"], "prisma": ["patienttype"], "ucsf": ["patient_type_desc"], "sharp": ["patient_type_desc"], "northshore": ["patienttype"], "trinity": ["group ip or op rollup"], "umms": ["patienttype"], "ucla": [], "commonspirit": ["group ja_patient type ip/op", "patient type ip/op"], "wellspan": ["patienttype"]}}, {"Short Name": "race", "DB Data Type": "<PERSON><PERSON><PERSON>(100)", "Role": null, "PHI": 0, "Element Reference": **********, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "race", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["race - description", "enc___race_desc"], "ucd": ["racedesc"], "genesis": [], "ech": [], "sutter": [], "osf": [], "sluh": [], "biome": [], "ucsd": ["enc - race desc"], "bjc": [], "uva": [], "bellin": ["race"], "montefiore": ["race"], "rwj": [], "prisma": [], "ucsf": ["race_desc"], "sharp": [], "northshore": [], "trinity": ["patient race"], "umms": ["race"], "ucla": ["patient race"], "commonspirit": [], "wellspan": ["race"]}}, {"Short Name": "admittime", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": "TIME", "PHI": 0, "Element Reference": 96662065, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "admittime", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["admit time - time", "enc___admission_time"], "ucd": [], "genesis": ["admittime"], "ech": ["admittime"], "sutter": ["enc - admission time"], "osf": ["admit_time"], "sluh": ["admittime"], "biome": ["admittime"], "ucsd": ["enc - admission time"], "bjc": ["admittime"], "uva": ["admittime"], "bellin": ["admittime"], "montefiore": ["admit time - time", "admit time - time"], "rwj": ["<PERSON><PERSON><PERSON> Admit Time - Time"], "prisma": ["admittime"], "ucsf": ["admit time", "admit_time"], "sharp": ["admit_time"], "northshore": ["admittime"], "trinity": [], "umms": ["admittime"], "ucla": ["admission time"], "commonspirit": ["check-in time"], "wellspan": ["admittime"]}}, {"Short Name": "dischargedate", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": "ANCHOR_DATE", "PHI": 1, "Element Reference": 958782091, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "dischargedate", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["discharge date - date", "enc___discharge_date"], "ucd": ["dichdate"], "genesis": ["dischargedate"], "ech": ["dischargedate"], "sutter": ["enc - discharge date"], "osf": ["discharge_date"], "sluh": ["dischargedate"], "biome": ["dischargedate"], "ucsd": ["enc - discharge date"], "bjc": ["dischargedate"], "uva": ["dischargedate"], "bellin": ["dischargedate"], "montefiore": ["discharge date - date", "discharge date - date"], "rwj": ["Discharge Date - Date"], "prisma": ["dischargedate"], "ucsf": ["discharge date", "discharge_date"], "sharp": ["discharge_date"], "northshore": ["dischargedate"], "trinity": ["discharge date"], "umms": ["dischargedate"], "ucla": ["discharge date"], "commonspirit": ["discharge date"], "wellspan": ["dischargedate"]}}, {"Short Name": "drgcode", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": 0, "Element Reference": 3102164369, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "drgcode", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["ms drg - code", "enc___ms_drg"], "ucd": ["msdrg"], "genesis": ["drgcode"], "ech": ["discharge_drg"], "sutter": ["enc - ms drg"], "osf": ["discharge_ms_drg"], "sluh": ["dischargemsdrg"], "biome": ["drgcode"], "ucsd": ["enc - ms drg"], "bjc": ["drgcode"], "uva": ["drgcode"], "bellin": ["drgcode"], "montefiore": ["ms drg - code", "ms drg - code"], "rwj": ["Reporting DRG - Code"], "prisma": ["drgcode"], "ucsf": ["ms-drg code", "ms_drg_code"], "sharp": ["discharge_ms_drg"], "northshore": ["drgcode"], "trinity": ["drg code"], "umms": ["drgcode"], "ucla": ["discharge ms-drg"], "commonspirit": ["drg code", "ms drg code"], "wellspan": ["drgcode"]}}, {"Short Name": "totalcost", "DB Data Type": "<PERSON><PERSON><PERSON>(15)", "Role": null, "PHI": 0, "Element Reference": 1707967097, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "totalcost", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["total cost", "enc___total_costs"], "ucd": [], "genesis": [], "ech": [], "sutter": [], "osf": [], "sluh": [], "biome": [], "ucsd": ["costs - total"], "bjc": [], "uva": [], "bellin": [], "montefiore": [], "rwj": ["Costs - Total Cost"], "prisma": [], "ucsf": [], "sharp": [], "northshore": [], "trinity": [], "umms": [], "ucla": ["total costs"], "commonspirit": ["total cost"], "wellspan": []}}, {"Short Name": "admitdx10code", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": 0, "Element Reference": 206430457, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "admitdx10code", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": [], "ucd": ["icd10admissiondiagnosis"], "genesis": [], "ech": [], "sutter": ["enc - icd10 admission diagnosis"], "osf": [], "sluh": [], "biome": [], "ucsd": ["enc - icd10 admission diagnosis"], "bjc": [], "uva": [], "bellin": [], "montefiore": [], "rwj": ["Admit ICD10 Diagnosis - ICD10 Diagnosis"], "prisma": [], "ucsf": [], "sharp": [], "northshore": [], "trinity": ["admit diagnosis code", "icd-10-cm admi diag code"], "umms": ["primaryadmissionicdcode"], "ucla": ["admit diagnosis code (icd-10)"], "commonspirit": [], "wellspan": []}}, {"Short Name": "expectedpayment", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": 0, "Element Reference": 531879229, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "expectedpayment", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": [], "ucd": [], "genesis": [], "ech": ["expectedpayment"], "sutter": ["enc - estimated net revenue"], "osf": ["expected_payment"], "sluh": [], "biome": ["expectedpayment"], "ucsd": ["payment - expected - encounter"], "bjc": ["expectedpayment"], "uva": ["expectedpayment"], "bellin": ["expectedpayment"], "montefiore": [], "rwj": ["Reimbursement - Historic Expected Payment"], "prisma": [], "ucsf": ["hospital expected"], "sharp": [], "northshore": ["expectedpayment"], "trinity": [], "umms": ["expectedpayment"], "ucla": [], "commonspirit": [], "wellspan": ["expectedpayment"]}}, {"Short Name": "prinicd10proccode", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": 0, "Element Reference": 3359778728, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "prinicd10proccode", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": [], "ucd": [], "genesis": [], "ech": [], "sutter": [], "osf": [], "sluh": [], "biome": ["prinicd9proccode, prinicd10proccode"], "ucsd": ["enc - primary icd10 surgical procedure"], "bjc": ["prinicd9proccode, prinicd10proccode"], "uva": ["prinicd10proccode"], "bellin": ["prinicdproccode"], "montefiore": ["primary icd10 procedure - code", "primary icd10 procedure - code"], "rwj": ["ICD10 PX Primary - ICD10 PX"], "prisma": ["prinicd10proccode"], "ucsf": ["primary procedure code"], "sharp": [], "northshore": [], "trinity": ["prin proc code"], "umms": ["prinicdproccode"], "ucla": ["principal procedure code (icd-10)"], "commonspirit": ["prin proc icd-10-pcs code", "icd10 px primary - icd10 px code"], "wellspan": ["prinicdproccode"]}}, {"Short Name": "netpatientrevenue", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": 0, "Element Reference": **********, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "netpatientrevenue", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["total revenue", "total_revenue"], "ucd": ["netrevnue", "netrevenue"], "genesis": ["netpatientrevenue"], "ech": ["netpatientrevenue"], "sutter": ["enc - total actual payment"], "osf": [], "sluh": ["netpatientrevenue"], "biome": ["netpatientrevenue"], "ucsd": [], "bjc": ["netpatientrevenue"], "uva": ["netpatientrevenue"], "bellin": ["netrevenue"], "montefiore": ["net revenue", "net revenue"], "rwj": ["Net Patient Service Revenue"], "prisma": ["netrevenue"], "ucsf": ["net revenue", "net_revenue"], "sharp": [], "northshore": ["netpatientrevenue"], "trinity": ["net patient revenue", "estimated net revenue"], "umms": ["netrevenue"], "ucla": ["net revenue"], "commonspirit": ["chw netrev", "net revenue", "encounter level - net revenue"], "wellspan": ["netrevenue"]}}, {"Short Name": "admitsource", "DB Data Type": "<PERSON><PERSON><PERSON>(255)", "Role": null, "PHI": 0, "Element Reference": **********, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "admitsource", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["admit source - description", "enc___admission_source_desc"], "ucd": ["admitsoucedesc", "admissionsource"], "genesis": ["admitsource"], "ech": ["admitsourcedesc"], "sutter": ["enc - admission source_desc", "enc - admission source desc"], "osf": ["admit_source_ desc"], "sluh": ["admitsource"], "biome": ["admitsource"], "ucsd": ["enc - admission source desc"], "bjc": ["admitsource"], "uva": ["admitsource"], "bellin": ["admitsource"], "montefiore": ["admit source", "admit source"], "rwj": ["Admit Source - Description"], "prisma": ["admitsource"], "ucsf": ["admit source", "admit_source_desc"], "sharp": ["admit_source_desc"], "northshore": ["admitsource"], "trinity": ["admit source"], "umms": ["admitsource"], "ucla": ["admission source description"], "commonspirit": ["check-in source name"], "wellspan": ["admitsource"]}}, {"Short Name": "primarypayor", "DB Data Type": "<PERSON><PERSON><PERSON>(100)", "Role": null, "PHI": 0, "Element Reference": 2020241290, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "primarypayor", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": [], "ucd": ["payordssdesc", "payordesc", "primarypayorcode"], "genesis": ["primarypayor"], "ech": ["payorcategorydesc"], "sutter": ["payor ar l2 rollup description", "payor ar l2 rollup code desc"], "osf": ["payor_category_desc"], "sluh": ["payorcategorydesc"], "biome": ["primarypayor"], "ucsd": ["payor group desc"], "bjc": ["primarypayor"], "uva": ["primarypayor"], "bellin": ["primarypayor"], "montefiore": [], "rwj": [], "prisma": ["primarypayor"], "ucsf": ["financial_class"], "sharp": ["primarypayor"], "northshore": ["primarypayor"], "trinity": ["primary payor"], "umms": ["primarypayor"], "ucla": ["financial category description (medicare, managed care, etc.)"], "commonspirit": [], "wellspan": ["primarypayor"]}}, {"Short Name": "prinicd9proccode", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": 0, "Element Reference": **********, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "prinicd9proccode", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["primary icd9 procedure - code", "enc___primary_surgical_procedure"], "ucd": [], "genesis": [], "ech": ["principalprocedurecode"], "sutter": [], "osf": ["principal_procedure_code"], "sluh": ["principalprocedureicd"], "biome": [], "ucsd": ["enc - primary icd9 surgical procedure"], "bjc": [], "uva": [], "bellin": [], "montefiore": [], "rwj": [], "prisma": [], "ucsf": [], "sharp": ["principal_procedure_code"], "northshore": ["prindx9code/prindx10code", "prinicd9proccode/prinicd10proccode"], "trinity": [], "umms": [], "ucla": ["principal procedure code (icd-9)"], "commonspirit": [], "wellspan": []}}, {"Short Name": "attendingmdnpi", "DB Data Type": "<PERSON><PERSON><PERSON>(15)", "Role": null, "PHI": 0, "Element Reference": **********, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "attendingmdnpi", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": [], "ucd": [], "genesis": [], "ech": [], "sutter": ["attending npi"], "osf": [], "sluh": [], "biome": [], "ucsd": [], "bjc": [], "uva": [], "bellin": [], "montefiore": [], "rwj": [], "prisma": [], "ucsf": [], "sharp": [], "northshore": [], "trinity": [], "umms": [], "ucla": ["attending md upin or identifier"], "commonspirit": [], "wellspan": []}}, {"Short Name": "servicesitename", "DB Data Type": "<PERSON><PERSON><PERSON>(100)", "Role": "HOSPITAL", "PHI": 0, "Element Reference": **********, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "servicesitename", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["entity - description", "enc___facility_desc"], "ucd": [], "genesis": [], "ech": ["siteofservicedesc"], "sutter": [], "osf": ["service_site_ desc"], "sluh": ["servicesitedesc"], "biome": ["entity name"], "ucsd": ["enc - facility desc"], "bjc": ["entity name"], "uva": [], "bellin": [], "montefiore": ["discharge department - clinical entity rollup", "discharge department - clinical entity rollup"], "rwj": ["DSS Entity - Entity Description"], "prisma": [], "ucsf": [], "sharp": ["service_site_desc"], "northshore": [], "trinity": ["entity name"], "umms": [], "ucla": ["site of service description (facility name)"], "commonspirit": ["entity name"], "wellspan": []}}, {"Short Name": "los", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": 0, "Element Reference": 965560956, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "los", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["days", "enc___length_of_stay"], "ucd": [], "genesis": [], "ech": ["lengthofstay"], "sutter": ["enc - length of stay"], "osf": ["los"], "sluh": ["los"], "biome": [], "ucsd": ["enc - patient days"], "bjc": [], "uva": ["los"], "bellin": [], "montefiore": ["days", "days"], "rwj": ["Inpatient Length of Stay"], "prisma": ["los"], "ucsf": [], "sharp": ["los"], "northshore": [], "trinity": [], "umms": [], "ucla": ["length of stay"], "commonspirit": ["days"], "wellspan": []}}, {"Short Name": "refferingphyname", "DB Data Type": "<PERSON><PERSON><PERSON>(50)", "Role": null, "PHI": 0, "Element Reference": **********, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "refferingphyname", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["refer physician - physician", "enc___referring_physician_desc"], "ucd": [], "genesis": [], "ech": ["referring_md_name"], "sutter": ["enc - referring physician_desc", "enc - referring physician desc"], "osf": ["md_referring_name"], "sluh": ["referringmdname"], "biome": [], "ucsd": [], "bjc": [], "uva": ["referringmdname"], "bellin": [], "montefiore": [], "rwj": ["Refer Physician - Physician Name"], "prisma": [], "ucsf": [], "sharp": ["md_referring_name"], "northshore": [], "trinity": [], "umms": [], "ucla": ["referring md name"], "commonspirit": [], "wellspan": []}}, {"Short Name": "hospname", "DB Data Type": "<PERSON><PERSON><PERSON>(100)", "Role": "HOSPITAL", "PHI": 0, "Element Reference": 1076784334, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "hospname", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": [], "ucd": [], "genesis": ["entity"], "ech": [], "sutter": ["enc - facility_desc"], "osf": [], "sluh": [], "biome": [], "ucsd": [], "bjc": [], "uva": ["encounterfacilityname"], "bellin": ["encounter facility name"], "montefiore": [], "rwj": [], "prisma": ["encounterfacilityname"], "ucsf": ["facility"], "sharp": [], "northshore": ["encounterfacilityname"], "trinity": [], "umms": ["encounter facility name"], "ucla": [], "commonspirit": [], "wellspan": ["encounter facility name"]}}, {"Short Name": "ethnicity", "DB Data Type": "<PERSON><PERSON><PERSON>(100)", "Role": null, "PHI": 0, "Element Reference": 2841753808, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "ethnicity", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["ethnicity - description", "ethnicity description"], "ucd": ["ethnicitydesc"], "genesis": [], "ech": [], "sutter": [], "osf": [], "sluh": [], "biome": [], "ucsd": [], "bjc": [], "uva": [], "bellin": ["ethnicity"], "montefiore": [], "rwj": [], "prisma": [], "ucsf": ["ethnicity"], "sharp": [], "northshore": [], "trinity": [], "umms": ["ethnicity"], "ucla": ["patient ethnicity"], "commonspirit": [], "wellspan": ["ethnicity"]}}, {"Short Name": "financialpayorclass", "DB Data Type": "<PERSON><PERSON><PERSON>(100)", "Role": null, "PHI": 0, "Element Reference": 363050933, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "financialpayorclass", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": [], "ucd": [], "genesis": ["financialpayorclass"], "ech": [], "sutter": [], "osf": [], "sluh": [], "biome": ["financialpayorclass"], "ucsd": ["financial class group desc"], "bjc": ["financialpayorclass"], "uva": ["financialpayorclass"], "bellin": ["financialpayorclass"], "montefiore": ["insurance plan 1 - financial class", "insurance plan 1 - financial class"], "rwj": ["Insurance Plan 1 - Financial Class"], "prisma": ["financialpayorclass"], "ucsf": ["payor_class"], "sharp": [], "northshore": ["financialpayorclass"], "trinity": [], "umms": ["financialpayorclass"], "ucla": [], "commonspirit": ["chw payor category"], "wellspan": ["financialpayorclass"]}}, {"Short Name": "attendingmdname", "DB Data Type": "<PERSON><PERSON><PERSON>(80)", "Role": null, "PHI": 0, "Element Reference": 1676828296, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "attendingmdname", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": [], "ucd": ["attendingphysician"], "genesis": [], "ech": [], "sutter": ["enc - attending physician_desc"], "osf": ["md_attending_name"], "sluh": ["attendingmdname"], "biome": [], "ucsd": [], "bjc": [], "uva": ["attendingmdname"], "bellin": [], "montefiore": ["attend physician - physician"], "rwj": ["Attend Physician - Physician Name"], "prisma": [], "ucsf": ["admitting md name"], "sharp": [], "northshore": [], "trinity": [], "umms": [], "ucla": ["attending md name"], "commonspirit": [], "wellspan": []}}, {"Short Name": "servicelinerollup", "DB Data Type": "<PERSON><PERSON><PERSON>(50)", "Role": null, "PHI": 0, "Element Reference": 160281864, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "servicelinerollup", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": [], "ucd": [], "genesis": [], "ech": [], "sutter": ["service line rollup"], "osf": [], "sluh": [], "biome": [], "ucsd": [], "bjc": [], "uva": [], "bellin": [], "montefiore": [], "rwj": [], "prisma": [], "ucsf": [], "sharp": [], "northshore": [], "trinity": [], "umms": [], "ucla": [], "commonspirit": [], "wellspan": []}}, {"Short Name": "referringmdrole", "DB Data Type": "<PERSON><PERSON><PERSON>(100)", "Role": null, "PHI": 0, "Element Reference": 928377168, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "referringmdrole", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": ["ref md specialty_code_description"], "ucd": [], "genesis": [], "ech": [], "sutter": ["referring specialty"], "osf": ["md_referring_specialty"], "sluh": ["referringmdspecialty"], "biome": [], "ucsd": [], "bjc": [], "uva": ["referringmdspecialty"], "bellin": [], "montefiore": [], "rwj": ["Refer Physician - Specialty"], "prisma": [], "ucsf": [], "sharp": [], "northshore": [], "trinity": [], "umms": [], "ucla": ["referring md specialty"], "commonspirit": [], "wellspan": []}}, {"Short Name": "servicesitecode", "DB Data Type": "<PERSON><PERSON><PERSON>(100)", "Role": "HOSPITAL", "PHI": 0, "Element Reference": 35494000, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "servicesitecode", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"uci": [], "ucd": [], "genesis": [], "ech": [], "sutter": [], "osf": [], "sluh": [], "biome": [], "ucsd": [], "bjc": [], "uva": [], "bellin": [], "montefiore": [], "rwj": [], "prisma": [], "ucsf": [], "sharp": [], "northshore": [], "trinity": [], "umms": [], "ucla": [], "commonspirit": [], "wellspan": []}}]}]}}