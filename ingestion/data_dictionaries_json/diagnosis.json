{"Dictionary": {"Dataset": {"Code": "diagnosis", "Id": 163}, "Version": "1.0", "Type": "<PERSON><PERSON><PERSON>", "Sections": [{"Container Class": "defaultContainer", "Parent Section": "Root", "Section Display Name": "<PERSON><PERSON><PERSON>", "Section Code": "DEFAULT", "Section Type": "Section", "Cardinality": "1..1", "Table": "RAW", "Elements": [{"Short Name": "ServiceSiteName", "DB Data Type": "<PERSON><PERSON><PERSON>(100)", "Role": "HOSPITAL", "PHI": 0, "Element Reference": 2076411968, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "ServiceSiteName", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"wellspan": [], "trinity": [], "sutter": [], "ucsd": [], "rwj": [], "commonspirit": [], "montefiore": [], "ech": [], "uva": [], "ucla": [], "umms": [], "bjc": [], "uci": [], "bellin": [], "ucd": [], "ucsf": []}}, {"Short Name": "EncounterNumber", "DB Data Type": "<PERSON><PERSON><PERSON>(64)", "Role": "IDENTIFIER", "PHI": 1, "Element Reference": **********, "Definition": null, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "EncounterNumber", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"wellspan": ["encounternumber"], "trinity": ["encounternumber"], "sutter": ["hsp_account_id"], "ucsd": ["encounternumber"], "rwj": ["patient encounter - encounter record number"], "commonspirit": ["patient account number"], "montefiore": ["patient encounter - encounter record number"], "ech": ["encounternumber"], "uva": ["hsp_account_id"], "ucla": ["encounter number"], "umms": ["encounternumber"], "bjc": ["ecd number (pe ecd extension)"], "uci": ["enc___patient_account"], "bellin": ["encounternumber"], "ucd": ["patientaccount"], "ucsf": ["patient_account"]}}, {"Short Name": "DiagSeqNo", "DB Data Type": "<PERSON><PERSON><PERSON>(255)", "Role": null, "PHI": 0, "Element Reference": **********, "Definition": null, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "DiagSeqNo", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"wellspan": ["diagseqno"], "trinity": ["diagseqno"], "sutter": ["dx_code_line"], "ucsd": ["icd10 dx - sequence number"], "rwj": ["icd10 dx sequence number - sequence number"], "commonspirit": ["diagseqno"], "montefiore": ["all icd10 diagnosis sequence number - sequence number"], "ech": ["diagnosissequencenumber"], "uva": ["diagseqno"], "ucla": ["secondary diagnosis sequence"], "umms": ["diagseqno"], "bjc": ["icd-10-cm sec diag coded sequence"], "uci": ["dx___sequence_number"], "bellin": ["diagseqno"], "ucd": ["icd10dxsequence"], "ucsf": ["icd10_sequence_no"]}}, {"Short Name": "DiagCode", "DB Data Type": "<PERSON><PERSON><PERSON>(255)", "Role": null, "PHI": 0, "Element Reference": 860361717, "Definition": null, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "DiagCode", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"wellspan": ["diagcode"], "trinity": ["diagnosiscode"], "sutter": ["diagnosis_code_dx_code"], "ucsd": ["icd10 dx - diagnosis code"], "rwj": ["icd10 dx - code"], "commonspirit": ["diagcode"], "montefiore": ["all icd10 diagnosis - icd10 diagnosis"], "ech": ["secondaryicd9diagnosiscode"], "uva": ["diagcode"], "ucla": ["secondary diagnosis (icd-10 code)"], "umms": ["diagcode"], "bjc": ["icd-10-cm sec diag code"], "uci": ["dx___diagnosis_code"], "bellin": ["diagcode"], "ucd": ["icd10dxcode"], "ucsf": ["icd10_diagnosis_code"]}}, {"Short Name": "DiagType", "DB Data Type": "<PERSON><PERSON><PERSON>(255)", "Role": null, "PHI": 0, "Element Reference": 4076478615, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "DiagType", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"wellspan": [], "trinity": [], "sutter": [], "ucsd": [], "rwj": [], "commonspirit": [], "montefiore": [], "ech": [], "uva": [], "ucla": [], "umms": [], "bjc": [], "uci": [], "bellin": [], "ucd": [], "ucsf": []}}, {"Short Name": "POA", "DB Data Type": "<PERSON><PERSON><PERSON>(255)", "Role": null, "PHI": 0, "Element Reference": 2960379293, "Definition": null, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "POA", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"wellspan": ["poa"], "trinity": ["poa"], "sutter": ["final_dx_poa_c"], "ucsd": ["icd10 dx - present on admission"], "rwj": ["present on admission - description"], "commonspirit": ["poa"], "montefiore": ["all icd10 diagnosis present on admission - present on admission"], "ech": ["secondarydiagnosispoaflag"], "uva": ["poa"], "ucla": ["secondary diagnosis present on admission (poa) indicator"], "umms": ["poa"], "bjc": ["present on adm code"], "uci": ["dx___present_on_admission"], "bellin": ["poa"], "ucd": ["poa"], "ucsf": ["icd10_poa_flag"]}}]}]}}