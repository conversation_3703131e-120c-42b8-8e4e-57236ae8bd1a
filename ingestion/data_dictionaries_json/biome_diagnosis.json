{"Dictionary": {"Dataset": {"Code": "diagnosis", "Id": 163}, "Version": "1.0", "Type": "Biome", "Sections": [{"Container Class": "defaultContainer", "Parent Section": "Root", "Section Display Name": "<PERSON><PERSON><PERSON>", "Section Code": "DEFAULT", "Section Type": "Section", "Cardinality": "1..1", "Table": "RAW", "Elements": [{"Element Reference": 2076411968, "Short Name": "servicesitename", "DB Data Type": "<PERSON><PERSON><PERSON>(100)", "Role": null, "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "servicesitename", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 4090799199, "Short Name": "clientfileid", "DB Data Type": "<PERSON><PERSON><PERSON>(17)", "Role": null, "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "clientfileid", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 1213110851, "Short Name": "filename", "DB Data Type": "<PERSON><PERSON><PERSON>(98)", "Role": null, "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "filename", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 2670785740, "Short Name": "version", "DB Data Type": "<PERSON><PERSON><PERSON>(22)", "Role": null, "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "version", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 2960379293, "Short Name": "poa", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": false, "Definition": {"Title": "Secondary diagnosis present on admission", "Definition": "Indicates if secondary diagnosis was present on admission (Y=Present, N=No, E=Undeclared)", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "poa", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 743086825, "Short Name": "datasetname", "DB Data Type": "<PERSON><PERSON><PERSON>(24)", "Role": null, "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "datasetname", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 4076478615, "Short Name": "diagtype", "DB Data Type": "<PERSON><PERSON><PERSON>(12)", "Role": null, "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "diagtype", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 2236893199, "Short Name": "diagseqno", "DB Data Type": "<PERSON><PERSON><PERSON>(20)", "Role": null, "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "diagseqno", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 3699837234, "Short Name": "encounternumber", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": "IDENTIFIER", "PHI": true, "Definition": {"Title": "Encounter Number", "Definition": "Encounter / visit / discharge number", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "encounternumber", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 860361717, "Short Name": "diagcode", "DB Data Type": "<PERSON><PERSON><PERSON>(10)", "Role": null, "PHI": false, "Definition": {"Title": "Secondary diagnosis ICD-9/10 code", "Definition": "Secondary diagnosis ICD-9/10 code, include each secondary diagnosis code as a separate row", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "diagcode", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 4035169011, "Short Name": "tenantid", "DB Data Type": "<PERSON><PERSON><PERSON>(25)", "Role": "TENANT", "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "tenantid", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 1076784334, "Short Name": "hospname", "DB Data Type": "<PERSON><PERSON><PERSON>(24)", "Role": null, "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "hospname", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 1614028513, "Short Name": "biomeencounterid", "DB Data Type": "bigint unsigned", "Role": null, "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "biomeencounterid", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 3855026243, "Short Name": "biomeimportdt", "DB Data Type": "<PERSON><PERSON><PERSON>(38)", "Role": null, "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "biomeimportdt", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}]}]}}