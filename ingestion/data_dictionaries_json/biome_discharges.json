{"Dictionary": {"Dataset": {"Code": "discharges", "Id": 108}, "Version": "1.0", "Type": "Biome", "Sections": [{"Container Class": "defaultContainer", "Parent Section": "Root", "Section Display Name": "<PERSON><PERSON><PERSON>", "Section Code": "DEFAULT", "Section Type": "Section", "Cardinality": "1..1", "Table": "RAW", "Elements": [{"Element Reference": **********, "Short Name": "dischargestatus", "DB Data Type": "<PERSON><PERSON><PERSON>(60)", "Role": null, "PHI": false, "Definition": {"Title": "Discharge Status", "Definition": "Indicate whether the patient was alive or deceased at discharge.", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "Discharge Status", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "dischargestatus", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": **********, "Short Name": "careentityid", "DB Data Type": "<PERSON><PERSON><PERSON>(25)", "Role": "CAREENTITY", "PHI": false, "Definition": {"Title": "Care Entity Id", "Definition": "Care Entity Id", "Source": "Biome", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "careentityid", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 965560956, "Short Name": "los", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": false, "Definition": {"Title": "Length of Stay", "Definition": "Length of Stay", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "los", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 743086825, "Short Name": "datasetname", "DB Data Type": "<PERSON><PERSON><PERSON>(24)", "Role": null, "PHI": false, "Definition": {"Title": "Dataset Name", "Definition": "Dataset Name", "Source": "Biome", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "datasetname", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 3223988868, "Short Name": "prindx9code", "DB Data Type": "<PERSON><PERSON><PERSON>(10)", "Role": null, "PHI": false, "Definition": {"Title": "Principal diagnosis ICD-9 code", "Definition": "Principal diagnosis ICD-9 code", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "prindx9code", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 4035169011, "Short Name": "tenantid", "DB Data Type": "<PERSON><PERSON><PERSON>(25)", "Role": "TENANT", "PHI": false, "Definition": {"Title": "Tenant Id", "Definition": "Tenant Id", "Source": "Biome", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "tenantid", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 225185267, "Short Name": "indirectcost", "DB Data Type": "decimal(18,6)", "Role": null, "PHI": false, "Definition": {"Title": "Indirect Cost", "Definition": "Indirect Cost", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "indirectcost", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": **********, "Short Name": "gender", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": "GENDER", "PHI": true, "Definition": {"Title": "Patient Gender", "Definition": "Patient gender (M=Male, F=Female, U=Undeclared or Unknown)", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "gender", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": **********, "Short Name": "originalmrn", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": "MEDRECNUM", "PHI": true, "Definition": {"Title": "Medical Record Number", "Definition": "Medical Record Number to identify the patient", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "originalmrn", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": **********, "Short Name": "medrecn", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": "MEDRECNUM", "PHI": true, "Definition": {"Title": "Medical Record Number", "Definition": "Medical Record Number to identify the patient", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "medrecn", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": **********, "Short Name": "secondarypayor", "DB Data Type": "<PERSON><PERSON><PERSON>(100)", "Role": null, "PHI": false, "Definition": {"Title": "", "Definition": "", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "Secondary Payor Category", "Selection Name": "Secondary Payor Category", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "secondarypayor", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 3867833841, "Short Name": "prindx10code", "DB Data Type": "<PERSON><PERSON><PERSON>(10)", "Role": null, "PHI": false, "Definition": {"Title": "Principal diagnosis ICD-10 code", "Definition": "Principal diagnosis ICD-10 code", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "prindx10code", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 932495030, "Short Name": "admitdx9code", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": false, "Definition": {"Title": "Admit diagnosis ICD-9 code", "Definition": "Admit diagnosis ICD-9 code", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "admitdx9code", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 1614028513, "Short Name": "biomeencounterid", "DB Data Type": "bigint unsigned", "Role": null, "PHI": false, "Definition": {"Title": "Biome Encounter Id", "Definition": "Biome Encounter Id", "Source": "Biome", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "biomeencounterid", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 182991122, "Short Name": "dob", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": "DOB", "PHI": true, "Definition": {"Title": "Patient's Date of Birth", "Definition": "<PERSON><PERSON>'s date of birth in ISO-8601 or ANSI INCITS 30-1997 (R2008) format", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "dob", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": **********, "Short Name": "inoutcode", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": "INOUTCODE", "PHI": false, "Definition": {"Title": "Encounter class", "Definition": "Encounter class e.g. Inpatient, Outpatient, Emergency, etc. ", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "inoutcode", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": **********, "Short Name": "admittype", "DB Data Type": "<PERSON><PERSON><PERSON>(50)", "Role": null, "PHI": false, "Definition": {"Title": "Admission status", "Definition": "Admission status e.g. Elective, <PERSON>rgent, Emergent, Salvage", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "admittype", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 2579321924, "Short Name": "primaryinsurancegroup", "DB Data Type": "<PERSON><PERSON><PERSON>(100)", "Role": null, "PHI": false, "Definition": {"Title": "Primary Insurance Group", "Definition": "Primary Insurance Group", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "primaryinsurancegroup", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 3060211418, "Short Name": "dctime", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": "TIME", "PHI": false, "Definition": {"Title": "Discharge Time", "Definition": "Discharge time (in ISO-8601 format)", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "dctime", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 3358798491, "Short Name": "admitdate", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": "ADMISSION_DATE", "PHI": true, "Definition": {"Title": "Admission Date", "Definition": "Admit date in ISO-8601 or ANSI INCITS 30-1997 (R2008) format", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "admitdate", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 1686880744, "Short Name": "icudays", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": false, "Definition": {"Title": "ICU Days", "Definition": "Number of days spent in ICU", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "icudays", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 3373283960, "Short Name": "directcost", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": false, "Definition": {"Title": "Direct Cost", "Definition": "Direct Cost", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "directcost", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": **********, "Short Name": "age", "DB Data Type": "decimal(6,3)", "Role": "AGE", "PHI": true, "Definition": {"Title": "Age", "Definition": "Patient's age in years at the time of admission", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "age", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": **********, "Short Name": "encounternumber", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": "IDENTIFIER", "PHI": true, "Definition": {"Title": "Encounter Number", "Definition": "Identifies unique visit/discharge/encounter for a patient", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "encounternumber", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 257228906, "Short Name": "patzip", "DB Data Type": "<PERSON><PERSON><PERSON>(9)", "Role": "PATIENT_ZIP", "PHI": true, "Definition": {"Title": "Patient Zip Code", "Definition": "Patient zip code; 5 or 9 digits", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "patzip", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 521579640, "Short Name": "patienttype", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": false, "Definition": {"Title": "Patient Type", "Definition": "Encounter class e.g. Inpatient, Outpatient, Emergency, etc. ", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "patienttype", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": **********, "Short Name": "race", "DB Data Type": "<PERSON><PERSON><PERSON>(50)", "Role": null, "PHI": false, "Definition": {"Title": "Race", "Definition": "Race", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "race", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 96662065, "Short Name": "admittime", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": "TIME", "PHI": false, "Definition": {"Title": "Admission Time", "Definition": "Admit time (in ISO-8601 format)", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "admittime", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 958782091, "Short Name": "dischargedate", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": "ANCHOR_DATE", "PHI": true, "Definition": {"Title": "Discharge Date", "Definition": "Discharge date in ISO-8601 or ANSI INCITS 30-1997 (R2008) format", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "dischargedate", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 3102164369, "Short Name": "drgcode", "DB Data Type": "<PERSON><PERSON><PERSON>(10)", "Role": null, "PHI": false, "Definition": {"Title": "Admit MS-DRG", "Definition": "Admit MS-DRG code", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "drgcode", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 1707967097, "Short Name": "totalcost", "DB Data Type": "decimal(18,6)", "Role": null, "PHI": false, "Definition": {"Title": "Total Cost", "Definition": "Calculated as: Actual (or Expected) Payment - Total Cost", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "totalcost", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 206430457, "Short Name": "admitdx10code", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": false, "Definition": {"Title": "Admit diagnosis ICD-10 code", "Definition": "Admit diagnosis ICD-10 code", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "admitdx10code", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 1213110851, "Short Name": "filename", "DB Data Type": "<PERSON><PERSON><PERSON>(91)", "Role": null, "PHI": false, "Definition": {"Title": "File Name", "Definition": "File Name", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "filename", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 531879229, "Short Name": "expectedpayment", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": false, "Definition": {"Title": "Expected Payment", "Definition": "Calculated as: Actual (or Expected) Payment - Total Cost", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "expectedpayment", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 3359778728, "Short Name": "prinicd10proccode", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": false, "Definition": {"Title": "Principal procedure ICD-10 code", "Definition": "Principal procedure ICD-10 code", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "prinicd10proccode", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": **********, "Short Name": "netpatientrevenue", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": false, "Definition": {"Title": "Net patient revenue", "Definition": "Net patient revenue (actual payment)", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "netpatientrevenue", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": **********, "Short Name": "hospname", "DB Data Type": "<PERSON><PERSON><PERSON>(22)", "Role": "HOSPITAL", "PHI": false, "Definition": {"Title": "Hospital Name", "Definition": "Hospital Name", "Source": "Biome", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "hospname", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": **********, "Short Name": "version", "DB Data Type": "<PERSON><PERSON><PERSON>(22)", "Role": null, "PHI": false, "Definition": {"Title": "Version", "Definition": "File Version", "Source": "Biome", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "version", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 1775830248, "Short Name": "admitsource", "DB Data Type": "<PERSON><PERSON><PERSON>(50)", "Role": null, "PHI": false, "Definition": {"Title": "Admit Source", "Definition": "Descriptive admit source; see http://hl7.org/fhir/admit-source for details.", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "admitsource", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 2020241290, "Short Name": "primarypayor", "DB Data Type": "<PERSON><PERSON><PERSON>(100)", "Role": null, "PHI": false, "Definition": {"Title": "Primary Payor Category", "Definition": "Primary Payor Category", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "primarypayor", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 2841268775, "Short Name": "prinicd9proccode", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": false, "Definition": {"Title": "Principal procedure ICD-9 code", "Definition": "Principal procedure ICD-9 code", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "prinicd9proccode", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 928377168, "Short Name": "referringmdrole", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": false, "Definition": {"Title": "Referring MD role", "Definition": "Role of the referring MD", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "referringmdrole", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 363050933, "Short Name": "financialpayorclass", "DB Data Type": "<PERSON><PERSON><PERSON>(100)", "Role": null, "PHI": false, "Definition": {"Title": "Financial Payor Class Category", "Definition": "Financial payor class Category", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "financialpayorclass", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 2076411968, "Short Name": "servicesitename", "DB Data Type": "<PERSON><PERSON><PERSON>(100)", "Role": null, "PHI": false, "Definition": {"Title": "Service Site Name", "Definition": "Hospital or clinic location names", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "servicesitename", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 2841753808, "Short Name": "ethnicity", "DB Data Type": "<PERSON><PERSON><PERSON>(20)", "Role": null, "PHI": false, "Definition": {"Title": "ethnicity", "Definition": "ethnicity", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "ethnicity", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 1676828296, "Short Name": "attendingmdname", "DB Data Type": "<PERSON><PERSON><PERSON>(64)", "Role": null, "PHI": false, "Definition": {"Title": "Attending MD Name", "Definition": "Attending MD Name", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "attendingmdname", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 3114509527, "Short Name": "refferingphyname", "DB Data Type": "<PERSON><PERSON><PERSON>(50)", "Role": null, "PHI": false, "Definition": {"Title": "Referring Physician Name", "Definition": "Referring Physician Name", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "refferingphyname", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": **********, "Short Name": "attendingmdnpi", "DB Data Type": "<PERSON><PERSON><PERSON>(15)", "Role": null, "PHI": false, "Definition": {"Title": "Attending MD NPI", "Definition": "Attending MD NPI", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "attendingmdnpi", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 160281864, "Short Name": "servicelinerollup", "DB Data Type": "<PERSON><PERSON><PERSON>(35)", "Role": null, "PHI": false, "Definition": {"Title": "Service Line Roll up", "Definition": "Hierarchical structure of service categories", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "servicelinerollup", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": **********, "Short Name": "clientfileid", "DB Data Type": "<PERSON><PERSON><PERSON>(17)", "Role": null, "PHI": false, "Definition": {"Title": "Client File Id", "Definition": "Client File Id", "Source": "Biome", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "clientfileid", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 3855026243, "Short Name": "biomeimportdt", "DB Data Type": "<PERSON><PERSON><PERSON>(35)", "Role": "DATE", "PHI": false, "Definition": {"Title": "Biome Import DateTime", "Definition": "Date and Time when ingestion received the file", "Source": "Biome", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "biomeimportdt", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}]}]}}