{"Dictionary": {"Dataset": {"Code": "cpt", "Id": 179}, "Version": "1.0", "Type": "<PERSON><PERSON><PERSON>", "Sections": [{"Container Class": "defaultContainer", "Parent Section": "Root", "Section Display Name": "<PERSON><PERSON><PERSON>", "Section Code": "DEFAULT", "Section Type": "Section", "Cardinality": "1..1", "Table": "RAW", "Elements": [{"Short Name": "ServiceSiteName", "DB Data Type": "<PERSON><PERSON><PERSON>(100)", "Role": "HOSPITAL", "PHI": 0, "Element Reference": 2076411968, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "ServiceSiteName", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"montefiore": [], "sutter": [], "umms": [], "ucla": [], "uva": [], "wellspan": [], "bellin": [], "ucsf": []}}, {"Short Name": "EncounterNumber", "DB Data Type": "<PERSON><PERSON><PERSON>(100)", "Role": "IDENTIFIER", "PHI": 1, "Element Reference": **********, "Definition": null, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "EncounterNumber", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"montefiore": ["patient encounter - encounter record number"], "sutter": ["hsp_account_id"], "umms": ["encounternumber"], "ucla": ["encounter number"], "uva": ["encounternumber"], "wellspan": ["encounternumber"], "bellin": ["encounternumber"], "ucsf": ["patientaccount"]}}, {"Short Name": "CPT4HCPCSCode", "DB Data Type": "<PERSON><PERSON><PERSON>(255)", "Role": null, "PHI": 0, "Element Reference": **********, "Definition": null, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "CPT4HCPCSCode", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"montefiore": [], "sutter": [], "umms": [], "ucla": [], "uva": ["CPT4HCPCSCode"], "wellspan": [], "bellin": [], "ucsf": []}}, {"Short Name": "CPTSeqNo", "DB Data Type": "<PERSON><PERSON><PERSON>(255)", "Role": null, "PHI": 0, "Element Reference": **********, "Definition": null, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "CPTSeqNo", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"montefiore": [], "sutter": [], "umms": [], "ucla": [], "uva": ["CPTCodeSequence"], "wellspan": [], "bellin": [], "ucsf": []}}, {"Short Name": "CPTDate", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": "|DATE|ANCHOR_DATE|", "PHI": 1, "Element Reference": 934113661, "Definition": null, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "CPTDate", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"montefiore": [], "sutter": [], "umms": [], "ucla": [], "uva": ["cptdate"], "wellspan": [], "bellin": [], "ucsf": []}}, {"Short Name": "CPTModifier1", "DB Data Type": "<PERSON><PERSON><PERSON>(255)", "Role": null, "PHI": 0, "Element Reference": 1398750117, "Definition": null, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "CPTModifier1", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"montefiore": [], "sutter": [], "umms": [], "ucla": [], "uva": [], "wellspan": [], "bellin": [], "ucsf": []}}, {"Short Name": "CPTModifier2", "DB Data Type": "<PERSON><PERSON><PERSON>(255)", "Role": null, "PHI": 0, "Element Reference": 2613064618, "Definition": null, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "CPTModifier2", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"montefiore": [], "sutter": [], "umms": [], "ucla": [], "uva": [], "wellspan": [], "bellin": [], "ucsf": []}}, {"Short Name": "CPTModifier3", "DB Data Type": "<PERSON><PERSON><PERSON>(255)", "Role": null, "PHI": 0, "Element Reference": 3545824584, "Definition": null, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "CPTModifier3", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"montefiore": [], "sutter": [], "umms": [], "ucla": [], "uva": [], "wellspan": [], "bellin": [], "ucsf": []}}, {"Short Name": "CPTCharge", "DB Data Type": "<PERSON><PERSON><PERSON>(100)", "Role": null, "PHI": 0, "Element Reference": 3485011650, "Definition": null, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "CPTCharge", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"montefiore": [], "sutter": [], "umms": [], "ucla": [], "uva": [], "wellspan": [], "bellin": [], "ucsf": []}}, {"Short Name": "BatchNumber", "DB Data Type": "<PERSON><PERSON><PERSON>(255)", "Role": null, "PHI": 0, "Element Reference": 1442714507, "Definition": null, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "BatchNumber", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"montefiore": [], "sutter": [], "umms": [], "ucla": [], "uva": [], "wellspan": [], "bellin": [], "ucsf": []}}]}]}}