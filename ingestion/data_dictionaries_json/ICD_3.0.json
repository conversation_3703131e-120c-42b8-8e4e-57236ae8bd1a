{"Dictionary": {"Dataset": {"Code": "ICD", "Id": 107}, "Version": "3.0", "Type": "<PERSON><PERSON><PERSON>", "Sections": [{"Container Class": "patientContainer", "Parent Section": "Root", "Section Display Name": "Demographics", "Section Code": "DEMOGRAPHICS", "Section Type": "Section", "Cardinality": "1..1", "Table": "DEMOGRAPHICS", "Elements": [{"Element Reference": 2000, "Name": "Last Name", "Section Display Name": "Demographics", "Coding Instructions": "Indicate the patient's last name. Hyphenated names should be recorded with a hyphen.", "Target Value": "The value on arrival at this facility", "Short Name": "LastName", "Data Type": "LN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2010, "Name": "First Name", "Section Display Name": "Demographics", "Coding Instructions": "Indicate the patient's first name.", "Target Value": "The value on arrival at this facility", "Short Name": "FirstName", "Data Type": "FN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2020, "Name": "Middle Name", "Section Display Name": "Demographics", "Coding Instructions": "Indicate the patient's middle name.\n\nNote(s):\nIt is acceptable to specify the middle initial.\n\nIf there is no middle name given, leave field blank.\n\nIf there are multiple middle names, enter all of the middle names sequentially.\n\nIf the name exceeds 50 characters, enter the first 50 letters only.", "Target Value": "The value on arrival at this facility", "Short Name": "MidName", "Data Type": "MN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2050, "Name": "Birth Date", "Section Display Name": "Demographics", "Coding Instructions": "Indicate the patient's date of birth.", "Target Value": "The value on arrival at this facility", "Short Name": "DOB", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2030, "Name": "SSN", "Section Display Name": "Demographics", "Coding Instructions": "Indicate the patient's United States Social Security Number (SSN).\n\nNote(s):\nIf the patient does not have a US Social Security Number (SSN), leave blank and check 'SSN NA'.", "Target Value": "The value on arrival at this facility", "Short Name": "SSN", "Data Type": "ST", "Precision": "9", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.4.1", "Code System": "2.16.840.1.113883.4.1", "Code System Name": "United States Social Security Number (SSN)", "Vendor Instruction": "SSN (2030) must be 9 numeric characters long", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2031, "Name": "SSN N/A", "Section Display Name": "Demographics", "Coding Instructions": "Indicate if the patient does not have a United States Social Security Number (SSN).", "Target Value": "The value on arrival at this facility", "Short Name": "SSNNA", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.4.1", "Code System": "2.16.840.1.113883.4.1", "Code System Name": "United States Social Security Number (SSN)", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2040, "Name": "Patient ID", "Section Display Name": "Demographics", "Coding Instructions": "Indicate the number created and automatically inserted by the software that uniquely identifies this patient.\n\nNote(s):\nOnce assigned to a patient at the participating facility, this number will never be changed or reassigned to a different patient. If the patient returns to the same participating facility or for follow up, they will receive this same unique patient identifier.", "Target Value": "The value on arrival at this facility", "Short Name": "NCDRPatientID", "Data Type": "NUM", "Precision": "9", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "Automatic", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.842", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": null, "Usual Range Min": null, "Usual Range Max": null, "Valid Range Min": 1.0, "Valid Range Max": "999,999,999"}], "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2045, "Name": "Other ID", "Section Display Name": "Demographics", "Coding Instructions": "Indicate an optional patient identifier, such as medical record number, that can be associated with the patient.", "Target Value": "N/A", "Short Name": "OtherID", "Data Type": "ST", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.843", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2060, "Name": "Sex", "Section Display Name": "Demographics", "Coding Instructions": "Indicate the patient's sex at birth.", "Target Value": "The value on arrival at this facility", "Short Name": "Sex", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Sex", "Code": "M", "Code System": "2.16.840.1.113883.5.1", "Code System Name": "HL7 Administrative Gender", "Selection Name": "Male", "Selection Definition": null, "Display Order": 1}, {"Name": "Sex", "Code": "F", "Code System": "2.16.840.1.113883.5.1", "Code System Name": "HL7 Administrative Gender", "Selection Name": "Female", "Selection Definition": null, "Display Order": 2}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2065, "Name": "Patient Zip Code", "Section Display Name": "Demographics", "Coding Instructions": "Indicate the patient's United States Postal Service zip code of their primary residence.\n\nNote(s):\nIf the patient does not have a U.S. residence, or is homeless, leave blank and check 'Zip Code NA'.", "Target Value": "The value on arrival at this facility", "Short Name": "ZipCode", "Data Type": "ST", "Precision": "5", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Patient Zip Code (2065) must be 5 numeric characters long", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2066, "Name": "Zip Code N/A", "Section Display Name": "Demographics", "Coding Instructions": "Indicate if the patient does not have a United States Postal Service zip code.\n\nNote(s):\nThis includes patients who do not have a U.S. residence or are homeless.", "Target Value": "The value on arrival at this facility", "Short Name": "ZipCodeNA", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2070, "Name": "Race - White", "Section Display Name": "Demographics", "Coding Instructions": "Indicate if the patient is White as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "<PERSON><PERSON><PERSON><PERSON>", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2106-3", "Code System": "2.16.840.1.113883.5.104", "Code System Name": "HL7 Race", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "<PERSON> (race)", "Definition": "Having origins in any of the original peoples of Europe, the Middle East, or North Africa.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": null}, {"Element Reference": 2071, "Name": "Race - Black/African American", "Section Display Name": "Demographics", "Coding Instructions": "Indicate if the patient is Black or African American as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "RaceBlack", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2054-5", "Code System": "2.16.840.1.113883.5.104", "Code System Name": "HL7 Race", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Black/African American (race)", "Definition": "Having origins in any of the black racial groups of Africa. Terms such as \"Haitian\" or \"Negro\" can be used in addition to \"Black or African American.\"", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": null}, {"Element Reference": 2073, "Name": "Race - American Indian/Alaskan Native", "Section Display Name": "Demographics", "Coding Instructions": "Indicate if the patient is American Indian or Alaskan Native as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "RaceAmIndian", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "1002-5", "Code System": "2.16.840.1.113883.5.104", "Code System Name": "HL7 Race", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "American Indian or Alaskan Native (race)", "Definition": "Having origins in any of the original peoples of North and South America (including Central America), and who maintains tribal affiliation or community attachment.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": null}, {"Element Reference": 2072, "Name": "Race - Asian", "Section Display Name": "Demographics", "Coding Instructions": "Indicate if the patient is Asian as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "Race<PERSON>ian", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2028-9", "Code System": "2.16.840.1.113883.5.104", "Code System Name": "HL7 Race", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Asian (race)", "Definition": "Having origins in any of the original peoples of the Far East, Southeast Asia, or the Indian subcontinent including, for example, Cambodia, China, India, Japan, Korea, Malaysia, Pakistan, the Philippine Islands, Thailand, and Vietnam.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": null}, {"Element Reference": 2074, "Name": "Race - Native Hawaiian/Pacific Islander", "Section Display Name": "Demographics", "Coding Instructions": "Indicate if the patient is Native Hawaiian or Pacific Islander as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "RaceNatHaw", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2076-8", "Code System": "2.16.840.1.113883.5.104", "Code System Name": "HL7 Race", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Race - Native Hawaiian/Pacific Islander - Native Hawaiian", "Definition": "Having origins in any of the original peoples of Hawaii, Guam, Samoa, or other Pacific Islands.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": null}, {"Element Reference": 2076, "Name": "Hispanic or Latino Ethnicity", "Section Display Name": "Demographics", "Coding Instructions": "Indicate if the patient is of Hispanic or Latino ethnicity as determined by the patient/family.", "Target Value": "The value on arrival at this facility", "Short Name": "<PERSON><PERSON><PERSON><PERSON>", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2135-2", "Code System": "2.16.840.1.113883.5.50", "Code System Name": "HL7 Ethnicity", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Hispanic or Latino Ethnicity", "Definition": "A person of Mexican, Puerto Rican, Cuban, South or Central American, or other Spanish culture or origin, regardless of race. The term, \"Spanish origin,\" can be used in addition to \"Hispanic or Latino.\" ", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "Episode of Care", "Section Code": "EOC", "Section Type": "Section", "Cardinality": "1..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 2999, "Name": "Episode Unique Key", "Section Display Name": "Episode of Care", "Coding Instructions": "Indicate the unique key associated with each patient episode record as assigned by the EMR/EHR or your software application.", "Target Value": "N/A", "Short Name": "<PERSON><PERSON><PERSON>", "Data Type": "ST", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "Automatic", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.855", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 3000, "Name": "Arrival Date", "Section Display Name": "Episode of Care", "Coding Instructions": "Indicate the date the patient arrived at your facility.", "Target Value": "N/A", "Short Name": "ArrivalDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 3040, "Name": "Reason for Admission", "Section Display Name": "Episode of Care", "Coding Instructions": "Indicate the primary reason for admission to your facility.", "Target Value": "The value on arrival at this facility", "Short Name": "ReasonForAdmit", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001132", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Reason for Admission", "Code": "100001133", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Admitted for this procedure", "Selection Definition": "The patient was admitted specifically to have the device or lead procedure,  including patients admitted for device infection with subsequent extraction.", "Display Order": 1}, {"Name": "Reason for Admission", "Code": "100001134", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Heart failure", "Selection Definition": "Heart failure is the primary reason the patient was admitted to this facility.", "Display Order": 2}, {"Name": "Reason for Admission", "Code": "100001227", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Other", "Selection Definition": "A cardiac problem (excluding heart failure) or non-cardiac problem is the primary reason the patient was admitted to this facility.", "Display Order": 3}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 15780, "Name": "Admitted For This Procedure Reason", "Section Display Name": "Episode of Care", "Coding Instructions": "If admitted for this procedure, indicate the reason (select all that apply). ", "Target Value": "The value on arrival at this facility", "Short Name": "AFTPR", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000003667", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Admitted for This Procedure Reason (15780) cannot equal Initial device implant or Generator device change when the Electrophysiology Device Implant Pathway (15826) is Leads only", "Selections": [{"Name": "Admitted For This Procedure Reason", "Code": "112000001324", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Device embolization", "Selection Definition": "Indicate if there is documentation that the patient experienced device embolization, the full dislodgement of a device from its original position that is then introduced to the circulatory system, potentially occluding blood supply to vessels and/or organs. \n", "Display Order": 1}, {"Name": "Admitted For This Procedure Reason", "Code": "112000003662", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Initial device implant", "Selection Definition": null, "Display Order": 2}, {"Name": "Admitted For This Procedure Reason", "Code": "40733004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Infection", "Selection Definition": null, "Display Order": 3}, {"Name": "Admitted For This Procedure Reason", "Code": "112000003665", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Generator device change", "Selection Definition": null, "Display Order": 4}, {"Name": "Admitted For This Procedure Reason", "Code": "234233007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Lead dislodgement", "Selection Definition": null, "Display Order": 5}, {"Name": "Admitted For This Procedure Reason", "Code": "100000351", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Other", "Selection Definition": null, "Display Order": 6}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 3040, "Parent Element Name": "Reason for Admission", "Parent Element Selection Name": "Admitted for this procedure"}]}, {"Element Reference": 3005, "Name": "Health Insurance", "Section Display Name": "Episode of Care", "Coding Instructions": "Indicate if the patient has health insurance.", "Target Value": "The value on arrival at this facility", "Short Name": "HealthIns", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "63513-6", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": "Health Insurance (3005) must not be NULL", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 3010, "Name": "Health Insurance Payment Source", "Section Display Name": "Episode of Care", "Coding Instructions": "Indicate the patient's health insurance payment type.\n\nNote(s):\nIf the patient has multiple insurance payors, select all payors.", "Target Value": "The value on arrival at this facility", "Short Name": "HIPS", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "*********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Health Insurance Payment Source", "Code": "5", "Code System": "2.16.840.1.113883.3.221.5", "Code System Name": "PHDSC", "Selection Name": "Private health insurance", "Selection Definition": "Private health insurance is coverage by a health plan provided through an employer or union or purchased by an individual from a private health insurance company. A health maintenance organization (HMO) is considered private health insurance.", "Display Order": 1}, {"Name": "Health Insurance Payment Source", "Code": "36", "Code System": "2.16.840.1.113883.3.221.5", "Code System Name": "PHDSC", "Selection Name": "State-specific plan (non-Medicaid)", "Selection Definition": "State Specific Plans - Some states have their own health insurance programs for low-income uninsured individuals. These health plans may be known by different names in different states.", "Display Order": 2}, {"Name": "Health Insurance Payment Source", "Code": "1", "Code System": "2.16.840.1.113883.3.221.5", "Code System Name": "PHDSC", "Selection Name": "Medicare (Part A or B)", "Selection Definition": "Medicare is a health insurance program for: people age 65 or older; people under age 65 with certain disabilities; and people of all ages with end-stage renal disease (permanent kidney failure requiring dialysis or a kidney transplant).\n\nMedicare Part A (Hospital Insurance) –\nPart A helps cover inpatient care in hospitals, including critical access hospitals, and skilled nursing facilities (not custodial or long-term care). It also helps cover hospice care and some home health care.\n\nMedicare Part B (Medical Insurance) –\nPart B helps cover doctors' services and outpatient care. It also covers some other medical services that Part A doesn't cover, such as some of the services of physical and occupational therapists, and some home health care. Part B helps pay for these covered services and supplies when they are medically necessary.", "Display Order": 3}, {"Name": "Health Insurance Payment Source", "Code": "112000002025", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Medicare Advantage (Part C)", "Selection Definition": "Medicare Part C (Medicare Advantage) –\nPart C is an alternative way to get Medicare coverage through private insurance companies instead of the federal government. Part C provides the same benefits as Medicare Part A and Part B, and may include additional benefits such as dental, vision, prescription drug and wellness programs coverage.", "Display Order": 4}, {"Name": "Health Insurance Payment Source", "Code": "2", "Code System": "2.16.840.1.113883.3.221.5", "Code System Name": "PHDSC", "Selection Name": "Medicaid", "Selection Definition": "Medicaid is a program administered at the state level, which provides medical assistance to the needy. Families with dependent children, the aged, blind, and disabled who are in financial need are eligible for Medicaid. It may be known by different names.", "Display Order": 5}, {"Name": "Health Insurance Payment Source", "Code": "31", "Code System": "2.16.840.1.113883.3.221.5", "Code System Name": "PHDSC", "Selection Name": "Military health care", "Selection Definition": "Military Health care - Military health care includes TRICARE/CHAMPUS (Civilian Health and Medical Program of the Uniformed Services) and CHAMPVA (Civilian Health and Medical Program of the Department of Veterans Affairs), as well as care provided by the Department of Veterans Affairs (VA).", "Display Order": 6}, {"Name": "Health Insurance Payment Source", "Code": "33", "Code System": "2.16.840.1.113883.3.221.5", "Code System Name": "PHDSC", "Selection Name": "Indian Health Service", "Selection Definition": "Indian Health Service (IHS) is a health care program through which the Department of Health and Human Services provides medical assistance to eligible American Indians at IHS facilities. In addition, the IHS helps pay the cost of selected health care services provided at non-IHS facilities.", "Display Order": 7}, {"Name": "Health Insurance Payment Source", "Code": "100000812", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Non-US insurance", "Selection Definition": "Non-US insurance refers to individuals with a payor that does not originate in the United States.", "Display Order": 8}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 3005, "Parent Element Name": "Health Insurance", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 12846, "Name": "Medicare Beneficiary Identifier", "Section Display Name": "Episode of Care", "Coding Instructions": "Indicate the patient's Medicare Beneficiary Identifier (MBI).\n\nNote(s):\nEnter the Medicare Beneficiary Identifier (MBI) for those patients insured by Medicare. Patients without Medicare will not have a MBI.", "Target Value": "The value on arrival at this facility", "Short Name": "MBI", "Data Type": "ST", "Precision": "11", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.4.927", "Code System": "2.16.840.1.113883.4.927", "Code System Name": "Centers for Medicare & Medicaid Services", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Medicare Beneficiary Identifier", "Definition": "The Medicare Access and CHIP Reauthorization Act (MACRA) of 2015, requires us to remove Social Security Numbers (SSNs) from all Medicare cards by April 2019. A new Medicare Beneficiary Identifier (MBI) will replace the SSN-based Health Insurance Claim Number (HICN) on the new Medicare cards for Medicare transactions like billing, eligibility status, and claim status.", "Source": "https://www.cms.gov/Medicare/New-Medicare-Card/index.html\n\n"}, "Parent Child Validations": [{"Parent Element Reference": 3010, "Parent Element Name": "Health Insurance Payment Source", "Parent Element Selection Name": "Medicare (Part A or B)"}, {"Parent Element Reference": 3010, "Parent Element Name": "Health Insurance Payment Source", "Parent Element Selection Name": "Medicare Advantage (Part C)"}]}, {"Element Reference": 3020, "Name": "<PERSON><PERSON> Enrolled in Research Study", "Section Display Name": "Episode of Care", "Coding Instructions": "Indicate if the patient is enrolled in an ongoing ACC - NCDR research study related to this registry.", "Target Value": "Any occurrence between arrival at this facility and discharge", "Short Name": "EnrolledStudy", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001095", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "<PERSON><PERSON> Enrolled in Research Study", "Definition": "A clinical or research study is one in which participants are assigned to receive one or more interventions (or no intervention) so that researchers can evaluate the effects of the interventions on biomedical or health-related outcomes. The assignments are determined by the study protocol. Participants may receive diagnostic, therapeutic, or other types of interventions.", "Source": "Clinicaltrials.gov Glossary of Common Site Terms retrieved from http://clinicaltrials.gov/ct2/about-studies/glossary#interventional-study"}, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Episode of Care", "Section Display Name": "Research Study", "Section Code": "RSTUDY", "Section Type": "Repeater Section", "Cardinality": "0..n", "Table": "RSTUDY", "Elements": [{"Element Reference": 3025, "Name": "Research Study Name", "Section Display Name": "Research Study", "Coding Instructions": "Indicate the research study name as provided by the research study protocol.\n\nNote(s):\nIf the patient is in more than one research study, list each separately.", "Target Value": "N/A", "Short Name": "StudyName", "Data Type": "ST", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001096", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 3020, "Parent Element Name": "<PERSON><PERSON> Enrolled in Research Study", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 3030, "Name": "Research Study Patient ID", "Section Display Name": "Research Study", "Coding Instructions": "Indicate the research study patient identification number as assigned by the research protocol.\n\nNote(s):\nIf the patient is in more than one research study, list each separately.", "Target Value": "N/A", "Short Name": "StudyPtID", "Data Type": "ST", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.852", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 3020, "Parent Element Name": "<PERSON><PERSON> Enrolled in Research Study", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Episode of Care", "Section Display Name": "Pathway", "Section Code": "PATHWAY", "Section Type": "Section", "Cardinality": "1..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 15826, "Name": "Electrophysiology Device Implant Pathway", "Section Display Name": "Pathway", "Coding Instructions": "Indicate all the Electrophysiology Device Implant Registry procedures performed during the episode of care. \n\nNote: Only select ‘Leads only’ when no generator change, generator implant or generator explant is performed. ", "Target Value": "Any occurrence between arrival and discharge", "Short Name": "ElecDevImpPath", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "252425004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Electrophysiology Device Implant Pathway", "Code": "72506001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Implantable cardioverter-defibrillator", "Selection Definition": null, "Display Order": 1}, {"Name": "Electrophysiology Device Implant Pathway", "Code": "449397007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Permanent pacemaker", "Selection Definition": null, "Display Order": 2}, {"Name": "Electrophysiology Device Implant Pathway", "Code": "100001025", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Leads only", "Selection Definition": null, "Display Order": 3}], "Ranges": null, "Definition": null, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "History and Risk Factors", "Section Code": "HISTORYANDRISK", "Section Type": "Section", "Cardinality": "0..1", "Table": "EPISODEOFCARE", "Elements": null}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "History and Risk Factors", "Section Display Name": "Condition History", "Section Code": "CONDHX", "Section Type": "Repeater Section", "Cardinality": "0..n", "Table": "CONDHX", "Elements": [{"Element Reference": 12903, "Name": "Condition History Name", "Section Display Name": "Condition History", "Coding Instructions": "Select from the following list of medical conditions based on prior clinical diagnosis/documentation. Additional definitions appear below for those selections that may need further clarification.", "Target Value": "N/A", "Short Name": "ConditionHx", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "312850006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "Condition History Name (12903) should not be duplicated in an episode", "Selections": [{"Name": "Condition History Name", "Code": "49436004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Atrial fibrillation", "Selection Definition": "Indicate if there is documentation/diagnosis of atrial fibrillation, a supraventricular tachyarrhythmia with uncoordinated atrial activation and consequent ineffective atrial contraction. Include all classifications of AFib.\n", "Display Order": 1}, {"Name": "Condition History Name", "Code": "410429000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Cardiac arrest", "Selection Definition": "Indicate if the patient experienced cardiac arrest. Cardiac arrest is the cessation of cardiac activity. The patient becomes unresponsive with no normal breathing and no signs of circulation. If corrective measures are not taken rapidly, this condition progresses to sudden death. Cardiac arrest should be used to signify an event as described above that is reversed, usually by CPR and/or defibrillation or cardioversion or cardiac pacing.", "Display Order": 2}, {"Name": "Condition History Name", "Code": "426856002", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Cardiomyopathy - ischemic", "Selection Definition": "Indicate if there is documentation/diagnosis of ischemic cardiomyopathy, weakening of the heart muscle associated with coronary artery disease that may lead to reduced systolic function and/or heart failure.\n", "Display Order": 3}, {"Name": "Condition History Name", "Code": "111000119104", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Cardiomyopathy - non-ischemic", "Selection Definition": "Indicate if there is documentation/diagnosis of non-ischemic cardiomyopathy, the weakening of the heart muscle due to any cause besides coronary artery disease, in which cardiac tissue is still oxygenated. Non-ischemic cardiomyopathy may lead to reduced systolic function and/or heart failure.\n", "Display Order": 4}, {"Name": "Condition History Name", "Code": "62914000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Cerebrovascular disease", "Selection Definition": "Indicate if there is documentation/diagnosis of cerebrovascular disease, including any one of the following: \n1) Cerebrovascular Accident (CVA): An acute episode of focal or global neurological dysfunction caused by brain, spinal cord, or retinal vascular injury as a result of hemorrhage or infarction. The duration of > 24 h has been used as an operational definition of persisting symptoms of stroke rather than TIA, based mostly on consensual practice rather than objective evidence. \n2) Transient Ischemic Attack (TIA): Transient episode of neurological dysfunction caused by focal or global brain, spinal cord, or retinal ischemia without acute infarction Note: The distinction between a TIA and ischemic stroke is the presence of infarction. The unifying concept driving the definition is that stroke is a marker of potentially disabling vascular brain injury. The duration of > 24 h has been used as an operational definition of persisting symptoms of stroke rather than TIA, based mostly on consensual practice rather than objective evidence. \n3) Non-invasive/invasive carotid test with > 79% occlusion. Noninvasive or invasive arterial imaging test: Noninvasive or invasive arterial imaging test demonstrating > 50% stenosis of any of the major extracranial or intracranial vessels to the brain \n4) Previous carotid artery surgery/intervention for carotid artery stenosis. History of cervical or cerebral artery revascularization surgery or percutaneous intervention This does not include chronic (nonvascular) neurological disease or other acute neurological insults such as metabolic and anoxic ischemic encephalopathy.", "Display Order": 5}, {"Name": "Condition History Name", "Code": "413839001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Chronic lung disease", "Selection Definition": "Indicate if there is documentation/diagnosis of chronic lung disease. \n\nChronic lung disease can include patients with chronic obstructive pulmonary disease, chronic bronchitis, or emphysema. It can also include a patient who is currently being chronically treated with inhaled or oral pharmacological therapy (e.g., beta-adrenergic agonist, anti-inflammatory agent, leukotriene receptor antagonist, or steroid). A history of chronic inhalation reactive disease (asbestosis, mesothelioma, black lung disease or pneumoconiosis) may qualify as chronic lung disease. Radiation induced pneumonitis or radiation fibrosis also qualifies as chronic lung disease.  \n\nPatients with asthma or seasonal allergies are not considered to have chronic lung disease. A history of atelectasis is a transient condition and does not qualify.", "Display Order": 6}, {"Name": "Condition History Name", "Code": "53741008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Coronary artery disease", "Selection Definition": "Indicate if the patient has a diagnosis of coronary artery disease (CAD) or documented history of: \n- Coronary artery stenosis >=50% (by cardiac catheterization or other modality or of direct imaging of the coronary arteries) \n- Previous CABG surgery \n- Previous PCI \n- Previous MI", "Display Order": 7}, {"Name": "Condition History Name", "Code": "108241001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Currently on dialysis", "Selection Definition": "Indicate if the patient is currently undergoing either hemodialysis or peritoneal dialysis on an ongoing basis as a result of renal failure.\n", "Display Order": 8}, {"Name": "Condition History Name", "Code": "73211009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Diabetes mellitus", "Selection Definition": "Indicate if there is documentation/diagnosis of Type 1 or Type 2 diabetes, a group of diseases that affect how the body uses glucose. This does not include pre-diabetes or gestational diabetes.\n", "Display Order": 9}, {"Name": "Condition History Name", "Code": "281666001:246090004=399020009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Familial history of non-ischemic cardiomyopathy", "Selection Definition": "Indicate if the patient has a documented family history of non-ischemic cardiomyopathy. \n", "Display Order": 10}, {"Name": "Condition History Name", "Code": "100001006", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Familial syndrome-risk of sudden death", "Selection Definition": "Indicate if the patient has a documented family history of sudden death resulting from any heart condition.\n", "Display Order": 11}, {"Name": "Condition History Name", "Code": "84114007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Heart failure", "Selection Definition": "Indicate if there is documentation/diagnosis of heart failure.\n", "Display Order": 12}, {"Name": "Condition History Name", "Code": "100001061", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Inotropic support", "Selection Definition": "Indicate if the patient is currently prescribed a positive IV inotropic agent(s) to attempt to achieve beneficial hemodynamic effects in the patient with systolic heart failure (HF). Positive IV inotropic medications include and not limited to Inamrinone, Milrinone, Norepinephrine, Dopamine and Dobutamine.  Digoxin is not captured.\n", "Display Order": 13}, {"Name": "Condition History Name", "Code": "22298006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Myocardial infarction", "Selection Definition": "Indicate if there is documentation/diagnosis of a prior myocardial infarction.\n", "Display Order": 14}, {"Name": "Condition History Name", "Code": "67198005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Paroxysmal SVT history", "Selection Definition": "Indicate if there is documentation of paroxysmal supraventricular tachycardia (SVT) including atrial flutter, atrioventricular nodal reentrant tachycardia (AVNRT), atrioventricular reciprocating tachycardia (AVRT) i.e. Wolff-<PERSON> White syndrome, atrial tachycardia, junctional tachycardia, and / or multifocal atrial tachycardia.  Paroxysmal AFib is not captured here, it is captured in Atrial Fibrillation by selecting “Paroxysmal” in Sequence 4400 (AFib Classification).\n", "Display Order": 15}, {"Name": "Condition History Name", "Code": "368009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Valvular heart disease", "Selection Definition": "Indicate if there is documentation/diagnosis of primary valvular disease. Primary valvular disease may also be documented/classified as:\n\nModerately severe or severe, or 3+ or 4+ aortic insufficiency. \n\nModerately severe or severe, or 3+ or 4+ mitral insufficiency with echocardiographic evidence that mitral insufficiency is a primary abnormality and not secondary to ventricular dilation. \n\nModerately severe or severe aortic stenosis defined by estimated aortic valve area by catheterization or Doppler echocardiography of <=1.0 cm2. \n\nModerately severe or severe mitral stenosis defined by estimated valve area catheterization doppler echocardiography of <1.0 \n\nPulmonic tricuspid disease that is known to be a primary abnormality.\n\nFor a diagnosis of Marfan syndrome aortic insufficiency that is moderate to severe, “Yes” is coded. \n\nWhen there is no supporting documentation of the etiology of the valve disease, “No” is coded.", "Display Order": 16}, {"Name": "Condition History Name", "Code": "100000949", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Structural abnormalities", "Selection Definition": "Indicate if there is documentation/diagnosis of structural defects in the heart or major blood vessels. Examples include, but are not limited to, arrhythmogenic ventricular cardiomyopathy (AVC), and congenital heart disease associated with sudden cardiac arrest.\n", "Display Order": 17}, {"Name": "Condition History Name", "Code": "271594007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Syncope", "Selection Definition": "Indicate if there is documentation of syncope, an abrupt, transient, and complete loss of consciousness associated with inability to maintain postural tone, with rapid and spontaneous recovery. An ICD/ATP shock preventing cardiac arrest is included.  \n", "Display Order": 18}, {"Name": "Condition History Name", "Code": "100001202", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Syndromes of sudden death", "Selection Definition": "Indicate if there is documentation/diagnosis that the patient has a syndrome that puts him/her at risk for sudden death. To code yes, the patient must be diagnosed with one of the syndromes listed in Sequence 4170 (Syndrome Type).\n", "Display Order": 19}, {"Name": "Condition History Name", "Code": "71908006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Ventricular fibrillation (not due to reversible cause)", "Selection Definition": "Indicate if there is documentation of a spontaneous ventricular fibrillation (VFib) not due to reversible cause and that was not induced. \n", "Display Order": 20}, {"Name": "Condition History Name", "Code": "25569003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Ventricular tachycardia", "Selection Definition": "Indicate if there is documentation of a spontaneous ventricular tachycardia (VT) with 3 or more consecutive complexes that was not induced.\n", "Display Order": 21}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Permanent pacemaker"}]}, {"Element Reference": 14264, "Name": "Condition History Occurrence", "Section Display Name": "Condition History", "Coding Instructions": "Indicate whether or not the patient been given a clinical diagnosis of the listed medical conditions.\n", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "ConditionHxOccurence", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "312850006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Any Value"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "History and Risk Factors", "Section Display Name": "Condition History Details", "Section Code": "CONDHXDET", "Section Type": "Section", "Cardinality": "0..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 4400, "Name": "Atrial Fibrillation Classification", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate the type of atrial fibrillation experienced by the patient.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "AFibClass", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000935", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Atrial Fibrillation Classification", "Code": "26593000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Paroxysmal", "Selection Definition": "AF that terminates spontaneously or with intervention within 7 days of onset. Episodes may recur with variable frequency.", "Display Order": 1}, {"Name": "Atrial Fibrillation Classification", "Code": "62459000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Persistent", "Selection Definition": "Continuous AF that is sustained >7 days or with electrical or pharmacological termination.", "Display Order": 2}, {"Name": "Atrial Fibrillation Classification", "Code": "100001029", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Long-standing Persistent", "Selection Definition": "Continuous AF of >12 months duration.", "Display Order": 3}, {"Name": "Atrial Fibrillation Classification", "Code": "6934004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Permanent", "Selection Definition": "The term “permanent AF” is used when the patient and clinician make a joint decision to stop further attempts to restore and/or maintain sinus rhythm.\n\n- Acceptance of AF represents a therapeutic attitude on the part of the patient and clinician rather than an inherent pathophysiological attribute of the AF.\n\n- Acceptance of AF may change as symptoms, the efficacy of therapeutic interventions, and patient and clinician preferences evolve.", "Display Order": 4}], "Ranges": null, "Definition": {"Title": "Atrial Fibrillation Classification", "Definition": "Atrial Fibrillation is a supraventricular tachyarrhythmia with uncoordinated atrial activation and consequently ineffective atrial contraction. \n\nElectrocardiogram (ECG) characteristics include: \n1) irregular R-R intervals (when atrioventricular [AV] conduction is present),\n2) absence of distinct repeating P waves, and\n3) irregular atrial activity.\n\nAtrial Fibrillation can be further characterized as:\n\n- Paroxysmal AF is defined as AF that terminates spontaneously or with intervention within seven days of onset. Episodes may recur with variable frequency.\n- Persistent AF is defined as AF that fails to self-terminate within seven days. Episodes often require pharmacologic or electrical cardioversion to restore sinus rhythm.\n- Long-standing persistent AF is defined as AF that has lasted for more than 12 month\n-Permanent AF is defined as when the patient and clinician make a joint decision to stop further attempts to restore and/or maintain sinus rhythm. Acceptance of AF represents a therapeutic attitude on the part of the patient and clinician rather than an inherent pathophysiological attribute of AF. Acceptance of AF may change as symptoms, efficacy of therapeutic interventions, and patient and clinician preferences evolve.", "Source": "January CT, <PERSON><PERSON>, <PERSON><PERSON>, et al. 2014 AHA/ACC/HRS Guideline for the Management of Patients With Atrial Fibrillation: A Report of the American College of Cardiology/American Heart Association Task Force on Practice Guidelines and the Heart Rhythm Society. J Am Coll Cardiol 2014. DOI: 10.1016/j.jacc.2014.03.022"}, "Parent Child Validations": [{"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Atrial fibrillation"}, {"Parent Element Reference": 14264, "Parent Element Name": "Condition History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4405, "Name": "Plans for Cardioversion of Atrial Fibrillation", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate if there is a planned cardioversion for atrial fibrillation.\n\nNote(s):\n1. Code No for a history of cardioversion.\n2. Code Yes, if the patient was in AFib and cardioverted prior to the start of the first generator implant procedure in this admission.\n3. Code Yes if the patient is scheduled for a cardioversion.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "AFibFlutterCardioPlans", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000934", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Plans for Cardioversion of Atrial Fibrillation", "Definition": "A cardioversion is performed using a synchronized shock and/or IV antiarrhythmic medications.", "Source": null}, "Parent Child Validations": [{"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}, {"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Atrial fibrillation"}, {"Parent Element Reference": 14264, "Parent Element Name": "Condition History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4225, "Name": "Most Recent Cardiac Arrest Date", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate the date of the most recent cardiac arrest.\n\nNote(s):\nIf the month or day of the cardiac arrest is unknown, please code 01/01/YYYY. If the specific year is unknown in the current record, the year may be estimated based on timeframes found in prior medical record documentation (Example: If the patient had \"most recent cardiac arrest\" documented in a record from 2011, then the year 2011 can be utilized and coded as 01/01/2011).", "Target Value": "The last value between birth and the first procedure in this admission", "Short Name": "CardiacArrestDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "410429000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "Most Recent Cardiac Arrest Date (4225) must be Less than or Equal to Procedure Start Date and Time (7000)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Cardiac arrest"}, {"Parent Element Reference": 14264, "Parent Element Name": "Condition History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4240, "Name": "<PERSON><PERSON><PERSON>", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate if the cardiac arrest was a result of bradycardia.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "BradyArrest", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "410429000:42752001=48867003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Cardiac arrest"}, {"Parent Element Reference": 14264, "Parent Element Name": "Condition History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4235, "Name": "<PERSON><PERSON><PERSON>", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate if the cardiac arrest was a result of ventricular fibrillation as defined below.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "VFibArrest", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "410429000:42752001=71908006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "<PERSON><PERSON><PERSON>", "Definition": "Rapid, usually more than 300 bpm (cycle length: 180 ms or less), grossly irregular ventricular rhythm with marked variability in QRS cycle length, morphology, and amplitude.", "Source": "JACC Vol. 48, No. 11, 2006 ACC/AHA/HRS Clinical Data Standards December 5, 2006:2360-96"}, "Parent Child Validations": [{"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}, {"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Cardiac arrest"}, {"Parent Element Reference": 14264, "Parent Element Name": "Condition History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4230, "Name": "<PERSON><PERSON><PERSON>", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate if the cardiac arrest was a result of ventricular tachycardia as defined below.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "VTachArrest", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "410429000:42752001=25569003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Ventricular Tachycardia", "Definition": "Ventricular Tachycardia (VT) is a cardiac arrhythmia of 3 or more\nconsecutive complexes in duration emanating from the ventricles\nat a rate 100 bpm (cycle length: 600 ms).", "Source": " JACC Vol. 48, No. 11, 2006 ACC/AHA/HRS Clinical Data\nStandards December 5, 2006:2360-96"}, "Parent Child Validations": [{"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}, {"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Cardiac arrest"}, {"Parent Element Reference": 14264, "Parent Element Name": "Condition History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4190, "Name": "Ischemic Cardiomyopathy Timeframe", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate the timeframe since the initial diagnosis of ischemic cardiomyopathy.", "Target Value": "The first value between birth and the first procedure in this admission", "Short Name": "ISCMTimeframe", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001022", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Ischemic Cardiomyopathy Timeframe", "Code": "100001028", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Less than 3 months", "Selection Definition": null, "Display Order": 1}, {"Name": "Ischemic Cardiomyopathy Timeframe", "Code": "100000924", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Greater than or equal to 3 months", "Selection Definition": null, "Display Order": 2}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}, {"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Cardiomyopathy - ischemic"}, {"Parent Element Reference": 14264, "Parent Element Name": "Condition History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4195, "Name": "Ischemic Cardiomyopathy Guideline Directed Medical Therapy Maximum Dose", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate if patient has been on guideline directed medical therapy at least 3 months.\n\nNote(s): \nDocumentation of GDMT is the responsibility of the clinician and cannot be determined by the abstractor based on a list of medications. Documentation of GDMT maximum, optimum, appropriate dose, medical management/medical therapy for cardiomyopathy or a discussion in the medical record regarding medications as it relates to the patient's cardiomyopathy is acceptable for documenting GDMT. Some other acceptable statements are good neurohormonal therapy, managed appropriately on HF medications, and failed medically management of heart failure.", "Target Value": "The first value between birth and the first procedure in this admission", "Short Name": "ISCMGDMTDose", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001021", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Ischemic Cardiomyopathy Guideline Directed Medical Therapy Maximum Dose", "Code": "100001037", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Yes (for 3 months)", "Selection Definition": "The patient has been prescribed guideline directed medical therapy for at least 3 months.\n\nThis may be coded if there is documentation of GDMT without a time frame, only if the abstractor can determine from the medical record that the patient has been on these exact medications for at least 3 months.", "Display Order": 1}, {"Name": "Ischemic Cardiomyopathy Guideline Directed Medical Therapy Maximum Dose", "Code": "100001036", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not documented", "Selection Definition": "There is no documentation of guideline directed medical therapy being prescribed.", "Display Order": 2}, {"Name": "Ischemic Cardiomyopathy Guideline Directed Medical Therapy Maximum Dose", "Code": "100001035", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not attempted", "Selection Definition": "Guideline directed medical therapy was not attempted on the patient.", "Display Order": 3}, {"Name": "Ischemic Cardiomyopathy Guideline Directed Medical Therapy Maximum Dose", "Code": "100001038", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Inability to complete", "Selection Definition": "The patient was unable to continue the guideline directed medical therapy for 3 months or the patient is on guideline directed medical therapy but it has been less than 3 months. Without a definitive time documented or the ability to determine the timeframe from the medical record, it would be captured as Inability to Complete. The duration of treatment would default to less than 3 months since the timeframe was not able to be determined. Inability to Complete would also include patients started on GDMT where it has been less than 3 months since therapy was started, patient refusal, an allergy or absolute contraindication.", "Display Order": 4}], "Ranges": null, "Definition": {"Title": "Ischemic Guideline Directed Medical Therapy Maximum Dose", "Definition": "For heart failure in the setting of LV systolic dysfunction, this may require individualization but typically should include the combination of an angiotensin-converting enzyme inhibitor or angiotensin receptor blocker and beta blocker therapy adjusted to target doses as tolerated, with diuretics adjusted if/as needed to control fluid retention. In selected patients, the addition of aldosterone antagonists is appropriate. In addition, in some cases the use of a combination of hydralazine and nitrates may be used instead of an ACE inhibitor / angiotensin receptor blocker. Patients who are going to receive substantial benefit from medical treatment alone usually show some clinical improvement during the first 3 to 6 months. Medical therapy is also assumed to include adequate rate control for tachyarrhythmias, including atrial fibrillation. Therefore, it is recommended that GDMT be provided for at least 3 months before planned reassessment of LV function to consider device implantation. If LV function improves to the point where primary prevention indications no longer apply, then device implantation is not indicated. For stable ischemic heart disease, GDMT should include aspirin (or a thienopyridine if aspirin is not tolerated), statin therapy, angiotensin-converting enzyme inhibition (or an angiotensin receptor blocker) and the use of beta-blockers after myocardial infarction. Therapy for angina/ischemia should include at least 1 of the following medications: beta-blockers, calcium channel antagonists, or nitrates. Therapy should also be directed at optimizing the treatment of associated conditions such as diabetes and uncontrolled hypertension.", "Source": "1) <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. 2013 ACCF/AHA guideline for the management of ST-elevation myocardial infarction: a report of the American College of Cardiology Foundation/American Heart Association Task Force on Practice Guidelines. J Am Coll Cardiol 2013;61\n2) <PERSON>, <PERSON><PERSON>back RF, <PERSON>, et al. ACCF/HRS/AHA/ASE/HFSA/SCAI/SCCT/SCMR 2013 appropriate use criteria for implantable cardioverter-defibrillators and cardiac resynchronization therapy. J Am Coll Cardiol 2013;61:1318-68. doi: 10.1016/j.jacc.2012.12.017"}, "Parent Child Validations": [{"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}, {"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Cardiomyopathy - ischemic"}, {"Parent Element Reference": 14264, "Parent Element Name": "Condition History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4205, "Name": "Non-Ischemic Cardiomyopathy Timeframe", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate the timeframe since the initial diagnosis of non-ischemic cardiomyopathy.", "Target Value": "The first value between birth and the first procedure in this admission", "Short Name": "NICMTimeframe", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001054", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Non-Ischemic Cardiomyopathy Timeframe", "Code": "100001028", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Less than 3 months", "Selection Definition": null, "Display Order": 1}, {"Name": "Non-Ischemic Cardiomyopathy Timeframe", "Code": "100000924", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Greater than or equal to 3 months", "Selection Definition": null, "Display Order": 2}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}, {"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Cardiomyopathy - non-ischemic"}, {"Parent Element Reference": 14264, "Parent Element Name": "Condition History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4210, "Name": "Non-Ischemic Guideline Directed Medical Therapy Maximum Dose", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate if patient has been on guideline directed medical therapy for at least 3 months.\n\nNote(s):\nDocumentation of GDMT is the responsibility of the clinician and cannot be determined by the abstractor based on a list of medications. Documentation of GDMT maximum, optimum, appropriate dose, medical management/medical therapy for cardiomyopathy or a discussion in the medical record regarding medications as it relates to the patient's cardiomyopathy is acceptable for documenting GDMT. Some other acceptable statements are good neurohormonal therapy, managed appropriately on HF medications, and failed medically management of heart failure.", "Target Value": "The first value between birth and the first procedure in this admission", "Short Name": "NICMGDMTDose", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001055", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Non-Ischemic Guideline Directed Medical Therapy Maximum Dose", "Code": "100001037", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Yes (for 3 months)", "Selection Definition": "The patient has been prescribed guideline directed medical therapy for at least 3 months.\n\nThis may be coded if there is documentation of GDMT without a time frame, only if the abstractor can determine from the medical record that the patient has been on these exact medications for at least 3 months.", "Display Order": 1}, {"Name": "Non-Ischemic Guideline Directed Medical Therapy Maximum Dose", "Code": "100001036", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not documented", "Selection Definition": "There is no documentation of guideline directed medical therapy being prescribed.", "Display Order": 2}, {"Name": "Non-Ischemic Guideline Directed Medical Therapy Maximum Dose", "Code": "100001035", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not attempted", "Selection Definition": "Guideline directed medical therapy was not attempted on the patient.", "Display Order": 3}, {"Name": "Non-Ischemic Guideline Directed Medical Therapy Maximum Dose", "Code": "100001038", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Inability to complete", "Selection Definition": "The patient was unable to continue the guideline directed medical therapy for 3 months or the patient is on guideline directed medical therapy but it has been less than 3 months. Without a definitive time documented or the ability to determine the timeframe from the medical record, it would be captured as Inability to Complete. The duration of treatment would default to less than 3 months since the timeframe was not able to be determined. Inability to Complete would also include patients started on GDMT where it has been less than 3 months since therapy was started, patient refusal, an allergy or absolute contraindication.", "Display Order": 4}], "Ranges": null, "Definition": {"Title": "Non-Ischemic Guideline Directed Medical Therapy Maximum Dose", "Definition": "For heart failure in the setting of LV systolic dysfunction, this may require individualization but typically should include the combination of an angiotensin-converting enzyme inhibitor or angiotensin receptor blocker and beta blocker therapy adjusted to target doses as tolerated, with diuretics adjusted if/as needed to control fluid retention. In selected patients, the addition of aldosterone antagonists is appropriate. In addition, in some cases the use of a combination of hydralazine and nitrates may be used instead of an ACE inhibitor / angiotensin receptor blocker. Patients who are going to receive substantial benefit from medical treatment alone usually show some clinical improvement during the first 3 to 6 months. Medical therapy is also assumed to include adequate rate control for tachyarrhythmias, including atrial fibrillation. Therefore, it is recommended that GDMT be provided for at least 3 months before planned reassessment of LV function to consider device implantation. If LV function improves to the point where primary prevention indications no longer apply, then device implantation is not indicated. For stable ischemic heart disease, GDMT should include aspirin (or a thienopyridine if aspirin is not tolerated), statin therapy, angiotensin-converting enzyme inhibition (or an angiotensin receptor blocker) and the use of beta-blockers after myocardial infarction. Therapy for angina/ischemia should include at least 1 of the following medications: beta-blockers, calcium channel antagonists, or nitrates. Therapy should also be directed at optimizing the treatment of associated conditions such as diabetes and uncontrolled hypertension.", "Source": "1) <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. 2013 ACCF/AHA guideline for the management of ST-elevation myocardial infarction: a report of the American College of Cardiology Foundation/American Heart Association Task Force on Practice Guidelines. J Am Coll Cardiol 2013;61\n2) <PERSON>, <PERSON><PERSON>back RF, <PERSON>, et al. ACCF/HRS/AHA/ASE/HFSA/SCAI/SCCT/SCMR 2013 appropriate use criteria for implantable cardioverter-defibrillators and cardiac resynchronization therapy. J Am Coll Cardiol 2013;61:1318-68. doi: 10.1016/j.jacc.2012.12.017"}, "Parent Child Validations": [{"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}, {"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Cardiomyopathy - non-ischemic"}, {"Parent Element Reference": 14264, "Parent Element Name": "Condition History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4010, "Name": "NYHA Functional Classification", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate the patient's New York Heart Association (NYHA) Functional Classification based upon the physician documented classification at the time of the current procedure. \n\nNote(s):\nThe NYHA Functional Classification must be specifically documented in the medical record and not coded by the abstractor based upon patient symptoms.", "Target Value": "The highest value on the first procedure in this admission", "Short Name": "NYHA", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "420816009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "NYHA Functional Classification", "Code": "420300004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Class I", "Selection Definition": "Patients with cardiac disease but without resulting limitations of physical activity. Ordinary physical activity does not cause undue fatigue, palpitation, or dyspnea.", "Display Order": 1}, {"Name": "NYHA Functional Classification", "Code": "421704003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Class II", "Selection Definition": "Patients with cardiac disease resulting in slight limitation of physical activity. They are comfortable at rest. Ordinary physical activity results in fatigue, palpitation, or dyspnea.", "Display Order": 2}, {"Name": "NYHA Functional Classification", "Code": "420913000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Class III", "Selection Definition": "Patients with cardiac disease resulting in marked limitation of physical activity. They are comfortable at rest. Less than ordinary activity causes fatigue, palpitation, or dyspnea.", "Display Order": 3}, {"Name": "NYHA Functional Classification", "Code": "422293003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Class IV", "Selection Definition": "Patients with cardiac disease resulting in inability to carry on any physical activity without discomfort. Symptoms are present even at rest or minimal exertion. If any physical activity is undertaken, discomfort is increased.", "Display Order": 4}], "Ranges": null, "Definition": {"Title": "NYHA", "Definition": "The NYHA classes focus on exercise capacity and the symptomatic status of the disease. ", "Source": "2013 ACCF/AHA Guideline for the Management of Heart Failure;  J Am Coll Cardiol. 2013;62(16):e147-e239. doi:10.1016/j.jacc.2013.05.019"}, "Parent Child Validations": [{"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Heart failure"}, {"Parent Element Reference": 14264, "Parent Element Name": "Condition History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4295, "Name": "Most Recent MI Date", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate the date of the most recent myocardial infarction.\n\nNote(s):\nWhen the patient has a history of an 'old or 'remote' MI documented in the medical record by the clinician and a time frame is unable to be determined from prior medical records or by consulting with the clinician, please code the MI as having occurred 5 years ago. For example: If the physician documents on 05/01/2020, that the patient has a history of MI, please code Most Recent MI Date, Seq. 4250, as 05/01/2015.\n\nIf the month or day of the myocardial infarction is unknown, please code 01/01/YYYY. If the specific year is unknown in the current record, the year may be estimated based on timeframes found in prior medical record documentation (Example: If the patient had \"most recent myocardial infarction\" documented in a record from 2011, then the year 2011 can be utilized and coded as 01/01/2011).", "Target Value": "The last value between birth and the first procedure in this admission", "Short Name": "PriorMIDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "22298006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "Most Recent MI Date (4295) must be Less than or Equal to Procedure Start Date and Time (7000)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Myocardial infarction"}, {"Parent Element Reference": 14264, "Parent Element Name": "Condition History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4545, "Name": "Structural Abnormality Type", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate the structural abnormality type(s).\n\nNote(s): \nWhen cardiomyopathy or ventricular arrhythmias are a result of Takot<PERSON><PERSON>,  code 'LV structural Abnormality' associated with risk of SCA.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "StructAbnType", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000949", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Structural Abnormality Type", "Code": "281170005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Arrhythmogenic right ventricular cardiomyopathy (ARVC)", "Selection Definition": "Coding Note: ARVC and Arrhythmogenic Left Ventricular Cardiomyopathy (ALVC) are a type of Arrhythmogenic Cardiomyopathy (ACM) and both are captured as ARVC (Arrhythmogenic Right Ventricular Cardiomyopathy).", "Display Order": 1}, {"Name": "Structural Abnormality Type", "Code": "13213009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Congenital heart disease associated with sudden cardiac arrest", "Selection Definition": "Congenital heart disease including but not limited to Tetralogy of Fallot, Ventricular Septal Defect (VSD), <PERSON><PERSON><PERSON> abnormality, Transposition of <PERSON> Vessels, Patent Foramen Ovale (PFO), Atrial-Septal Defect (ASD), <PERSON><PERSON> syndrome and <PERSON> <PERSON> <PERSON> syndrome, and Common Ventricle that put the patient at risk for sudden cardiac arrest.\n", "Display Order": 2}, {"Name": "Structural Abnormality Type", "Code": "233873004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Hypertrophic cardiomyopathy (HCM) with high-risk features", "Selection Definition": "Hypertrophic Cardiomyopathy with High Risk Features:\nHigh risk features include:\n- Cardiac arrest (VF)\n- Spontaneous sustained VT\n- Family history of premature sudden death\n- Unexplained syncope\n- LV thickness greater than or equal to 30 mm\n- Abnormal exercise BP\n- Nonsustained spontaneous VT\n- AF\n- Myocardial ischemia\n- LV outflow obstruction\n- High-risk mutation\n- Intense (competitive) physical exertion", "Display Order": 3}, {"Name": "Structural Abnormality Type", "Code": "100001018", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Infiltrative", "Selection Definition": "Infiltrative structural abnormalities including but not limited to amyloidosis, cardiac sarcoidosis, giant cell myocarditis, Propionic Acidemia, and Chagas disease.\n\nWhen Danon disease and Fabry Disease causes cardiomyopathy, then infiltrative is coded.\n", "Display Order": 4}, {"Name": "Structural Abnormality Type", "Code": "87878005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "LV structural abnormality associated with risk for sudden cardiac arrest", "Selection Definition": "Left ventricular structural abnormalities including but not limited to left ventricular aneurysm, Traumatic VSD, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Muscular Dystrophy, Duchenne Muscular Dystrophy, <PERSON>'s Muscular Dystrophy, Myotonic Dystrophy, and LV non-compaction syndrome that put the patient at risk for sudden cardiac arrest.", "Display Order": 5}], "Ranges": null, "Definition": {"Title": "Structural Abnormality Type - Value Set", "Definition": "Left Ventricular Structural Abnormality Associated with Risk for Sudden Cardiac Arrest - Refer to the source for the supporting definition.\n", "Source": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, et al. ACC/AHA/ESC 2006 guidelines for management of patients with ventricular arrhythmias and the prevention of sudden cardiac death: a report of the American College of Cardiology/American Heart Association Task Force and the European Society of Cardiology Committee for Practice Guidelines (Writing Committee to Develop Guidelines for Management of Patients With Ventricular Arrhythmias and the Prevention of Sudden Cardiac Death). Circulation. 2006;114:e385-e484."}, "Parent Child Validations": [{"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Structural abnormalities"}, {"Parent Element Reference": 14264, "Parent Element Name": "Condition History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 15785, "Name": "Infiltrative Structural Abnormality Type", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate the infiltrative structural abnormality type(s).", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "InfiltraStruct", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": null, "Is Dynamic List": "Yes", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "312850006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Infiltrative Structural Abnormality Type", "Code": "715655000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Amyloidosis - ATTR", "Selection Definition": null, "Display Order": 1}, {"Name": "Infiltrative Structural Abnormality Type", "Code": "23132008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Amyloidosis - AL", "Selection Definition": null, "Display Order": 2}, {"Name": "Infiltrative Structural Abnormality Type", "Code": "274945004", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Amyloidosis - Other", "Selection Definition": null, "Display Order": 3}, {"Name": "Infiltrative Structural Abnormality Type", "Code": "31541009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Cardiac Sarcoidosis", "Selection Definition": null, "Display Order": 4}, {"Name": "Infiltrative Structural Abnormality Type", "Code": "998008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Chagas Disease", "Selection Definition": null, "Display Order": 5}, {"Name": "Infiltrative Structural Abnormality Type", "Code": "60812006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Giant Cell Myocarditis", "Selection Definition": null, "Display Order": 6}, {"Name": "Infiltrative Structural Abnormality Type", "Code": "100001018", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Other Infiltrative Structural Abnormality", "Selection Definition": null, "Display Order": 7}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4545, "Parent Element Name": "Structural Abnormality Type", "Parent Element Selection Name": "Infiltrative"}]}, {"Element Reference": 4170, "Name": "Syndromes with Risk of Sudden Death Type", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate the type of syndrome that puts the patient at risk for sudden death.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "SyndromeRiskType", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001105", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Syndromes with Risk of Sudden Death Type", "Code": "418818005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Brugada", "Selection Definition": "Polymorphic ventricular tachycardia in the absence of structural heart disease, associated with a baseline ECG pattern during sinus rhythm showing right bundle branch block with ST segment elevation in leads V1 through V3.  It can also be characterized by documentation of ECG patterns associated with Brugada Syndrome, some of which may be unmasked when provoked with drugs.\n\nThe most common genetic mutations identified for <PERSON><PERSON><PERSON> syndrome are in a sodium channel gene (SCN5A). Sodium channel blocking drugs, therefore, may exacerbate the electrocardiographic features and clinical presentation. <PERSON><PERSON><PERSON> syndrome typically presents before the age of 50 years.", "Display Order": 1}, {"Name": "Syndromes with Risk of Sudden Death Type", "Code": "100000956", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Catecholaminergic polymorphic VT", "Selection Definition": "CPVT is a highly malignant inheritable cardiac channelopathy in individuals without structural heart disease and QT prolongation. It is often thought of as a disease of childhood with patients presenting before the age of 21 with symptoms such as syncope or sudden cardiac arrest; however, the adult form presents between the ages of 32-48. CVPT is triggered by physical or emotional stress in patients ECG is normal. ", "Display Order": 2}, {"Name": "Syndromes with Risk of Sudden Death Type", "Code": "100001014", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": " Idiopathic/Primary VT/VF", "Selection Definition": "VT that occurs in patients without structural heart disease, metabolic abnormalities, or the long QT syndrome.", "Display Order": 3}, {"Name": "Syndromes with Risk of Sudden Death Type", "Code": "9651007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Long QT", "Selection Definition": "Long QT syndrome (LQTS) describes a heterogeneous group of inherited channelopathies that confer risks of polymorphic ventricular tachycardia and sudden cardiac death. Diagnosis is clinical and is made on the basis of the presentation and electrocardiogram, with the probability of LQTS calculated by the <PERSON> score. Genetic testing is generally advised; variants in KCNQ1, KCNH2, and SCN5A are responsible for LQT1, LQT2, and LQT3, respectively, accounting for approximately 75% of genetically resolved cases.", "Display Order": 4}, {"Name": "Syndromes with Risk of Sudden Death Type", "Code": "*********", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Short QT", "Selection Definition": "Short QT (SQT) refers to the electrocardiographic manifestation of accelerated cardiac repolarization. <PERSON><PERSON><PERSON> et al. were the first to suggest an association with atrial and ventricular fibrillation in 2000. The familial nature and arrhythmogenic potential of SQT were confirmed by <PERSON><PERSON><PERSON> et al. in 2003. Acquired disease –the most common cause– results from electrolyte disturbances or drugs, in addition to hypercalcemia, hyperkalemia, and acidosis; SQT manifests with digoxin, androgen use, increased vagal tone and after ventricular fibrillation (<PERSON>, 2004; <PERSON><PERSON><PERSON>, <PERSON>, & James, 2009; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2015). SQTS is a rare, sporadic or autosomal dominant disease that manifests with atrial and ventricular arrhythmias, sudden cardiac death and shortened QT (<PERSON><PERSON><PERSON> et al., 2004). Cardiac arrest occurs as the presenting symptom in up to 40% of the cases (<PERSON> et al., 2014). Mutations in potassium (KCNH2, KCNQ1, KCNJ2) and calcium (CACNA1C, CACNB2, CACNA2D1) channels have been identified as disease causing. ", "Display Order": 5}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Syndromes of sudden death"}, {"Parent Element Reference": 14264, "Parent Element Name": "Condition History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 14720, "Name": "Ventricular Fibrillation Date", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate the date of the ventricular fibrillation.", "Target Value": "The last value between birth and the first procedure in this admission", "Short Name": "VFibDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "NULL", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "71908006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "Ventricular Fibrillation Date (14720) must be Less than or Equal to Procedure Start Date and Time (7000)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Ventricular fibrillation (not due to reversible cause)"}, {"Parent Element Reference": 14264, "Parent Element Name": "Condition History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4250, "Name": "Most Recent Ventricular Tachycardia Date", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate the date of the most recent ventricular tachycardia.\n\nNote(s):\nIf the month or day of the ventricular tachycardia is unknown, please code 01/01/YYYY. If the specific year is unknown in the current record, the year may be estimated based on timeframes found in prior medical record documentation (Example: If the patient had \"most recent ventricular tachycardia\" documented in a record from 2011, then the year 2011 can be utilized and coded as 01/01/2011).\n\nCode the most recent and significant episode of VT. When the patient has a history of VT documented in the medical record by the clinician and a time frame is unable to be determined from prior medical records or by consulting with the clinician, please code the VT as having occurred 5 years ago. For example: If the physician documents on 05/01/2020, that the patient has a history of VT, please code Most Recent VT Date, Seq. 4250, as 05/01/2015.", "Target Value": "The last value between birth and the first procedure in this admission", "Short Name": "VTDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "25569003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Ventricular tachycardia"}, {"Parent Element Reference": 14264, "Parent Element Name": "Condition History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4275, "Name": "Ventricular Tachycardia Type", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate the type of ventricular tachycardia.\n\nNote(s):\n\nWhen only VT is documented, code VT Type, Seq. 4275 as Non-sustained VT.\nIf the VT is documented as sustained VT, code VT Type, Seq. 4275 as Sustained Monomorphic VT.\nIf there is documentation of VT treated with ATP (anti-tachycardia pacing) or shock therapy, or if there is VT arrest and the VT type is unknown, code VT Type, Seq. 4275 as Sustained Monomorphic VT.\nIf there are multiple episodes of VT, code the most severe episode of VT.\nIf sustained Vflutter is documented, code as Monomorphic VT.\n\n\n\n\n", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "VTType", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001124", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Ventricular Tachycardia Type", "Code": "251158004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Monomorphic", "Selection Definition": "Sustained monomorphic ventricular tachycardia (VT) is VT >30 seconds in duration or requiring termination due to hemodynamic compromise in <30 seconds that has a stable, single QRS morphology.", "Display Order": 1}, {"Name": "Ventricular Tachycardia Type", "Code": "444658006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": " Non-sustained", "Selection Definition": "Non-sustained or un-sustained ventricular tachycardia (VT) is three or more beats in duration, terminating spontaneously in <30 seconds. Non-sustained VT can be monomorphic or polymorphic.", "Display Order": 2}, {"Name": "Ventricular Tachycardia Type", "Code": "251159007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Polymorphic", "Selection Definition": "Sustained polymorphic ventricular tachycardia (VT) is VT >30 seconds in duration or requiring termination due to hemodynamic compromise in <30 seconds that has a changing or multiform QRS morphology at cycle length >180 milliseconds.", "Display Order": 3}, {"Name": "Ventricular Tachycardia Type", "Code": "100001127", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Monomorphic/polymorphic", "Selection Definition": "The patient has a history of both sustained monomorphic and sustained polymorphic ventricular tachycardia.", "Display Order": 4}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Ventricular tachycardia"}, {"Parent Element Reference": 14264, "Parent Element Name": "Condition History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4255, "Name": "Ventricular Tachycardia Occurred Post Cardiac Surgery", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate if the ventricular tachycardia occurred within the 48 hours after cardiac surgery.\n\nNote(s):\nOccurred Post Cardiac Surgery, Seq.4255, refers to open chest surgery, for example: CABG or Valve replacement. If there are multiple episodes of VT, code the most significant episode of VT.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "VTPostCardiacSurgery", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001123", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Ventricular tachycardia"}, {"Parent Element Reference": 14264, "Parent Element Name": "Condition History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4260, "Name": "Bradycardia Dependent Ventricular Tachycardia", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate if the ventricular tachycardia is bradycardia dependent.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000946", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Ventricular tachycardia"}, {"Parent Element Reference": 14264, "Parent Element Name": "Condition History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4265, "Name": "Ventricular Tachycardia Reversible Cause", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate if the ventricular tachycardia was deemed to be a result of a reversible cause. This could include, but is not limited to, drug abuse or electrolyte imbalance.\n\nNote(s):\nIf there are multiple episodes of VT, code the most significant episode of VT.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "VTReverseCause", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001126", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Ventricular Tachycardia Reversible Cause", "Definition": "Definition of ventricular tachycardia due to a reversible cause.\nThe most common putative reversible causes of arrest are acute ischemia and electrolyte imbalance. Other common potential causes to which cardiac arrest is attributed include proarrhythmic effects of antiarrhythmic drugs (see supporting references).\n\n1) Electrolyte abnormalities, including hypokalemia and hypomagnesemia, facilitate development of VT in predisposed patients receiving antiarrhythmic agents and other drugs associated with the LQTS. However, hypokalemia can also result from cardiac arrest and should not otherwise be assumed to be the cause of cardiac arrest, except under unusual circumstances.(see reference below) Correction of hypokalemia does not affect inducibility of monomorphic VT occurring after MI. Electrolyte abnormalities should not be assumed to be the cause of cardiac arrest, except in the presence of drug-induced LQTS. \n\n2) Drugs: In patients who develop polymorphic VT in association with drug-induced QT prolongation, withdrawal of the offending antiarrhythmic or other agent (e.g., antipsychotic) is usually sufficient to prevent arrhythmia recurrence. If ventricular function is normal, no therapy beyond drug withdrawal, avoidance of future drug exposure, and correction of electrolyte abnormalities is necessary. However, if ventricular function is abnormal, cardiac arrest or syncope should not be attributed solely to antiarrhythmic drugs, and evaluation and treatment should be similar to patients experiencing such events in the absence of antiarrhythmic drugs. Occasionally, patients develop monomorphic sustained VT only in the presence of antiarrhythmic drugs without QT prolongation. In such cases, it may appear that the development of spontaneous VT is dependent on drug administration. In most patients exhibiting this behavior, the monomorphic VT is inducible by EP testing in the absence of antiarrhythmic drugs.", "Source": "ACC/AHA/ESC 2006 Guidelines for Management of Patients With Ventricular Arrhythmias"}, "Parent Child Validations": [{"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Ventricular tachycardia"}, {"Parent Element Reference": 14264, "Parent Element Name": "Condition History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4270, "Name": "Ventricular Tachycardia with Hemodynamic Instability", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate if the patient demonstrated hemodynamic instability while having episodes of sustained or non-sustained ventricular tachycardia.\n\nNote(s):\nHemodynamic instability can include periods of reduced, unstable, or abnormal blood pressure with near syncope, or episodes of syncope. It creates a state of hypoperfusion that does not support normal organ perfusion or function.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "HemoInstability", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001125", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Ventricular tachycardia"}, {"Parent Element Reference": 14264, "Parent Element Name": "Condition History Occurrence", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "History and Risk Factors", "Section Display Name": "Procedure History", "Section Code": "PROCHX", "Section Type": "Repeater Section", "Cardinality": "0..n", "Table": "PROCHX", "Elements": [{"Element Reference": 12905, "Name": "Procedure History Name", "Section Display Name": "Procedure History", "Coding Instructions": "Select the procedures for which the patient has a medical history.\n\nNotes:  \n1. Do NOT select \"Candidate for VAD\" while also selecting \"Currently on VAD\"\n2. Do NOT select \"On Heart Transplant Waiting List\" while also selecting \"Candidate for transplant\"\n", "Target Value": "N/A", "Short Name": "ProcedHxName", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "416940007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "Procedure History Name (12905) should not be duplicated in an episode", "Selections": [{"Name": "Procedure History Name", "Code": "112000001755", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Aortic valve procedure", "Selection Definition": "Any previous surgical or interventional replacement and/or repair of the aortic valve.", "Display Order": 1}, {"Name": "Procedure History Name", "Code": "33367005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Coronary angiography", "Selection Definition": null, "Display Order": 2}, {"Name": "Procedure History Name", "Code": "232717009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Prior coronary artery bypass graft", "Selection Definition": null, "Display Order": 3}, {"Name": "Procedure History Name", "Code": "100000954", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "CV implantable electronic device", "Selection Definition": null, "Display Order": 4}, {"Name": "Procedure History Name", "Code": "415070008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Prior PCI", "Selection Definition": null, "Display Order": 5}, {"Name": "Procedure History Name", "Code": "112000002045", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Candidate for VAD", "Selection Definition": "VAD (ventricular assist device) is the general term for a surgically implanted MCS (mechanical circulatory support) device that is intended for use outside the hospital. The purpose of a VAD is to support patients with HF by increasing perfusion and reducing the filling pressures in the heart. Treatment with VAD is currently being considered for this patient.", "Display Order": 6}, {"Name": "Procedure History Name", "Code": "112000002046", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Currently on VAD", "Selection Definition": "VAD (ventricular assist device) is the general term for a surgically implanted MCS (mechanical circulatory support) device that is intended for use outside the hospital. The purpose of a VAD is to support patients with HF by increasing perfusion and reducing the filling pressures in the heart. The patient is currently being treated with VAD.", "Display Order": 7}, {"Name": "Procedure History Name", "Code": "471300007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "On Heart Transplant Waiting List", "Selection Definition": "The patient is currently waiting for a transplant to be performed.", "Display Order": 8}, {"Name": "Procedure History Name", "Code": "100000821", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Candidate for transplant", "Selection Definition": "The patient currently meets the criteria for transplant.", "Display Order": 9}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 14268, "Name": "Procedure History Occurrence", "Section Display Name": "Procedure History", "Coding Instructions": "Indicate whether or not the patient has undergone the listed medical procedures.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "ProcHxOccur", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "416940007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 12905, "Parent Element Name": "Procedure History Name", "Parent Element Selection Name": "Any Value"}]}, {"Element Reference": 14252, "Name": "Procedure History Date", "Section Display Name": "Procedure History", "Coding Instructions": "Indicate the date the procedure was performed.\n \nNote(s): \nIf the month or day of the procedure is unknown, please code 01/01/YYYY. If the specific year is unknown in the current record, the year may be estimated based on timeframes found in prior medical record documentation (Example: If the patient had \"most recent procedure\" documented in a record from 2011, then the year 2011 can be utilized and coded as 01/01/2011).", "Target Value": "The last value between birth and the first procedure in this admission", "Short Name": "ProcHistDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "416940007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "Procedure History Date (14252) must be Less than or Equal to Procedure Start Date and Time (7000)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 12905, "Parent Element Name": "Procedure History Name", "Parent Element Selection Name": "Aortic valve procedure"}, {"Parent Element Reference": 12905, "Parent Element Name": "Procedure History Name", "Parent Element Selection Name": "Prior coronary artery bypass graft"}, {"Parent Element Reference": 12905, "Parent Element Name": "Procedure History Name", "Parent Element Selection Name": "Coronary angiography"}, {"Parent Element Reference": 12905, "Parent Element Name": "Procedure History Name", "Parent Element Selection Name": "CV implantable electronic device"}, {"Parent Element Reference": 12905, "Parent Element Name": "Procedure History Name", "Parent Element Selection Name": "Prior PCI"}, {"Parent Element Reference": 14268, "Parent Element Name": "Procedure History Occurrence", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "History and Risk Factors", "Section Display Name": "Procedure History Details", "Section Code": "PROCHXDET", "Section Type": "Section", "Cardinality": "0..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 4305, "Name": "Performed After Most Recent Cardiac Arrest", "Section Display Name": "Procedure History Details", "Coding Instructions": "Indicate if the coronary angiography was performed after the most recent cardiac arrest.\n\nNote(s):\nIf the patient has had a history of cardiac arrest, then the response should be based on whether the most recent angiogram was performed after the most recent cardiac arrest.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "PerfAfterRecentCA", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001201", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 12905, "Parent Element Name": "Procedure History Name", "Parent Element Selection Name": "Coronary angiography"}, {"Parent Element Reference": 14268, "Parent Element Name": "Procedure History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4310, "Name": "Results of Angiography", "Section Display Name": "Procedure History Details", "Coding Instructions": "Indicate the result of the coronary angiography performed.\n\nSelect ‘Significant disease’ for patients who have a history of PCI/CABG, without a repeat coronary angiogram.", "Target Value": "Any occurrence between birth and the procedure", "Short Name": "CoronaryAngioResults", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "365853002:418775008=77343006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Results of Angiography", "Code": "100000641", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "No significant disease", "Selection Definition": "There was <50% stenosis in the left main coronary artery and <70% in all major coronary artery branches >= 2.0 mm.", "Display Order": 1}, {"Name": "Results of Angiography", "Code": "100001223", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Significant disease", "Selection Definition": "There was >= 50% stenosis in the left main coronary artery and/or >=70% stenosis in any major coronary artery (>= 2.0 mm).", "Display Order": 2}, {"Name": "Results of Angiography", "Code": "100001220", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Non-revascularized significant disease", "Selection Definition": "The patient is not a candidate for revascularization of their significant coronary artery disease.", "Display Order": 3}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 12905, "Parent Element Name": "Procedure History Name", "Parent Element Selection Name": "Coronary angiography"}, {"Parent Element Reference": 14268, "Parent Element Name": "Procedure History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4315, "Name": "Revascularization Performed", "Section Display Name": "Procedure History Details", "Coding Instructions": "Indicate if an attempt at revascularization of the coronary artery disease was performed.\n\nNote(s):\nThe intent is to evaluate the status of the arteries and / or grafts at the time of the ICD implant. Code the status of the vessels/grafts at the time of the most recent catheterization.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "Revasc<PERSON>erf", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "81266008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4310, "Parent Element Name": "Results of Angiography", "Parent Element Selection Name": "Significant disease"}]}, {"Element Reference": 4320, "Name": "Revascularization Outcome", "Section Display Name": "Procedure History Details", "Coding Instructions": "Indicate the outcome of the revascularization.", "Target Value": "The last value between birth and current procedure", "Short Name": "RevascOutcome", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001224", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Revascularization Outcome", "Code": "100001221", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Complete revascularization", "Selection Definition": "Residual stenosis <50% in all revascularizable diseased coronary arteries.", "Display Order": 1}, {"Name": "Revascularization Outcome", "Code": "100001222", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Incomplete revascularization", "Selection Definition": "Not all revascularizable diseased coronary arteries resulted in <50% stenosis.", "Display Order": 2}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4315, "Parent Element Name": "Revascularization Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 15793, "Name": "Prior CIED Device Type", "Section Display Name": "Procedure History Details", "Coding Instructions": "Indicate the prior defibrillator(s) or permanent pacemaker(s) that the patient has had previously implanted.\n", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "PriCIEDDevTyp", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": null, "Is Dynamic List": "Yes", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000003752", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Prior CIED Device Type", "Code": "112000003709", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Left ventricular endocardial pacemaker", "Selection Definition": null, "Display Order": 1}, {"Name": "Prior CIED Device Type", "Code": "100001216", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "CRT-D", "Selection Definition": null, "Display Order": 2}, {"Name": "Prior CIED Device Type", "Code": "112000003612", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extravascular ICD", "Selection Definition": null, "Display Order": 3}, {"Name": "Prior CIED Device Type", "Code": "100001215", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Dual chamber ICD", "Selection Definition": null, "Display Order": 4}, {"Name": "Prior CIED Device Type", "Code": "100001214", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Single chamber ICD", "Selection Definition": null, "Display Order": 5}, {"Name": "Prior CIED Device Type", "Code": "100001217", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Sub Q ICD", "Selection Definition": null, "Display Order": 6}, {"Name": "Prior CIED Device Type", "Code": "704708004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "CRT-P", "Selection Definition": null, "Display Order": 7}, {"Name": "Prior CIED Device Type", "Code": "112000003679", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Dual chamber transvenous pacemaker", "Selection Definition": null, "Display Order": 8}, {"Name": "Prior CIED Device Type", "Code": "112000003680", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Single chamber transvenous pacemaker", "Selection Definition": null, "Display Order": 9}, {"Name": "Prior CIED Device Type", "Code": "112000003671", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Leadless dual chamber pacemaker", "Selection Definition": null, "Display Order": 10}, {"Name": "Prior CIED Device Type", "Code": "112000002030", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Leadless single chamber pacemaker", "Selection Definition": null, "Display Order": 11}, {"Name": "Prior CIED Device Type", "Code": "112000003669", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "His Bundle pacemaker", "Selection Definition": null, "Display Order": 12}, {"Name": "Prior CIED Device Type", "Code": "112000003670", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Left Bundle pacemaker", "Selection Definition": null, "Display Order": 13}, {"Name": "Prior CIED Device Type", "Code": "467207002", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Cardiac contractility modulation", "Selection Definition": null, "Display Order": 14}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 12905, "Parent Element Name": "Procedure History Name", "Parent Element Selection Name": "CV implantable electronic device"}, {"Parent Element Reference": 14268, "Parent Element Name": "Procedure History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4510, "Name": "Cardiomyopathy prior to PCI", "Section Display Name": "Procedure History Details", "Coding Instructions": "Indicate if the patient had pre-existing cardiomyopathy prior to the PCI procedure.\n\nNote(s): \nIf there is no documentation regarding pre-existing cardiomyopathy, code No. If the patient has documentation of an LVEF < 40% as well as heart failure prior to the PCI, code Yes.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "PriorPCICardioPresent", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000952", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}, {"Parent Element Reference": 12905, "Parent Element Name": "Procedure History Name", "Parent Element Selection Name": "Prior PCI"}, {"Parent Element Reference": 14268, "Parent Element Name": "Procedure History Occurrence", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "Diagnostic Studies", "Section Code": "DIAGSTUDIES", "Section Type": "Section", "Cardinality": "0..1", "Table": "EPISODEOFCARE", "Elements": null}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Diagnostic Studies", "Section Display Name": "EP Study", "Section Code": "EPStudy", "Section Type": "Section", "Cardinality": "0..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 5000, "Name": "Electrophysiology Study", "Section Display Name": "EP Study", "Coding Instructions": "Indicate if the patient had an electrophysiology study (EPS).\nNote(s): Code 'Yes' for an EP Study/Ablation performed for either ventricular or atrial arrhythmias prior to the start of the ICD procedure.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "EPStudy", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "252425004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Electrophysiology Study", "Definition": "One or more catheters capable of recording and pacing are placed in one or more of the cardiac chambers. The catheters may be used to measure conduction of the impulse from the sinus node to the ventricle; induce a tachycardia; and/or localize (map) the location where the tachycardia originates.", "Source": "NCDR"}, "Parent Child Validations": [{"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}]}, {"Element Reference": 5005, "Name": "Electrophysiology Study Date", "Section Display Name": "EP Study", "Coding Instructions": "Indicate the date in which the most recent electrophysiology study (EPS) was performed.\n\nNote(s):\nIf the month or day of the EP study is unknown, please code 01/01/YYYY. If the specific year is unknown in the current record, the year may be estimated based on timeframes found in prior medical record documentation (Example: If the patient had \"most recent EP study\" documented in a record from 2011, then the year 2011 can be utilized and coded as 01/01/2011).", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "EPStudyDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "252425004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Electrophysiology Study", "Definition": "One or more catheters capable of recording and pacing are placed in one or more of the cardiac chambers. The catheters may be used to measure conduction of the impulse from the sinus node to the ventricle; induce a tachycardia; and/or localize (map) the location where the tachycardia originates.", "Source": "NCDR"}, "Parent Child Validations": [{"Parent Element Reference": 5000, "Parent Element Name": "Electrophysiology Study", "Parent Element Selection Name": "Yes"}, {"Parent Element Reference": 5010, "Parent Element Name": "Electrophysiology Study Date Unknown", "Parent Element Selection Name": "No (or Not Answered)"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "EP Study", "Section Display Name": "EP Study", "Section Code": "EPStudy2", "Section Type": "Section", "Cardinality": "0..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 5010, "Name": "Electrophysiology Study Date Unknown", "Section Display Name": "EP Study", "Coding Instructions": "Indicate if the date when the electrophysiology study (EPS) was performed is unknown.", "Target Value": "The last value between birth and the first procedure in this admission", "Short Name": "EPStudyDateUnk", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "252425004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 5000, "Parent Element Name": "Electrophysiology Study", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 5015, "Name": "Clinically Relevant Ventricular Arrhythmias Induced", "Section Display Name": "EP Study", "Coding Instructions": "Indicate if clinically relevant ventricular arrhythmias were induced during the electrophysiology study.\n\nNotes(s):\nA clinically relevant ventricular arrhythmia induced during electrophysiology study most often represents sustained monomorphic ventricular tachycardia, but can include other clinically relevant, sustained ventricular tachyarrhythmias thought to contribute to syncope, aborted cardiac death, or other serious clinical presentations.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "VentArrythInduced", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001119", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 5000, "Parent Element Name": "Electrophysiology Study", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Diagnostic Studies", "Section Display Name": "Diagnostic Studies", "Section Code": "DIAGSTUDIES2", "Section Type": "Section", "Cardinality": "0..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 5030, "Name": "Electrocardiogram Performed", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate if the patient had an electrocardiogram (ECG).\n\nNote: 12-lead ECG only", "Target Value": "The last value within 90 days of procedure start", "Short Name": "ECG", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "164847006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Permanent pacemaker"}]}, {"Element Reference": 5040, "Name": "Electrocardiogram Normal", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate if the electrocardiogram (ECG) clinical interpretation notes normal sinus rhythm ECG.", "Target Value": "The last value within 90 days of procedure start", "Short Name": "ECGNormal", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "164854000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 5030, "Parent Element Name": "Electrocardiogram Performed", "Parent Element Selection Name": "Yes"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}]}, {"Element Reference": 5105, "Name": "Ventricular Paced", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate if the patient is ventricular paced.\n\nNote(s): \nIf no ECG is available, a pre-procedure 6 inch cardiac rhythm strip or clinician documentation may be utilized to obtain this information.", "Target Value": "The last value within 90 days of procedure start", "Short Name": "VPaced", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "251266004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "When Ventricular Paced (5105) is (No) then Only Ventricular Paced QRS Complexes Present (5045) must not be (Yes)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Permanent pacemaker"}]}, {"Element Reference": 5045, "Name": "Only Ventricular Paced QRS Complexes Present", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate if there were only ventricular paced QRS complexes present.\n\nNote(s):\nIf the patient has some intrinsic ventricular complexes present, code “No”.\n\nIf no ECG is available, a pre-procedure 6 inch cardiac rhythm strip or clinician documentation may be utilized to obtain this information.", "Target Value": "The last value within 90 days of procedure start", "Short Name": "VPQRS", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001120", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Permanent pacemaker"}]}, {"Element Reference": 5050, "Name": "Ventricular Paced QRS Duration", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate the duration of the ventricular paced QRS complex in milliseconds that was derived from the surface electrocardiogram (ECG). Surface ECGs are obtained from the surface of the body and do not include intracardiac ECGs.\n\nNote(s):\nIf no ECG is available, a pre-procedure 6-inch cardiac rhythm strip or clinician documentation may be utilized to obtain this information.\n\nProvider documentation of QRS, BBB, and atrial rhythm will be utilized first, then the finding from most recent ECG. If neither are available, use a 6 inch rhythm strip or device interrogation to code QRS, BBB, and atrial rhythm. However, if a 6 inch rhythm strip is used, ECG, Seq. 5030, will be coded No. Use the following order to code:\n1. Provider documentation, if not then\n2. Most recent ECG, if not, then\n3. 6 inch rhythm strip and/or device interrogation\n", "Target Value": "The last value within 90 days of procedure start", "Short Name": "VPacedQRS", "Data Type": "PQ", "Precision": "3,0", "Unit Of Measure": "msec", "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001121", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "msec", "Usual Range Min": 20.0, "Usual Range Max": 250.0, "Valid Range Min": 10.0, "Valid Range Max": "300"}], "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 5045, "Parent Element Name": "Only Ventricular Paced QRS Complexes Present", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 5055, "Name": "Non-Ventricular Paced QRS duration", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate the duration of the non-ventricular paced or intrinsic QRS complex, in milliseconds, that was derived from the surface electrocardiogram (ECG).  Surface ECGs are obtained from the surface of the body and do not include intracardiac ECGs.\n\nNote(s): \nIf no ECG is available, a pre-procedure 6-inch cardiac rhythm strip or clinician documentation may be utilized to obtain this information.\n\nProvider documentation of QRS, BBB, and atrial rhythm will be utilized first, then the finding from most recent ECG. If neither are available, use a 6 inch rhythm strip or device interrogation to code QRS, BBB, and atrial rhythm. However, if a 6 inch rhythm strip is used, ECG, Seq. 5030, will be coded No. Use the following order to code:\n1. Provider documentation, if not then\n2. Most recent ECG, if not, then\n3. 6 inch rhythm strip and/or device interrogation", "Target Value": "The last value within 90 days of procedure start", "Short Name": "NVPQRS", "Data Type": "PQ", "Precision": "3,0", "Unit Of Measure": "msec", "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "251208001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "msec", "Usual Range Min": 20.0, "Usual Range Max": 250.0, "Valid Range Min": 10.0, "Valid Range Max": "300"}], "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 5045, "Parent Element Name": "Only Ventricular Paced QRS Complexes Present", "Parent Element Selection Name": "No"}]}, {"Element Reference": 5060, "Name": "Abnormal Intraventricular Conduction", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate if the patient has abnormal intraventricular conduction, bundle branch blocks, or non-specific conduction delays.\n\nNote(s):\nCode 'No' if the abnormal intraventricular conduction is determined by the physician to be transient or rate related.\nThis data element is evaluating the intrinsic rhythm.\nCode 'No' if the QRS duration is >= 110msec without supporting documentation from the clinician.  Must be a clinical diagnosis.", "Target Value": "The last value within 90 days of procedure start", "Short Name": "AbConduction", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "4554005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Permanent pacemaker"}]}, {"Element Reference": 5065, "Name": "Abnormal Intraventricular Conduction Types", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate the type of intraventricular conduction(s) the patient has.\n\nNote(s):\nIf the patient has multiple intraventricular conduction types, select all types.", "Target Value": "The last value within 90 days of procedure start", "Short Name": "IntraVentConductionType", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001142", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Abnormal Intraventricular Conduction Types", "Code": "32758004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Alternating RBBB and LBBB", "Selection Definition": null, "Display Order": 1}, {"Name": "Abnormal Intraventricular Conduction Types", "Code": "698252002", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Delay, nonspecific", "Selection Definition": null, "Display Order": 2}, {"Name": "Abnormal Intraventricular Conduction Types", "Code": "164909002", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": " Left bundle branch block (LBBB)", "Selection Definition": null, "Display Order": 3}, {"Name": "Abnormal Intraventricular Conduction Types", "Code": "164907000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Right bundle branch block (RBBB)", "Selection Definition": null, "Display Order": 4}], "Ranges": null, "Definition": {"Title": "Intraventricular Conduction Types", "Definition": "-Left Bundle Branch is characterized by QRS duration 120 ms or longer, delayed onset of intrinsicoid deflection in 1, V5, and V6 >60 ms, broad and notched or slurred R waves in I, aVL, V5, and V6, rS or QS complexes in right precordial leads, ST-segment and T waves in opposite polarity to the major QRS deflection.\n-Non-Specific abnormal Intraventricular conduction delays are characterized by a QRS duration of 110 ms or more with morphology different from LBBB or RBBB.\n-Right Bundle Branch Block is characterized by a QRS duration of 120 ms, rsR'or rSR' complexes in V1 and V2, Delayed onset of intrinsicoid, deflection in V1 and V2 >50 ms, Broad, slurred S wave in 1, V5, and V6 Secondary ST-T wave changes.", "Source": "ACC/AHA/HRS 2006 Key Data Elements and Definitions for Electrophysiological Studies and Procedures."}, "Parent Child Validations": [{"Parent Element Reference": 5060, "Parent Element Name": "Abnormal Intraventricular Conduction", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 5100, "Name": "Atrial Rhythm", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate the patient's atrial rhythm at the start of the procedure.\n\nNote(s):\nIf the patient has multiple atrial rhythms, select all that apply.\nIn the event that a patient is ventricular paced, indicate the underlying atrial rhythm.  \nIf the atrial rhythm is not documented, leave \"Atrial Rhythm\" blank.\nTarget value applies to the first procedure captured for this registry.\nIf no ECG is available, a pre-procedure 6 inch cardiac rhythm strip or clinician documentation may be utilized to obtain this information.\n\nProvider documentation of QRS, BBB, and atrial rhythm will be utilized first, then the finding from most recent ECG. If neither are available, use a 6-inch rhythm strip or device interrogation to code QRS, BBB, and atrial rhythm. However, if a 6 inch rhythm strip is used, ECG, Seq. 5030, will be coded No. Use the following order to code:\n1. Provider documentation, if not then\n2. Most recent ECG, if not, then\n3. 6 inch rhythm strip and/or device interrogation", "Target Value": "The last value within 90 days of procedure start", "Short Name": "AtrialRhythm", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "106068003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Atrial Rhythm", "Code": "49436004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Atrial fibrillation", "Selection Definition": null, "Display Order": 1}, {"Name": "Atrial Rhythm", "Code": "5370000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Atrial flutter", "Selection Definition": null, "Display Order": 2}, {"Name": "Atrial Rhythm", "Code": "251268003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Atrial paced", "Selection Definition": null, "Display Order": 3}, {"Name": "Atrial Rhythm", "Code": "276796006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Atrial tachycardia", "Selection Definition": null, "Display Order": 4}, {"Name": "Atrial Rhythm", "Code": "106067008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Sinus", "Selection Definition": null, "Display Order": 5}, {"Name": "Atrial Rhythm", "Code": "5609005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Sinus arrest", "Selection Definition": null, "Display Order": 6}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Permanent pacemaker"}]}, {"Element Reference": 4150, "Name": "Prior LVEF Assessed", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate if a left ejection fraction percentage has been assessed.\n\nNote: If the LVEF has a date or statement of date affiliated with it, which confirms it was performed in the last 12 months, then you are able to utilize that LVEF in coding. An LVEF measurement in a dictated note is not sufficient unless there is a date affiliated with it (e.g., LVEF asssessed May 2020).", "Target Value": "Any occurrence between 12 months prior to arrival and start of the first procedure", "Short Name": "PriorLVEFAssessed", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001027", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Permanent pacemaker"}]}, {"Element Reference": 4155, "Name": "Most Recent LVEF Date", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate the date of the implanting physician cited LVEF or the most recent LVEF assessed if the implanting physician value is not available. \n\nNote(s): \nIf the month or day of the LVEF is unknown, please code 01/01/YYYY. If the specific year is unknown in the current record, the year may be estimated based on timeframes found in prior medical record documentation (Example: If the patient had \"most recent LVEF\" documented in a record from 2011, then the year 2011 can be utilized and coded as 01/01/2011).\n\nIf the LVEF has a date or statement of date affiliated with it, which confirms it was performed in the last 12 months, then you are able to utilize that LVEF in coding. An LVEF measurement in a dictated note is not sufficient unless there is a date affiliated with it (e.g., LVEF assessed May 2020).", "Target Value": "Any occurrence between 12 months prior to arrival and start of the first procedure", "Short Name": "PriorLVEFDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001027", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4150, "Parent Element Name": "Prior LVEF Assessed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4160, "Name": "Most Recent LVEF %", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate the left ventricular ejection fraction cited by the implanting physician as the indication for the ICD. In the absence of a physician cited LVEF, indicate the most recent left ventricular ejection fraction. The left ventricular ejection fraction can be assessed via invasive (i.e. LV gram), or non-invasive (i.e. Echo, MR, CT or Nuclear) testing.\n\nNote(s):\nEnter a percentage in the range of 01 - 99. If a percentage range is reported, report the lowest number of the range (i.e.50-55%, is reported as 50%).\nAn LVEF measurement is reported as \"less than\" or \"greater than\", code to the nearest whole number (e.g., < 40% is coded 39% and > 40% is coded 41%).\n", "Target Value": "The last value between 12 months prior to arrival and start of the first procedure", "Short Name": "PriorLVEF", "Data Type": "PQ", "Precision": "2,0", "Unit Of Measure": "%", "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "10230-1", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "%", "Usual Range Min": 5.0, "Usual Range Max": 70.0, "Valid Range Min": 1.0, "Valid Range Max": "99"}], "Definition": {"Title": "Most Recent LVEF %", "Definition": "The left ventricular ejection fraction is the percentage of blood emptied from the left ventricle at the end of contraction.", "Source": "ACC Clinical Data Standards, Society for Thoracic Surgeons Adult Cardiac Surgery Database (STS)"}, "Parent Child Validations": [{"Parent Element Reference": 4150, "Parent Element Name": "Prior LVEF Assessed", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "Labs", "Section Code": "LABS", "Section Type": "Section", "Cardinality": "0..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 6025, "Name": "Blood Urea Nitrogen", "Section Display Name": "Labs", "Coding Instructions": "Indicate the blood urea nitrogen (BUN) value, in mg/dL.\n\nNote(s):\nWhen the value falls outside of the usual range (Example: Bun is > 20 but less than 99), an \"Outlier Warning\" will be displayed in the quality check. This will not affect DQR submission. It is a notification to double check the entered value. If the BUN value is greater than the valid range (over 100), code “99”.", "Target Value": "The last value within 30 days prior to the first procedure in this admission", "Short Name": "BUN", "Data Type": "PQ", "Precision": "2,0", "Unit Of Measure": "mg/dL", "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "6299-2", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "mg/dL", "Usual Range Min": 5.0, "Usual Range Max": 20.0, "Valid Range Min": 1.0, "Valid Range Max": "99"}], "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 6026, "Parent Element Name": "BUN Not Drawn", "Parent Element Selection Name": "No (or Not Answered)"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Permanent pacemaker"}]}, {"Element Reference": 6026, "Name": "BUN Not Drawn", "Section Display Name": "Labs", "Coding Instructions": "Indicate if a blood urea nitrogen (BUN) was not drawn.", "Target Value": "The last value within 30 days prior to the first procedure in this admission", "Short Name": "BUNND", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "6299-2", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Permanent pacemaker"}]}, {"Element Reference": 6030, "Name": "Hemoglobin", "Section Display Name": "Labs", "Coding Instructions": "Indicate the hemoglobin (Hgb) value in g/dL.", "Target Value": "The last value within 30 days prior to the first procedure in this admission", "Short Name": "HGB", "Data Type": "PQ", "Precision": "4,2", "Unit Of Measure": "g/dL", "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "718-7", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "g/dL", "Usual Range Min": 5.0, "Usual Range Max": 20.0, "Valid Range Min": 1.0, "Valid Range Max": "50.00"}], "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 6031, "Parent Element Name": "Hemoglobin Not Drawn", "Parent Element Selection Name": "No (or Not Answered)"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Permanent pacemaker"}]}, {"Element Reference": 6031, "Name": "Hemoglobin Not Drawn", "Section Display Name": "Labs", "Coding Instructions": "Indicate if the hemoglobin was not drawn.", "Target Value": "The last value within 30 days prior to the first procedure in this admission", "Short Name": "HGBND", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "718-7", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Permanent pacemaker"}]}, {"Element Reference": 6035, "Name": "Sodium", "Section Display Name": "Labs", "Coding Instructions": "Indicate the sodium (Na) level, in mEq/L.", "Target Value": "The last value within 30 days prior to the first procedure in this admission", "Short Name": "Sodium", "Data Type": "PQ", "Precision": "3,0", "Unit Of Measure": "mEq/L", "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2950-4", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "mEq/L", "Usual Range Min": 120.0, "Usual Range Max": 150.0, "Valid Range Min": 1.0, "Valid Range Max": "300"}], "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 6036, "Parent Element Name": "Sodium Not Drawn", "Parent Element Selection Name": "No (or Not Answered)"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Permanent pacemaker"}]}, {"Element Reference": 6036, "Name": "Sodium Not Drawn", "Section Display Name": "Labs", "Coding Instructions": "Indicate if the sodium level was not drawn.", "Target Value": "The last value within 30 days prior to the first procedure in this admission", "Short Name": "SodiumND", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2950-4", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Permanent pacemaker"}]}, {"Element Reference": 6045, "Name": "International Normalized Ratio (INR)", "Section Display Name": "Labs", "Coding Instructions": "Record the international normalized ratio (INR).\n\nNote(s): Enter the value to as many decimal places as is available on the medical record and to where the tool will allow.  Do not round - values are not altered.", "Target Value": "The last value between 1 day prior to the procedure and the current procedure", "Short Name": "INR", "Data Type": "PQ", "Precision": "3,1", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "34714-6", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": null, "Usual Range Min": 0.9, "Usual Range Max": 1.3, "Valid Range Min": 0.5, "Valid Range Max": "30.0"}], "Definition": {"Title": "International Normalized Ratio (INR)", "Definition": "The INR is specifically intented for evaluating protime results on patients stabilized on long term oral anticoagulant therapy. The INR is not appropriate to evalulate hemostatic function in patients with liver disease, for screening for hereditary factor deficiencies or acquired vitamin K deficiencies, or for routine preoperative screening; this should be evaluated on the normal range in seconds. INR is calculated by the equation, INR = (PTR) raised to the power of ISI, where ISI = International Sensitivity Index (assigned to each reagent thromboplastin). PTR = prothrombin time ratio (pat PT/pop mean PT). Computation of the INR of specific thromboplastin reagent should allow for uniformity in prothrombin time testing regardless of the reagent system or instrumentation used.", "Source": "http://s.details.loinc.org/LOINC/6301-6.html?sections=Simple"}, "Parent Child Validations": [{"Parent Element Reference": 6046, "Parent Element Name": "International Normalized Ratio Not Drawn", "Parent Element Selection Name": "No (or Not Answered)"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Permanent pacemaker"}]}, {"Element Reference": 6046, "Name": "International Normalized Ratio Not Drawn", "Section Display Name": "Labs", "Coding Instructions": "Indicate if INR was not drawn.", "Target Value": "N/A", "Short Name": "INRND", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "34714-6", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Permanent pacemaker"}]}, {"Element Reference": 6050, "Name": "Creatinine", "Section Display Name": "Labs", "Coding Instructions": "Indicate the creatinine (Cr) level mg/dL.\n\nNote(s):\nThis may include POC (Point of Care) testing results or results obtained prior to arrival at this facility.", "Target Value": "The last value between 30 days prior to the procedure and the current procedure", "Short Name": "PreProcCreat", "Data Type": "PQ", "Precision": "4,2", "Unit Of Measure": "mg/dL", "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2160-0", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "mg/dL", "Usual Range Min": 0.1, "Usual Range Max": 5.0, "Valid Range Min": 0.1, "Valid Range Max": "30.00"}], "Definition": {"Title": "Creatinine", "Definition": "Creatinine or creatine anhydride, is a breakdown product of creatine phosphate in muscle. The loss of water molecule from creatine results in the formation of creatinine. It is transferred to the kidneys by blood plasma, whereupon it is eliminated by glomerular filtration and partial tubular excretion. Creatinine is usually produced at a fairly constant rate and measuring its serum level is a simple test. A rise in blood creatinine levels is observed only with marked damage to functioning nephrons; therefore this test is not suitable for detecting early kidney disease. Creatine and creatinine are metabolized in the kidneys, muscle, liver and pancreas.", "Source": "http://s.details.loinc.org/LOINC/2160-0.html?sections=Simple"}, "Parent Child Validations": [{"Parent Element Reference": 6051, "Parent Element Name": "<PERSON><PERSON><PERSON>ine Not Drawn", "Parent Element Selection Name": "No (or Not Answered)"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Permanent pacemaker"}]}, {"Element Reference": 6051, "Name": "<PERSON><PERSON><PERSON>ine Not Drawn", "Section Display Name": "Labs", "Coding Instructions": "Indicate if a creatinine level was not drawn.", "Target Value": "N/A", "Short Name": "PreProcCreatND", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2160-0", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Permanent pacemaker"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "Procedure Information", "Section Code": "PROCINFO", "Section Type": "Repeater Section", "Cardinality": "1..n", "Table": "PROCINFO", "Elements": [{"Element Reference": 15694, "Name": "Procedure Room Entry Date and Time", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate the date and time the patient entered the procedure room.\n", "Target Value": "The value on current procedure", "Short Name": "ProcedureEntryTime", "Data Type": "TS", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001197", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Procedure Room Entry Date and Time (15694) must be Greater than or Equal to Arrival Date (3000)\n\nProcedure Room Entry Date and Time (15694) must be Less than Procedure Room Exit Date and Time (15695)\n\nProcedure Room Entry Date and Time (15694) must be unique within an episode of care", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7000, "Name": "Procedure Start Date and Time", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate the date and time the procedure started. The time of the procedure is the time that the skin incision, vascular access, or its equivalent, was made in order to start the procedure.\n\nNote(s):\nIndicate the date/time (mm/dd/yyyy hours:minutes) using the military 24-hour clock, beginning at midnight (0000 hours).", "Target Value": "Any occurrence on current procedure", "Short Name": "ProcedureStartDateTime", "Data Type": "TS", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "1000142460", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Procedure Start Date and Time (7000) must be Greater than or Equal to Arrival Date (3000)\n\nProcedure Start Date and Time (7000) must be Less than Procedure End Date and Time (7005)\n\nProcedure Start Date and Time (7000) must be Less than or Equal to Discharge Date (10100)\n\nProcedure Start Date and Time (7000) must be unique within an episode of care", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7005, "Name": "Procedure End Date and Time", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate the ending date and time at which the operator breaks scrub at the end of the procedure.\n\nNote(s):\nIf more than one operator is involved in the case then use the date and time the last operator breaks scrub.", "Target Value": "The value on current procedure", "Short Name": "ProcedureEndDateTime", "Data Type": "TS", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "1000142459", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Procedure End Date and Time (7005) must be Less than or Equal to Discharge Date (10100)\n\nProcedure End Date and Time (7005) and Procedure Start Date and Time (7000) must not overlap on multiple procedures", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 15695, "Name": "Procedure Room Exit Date and Time", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate the date and time the patient exits the procedure room.\n", "Target Value": "The value on current procedure", "Short Name": "ProcedureStopTime", "Data Type": "TS", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001198", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Procedure Room Entry Date and Time (15694) and Procedure Room Exit Date and Time (15695) must not overlap on multiple procedures\n\nProcedure Room Exit Date and Time (15695) must be unique within an episode of care", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7010, "Name": "Procedure Type", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate the procedure that was performed. ", "Target Value": "Any occurrence on current procedure", "Short Name": "ProcedureType", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001857", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "When Procedure Type (7010) is (Generator explant) then Device Explanted (7660) must not be (Not explanted, Previously explanted)\n\nProcedure Type (7010) of (Initial generator implant) may only take place in the initial lab visit", "Selections": [{"Name": "Procedure Type", "Code": "428625001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Generator change", "Selection Definition": "The patient already has a device and is receiving a generator that is an upgrade or a change from one that was previously implanted.", "Display Order": 1}, {"Name": "Procedure Type", "Code": "233171004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Generator explant", "Selection Definition": "Patient already has a device and is having the generator removed without re-implant of another generator during the current procedure.", "Display Order": 2}, {"Name": "Procedure Type", "Code": "233170003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Initial generator implant", "Selection Definition": "The patient is receiving a device for the first time.", "Display Order": 3}, {"Name": "Procedure Type", "Code": "100001025", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Lead only", "Selection Definition": "A lead procedure is being performed without a generator change.", "Display Order": 4}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7015, "Name": "ICD Indication", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate the ICD Device indication as documented by the provider. ", "Target Value": "Any occurrence on current procedure", "Short Name": "ICDIndication", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "432678004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "ICD Indication", "Code": "315233008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Primary prevention", "Selection Definition": "Primary Prevention is an indication for an ICD to prevent sudden cardiac death. It refers to use of ICDs in individuals who are at risk for but have not yet had an episode of sustained ventricular tachycardia, ventricular fibrillation, or resuscitated cardiac arrest.", "Display Order": 1}, {"Name": "ICD Indication", "Code": "315234002", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Secondary prevention", "Selection Definition": "Secondary prevention refers to an indication for ICD exclusively for patients who have survived one or more cardiac arrests or sustained ventricular tachycardia. Patients with cardiac conditions associated with a high risk of sudden death who have unexplained syncope that is likely to be due to ventricular arrhythmias are considered to have a secondary indication.", "Display Order": 2}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator change"}, {"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Initial generator implant"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}]}, {"Element Reference": 7600, "Name": "Generator Operator Last Name", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate the last name of the operator who is implanting the device.\n\nNote(s):\nIf more than one operator is involved, only code the primary operator.\n\nIf the name exceeds 50 characters, enter the first 50 letters only.", "Target Value": "The value on current procedure", "Short Name": "GenOpLName", "Data Type": "LN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001853", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator change"}, {"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator explant"}, {"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Initial generator implant"}]}, {"Element Reference": 7605, "Name": "Generator Operator First Name", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate the first name of the operator who is implanting the device.\n\nNote(s):\nIf the name exceeds 50 characters, enter the first 50 letters only.", "Target Value": "The value on current procedure", "Short Name": "GenOpFName", "Data Type": "FN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001853", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator change"}, {"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator explant"}, {"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Initial generator implant"}]}, {"Element Reference": 7610, "Name": "Generator Operator Middle Name", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate the middle name of the operator who is implanting the device.\n\nNote(s):\nIt is acceptable to specify the middle initial.\n\nIf there is no middle name given, leave field blank.\n\nIf there are multiple middle names, enter all of the middle names sequentially.\n\nIf the name exceeds 50 characters, enter the first 50 letters only.", "Target Value": "The value on current procedure", "Short Name": "GenOpMName", "Data Type": "MN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001853", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator change"}, {"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator explant"}, {"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Initial generator implant"}]}, {"Element Reference": 7615, "Name": "Generator Operator NPI", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate the National Provider Identifier (NPI) of the operator who is implanting the device. NPI's, assigned by the Centers for Medicare and Medicaid Services (CMS), are used to uniquely identify physicians for Medicare billing purposes.", "Target Value": "The value on current procedure", "Short Name": "GenOpNPI", "Data Type": "NUM", "Precision": "10", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.4.6", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator change"}, {"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator explant"}, {"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Initial generator implant"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Procedure Information", "Section Display Name": "Fellow Information", "Section Code": "FELLOW", "Section Type": "Repeater Section", "Cardinality": "0..n", "Table": "FELLOW", "Elements": [{"Element Reference": 15433, "Name": "Fellow Last Name", "Section Display Name": "Fellow Information", "Coding Instructions": "Indicate the last name of the Fellow-in-Training operator.\n\nNote(s):\nIf the name exceeds 50 characters, enter the first 50 characters only.", "Target Value": "The value on current procedure", "Short Name": "FIT_LastName", "Data Type": "LN", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000003534", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator change"}, {"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator explant"}, {"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Initial generator implant"}]}, {"Element Reference": 15434, "Name": "Fellow First Name", "Section Display Name": "Fellow Information", "Coding Instructions": "Indicate the first name of the Fellow-in-Training operator.\n\nNote(s):\nIf the name exceeds 50 characters, enter the first 50 characters only.", "Target Value": "The value on current procedure", "Short Name": "FIT_FirstName", "Data Type": "FN", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000003534", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator change"}, {"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator explant"}, {"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Initial generator implant"}]}, {"Element Reference": 15435, "Name": "Fellow Middle Name", "Section Display Name": "Fellow Information", "Coding Instructions": "Indicate the middle name of the Fellow-in-Training operator.\n\nNote(s):\nIf the name exceeds 50 characters, enter the first 50 characters only.", "Target Value": "The value on current procedure", "Short Name": "FIT_MidName", "Data Type": "MN", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000003534", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator change"}, {"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator explant"}, {"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Initial generator implant"}]}, {"Element Reference": 15436, "Name": "Fellow NPI", "Section Display Name": "Fellow Information", "Coding Instructions": "Indicate the National Provider Identifier (NPI) of the Fellow-in-Training operator who is performing the procedure. NPI's, assigned by the Centers for Medicare and Medicaid Services (CMS), are used to uniquely identify physicians for Medicare billing purposes.", "Target Value": "The value on current procedure", "Short Name": "FIT_NPI", "Data Type": "NUM", "Precision": "10", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000003534", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Fellow <PERSON><PERSON> (15436) must only be entered/selected once.", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator change"}, {"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator explant"}, {"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Initial generator implant"}]}, {"Element Reference": 15431, "Name": "Fellowship Program Identification Number", "Section Display Name": "Fellow Information", "Coding Instructions": "Indicate the institution's Accreditation Council for Graduate Medical Education (ACGME) number for the program in which the Fellow is participating.", "Target Value": "The value on current procedure", "Short Name": "FITProgID", "Data Type": "ST", "Precision": "15", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "224873004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Fellowship Program Identification Number", "Definition": "The institution's Accreditation Council for Graduate Medical Education (ACGME) number for the program in which the Fellow is participating.\n\nACGME oversees the accreditation of fellowship programs in the US. Each accredited training program is assigned a program ID.", "Source": "A list of programs by specialty can be found here: ACGME - Accreditation Data System (ADS): https://apps.acgme.org/ads/Public/Reports/Report/1 ."}, "Parent Child Validations": [{"Parent Element Reference": 15436, "Parent Element Name": "Fellow NPI", "Parent Element Selection Name": "Any Value"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Procedure Information", "Section Display Name": "Shared Decision Making", "Section Code": "SDM", "Section Type": "Section", "Cardinality": "0..1", "Table": "PROCINFO", "Elements": [{"Element Reference": 14732, "Name": "Shared Decision Making", "Section Display Name": "Shared Decision Making", "Coding Instructions": "Indicate if Shared Decision Making (SDM) was performed for the procedure.\n\nA statement by the Provider that a SDM encounter occurred, use of a Smart phrase pertaining to SDM within the facility’s EHR system, or use of a SDM Tool are all sufficient for coding \"Yes\". ", "Target Value": "The value on current procedure", "Short Name": "SDM_Proc", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "NULL", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000002041", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Shared Decision Making", "Definition": "Shared decision-making is when patients and clinicians work as a team to make care decisions. The provider offers various options and describes their risks and benefits, and the patient expresses his or her preferences and values. Tools can help facilitate a collaborative process between providers and patients and can:\n - Increase knowledge and satisfaction regarding care\n - Define clearer goals for treatment\n - Align health decisions with patient values\n\nInformed consent is not the same as shared decision-making.", "Source": null}, "Parent Child Validations": null}, {"Element Reference": 14733, "Name": "Shared Decision Making Tool Used", "Section Display Name": "Shared Decision Making", "Coding Instructions": "Indicate if a shared decision making tool was used.", "Target Value": "The value on current procedure", "Short Name": "SDM_Tool", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "NULL", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "415806002", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 14732, "Parent Element Name": "Shared Decision Making", "Parent Element Selection Name": "Yes"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}]}, {"Element Reference": 14734, "Name": "Shared Decision Making Tool Name", "Section Display Name": "Shared Decision Making", "Coding Instructions": "Indicate what tool was used. \nIf the tool used is not in the drop-down list, <NAME_EMAIL> to have a selection added.", "Target Value": "The value on current procedure", "Short Name": "SDM_Tool_Name", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "NULL", "Is Dynamic List": "Yes", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "405083000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Shared Decision Making Tool Name", "Code": "112000002028", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Colorado Shared Decision Making Tool", "Selection Definition": null, "Display Order": 1}, {"Name": "Shared Decision Making Tool Name", "Code": "100000351", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Other Shared Decision Making Tool", "Selection Definition": null, "Display Order": 2}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 14733, "Parent Element Name": "Shared Decision Making Tool Used", "Parent Element Selection Name": "Yes"}, {"Parent Element Reference": 15826, "Parent Element Name": "Electrophysiology Device Implant Pathway", "Parent Element Selection Name": "Implantable cardioverter-defibrillator"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Procedure Information", "Section Display Name": "Clinical Trial", "Section Code": "CLINICALTRIAL", "Section Type": "Section", "Cardinality": "0..1", "Table": "PROCINFO", "Elements": [{"Element Reference": 7020, "Name": "Premarket Clinical Trial", "Section Display Name": "Clinical Trial", "Coding Instructions": "Indicate if the Device or Lead procedure is part of a pre-market clinical trial(s), excluding post-market surveillance trial.", "Target Value": "Any occurrence on current procedure", "Short Name": "ClinicalTrial", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "428024001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 15786, "Name": "Post-market Surveillance", "Section Display Name": "Clinical Trial", "Coding Instructions": "Indicate if the ICD procedure (generator implant or lead procedure) or pacemaker procedure is part of post-market surveillance trial(s).", "Target Value": "Any occurrence on current procedure", "Short Name": "PostMarSur", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000003663", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Procedure Information", "Section Display Name": "Device Implant/Explant", "Section Code": "IMPLEXPL", "Section Type": "Section", "Cardinality": "0..1", "Table": "PROCINFO", "Elements": [{"Element Reference": 7620, "Name": "Device Implanted", "Section Display Name": "Device Implant/Explant", "Coding Instructions": "Indicate if a device was implanted.", "Target Value": "Any occurrence on current procedure", "Short Name": "DeviceImplanted", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "232965003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Initial generator implant"}, {"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator change"}]}, {"Element Reference": 15794, "Name": "Final Device Type", "Section Display Name": "Device Implant/Explant", "Coding Instructions": "Indicate the device type that was implanted at the completion of the procedure.", "Target Value": "Any occurrence on current procedure", "Short Name": "Final_Device_Type", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": null, "Is Dynamic List": "Yes", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "260846005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Final Device Type", "Code": "100001216", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "CRT-D", "Selection Definition": "A cardiac resynchronization therapy device and defibrillator (CRT-D) has dual capabilities. It is a biventricular pacemaker that sends electrical signals to both ventricles as well as a defibrillator. It may or may not have an atrial pacing wire.", "Display Order": 1}, {"Name": "Final Device Type", "Code": "112000003612", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extravascular ICD", "Selection Definition": "The extravascular (EV) ICD system has a lead (thin wire) is placed outside the heart and veins, under the sternum (breastbone).", "Display Order": 2}, {"Name": "Final Device Type", "Code": "100001215", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "ICD dual chamber", "Selection Definition": "A dual-chamber ICD defibrillates the ventricle and paces the atrium and ventricle.", "Display Order": 3}, {"Name": "Final Device Type", "Code": "100001214", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "ICD single chamber", "Selection Definition": "A single-chamber ICD defibrillates the ventricle and paces the ventricle.", "Display Order": 4}, {"Name": "Final Device Type", "Code": "100001217", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "S-ICD (Sub Q)", "Selection Definition": "A subcutaneous only defibrillator.", "Display Order": 5}, {"Name": "Final Device Type", "Code": "112000003680", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Single chamber transvenous permanent pacemaker", "Selection Definition": "A type of pacemaker that uses one transvenous lead to stimulate either the right atrium or right ventricle of the heart.", "Display Order": 6}, {"Name": "Final Device Type", "Code": "112000003679", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Dual chamber transvenous  permanent pacemaker", "Selection Definition": "A type of pacemaker that uses two transvenous leads to stimulate both the right atrium and right ventricle of the heart.", "Display Order": 7}, {"Name": "Final Device Type", "Code": "704708004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "CRT-P", "Selection Definition": "A CRT procedure is the placement of a biventricular pacemaker that sends electrical signals to both ventricles that resynchronizes the heart chambers and helps it pump more effectively. It may or may not have an atrial pacing wire.\n", "Display Order": 8}, {"Name": "Final Device Type", "Code": "112000002030", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Leadless single chamber permanent pacemaker", "Selection Definition": "A self-contained transvenous pacemaker generator and electrode system implanted directly into the right ventricle. The device is implanted via a femoral vein transcatheter approach; it requires no chest incision or subcutaneous generator pocket.", "Display Order": 9}, {"Name": "Final Device Type", "Code": "112000003671", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Leadless dual chamber permanent pacemaker", "Selection Definition": "A self-contained transvenous pacemaker generator and electrode system implanted directly into the right ventricle and right atrium. The device is implanted via a femoral vein transcatheter approach; it requires no chest incision or subcutaneous generator pocket.\n", "Display Order": 10}, {"Name": "Final Device Type", "Code": "112000003669", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "His bundle permanent pacemaker", "Selection Definition": "His-bundle pacing is a method for delivering permanent pacing. It produces physiological ventricular activation via the His-Purkinje system\n", "Display Order": 11}, {"Name": "Final Device Type", "Code": "112000003670", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Left bundle permanent pacemaker", "Selection Definition": "Left bundle pacing is a method for delivering permanent pacing. It produces physiological ventricular activation via the Left bundle.\n", "Display Order": 12}, {"Name": "Final Device Type", "Code": "112000003709", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Left ventricular endocardial pacemaker", "Selection Definition": "Left ventricular (LV) endocardial pacing is a treatment for patients with heart failure, severe LV dysfunction, and electrical dyssynchrony. It's an alternative therapy to cardiac resynchronization therapy (CRT) for patients who don't respond to conventional CRT or when it's not possible to place a lead through the coronary sinus.", "Display Order": 13}, {"Name": "Final Device Type", "Code": "467207002", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Cardiac Contractility Modulation", "Selection Definition": "A device-based therapy using electrical impulses to improve contractility and pumping function primarily for treatment of heart failure. ", "Display Order": 14}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7620, "Parent Element Name": "Device Implanted", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 7630, "Name": "Coronary Sinus/Left Ventricular (CS/LV) lead", "Section Display Name": "Device Implant/Explant", "Coding Instructions": "If an attempt was made to implant a coronary sinus/left ventricular (CS/LV) lead during the current procedure, indicate the results of the attempt.\n\nNote(s): When a guidewire or catheter is used to perform a venogram and it is determined there is an obstruction or the branches are not conducive to implanting the LV lead (and there is no further attempt to access the coronary sinus vein with the intent of implanting the left ventricular lead), code  \"Not Attempted\".", "Target Value": "Any occurrence on current procedure", "Short Name": "CSLVLead", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000985", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Coronary Sinus/Left Ventricular (CS/LV) lead", "Code": "100001143", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Implant unsuccessful", "Selection Definition": null, "Display Order": 1}, {"Name": "Coronary Sinus/Left Ventricular (CS/LV) lead", "Code": "100001084", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Previously implanted", "Selection Definition": null, "Display Order": 2}, {"Name": "Coronary Sinus/Left Ventricular (CS/LV) lead", "Code": "100001107", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Successfully implanted", "Selection Definition": null, "Display Order": 3}, {"Name": "Coronary Sinus/Left Ventricular (CS/LV) lead", "Code": "100001057", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not attempted", "Selection Definition": null, "Display Order": 4}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7620, "Parent Element Name": "Device Implanted", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 15827, "Name": "His Bundle Lead", "Section Display Name": "Device Implant/Explant", "Coding Instructions": "If an attempt was made to implant a His bundle lead during the current procedure, indicate the results of the attempt.\n", "Target Value": "The value on current procedure", "Short Name": "HisBunLead", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "345000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "His Bundle Lead", "Code": "100001143", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Implant unsuccessful", "Selection Definition": null, "Display Order": 1}, {"Name": "His Bundle Lead", "Code": "100001084", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Previously implanted", "Selection Definition": null, "Display Order": 2}, {"Name": "His Bundle Lead", "Code": "100001107", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Successfully implanted", "Selection Definition": null, "Display Order": 3}, {"Name": "His Bundle Lead", "Code": "100001057", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not attempted", "Selection Definition": null, "Display Order": 4}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7620, "Parent Element Name": "Device Implanted", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 15828, "Name": "Left Bundle Lead", "Section Display Name": "Device Implant/Explant", "Coding Instructions": "If an attempt was made to implant a Left bundle lead during the current procedure, indicate the results of the attempt.", "Target Value": "The value on current procedure", "Short Name": "LeftBunLead", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "74031005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Left Bundle Lead", "Code": "100001143", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Implant unsuccessful", "Selection Definition": null, "Display Order": 1}, {"Name": "Left Bundle Lead", "Code": "100001084", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Previously implanted", "Selection Definition": null, "Display Order": 2}, {"Name": "Left Bundle Lead", "Code": "100001107", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Successfully implanted", "Selection Definition": null, "Display Order": 3}, {"Name": "Left Bundle Lead", "Code": "100001057", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not attempted", "Selection Definition": null, "Display Order": 4}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7620, "Parent Element Name": "Device Implanted", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 15781, "Name": "Co-implant Device", "Section Display Name": "Device Implant/Explant", "Coding Instructions": "Indicate if additional devices were implanted in the body, regardless of their function.\n\n\nNote(s): Baseline co-implantation refers to the simultaneous implantation of additional cardiac devices to enhance the treatment efficacy. This approach aims to optimize cardiac function, manage arrhythmias, and improve overall patient outcomes through a more comprehensive therapeutic strategy. This may include combining wireless systems with existing pacing or defibrillation devices to enhance therapeutic effectiveness.", "Target Value": "The value on current procedure", "Short Name": "B<PERSON>oDev", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000003681", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7620, "Parent Element Name": "Device Implanted", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Device Implant/Explant", "Section Display Name": "Implant Device Information", "Section Code": "IMPLANTDEV", "Section Type": "Repeater Section", "Cardinality": "0..n", "Table": "IMPLANTDEV", "Elements": [{"Element Reference": 7635, "Name": "Implant Device ID", "Section Display Name": "Implant Device Information", "Coding Instructions": "Indicate the assigned identification number associated with the implanted device.\n\nNote(s):\nThe devices that should be collected in your application are controlled by a Defibrillator Device Master file. This file is maintained by the NCDR and will be made available on the internet for downloading and importing/updating into your application.", "Target Value": "Any occurrence on current procedure", "Short Name": "ICDImpID", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "Yes", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.6.1.21", "Code System": "2.16.840.1.113883.3.3478.6.1.21", "Code System Name": "ACC NCDR EP Devices", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7620, "Parent Element Name": "Device Implanted", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 7640, "Name": "Implant Device Serial Number", "Section Display Name": "Implant Device Information", "Coding Instructions": "Indicate the serial number of the device that was implanted.", "Target Value": "Any occurrence on current procedure", "Short Name": "ICDImpSerNo", "Data Type": "ST", "Precision": "30", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.850", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "An Implant Device Serial Number (7640) may only be entered/selected once\n\nWhen Implant Device Serial Number (7640) is answered, Implant Device ID (7635) cannot be Null", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7620, "Parent Element Name": "Device Implanted", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 7645, "Name": "Implant Unique Device Identifier", "Section Display Name": "Implant Device Information", "Coding Instructions": "Indicate the direct identifier portion of the Unique Device Identifier (UDI) associated with the device used for implant. This ID is provided by the device manufacturer, and is either a GTIN or HIBC number.", "Target Value": "Any occurrence on current procedure", "Short Name": "ICDImpUDI", "Data Type": "ST", "Precision": "150", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "No Action", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3719", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Unique Device Identifier (UDI)", "Definition": "An identifier that is the main (primary) lookup for a medical device product and meets the requirements to uniquely identify a device through its distribution and use. This value is supplied to the FDA by the manufacturer.", "Source": "US FDA"}, "Parent Child Validations": [{"Parent Element Reference": 7620, "Parent Element Name": "Device Implanted", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Device Implant/Explant", "Section Display Name": "Indications", "Section Code": "IND", "Section Type": "Section", "Cardinality": "0..1", "Table": "PROCINFO", "Elements": [{"Element Reference": 14730, "Name": "Bradycardia Indication Present", "Section Display Name": "Indications", "Coding Instructions": "Indicate if a bradycardia indication was also present.", "Target Value": "The value on current procedure", "Short Name": "BradIndPres", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "NULL", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000002042", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator change"}, {"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Initial generator implant"}]}, {"Element Reference": 14731, "Name": "Reason Pacing Indicated", "Section Display Name": "Indications", "Coding Instructions": "Select the reason pacing was indicated.\n\nNote(s): Code 'Chronotropic Incompetence' when pharmacological rate control is documented by the clinician. \n\nCode \"Complete Heart Block\" If a patient has symptomatic first- or second-degree heart block and no other indication.", "Target Value": "The value on current procedure", "Short Name": "ReasonPacIndic", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": "NULL", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001097", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Parent/Child Validation Notes: See Implant Device Types dynamic list. Enable the element when the element reference number is listed under the enableElements column applicable to the Final Device Type (15794) under the dynamic list.", "Selections": [{"Name": "Reason Pacing Indicated", "Code": "54016002", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "2:1 AV Block", "Selection Definition": "P-waves with a constant rate (or near constant rate because of ventriculophasic sinus arrhythmia) rate (<100 bpm) where every other P-wave conducts to the ventricles", "Display Order": 1}, {"Name": "Reason Pacing Indicated", "Code": "28189009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Mobitz Type II", "Selection Definition": "P-waves with a constant rate (< 100 bpm) with a periodic single non-conducted P-wave associated with other P-waves before and after the non-conducted P-wave with constant PR intervals (excluding 2:1 atrioventricular block)", "Display Order": 2}, {"Name": "Reason Pacing Indicated", "Code": "428663009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Atrioventricular Node Ablation", "Selection Definition": null, "Display Order": 3}, {"Name": "Reason Pacing Indicated", "Code": "100000931", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Anticipated requirement of > 40% RV pacing", "Selection Definition": null, "Display Order": 4}, {"Name": "Reason Pacing Indicated", "Code": "427989008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Chronotropic incompetence", "Selection Definition": "Broadly defined as the inability of the heart to increase its rate commensurate with increased activity or demand, in many studies translates to failure to attain 80% of expected heart rate reserve during exercise. ", "Display Order": 5}, {"Name": "Reason Pacing Indicated", "Code": "27885002", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Complete heart block", "Selection Definition": "No evidence of atrioventricular conduction.", "Display Order": 6}, {"Name": "Reason Pacing Indicated", "Code": "112000002017", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "HF unresponsive to GDMT", "Selection Definition": null, "Display Order": 7}, {"Name": "Reason Pacing Indicated", "Code": "36083008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Sick sinus syndrome", "Selection Definition": "Sick sinus syndrome or sinus node dysfunction must be symptomatic to code 14731 as 'Yes'. This includes sinus bradycardia, ectopic atrial bradycardia, sinoatrial exit block, sinus pause, sinus node arrest, and tachycardia-bradycardia syndrome; all of which must be symptomatic.\n\n", "Display Order": 8}, {"Name": "Reason Pacing Indicated", "Code": "100000351", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Other", "Selection Definition": null, "Display Order": 9}], "Ranges": null, "Definition": {"Title": "Reason Pacing Indicated", "Definition": "Refer to the source for the supporting definition.", "Source": "<PERSON>, Stainback RF, <PERSON>, et al. ACCF/HRS/AHA/ ASE/HFSA/SCAI/SCCT/SCMR 2013 appropriate use criteria for implantable cardioverter-defibrillators and cardiac resynchronization therapy. J Am Coll Cardiol 2013;61:1318–68. doi: 10.1016/j.jacc.2012.12.017"}, "Parent Child Validations": [{"Parent Element Reference": 15794, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "CRT-D"}, {"Parent Element Reference": 15794, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "ICD dual chamber"}, {"Parent Element Reference": 15794, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "Single chamber transvenous permanent pacemaker"}, {"Parent Element Reference": 15794, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "Dual chamber transvenous  permanent pacemaker"}, {"Parent Element Reference": 15794, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "Leadless single chamber permanent pacemaker"}, {"Parent Element Reference": 15794, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "Leadless dual chamber permanent pacemaker"}, {"Parent Element Reference": 15794, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "CRT-P"}, {"Parent Element Reference": 15794, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "His bundle permanent pacemaker"}, {"Parent Element Reference": 15794, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "Left bundle permanent pacemaker"}, {"Parent Element Reference": 15794, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "Left ventricular endocardial pacemaker"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Device Implant/Explant", "Section Display Name": "Generator Removal", "Section Code": "GENRMVL", "Section Type": "Section", "Cardinality": "0..1", "Table": "PROCINFO", "Elements": [{"Element Reference": 7650, "Name": "Reason(s) for Generator Replacement", "Section Display Name": "Generator Removal", "Coding Instructions": "Indicate the reason(s) for the replacement.", "Target Value": "Any occurrence on current procedure", "Short Name": "ReImplantReason", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000991", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Reason(s) for Generator Replacement", "Code": "100001087", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Device relocation", "Selection Definition": null, "Display Order": 1}, {"Name": "Reason(s) for Generator Replacement", "Code": "100001088", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "End of expected battery life", "Selection Definition": null, "Display Order": 2}, {"Name": "Reason(s) for Generator Replacement", "Code": "100001089", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Faulty connector/header", "Selection Definition": null, "Display Order": 3}, {"Name": "Reason(s) for Generator Replacement", "Code": "100001091", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Infection", "Selection Definition": null, "Display Order": 4}, {"Name": "Reason(s) for Generator Replacement", "Code": "100001090", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Malfunction", "Selection Definition": null, "Display Order": 5}, {"Name": "Reason(s) for Generator Replacement", "Code": "100001092", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Replaced at time of lead revision", "Selection Definition": null, "Display Order": 6}, {"Name": "Reason(s) for Generator Replacement", "Code": "100001093", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Under manufacturer advisory/recall", "Selection Definition": null, "Display Order": 7}, {"Name": "Reason(s) for Generator Replacement", "Code": "100001094", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Upgrade", "Selection Definition": null, "Display Order": 8}, {"Name": "Reason(s) for Generator Replacement", "Code": "112000003710", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Other", "Selection Definition": null, "Display Order": 9}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator change"}]}, {"Element Reference": 7660, "Name": "Device Explanted", "Section Display Name": "Generator Removal", "Coding Instructions": "Indicate if the previous device was explanted.", "Target Value": "Any occurrence between previous device implant and current procedure", "Short Name": "DeviceExplant", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "233171004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Device Explanted", "Code": "100001140", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not explanted", "Selection Definition": null, "Display Order": 1}, {"Name": "Device Explanted", "Code": "100001141", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Explanted", "Selection Definition": null, "Display Order": 2}, {"Name": "Device Explanted", "Code": "100001083", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Previously explanted", "Selection Definition": null, "Display Order": 3}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator explant"}, {"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator change"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Generator Removal", "Section Display Name": "Explant Device Information", "Section Code": "EXPLANTDEV", "Section Type": "Repeater Section", "Cardinality": "0..n", "Table": "EXPLANTDEV", "Elements": [{"Element Reference": 7675, "Name": "Explant Device ID", "Section Display Name": "Explant Device Information", "Coding Instructions": "Indicate the assigned identification number associated with the explanted device.\n\nNote(s):\nThe devices that should be collected in your application are controlled by a Defibrillator Device Master file. This file is maintained by the NCDR and will be made available on the internet for downloading and importing/updating into your application.", "Target Value": "Any occurrence between previous device implant and current procedure", "Short Name": "ICDExpID", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "Yes", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.6.1.21", "Code System": "2.16.840.1.113883.3.3478.6.1.21", "Code System Name": "ACC NCDR EP Devices", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7660, "Parent Element Name": "Device Explanted", "Parent Element Selection Name": "Explanted"}]}, {"Element Reference": 7680, "Name": "Explant Device Serial Number", "Section Display Name": "Explant Device Information", "Coding Instructions": "Indicate the serial number of the explanted device.", "Target Value": "Any occurrence between previous device implant and current procedure", "Short Name": "ICDExpSerNo", "Data Type": "ST", "Precision": "30", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.850", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "When Explant Device Serial Number (7680) is answered, Explant Device ID (7675) cannot be Null\n\nAn Explant Device Serial Number (7680) may only be entered/selected once", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7660, "Parent Element Name": "Device Explanted", "Parent Element Selection Name": "Explanted"}]}, {"Element Reference": 7685, "Name": "Explant Unique Device Identifier", "Section Display Name": "Explant Device Information", "Coding Instructions": "Indicate the direct identifier portion of the Unique Device Identifier (UDI) associated with the device used for explant. This ID is provided by the device manufacturer, and is either a GTIN or HIBC number.", "Target Value": "Any occurrence on current procedure", "Short Name": "ICDExplantUDI", "Data Type": "ST", "Precision": "150", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "No Action", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3719", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Unique Device Identifier (UDI)", "Definition": "An identifier that is the main (primary) lookup for a medical device product and meets the requirements to uniquely identify a device through its distribution and use. This value is supplied to the FDA by the manufacturer.", "Source": "US FDA"}, "Parent Child Validations": [{"Parent Element Reference": 7660, "Parent Element Name": "Device Explanted", "Parent Element Selection Name": "Explanted"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Generator Removal", "Section Display Name": "Generator Removal", "Section Code": "GENRMVL2", "Section Type": "Section", "Cardinality": "0..1", "Table": "PROCINFO", "Elements": [{"Element Reference": 7670, "Name": "Explant Treatment Recommendation", "Section Display Name": "Generator Removal", "Coding Instructions": "Indicate the planned treatment post explant of the device at the time of the current procedure.", "Target Value": "Any occurrence on current procedure", "Short Name": "ExplantTreatment", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001003", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Explant Treatment Recommendation", "Code": "100000995", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Downgrade", "Selection Definition": "The ICD/CRT-D device has been explanted with re-implant of a device with only pacing and no defibrillation capabilities during the current procedure.", "Display Order": 1}, {"Name": "Explant Treatment Recommendation", "Code": "100001049", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "No Re-implant", "Selection Definition": "The device has been explanted with no re-implant of any device with pacing or defibrillation capabilities during the current procedure.", "Display Order": 2}, {"Name": "Explant Treatment Recommendation", "Code": "112000003672", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Upgrade", "Selection Definition": "The ICD/CRT-D/pacemaker device has been explanted, and a new device with additional or enhanced capabilities, has been implanted during the current procedure.", "Display Order": 3}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator explant"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Procedure Information", "Section Display Name": "Lead Assessment", "Section Code": "LEADASSESSMENT", "Section Type": "Section", "Cardinality": "0..1", "Table": "PROCINFO", "Elements": [{"Element Reference": 7690, "Name": "Lead Operator Last Name", "Section Display Name": "Lead Assessment", "Coding Instructions": "Indicate the last name of the operator who is performing the lead procedure.\n\nNote(s):\nIf the name exceeds 50 characters, enter the first 50 letters only.\n\nIf more than one physician performs the lead procedure, code the\noperator of record.", "Target Value": "The value on current procedure", "Short Name": "LeadOpLName", "Data Type": "LN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001853", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7695, "Name": "Lead Operator First Name", "Section Display Name": "Lead Assessment", "Coding Instructions": "Indicate the first name of the operator who is performing the lead procedure.\n\nNote(s):\nIf the name exceeds 50 characters, enter the first 50 letters only.\n\nIf more than one physician performs the lead procedure, code the\noperator of record.", "Target Value": "The value on current procedure", "Short Name": "LeadOpFName", "Data Type": "FN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001853", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7700, "Name": "Lead Operator Middle Name", "Section Display Name": "Lead Assessment", "Coding Instructions": "Indicate the middle name of the operator who is performing the lead procedure.\n\nNote(s):\nIt is acceptable to specify the middle initial.\n\nIf there is no middle name given, leave field blank.\n\nIf there are multiple middle names, enter all of the middle names sequentially.\n\nIf the name exceeds 50 characters, enter the first 50 letters only.", "Target Value": "The value on current procedure", "Short Name": "LeadOpMName", "Data Type": "MN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001853", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7705, "Name": "Lead Operator NPI", "Section Display Name": "Lead Assessment", "Coding Instructions": "Indicate the National Provider Identifier (NPI) of the operator who is performing the lead procedure. NPI's, assigned by the Centers for Medicare and Medicaid Services (CMS), are used to uniquely identify physicians for Medicare billing purposes.", "Target Value": "The value on current procedure", "Short Name": "LeadOpNPI", "Data Type": "NUM", "Precision": "10", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.4.6", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Lead Assessment", "Section Display Name": "Leads", "Section Code": "LEADS", "Section Type": "Repeater Section", "Cardinality": "0..n", "Table": "LEADS", "Elements": [{"Element Reference": 7710, "Name": "Lead Counter", "Section Display Name": "Leads", "Coding Instructions": "The software-assigned lead counter should start at one and be incremented by one for each new or existing lead documented.", "Target Value": "N/A", "Short Name": "LeadCounter", "Data Type": "CTR", "Precision": "2", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001858", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": null, "Usual Range Min": null, "Usual Range Max": null, "Valid Range Min": 1.0, "Valid Range Max": "99"}], "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7715, "Name": "Lead Identification", "Section Display Name": "Leads", "Coding Instructions": "Indicate if the lead is a new or existing lead.  All new leads placed or existing leads extracted, abandoned, or reused should be identified in the leads section.\n\nNote(s):\nIf a lead was attempted, but not actually implanted, do not include it. For example, if a lead turns out to be too short, or with inadequate coil spacing, or is too large/unstable for the coronary sinus branch vein, do not include it in the registry.", "Target Value": "The value on current procedure", "Short Name": "LeadType", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000990", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Lead Identification", "Code": "100001047", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "New   ", "Selection Definition": "A lead that is implanted for the first time.", "Display Order": 1}, {"Name": "Lead Identification", "Code": "100001001", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Existing", "Selection Definition": "A lead that has been previously implanted.", "Display Order": 2}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7710, "Parent Element Name": "Lead Counter", "Parent Element Selection Name": "Any Value"}]}, {"Element Reference": 7740, "Name": "Existing Lead Implant Date", "Section Display Name": "Leads", "Coding Instructions": "Indicate the date the existing lead was initially implanted.\n\nNote(s):\nIf the month or day of the implant is unknown, please code 01/01/YYYY. If the specific year is unknown in the current record, the year may be estimated based on timeframes found in prior medical record documentation (Example: If the patient had a lead implant documented in a record from 2011, then the year 2011 can be utilized and coded as 01/01/2011).", "Target Value": "The last value between birth and current procedure", "Short Name": "ExLeadDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001015", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Existing Lead Implant Date (7740) must be Less than or Equal to Procedure Start Date and Time (7000)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7715, "Parent Element Name": "Lead Identification", "Parent Element Selection Name": "Existing"}]}, {"Element Reference": 7745, "Name": "Existing Lead Status", "Section Display Name": "Leads", "Coding Instructions": "Indicate the status of the existing lead.", "Target Value": "Any occurrence on current procedure", "Short Name": "ExLeadStat", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000989", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Existing Lead Status", "Code": "100001004", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extracted", "Selection Definition": "The existing lead was extracted in whole or part and removed.", "Display Order": 1}, {"Name": "Existing Lead Status", "Code": "100000925", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Abandoned", "Selection Definition": "The existing lead was left in situ, abandoned and not reused.", "Display Order": 2}, {"Name": "Existing Lead Status", "Code": "100001099", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Reused", "Selection Definition": "The existing lead was left in situ and reused.", "Display Order": 3}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7715, "Parent Element Name": "Lead Identification", "Parent Element Selection Name": "Existing"}]}, {"Element Reference": 7720, "Name": "Lead Identification Number", "Section Display Name": "Leads", "Coding Instructions": "Indicate the assigned identification for new or existing leads placed, reused, extracted or abandoned during the procedure.\n\nNote(s):\nThe lead devices that should be collected in your application are controlled by a Leads Device Master file. This file is maintained by the NCDR and will be made available on the internet for downloading and importing/updating into your application.", "Target Value": "The value on current procedure", "Short Name": "LeadID", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "Yes", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.6.1.20", "Code System": "2.16.840.1.113883.3.3478.6.1.20", "Code System Name": "ACC NCDR Lead Devices", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7710, "Parent Element Name": "Lead Counter", "Parent Element Selection Name": "Any Value"}]}, {"Element Reference": 7725, "Name": "Lead Serial Number", "Section Display Name": "Leads", "Coding Instructions": "Indicate the manufacturer's serial number of the lead.", "Target Value": "The value on current procedure", "Short Name": "LeadSerNo", "Data Type": "ST", "Precision": "30", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.850", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "A Lead Serial Number (7725) may only be entered/selected once\n\nWhen Lead Serial Number (7725) is answered, Lead Identification Number (7720) cannot be Null", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7710, "Parent Element Name": "Lead Counter", "Parent Element Selection Name": "Any Value"}]}, {"Element Reference": 7730, "Name": "Lead Unique Device Identifier", "Section Display Name": "Leads", "Coding Instructions": "Indicate the direct identifier portion of the Unique Device Identifier (UDI) associated with the device used for implant. This ID is provided by the device manufacturer, and is either a GTIN or HIBC number.", "Target Value": "The value on current procedure", "Short Name": "LeadUDI", "Data Type": "ST", "Precision": "150", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "No Action", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3719", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Unique Device Identifier (UDI)", "Definition": "An identifier that is the main (primary) lookup for a medical device product and meets the requirements to uniquely identify a device through its distribution and use. This value is supplied to the FDA by the manufacturer.", "Source": "US FDA"}, "Parent Child Validations": [{"Parent Element Reference": 7710, "Parent Element Name": "Lead Counter", "Parent Element Selection Name": "Any Value"}]}, {"Element Reference": 7735, "Name": "Lead Location", "Section Display Name": "Leads", "Coding Instructions": "Indicate the location of the lead.", "Target Value": "Any occurrence on current procedure", "Short Name": "LeadLocation", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001246", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Lead Location", "Code": "72107004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Azygos vein", "Selection Definition": "A pacing or defibrillating lead placed in a vein (azygos) on the right side at the back of the thorax.", "Display Order": 1}, {"Name": "Lead Location", "Code": "345000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "His bundle", "Selection Definition": "A pacing or defibrillating lead placed at the location of the His bundle.", "Display Order": 2}, {"Name": "Lead Location", "Code": "74031005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Left bundle", "Selection Definition": "A pacing or defibrillating lead placed at the location of the left bundle.", "Display Order": 3}, {"Name": "Lead Location", "Code": "112000003605", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "LV endocardial", "Selection Definition": "A pacing or defibrillating lead placed onto the left ventricular endocardium.", "Display Order": 4}, {"Name": "Lead Location", "Code": "100001136", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "LV epicardial (CVS)", "Selection Definition": "A pacing or defibrillating lead placed transvenously onto the left ventricle through the coronary venous system.", "Display Order": 5}, {"Name": "Lead Location", "Code": "100001135", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "LV epicardial (surgical)", "Selection Definition": "A pacing or defibrillation lead placed transthoracically onto the left ventricular epicardium.", "Display Order": 6}, {"Name": "Lead Location", "Code": "3194006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "RA endocardial", "Selection Definition": "A pacing lead placed transvenously into the right atrial endocardium.", "Display Order": 7}, {"Name": "Lead Location", "Code": "112000002026", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "RA epicardial", "Selection Definition": "A pacing or defibrillating lead placed on the outside of the cardiac muscle onto right atrium", "Display Order": 8}, {"Name": "Lead Location", "Code": "304059001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "RV endocardial", "Selection Definition": "A pacing or defibrillation lead placed transvenously into the right ventricular endocardium.", "Display Order": 9}, {"Name": "Lead Location", "Code": "112000002027", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "RV epicardial", "Selection Definition": "A pacing or defibrillating lead placed on the outside of the cardiac muscle onto right ventricle.", "Display Order": 10}, {"Name": "Lead Location", "Code": "100001106", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Subcutaneous array", "Selection Definition": "A defibrillation electrode that is placed subcutaneously.", "Display Order": 11}, {"Name": "Lead Location", "Code": "100001138", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Subcutaneous ICD", "Selection Definition": "A defibrillation lead placed subcutaneously.", "Display Order": 12}, {"Name": "Lead Location", "Code": "33547000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Substernal", "Selection Definition": "A pacing or defibrillating lead placed under the sternum.", "Display Order": 13}, {"Name": "Lead Location", "Code": "100001137", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Superior Vena Cava/subclavian", "Selection Definition": "A defibrillating lead placed in the superior vena cava or subclavian vein.", "Display Order": 14}, {"Name": "Lead Location", "Code": "100001066", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Other Lead location", "Selection Definition": "A lead placed in a location not specified above.", "Display Order": 15}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7710, "Parent Element Name": "Lead Counter", "Parent Element Selection Name": "Any Value"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Procedure Information", "Section Display Name": "Intra or Post-Proc Events", "Section Code": "INTPOSTEVENT", "Section Type": "Section", "Cardinality": "0..1", "Table": "PROCINFO", "Elements": null}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Intra or Post-Procedure Events", "Section Display Name": "Intra or Post-Procedure Events", "Section Code": "IPPEVENTS", "Section Type": "Repeater Section", "Cardinality": "0..n", "Table": "IPPEVENTS", "Elements": [{"Element Reference": 9001, "Name": "Intra/Post-Procedure Events", "Section Display Name": "Intra or Post-Procedure Events", "Coding Instructions": "Indicate the event that occurred between the start of the procedure and the next procedure or discharge.\n\n\n", "Target Value": "Any occurrence between start of procedure and until next procedure or discharge", "Short Name": "PostProcEvent", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "Yes", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Intra/Post-Procedure Events (9001) should not be duplicated in a lab visit", "Selections": [{"Name": "Intra/Post-Procedure Events", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Bleeding - Access Site", "Selection Definition": "Indicate if the patient experienced a bleeding event at the percutaneous access site observed and documented in the medical record that was associated with any of the following:\n1. Hemoglobin drop of >=3 g/dL; \n2. Transfusion of whole blood or packed red blood cells; \n3. Procedural intervention/surgery at the bleeding site to reverse/stop or correct the bleeding (such as surgical closure/exploration of the arteriotomy site, balloon angioplasty to seal an arterial tear).\nDo not include bleeding at the site of the generator implant/explant.\n", "Display Order": 1}, {"Name": "Intra/Post-Procedure Events", "Code": "74474003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Bleeding - Gastrointestinal", "Selection Definition": "Indicate if the patient experienced a gastrointestinal bleeding event observed and documented in the medical record that was associated with any of the following: \n1. Hemoglobin drop of >=3 g/dL; \n2. Transfusion of whole blood or packed red blood cells; \n3. Procedural intervention/surgery at the bleeding site to reverse/stop or correct the bleeding (such as surgical closure or endoscopy with cautery of a GI bleed).\n", "Display Order": 2}, {"Name": "Intra/Post-Procedure Events", "Code": "95549001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Bleeding - Retroperitoneal", "Selection Definition": "Indicate if the patient experienced a retroperioneal bleeding event observed and documented in the medical record that was associated with any of the following: \n1. Hemoglobin drop of >=3 g/dL; \n2. Transfusion of whole blood or packed red blood cells; \n3. Procedural intervention/surgery at the bleeding site to reverse/stop or correct the bleeding (such as surgical closures/exploration of the arteriotomy site, balloon angioplasty to seal an arterial tear).", "Display Order": 3}, {"Name": "Intra/Post-Procedure Events", "Code": "385494008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Hematoma (Re-op, evac, or transfusion)", "Selection Definition": "Indicate if there is documentation that the patient experienced a pocket hematoma at the incision site requiring a reoperation, evacuation or transfusion.\n", "Display Order": 4}, {"Name": "Intra/Post-Procedure Events", "Code": "5447007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Transfusion", "Selection Definition": "Indicate if there is documentation that patient received a transfusion of whole or packed red blood cells.\n", "Display Order": 5}, {"Name": "Intra/Post-Procedure Events", "Code": "213217008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Vascular complications", "Selection Definition": "Indicate if there is documentation that the patient experienced a vascular complication attributable to the current procedure that required an intervention. \n\nVascular complications can include, but are not limited to: access site occlusions, peripheral embolizations, dissections, pseudoaneurysms and/or AV fistulas. Any noted vascular complication must have had an intervention such as a fibrin injection, angioplasty, or surgical repair to qualify. Prolonged pressure does not qualify as an intervention, but ultrasonic guided compression after making a diagnosis of pseudoaneurysm does qualify. A retroperitoneal bleed or access site bleed is not captured in this event. \n\n\nA  retroperitoneal bleed or access site bleed or hematoma requiring transfusion is not a vascular complication under this data element.", "Display Order": 6}, {"Name": "Intra/Post-Procedure Events", "Code": "410429000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Cardiac arrest", "Selection Definition": "Indicate if the patient experienced cardiac arrest. Cardiac arrest is the cessation of cardiac activity. The patient becomes unresponsive with no normal breathing and no signs of circulation. If corrective measures are not taken rapidly, this condition progresses to sudden death. Cardiac arrest should be used to signify an event as described above that is reversed, usually by CPR and/or defibrillation or cardioversion or cardiac pacing.", "Display Order": 7}, {"Name": "Intra/Post-Procedure Events", "Code": "36191001:123005000=302509004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Cardiac perforation", "Selection Definition": "Indicate if there is angiographic or clinical evidence of a new cardiac perforation due to forward movement of pacing or defibrillator leads.\n", "Display Order": 8}, {"Name": "Intra/Post-Procedure Events", "Code": "100000029", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Coronary venous dissection", "Selection Definition": "Indicate if there is documentation of coronary venous dissection, a tear of the coronary sinus endothelium with dissection into the coronary sinus wall (sometimes referred to as \"staining\" following contrast injection). It can be caused by the lead, guide, or guidewire. \n", "Display Order": 9}, {"Name": "Intra/Post-Procedure Events", "Code": "22298006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Myocardial infarction", "Selection Definition": "Indicate if the patient was diagnosed with a myocardial infarction. ", "Display Order": 10}, {"Name": "Intra/Post-Procedure Events", "Code": "64915003:260870009=103391001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Urgent cardiac surgery", "Selection Definition": "Indicate if there is documentation that the patient required an unplanned or emergent cardiac surgery.\n", "Display Order": 11}, {"Name": "Intra/Post-Procedure Events", "Code": "373945007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Pericardial effusion", "Selection Definition": "Indicate if there is documentation of pericardial fluid in the pericardial space", "Display Order": 12}, {"Name": "Intra/Post-Procedure Events", "Code": "35304003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Cardiac tamponade", "Selection Definition": "Indicate if there is documentation that the patient experienced cardiac tamponade, the presence of pericardial fluid in the pericardial space leading to hemodynamic instability and requiring unplanned or emergent intervention. \n", "Display Order": 13}, {"Name": "Intra/Post-Procedure Events", "Code": "100000977", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Stroke (Any)", "Selection Definition": "Indicate if the patient was diagnosed with a stroke (ischemic, hemorrhagic, or undetermined). ", "Display Order": 14}, {"Name": "Intra/Post-Procedure Events", "Code": "266257000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Transient ischemic attack (TIA)", "Selection Definition": "Indicate if the patient was diagnosed with a transient ischemic attack (TIA), a temporary episode of neurological disfunction.", "Display Order": 15}, {"Name": "Intra/Post-Procedure Events", "Code": "31892009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Hemothorax", "Selection Definition": "Indicate if there is documentation that the patient experienced hemothorax, any accumulation of blood in the thorax/pleural space.", "Display Order": 16}, {"Name": "Intra/Post-Procedure Events", "Code": "36118008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Pneumothorax", "Selection Definition": "Indicate if there is documentation that the patient experienced pneumothorax, air in the pleural space.", "Display Order": 17}, {"Name": "Intra/Post-Procedure Events", "Code": "100001017", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Infection requiring antibiotics", "Selection Definition": "Indicate if there is documentation that the patient experienced an infection related to the current device or lead procedure that required antibiotics during the episode of care.\n", "Display Order": 18}, {"Name": "Intra/Post-Procedure Events", "Code": "112000001324", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Device embolization", "Selection Definition": "Indicate if there is documentation that the patient experienced device embolization, the full dislodgement of a device from its original position that is then introduced to the circulatory system, potentially occluding blood supply to vessels and/or organs. \n", "Display Order": 19}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 9002, "Name": "Intra/Post-Procedure Events Occurred", "Section Display Name": "Intra or Post-Procedure Events", "Coding Instructions": "Indicate if the specific intra or post procedure event(s) occurred.", "Target Value": "Any occurrence between start of procedure and until next procedure or discharge", "Short Name": "PostProcOccurred", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "1000142479", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 9001, "Parent Element Name": "Intra/Post-Procedure Events", "Parent Element Selection Name": "Any Value"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Intra or Post-Procedure Events", "Section Display Name": "Intra or Post-Procedure Event Details", "Section Code": "IPPEVENTDET", "Section Type": "Section", "Cardinality": "0..1", "Table": "IPPEVENTS", "Elements": [{"Element Reference": 15784, "Name": "Vascular Complication Location", "Section Display Name": "Intra or Post-Procedure Event Details", "Coding Instructions": "Indicate the location or locations of the vascular complication", "Target Value": "Any occurrence between start of procedure and until next procedure or discharge", "Short Name": "VasComLoc", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001038", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Vascular Complication Location", "Code": "45048000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Neck", "Selection Definition": null, "Display Order": 1}, {"Name": "Vascular Complication Location", "Code": "51185008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Chest", "Selection Definition": null, "Display Order": 2}, {"Name": "Vascular Complication Location", "Code": "26893007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "<PERSON><PERSON><PERSON>", "Selection Definition": null, "Display Order": 3}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 9001, "Parent Element Name": "Intra/Post-Procedure Events", "Parent Element Selection Name": "Vascular complications"}, {"Parent Element Reference": 9002, "Parent Element Name": "Intra/Post-Procedure Events Occurred", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 15782, "Name": "Vascular Complication Intervention", "Section Display Name": "Intra or Post-Procedure Event Details", "Coding Instructions": "Indicate if the vascular complication that occurred required intervention. \n", "Target Value": "The value on current procedure", "Short Name": "VasComInt", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001034", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 9001, "Parent Element Name": "Intra/Post-Procedure Events", "Parent Element Selection Name": "Vascular complications"}, {"Parent Element Reference": 9002, "Parent Element Name": "Intra/Post-Procedure Events Occurred", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 15783, "Name": "Vascular Complication Intervention Type", "Section Display Name": "Intra or Post-Procedure Event Details", "Coding Instructions": "Indicate the intervention type.", "Target Value": "Any occurrence between start of procedure and until next procedure or discharge", "Short Name": "VasComIntTyp", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001034", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Vascular Complication Intervention Type", "Code": "112000003673", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Endovascular repair", "Selection Definition": null, "Display Order": 1}, {"Name": "Vascular Complication Intervention Type", "Code": "112000003674", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Surgical repair", "Selection Definition": null, "Display Order": 2}, {"Name": "Vascular Complication Intervention Type", "Code": "112000003675", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Thrombin injection", "Selection Definition": null, "Display Order": 3}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15782, "Parent Element Name": "Vascular Complication Intervention", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 9065, "Name": "Pericardial Effusion Requiring Intervention", "Section Display Name": "Intra or Post-Procedure Event Details", "Coding Instructions": "Indicate if the documented pericardial effusion required intervention, such as pericardiocentesis. \n", "Target Value": "Any occurrence between start of procedure and until next procedure or discharge", "Short Name": "PeriEffusionInterv", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001073", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Pericardial Effusion Requiring Intervention", "Definition": "Indicate if the patient had a pericardial effusion that required intervention of any kind. Code ‘no’ if the effusion was simply monitored.", "Source": null}, "Parent Child Validations": [{"Parent Element Reference": 9001, "Parent Element Name": "Intra/Post-Procedure Events", "Parent Element Selection Name": "Pericardial effusion"}, {"Parent Element Reference": 9002, "Parent Element Name": "Intra/Post-Procedure Events Occurred", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 15788, "Name": "Cardiac Tamponade Intervention Type", "Section Display Name": "Intra or Post-Procedure Event Details", "Coding Instructions": "Indicate if treatment for cardiac tamponade required percutaneous intervention (pericardiocentesis) and/or surgical intervention.", "Target Value": "Any occurrence between start of procedure and until next procedure or discharge", "Short Name": "TampIntTyp", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "35304003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Cardiac Tamponade Intervention Type", "Code": "64915003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Open cardiac surgery", "Selection Definition": null, "Display Order": 1}, {"Name": "Cardiac Tamponade Intervention Type", "Code": "122462000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Percutaneous drainage", "Selection Definition": null, "Display Order": 2}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 9001, "Parent Element Name": "Intra/Post-Procedure Events", "Parent Element Selection Name": "Cardiac tamponade"}, {"Parent Element Reference": 9002, "Parent Element Name": "Intra/Post-Procedure Events Occurred", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 9210, "Name": "Hemothorax Requiring Drainage", "Section Display Name": "Intra or Post-Procedure Event Details", "Coding Instructions": "Indicate if the patient was diagnosed with a hemothorax that required drainage.", "Target Value": "Any occurrence between start of procedure and until next procedure or discharge", "Short Name": "HemothoraxReqDrng", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001011", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 9001, "Parent Element Name": "Intra/Post-Procedure Events", "Parent Element Selection Name": "Hemothorax"}, {"Parent Element Reference": 9002, "Parent Element Name": "Intra/Post-Procedure Events Occurred", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 15789, "Name": "Pneumothorax Requiring Intervention", "Section Display Name": "Intra or Post-Procedure Event Details", "Coding Instructions": "Indicate if a pneumothorax occurred requiring an intervention (such as insertion of a chest tube) as documented by the provider. ", "Target Value": "The value on current procedure", "Short Name": "PneuReqInt", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000002152", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 9001, "Parent Element Name": "Intra/Post-Procedure Events", "Parent Element Selection Name": "Pneumothorax"}, {"Parent Element Reference": 9002, "Parent Element Name": "Intra/Post-Procedure Events Occurred", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Intra or Post-Procedure Events", "Section Display Name": "Post Procedure Events", "Section Code": "POSTPROCEVENTS", "Section Type": "Section", "Cardinality": "0..1", "Table": "IPPEVENTS", "Elements": [{"Element Reference": 9255, "Name": "<PERSON>rew Problem", "Section Display Name": "Post Procedure Events", "Coding Instructions": "Indicate if the patient had a pacing and/or sensing problem associated with high impedance due to a poor connection between a lead and device caused by a loose set screw.\n\nNote(s):\nIndicate if the patient experienced a set screw problem between completion of the pacemaker or ICD procedure until next the pacemaker or ICD procedure or discharge.", "Target Value": "Any occurrence between completion of the procedure and until next procedure or discharge", "Short Name": "SetScrew", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000038", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 9260, "Name": "Lead Dislodgement", "Section Display Name": "Post Procedure Events", "Coding Instructions": "Indicate if the patient experienced a lead dislodgement as documented by movement of a lead that requires repositioning and reoperation.", "Target Value": "Any occurrence between completion of the procedure and until next procedure or discharge", "Short Name": "LeadDislodge", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "234233007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 9265, "Name": "Lead Location (Dislodgement)", "Section Display Name": "Post Procedure Events", "Coding Instructions": "Select the first (or primary) lead identified as dislodged when more than one dislodgement is identified.", "Target Value": "Any occurrence between completion of the procedure and until next procedure or discharge", "Short Name": "LeadDislodgeLoc", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001246", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Lead Location (Dislodgement)", "Code": "72107004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Azygos vein", "Selection Definition": "A pacing or defibrillating lead placed in a vein (azygos) on the right side at the back of the thorax.", "Display Order": 1}, {"Name": "Lead Location (Dislodgement)", "Code": "345000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "His bundle", "Selection Definition": "A pacing or defibrillating lead placed at the location of the His bundle.", "Display Order": 2}, {"Name": "Lead Location (Dislodgement)", "Code": "74031005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Left bundle", "Selection Definition": "A pacing or defibrillating lead placed at the location of the left bundle.", "Display Order": 3}, {"Name": "Lead Location (Dislodgement)", "Code": "112000003605", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "LV endocardial", "Selection Definition": "A pacing or defibrillating lead placed onto the left ventricular endocardium.", "Display Order": 4}, {"Name": "Lead Location (Dislodgement)", "Code": "100001136", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "LV epicardial (CVS)", "Selection Definition": "A pacing or defibrillating lead placed transvenously onto the left ventricle through the coronary venous system.", "Display Order": 5}, {"Name": "Lead Location (Dislodgement)", "Code": "100001135", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "LV epicardial (surgical)", "Selection Definition": "A pacing or defibrillation lead placed transthoracically onto the left ventricular epicardium.", "Display Order": 6}, {"Name": "Lead Location (Dislodgement)", "Code": "3194006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "RA endocardial", "Selection Definition": "A pacing lead placed transvenously into the right atrial endocardium.", "Display Order": 7}, {"Name": "Lead Location (Dislodgement)", "Code": "112000002026", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "RA epicardial", "Selection Definition": "A pacing or defibrillating lead placed on the outside of the cardiac muscle onto right atrium", "Display Order": 8}, {"Name": "Lead Location (Dislodgement)", "Code": "304059001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "RV endocardial", "Selection Definition": "A pacing or defibrillation lead placed transvenously into the right ventricular endocardium.", "Display Order": 9}, {"Name": "Lead Location (Dislodgement)", "Code": "112000002027", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "RV epicardial", "Selection Definition": "A pacing or defibrillating lead placed on the outside of the cardiac muscle onto right ventricle.", "Display Order": 10}, {"Name": "Lead Location (Dislodgement)", "Code": "100001106", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Subcutaneous array", "Selection Definition": "A defibrillation electrode that is placed subcutaneously.", "Display Order": 11}, {"Name": "Lead Location (Dislodgement)", "Code": "100001138", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Subcutaneous ICD", "Selection Definition": "A defibrillation lead placed subcutaneously.", "Display Order": 12}, {"Name": "Lead Location (Dislodgement)", "Code": "33547000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Substernal", "Selection Definition": "A pacing or defibrillating lead placed under the sternum.", "Display Order": 13}, {"Name": "Lead Location (Dislodgement)", "Code": "100001137", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Superior Vena Cava/subclavian", "Selection Definition": "A defibrillating lead placed in the superior vena cava or subclavian vein.", "Display Order": 14}, {"Name": "Lead Location (Dislodgement)", "Code": "100001066", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Other Lead location", "Selection Definition": "A lead placed in a location not specified above.", "Display Order": 15}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 9260, "Parent Element Name": "Lead Dislodgement", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Procedure Information", "Section Display Name": "Conduction System Pacing", "Section Code": "CSP", "Section Type": "Section", "Cardinality": "0..1", "Table": "PROCINFO", "Elements": [{"Element Reference": 15790, "Name": "Final Paced QRS Duration", "Section Display Name": "Conduction System Pacing", "Coding Instructions": "Indicate the final paced QRS duration in milliseconds. Duration should be noted in provider notes or a device testing report and not abstracted solely based on ECG measurements without provider documentation.", "Target Value": "The value on current procedure", "Short Name": "FinPacedQRSDur", "Data Type": "PQ", "Precision": "3,0", "Unit Of Measure": "msec", "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001121", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "msec", "Usual Range Min": 20.0, "Usual Range Max": 250.0, "Valid Range Min": 10.0, "Valid Range Max": "300"}], "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15828, "Parent Element Name": "Left Bundle Lead", "Parent Element Selection Name": "Previously implanted"}, {"Parent Element Reference": 15828, "Parent Element Name": "Left Bundle Lead", "Parent Element Selection Name": "Successfully implanted"}, {"Parent Element Reference": 15829, "Parent Element Name": "Final Paced QRS Duration Not Assessed", "Parent Element Selection Name": "No (or Not Answered)"}]}, {"Element Reference": 15829, "Name": "Final Paced QRS Duration Not Assessed", "Section Display Name": "Conduction System Pacing", "Coding Instructions": "Indicate if the final paced QRS duration was not assessed or not documented.\n", "Target Value": "The value on current procedure", "Short Name": "FinPacedQRSDurNotAss", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001121", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15828, "Parent Element Name": "Left Bundle Lead", "Parent Element Selection Name": "Previously implanted"}, {"Parent Element Reference": 15828, "Parent Element Name": "Left Bundle Lead", "Parent Element Selection Name": "Successfully implanted"}]}, {"Element Reference": 15787, "Name": "Unipolar Paced QRS Morphology", "Section Display Name": "Conduction System Pacing", "Coding Instructions": "Indicate the unipolar paced QRS morphology as noted in lead V1. If bipolar pacing code 'No.' Unipolar paced QRS morphology is typically shown as tall R-waves preceded by small Q-complexes (qR-waves) or deep Q-waves followed by small R-complexes (Qr-waves).  Code based on provider documentation and not solely based on an ECG printout/scan. \n\n", "Target Value": "The value on current procedure", "Short Name": "UniPacQRSMorph", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000003668", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Unipolar Paced QRS Morphology", "Code": "100013073", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "No", "Selection Definition": null, "Display Order": 1}, {"Name": "Unipolar Paced QRS Morphology", "Code": "112000003676", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Yes - qR", "Selection Definition": null, "Display Order": 2}, {"Name": "Unipolar Paced QRS Morphology", "Code": "112000003677", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Yes - Qr", "Selection Definition": null, "Display Order": 3}, {"Name": "Unipolar Paced QRS Morphology", "Code": "112000003678", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Yes - Other", "Selection Definition": null, "Display Order": 4}, {"Name": "Unipolar Paced QRS Morphology", "Code": "112000001830", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not documented", "Selection Definition": null, "Display Order": 5}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15828, "Parent Element Name": "Left Bundle Lead", "Parent Element Selection Name": "Previously implanted"}, {"Parent Element Reference": 15828, "Parent Element Name": "Left Bundle Lead", "Parent Element Selection Name": "Successfully implanted"}]}, {"Element Reference": 15791, "Name": "R Wave Peak Time Duration", "Section Display Name": "Conduction System Pacing", "Coding Instructions": "Indicate R Wave Peak Time Duration (RWPT) in leads V5 -V6.  Code based on provider notes or a device testing report and not abstracted solely based on ECG measurements without provider documentation.  ", "Target Value": "The value on current procedure", "Short Name": "RwavPeakTimDur", "Data Type": "PQ", "Precision": "3,0", "Unit Of Measure": "msec", "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "426453001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "msec", "Usual Range Min": 20.0, "Usual Range Max": 250.0, "Valid Range Min": 10.0, "Valid Range Max": "300"}], "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15787, "Parent Element Name": "Unipolar Paced QRS Morphology", "Parent Element Selection Name": "Yes - Other"}, {"Parent Element Reference": 15787, "Parent Element Name": "Unipolar Paced QRS Morphology", "Parent Element Selection Name": "Yes - qR"}, {"Parent Element Reference": 15787, "Parent Element Name": "Unipolar Paced QRS Morphology", "Parent Element Selection Name": "Yes - Qr"}, {"Parent Element Reference": 15830, "Parent Element Name": "R Wave Peak Time Duration Not Assessed", "Parent Element Selection Name": "No (or Not Answered)"}]}, {"Element Reference": 15830, "Name": "R Wave Peak Time Duration Not Assessed", "Section Display Name": "Conduction System Pacing", "Coding Instructions": "Indicate whether R Wave Peak Time Duration (RWPT) was not assessed. ", "Target Value": "The value on current procedure", "Short Name": "RwavPeakTimDurNotAss", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "426453001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15787, "Parent Element Name": "Unipolar Paced QRS Morphology", "Parent Element Selection Name": "Yes - Other"}, {"Parent Element Reference": 15787, "Parent Element Name": "Unipolar Paced QRS Morphology", "Parent Element Selection Name": "Yes - qR"}, {"Parent Element Reference": 15787, "Parent Element Name": "Unipolar Paced QRS Morphology", "Parent Element Selection Name": "Yes - Qr"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "Discharge", "Section Code": "DISCHARGE", "Section Type": "Section", "Cardinality": "1..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 10005, "Name": "Coronary Artery Bypass Graft", "Section Display Name": "Discharge", "Coding Instructions": "Indicate if coronary artery bypass graft (CABG) Surgery was performed.", "Target Value": "Any occurrence between arrival and discharge", "Short Name": "CABG", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "232717009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 10010, "Name": "Coronary Artery Bypass Graft Date", "Section Display Name": "Discharge", "Coding Instructions": "Indicate the date of the coronary artery bypass graft (CABG) surgery.", "Target Value": "The first value between arrival and discharge", "Short Name": "CABGDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "232717009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "Coronary Artery Bypass Graft Date (10010) must be Greater than or Equal to Arrival Date (3000)\n\nCoronary Artery Bypass Graft Date (10010) must be Less than or Equal to Discharge Date (10100)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 10005, "Parent Element Name": "Coronary Artery Bypass Graft", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 10015, "Name": "Percutaneous Coronary Intervention", "Section Display Name": "Discharge", "Coding Instructions": "Indicate if the patient had a percutaneous coronary intervention (PCI).", "Target Value": "Any occurrence between arrival and discharge", "Short Name": "PCI", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "415070008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 10020, "Name": "Percutaneous Coronary Intervention Date", "Section Display Name": "Discharge", "Coding Instructions": "Indicate the date of the percutaneous coronary intervention (PCI) procedure.", "Target Value": "The first value between arrival and discharge", "Short Name": "PCIDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "415070008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "Percutaneous Coronary Intervention Date (10020) must be Less than or Equal to Discharge Date (10100)\n\nPercutaneous Coronary Intervention Date (10020) must be Greater than or Equal to Arrival Date (3000)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 10015, "Parent Element Name": "Percutaneous Coronary Intervention", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 10100, "Name": "Discharge Date", "Section Display Name": "Discharge", "Coding Instructions": "Indicate the date on which the patient was discharged from your facility.", "Target Value": "The value on discharge", "Short Name": "DCDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Discharge Date (10100) must be Greater than or Equal to 01/01/2025\n\nDischarge Date (10100) and Arrival Date (3000) must not overlap on multiple episodes", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 10105, "Name": "Discharge Status", "Section Display Name": "Discharge", "Coding Instructions": "Indicate whether the patient was alive or deceased at discharge.", "Target Value": "The value on discharge", "Short Name": "DCStatus", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "75527-2", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": [{"Name": "Discharge Status", "Code": "438949009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Alive", "Selection Definition": null, "Display Order": 1}, {"Name": "Discharge Status", "Code": "20", "Code System": "2.16.840.1.113883.12.112", "Code System Name": "HL7 Discharge disposition", "Selection Name": "Deceased", "Selection Definition": null, "Display Order": 2}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 10110, "Name": "Discharge Location", "Section Display Name": "Discharge", "Coding Instructions": "Indicate the location to which the patient was discharged.", "Target Value": "The value on discharge", "Short Name": "DCLocation", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "75528-0", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": [{"Name": "Discharge Location", "Code": "01", "Code System": "2.16.840.1.113883.12.112", "Code System Name": "HL7 Discharge disposition", "Selection Name": "Home", "Selection Definition": null, "Display Order": 1}, {"Name": "Discharge Location", "Code": "64", "Code System": "2.16.840.1.113883.12.112", "Code System Name": "HL7 Discharge disposition", "Selection Name": "Skilled nursing facility", "Selection Definition": null, "Display Order": 2}, {"Name": "Discharge Location", "Code": "62", "Code System": "2.16.840.1.113883.12.112", "Code System Name": "HL7 Discharge disposition", "Selection Name": "Extended care/transitional care unit/rehab", "Selection Definition": "Continued \"non-acute\" care at an extended care facility, transitional care unit, or rehabilitation unit.", "Display Order": 3}, {"Name": "Discharge Location", "Code": "100001249", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Other", "Selection Definition": null, "Display Order": 4}, {"Name": "Discharge Location", "Code": "02", "Code System": "2.16.840.1.113883.12.112", "Code System Name": "HL7 Discharge disposition", "Selection Name": "Other acute care hospital", "Selection Definition": null, "Display Order": 5}, {"Name": "Discharge Location", "Code": "07", "Code System": "2.16.840.1.113883.12.112", "Code System Name": "HL7 Discharge disposition", "Selection Name": "Left against medical advice (AMA)", "Selection Definition": "The patient was discharged or eloped against medical advice.", "Display Order": 6}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 10105, "Parent Element Name": "Discharge Status", "Parent Element Selection Name": "Alive"}]}, {"Element Reference": 10120, "Name": "Death During the Procedure", "Section Display Name": "Discharge", "Coding Instructions": "Indicate if the patient expired during the procedure.\n\nNote(s): Make sure to only capture 'death during the procedure' in the procedure appropriate registry. \n \nFor example, if the patient had a CathPCI procedure and a TVT procedure in the same episode of care (hospitalization) but different cath lab visits and the death occurred during the TVT procedure, code 'Yes' only in the TVT Registry and not the CathPCI Registry.  If the CathPCI procedure and TVT procedure occurred during the same cath lab visit then code 'Yes' in both registries.", "Target Value": "Any occurrence on discharge", "Short Name": "DeathProcedure", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000923", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 10105, "Parent Element Name": "Discharge Status", "Parent Element Selection Name": "Deceased"}]}, {"Element Reference": 10125, "Name": "Cause of Death", "Section Display Name": "Discharge", "Coding Instructions": "Indicate the primary cause of death, i.e. the first significant abnormal event which ultimately led to death.", "Target Value": "The value on time of death", "Short Name": "DeathCause", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "184305005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Cause of Death", "Code": "100014107", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Cardiac", "Selection Definition": null, "Display Order": 1}, {"Name": "Cause of Death", "Code": "112000000343", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Non-Cardiac", "Selection Definition": null, "Display Order": 2}, {"Name": "Cause of Death", "Code": "112000000342", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Undetermined", "Selection Definition": null, "Display Order": 3}], "Ranges": null, "Definition": {"Title": "Cause of Death", "Definition": "Death is classified into 1 of 3 categories: 1) cardiovascular death;  2) non - cardiovascular death; and 3) undetermined cause of death. \n\nThe intent of the classification schema is to identify one, and only one, of the categories as the underlying cause of death. The key priority is differentiating between cardiovascular and non-cardiovascular causes of death.", "Source": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, et al. 2014 ACC/AHA Key Data Elements and Definitions for Cardiovascular Endpoint Events in Clinical Trials: A Report of the American College of Cardiology/American Heart Association Task Force on Clinical Data Standards (Writing Committee to Develop Cardiovascular Endpoints Data Standards). J Am Coll Cardiol. 2015;():. Doi:10.1016/j.jacc.2014.12.018."}, "Parent Child Validations": [{"Parent Element Reference": 10105, "Parent Element Name": "Discharge Status", "Parent Element Selection Name": "Deceased"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Discharge", "Section Display Name": "Discharge Medications", "Section Code": "DischargeMeds", "Section Type": "Repeater Section", "Cardinality": "0..n", "Table": "DischargeMeds", "Elements": [{"Element Reference": 10200, "Name": "Discharge Medication Code", "Section Display Name": "Discharge Medications", "Coding Instructions": "Indicate the medications the patient was prescribed upon discharge. \n\nNote: Discharge medications are not required for patients who expired, were discharged to “Other acute care hospital,” or “Left against medical advice (AMA).”\n\nThe medication(s) collected in this field are controlled by the Medication Master file. This file is maintained by NCDR and will be made available for downloading and importing/updating into your application. Each medication in the Medication Master file is assigned to a value set.", "Target Value": "N/A", "Short Name": "DC_MedID", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "Yes", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100013057", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Discharge Medication Code (10200) must not be duplicated in an episode", "Selections": [{"Name": "Discharge Medication Code", "Code": "372603003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Aldosterone Antagonist", "Selection Definition": null, "Display Order": 1}, {"Name": "Discharge Medication Code", "Code": "41549009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Angiotensin Converting Enzyme Inhibitor", "Selection Definition": null, "Display Order": 2}, {"Name": "Discharge Medication Code", "Code": "112000001832", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Angiotensin Receptor-Neprilysin Inhibitor", "Selection Definition": null, "Display Order": 3}, {"Name": "Discharge Medication Code", "Code": "372913009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Angiotensin II Receptor Blocker", "Selection Definition": null, "Display Order": 4}, {"Name": "Discharge Medication Code", "Code": "426228001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Renin Inhibitor", "Selection Definition": null, "Display Order": 5}, {"Name": "Discharge Medication Code", "Code": "67507000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Antiarrhythmic Drug", "Selection Definition": null, "Display Order": 6}, {"Name": "Discharge Medication Code", "Code": "372560006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Antiplatelet Agent", "Selection Definition": null, "Display Order": 7}, {"Name": "Discharge Medication Code", "Code": "1191", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "<PERSON><PERSON><PERSON>", "Selection Definition": null, "Display Order": 8}, {"Name": "Discharge Medication Code", "Code": "1364430", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Apixaban", "Selection Definition": null, "Display Order": 9}, {"Name": "Discharge Medication Code", "Code": "33252009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Beta Blocker", "Selection Definition": null, "Display Order": 10}, {"Name": "Discharge Medication Code", "Code": "1927851", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Betrixaban", "Selection Definition": null, "Display Order": 11}, {"Name": "Discharge Medication Code", "Code": "1546356", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Dabigatran", "Selection Definition": null, "Display Order": 12}, {"Name": "Discharge Medication Code", "Code": "1599538", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Edoxaban", "Selection Definition": null, "Display Order": 13}, {"Name": "Discharge Medication Code", "Code": "1114195", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Rivaroxaban", "Selection Definition": null, "Display Order": 14}, {"Name": "Discharge Medication Code", "Code": "11289", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Warfarin", "Selection Definition": null, "Display Order": 15}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 10105, "Parent Element Name": "Discharge Status", "Parent Element Selection Name": "Alive"}, {"Parent Element Reference": 10110, "Parent Element Name": "Discharge Location", "Parent Element Selection Name": "Extended care/transitional care unit/rehab"}, {"Parent Element Reference": 10110, "Parent Element Name": "Discharge Location", "Parent Element Selection Name": "Home"}, {"Parent Element Reference": 10110, "Parent Element Name": "Discharge Location", "Parent Element Selection Name": "Other"}, {"Parent Element Reference": 10110, "Parent Element Name": "Discharge Location", "Parent Element Selection Name": "Skilled nursing facility"}]}, {"Element Reference": 10205, "Name": "Discharge Medication Prescribed", "Section Display Name": "Discharge Medications", "Coding Instructions": "Indicate if the medication was prescribed, not prescribed, or was not prescribed for either a medical or patient reason.\n\nNote(s): \nDischarge medications do not need to be recorded for patients who were discharged to \"Other acute care hospital\", or \"Left against medical advice (AMA)\". ", "Target Value": "The value on discharge", "Short Name": "DC_MedAdmin", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "432102000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "When Discharge Medication Code (10200) is answered, Discharge Medications Prescribed (10205) cannot be Null", "Selections": [{"Name": "Discharge Medication Prescribed", "Code": "100001247", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Yes", "Selection Definition": "Code 'Yes' if this medication was initiated (or prescribed) post procedure and for discharge.", "Display Order": 1}, {"Name": "Discharge Medication Prescribed", "Code": "100001048", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "No - No Reason", "Selection Definition": "Code 'No' if this medication was not prescribed post procedure or for discharge and there was no mention of a reason  why it was not ordered within the medical documentation.", "Display Order": 2}, {"Name": "Discharge Medication Prescribed", "Code": "100001034", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "No - Medical Reason", "Selection Definition": "Code 'No Medical Reason' if this medication was not prescribed post procedure or for discharge and there was a reason documented related to a medical issue or medical concern for not prescribing the medicine.", "Display Order": 3}, {"Name": "Discharge Medication Prescribed", "Code": "100001071", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "No - Patient Reason", "Selection Definition": "Code 'No, Patient Reason' if this medication was not prescribed post procedure or for discharge and there was a reason documented related to the patient's preference.", "Display Order": 4}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 10200, "Parent Element Name": "Discharge Medication Code", "Parent Element Selection Name": "Any Value"}]}]}, {"Container Class": "submissionInfoContainer", "Parent Section": "Root", "Section Display Name": "Administration", "Section Code": "ADMIN", "Section Type": "Section", "Cardinality": "1..1", "Table": "ADMIN", "Elements": [{"Element Reference": 1000, "Name": "Participant ID", "Section Display Name": "Administration", "Coding Instructions": "Indicate the participant ID of the submitting facility.", "Target Value": "N/A", "Short Name": "PartID", "Data Type": "NUM", "Precision": "8", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.836", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Participant ID", "Definition": "Participant ID is a unique number assigned to each database participant by NCDR. A database participant is defined as one entity that signs a Participation Agreement with the NCDR, submits one data submission file to the harvest, and receives one report on their data.\n\nEach participant's data if submitted to harvest must be in one data submission file for a quarter. If one participant keeps their data in more than one file (e.g. at two sites), then the data must be combined into a single data submission to the system to file for the harvest. If two or more participants share a single purchased software, and enter cases into one database, then the data must be exported into different data submission files, one for each participant ID.", "Source": "NCDR"}, "Parent Child Validations": null}, {"Element Reference": 1010, "Name": "Participant Name", "Section Display Name": "Administration", "Coding Instructions": "Indicate the full name of the facility where the procedure was performed.\n\nNote(s):\nValues should be full, official hospital names with no abbreviations or variations in spelling.", "Target Value": "N/A", "Short Name": "PartName", "Data Type": "ST", "Precision": "100", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.836", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 1020, "Name": "Time Frame of Data Submission", "Section Display Name": "Administration", "Coding Instructions": "Indicate the time frame of data included in the data submission. Format: YYYYQQ. e.g.,2016Q1", "Target Value": "N/A", "Short Name": "Timeframe", "Data Type": "ST", "Precision": "6", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "1.3.6.1.4.1.19376.1.4.1.6.5.45", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 1040, "Name": "Transmission Number", "Section Display Name": "Administration", "Coding Instructions": "This is a unique number created, and automatically inserted by the software into export file. It identifies the number of times the software has created a data submission file. The transmission number should be incremented by one every time the data submission files are exported. The transmission number should never be repeated.", "Target Value": "N/A", "Short Name": "XmsnId", "Data Type": "NUM", "Precision": "9", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "1.3.6.1.4.1.19376.1.4.1.6.5.45", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": null, "Usual Range Min": null, "Usual Range Max": null, "Valid Range Min": 1.0, "Valid Range Max": "999,999,999"}], "Definition": null, "Parent Child Validations": null}, {"Element Reference": 1050, "Name": "Vendor Identifier", "Section Display Name": "Administration", "Coding Instructions": "Vendor identification (agreed upon by mutual selection between the vendor and the NCDR) to identify software vendor. This is entered into the schema automatically by vendor software. Vendors must use consistent name identification across sites. Changes to vendor name identification must be approved by the NCDR.", "Target Value": "N/A", "Short Name": "VendorId", "Data Type": "ST", "Precision": "15", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.840", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 1060, "Name": "Vendor Software Version", "Section Display Name": "Administration", "Coding Instructions": "Vendor's software product name and version number identifying the software which created this record (assigned by vendor). Vendor controls the value in this field. This is entered into the schema automatically by vendor software.", "Target Value": "N/A", "Short Name": "Vendor<PERSON><PERSON>", "Data Type": "ST", "Precision": "20", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.847", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 1070, "Name": "Registry Identifier", "Section Display Name": "Administration", "Coding Instructions": "The NCDR registry identifier describes the data registry to which these records apply. It is implemented in the software at the time the data is collected and records are created. This is entered into the schema automatically by software.", "Target Value": "N/A", "Short Name": "RegistryId", "Data Type": "ST", "Precision": "30", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "ACC-NCDR-ICD-3.0", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.841", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 1071, "Name": "Registry Schema Version", "Section Display Name": "Administration", "Coding Instructions": "Schema version describes the version number of the Registry Transmission Document (RTD) schema to which each record conforms. It is an attribute that includes a constant value indicating the version of schema file. This is entered into the schema automatically by software.", "Target Value": "N/A", "Short Name": "SchemaVersion", "Data Type": "NUM", "Precision": "3,1", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "1", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "ICD, LDS, PPM", "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "1000142438", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}]}]}}