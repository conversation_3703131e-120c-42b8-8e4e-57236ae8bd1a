{"Dictionary": {"Dataset": {"Code": "ICD", "Id": 107}, "Version": "2.3", "Type": "<PERSON><PERSON><PERSON>", "Sections": [{"Container Class": "patientContainer", "Parent Section": "Root", "Section Display Name": "<PERSON><PERSON>", "Section Code": "DEMOGRAPHICS", "Section Type": "Section", "Cardinality": "1..1", "Table": "DEMOGRAPHICS", "Elements": [{"Element Reference": 2000, "Name": "Last Name", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate the patient's last name. Hyphenated names should be recorded with a hyphen.", "Target Value": "The value on arrival at this facility", "Short Name": "LastName", "Data Type": "LN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2010, "Name": "First Name", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate the patient's first name.", "Target Value": "The value on arrival at this facility", "Short Name": "FirstName", "Data Type": "FN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2020, "Name": "Middle Name", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate the patient's middle name.\n\nNote(s):\nIt is acceptable to specify the middle initial.\n\nIf there is no middle name given, leave field blank.\n\nIf there are multiple middle names, enter all of the middle names sequentially.\n\nIf the name exceeds 50 characters, enter the first 50 letters only.", "Target Value": "The value on arrival at this facility", "Short Name": "MidName", "Data Type": "MN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2030, "Name": "SSN", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate the patient's United States Social Security Number (SSN).\n\nNote(s):\nIf the patient does not have a US Social Security Number (SSN), leave blank and check 'SSN NA'.", "Target Value": "The value on arrival at this facility", "Short Name": "SSN", "Data Type": "ST", "Precision": "9", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.4.1", "Code System": "2.16.840.1.113883.4.1", "Code System Name": "United States Social Security Number (SSN)", "Vendor Instruction": "SSN (2030) must be 9 numeric characters long", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 2031, "Parent Element Name": "SSN N/A", "Parent Element Selection Name": "No"}]}, {"Element Reference": 2031, "Name": "SSN N/A", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if the patient does not have a United States Social Security Number (SSN).", "Target Value": "The value on arrival at this facility", "Short Name": "SSNNA", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.4.1", "Code System": "2.16.840.1.113883.4.1", "Code System Name": "United States Social Security Number (SSN)", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2040, "Name": "Patient ID", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate the number created and automatically inserted by the software that uniquely identifies this patient.\n\nNote(s):\nOnce assigned to a patient at the participating facility, this number will never be changed or reassigned to a different patient. If the patient returns to the same participating facility or for follow up, they will receive this same unique patient identifier.", "Target Value": "The value on arrival at this facility", "Short Name": "NCDRPatientID", "Data Type": "NUM", "Precision": "9", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "Automatic", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.842", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": null, "Usual Range Min": null, "Usual Range Max": null, "Valid Range Min": 1.0, "Valid Range Max": "999,999,999"}], "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2045, "Name": "Other ID", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate an optional patient identifier, such as medical record number, that can be associated with the patient.", "Target Value": "N/A", "Short Name": "OtherID", "Data Type": "ST", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.843", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2050, "Name": "Birth Date", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate the patient's date of birth.", "Target Value": "The value on arrival at this facility", "Short Name": "DOB", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Birth Date (2050) must be Less than Most Recent Cardiac Arrest Date (4225)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2060, "Name": "Sex", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate the patient's sex at birth.", "Target Value": "The value on arrival at this facility", "Short Name": "Sex", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Sex", "Code": "M", "Code System": "2.16.840.1.113883.5.1", "Code System Name": "HL7 Administrative Gender", "Selection Name": "Male", "Selection Definition": null, "Display Order": 1}, {"Name": "Sex", "Code": "F", "Code System": "2.16.840.1.113883.5.1", "Code System Name": "HL7 Administrative Gender", "Selection Name": "Female", "Selection Definition": null, "Display Order": 2}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2065, "Name": "Patient Zip Code", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate the patient's United States Postal Service zip code of their primary residence.\n\nNote(s):\nIf the patient does not have a U.S. residence, or is homeless, leave blank and check 'Zip Code NA'.", "Target Value": "The value on arrival at this facility", "Short Name": "ZipCode", "Data Type": "ST", "Precision": "5", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Patient Zip Code (2065) must be 5 numeric characters long", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 2066, "Parent Element Name": "Zip Code N/A", "Parent Element Selection Name": "No"}]}, {"Element Reference": 2066, "Name": "Zip Code N/A", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if the patient does not have a United States Postal Service zip code.\n\nNote(s):\nThis includes patients who do not have a U.S. residence or are homeless.", "Target Value": "The value on arrival at this facility", "Short Name": "ZipCodeNA", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2070, "Name": "Race - White", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if the patient is White as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "<PERSON><PERSON><PERSON><PERSON>", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2106-3", "Code System": "2.16.840.1.113883.5.104", "Code System Name": "HL7 Race", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "<PERSON> (race)", "Definition": "Having origins in any of the original peoples of Europe, the Middle East, or North Africa.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": null}, {"Element Reference": 2071, "Name": "Race - Black/African American", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if the patient is Black or African American as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "RaceBlack", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2054-5", "Code System": "2.16.840.1.113883.5.104", "Code System Name": "HL7 Race", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Black/African American (race)", "Definition": "Having origins in any of the black racial groups of Africa. Terms such as \"Haitian\" or \"Negro\" can be used in addition to \"Black or African American.\"", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": null}, {"Element Reference": 2073, "Name": "Race - American Indian/Alaskan Native", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if the patient is American Indian or Alaskan Native as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "RaceAmIndian", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "1002-5", "Code System": "2.16.840.1.113883.5.104", "Code System Name": "HL7 Race", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "American Indian or Alaskan Native (race)", "Definition": "Having origins in any of the original peoples of North and South America (including Central America), and who maintains tribal affiliation or community attachment.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": null}, {"Element Reference": 2072, "Name": "Race - Asian", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if the patient is Asian as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "Race<PERSON>ian", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2028-9", "Code System": "2.16.840.1.113883.5.104", "Code System Name": "HL7 Race", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Asian (race)", "Definition": "Having origins in any of the original peoples of the Far East, Southeast Asia, or the Indian subcontinent including, for example, Cambodia, China, India, Japan, Korea, Malaysia, Pakistan, the Philippine Islands, Thailand, and Vietnam.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": null}, {"Element Reference": 2074, "Name": "Race - Native Hawaiian/Pacific Islander", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if the patient is Native Hawaiian or Pacific Islander as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "RaceNatHaw", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2076-8", "Code System": "2.16.840.1.113883.5.104", "Code System Name": "HL7 Race", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Race - Native Hawaiian/Pacific Islander - Native Hawaiian", "Definition": "Having origins in any of the original peoples of Hawaii, Guam, Samoa, or other Pacific Islands.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": null}, {"Element Reference": 2080, "Name": "Race - Asian Indian", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if the patient is Asian Indian as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2029-7", "Code System": "2.16.840.1.113883.5.104", "Code System Name": "HL7 Race", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Asian Indian", "Definition": "Having origins in any of the original peoples of India.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": [{"Parent Element Reference": 2072, "Parent Element Name": "Race - Asian", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 2081, "Name": "Race - Chinese", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if the patient is Chinese as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "RaceChinese", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2034-7", "Code System": "2.16.840.1.113883.5.104", "Code System Name": "HL7 Race", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Asian - Chinese", "Definition": "Having origins in any of the original peoples of China.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": [{"Parent Element Reference": 2072, "Parent Element Name": "Race - Asian", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 2082, "Name": "Race - Filipino", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if the patient is Filipino as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "RaceFilipino", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2036-2", "Code System": "2.16.840.1.113883.5.104", "Code System Name": "HL7 Race", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Asian - Filipino", "Definition": "Having origins in any of the original peoples of the Philippines.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": [{"Parent Element Reference": 2072, "Parent Element Name": "Race - Asian", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 2083, "Name": "Race - Japanese", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if the patient is Japanese as determined by the patient/family.  \n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "RaceJapanese", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2039-6", "Code System": "2.16.840.1.113883.5.104", "Code System Name": "HL7 Race", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Asian - Japanese", "Definition": "Having origins in any of the original peoples of Japan.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": [{"Parent Element Reference": 2072, "Parent Element Name": "Race - Asian", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 2084, "Name": "Race - Korean", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if the patient is Korean as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "RaceKorean", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2040-4", "Code System": "2.16.840.1.113883.5.104", "Code System Name": "HL7 Race", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Asian - Korean ", "Definition": "Having origins in any of the original peoples of Korea.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity "}, "Parent Child Validations": [{"Parent Element Reference": 2072, "Parent Element Name": "Race - Asian", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 2085, "Name": "Race - Vietnamese", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if the patient is Vietnamese as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "RaceVietnamese", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2047-9", "Code System": "2.16.840.1.113883.5.104", "Code System Name": "HL7 Race", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Asian - Vietnamese", "Definition": "Having origins in any of the original peoples of Viet Nam.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": [{"Parent Element Reference": 2072, "Parent Element Name": "Race - Asian", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 2086, "Name": "Race - Other Asian", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if the patient is of Other Asian descent as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001130", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Asian - Other Asian", "Definition": "Having origins in any of the original peoples elsewhere in Asia.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": [{"Parent Element Reference": 2072, "Parent Element Name": "Race - Asian", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 2090, "Name": "Race - Native Hawaiian", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if the patient is Native Hawaiian as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "RaceNativeHawaii", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2079-2", "Code System": "2.16.840.1.113883.5.104", "Code System Name": "HL7 Race", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Native Hawaiian", "Definition": "Having origins in any of the original peoples of the islands of Hawaii.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity "}, "Parent Child Validations": [{"Parent Element Reference": 2074, "Parent Element Name": "Race - Native Hawaiian/Pacific Islander", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 2091, "Name": "Race - Guamanian or Chamorro", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if the patient is Guamanian or Chamorro as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "RaceGuamChamorro", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2086-7", "Code System": "2.16.840.1.113883.5.104", "Code System Name": "HL7 Race", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Native Hawaiian/Pacific Islander - Guamanian or Chamorro", "Definition": "Having origins in any of the original peoples of the Mariana Islands or the island of Guam.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": [{"Parent Element Reference": 2074, "Parent Element Name": "Race - Native Hawaiian/Pacific Islander", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 2092, "Name": "Race - Samoan", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if the patient is Samoan as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "Race<PERSON><PERSON><PERSON>", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2080-0", "Code System": "2.16.840.1.113883.5.104", "Code System Name": "HL7 Race", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Native Hawaiian/Pacific Islander - Samoan", "Definition": "Having origins in any of the original peoples of the island of the Samoa.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": [{"Parent Element Reference": 2074, "Parent Element Name": "Race - Native Hawaiian/Pacific Islander", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 2093, "Name": "Race - Other Pacific Islander", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if the patient is Other Pacific Islander as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "RacePacificIslandOther", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2500-7", "Code System": "2.16.840.1.113883.5.104", "Code System Name": "HL7 Race", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Native Hawaiian/Pacific Islander - Other Pacific Island", "Definition": "Having origins in any of the original peoples of any other island in the Pacific. ", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": [{"Parent Element Reference": 2074, "Parent Element Name": "Race - Native Hawaiian/Pacific Islander", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 2076, "Name": "Hispanic or Latino Ethnicity", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if the patient is of Hispanic or Latino ethnicity as determined by the patient/family. \n\nNote(s):\nIf the patient has multiple hispanic or latin ethnicity, specify them using the other ethnicity selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "<PERSON><PERSON><PERSON><PERSON>", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2135-2", "Code System": "2.16.840.1.113883.5.50", "Code System Name": "HL7 Ethnicity", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Hispanic or Latino Ethnicity", "Definition": "A person of Mexican, Puerto Rican, Cuban, South or Central American, or other Spanish culture or origin, regardless of race. The term, \"Spanish origin,\" can be used in addition to \"Hispanic or Latino.\" ", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": null}, {"Element Reference": 2100, "Name": "Hispanic Ethnicity Type - Mexican, Mexican-American, Chicano", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if the patient is Mexican, Mexican - American, or Chicano as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple hispanic or latin ethnicity, specify them using the other ethnicity selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "HispEthnicityMexican", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2148-5", "Code System": "2.16.840.1.113883.5.50", "Code System Name": "HL7 Ethnicity", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Hispanic Ethnicity - Mexican/Mexican American/Chicano", "Definition": "Having origins in any of the original peoples of Mexico.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": [{"Parent Element Reference": 2076, "Parent Element Name": "Hispanic or Latino Ethnicity", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 2101, "Name": "Hispanic Ethnicity Type - Puerto Rican", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if the patient is Puerto Rican as determined by the patient/family. \n\nNote(s):\nIf the patient has multiple hispanic or latin ethnicity, specify them using the other ethnicity selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "HispEthnicityPuertoRico", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2180-8", "Code System": "2.16.840.1.113883.5.50", "Code System Name": "HL7 Ethnicity", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Hispanic Ethnicity - Puerto Rican", "Definition": "Having origins in any of the original peoples of Puerto Rico.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": [{"Parent Element Reference": 2076, "Parent Element Name": "Hispanic or Latino Ethnicity", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 2102, "Name": "Hispanic Ethnicity Type - Cuban", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if the patient is Cuban as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple hispanic or latin ethnicity, specify them using the other ethnicity selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "HispEthnicityCuban", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2182-4", "Code System": "2.16.840.1.113883.5.50", "Code System Name": "HL7 Ethnicity", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Hispanic Ethnicity - Cuban", "Definition": "Having origins in any of the original peoples of Cuba.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": [{"Parent Element Reference": 2076, "Parent Element Name": "Hispanic or Latino Ethnicity", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 2103, "Name": "Hispanic Ethnicity Type - Other Hispanic, Latino or Spanish Origin", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if the patient is another Hispanic, Latino, or Spanish origin as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple hispanic or latin ethnicity, specify them using the other ethnicity selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "HispEthnicityOther<PERSON><PERSON>in", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001131", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Hispanic Ethnicity - Other Hispanic/Latino/Spanish Origin", "Definition": "Having origins in any of the originals peoples in other Hispanic, Latino or Spanish territories.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": [{"Parent Element Reference": 2076, "Parent Element Name": "Hispanic or Latino Ethnicity", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "<PERSON>. Episode of Care", "Section Code": "EOC", "Section Type": "Section", "Cardinality": "1..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 2999, "Name": "Episode Unique Key", "Section Display Name": "<PERSON>. Episode of Care", "Coding Instructions": "Indicate the unique key associated with each patient episode record as assigned by the EMR/EHR or your software application.", "Target Value": "N/A", "Short Name": "<PERSON><PERSON><PERSON>", "Data Type": "ST", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "Automatic", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.855", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 3000, "Name": "Arrival Date", "Section Display Name": "<PERSON>. Episode of Care", "Coding Instructions": "Indicate the date the patient arrived at your facility.", "Target Value": "N/A", "Short Name": "ArrivalDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Arrival Date (3000) must be Greater than or Equal to Birth Date (2050)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 3040, "Name": "Reason for Admission", "Section Display Name": "<PERSON>. Episode of Care", "Coding Instructions": "Indicate the primary reason for admission to your facility.", "Target Value": "The value on arrival at this facility", "Short Name": "ReasonForAdmit", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001132", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Reason for Admission", "Code": "100001133", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Admitted for procedure", "Selection Definition": "The patient was admitted specifically to have the device or lead procedure,  including patients admitted for device infection with subsequent extraction.", "Display Order": 1}, {"Name": "Reason for Admission", "Code": "100001134", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Admitted for Heart Failure", "Selection Definition": "Heart failure is the primary reason the patient was admitted to this facility.", "Display Order": 2}, {"Name": "Reason for Admission", "Code": "100001227", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Other Reason", "Selection Definition": "A cardiac problem (excluding heart failure) or non-cardiac problem is the primary reason the patient was admitted to this facility.", "Display Order": 3}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 3005, "Name": "Health Insurance", "Section Display Name": "<PERSON>. Episode of Care", "Coding Instructions": "Indicate if the patient has health insurance.", "Target Value": "The value on arrival at this facility", "Short Name": "HealthIns", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "63513-6", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": "Health Insurance (3005) must not be NULL", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 3010, "Name": "Health Insurance Payment Source", "Section Display Name": "<PERSON>. Episode of Care", "Coding Instructions": "Indicate the patient's health insurance payment type.\n\nNote(s):\nIf the patient has multiple insurance payors, select all payors.", "Target Value": "The value on arrival at this facility", "Short Name": "HIPS", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "*********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Health Insurance Payment Source", "Code": "5", "Code System": "2.16.840.1.113883.3.221.5", "Code System Name": "PHDSC", "Selection Name": "Private Health Insurance", "Selection Definition": "Private health insurance is coverage by a health plan provided through an employer or union or purchased by an individual from a private health insurance company. A health maintenance organization (HMO) is considered private health insurance.", "Display Order": 1}, {"Name": "Health Insurance Payment Source", "Code": "1", "Code System": "2.16.840.1.113883.3.221.5", "Code System Name": "PHDSC", "Selection Name": "Medicare", "Selection Definition": "Medicare is the Federal program which helps pay health care costs for people 65 and older and for certain people under 65 with long-term disabilities.", "Display Order": 2}, {"Name": "Health Insurance Payment Source", "Code": "2", "Code System": "2.16.840.1.113883.3.221.5", "Code System Name": "PHDSC", "Selection Name": "Medicaid", "Selection Definition": "Medicaid is a program administered at the state level, which provides medical assistance to the needy. Families with dependent children, the aged, blind, and disabled who are in financial need are eligible for Medicaid. It may be known by different names.", "Display Order": 3}, {"Name": "Health Insurance Payment Source", "Code": "31", "Code System": "2.16.840.1.113883.3.221.5", "Code System Name": "PHDSC", "Selection Name": "Military Health Care", "Selection Definition": "Military Health care - Military health care includes TRICARE/CHAMPUS (Civilian Health and Medical Program of the Uniformed Services) and CHAMPVA (Civilian Health and Medical Program of the Department of Veterans Affairs), as well as care provided by the Department of Veterans Affairs (VA).", "Display Order": 4}, {"Name": "Health Insurance Payment Source", "Code": "36", "Code System": "2.16.840.1.113883.3.221.5", "Code System Name": "PHDSC", "Selection Name": "State-Specific Plan (non-Medicaid)", "Selection Definition": "State Specific Plans - Some states have their own health insurance programs for low-income uninsured individuals. These health plans may be known by different names in different states.", "Display Order": 5}, {"Name": "Health Insurance Payment Source", "Code": "33", "Code System": "2.16.840.1.113883.3.221.5", "Code System Name": "PHDSC", "Selection Name": "Indian Health Service", "Selection Definition": "Indian Health Service (IHS) is a health care program through which the Department of Health and Human Services provides medical assistance to eligible American Indians at IHS facilities. In addition, the IHS helps pay the cost of selected health care services provided at non-IHS facilities.", "Display Order": 6}, {"Name": "Health Insurance Payment Source", "Code": "100000812", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Non-US Insurance", "Selection Definition": "Non-US insurance refers to individuals with a payor that does not originate in the United States.", "Display Order": 7}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 3005, "Parent Element Name": "Health Insurance", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 12846, "Name": "Medicare Beneficiary Identifier", "Section Display Name": "<PERSON>. Episode of Care", "Coding Instructions": "Indicate the patient's Medicare Beneficiary Identifier (MBI).\n\nNote(s):\nEnter the Medicare Beneficiary Identifier (MBI) for those patients insured by Medicare. Patients without Medicare will not have a MBI.", "Target Value": "The value on arrival at this facility", "Short Name": "MBI", "Data Type": "ST", "Precision": "11", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.4.927", "Code System": "2.16.840.1.113883.4.927", "Code System Name": "Center for medicare and medicaid services, MBI", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Medicare Beneficiary Identifier", "Definition": "The Medicare Access and CHIP Reauthorization Act (MACRA) of 2015, requires us to remove Social Security Numbers (SSNs) from all Medicare cards by April 2019. A new Medicare Beneficiary Identifier (MBI) will replace the SSN-based Health Insurance Claim Number (HICN) on the new Medicare cards for Medicare transactions like billing, eligibility status, and claim status.", "Source": "https://www.cms.gov/Medicare/New-Medicare-Card/index.html\n\n"}, "Parent Child Validations": null}, {"Element Reference": 3035, "Name": "Patient Restriction", "Section Display Name": "<PERSON>. Episode of Care", "Coding Instructions": "Indicate if the patient requested for their information not to be used for any research or studies for the associated episode of care.\n\nNote(s):\nDocumentation must be found in the patient record to support the request of removal of their information.\nIntended for future use.", "Target Value": "The value on arrival at this facility", "Short Name": "PtRestriction", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000922", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 3020, "Name": "<PERSON><PERSON> Enrolled in Research Study", "Section Display Name": "<PERSON>. Episode of Care", "Coding Instructions": "Indicate if the patient is enrolled in an ongoing ACC - NCDR research study related to this registry. Intended for future use.", "Target Value": "Any occurrence between arrival at this facility and discharge", "Short Name": "EnrolledStudy", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001095", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "<PERSON><PERSON> Enrolled in Research Study", "Definition": "A clinical or research study is one in which participants are assigned to receive one or more interventions (or no intervention) so that researchers can evaluate the effects of the interventions on biomedical or health-related outcomes. The assignments are determined by the study protocol. Participants may receive diagnostic, therapeutic, or other types of interventions.", "Source": "Clinicaltrials.gov Glossary of Common Site Terms retrieved from http://clinicaltrials.gov/ct2/about-studies/glossary#interventional-study"}, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "<PERSON>. Episode of Care", "Section Display Name": "Research Study", "Section Code": "RESEARCHSTUDY", "Section Type": "Repeater Section", "Cardinality": "0..n", "Table": "RESEARCHSTUDY", "Elements": [{"Element Reference": 3025, "Name": "Research Study Name", "Section Display Name": "Research Study", "Coding Instructions": "Indicate the research study name as provided by the research study protocol.\n\nNote(s):\nIf the patient is in more than one research study, list each separately.\nIntended for future use.", "Target Value": "N/A", "Short Name": "StudyName", "Data Type": "ST", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001096", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 3020, "Parent Element Name": "<PERSON><PERSON> Enrolled in Research Study", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 3030, "Name": "Research Study Patient ID", "Section Display Name": "Research Study", "Coding Instructions": "Indicate the research study patient identification number as assigned by the research protocol.\n\nNote(s):\nIf the patient is in more than one research study, list each separately.\nIntended for future use.", "Target Value": "N/A", "Short Name": "StudyPtID", "Data Type": "ST", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.852", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 3020, "Parent Element Name": "<PERSON><PERSON> Enrolled in Research Study", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "C. History and Risk Factors", "Section Code": "HISTORYANDRISK", "Section Type": "Section", "Cardinality": "0..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 4000, "Name": "Heart Failure", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the patient has been diagnosed with heart failure.\n\nNote(s):\nHeart failure cannot be coded by the abstractor based on clinical symptoms or diagnostic studies.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "HF", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "84114007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Heart Failure", "Definition": "Heart failure is a complex clinical syndrome that results from any structural or functional impairment of ventricular filling or ejection of blood. The cardinal manifestations of HF are dyspnea and fatigue, which may limit exercise tolerance, and fluid retention, which may lead to pulmonary and/or splanchnic congestion and/or peripheral edema. Some patients have exercise intolerance but little evidence of fluid retention, whereas others complain primarily of edema, dyspnea, or fatigue. Because some patients present without signs or symptoms of volume overload, the term \"heart failure\" is preferred over \"congestive heart failure.\" There is no single diagnostic test for HF because it is largely a clinical diagnosis based on a careful history and physical examination.", "Source": "2013 ACCF/AHA Guideline for the Management of Heart Failure;  J Am Coll Cardiol. 2013;62(16):e147-e239. doi:10.1016/j.jacc.2013.05.019"}, "Parent Child Validations": null}, {"Element Reference": 4010, "Name": "NYHA Functional Classification", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate the patient's New York Heart Association (NYHA) Functional Classification based upon the physician documented classification at the time of the current procedure. \n\nNote(s):\nThe NYHA Functional Classification must be specifically documented in the medical record and not coded by the abstractor based upon patient symptoms.", "Target Value": "The highest value on the first procedure in this admission", "Short Name": "NYHA", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "420816009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "NYHA Functional Classification", "Code": "420300004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Class I", "Selection Definition": "Patients with cardiac disease but without resulting limitations of physical activity. Ordinary physical activity does not cause undue fatigue, palpitation, or dyspnea.", "Display Order": 1}, {"Name": "NYHA Functional Classification", "Code": "421704003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Class II", "Selection Definition": "Patients with cardiac disease resulting in slight limitation of physical activity. They are comfortable at rest. Ordinary physical activity results in fatigue, palpitation, or dyspnea.", "Display Order": 2}, {"Name": "NYHA Functional Classification", "Code": "420913000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Class III", "Selection Definition": "Patients with cardiac disease resulting in marked limitation of physical activity. They are comfortable at rest. Less than ordinary activity causes fatigue, palpitation, or dyspnea.", "Display Order": 3}, {"Name": "NYHA Functional Classification", "Code": "422293003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Class IV", "Selection Definition": "Patients with cardiac disease resulting in inability to carry on any physical activity without discomfort. Symptoms are present even at rest or minimal exertion. If any physical activity is undertaken, discomfort is increased.", "Display Order": 4}], "Ranges": null, "Definition": {"Title": "NYHA", "Definition": "The NYHA classes focus on exercise capacity and the symptomatic status of the disease. ", "Source": "2013 ACCF/AHA Guideline for the Management of Heart Failure;  J Am Coll Cardiol. 2013;62(16):e147-e239. doi:10.1016/j.jacc.2013.05.019"}, "Parent Child Validations": [{"Parent Element Reference": 4000, "Parent Element Name": "Heart Failure", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4150, "Name": "Prior LVEF Assessed", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if a left ejection fraction percentage has been assessed.\n\nNote: If the LVEF has a date or statement of date affiliated with it, which confirms it was performed in the last 12 months, then you are able to utilize that LVEF in coding. An LVEF measurement in a dictated note is not sufficient unless there is a date affiliated with it (e.g., LVEF asssessed May 2020).", "Target Value": "Any occurrence between 12 months prior to arrival and start of the first procedure", "Short Name": "PriorLVEFAssessed", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001027", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 4155, "Name": "Most Recent LVEF Date", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate the date of the implanting physician cited LVEF or the most recent LVEF assessed if the implanting physician value is not available. \n\nNote(s): \nIf the month or day of the LVEF is unknown, please code 01/01/YYYY. If the specific year is unknown in the current record, the year may be estimated based on timeframes found in prior medical record documentation (Example: If the patient had \"most recent LVEF\" documented in a record from 2011, then the year 2011 can be utilized and coded as 01/01/2011).\n\nIf the LVEF has a date or statement of date affiliated with it, which confirms it was performed in the last 12 months, then you are able to utilize that LVEF in coding. An LVEF measurement in a dictated note is not sufficient unless there is a date affiliated with it (e.g., LVEF assessed May 2020).", "Target Value": "Any occurrence between 12 months prior to arrival and start of the first procedure", "Short Name": "PriorLVEFDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001027", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Most Recent LVEF Date (4155) must be Greater than or Equal to Birth Date (2050)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4150, "Parent Element Name": "Prior LVEF Assessed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4160, "Name": "Most Recent LVEF %", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate the left ventricular ejection fraction cited by the implanting physician as the indication for the ICD. In the absence of a physician cited LVEF, indicate the most recent left ventricular ejection fraction. The left ventricular ejection fraction can be assessed via invasive (i.e. LV gram), or non-invasive (i.e. Echo, MR, CT or Nuclear) testing.\n\nNote(s):\nEnter a percentage in the range of 01 - 99. If a percentage range is reported, report the lowest number of the range (i.e.50-55%, is reported as 50%).\nAn LVEF measurement is reported as \"less than\" or \"greater than\", code to the nearest whole number (e.g., < 40% is coded 39% and > 40% is coded 41%).\n", "Target Value": "The last value between 12 months prior to arrival and start of the first procedure", "Short Name": "PriorLVEF", "Data Type": "PQ", "Precision": "2,0", "Unit Of Measure": "%", "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "10230-1", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "%", "Usual Range Min": 5.0, "Usual Range Max": 70.0, "Valid Range Min": 1.0, "Valid Range Max": "99"}], "Definition": {"Title": "Most Recent LVEF %", "Definition": "The left ventricular ejection fraction is the percentage of blood emptied from the left ventricle at the end of contraction.", "Source": "ACC Clinical Data Standards, Society for Thoracic Surgeons Adult Cardiac Surgery Database (STS)"}, "Parent Child Validations": [{"Parent Element Reference": 4150, "Parent Element Name": "Prior LVEF Assessed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4165, "Name": "Syndromes with Risk of Sudden Death", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the patient has a syndrome that puts him/her at risk for sudden death.\n\nNote(s): \nThe patient must be diagnosed with one of the syndromes listed in Seq. 4170 that puts him/her at risk for sudden death.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "SyndromeRiskDeath", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001202", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 4170, "Name": "Syndromes with Risk of Sudden Death Type", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate the type of syndrome that puts the patient at risk for sudden death.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "SyndromeRiskType", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001105", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Syndromes with Risk of Sudden Death Type", "Code": "9651007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Long QT syndrome", "Selection Definition": "Long QT syndrome (LQTS) describes a heterogeneous group of inherited channelopathies that confer risks of polymorphic ventricular tachycardia and sudden cardiac death. Diagnosis is clinical and is made on the basis of the presentation and electrocardiogram, with the probability of LQTS calculated by the <PERSON> score. Genetic testing is generally advised; variants in KCNQ1, KCNH2, and SCN5A are responsible for LQT1, LQT2, and LQT3, respectively, accounting for approximately 75% of genetically resolved cases.", "Display Order": 1}, {"Name": "Syndromes with Risk of Sudden Death Type", "Code": "*********", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Short QT syndrome", "Selection Definition": "Short QT (SQT) refers to the electrocardiographic manifestation of accelerated cardiac repolarization. <PERSON><PERSON><PERSON> et al. were the first to suggest an association with atrial and ventricular fibrillation in 2000. The familial nature and arrhythmogenic potential of SQT were confirmed by <PERSON><PERSON><PERSON> et al. in 2003. Acquired disease –the most common cause– results from electrolyte disturbances or drugs, in addition to hypercalcemia, hyperkalemia, and acidosis; SQT manifests with digoxin, androgen use, increased vagal tone and after ventricular fibrillation (<PERSON>, 2004; <PERSON><PERSON><PERSON>, <PERSON>, & James, 2009; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2015). SQTS is a rare, sporadic or autosomal dominant disease that manifests with atrial and ventricular arrhythmias, sudden cardiac death and shortened QT (<PERSON><PERSON><PERSON> et al., 2004). Cardiac arrest occurs as the presenting symptom in up to 40% of the cases (<PERSON> et al., 2014). Mutations in potassium (KCNH2, KCNQ1, KCNJ2) and calcium (CACNA1C, CACNB2, CACNA2D1) channels have been identified as disease causing. ", "Display Order": 2}, {"Name": "Syndromes with Risk of Sudden Death Type", "Code": "*********", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "<PERSON><PERSON><PERSON> syndrome", "Selection Definition": "Polymorphic ventricular tachycardia in the absence of structural heart disease, associated with a baseline ECG pattern during sinus rhythm showing right bundle branch block with ST segment elevation in leads V1 through V3.  It can also be characterized by documentation of ECG patterns associated with Brugada Syndrome, some of which may be unmasked when provoked with drugs.\n\nThe most common genetic mutations identified for <PERSON><PERSON><PERSON> syndrome are in a sodium channel gene (SCN5A). Sodium channel blocking drugs, therefore, may exacerbate the electrocardiographic features and clinical presentation. <PERSON><PERSON><PERSON> syndrome typically presents before the age of 50 years.", "Display Order": 3}, {"Name": "Syndromes with Risk of Sudden Death Type", "Code": "100000956", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Catecholaminergic polymorphic VT", "Selection Definition": "CPVT is a highly malignant inheritable cardiac channelopathy in individuals without structural heart disease and QT prolongation. It is often thought of as a disease of childhood with patients presenting before the age of 21 with symptoms such as syncope or sudden cardiac arrest; however, the adult form presents between the ages of 32-48. CVPT is triggered by physical or emotional stress in patients ECG is normal. ", "Display Order": 4}, {"Name": "Syndromes with Risk of Sudden Death Type", "Code": "100001014", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Idiopathic/primary VT/VF", "Selection Definition": "VT that occurs in patients without structural heart disease, metabolic abnormalities, or the long QT syndrome.", "Display Order": 5}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4165, "Parent Element Name": "Syndromes with Risk of Sudden Death", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4175, "Name": "Familial Syndrome with Risk of Sudden Death", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the patient has any first degree family member, who is a direct blood relative (parents, siblings, children), who has been diagnosed with a syndrome with risk of sudden death.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "FamilialSyndSuddenDeath", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001006", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Familial Syndrome with Risk of Sudden Death", "Definition": "Sudden cardiac death may result from a combination of epidemiological risk factors, structural, metabolic and genetic determinants. Syndromes with risk of sudden death may include:\n- Brugada Syndrome \n- Catecholaminergic Polymorphic Ventricular Tachycardia (CPVT) \n- Long QT Syndrome (LQTS)\n- Short QT Syndrome (SQTS)\n- Timothy Syndrome\n- <PERSON> Parkinson White (WPW)\n\nOther related conditions may include structural malformations of the heart muscle. A dysplasia (misplaced) or cardiomyopathy (thickening) of the heart muscle can be related to Arrhythmogenic Right Ventricular Dysplasia/Cardiomyopathy (ARVD/C), hypertrophic cardiomyopathy (HCM), or Dilated Cardiomyopathy (DM).", "Source": "Circulation. 2008; 118: 1854-1863 doi: 10.1161/CIRCULATIONAHA.108.783654"}, "Parent Child Validations": null}, {"Element Reference": 4180, "Name": "Familial History of Non-Ischemic Cardiomyopathy", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the patient has any first degree family member, who is a direct blood relative (parents, siblings, children), who has a history of non-ischemic cardiomyopathy.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "FamilialHxNICM", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "281666001:246090004=399020009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 4185, "Name": "Ischemic Cardiomyopathy", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the patient has been diagnosed with a history of ischemic cardiomyopathy (ICM).\n\nNote(s): \nICM is a clinical diagnosis which must be documented by a provider. Documented mixed cardiomyopathy is coded as both ICM, Seq. 4185 and NICM, Seq. 4200.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "ISCM", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "426856002", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Ischemic Cardiomyopathy", "Definition": "Indicate if the patient has a history of ischemic cardiomyopathy documented by heart failure and reduced systolic function (ejection fraction <40%) and history of any one of the following:\n1. History of myocardial infarction (MI) manifested as\na) Wall motion abnormality felt consistent with MI on echocardiography, nuclear imaging, ventriculography, cardiac MR, or other imaging;\nb) ECG evidence of prior MI or acute MI;\nc) Cardiac biomarker elevation and clinical presentation (e.g., chest pain) consistent with MI;\n2. History of Percutaneous Coronary Angioplasty;\n3. History of Coronary Artery Bypass Graft Surgery;\n4. Conventional coronary angiography demonstrates >=70% stenosis in at least one major coronary artery.\n5. Stress testing (with or without imaging) diagnostic of coronary artery disease.", "Source": "NCDR"}, "Parent Child Validations": null}, {"Element Reference": 4190, "Name": "Ischemic Cardiomyopathy Timeframe", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate the timeframe since the initial diagnosis of ischemic cardiomyopathy.", "Target Value": "The first value between birth and the first procedure in this admission", "Short Name": "ISCMTimeframe", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001022", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Ischemic Cardiomyopathy Timeframe", "Code": "100001028", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Less than 3 months", "Selection Definition": null, "Display Order": 1}, {"Name": "Ischemic Cardiomyopathy Timeframe", "Code": "100000924", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "3 months or more", "Selection Definition": null, "Display Order": 2}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4185, "Parent Element Name": "Ischemic Cardiomyopathy", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4195, "Name": "Ischemic Cardiomyopathy Guideline Directed Medical Therapy Maximum Dose", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if patient has been on guideline directed medical therapy at least 3 months.\n\nNote(s): \nDocumentation of GDMT is the responsibility of the clinician and cannot be determined by the abstractor based on a list of medications. Documentation of GDMT maximum, optimum, appropriate dose, medical management/medical therapy for cardiomyopathy or a discussion in the medical record regarding medications as it relates to the patient's cardiomyopathy is acceptable for documenting GDMT. Some other acceptable statements are good neurohormonal therapy, managed appropriately on HF medications, and failed medically management of heart failure.", "Target Value": "The first value between birth and the first procedure in this admission", "Short Name": "ISCMGDMTDose", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001021", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Ischemic Cardiomyopathy Guideline Directed Medical Therapy Maximum Dose", "Code": "100001037", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Yes (for 3 months)", "Selection Definition": "The patient has been prescribed guideline directed medical therapy for at least 3 months.\n\nThis may be coded if there is documentation of GDMT without a time frame, only if the abstractor can determine from the medical record that the patient has been on these exact medications for at least 3 months.", "Display Order": 1}, {"Name": "Ischemic Cardiomyopathy Guideline Directed Medical Therapy Maximum Dose", "Code": "100001036", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not Documented", "Selection Definition": "There is no documentation of guideline directed medical therapy being prescribed.", "Display Order": 2}, {"Name": "Ischemic Cardiomyopathy Guideline Directed Medical Therapy Maximum Dose", "Code": "100001035", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not Attempted", "Selection Definition": "Guideline directed medical therapy was not attempted on the patient.", "Display Order": 3}, {"Name": "Ischemic Cardiomyopathy Guideline Directed Medical Therapy Maximum Dose", "Code": "100001038", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Inability to complete", "Selection Definition": "The patient was unable to continue the guideline directed medical therapy for 3 months or the patient is on guideline directed medical therapy but it has been less than 3 months. Without a definitive time documented or the ability to determine the timeframe from the medical record, it would be captured as Inability to Complete. The duration of treatment would default to less than 3 months since the timeframe was not able to be determined. Inability to Complete would also include patients started on GDMT where it has been less than 3 months since therapy was started, patient refusal, an allergy or absolute contraindication.", "Display Order": 4}], "Ranges": null, "Definition": {"Title": "Ischemic Guideline Directed Medical Therapy Maximum Dose", "Definition": "For heart failure in the setting of LV systolic dysfunction, this may require individualization but typically should include the combination of an angiotensin-converting enzyme inhibitor or angiotensin receptor blocker and beta blocker therapy adjusted to target doses as tolerated, with diuretics adjusted if/as needed to control fluid retention. In selected patients, the addition of aldosterone antagonists is appropriate. In addition, in some cases the use of a combination of hydralazine and nitrates may be used instead of an ACE inhibitor / angiotensin receptor blocker. Patients who are going to receive substantial benefit from medical treatment alone usually show some clinical improvement during the first 3 to 6 months. Medical therapy is also assumed to include adequate rate control for tachyarrhythmias, including atrial fibrillation. Therefore, it is recommended that GDMT be provided for at least 3 months before planned reassessment of LV function to consider device implantation. If LV function improves to the point where primary prevention indications no longer apply, then device implantation is not indicated. For stable ischemic heart disease, GDMT should include aspirin (or a thienopyridine if aspirin is not tolerated), statin therapy, angiotensin-converting enzyme inhibition (or an angiotensin receptor blocker) and the use of beta-blockers after myocardial infarction. Therapy for angina/ischemia should include at least 1 of the following medications: beta-blockers, calcium channel antagonists, or nitrates. Therapy should also be directed at optimizing the treatment of associated conditions such as diabetes and uncontrolled hypertension.", "Source": "1) <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. 2013 ACCF/AHA guideline for the management of ST-elevation myocardial infarction: a report of the American College of Cardiology Foundation/American Heart Association Task Force on Practice Guidelines. J Am Coll Cardiol 2013;61\n2) <PERSON>, <PERSON><PERSON>back RF, <PERSON>, et al. ACCF/HRS/AHA/ASE/HFSA/SCAI/SCCT/SCMR 2013 appropriate use criteria for implantable cardioverter-defibrillators and cardiac resynchronization therapy. J Am Coll Cardiol 2013;61:1318-68. doi: 10.1016/j.jacc.2012.12.017"}, "Parent Child Validations": [{"Parent Element Reference": 4185, "Parent Element Name": "Ischemic Cardiomyopathy", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4200, "Name": "Non-Ischemic Cardiomyopathy", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the patient has been diagnosed with a history of non-ischemic cardiomyopathy.\n\nNote(s):\nA patient with heart failure or a documented history of heart failure and an ejection fraction less than 40 would qualify as a 'Yes' if the operator identifies the cardiomyopathy is non-ischemic in origin.\n\nNICM is a clinical diagnosis which must be documented by a provider. Documented mixed cardiomyopathy is coded as both ICM, Seq. 4185 and NICM, Seq. 4200.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "NICM", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "111000119104", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Non-Ischemic Cardiomyopathy", "Definition": "Angiotensin-converting enzyme (ACE) inhibitors reduce morbidity and mortality in heart failure with reduced ejection fraction (HFrEF). Randomized controlled trials (RCTs) clearly establish the benefits of ACE inhibition in patients with mild, moderate, or severe symptoms of HF and in patients with or without coronary artery disease (128–133). ACE inhibitors can produce angioedema and should be given with caution to patients with low systemic blood pressures, renal insufficiency, or elevated serum potassium. ACE inhibitors also inhibit kininase and increase levels of bradykinin, which can induce cough but also may contribute to their beneficial effect through vasodilation.\nAngiotensin receptor blockers (ARBs) were developed with the rationale that angiotensin II production continues in the presence of ACE inhibition, driven through alternative enzyme pathways. ARBs do not inhibit kininase and are associated with a much lower incidence of cough and angioedema than ACE inhibitors; but like ACE inhibitors, ARBs should be given with caution to patients with low systemic blood pressure, renal insufficiency, or elevated serum potassium. Long-term therapy with ARBs produces hemodynamic, neurohormonal, and clinical effects consistent with those expected after interference with the renin-angiotensin system and have been shown in RCTs (134–137) to reduce morbidity and mortality, especially in ACE inhibitor–intolerant patients.\nIn ARNI, an ARB is combined with an inhibitor of neprilysin, an enzyme that degrades natriuretic peptides, bradykinin, adrenomedullin, and other vasoactive peptides. In an RCT that compared the first approved ARNI, valsartan/sacubitril, with enalapril in symptomatic patients with HFrEF tolerating an adequate dose of either ACE inhibitor or ARB, the ARNI reduced the composite endpoint of cardiovascular death or HF hospitalization significantly, by 20% (138). The benefit was seen to a similar extent for both death and HF hospitalization and was consistent across subgroups. The use The use of ARNI is associated with the risk of hypotension and renal insufficiency and may lead to angioedema, as well. ", "Source": "2017 ACC/AHA/HFSA Focused Update of the 2013 ACCF/AHA Guideline for the Management of Heart Failure Clyde <PERSON>, MD, MSC, MACC, FAHA, FHFSA, Chair <PERSON><PERSON>, <PERSON>, FACC, FAHA, Vice Chair"}, "Parent Child Validations": null}, {"Element Reference": 4205, "Name": "Non-Ischemic Cardiomyopathy Timeframe", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate the timeframe since the initial diagnosis of non-ischemic cardiomyopathy.", "Target Value": "The first value between birth and the first procedure in this admission", "Short Name": "NICMTimeframe", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001054", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Non-Ischemic Cardiomyopathy Timeframe", "Code": "100001028", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Less than 3 months", "Selection Definition": null, "Display Order": 1}, {"Name": "Non-Ischemic Cardiomyopathy Timeframe", "Code": "100000924", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "3 months or more", "Selection Definition": null, "Display Order": 2}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4200, "Parent Element Name": "Non-Ischemic Cardiomyopathy", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4210, "Name": "Non-Ischemic Guideline Directed Medical Therapy Maximum Dose", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if patient has been on guideline directed medical therapy for at least 3 months.\n\nNote(s):\nDocumentation of GDMT is the responsibility of the clinician and cannot be determined by the abstractor based on a list of medications. Documentation of GDMT maximum, optimum, appropriate dose, medical management/medical therapy for cardiomyopathy or a discussion in the medical record regarding medications as it relates to the patient's cardiomyopathy is acceptable for documenting GDMT. Some other acceptable statements are good neurohormonal therapy, managed appropriately on HF medications, and failed medically management of heart failure.", "Target Value": "The first value between birth and the first procedure in this admission", "Short Name": "NICMGDMTDose", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001055", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Non-Ischemic Guideline Directed Medical Therapy Maximum Dose", "Code": "100001037", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Yes (for 3 months)", "Selection Definition": "The patient has been prescribed guideline directed medical therapy for at least 3 months.\n\nThis may be coded if there is documentation of GDMT without a time frame, only if the abstractor can determine from the medical record that the patient has been on these exact medications for at least 3 months.", "Display Order": 1}, {"Name": "Non-Ischemic Guideline Directed Medical Therapy Maximum Dose", "Code": "100001036", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not Documented", "Selection Definition": "There is no documentation of guideline directed medical therapy being prescribed.", "Display Order": 2}, {"Name": "Non-Ischemic Guideline Directed Medical Therapy Maximum Dose", "Code": "100001035", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not Attempted", "Selection Definition": "Guideline directed medical therapy was not attempted on the patient.", "Display Order": 3}, {"Name": "Non-Ischemic Guideline Directed Medical Therapy Maximum Dose", "Code": "100001038", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Inability to complete", "Selection Definition": "The patient was unable to continue the guideline directed medical therapy for 3 months or the patient is on guideline directed medical therapy but it has been less than 3 months. Without a definitive time documented or the ability to determine the timeframe from the medical record, it would be captured as Inability to Complete. The duration of treatment would default to less than 3 months since the timeframe was not able to be determined. Inability to Complete would also include patients started on GDMT where it has been less than 3 months since therapy was started, patient refusal, an allergy or absolute contraindication.", "Display Order": 4}], "Ranges": null, "Definition": {"Title": "Non-Ischemic Guideline Directed Medical Therapy Maximum Dose", "Definition": "For heart failure in the setting of LV systolic dysfunction, this may require individualization but typically should include the combination of an angiotensin-converting enzyme inhibitor or angiotensin receptor blocker and beta blocker therapy adjusted to target doses as tolerated, with diuretics adjusted if/as needed to control fluid retention. In selected patients, the addition of aldosterone antagonists is appropriate. In addition, in some cases the use of a combination of hydralazine and nitrates may be used instead of an ACE inhibitor / angiotensin receptor blocker. Patients who are going to receive substantial benefit from medical treatment alone usually show some clinical improvement during the first 3 to 6 months. Medical therapy is also assumed to include adequate rate control for tachyarrhythmias, including atrial fibrillation. Therefore, it is recommended that GDMT be provided for at least 3 months before planned reassessment of LV function to consider device implantation. If LV function improves to the point where primary prevention indications no longer apply, then device implantation is not indicated. For stable ischemic heart disease, GDMT should include aspirin (or a thienopyridine if aspirin is not tolerated), statin therapy, angiotensin-converting enzyme inhibition (or an angiotensin receptor blocker) and the use of beta-blockers after myocardial infarction. Therapy for angina/ischemia should include at least 1 of the following medications: beta-blockers, calcium channel antagonists, or nitrates. Therapy should also be directed at optimizing the treatment of associated conditions such as diabetes and uncontrolled hypertension.", "Source": "1) <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. 2013 ACCF/AHA guideline for the management of ST-elevation myocardial infarction: a report of the American College of Cardiology Foundation/American Heart Association Task Force on Practice Guidelines. J Am Coll Cardiol 2013;61\n2) <PERSON>, <PERSON><PERSON>back RF, <PERSON>, et al. ACCF/HRS/AHA/ASE/HFSA/SCAI/SCCT/SCMR 2013 appropriate use criteria for implantable cardioverter-defibrillators and cardiac resynchronization therapy. J Am Coll Cardiol 2013;61:1318-68. doi: 10.1016/j.jacc.2012.12.017"}, "Parent Child Validations": [{"Parent Element Reference": 4200, "Parent Element Name": "Non-Ischemic Cardiomyopathy", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4215, "Name": "On Inotropic Support", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the patient is currently prescribed positive IV inotropic agents.\n\nNote(s): \nCode only if the patient is currently on a positive IV inotropic medication. Code On Inotropic Support, Seq. 4215, as No, for patients being administered IV Digoxin. Some examples of positive inotropic IV medications are Inamrinone, Milrinone, Norepinephrine, Dopamine and Dobutamine.", "Target Value": "The last value between birth and the first procedure in this admission", "Short Name": "InotropicSupport", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001061", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "On Inotropic Support", "Definition": "On inotropic support includes beta adrenergic receptor agonist in an attempt to achieve beneficial hemodynamic effects in the patient with systolic heart failure (HF).", "Source": "<PERSON><PERSON>G<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. 2013 ACCF/AHA guideline for the management of ST-elevation myocardial infarction: a report of the American College of Cardiology Foundation/American Heart Association Task Force on Practice Guidelines. J Am Coll Cardiol 2013;61"}, "Parent Child Validations": null}, {"Element Reference": 4220, "Name": "Prior <PERSON><PERSON> Arrest", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the patient experienced cardiac arrest due to arrhythmia.\n\nNote(s):\nCode 'No' if a patient experienced ventricular fibrillation caused by lead manipulation during the procedure, and it required defibrillation.\n\nCode 'No' to an appropriate ICD shock that aborts an arrest, whether for ventricular tachycardia or ventricular fibrillation", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "CardiacArrest", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "410429000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Pre-Arrival Cardiac Arrest", "Definition": "\"Sudden\" Cardiac arrest is the sudden cessation of cardiac activity. The victim becomes unresponsive with no normal breathing and no signs of circulation. If corrective measures are not taken rapidly, this condition progresses to sudden death. Cardiac arrest should be used to signify an event as described above that is reversed, usually by CPR and/or defibrillation or cardioversion or cardiac pacing.", "Source": "2013 ACCF/AHA key data elements and definitions for measuring the clinical management and outcomes of patients with acute coronary syndromes and coronary artery disease.  "}, "Parent Child Validations": null}, {"Element Reference": 4225, "Name": "Most Recent Cardiac Arrest Date", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate the date of the most recent cardiac arrest.\n\nNote(s):\nIf the month or day of the cardiac arrest is unknown, please code 01/01/YYYY. If the specific year is unknown in the current record, the year may be estimated based on timeframes found in prior medical record documentation (Example: If the patient had \"most recent cardiac arrest\" documented in a record from 2011, then the year 2011 can be utilized and coded as 01/01/2011).", "Target Value": "The last value between birth and the first procedure in this admission", "Short Name": "CardiacArrestDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "410429000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "Most Recent Cardiac Arrest Date (4225) must be Less than or Equal to Procedure Start Date and Time (7000)\n\nMost Recent Cardiac Arrest Date (4225) must be Greater than or Equal to Birth Date (2050)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4220, "Parent Element Name": "Prior <PERSON><PERSON> Arrest", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4230, "Name": "<PERSON><PERSON><PERSON>", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the cardiac arrest was a result of ventricular tachycardia as defined below.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "VTachArrest", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "410429000:42752001=25569003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Ventricular Tachycardia", "Definition": "Ventricular Tachycardia (VT) is a cardiac arrhythmia of 3 or more\nconsecutive complexes in duration emanating from the ventricles\nat a rate 100 bpm (cycle length: 600 ms).", "Source": " JACC Vol. 48, No. 11, 2006 ACC/AHA/HRS Clinical Data\nStandards December 5, 2006:2360-96"}, "Parent Child Validations": [{"Parent Element Reference": 4220, "Parent Element Name": "Prior <PERSON><PERSON> Arrest", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4235, "Name": "<PERSON><PERSON><PERSON>", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the cardiac arrest was a result of ventricular fibrillation as defined below.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "VFibArrest", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "410429000:42752001=71908006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "<PERSON><PERSON><PERSON>", "Definition": "Rapid, usually more than 300 bpm (cycle length: 180 ms or less), grossly irregular ventricular rhythm with marked variability in QRS cycle length, morphology, and amplitude.", "Source": "JACC Vol. 48, No. 11, 2006 ACC/AHA/HRS Clinical Data Standards December 5, 2006:2360-96"}, "Parent Child Validations": [{"Parent Element Reference": 4220, "Parent Element Name": "Prior <PERSON><PERSON> Arrest", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4240, "Name": "<PERSON><PERSON><PERSON>", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the cardiac arrest was a result of bradycardia.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "BradyArrest", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "410429000:42752001=48867003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4220, "Parent Element Name": "Prior <PERSON><PERSON> Arrest", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4245, "Name": "Ventricular Tachycardia", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the patient has a history of ventricular tachycardia (VT). To qualify as history, VT should be spontaneous and not induced.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "VT", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "25569003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Ventricular Tachycardia", "Definition": "Ventricular Tachycardia (VT) is a cardiac arrhythmia of 3 or more consecutive complexes in duration emanating from the ventricles at a rate 100 bpm (cycle length: 600 ms).", "Source": "JACC Vol. 48, No. 11, 2006 ACC/AHA/HRS Clinical Data Standards December 5, 2006:2360-96"}, "Parent Child Validations": null}, {"Element Reference": 4250, "Name": "Most Recent Ventricular Tachycardia Date", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate the date of the most recent ventricular tachycardia.\n\nNote(s):\nIf the month or day of the ventricular tachycardia is unknown, please code 01/01/YYYY. If the specific year is unknown in the current record, the year may be estimated based on timeframes found in prior medical record documentation (Example: If the patient had \"most recent ventricular tachycardia\" documented in a record from 2011, then the year 2011 can be utilized and coded as 01/01/2011).\n\nCode the most recent and significant episode of VT. When the patient has a history of VT documented in the medical record by the clinician and a time frame is unable to be determined from prior medical records or by consulting with the clinician, please code the VT as having occurred 5 years ago. For example: If the physician documents on 05/01/2020, that the patient has a history of VT, please code Most Recent VT Date, Seq. 4250, as 05/01/2015.", "Target Value": "The last value between birth and the first procedure in this admission", "Short Name": "VTDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "25569003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "Most Recent Ventricular Tachycardia Date (4250) must be Greater than or Equal to Birth Date (2050)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4245, "Parent Element Name": "Ventricular Tachycardia", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4275, "Name": "Ventricular Tachycardia Type", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate the type of ventricular tachycardia.\n\nNote(s):\nWhen only VT is documented code VT Type, Seq. 4275 as Non-sustained VT. If the VT is documented as sustained VT, code VT Type, Seq. 4275, as sustained monomorphic VT. If there is documentation of VT treated appropriately with ATP or Shock therapy or VT Arrest and the VT type is unknown, code as sustained monomorphic VT. If there are multiple episodes of VT, code the most significant episode of VT.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "VTType", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001124", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Ventricular Tachycardia Type", "Code": "444658006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Non Sustained VT", "Selection Definition": "Non-sustained or un-sustained ventricular tachycardia (VT) is three or more beats in duration, terminating spontaneously in <30 seconds. Non-sustained VT can be monomorphic or polymorphic.", "Display Order": 1}, {"Name": "Ventricular Tachycardia Type", "Code": "251158004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Monomorphic VT", "Selection Definition": "Sustained monomorphic ventricular tachycardia (VT) is VT >30 seconds in duration or requiring termination due to hemodynamic compromise in <30 seconds that has a stable, single QRS morphology.", "Display Order": 2}, {"Name": "Ventricular Tachycardia Type", "Code": "251159007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Polymorphic VT", "Selection Definition": "Sustained polymorphic ventricular tachycardia (VT) is VT >30 seconds in duration or requiring termination due to hemodynamic compromise in <30 seconds that has a changing or multiform QRS morphology at cycle length >180 milliseconds.", "Display Order": 3}, {"Name": "Ventricular Tachycardia Type", "Code": "100001127", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Monomorphic and Polymorphic VT", "Selection Definition": "The patient has a history of both sustained monomorphic and sustained polymorphic ventricular tachycardia.", "Display Order": 4}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4245, "Parent Element Name": "Ventricular Tachycardia", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4255, "Name": "Ventricular Tachycardia Occurred Post Cardiac Surgery", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the ventricular tachycardia occurred within the 48 hours after cardiac surgery.\n\nNote(s):\nOccurred Post Cardiac Surgery, Seq.4255, refers to open chest surgery, for example: CABG or Valve replacement. If there are multiple episodes of VT, code the most significant episode of VT.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "VTPostCardiacSurgery", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001123", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4245, "Parent Element Name": "Ventricular Tachycardia", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4260, "Name": "Bradycardia Dependent Ventricular Tachycardia", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the ventricular tachycardia is bradycardia dependent.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000946", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4245, "Parent Element Name": "Ventricular Tachycardia", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4265, "Name": "Ventricular Tachycardia Reversible Cause", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the ventricular tachycardia was deemed to be a result of a reversible cause. This could include, but is not limited to, drug abuse or electrolyte imbalance.\n\nNote(s):\nIf there are multiple episodes of VT, code the most significant episode of VT.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "VTReverseCause", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001126", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Ventricular Tachycardia Reversible Cause", "Definition": "Definition of ventricular tachycardia due to a reversible cause.\nThe most common putative reversible causes of arrest are acute ischemia and electrolyte imbalance. Other common potential causes to which cardiac arrest is attributed include proarrhythmic effects of antiarrhythmic drugs (see supporting references).\n\n1) Electrolyte abnormalities, including hypokalemia and hypomagnesemia, facilitate development of VT in predisposed patients receiving antiarrhythmic agents and other drugs associated with the LQTS. However, hypokalemia can also result from cardiac arrest and should not otherwise be assumed to be the cause of cardiac arrest, except under unusual circumstances.(see reference below) Correction of hypokalemia does not affect inducibility of monomorphic VT occurring after MI. Electrolyte abnormalities should not be assumed to be the cause of cardiac arrest, except in the presence of drug-induced LQTS. \n\n2) Drugs: In patients who develop polymorphic VT in association with drug-induced QT prolongation, withdrawal of the offending antiarrhythmic or other agent (e.g., antipsychotic) is usually sufficient to prevent arrhythmia recurrence. If ventricular function is normal, no therapy beyond drug withdrawal, avoidance of future drug exposure, and correction of electrolyte abnormalities is necessary. However, if ventricular function is abnormal, cardiac arrest or syncope should not be attributed solely to antiarrhythmic drugs, and evaluation and treatment should be similar to patients experiencing such events in the absence of antiarrhythmic drugs. Occasionally, patients develop monomorphic sustained VT only in the presence of antiarrhythmic drugs without QT prolongation. In such cases, it may appear that the development of spontaneous VT is dependent on drug administration. In most patients exhibiting this behavior, the monomorphic VT is inducible by EP testing in the absence of antiarrhythmic drugs.", "Source": "ACC/AHA/ESC 2006 Guidelines for Management of Patients With Ventricular Arrhythmias"}, "Parent Child Validations": [{"Parent Element Reference": 4245, "Parent Element Name": "Ventricular Tachycardia", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4270, "Name": "Ventricular Tachycardia with Hemodynamic Instability", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the patient demonstrated hemodynamic instability while having episodes of sustained or non-sustained ventricular tachycardia.\n\nNote(s):\nHemodynamic instability can include periods of reduced, unstable, or abnormal blood pressure with near syncope, or episodes of syncope. It creates a state of hypoperfusion that does not support normal organ perfusion or function.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "HemoInstability", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001125", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4245, "Parent Element Name": "Ventricular Tachycardia", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 14719, "Name": "Ventricular Fibrillation", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the patient had a history of ventricular fibrillation not due to reversible cause.", "Target Value": "The last value between birth and the first procedure in this admission", "Short Name": "VFib", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "NULL", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "71908006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Ventricular Fibrillation", "Definition": "Rapid, usually more than 300 bpm (cycle length: 180 ms or less), grossly irregular ventricular rhythm with marked variability in QRS cycle length, morphology, and amplitude.", "Source": "JACC Vol. 48, No. 11, 2006 ACC/AHA/HRS Clinical Data Standards December 5, 2006:2360-96"}, "Parent Child Validations": null}, {"Element Reference": 14720, "Name": "Ventricular Fibrillation Date", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate the date of the ventricular fibrillation.", "Target Value": "The last value between birth and the first procedure in this admission", "Short Name": "VFibDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "NULL", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "71908006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "Ventricular Fibrillation Date (14720) must be Greater than or Equal to Birth Date (2050)\n\nVentricular Fibrillation Date (14720) must be Less than or Equal to Discharge Date (10100)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 14719, "Parent Element Name": "Ventricular Fibrillation", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4280, "Name": "Syncope", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the patient has a history of syncope, due to, or highly suspicious for, arrhythmic origin.\n\nNote(s):\nCode 'No' if the patient reports pre-syncope/near syncope (as described by dizziness, lightheadedness, feeling faint, or graying out).", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "Syncope", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "271594007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Syncope", "Definition": "Syncope presents with an abrupt, transient, complete loss of consciousness, associated with inability to maintain postural tone, with rapid and spontaneous recovery. ", "Source": "2017 ACC/AHA/HRS Guideline for the Evaluation and Management of Patients With Syncope: A Report of the ACC/AHA Task Force on Clinical Practice Guidelines and the HRS. <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>  "}, "Parent Child Validations": null}, {"Element Reference": 4285, "Name": "Coronary Artery Disease", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the patient has a history of coronary artery disease (CAD).", "Target Value": "Any occurrence between birth and the procedure", "Short Name": "CAD", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "53741008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Coronary Artery Disease", "Definition": "A history of any of the following:\n- Coronary artery stenosis >=50% (by cardiac catheterization or other modality or of direct imaging of the coronary arteries)\n- Previous CABG surgery\n- Previous PCI\n- Previous MI", "Source": "ACCF/AHA 2011 Key Data Elements and Definitions of a Base Cardiovascular Vocabulary for Electronic Health Records (JACC 2011;58;202-222)."}, "Parent Child Validations": null}, {"Element Reference": 4290, "Name": "Prior Myocardial Infarction", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the patient has ever been diagnosed with a myocardial infarction.\n\nNote(s):\nA myocardial infarction is a clinical diagnosis which must be documented by a provider.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "PriorMI", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "22298006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Myocardial Infarction/Prior MI", "Definition": "Criteria for acute myocardial infarction: \nThe term acute myocardial infarction (MI) should be used when there is evidence of myocardial necrosis in a clinical setting consistent with acute myocardial ischemia. Under these conditions any one of the following criteria meets the diagnosis for MI:\n- Detection of a rise and/or fall of cardiac biomarker values [preferably cardiac troponin (cTn) with at least one value above the 99th percentile upper reference limit (URL) and with at least one of the following:\n\nSymptoms of ischemia.\nNew or presumed new significant ST-segment-T wave (ST-T) changes or new left bundle branch block (LBBB). Development of pathological Q waves in the ECG.\nImaging evidence of new loss of viable myocardium or new regional wall motion abnormality. Identification of an intracoronary thrombus by angiography or autopsy.\n\n- Cardiac death with symptoms suggestive of myocardial ischemia and presumed new ischemic ECG changes or new LBBB, but death occurred before cardiac biomarkers were obtained, or before cardiac biomarker values would be increased.\n\n- Percutaneous coronary intervention (PCI) related MI is arbitrarily defined by elevation of cTn values (>5 x 99th percentile URL) in patients with normal baseline values (99th percentile URL) or a rise of cTn values >20% if the baseline values are elevated and are stable or falling. In addition, either (i) symptoms suggestive of myocardial ischemia or (ii) new ischemic ECG changes or (iii) angiographic findings consistent with a procedural complication or (iv) imaging demonstration of new loss of viable myocardium or new regional wall motion abnormality are required.\n\n- Stent thrombosis associated with MI when detected by coronary angiography or autopsy in the setting of myocardial ischemia and with a rise and/or fall of cardiac biomarker values with at least one value above the 99th percentile URL.\n\n- Coronary artery bypass grafting (CABG) related MI is arbitrarily defined by elevation of cardiac biomarker values (>10 x 99th percentile URL) in patients with normal baseline cTn values (99th percentile URL). In addition, either (i) new pathological Q waves or new LBBB, or (ii) angiographic documented new graft or new native coronary artery occlusion, or (iii) imaging evidence of new loss of viable myocardium or new regional wall motion abnormality.\n\nAny one of the following criteria meets the diagnosis for prior MI:\n- Pathological Q waves with or without symptoms in the absence of non-ischemic causes.\n- Imaging evidence of a region of loss of viable myocardium that is thinned and fails to contract, in the absence of a non-ischemic cause.\n- Pathological findings of a prior MI.", "Source": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>fe AS, et al. Third Universal Definition of Myocardial Infarction. J Am Coll Cardiol. 2012;60(16):1581-1598. doi:10.1016/j.jacc.2012.08.001."}, "Parent Child Validations": null}, {"Element Reference": 4295, "Name": "Most Recent MI Date", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate the date of the most recent myocardial infarction.\n\nNote(s):\nWhen the patient has a history of an 'old or 'remote' MI documented in the medical record by the clinician and a time frame is unable to be determined from prior medical records or by consulting with the clinician, please code the MI as having occurred 5 years ago. For example: If the physician documents on 05/01/2020, that the patient has a history of MI, please code Most Recent MI Date, Seq. 4250, as 05/01/2015.\n\nIf the month or day of the myocardial infarction is unknown, please code 01/01/YYYY. If the specific year is unknown in the current record, the year may be estimated based on timeframes found in prior medical record documentation (Example: If the patient had \"most recent myocardial infarction\" documented in a record from 2011, then the year 2011 can be utilized and coded as 01/01/2011).", "Target Value": "The last value between birth and the first procedure in this admission", "Short Name": "PriorMIDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "22298006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "Most Recent MI Date (4295) must be Greater than or Equal to Birth Date (2050)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4290, "Parent Element Name": "Prior Myocardial Infarction", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4300, "Name": "Coronary Angiography", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the patient has had a prior diagnostic coronary angiography.\n\nNote(s):\nWhen a patient has had a CABG/PCI in the past and there is not a repeat coronary angiography after the CABG/PCI, please code as follows:\nCoronary Angiography, Seq. 4300 will be \"Yes\" as the patient had to have an angiogram prior to the CABG/PCI.\nResults of Angiography, Seq. 4310 will be \"Significant disease\" as the surgery/PCI would not have been performed.\nRevascularization performed, Seq. 4315, will be \"Yes\" as the patient had a CABG/PCI.\nRevascularization Outcome, Seq. 4320, will be complete as the clinician would have addressed all revascularizable vessels. \n", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "CoronaryAngio", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "33367005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Coronary Angiography", "Definition": "Coronary angiography is defined as the passage of a catheter into the aortic root or other great vessels for angiography of the native coronary arteries or bypass grafts supplying native coronary arteries. This element would NOT include noninvasive CT angiography.", "Source": "American College of Cardiology and American  Heart Association Task Force on Clinical Data Standards (Writing Committee to Develop Artery Disease: A Report of the American College of Cardiology Foundation/American.\nManagement and Outcomes of Patients With Acute Coronary Syndromes and Coronary 2013 ACCF/AHA Key Data Elements and Definitions for Measuring the Clinical Circulation. 2013;127:1052-1089; originally published online January 28, 2013;"}, "Parent Child Validations": null}, {"Element Reference": 4305, "Name": "Performed After Most Recent Cardiac Arrest", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the coronary angiography was performed after the most recent cardiac arrest.\n\nNote(s):\nIf the patient has had a history of cardiac arrest, then the response should be based on whether the most recent angiogram was performed after the most recent cardiac arrest.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "PerfAfterRecentCA", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001201", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4220, "Parent Element Name": "Prior <PERSON><PERSON> Arrest", "Parent Element Selection Name": "Yes"}, {"Parent Element Reference": 4300, "Parent Element Name": "Coronary Angiography", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4310, "Name": "Results of Angiography", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate the result of the coronary angiography performed.", "Target Value": "Any occurrence between birth and the procedure", "Short Name": "CoronaryAngioResults", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "365853002:418775008=77343006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Results of Angiography", "Code": "100000641", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "No significant disease", "Selection Definition": "There was <50% stenosis in the left main coronary artery and <70% in all major coronary artery branches >= 2.0 mm.", "Display Order": 1}, {"Name": "Results of Angiography", "Code": "100001223", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Significant disease", "Selection Definition": "There was >= 50% stenosis in the left main coronary artery and/or >=70% stenosis in any major coronary artery (>= 2.0 mm).", "Display Order": 2}, {"Name": "Results of Angiography", "Code": "100001220", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Non-revascularizable significant disease", "Selection Definition": "The patient is not a candidate for revascularization of their significant coronary artery disease.", "Display Order": 3}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4300, "Parent Element Name": "Coronary Angiography", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4315, "Name": "Revascularization Performed", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if an attempt at revascularization of the coronary artery disease was performed.\n\nNote(s):\nThe intent is to evaluate the status of the arteries and / or graphs at the time of the ICD implant. Code the status of the vessels/graphs at the time of the most recent catheterization.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "Revasc<PERSON>erf", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "81266008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4310, "Parent Element Name": "Results of Angiography", "Parent Element Selection Name": "Significant disease"}]}, {"Element Reference": 4320, "Name": "Revascularization Outcome", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate the outcome of the revascularization.", "Target Value": "The last value between birth and current procedure", "Short Name": "RevascOutcome", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001224", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Revascularization Outcome", "Code": "100001221", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Complete revascularization", "Selection Definition": "Residual stenosis <50% in all revascularizable diseased coronary arteries.", "Display Order": 1}, {"Name": "Revascularization Outcome", "Code": "100001222", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Incomplete revascularization", "Selection Definition": "Not all revascularizable diseased coronary arteries resulted in <50% stenosis.", "Display Order": 2}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4315, "Parent Element Name": "Revascularization Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4325, "Name": "Prior Cardiovascular Implantable Electronic Device", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the patient currently has a permanent pacemaker or defibrillator present or if they had at any time in the past.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "PriorCIED", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000954", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 4355, "Name": "On Heart Transplant Waiting List", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the patient is currently on a waiting list to receive a heart transplant.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "TransplantWaitList", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "471300007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 4360, "Name": "Candidate for Transplant", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the patient has been identified as a candidate for a heart transplant or is actively under consideration by an advanced heart failure/cardiac team.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "TransplantCandidate", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000821", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Candidate for Transplant", "Definition": "Refer to the source for the supporting definition", "Source": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, et al. Listing criteria for heart transplantation: International Society for Heart and Lung Transplantation guidelines for the care of cardiac transplant candidates-2006. J Heart Lung Transplant. 2006;25:1024-42"}, "Parent Child Validations": null}, {"Element Reference": 14751, "Name": "Candidate for VAD", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the patient has been identified as a candidate for any ventricular assist device (LVAD, RVAD or BiVAD) as a patient with refractory, end-stage heart failure.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "CandidateforVAD", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "NULL", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000002045", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Candidate for VAD", "Definition": "Refer to the source for the supporting definition.", "Source": "<PERSON><PERSON>, <PERSON>, <PERSON>, et al. 2009 focused update: ACCF/AHA guidelines for the diagnosis and management of heart failure in adults: a report of the American College of Cardiology/American Heart Association Task Force on Practice Guidelines. J Am Coll Cardiol 2009;53:1343-82"}, "Parent Child Validations": null}, {"Element Reference": 14752, "Name": "Currently on VAD", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the patient is currently on a ventricular assist device (LVAD, RVAD or BiVAD) as a patient with refractory, end-stage heart failure.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "CurrentlyonVAD", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "NULL", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000002046", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 4399, "Name": "Atrial Fibrillation", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the patient has a history of atrial fibrillation.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "AFib", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "49436004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 4400, "Name": "Atrial Fibrillation Classification", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate the type of atrial fibrillation experienced by the patient.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "AFibClass", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000935", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Atrial Fibrillation Classification", "Code": "26593000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Paroxysmal", "Selection Definition": "AF that terminates spontaneously or with intervention within 7 days of onset. Episodes may recur with variable frequency.", "Display Order": 1}, {"Name": "Atrial Fibrillation Classification", "Code": "62459000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Persistent", "Selection Definition": "Continuous AF that is sustained >7 days or with electrical or pharmacological termination.", "Display Order": 2}, {"Name": "Atrial Fibrillation Classification", "Code": "100001029", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Long-standing Persistent", "Selection Definition": "Continuous AF of >12 months duration.", "Display Order": 3}, {"Name": "Atrial Fibrillation Classification", "Code": "6934004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Permanent", "Selection Definition": "The term “permanent AF” is used when the patient and clinician make a joint decision to stop further attempts to restore and/or maintain sinus rhythm.\n\n- Acceptance of AF represents a therapeutic attitude on the part of the patient and clinician rather than an inherent pathophysiological attribute of the AF.\n\n- Acceptance of AF may change as symptoms, the efficacy of therapeutic interventions, and patient and clinician preferences evolve.", "Display Order": 4}], "Ranges": null, "Definition": {"Title": "Atrial Fibrillation Classification", "Definition": "Atrial Fibrillation is a supraventricular tachyarrhythmia with uncoordinated atrial activation and consequently ineffective atrial contraction. \n\nElectrocardiogram (ECG) characteristics include: \n1) irregular R-R intervals (when atrioventricular [AV] conduction is present),\n2) absence of distinct repeating P waves, and\n3) irregular atrial activity.\n\nAtrial Fibrillation can be further characterized as:\n\n- Paroxysmal AF is defined as AF that terminates spontaneously or with intervention within seven days of onset. Episodes may recur with variable frequency.\n- Persistent AF is defined as AF that fails to self-terminate within seven days. Episodes often require pharmacologic or electrical cardioversion to restore sinus rhythm.\n- Long-standing persistent AF is defined as AF that has lasted for more than 12 month\n-Permanent AF is defined as when the patient and clinician make a joint decision to stop further attempts to restore and/or maintain sinus rhythm. Acceptance of AF represents a therapeutic attitude on the part of the patient and clinician rather than an inherent pathophysiological attribute of AF. Acceptance of AF may change as symptoms, efficacy of therapeutic interventions, and patient and clinician preferences evolve.", "Source": "January CT, <PERSON><PERSON>, <PERSON><PERSON>, et al. 2014 AHA/ACC/HRS Guideline for the Management of Patients With Atrial Fibrillation: A Report of the American College of Cardiology/American Heart Association Task Force on Practice Guidelines and the Heart Rhythm Society. J Am Coll Cardiol 2014. DOI: 10.1016/j.jacc.2014.03.022"}, "Parent Child Validations": [{"Parent Element Reference": 4399, "Parent Element Name": "Atrial Fibrillation", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4405, "Name": "Plans for Cardioversion of Atrial Fibrillation", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if there is a planned cardioversion for atrial fibrillation.\n\nNote(s):\n1. Code No for a history of cardioversion.\n2. Code Yes, if the patient was in AFib and cardioverted prior to the start of the first generator implant procedure in this admission.\n3. Code Yes if the patient is scheduled for a cardioversion.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "AFibFlutterCardioPlans", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000934", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Plans for Cardioversion of Atrial Fibrillation", "Definition": "A cardioversion is performed using a synchronized shock and/or IV antiarrhythmic medications.", "Source": null}, "Parent Child Validations": [{"Parent Element Reference": 4399, "Parent Element Name": "Atrial Fibrillation", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4490, "Name": "Paroxysmal SVT History", "Section Display Name": "C. History and Risk Factors", "Coding Instructions": "Indicate if the patient has a history of paroxysmal supraventricular tachycardia (SVT).\n\nNote(s):\nCode 'Yes' if the patient has a history of atrial flutter, atrioventricular nodal reentrant tachycardia (AVNRT), atrioventricular reciprocating tachycardia (AVRT) e.g. <PERSON>-<PERSON>-White syndrome, atrial tachycardia, junctional tachycardia, and / or multifocal atrial tachycardia. However, it will not include paroxysmal atrial fibrillation.", "Target Value": "Any occurrence between birth and the procedure", "Short Name": "ParoxySVTHistory", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "67198005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "C. History and Risk Factors", "Section Display Name": "Other History", "Section Code": "OtherHistory", "Section Type": "Section", "Cardinality": "0..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 4495, "Name": "Prior Percutaneous Coronary Intervention", "Section Display Name": "Other History", "Coding Instructions": "Indicate if the patient had a percutaneous coronary intervention (PCI), prior to this admission.", "Target Value": "Any occurrence between birth and arrival at this facility", "Short Name": "PriorPCI", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "415070008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Percutaneous Coronary Intervention", "Definition": "A percutaneous coronary intervention (PCI) is the placement of an angioplasty guide wire, balloon, or other device (e.g. stent, atherectomy, brachytherapy, or thrombectomy catheter) into a native coronary artery or coronary artery bypass graft for the purpose of mechanical coronary revascularization.", "Source": "Medline Plus, 2017 by Merriam-Webster, Incorporated"}, "Parent Child Validations": null}, {"Element Reference": 4500, "Name": "Most Recent Percutaneous Coronary Intervention Date", "Section Display Name": "Other History", "Coding Instructions": "Indicate the date of the most recent percutaneous coronary intervention (PCI) that the patient received prior to this admission.\n\nNote(s):\nIf the month or day of the PCI is unknown, please code 01/01/YYYY. If the specific year is unknown in the current record, the year may be estimated based on timeframes found in prior medical record documentation. For example: If the patient had \"most recent PCI\" documented in a record from 2011, then the year 2011 can be utilized and coded as 01/01/2011. \n\nWhen the patient has a history of an 'old or 'remote' PCI documented in the medical record by the clinician and a time frame is unable to be determined from prior medical records or by consulting with the clinician, please code the PCI as having occurred 5 years ago. For example: If the physician documents on 05/01/2020, that the patient has a history of PCI, please code Most Recent PCI Date, Seq. 4250, as 05/01/2015.", "Target Value": "Any occurrence between birth and arrival at this facility", "Short Name": "PriorPCIDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "415070008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "Most Recent Percutaneous Coronary Intervention Date (4500) must be Greater than or Equal to Birth Date (2050)\n\nMost Recent Percutaneous Coronary Intervention Date (4500) must be Less than or Equal to Arrival Date (3000)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4495, "Parent Element Name": "Prior Percutaneous Coronary Intervention", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4510, "Name": "Cardiomyopathy prior to PCI", "Section Display Name": "Other History", "Coding Instructions": "Indicate if the patient had pre-existing cardiomyopathy prior to the PCI procedure.\n\nNote(s): \nIf there is no documentation regarding pre-existing cardiomyopathy, code No. If the patient has documentation of an LVEF < 40% as well as heart failure prior to the PCI, code Yes.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "PriorPCICardioPresent", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000952", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4495, "Parent Element Name": "Prior Percutaneous Coronary Intervention", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4505, "Name": "Prior PCI Elective", "Section Display Name": "Other History", "Coding Instructions": "Indicate if the prior PCI was performed as an elective procedure and was not performed in an urgent or emergent situation. For stable inpatients, the procedure was performed during the hospitalization for convenience and ease of scheduling and NOT because the patient's clinical situation demanded the procedure prior to discharge.\n\nNote(s):\nIf the facility is unable to determine whether the procedure was elective, leave blank.", "Target Value": "Any occurrence between birth and arrival at this facility", "Short Name": "PriorPCIElective", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000997", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4495, "Parent Element Name": "Prior Percutaneous Coronary Intervention", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4515, "Name": "Prior Coronary Artery Bypass Graft", "Section Display Name": "Other History", "Coding Instructions": "Indicate if the patient had coronary artery bypass graft (CABG) surgery prior to this admission.", "Target Value": "Any occurrence between birth and arrival at this facility", "Short Name": "PriorCABG", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "232717009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 4520, "Name": "Most Recent Coronary Artery Bypass Graft Date", "Section Display Name": "Other History", "Coding Instructions": "Indicate the date of the most recent CABG that the patient received prior to this admission.\n\nNote(s):\nWhen the patient has a history of an 'old or 'remote' CABG documented in the medical record by the clinician and a time frame is unable to be determined from prior medical records or by consulting with the clinician, code the CABG as having occurred 5 years ago. For example: If the physician documents on 05/01/2020, that the patient has a history of CABG, code Most Recent CABG Date, Seq. 4520, as 05/01/2015.\n\nIf the month or day of the CABG is unknown, please code 01/01/YYYY. If the specific year is unknown in the current record, the year may be estimated based on timeframes found in prior medical record documentation (Example: If the patient had \"most recent CABG\" documented in a record from 2011, then the year 2011 can be utilized and coded as 01/01/2011).", "Target Value": "Any occurrence between birth and arrival at this facility", "Short Name": "PriorCABGDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "232717009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "Most Recent Coronary Artery Bypass Graft Date (4520) must be Greater than or Equal to Birth Date (2050)\n\nMost Recent Coronary Artery Bypass Graft Date (4520) must be Less than or Equal to Arrival Date (3000)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4515, "Parent Element Name": "Prior Coronary Artery Bypass Graft", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4525, "Name": "Prior CABG Elective", "Section Display Name": "Other History", "Coding Instructions": "Indicate if the prior CABG was performed as an elective procedure and was not performed in an urgent or emergent situation. For stable inpatients, the procedure was performed during the hospitalization for convenience and ease of scheduling and NOT because the patient's clinical situation demanded the procedure prior to discharge.\n\nNote(s):\nIf the facility is unable to determine whether the procedure was elective, leave blank.", "Target Value": "Any occurrence between birth and arrival at this facility", "Short Name": "PriorCABGElective", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000996", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4515, "Parent Element Name": "Prior Coronary Artery Bypass Graft", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4530, "Name": "Cardiomyopathy prior to Coronary Artery Bypass Graft", "Section Display Name": "Other History", "Coding Instructions": "Indicate if the patient had pre-existing cardiomyopathy prior to the CABG procedure.\n\nNote(s): \nIf there is no documentation regarding pre-existing cardiomyopathy, code No. If the patient has documentation of an LVEF < 40% as well as heart failure prior to the PCI, code Yes.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "PriorCABGCardioPresent", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000951", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4515, "Parent Element Name": "Prior Coronary Artery Bypass Graft", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 14722, "Name": "Prior Aortic Valve Procedure", "Section Display Name": "Other History", "Coding Instructions": "Indicate if the patient had a prior aortic valve procedure.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "Prior_AVProc", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "NULL", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001755", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 14725, "Name": "Prior Aortic Valve Procedure Date", "Section Display Name": "Other History", "Coding Instructions": "Indicate the date of the most recent prior aortic valve procedure.\n\nNote(s):\nIf the month or day of the Aortic Valve Procedure is unknown, please code 01/01/YYYY. If the specific year is unknown in the current record, the year may be estimated based on timeframes found in prior medical record documentation (Example: If the patient had \"aortic valve procedure\" documented in a record from 2018, then the year 2018 can be utilized and coded as 01/01/2018).", "Target Value": "The last value between birth and the first procedure in this admission", "Short Name": "AVP_Date", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "NULL", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001755", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Prior Aortic Valve Procedure Date (14725) must be Greater than or Equal to Birth Date (2050)\n\nPrior Aortic Valve Procedure Date (14725) must be Less than or Equal to Discharge Date (10100)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 14722, "Parent Element Name": "Prior Aortic Valve Procedure", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 14726, "Name": "Prior Aortic Valve Procedure Elective", "Section Display Name": "Other History", "Coding Instructions": "Indicate if the prior aortic valve procedure was performed as an elective procedure and was not performed in an urgent or emergent situation. For stable inpatients, the procedure was performed during the hospitalization for convenience and ease of scheduling and NOT because the patient's clinical situation demanded the procedure prior to discharge.", "Target Value": "The last value between birth and the first procedure in this admission", "Short Name": "AVP_Elective", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "NULL", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "118798003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 14722, "Parent Element Name": "Prior Aortic Valve Procedure", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4535, "Name": "Primary Valvular Heart Disease", "Section Display Name": "Other History", "Coding Instructions": "Indicate if the patient has a history of primary valvular heart disease that is moderately severe or severe.\n\nNote(s):\nLack of supporting documentation as evidence that the valve replacement was done for the purposes of treating Primary Valvular Heart Disease, code \"No\".\n", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "PrimaryValvularHD", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "368009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Valvular Disease", "Definition": "Primary valvular heart disease is defined by heart disease that is primarily due to a valvular defect or abnormality, and is classified as:\n1. Moderately severe or severe, or 3+ or 4+ aortic insufficiency.\n2. Moderately severe or severe, or 3+ or 4+ mitral insufficiency with echocardiographic evidence that mitral insufficiency is a primary abnormality and not secondary to ventricular dilation.\n3. Moderately severe or severe aortic stenosis defined by estimated aortic valve area by catheterization or Doppler echocardiography of <=1.0 cm2.\n4. Moderately severe or severe mitral stenosis defined by estimated mitral valve area by catheterization or Doppler echocardiography of <1.0 cm2.\n5. Moderately severe or severe pulmonic or tricuspid valve disease that is known to be a primary abnormality.", "Source": null}, "Parent Child Validations": null}, {"Element Reference": 4540, "Name": "Other Structural Abnormalities", "Section Display Name": "Other History", "Coding Instructions": "Indicate if the patient has any other structural abnormality of the heart, ventricles or great vessels (excluding primary valvular heart disease). These conditions are frequently found in imaging reports such as echo, MRI, CAT scan, MUGA or other imaging studies.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "OtherStructAbn", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000949", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 4545, "Name": "Structural Abnormality Type", "Section Display Name": "Other History", "Coding Instructions": "Indicate the structural abnormality type(s).", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "StructAbnType", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000949", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Structural Abnormality Type", "Code": "87878005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "LV structural abnormality associated with risk for sudden cardiac arrest", "Selection Definition": "Left ventricular structural abnormalities including but not limited to left ventricular aneurysm, LV non-compaction syndrome that put the patient at risk for sudden cardiac arrest.", "Display Order": 1}, {"Name": "Structural Abnormality Type", "Code": "233873004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Hypertrophic cardiomyopathy (HCM) with high risk features", "Selection Definition": null, "Display Order": 2}, {"Name": "Structural Abnormality Type", "Code": "100001018", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Infiltrative", "Selection Definition": "Infiltrative structural abnormalities including but not limited to amyloidosis, sarcoidosis, giant cell myocarditis, and Chagas disease.", "Display Order": 3}, {"Name": "Structural Abnormality Type", "Code": "281170005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Arrhythmogenic right ventricular cardiomyopathy (ARVC)", "Selection Definition": null, "Display Order": 4}, {"Name": "Structural Abnormality Type", "Code": "13213009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Congenital heart disease associated with sudden cardiac arrest", "Selection Definition": "Congenital heart disease including but not limited to Tetralogy of Fallot and Ventricular Septal Defect that put the patient at risk for sudden cardiac arrest.", "Display Order": 5}], "Ranges": null, "Definition": {"Title": "Structural Abnormality Type", "Definition": "Left Ventricular Structural Abnormality Associated with Risk for Sudden Cardiac Arrest - Refer to the source for the supporting definition.\n\nHypertrophic Cardiomyopathy with High Risk Features:\nHigh risk features include:\n- Cardiac arrest (VF)\n- Spontaneous sustained VT\n- Family history of premature sudden death\n- Unexplained syncope\n- LV thickness greater than or equal to 30 mm\n- Abnormal exercise BP\n- Nonsustained spontaneous VT\n- AF\n- Myocardial ischemia\n- LV outflow obstruction\n- High-risk mutation\n- Intense (competitive) physical exertion", "Source": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, et al. ACC/AHA/ESC 2006 guidelines for management of patients with ventricular arrhythmias and the prevention of sudden cardiac death: a report of the American College of Cardiology/American Heart Association Task Force and the European Society of Cardiology Committee for Practice Guidelines (Writing Committee to Develop Guidelines for Management of Patients With Ventricular Arrhythmias and the Prevention of Sudden Cardiac Death). Circulation. 2006;114:e385-e484."}, "Parent Child Validations": [{"Parent Element Reference": 4540, "Parent Element Name": "Other Structural Abnormalities", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4550, "Name": "Cerebrovascular Disease", "Section Display Name": "Other History", "Coding Instructions": "Indicate if the patient has a history of cerebrovascular disease.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "PriorCVD", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "62914000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Cerebrovascular Disease", "Definition": "Cerebrovascular Disease documented by any one of the following:\n\n1). Cerebrovascular Accident (CVA): An acute episode of focal or global neurological dysfunction caused by brain, spinal cord, or retinal vascular injury as a result of hemorrhage or infarction. The duration of > 24 h has been used as an operational definition of persisting symptoms of stroke rather than TIA, based mostly on consensual practice rather than objective evidence.\n\n2). Transient Ischemic Attack (TIA): Transient episode of neurological dysfunction caused by focal or global\nbrain, spinal cord, or retinal ischemia without acute infarction Note: The distinction between a TIA and ischemic stroke is the presence of infarction. The unifying concept driving the definition is that stroke is a marker of potentially disabling vascular brain injury. The duration of > 24 h has been used as an operational definition of persisting symptoms of stroke rather than TIA, based mostly on consensual practice rather than objective evidence. \n\n3). Non-invasive/invasive carotid test with > 79% occlusion. Noninvasive or invasive arterial imaging test: Noninvasive or invasive arterial imaging test demonstrating > 50% stenosis of any of the major extracranial or intracranial vessels to the brain\n\n4). Previous carotid artery surgery/intervention for carotid artery stenosis. History of cervical or cerebral artery revascularization surgery or percutaneous intervention\n\nThis does not include chronic (nonvascular) neurological disease or other acute neurological insults such as metabolic and anoxic ischemic encephalopathy.\n\n", "Source": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. 2013 ACCF/AHA key data elements and definitions for measuring the clinical management and outcomes of patients with acute coronary syndromes and coronary artery disease: a report of the American College of Cardiology Foundation/American Heart Association Task Force on Clinical Data Standards (Writing Committee to Develop Acute Coronary Syndromes and Coronary Artery Disease Clinical Data Standards). J Am Coll Cardiol. 2013;61:992–1025 (7).\""}, "Parent Child Validations": null}, {"Element Reference": 4555, "Name": "Diabe<PERSON>", "Section Display Name": "Other History", "Coding Instructions": "Indicate if the patient has a history of diabetes mellitus regardless of duration of disease or need for diabetic medications.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "Diabetes", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "73211009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Diabe<PERSON>", "Definition": "The American Diabetes Association criteria include documentation of the following:\n\n1. A1c >=6.5%; or\n2. Fasting plasma glucose >=126 mg/dl (7.0 mmol/l); or\n3. Two-hour plasma glucose >=200 mg/dl (11.1 mmol/l) during an oral glucose tolerance test; or\n4. In a patient with classic symptoms of hyperglycemia or hyperglycemic crisis, a random plasma glucose >=200 mg/dl (11.1 mmol/l)\n\nThis does not include gestational diabetes.", "Source": "American Diabetes Association Care. 2011;34 Suppl 1:S4-10."}, "Parent Child Validations": null}, {"Element Reference": 4560, "Name": "Currently on Dialysis", "Section Display Name": "Other History", "Coding Instructions": "Indicate if the patient is currently undergoing either hemodialysis or peritoneal dialysis on an ongoing basis as a result of renal failure.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "CurrentDialysis", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "108241001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 4575, "Name": "Chronic Lung Disease", "Section Display Name": "Other History", "Coding Instructions": "Indicate if the patient has a history of chronic lung disease.\n\nNote(s):\nA history of chronic inhalation reactive disease (asbestosis, mesothelioma, black lung disease or pneumoconiosis) may qualify as chronic lung disease. Radiation induced pneumonitis or radiation fibrosis also qualifies as chronic lung disease. A history of atelectasis is a transient condition and does not qualify.", "Target Value": "Any occurrence between birth and the procedure", "Short Name": "ChronicLungDisease", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "413839001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Chronic Lung Disease", "Definition": "Chronic lung disease can include patients with chronic obstructive pulmonary disease, chronic bronchitis, or emphysema. It can also include a patient who is currently being chronically treated with inhaled or oral pharmacological therapy (e.g., beta-adrenergic agonist, anti-inflammatory agent, leukotriene receptor antagonist, or steroid). Patients with asthma or seasonal allergies are not considered to have chronic lung disease.", "Source": "ACC/AHA Key Data Elements and Definitions for Measuring the Clinical Management and Outcomes of Patients With Chronic Heart Failure Circulation. 2005;112:1888-1916"}, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "D. Diagnostic Studies", "Section Code": "DIAGSTUDIES", "Section Type": "Section", "Cardinality": "0..1", "Table": "EPISODEOFCARE", "Elements": null}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "D. Diagnostic Studies", "Section Display Name": "EP Study", "Section Code": "EPStudy", "Section Type": "Section", "Cardinality": "0..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 5000, "Name": "Electrophysiology Study", "Section Display Name": "EP Study", "Coding Instructions": "Indicate if the patient had an electrophysiology study (EPS).\nNote(s): Code 'Yes' for an EP Study/Ablation performed for either ventricular or atrial arrhythmias prior to the start of the ICD procedure.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "EPStudy", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "252425004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Electrophysiology Study", "Definition": "One or more catheters capable of recording and pacing are placed in one or more of the cardiac chambers. The catheters may be used to measure conduction of the impulse from the sinus node to the ventricle; induce a tachycardia; and/or localize (map) the location where the tachycardia originates.", "Source": "NCDR"}, "Parent Child Validations": null}, {"Element Reference": 5005, "Name": "Electrophysiology Study Date", "Section Display Name": "EP Study", "Coding Instructions": "Indicate the date in which the most recent electrophysiology study (EPS) was performed.\n\nNote(s):\nIf the month or day of the EP study is unknown, please code 01/01/YYYY. If the specific year is unknown in the current record, the year may be estimated based on timeframes found in prior medical record documentation (Example: If the patient had \"most recent EP study\" documented in a record from 2011, then the year 2011 can be utilized and coded as 01/01/2011).", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "EPStudyDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "252425004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "Electrophysiology Study Date (5005) must be Greater than or Equal to Birth Date (2050)", "Selections": null, "Ranges": null, "Definition": {"Title": "Electrophysiology Study", "Definition": "One or more catheters capable of recording and pacing are placed in one or more of the cardiac chambers. The catheters may be used to measure conduction of the impulse from the sinus node to the ventricle; induce a tachycardia; and/or localize (map) the location where the tachycardia originates.", "Source": "NCDR"}, "Parent Child Validations": [{"Parent Element Reference": 5010, "Parent Element Name": "Electrophysiology Study Date Unknown", "Parent Element Selection Name": "No (or Not Answered)"}, {"Parent Element Reference": 5000, "Parent Element Name": "Electrophysiology Study", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "EP Study", "Section Display Name": "EP Study", "Section Code": "EPStudy2", "Section Type": "Section", "Cardinality": "0..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 5010, "Name": "Electrophysiology Study Date Unknown", "Section Display Name": "EP Study", "Coding Instructions": "Indicate if the date when the electrophysiology study (EPS) was performed is unknown.", "Target Value": "The last value between birth and the first procedure in this admission", "Short Name": "EPStudyDateUnk", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "252425004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Electrophysiology Study", "Definition": "One or more catheters capable of recording and pacing are placed in one or more of the cardiac chambers. The catheters may be used to measure conduction of the impulse from the sinus node to the ventricle; induce a tachycardia; and/or localize (map) the location where the tachycardia originates.", "Source": "NCDR"}, "Parent Child Validations": [{"Parent Element Reference": 5000, "Parent Element Name": "Electrophysiology Study", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 5015, "Name": "Clinically Relevant Ventricular Arrhythmias Induced", "Section Display Name": "EP Study", "Coding Instructions": "Indicate if clinically relevant ventricular arrhythmias were induced during the electrophysiology study.\n\nNotes(s):\nA clinically relevant ventricular arrhythmia induced during electrophysiology study most often represents sustained monomorphic ventricular tachycardia, but can include other clinically relevant, sustained ventricular tachyarrhythmias thought to contribute to syncope, aborted cardiac death, or other serious clinical presentations.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "VentArrythInduced", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001119", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 5000, "Parent Element Name": "Electrophysiology Study", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "D. Diagnostic Studies", "Section Display Name": "Diagnostic Studies", "Section Code": "DiagnosticStudies", "Section Type": "Section", "Cardinality": "0..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 5030, "Name": "Electrocardiogram Performed", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate if the patient had an electrocardiogram (ECG).", "Target Value": "The last value within 30 days prior to the first procedure in this admission", "Short Name": "ECG", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "164847006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 5035, "Name": "Electrocardiogram Date", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate the date in which the most recent electrocardiogram (ECG) was performed.", "Target Value": "The last value within 30 days prior to the first procedure in this admission", "Short Name": "ECGDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "164847006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "Electrocardiogram Date (5035) must be Greater than or Equal to Birth Date (2050)\n\nElectrocardiogram Date (5035) must be Less than or Equal to Procedure Start Date and Time (7000)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 5030, "Parent Element Name": "Electrocardiogram Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 5040, "Name": "Electrocardiogram Normal", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate if the electrocardiogram (ECG) clinical interpretation notes normal sinus rhythm ECG.", "Target Value": "The last value within 30 days prior to the first procedure in this admission", "Short Name": "ECGNormal", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "164854000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 5030, "Parent Element Name": "Electrocardiogram Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 5105, "Name": "Ventricular Paced", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate if the patient is ventricular paced.\n\nNote(s): \nIf no ECG is available, a pre-procedure 6 inch cardiac rhythm strip or clinician documentation may be utilized to obtain this information.", "Target Value": "The last value on start of the procedure", "Short Name": "VPaced", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "251266004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "When Ventricular Paced (5105) is (No) then Only Ventricular Paced QRS Complexes Present (5045) must not be (Yes)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 5045, "Name": "Only Ventricular Paced QRS Complexes Present", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate if there were only ventricular paced QRS complexes present.\n\nNote(s):\nIf the patient has some intrinsic ventricular complexes present, code “No”.\n\nIf no ECG is available, a pre-procedure 6 inch cardiac rhythm strip or clinician documentation may be utilized to obtain this information.", "Target Value": "The last value within 30 days prior to the first procedure in this admission", "Short Name": "VPQRS", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001120", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 5050, "Name": "Ventricular Paced QRS Duration", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate the duration of the ventricular paced QRS complex in milliseconds that was derived from the surface electrocardiogram (ECG). Surface ECGs are obtained from the surface of the body and do not include intracardiac ECGs.\n\nNote(s):\nIf no ECG is available, a pre-procedure 6-inch cardiac rhythm strip or clinician documentation may be utilized to obtain this information.\n\nProvider documentation of QRS, BBB, and atrial rhythm will be utilized first, then the finding from most recent ECG. If neither are available, use a 6 inch rhythm strip or device interrogation to code QRS, BBB, and atrial rhythm. However, if a 6 inch rhythm strip is used, ECG, Seq. 5030, will be coded No. Use the following order to code:\n1. Provider documentation, if not then\n2. Most recent ECG, if not, then\n3. 6 inch rhythm strip and/or device interrogation\n", "Target Value": "The last value within 30 days prior to the first procedure in this admission", "Short Name": "VPacedQRS", "Data Type": "PQ", "Precision": "3,0", "Unit Of Measure": "msec", "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001121", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "msec", "Usual Range Min": 20.0, "Usual Range Max": 250.0, "Valid Range Min": 10.0, "Valid Range Max": "300"}], "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 5045, "Parent Element Name": "Only Ventricular Paced QRS Complexes Present", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 5055, "Name": "Non-Ventricular Paced QRS duration", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate the duration of the non-ventricular paced or intrinsic QRS complex, in milliseconds, that was derived from the surface electrocardiogram (ECG).  Surface ECGs are obtained from the surface of the body and do not include intracardiac ECGs.\n\nNote(s): \nIf no ECG is available, a pre-procedure 6-inch cardiac rhythm strip or clinician documentation may be utilized to obtain this information.\n\nProvider documentation of QRS, BBB, and atrial rhythm will be utilized first, then the finding from most recent ECG. If neither are available, use a 6 inch rhythm strip or device interrogation to code QRS, BBB, and atrial rhythm. However, if a 6 inch rhythm strip is used, ECG, Seq. 5030, will be coded No. Use the following order to code:\n1. Provider documentation, if not then\n2. Most recent ECG, if not, then\n3. 6 inch rhythm strip and/or device interrogation", "Target Value": "The last value within 30 days prior to the first procedure in this admission", "Short Name": "NVPQRS", "Data Type": "PQ", "Precision": "3,0", "Unit Of Measure": "msec", "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "251208001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "msec", "Usual Range Min": 20.0, "Usual Range Max": 250.0, "Valid Range Min": 10.0, "Valid Range Max": "300"}], "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 5045, "Parent Element Name": "Only Ventricular Paced QRS Complexes Present", "Parent Element Selection Name": "No"}]}, {"Element Reference": 5060, "Name": "Abnormal Intraventricular Conduction", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate if the patient has abnormal intraventricular conduction, bundle branch blocks, or non-specific conduction delays.\n\nNote(s):\nCode 'No' if the abnormal intraventricular conduction is determined by the physician to be transient or rate related.\nThis data element is evaluating the intrinsic rhythm.", "Target Value": "The last value within 30 days prior to the first procedure in this admission", "Short Name": "AbConduction", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "4554005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 5065, "Name": "Abnormal Intraventricular Conduction Types", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate the type of intraventricular conduction(s) the patient has.\n\nNote(s):\nIf the patient has multiple intraventricular conduction types, select all types.", "Target Value": "The last value within 30 days prior to the first procedure in this admission", "Short Name": "IntraVentConductionType", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001142", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Abnormal Intraventricular Conduction Types", "Code": "164909002", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Left bundle branch block", "Selection Definition": null, "Display Order": 1}, {"Name": "Abnormal Intraventricular Conduction Types", "Code": "164907000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Right bundle branch block", "Selection Definition": null, "Display Order": 2}, {"Name": "Abnormal Intraventricular Conduction Types", "Code": "698252002", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Delay, Non-specific", "Selection Definition": null, "Display Order": 3}, {"Name": "Abnormal Intraventricular Conduction Types", "Code": "32758004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Alternating RBBB and LBBB", "Selection Definition": null, "Display Order": 4}], "Ranges": null, "Definition": {"Title": "Intraventricular Conduction Types", "Definition": "-Left Bundle Branch is characterized by QRS duration 120 ms or longer, delayed onset of intrinsicoid deflection in 1, V5, and V6 >60 ms, broad and notched or slurred R waves in I, aVL, V5, and V6, rS or QS complexes in right precordial leads, ST-segment and T waves in opposite polarity to the major QRS deflection.\n-Non-Specific abnormal Intraventricular conduction delays are characterized by a QRS duration of 110 ms or more with morphology different from LBBB or RBBB.\n-Right Bundle Branch Block is characterized by a QRS duration of 120 ms, rsR'or rSR' complexes in V1 and V2, Delayed onset of intrinsicoid, deflection in V1 and V2 >50 ms, Broad, slurred S wave in 1, V5, and V6 Secondary ST-T wave changes.", "Source": "ACC/AHA/HRS 2006 Key Data Elements and Definitions for Electrophysiological Studies and Procedures."}, "Parent Child Validations": [{"Parent Element Reference": 5060, "Parent Element Name": "Abnormal Intraventricular Conduction", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 5100, "Name": "Atrial Rhythm", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate the patient's atrial rhythm at the start of the procedure.\n\nNote(s):\nIf the patient has multiple atrial rhythms, select all that apply.\n\nIn the event that a patient is ventricular paced, indicate the underlying atrial rhythm.\n\nTarget value applies to the first procedure captured for this registry.\n\nIf no ECG is available, a pre-procedure 6 inch cardiac rhythm strip or clinician documentation may be utilized to obtain this information.\n\nProvider documentation of QRS, BBB, and atrial rhythm will be utilized first, then the finding from most recent ECG. If neither are available, use a 6 inch rhythm strip or device interrogation to code QRS, BBB, and strial rhythm. However, if a 6 inch rhythm strip is used, ECG, Seq. 5030, will be coded No. Use the following order to code:\n1. Provider documentation, if not then\n2. Most recent ECG, if not, then\n3. 6 inch rhythm strip and/or device interrogation\n", "Target Value": "The last value within 30 days prior to the first procedure in this admission", "Short Name": "AtrialRhythm", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "106068003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Atrial Rhythm", "Code": "106067008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Sinus node rhythm", "Selection Definition": null, "Display Order": 1}, {"Name": "Atrial Rhythm", "Code": "49436004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Atrial fibrillation", "Selection Definition": null, "Display Order": 2}, {"Name": "Atrial Rhythm", "Code": "276796006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Atrial tachycardia", "Selection Definition": null, "Display Order": 3}, {"Name": "Atrial Rhythm", "Code": "5370000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Atrial flutter", "Selection Definition": null, "Display Order": 4}, {"Name": "Atrial Rhythm", "Code": "5609005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Sinus arrest", "Selection Definition": null, "Display Order": 5}, {"Name": "Atrial Rhythm", "Code": "251268003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Atrial paced", "Selection Definition": null, "Display Order": 6}], "Ranges": null, "Definition": null, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "E. Labs", "Section Code": "LABS", "Section Type": "Section", "Cardinality": "0..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 6025, "Name": "Blood Urea Nitrogen", "Section Display Name": "E. Labs", "Coding Instructions": "Indicate the blood urea nitrogen (BUN) value, in mg/dL.\n\nNote(s):\nWhen the value falls outside of the usual range (Example: Bun is > 20 but less than 99), an \"Outlier Warning\" will be displayed in the quality check. This will not affect DQR submission. It is a notification to double check the entered value. If the BUN value is greater than the valid range (over 100), code “99”.", "Target Value": "The last value within 30 days prior to the first procedure in this admission", "Short Name": "BUN", "Data Type": "PQ", "Precision": "2,0", "Unit Of Measure": "mg/dL", "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "6299-2", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "mg/dL", "Usual Range Min": 5.0, "Usual Range Max": 20.0, "Valid Range Min": 1.0, "Valid Range Max": "99"}], "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 6026, "Parent Element Name": "BUN Not Drawn", "Parent Element Selection Name": "No"}]}, {"Element Reference": 6026, "Name": "BUN Not Drawn", "Section Display Name": "E. Labs", "Coding Instructions": "Indicate if a blood urea nitrogen (BUN) was not drawn.", "Target Value": "The last value within 30 days prior to the first procedure in this admission", "Short Name": "BUNND", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "6299-2", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 6030, "Name": "Hemoglobin", "Section Display Name": "E. Labs", "Coding Instructions": "Indicate the hemoglobin (Hgb) value in g/dL.", "Target Value": "The last value within 30 days prior to the first procedure in this admission", "Short Name": "HGB", "Data Type": "PQ", "Precision": "4,2", "Unit Of Measure": "g/dL", "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "718-7", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "g/dL", "Usual Range Min": 5.0, "Usual Range Max": 20.0, "Valid Range Min": 1.0, "Valid Range Max": "50.00"}], "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 6031, "Parent Element Name": "Hemoglobin Not Drawn", "Parent Element Selection Name": "No"}]}, {"Element Reference": 6031, "Name": "Hemoglobin Not Drawn", "Section Display Name": "E. Labs", "Coding Instructions": "Indicate if the hemoglobin was not drawn.", "Target Value": "The last value within 30 days prior to the first procedure in this admission", "Short Name": "HGBND", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "718-7", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 6035, "Name": "Sodium", "Section Display Name": "E. Labs", "Coding Instructions": "Indicate the sodium (Na) level, in mEq/L.", "Target Value": "The last value within 30 days prior to the first procedure in this admission", "Short Name": "Sodium", "Data Type": "PQ", "Precision": "3,0", "Unit Of Measure": "mEq/L", "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2950-4", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "mEq/L", "Usual Range Min": 120.0, "Usual Range Max": 150.0, "Valid Range Min": 1.0, "Valid Range Max": "300"}], "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 6036, "Parent Element Name": "Sodium Not Drawn", "Parent Element Selection Name": "No"}]}, {"Element Reference": 6036, "Name": "Sodium Not Drawn", "Section Display Name": "E. Labs", "Coding Instructions": "Indicate if the sodium level was not drawn.", "Target Value": "The last value within 30 days prior to the first procedure in this admission", "Short Name": "SodiumND", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2950-4", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "F. Procedure Information", "Section Code": "PROCINFO", "Section Type": "Repeater Section", "Cardinality": "1..n", "Table": "PROCINFO", "Elements": [{"Element Reference": 7000, "Name": "Procedure Start Date and Time", "Section Display Name": "F. Procedure Information", "Coding Instructions": "Indicate the date and time the procedure started. The time of the procedure is the time that the skin incision, vascular access, or its equivalent, was made in order to start the procedure.\n\nNote(s):\nIndicate the date/time (mm/dd/yyyy hours:minutes) using the military 24-hour clock, beginning at midnight (0000 hours).", "Target Value": "Any occurrence on current procedure", "Short Name": "ProcedureStartDateTime", "Data Type": "TS", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "1000142460", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Procedure Start Date and Time (7000) must be Greater than or Equal to Arrival Date (3000)\n\nProcedure Start Date and Time (7000) must be Less than Procedure End Date and Time (7005)\n\nProcedure Start Date and Time (7000) must be Greater than or Equal to Birth Date (2050)\n\nProcedure Start Date and Time (7000) must be Greater than or Equal to Prior Aortic Valve Procedure Date (14725)\n\nProcedure Start Date and Time (7000) must be Greater than or Equal to Ventricular Fibrillation Date (14720)\n\nMutiple lab visits may not share the same Procedure Start Date and Time (7000)\n\nMultiple lab visits may not share the same Procedure Start Date and Time (7000)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7005, "Name": "Procedure End Date and Time", "Section Display Name": "F. Procedure Information", "Coding Instructions": "Indicate the ending date and time at which the operator breaks scrub at the end of the procedure.\n\nNote(s):\nIf more than one operator is involved in the case then use the date and time the last operator breaks scrub.", "Target Value": "The value on current procedure", "Short Name": "ProcedureEndDateTime", "Data Type": "TS", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "1000142459", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Procedure End Date and Time (7005) must be Less than or Equal to Discharge Date (10100)\n\nProcedure End Date and Time (7005) must be Greater than or Equal to Birth Date (2050)\n\nProcedure End Date and Time (7005) and Procedure Start Date and Time (7000) must not overlap on multiple procedures", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7010, "Name": "Procedure Type", "Section Display Name": "F. Procedure Information", "Coding Instructions": "Indicate the procedure that was performed. ", "Target Value": "Any occurrence on current procedure", "Short Name": "ProcedureType", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001857", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "When Procedure Type (7010) is (Generator Explant) then Device Explanted (7660) must not be (Not Explanted, Previously Explanted)\n\nProcedure Type (7010) of (Initial Generator Implant) may only take place in the initial lab visit", "Selections": [{"Name": "Procedure Type", "Code": "233170003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Initial Generator Implant", "Selection Definition": "The patient is receiving a device for the first time. Complete all sections of the data collection form for all patients having an initial generator implant.", "Display Order": 1}, {"Name": "Procedure Type", "Code": "428625001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Generator change", "Selection Definition": "The patient already has a device and is receiving a generator that is an upgrade or a change from one that was previously implanted. Complete all sections of the data collection form for all patients having a generator change/upgrade.", "Display Order": 2}, {"Name": "Procedure Type", "Code": "233171004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Generator explant", "Selection Definition": "Patient already has a device and is having the generator removed without re-implant of another generator during the current procedure.", "Display Order": 3}, {"Name": "Procedure Type", "Code": "100001025", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Lead Only", "Selection Definition": "A lead procedure is being performed without a generator change. Complete all sections of the data collection form, except section D (Diagnostic Studies), section E (Labs), and section G (Device Implant/Explant) for all patients having a procedure where new leads were implanted and/or existing leads were reused, extracted or abandoned.", "Display Order": 4}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7015, "Name": "ICD Indication", "Section Display Name": "F. Procedure Information", "Coding Instructions": "Indicate the ICD procedure indication", "Target Value": "Any occurrence on current procedure", "Short Name": "ICDIndication", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "432678004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "ICD Indication", "Code": "315233008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Primary prevention", "Selection Definition": "Primary Prevention is an indication for an ICD to prevent sudden cardiac death. It refers to use of ICDs in individuals who are at risk for but have not yet had an episode of sustained ventricular tachycardia, ventricular fibrillation, or resuscitated cardiac arrest.", "Display Order": 1}, {"Name": "ICD Indication", "Code": "315234002", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Secondary prevention", "Selection Definition": "Secondary prevention refers to an indication for ICD exclusively for patients who have survived one or more cardiac arrests or sustained ventricular tachycardia. Patients with cardiac conditions associated with a high risk of sudden death who have unexplained syncope that is likely to be due to ventricular arrhythmias are considered to have a secondary indication.", "Display Order": 2}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator change"}, {"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Initial Generator Implant"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "F. Procedure Information", "Section Display Name": "Shared Decision Making", "Section Code": "SharedDecisionMaking", "Section Type": "Section", "Cardinality": "0..1", "Table": "PROCINFO", "Elements": [{"Element Reference": 14732, "Name": "Shared Decision Making", "Section Display Name": "Shared Decision Making", "Coding Instructions": "Indicate if Shared Decision Making was performed for a primary prevention procedure.", "Target Value": "The value on current procedure", "Short Name": "SDM_Proc", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "NULL", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000002041", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Shared Decision Making", "Definition": "Shared decision-making is when patients and clinicians work as a team to make care decisions. Tools can help facilitate a collaborative process between providers and patients and can:\n - Increase knowledge and satisfaction regarding care\n - Define clearer goals for treatment\n - Align health decisions with patient values", "Source": null}, "Parent Child Validations": null}, {"Element Reference": 14733, "Name": "Shared Decision Making Tool Used", "Section Display Name": "Shared Decision Making", "Coding Instructions": "Indicate if a shared decision making tool was used.", "Target Value": "The value on current procedure", "Short Name": "SDM_Tool", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "NULL", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "415806002", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 14732, "Parent Element Name": "Shared Decision Making", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 14734, "Name": "Shared Decision Making Tool Name", "Section Display Name": "Shared Decision Making", "Coding Instructions": "Indicate what tool was used. \nIf the tool used is not in the drop-down list, <NAME_EMAIL> to have a selection added.", "Target Value": "The value on current procedure", "Short Name": "SDM_Tool_Name", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "NULL", "Is Dynamic List": "Yes", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "405083000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Shared Decision Making Tool Name", "Code": "112000002028", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Colorado Shared Decision Making Tool", "Selection Definition": null, "Display Order": 1}, {"Name": "Shared Decision Making Tool Name", "Code": "112000002029", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Mayo ICD Shared Decision Making Tool", "Selection Definition": null, "Display Order": 2}, {"Name": "Shared Decision Making Tool Name", "Code": "100000351", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Other Shared Decision Making Tool", "Selection Definition": null, "Display Order": 3}, {"Name": "Shared Decision Making Tool Name", "Code": "112000002040", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "CMS Shared Decision Making Tool", "Selection Definition": null, "Display Order": 4}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 14733, "Parent Element Name": "Shared Decision Making Tool Used", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "F. Procedure Information", "Section Display Name": "Premarket Clinical Trial", "Section Code": "PremarketClinicalTrial", "Section Type": "Section", "Cardinality": "0..1", "Table": "PROCINFO", "Elements": [{"Element Reference": 7020, "Name": "Premarket Clinical Trial", "Section Display Name": "Premarket Clinical Trial", "Coding Instructions": "Indicate if the ICD procedure (generator implant or lead procedure) is part of a clinical trial, excluding post-market surveillance trials.", "Target Value": "Any occurrence on current procedure", "Short Name": "ClinicalTrial", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "428024001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "F. Procedure Information", "Section Display Name": "<PERSON><PERSON>ce Implant/Explant", "Section Code": "IMPLEXPL", "Section Type": "Section", "Cardinality": "0..1", "Table": "PROCINFO", "Elements": [{"Element Reference": 7600, "Name": "Generator Operator Last Name", "Section Display Name": "<PERSON><PERSON>ce Implant/Explant", "Coding Instructions": "Indicate the last name of the operator who is implanting the device.\n\nNote(s):\nIf the name exceeds 50 characters, enter the first 50 letters only.", "Target Value": "The value on current procedure", "Short Name": "GenOpLName", "Data Type": "LN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001853", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7605, "Name": "Generator Operator First Name", "Section Display Name": "<PERSON><PERSON>ce Implant/Explant", "Coding Instructions": "Indicate the first name of the operator who is implanting the device.\n\nNote(s):\nIf the name exceeds 50 characters, enter the first 50 letters only.", "Target Value": "The value on current procedure", "Short Name": "GenOpFName", "Data Type": "FN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001853", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7610, "Name": "Generator Operator Middle Name", "Section Display Name": "<PERSON><PERSON>ce Implant/Explant", "Coding Instructions": "Indicate the middle name of the operator who is implanting the device.\n\nNote(s):\nIt is acceptable to specify the middle initial.\n\nIf there is no middle name given, leave field blank.\n\nIf there are multiple middle names, enter all of the middle names sequentially.\n\nIf the name exceeds 50 characters, enter the first 50 letters only.", "Target Value": "The value on current procedure", "Short Name": "GenOpMName", "Data Type": "MN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001853", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7615, "Name": "Generator Operator NPI", "Section Display Name": "<PERSON><PERSON>ce Implant/Explant", "Coding Instructions": "Indicate the National Provider Identifier (NPI) of the operator who is implanting the device. NPI's, assigned by the Centers for Medicare and Medicaid Services (CMS), are used to uniquely identify physicians for Medicare billing purposes.", "Target Value": "The value on current procedure", "Short Name": "GenOpNPI", "Data Type": "NUM", "Precision": "10", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.4.6", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7620, "Name": "Device Implanted", "Section Display Name": "<PERSON><PERSON>ce Implant/Explant", "Coding Instructions": "Indicate if a device was implanted.", "Target Value": "Any occurrence on current procedure", "Short Name": "DeviceImplanted", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "232965003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Initial Generator Implant"}, {"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator change"}]}, {"Element Reference": 7625, "Name": "Final Device Type", "Section Display Name": "<PERSON><PERSON>ce Implant/Explant", "Coding Instructions": "Indicate the device type that was implanted at the completion of the procedure.", "Target Value": "Any occurrence on current procedure", "Short Name": "FinalDeviceType", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "260846005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "When Final Device Type (7625) is (CRT-P, His/Left Bundle Pacemaker, Leadless Single Chamber Pacemaker) then ICD Indication (7015) must be Null.", "Selections": [{"Name": "Final Device Type", "Code": "100001214", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "ICD Single Chamber", "Selection Definition": "A single-chamber ICD defibrillates the ventricle and paces the ventricle.", "Display Order": 1}, {"Name": "Final Device Type", "Code": "100001215", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "ICD Dual Chamber", "Selection Definition": "A dual-chamber ICD defibrillates the ventricle and paces the atrium and ventricle.", "Display Order": 2}, {"Name": "Final Device Type", "Code": "100001216", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "CRT-D", "Selection Definition": "A cardiac resynchronization therapy device and defibrillator (CRT-D) has dual capabilities. It is a biventricular pacemaker that sends electrical signals to both ventricles as well as a defibrillator. It may or may not have an atrial pacing wire.", "Display Order": 3}, {"Name": "Final Device Type", "Code": "100001217", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "S-ICD (Sub Q)", "Selection Definition": "A subcutaneous only defibrillator.", "Display Order": 4}, {"Name": "Final Device Type", "Code": "704708004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "CRT-P", "Selection Definition": "A CRT procedure is the placement of a biventricular pacemaker that sends electrical signals to both ventricles that resynchronizes the heart chambers and helps it pump more effectively. It may or may not have an atrial pacing wire.\n", "Display Order": 5}, {"Name": "Final Device Type", "Code": "112000002030", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Leadless Single Chamber Pacemaker", "Selection Definition": "A self-contained transvenous pacemaker generator and electrode system implanted directly into the right ventricle. The device is implanted via a femoral vein transcatheter approach; it requires no chest incision or subcutaneous generator pocket.", "Display Order": 6}, {"Name": "Final Device Type", "Code": "112000002039", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "His/Left Bundle Pacemaker", "Selection Definition": "His-bundle pacing is a method for delivering permanent pacing. It produces physiological ventricular activation via the His-Purkinje system. / Left bundle pacing is a method for delivering permanent pacing. It produces physiological ventricular activation via the Left bundle.", "Display Order": 7}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7620, "Parent Element Name": "Device Implanted", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 7630, "Name": "Coronary Sinus/Left Ventricular (CS/LV) lead", "Section Display Name": "<PERSON><PERSON>ce Implant/Explant", "Coding Instructions": "Indicate if the coronary sinus/left ventricular (CS/LV) lead was implanted during the current procedure.", "Target Value": "Any occurrence on current procedure", "Short Name": "CSLVLead", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000985", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Coronary Sinus/Left Ventricular (CS/LV) lead", "Code": "100001057", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not Attempted", "Selection Definition": null, "Display Order": 1}, {"Name": "Coronary Sinus/Left Ventricular (CS/LV) lead", "Code": "100001107", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Successfully Implanted", "Selection Definition": null, "Display Order": 2}, {"Name": "Coronary Sinus/Left Ventricular (CS/LV) lead", "Code": "100001084", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Previously Implanted", "Selection Definition": null, "Display Order": 3}, {"Name": "Coronary Sinus/Left Ventricular (CS/LV) lead", "Code": "100001143", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Implant unsuccessful", "Selection Definition": null, "Display Order": 4}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7620, "Parent Element Name": "Device Implanted", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 14739, "Name": "His/Left Bundle Lead", "Section Display Name": "<PERSON><PERSON>ce Implant/Explant", "Coding Instructions": "Indicate if the His/left bundle lead was implanted during the current procedure.", "Target Value": "The value on current procedure", "Short Name": "HisLBundleLead", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "NULL", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000002024", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "His/Left Bundle Lead", "Code": "100001057", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not Attempted", "Selection Definition": null, "Display Order": 1}, {"Name": "His/Left Bundle Lead", "Code": "100001107", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Successfully Implanted", "Selection Definition": null, "Display Order": 2}, {"Name": "His/Left Bundle Lead", "Code": "100001084", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Previously Implanted", "Selection Definition": null, "Display Order": 3}, {"Name": "His/Left Bundle Lead", "Code": "100001143", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Implant unsuccessful", "Selection Definition": null, "Display Order": 4}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7620, "Parent Element Name": "Device Implanted", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 14729, "Name": "Primary Tachycardia Indication Present", "Section Display Name": "<PERSON><PERSON>ce Implant/Explant", "Coding Instructions": "Indicate if there was a primary tachycardia indication for ICD implantation. ", "Target Value": "The value on current procedure", "Short Name": "PrimTachIndPres", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "NULL", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000002043", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7625, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "CRT-D"}, {"Parent Element Reference": 7625, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "ICD Dual Chamber"}, {"Parent Element Reference": 7625, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "ICD Single Chamber"}, {"Parent Element Reference": 7625, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "S-ICD (Sub Q)"}]}, {"Element Reference": 14730, "Name": "Bradycardia Indication Present", "Section Display Name": "<PERSON><PERSON>ce Implant/Explant", "Coding Instructions": "Indicate if a bradycardia indication was also present.", "Target Value": "The value on current procedure", "Short Name": "BradIndPres", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "NULL", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000002042", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 14729, "Parent Element Name": "Primary Tachycardia Indication Present", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 14737, "Name": "Primary Bradycardia Indication Present", "Section Display Name": "<PERSON><PERSON>ce Implant/Explant", "Coding Instructions": "Indicate if the primary indication was bradycardia.", "Target Value": "The value on current procedure", "Short Name": "PrimBradIndPres", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "NULL", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000002044", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7625, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "CRT-P"}, {"Parent Element Reference": 7625, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "His/Left Bundle Pacemaker"}, {"Parent Element Reference": 7625, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "Leadless Single Chamber Pacemaker"}]}, {"Element Reference": 14731, "Name": "Reason Pacing Indicated", "Section Display Name": "<PERSON><PERSON>ce Implant/Explant", "Coding Instructions": "Select the reason pacing was indicated.", "Target Value": "The value on current procedure", "Short Name": "ReasonPacIndic", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": "NULL", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001097", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Reason Pacing Indicated", "Code": "36083008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Sick sinus syndrome", "Selection Definition": "Sick sinus syndrome or sinus node dysfunction must be symptomatic to code 14731 as 'Yes'. This includes sinus bradycardia, ectopic atrial bradycardia, sinoatrial exit block, sinus pause, sinus node arrest, and tachycardia-bradycardia syndrome; all of which must be symptomatic.\n\n", "Display Order": 1}, {"Name": "Reason Pacing Indicated", "Code": "27885002", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Complete heart block", "Selection Definition": "No evidence of atrioventricular conduction.", "Display Order": 2}, {"Name": "Reason Pacing Indicated", "Code": "427989008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Chronotropic incompetence", "Selection Definition": "Broadly defined as the inability of the heart to increase its rate commensurate with increased activity or demand, in many studies translates to failure to attain 80% of expected heart rate reserve during exercise. ", "Display Order": 3}, {"Name": "Reason Pacing Indicated", "Code": "28189009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Mobitz Type II", "Selection Definition": "P-waves with a constant rate (< 100 bpm) with a periodic single non-conducted P-wave associated with other P-waves before and after the non-conducted P-wave with constant PR intervals (excluding 2:1 atrioventricular block)", "Display Order": 4}, {"Name": "Reason Pacing Indicated", "Code": "54016002", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "2:1 AV Block", "Selection Definition": "P-waves with a constant rate (or near constant rate because of ventriculophasic sinus arrhythmia) rate (<100 bpm) where every other P-wave conducts to the ventricles", "Display Order": 5}, {"Name": "Reason Pacing Indicated", "Code": "428663009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Atrioventricular Node Ablation", "Selection Definition": null, "Display Order": 6}, {"Name": "Reason Pacing Indicated", "Code": "112000002017", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "HF Unresponsive to GDMT", "Selection Definition": null, "Display Order": 7}, {"Name": "Reason Pacing Indicated", "Code": "100000931", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Anticipated requirement of > 40% RV pacing", "Selection Definition": null, "Display Order": 8}], "Ranges": null, "Definition": {"Title": "Reason Pacing Indicated", "Definition": "Refer to the source for the supporting definition.", "Source": "<PERSON>, Stainback RF, <PERSON>, et al. ACCF/HRS/AHA/ ASE/HFSA/SCAI/SCCT/SCMR 2013 appropriate use criteria for implantable cardioverter-defibrillators and cardiac resynchronization therapy. J Am Coll Cardiol 2013;61:1318–68. doi: 10.1016/j.jacc.2012.12.017"}, "Parent Child Validations": [{"Parent Element Reference": 7625, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "ICD Dual Chamber"}, {"Parent Element Reference": 7625, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "CRT-D"}, {"Parent Element Reference": 7625, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "CRT-P"}, {"Parent Element Reference": 7625, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "Leadless Single Chamber Pacemaker"}, {"Parent Element Reference": 7625, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "His/Left Bundle Pacemaker"}]}, {"Element Reference": 14735, "Name": "Primary Pacing Mode", "Section Display Name": "<PERSON><PERSON>ce Implant/Explant", "Coding Instructions": "Select the primary pacing mode.", "Target Value": "The value on current procedure", "Short Name": "PriPacMode", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "NULL", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000002023", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Primary Pacing Mode", "Code": "112000002019", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "DDD(R)", "Selection Definition": null, "Display Order": 1}, {"Name": "Primary Pacing Mode", "Code": "112000002018", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "VVI(R)", "Selection Definition": null, "Display Order": 2}, {"Name": "Primary Pacing Mode", "Code": "112000002020", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "DDI(R)", "Selection Definition": null, "Display Order": 3}, {"Name": "Primary Pacing Mode", "Code": "112000002021", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "DDD(R)/AAI(R)", "Selection Definition": null, "Display Order": 4}, {"Name": "Primary Pacing Mode", "Code": "112000002022", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "RVPP (Right Ventricular Pacing Prevention Algorithm)", "Selection Definition": null, "Display Order": 5}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7625, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "CRT-D"}, {"Parent Element Reference": 7625, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "CRT-P"}, {"Parent Element Reference": 7625, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "His/Left Bundle Pacemaker"}, {"Parent Element Reference": 7625, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "ICD Dual Chamber"}, {"Parent Element Reference": 7625, "Parent Element Name": "Final Device Type", "Parent Element Selection Name": "Leadless Single Chamber Pacemaker"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "<PERSON><PERSON>ce Implant/Explant", "Section Display Name": "Implant Device Information", "Section Code": "ImplantDevice", "Section Type": "Section", "Cardinality": "0..1", "Table": "PROCINFO", "Elements": [{"Element Reference": 7635, "Name": "Implant Device ID", "Section Display Name": "Implant Device Information", "Coding Instructions": "Indicate the assigned identification number associated with the implanted device.\n\nNote(s):\nThe devices that should be collected in your application are controlled by a Defibrillator Device Master file. This file is maintained by the NCDR and will be made available on the internet for downloading and importing/updating into your application.", "Target Value": "Any occurrence on current procedure", "Short Name": "ICDImpID", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "Yes", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.6.1.21", "Code System": "2.16.840.1.113883.3.3478.6.1.21", "Code System Name": "ACC NCDR EP Devices", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7620, "Parent Element Name": "Device Implanted", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 7640, "Name": "Implant Device Serial Number", "Section Display Name": "Implant Device Information", "Coding Instructions": "Indicate the serial number of the device that was implanted.", "Target Value": "Any occurrence on current procedure", "Short Name": "ICDImpSerNo", "Data Type": "ST", "Precision": "30", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.850", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "A Implant Device Serial Number (7640) may only be entered/selected once\n\nWhen Implant Device Serial Number (7640) is answered, Implant Device ID (7635) cannot be Null", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7620, "Parent Element Name": "Device Implanted", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 7645, "Name": "Implant Unique Device Identifier", "Section Display Name": "Implant Device Information", "Coding Instructions": "Indicate the direct identifier portion of the Unique Device Identifier (UDI) associated with the device used for implant. This ID is provided by the device manufacturer, and is either a GTIN or HIBC number.", "Target Value": "Any occurrence on current procedure", "Short Name": "ICDImpUDI", "Data Type": "ST", "Precision": "150", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "No Action", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3719", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Unique Device Identifier (UDI)", "Definition": "An identifier that is the main (primary) lookup for a medical device product and meets the requirements to uniquely identify a device through its distribution and use. This value is supplied to the FDA by the manufacturer.", "Source": "US FDA"}, "Parent Child Validations": [{"Parent Element Reference": 7620, "Parent Element Name": "Device Implanted", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "<PERSON><PERSON>ce Implant/Explant", "Section Display Name": "Change Or Explant Information", "Section Code": "ChangeOrExplant", "Section Type": "Section", "Cardinality": "0..1", "Table": "PROCINFO", "Elements": [{"Element Reference": 7650, "Name": "Reason(s) for Generator Replacement", "Section Display Name": "Change Or Explant Information", "Coding Instructions": "Indicate the reason(s) for the replacement.", "Target Value": "Any occurrence on current procedure", "Short Name": "ReImplantReason", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000991", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Reason(s) for Generator Replacement", "Code": "100001088", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Reimplant Reason - End of Battery Life", "Selection Definition": null, "Display Order": 1}, {"Name": "Reason(s) for Generator Replacement", "Code": "100001092", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Reimplant Reason - Replaced At Time of Lead Revision", "Selection Definition": null, "Display Order": 2}, {"Name": "Reason(s) for Generator Replacement", "Code": "100001094", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Reimplant Reason - Upgrade", "Selection Definition": null, "Display Order": 3}, {"Name": "Reason(s) for Generator Replacement", "Code": "100001091", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Reimplant Reason - Infection", "Selection Definition": null, "Display Order": 4}, {"Name": "Reason(s) for Generator Replacement", "Code": "100001093", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Reimplant Reason - Under Manufacturer Advisory/Recall", "Selection Definition": null, "Display Order": 5}, {"Name": "Reason(s) for Generator Replacement", "Code": "100001089", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Reimplant Reason - <PERSON><PERSON><PERSON> Connector/Header", "Selection Definition": null, "Display Order": 6}, {"Name": "Reason(s) for Generator Replacement", "Code": "100001087", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Reimplant Reason - Device Relocation", "Selection Definition": null, "Display Order": 7}, {"Name": "Reason(s) for Generator Replacement", "Code": "100001090", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Reimplant Reason - Generator Malfunction", "Selection Definition": null, "Display Order": 8}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator change"}]}, {"Element Reference": 7660, "Name": "Device Explanted", "Section Display Name": "Change Or Explant Information", "Coding Instructions": "Indicate if the previous device was explanted.", "Target Value": "Any occurrence between previous device implant and current procedure", "Short Name": "DeviceExplant", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "233171004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Device Explanted", "Code": "100001140", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not explanted", "Selection Definition": null, "Display Order": 1}, {"Name": "Device Explanted", "Code": "100001141", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Explanted", "Selection Definition": null, "Display Order": 2}, {"Name": "Device Explanted", "Code": "100001083", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Previously explanted", "Selection Definition": null, "Display Order": 3}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator explant"}, {"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator change"}]}, {"Element Reference": 7665, "Name": "Prior Generator Explant Date", "Section Display Name": "Change Or Explant Information", "Coding Instructions": "Indicate the date the device was explanted.\n\nNote(s):\nIf the month or day of the device explanted is unknown, please code 01/01/YYYY. If the specific year is unknown in the current record, the year may be estimated based on timeframes found in prior medical record documentation (Example: If the patient had device explanted documented in a record from 2011, then the year 2011 can be utilized and coded as 01/01/2011).", "Target Value": "The last value between the implant and the end of current procedure", "Short Name": "ExplantDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "416940007:363589002=233171004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "Prior Generator Explant Date (7665) must be Less than or Equal to Procedure Start Date and Time (7000)\n\nPrior Generator Explant Date (7665) must be Less than or Equal to Discharge Date (10100)\n\nPrior Generator Explant Date (7665) must be Greater than or Equal to Birth Date (2050)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7660, "Parent Element Name": "Device Explanted", "Parent Element Selection Name": "Previously explanted"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Change Or Explant Information", "Section Display Name": "Explant Device Information", "Section Code": "ExplantDevice", "Section Type": "Section", "Cardinality": "0..1", "Table": "PROCINFO", "Elements": [{"Element Reference": 7675, "Name": "Explant Device ID", "Section Display Name": "Explant Device Information", "Coding Instructions": "Indicate the assigned identification number associated with the explanted device.\n\nNote(s):\nThe devices that should be collected in your application are controlled by a Defibrillator Device Master file. This file is maintained by the NCDR and will be made available on the internet for downloading and importing/updating into your application.", "Target Value": "Any occurrence between previous device implant and current procedure", "Short Name": "ICDExpID", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "Yes", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.6.1.21", "Code System": "2.16.840.1.113883.3.3478.6.1.21", "Code System Name": "ACC NCDR EP Devices", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7660, "Parent Element Name": "Device Explanted", "Parent Element Selection Name": "Explanted"}]}, {"Element Reference": 7680, "Name": "Explant Device Serial Number", "Section Display Name": "Explant Device Information", "Coding Instructions": "Indicate the serial number of the explanted device.", "Target Value": "Any occurrence between previous device implant and current procedure", "Short Name": "ICDExpSerNo", "Data Type": "ST", "Precision": "30", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.850", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "When Explant Device Serial Number (7680) is answered, Explant Device ID (7675) cannot be Null", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7660, "Parent Element Name": "Device Explanted", "Parent Element Selection Name": "Explanted"}]}, {"Element Reference": 7685, "Name": "Explant Unique Device Identifier", "Section Display Name": "Explant Device Information", "Coding Instructions": "Indicate the direct identifier portion of the Unique Device Identifier (UDI) associated with the device used for implant. This ID is provided by the device manufacturer, and is either a GTIN or HIBC number.", "Target Value": "Any occurrence on current procedure", "Short Name": "ICDExplantUDI", "Data Type": "ST", "Precision": "150", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "No Action", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3719", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Unique Device Identifier (UDI)", "Definition": "An identifier that is the main (primary) lookup for a medical device product and meets the requirements to uniquely identify a device through its distribution and use. This value is supplied to the FDA by the manufacturer.", "Source": "US FDA"}, "Parent Child Validations": [{"Parent Element Reference": 7660, "Parent Element Name": "Device Explanted", "Parent Element Selection Name": "Explanted"}]}, {"Element Reference": 7670, "Name": "Explant Treatment Recommendation", "Section Display Name": "Explant Device Information", "Coding Instructions": "Indicate the planned treatment post explant of the device at the time of the current procedure.", "Target Value": "Any occurrence on current procedure", "Short Name": "ExplantTreatment", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001003", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Explant Treatment Recommendation", "Code": "100001049", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "No Re-implant", "Selection Definition": "The device has been explanted with no re-implant of any device with pacing or defibrillation capabilities during the current procedure.", "Display Order": 1}, {"Name": "Explant Treatment Recommendation", "Code": "100000995", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Downgrade", "Selection Definition": "The ICD/CRT-D device has been explanted with re-implant of a device with only pacing and no defibrillation capabilities during the current procedure.", "Display Order": 2}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7010, "Parent Element Name": "Procedure Type", "Parent Element Selection Name": "Generator explant"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "F. Procedure Information", "Section Display Name": "H. Lead Assessment", "Section Code": "LEADASSESSMENT", "Section Type": "Section", "Cardinality": "0..1", "Table": "PROCINFO", "Elements": [{"Element Reference": 7690, "Name": "Lead Operator Last Name", "Section Display Name": "H. Lead Assessment", "Coding Instructions": "Indicate the last name of the operator who is performing the lead procedure.\n\nNote(s):\nIf the name exceeds 50 characters, enter the first 50 letters only.\n\nIf more than one physician performs the lead procedure, code the\noperator of record.", "Target Value": "The value on current procedure", "Short Name": "LeadOpLName", "Data Type": "LN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001853", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7695, "Name": "Lead Operator First Name", "Section Display Name": "H. Lead Assessment", "Coding Instructions": "Indicate the first name of the operator who is performing the lead procedure.\n\nNote(s):\nIf the name exceeds 50 characters, enter the first 50 letters only.\n\nIf more than one physician performs the lead procedure, code the\noperator of record.", "Target Value": "The value on current procedure", "Short Name": "LeadOpFName", "Data Type": "FN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001853", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7700, "Name": "Lead Operator Middle Name", "Section Display Name": "H. Lead Assessment", "Coding Instructions": "Indicate the middle name of the operator who is performing the lead procedure.\n\nNote(s):\nIt is acceptable to specify the middle initial.\n\nIf there is no middle name given, leave field blank.\n\nIf there are multiple middle names, enter all of the middle names sequentially.\n\nIf the name exceeds 50 characters, enter the first 50 letters only.", "Target Value": "The value on current procedure", "Short Name": "LeadOpMName", "Data Type": "MN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001853", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7705, "Name": "Lead Operator NPI", "Section Display Name": "H. Lead Assessment", "Coding Instructions": "Indicate the National Provider Identifier (NPI) of the operator who is performing the lead procedure. NPI's, assigned by the Centers for Medicare and Medicaid Services (CMS), are used to uniquely identify physicians for Medicare billing purposes.", "Target Value": "The value on current procedure", "Short Name": "LeadOpNPI", "Data Type": "NUM", "Precision": "10", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.4.6", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "H. Lead Assessment", "Section Display Name": "Leads", "Section Code": "Leads", "Section Type": "Repeater Section", "Cardinality": "0..n", "Table": "Leads", "Elements": [{"Element Reference": 7710, "Name": "Lead Counter", "Section Display Name": "Leads", "Coding Instructions": "The software-assigned lead counter should start at one and be incremented by one for each new or existing lead documented.", "Target Value": "N/A", "Short Name": "LeadCounter", "Data Type": "CTR", "Precision": "2", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001858", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": null, "Usual Range Min": null, "Usual Range Max": null, "Valid Range Min": 1.0, "Valid Range Max": "99"}], "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7715, "Name": "Lead Identification", "Section Display Name": "Leads", "Coding Instructions": "Indicate if the lead is a new or existing lead.  All new leads placed or existing leads extracted, abandoned, or reused should be identified in the leads section.\n\nNote(s):\nIf a lead was attempted, but not actually implanted, do not include it. For example, if a lead turns out to be too short, or with inadequate coil spacing, or is too large/unstable for the coronary sinus branch vein, do not include it in the registry.", "Target Value": "The value on current procedure", "Short Name": "LeadType", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000990", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Lead Identification", "Code": "100001047", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "New   ", "Selection Definition": "A lead that is implanted for the first time.", "Display Order": 1}, {"Name": "Lead Identification", "Code": "100001001", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Existing", "Selection Definition": "A lead that has been previously implanted.", "Display Order": 2}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7720, "Name": "Lead Identification Number", "Section Display Name": "Leads", "Coding Instructions": "Indicate the assigned identification for new or existing leads placed, reused, extracted or abandoned during the procedure.\n\nNote(s):\nThe lead devices that should be collected in your application are controlled by a Leads Device Master file. This file is maintained by the NCDR and will be made available on the internet for downloading and importing/updating into your application.", "Target Value": "The value on current procedure", "Short Name": "LeadID", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "Yes", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.6.1.20", "Code System": "2.16.840.1.113883.3.3478.6.1.20", "Code System Name": "ACC NCDR Lead Devices", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7725, "Name": "Lead Serial Number", "Section Display Name": "Leads", "Coding Instructions": "Indicate the manufacturer's serial number of the lead.", "Target Value": "The value on current procedure", "Short Name": "LeadSerNo", "Data Type": "ST", "Precision": "30", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.850", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "A Lead Serial Number (7725) may only be entered/selected once\n\nWhen Lead Serial Number (7725) is answered, Lead Identification Number (7720) cannot be Null", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7730, "Name": "Lead Unique Device Identifier", "Section Display Name": "Leads", "Coding Instructions": "Indicate the direct identifier portion of the Unique Device Identifier (UDI) associated with the device used for implant. This ID is provided by the device manufacturer, and is either a GTIN or HIBC number.", "Target Value": "The value on current procedure", "Short Name": "LeadUDI", "Data Type": "ST", "Precision": "150", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "No Action", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3719", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Unique Device Identifier (UDI)", "Definition": "An identifier that is the main (primary) lookup for a medical device product and meets the requirements to uniquely identify a device through its distribution and use. This value is supplied to the FDA by the manufacturer.", "Source": "US FDA"}, "Parent Child Validations": null}, {"Element Reference": 7735, "Name": "Lead Location", "Section Display Name": "Leads", "Coding Instructions": "Indicate the location of the lead.", "Target Value": "Any occurrence on current procedure", "Short Name": "LeadLocation", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001246", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Lead Location", "Code": "3194006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "RA endocardial", "Selection Definition": "A pacing lead placed transvenously into the right atrial endocardium.", "Display Order": 1}, {"Name": "Lead Location", "Code": "112000002026", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "RA epicardial", "Selection Definition": "A pacing or defibrillating lead placed on the outside of the cardiac muscle onto right atrium", "Display Order": 2}, {"Name": "Lead Location", "Code": "100001136", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "LV epicardial (CVS)", "Selection Definition": "A pacing or defibrillating lead placed transvenously onto the left ventricle through the coronary venous system.", "Display Order": 3}, {"Name": "Lead Location", "Code": "100001135", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "LV epicardial (surgical)", "Selection Definition": "A pacing or defibrillation lead placed transthoracically onto the left ventricular epicardium.", "Display Order": 4}, {"Name": "Lead Location", "Code": "304059001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "RV endocardial", "Selection Definition": "A pacing or defibrillation lead placed transvenously into the right ventricular endocardium.", "Display Order": 5}, {"Name": "Lead Location", "Code": "112000002027", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "RV epicardial", "Selection Definition": "A pacing or defibrillating lead placed on the outside of the cardiac muscle onto right ventricle.", "Display Order": 6}, {"Name": "Lead Location", "Code": "345000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "His bundle", "Selection Definition": "A pacing or defibrillating lead placed at the location of the His bundle.", "Display Order": 7}, {"Name": "Lead Location", "Code": "74031005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Left bundle", "Selection Definition": "A pacing or defibrillating lead placed at the location of the left bundle.", "Display Order": 8}, {"Name": "Lead Location", "Code": "100001137", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Superior Vena Cava/subclavian", "Selection Definition": "A defibrillating lead placed in the superior vena cava or subclavian vein.", "Display Order": 9}, {"Name": "Lead Location", "Code": "100001138", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Subcutaneous ICD", "Selection Definition": "A defibrillation lead placed subcutaneously.", "Display Order": 10}, {"Name": "Lead Location", "Code": "100001106", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Subcutaneous array", "Selection Definition": "A defibrillation electrode that is placed subcutaneously.", "Display Order": 11}, {"Name": "Lead Location", "Code": "33547000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Substernal", "Selection Definition": "A pacing or defibrillating lead placed under the sternum.", "Display Order": 12}, {"Name": "Lead Location", "Code": "72107004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Azygos vein", "Selection Definition": "A pacing or defibrillating lead placed in a vein (azygos) on the right side at the back of the thorax.", "Display Order": 13}, {"Name": "Lead Location", "Code": "100001066", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Other Lead location", "Selection Definition": "A lead placed in a location not specified above.", "Display Order": 14}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7740, "Name": "Existing Lead Implant Date", "Section Display Name": "Leads", "Coding Instructions": "Indicate the date the existing lead was initially implanted.\n\nNote(s):\nIf the month or day of the implant is unknown, please code 01/01/YYYY. If the specific year is unknown in the current record, the year may be estimated based on timeframes found in prior medical record documentation (Example: If the patient had a lead implant documented in a record from 2011, then the year 2011 can be utilized and coded as 01/01/2011).", "Target Value": "The last value between birth and current procedure", "Short Name": "ExLeadDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001015", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Existing Lead Implant Date (7740) must be Greater than or Equal to Birth Date (2050)\n\nExisting Lead Implant Date (7740) must be Less than or Equal to Procedure Start Date and Time (7000)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7715, "Parent Element Name": "Lead Identification", "Parent Element Selection Name": "Existing"}]}, {"Element Reference": 7745, "Name": "Existing Lead Status", "Section Display Name": "Leads", "Coding Instructions": "Indicate the status of the existing lead.", "Target Value": "Any occurrence on current procedure", "Short Name": "ExLeadStat", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000989", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Existing Lead Status", "Code": "100001004", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extracted", "Selection Definition": "The existing lead was extracted in whole or part and removed.", "Display Order": 1}, {"Name": "Existing Lead Status", "Code": "100000925", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Abandoned", "Selection Definition": "The existing lead was left in situ, abandoned and not reused.", "Display Order": 2}, {"Name": "Existing Lead Status", "Code": "100001099", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Reused", "Selection Definition": "The existing lead was left in situ and reused.", "Display Order": 3}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7715, "Parent Element Name": "Lead Identification", "Parent Element Selection Name": "Existing"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "F. Procedure Information", "Section Display Name": "I. Intra Or Post Procedure Events", "Section Code": "IPPEVENTS", "Section Type": "Section", "Cardinality": "0..1", "Table": "PROCINFO", "Elements": [{"Element Reference": 9000, "Name": "Cardiac Arrest", "Section Display Name": "I. Intra Or Post Procedure Events", "Coding Instructions": "Indicate if the patient experienced cardiac arrest.", "Target Value": "Any occurrence between start of procedure and until next procedure or discharge", "Short Name": "CArrest", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "410429000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Cardiac Arrest", "Definition": "\"Sudden\" Cardiac arrest is the sudden cessation of cardiac activity. The victim becomes unresponsive with no normal breathing and no signs of circulation. If corrective measures are not taken rapidly, this condition progresses to sudden death. Cardiac arrest should be used to signify an event as described above that is reversed, usually by CPR and/or defibrillation or cardioversion or cardiac pacing.", "Source": "ACCF/AHA 2011 Key Data Elements and Definitions of a Base Cardiovascular Vocabulary for Electronic Health Records. JACC Vol. 58, No. 2, 2011 <PERSON><PERSON><PERSON><PERSON> et al. 203; July 5, 2011:202-22"}, "Parent Child Validations": null}, {"Element Reference": 9005, "Name": "Myocardial Infarction", "Section Display Name": "I. Intra Or Post Procedure Events", "Coding Instructions": "Indicate if the patient had a myocardial infarction.", "Target Value": "Any occurrence between start of procedure and until next procedure or discharge", "Short Name": "PostMI", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "22298006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Myocardial Infarction/Prior MI", "Definition": "Criteria for acute myocardial infarction: \nThe term acute myocardial infarction (MI) should be used when there is evidence of myocardial necrosis in a clinical setting consistent with acute myocardial ischemia. Under these conditions any one of the following criteria meets the diagnosis for MI:\n- Detection of a rise and/or fall of cardiac biomarker values [preferably cardiac troponin (cTn) with at least one value above the 99th percentile upper reference limit (URL) and with at least one of the following:\n\nSymptoms of ischemia.\nNew or presumed new significant ST-segment-T wave (ST-T) changes or new left bundle branch block (LBBB). Development of pathological Q waves in the ECG.\nImaging evidence of new loss of viable myocardium or new regional wall motion abnormality. Identification of an intracoronary thrombus by angiography or autopsy.\n\n- Cardiac death with symptoms suggestive of myocardial ischemia and presumed new ischemic ECG changes or new LBBB, but death occurred before cardiac biomarkers were obtained, or before cardiac biomarker values would be increased.\n\n- Percutaneous coronary intervention (PCI) related MI is arbitrarily defined by elevation of cTn values (>5 x 99th percentile URL) in patients with normal baseline values (99th percentile URL) or a rise of cTn values >20% if the baseline values are elevated and are stable or falling. In addition, either (i) symptoms suggestive of myocardial ischemia or (ii) new ischemic ECG changes or (iii) angiographic findings consistent with a procedural complication or (iv) imaging demonstration of new loss of viable myocardium or new regional wall motion abnormality are required.\n\n- Stent thrombosis associated with MI when detected by coronary angiography or autopsy in the setting of myocardial ischemia and with a rise and/or fall of cardiac biomarker values with at least one value above the 99th percentile URL.\n\n- Coronary artery bypass grafting (CABG) related MI is arbitrarily defined by elevation of cardiac biomarker values (>10 x 99th percentile URL) in patients with normal baseline cTn values (99th percentile URL). In addition, either (i) new pathological Q waves or new LBBB, or (ii) angiographic documented new graft or new native coronary artery occlusion, or (iii) imaging evidence of new loss of viable myocardium or new regional wall motion abnormality.\n\nAny one of the following criteria meets the diagnosis for prior MI:\n- Pathological Q waves with or without symptoms in the absence of non-ischemic causes.\n- Imaging evidence of a region of loss of viable myocardium that is thinned and fails to contract, in the absence of a non-ischemic cause.\n- Pathological findings of a prior MI.", "Source": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>fe AS, et al. Third Universal Definition of Myocardial Infarction. J Am Coll Cardiol. 2012;60(16):1581-1598. doi:10.1016/j.jacc.2012.08.001."}, "Parent Child Validations": null}, {"Element Reference": 9010, "Name": "Cardiac Perforation", "Section Display Name": "I. Intra Or Post Procedure Events", "Coding Instructions": "Indicate if the patient had a new cardiac perforation occurred.\n\nNote(s):\nCardiac perforation may or may not be symptomatic and may or may not be self sealing.  It can be documented by migration of pacing or defibrillator leads to the epicardial surface, resulting in pain and/or hypotension, pericardial effusion, cardiac tamponade, failure to capture, capture of the diaphragm, phrenic nerve or intercostals muscle of sufficient magnitude to require repositioning.", "Target Value": "Any occurrence between start of procedure and until next procedure or discharge", "Short Name": "CardiacPerf", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "36191001:123005000=302509004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 9015, "Name": "Coronary Venous Dissection", "Section Display Name": "I. Intra Or Post Procedure Events", "Coding Instructions": "Indicate if the patient had a coronary venous dissection as documented by manipulation of the pacing or defibrillating leads in the coronary sinus which can result in a tear of the coronary sinus endothelium with dissection into the coronary sinus wall sometimes at times referred to as \"staining\" following contrast injection.  This can also result in perforation of the coronary sinus.", "Target Value": "Any occurrence between start of procedure and until next procedure or discharge", "Short Name": "CVDissect", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000029", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 9055, "Name": "Cardiac Tamponade", "Section Display Name": "I. Intra Or Post Procedure Events", "Coding Instructions": "Indicate if the patient experienced fluid in the pericardial space compromising cardiac filling and requiring intervention.", "Target Value": "Any occurrence between start of procedure and until next procedure or discharge", "Short Name": "Tamponade", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "35304003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 9120, "Name": "Stroke", "Section Display Name": "I. Intra Or Post Procedure Events", "Coding Instructions": "Indicate if the patient was diagnosed with a stroke.", "Target Value": "Any occurrence between start of procedure and until next procedure or discharge", "Short Name": "Stroke", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "230690007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Stroke (CVA)", "Definition": "An ischemic stroke is an acute episode of focal or global neurological  dysfunction caused by brain, spinal cord, or retinal vascular injury as a result of  infarction of central nervous system tissue. Hemorrhage may be a consequence of ischemic stroke. In this situation, the stroke is an ischemic stroke with hemorrhagic transformation and not a hemorrhagic stroke. A hemorrhagic stroke is defined as an acute episode of focal or global cerebral or spinal dysfunction caused by intraparenchymal, intraventricular, or subarachnoid hemorrhage (note: subdural hematomas are intracranial hemorrhagic events and not strokes).", "Source": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, et al. 2014 ACC/AHA Key Data Elements and Definitions for Cardiovascular Endpoint Events in Clinical Trials: A Report of the American College of Cardiology/American Heart Association Task Force on Clinical Data Standards (Writing Committee to Develop Cardiovascular Endpoints Data Standards). J Am Coll Cardiol. 2015;():. Doi:10.1016/j.jacc.2014.12.018."}, "Parent Child Validations": null}, {"Element Reference": 9140, "Name": "Transient Ischemic Attack (TIA)", "Section Display Name": "I. Intra Or Post Procedure Events", "Coding Instructions": "Indicate if the patient had a transient ischemic attack (TIA).\n\nNote(s):\nPersistence of symptoms is an acceptable indicator of acute infarction. If it is used, duration of symptom persistence that will be used to distinguish between transient ischemia and acute infarction should be defined for any clinical trial in which it is used.", "Target Value": "Any occurrence between start of procedure and until next procedure or discharge", "Short Name": "PostTIA", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "266257000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Transient Ischemic Attack (TIA)", "Definition": "Transient episode of focal neurological dysfunction caused by brain, spinal cord, or retinal ischemia without acute infarction.", "Source": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, et al. 2014 ACC/AHA Key Data Elements and Definitions for Cardiovascular Endpoint Events in Clinical Trials: A Report of the American College of Cardiology/American Heart Association Task Force on Clinical Data Standards (Writing Committee to Develop Cardiovascular Endpoints Data Standards). J Am Coll Cardiol. 2015;():.Doi:10.1016/j.jacc.2014.12.018."}, "Parent Child Validations": null}, {"Element Reference": 9180, "Name": "Hematoma", "Section Display Name": "I. Intra Or Post Procedure Events", "Coding Instructions": "Indicate if the patient experienced a pocket hematoma as a result of the procedure, requiring a reoperation, evacuation or transfusion.", "Target Value": "Any occurrence between start of procedure and until next procedure or discharge", "Short Name": "Hematoma", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "385494008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 9195, "Name": "Infection Requiring Antibiotics", "Section Display Name": "I. Intra Or Post Procedure Events", "Coding Instructions": "Indicate if the patient experienced an infection related to the procedure which required antibiotics.", "Target Value": "Any occurrence between start of procedure and until next procedure or discharge", "Short Name": "InfectionReqAnti", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001017", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 9205, "Name": "Hemothorax", "Section Display Name": "I. Intra Or Post Procedure Events", "Coding Instructions": "Indicate if the patient experienced a hemothorax as documented by accumulation of blood in the thorax.", "Target Value": "Any occurrence between start of procedure and until next procedure or discharge", "Short Name": "Hemothorax", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "31892009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 9215, "Name": "Pneumothorax", "Section Display Name": "I. Intra Or Post Procedure Events", "Coding Instructions": "Indicate if the patient experienced a pneumothorax requiring intervention (chest tube).", "Target Value": "Any occurrence between start of procedure and until next procedure or discharge", "Short Name": "Pneumothorax", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "36118008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 9250, "Name": "Urgent Cardiac Surgery", "Section Display Name": "I. Intra Or Post Procedure Events", "Coding Instructions": "Indicate if the patient needed to have urgent, unplanned cardiac surgery.", "Target Value": "Any occurrence between start of procedure and until next procedure or discharge", "Short Name": "UrgentSurgery", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "64915003:260870009=103391001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "I. Intra Or Post Procedure Events", "Section Display Name": "Post Procedure Events", "Section Code": "PostProcedureEvents", "Section Type": "Section", "Cardinality": "0..1", "Table": "PROCINFO", "Elements": [{"Element Reference": 9255, "Name": "<PERSON>rew Problem", "Section Display Name": "Post Procedure Events", "Coding Instructions": "Indicate if the patient had a pacing and/or sensing problem associated with high impedance due to a poor connection between a lead and device caused by a loose set screw.\n\nNote(s):\nIndicate if the patient experienced a set screw problem between completion of ICD procedure until next ICD procedure or discharge.", "Target Value": "Any occurrence between completion of the procedure and until next procedure or discharge", "Short Name": "SetScrew", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000038", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 9260, "Name": "Lead Dislodgement", "Section Display Name": "Post Procedure Events", "Coding Instructions": "Indicate if the patient experienced a lead dislodgement as documented by movement of a lead that requires repositioning and reoperation.", "Target Value": "Any occurrence between completion of the procedure and until next procedure or discharge", "Short Name": "LeadDislodge", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "234233007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 9265, "Name": "Lead Location (Dislodgement)", "Section Display Name": "Post Procedure Events", "Coding Instructions": "Indicate the location of the lead in which the dislodgement occurred.", "Target Value": "Any occurrence between completion of the procedure and until next procedure or discharge", "Short Name": "LeadDislodgeLoc", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001246", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Lead Location (Dislodgement)", "Code": "3194006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "RA endocardial", "Selection Definition": "A pacing lead placed transvenously into the right atrial endocardium.", "Display Order": 1}, {"Name": "Lead Location (Dislodgement)", "Code": "112000002026", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "RA epicardial", "Selection Definition": "A pacing or defibrillating lead placed on the outside of the cardiac muscle onto right atrium", "Display Order": 2}, {"Name": "Lead Location (Dislodgement)", "Code": "100001136", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "LV epicardial (CVS)", "Selection Definition": "A pacing or defibrillating lead placed transvenously onto the left ventricle through the coronary venous system.", "Display Order": 3}, {"Name": "Lead Location (Dislodgement)", "Code": "100001135", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "LV epicardial (surgical)", "Selection Definition": "A pacing or defibrillation lead placed transthoracically onto the left ventricular epicardium.", "Display Order": 4}, {"Name": "Lead Location (Dislodgement)", "Code": "304059001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "RV endocardial", "Selection Definition": "A pacing or defibrillation lead placed transvenously into the right ventricular endocardium.", "Display Order": 5}, {"Name": "Lead Location (Dislodgement)", "Code": "112000002027", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "RV epicardial", "Selection Definition": "A pacing or defibrillating lead placed on the outside of the cardiac muscle onto right ventricle.", "Display Order": 6}, {"Name": "Lead Location (Dislodgement)", "Code": "345000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "His bundle", "Selection Definition": "A pacing or defibrillating lead placed at the location of the His bundle.", "Display Order": 7}, {"Name": "Lead Location (Dislodgement)", "Code": "74031005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Left bundle", "Selection Definition": "A pacing or defibrillating lead placed at the location of the left bundle.", "Display Order": 8}, {"Name": "Lead Location (Dislodgement)", "Code": "100001137", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Superior Vena Cava/subclavian", "Selection Definition": "A defibrillating lead placed in the superior vena cava or subclavian vein.", "Display Order": 9}, {"Name": "Lead Location (Dislodgement)", "Code": "100001138", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Subcutaneous ICD", "Selection Definition": "A defibrillation lead placed subcutaneously.", "Display Order": 10}, {"Name": "Lead Location (Dislodgement)", "Code": "100001106", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Subcutaneous array", "Selection Definition": "A defibrillation electrode that is placed subcutaneously.", "Display Order": 11}, {"Name": "Lead Location (Dislodgement)", "Code": "33547000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Substernal", "Selection Definition": "A pacing or defibrillating lead placed under the sternum.", "Display Order": 12}, {"Name": "Lead Location (Dislodgement)", "Code": "72107004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Azygos vein", "Selection Definition": "A pacing or defibrillating lead placed in a vein (azygos) on the right side at the back of the thorax.", "Display Order": 13}, {"Name": "Lead Location (Dislodgement)", "Code": "100001066", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Other Lead location", "Selection Definition": "A lead placed in a location not specified above.", "Display Order": 14}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 9260, "Parent Element Name": "Lead Dislodgement", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "<PERSON><PERSON>", "Section Code": "DISCHARGE", "Section Type": "Section", "Cardinality": "1..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 10005, "Name": "Coronary Artery Bypass Graft", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if coronary artery bypass graft (CABG) Surgery was performed.", "Target Value": "Any occurrence between arrival and discharge", "Short Name": "CABG", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "232717009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 10010, "Name": "Coronary Artery Bypass Graft Date", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate the date of the coronary artery bypass graft (CABG) surgery.\n\nNote(s):\nIf the month or day of the CABG is unknown, please code 01/01/YYYY. If the specific year is unknown in the current record, the year may be estimated based on timeframes found in prior medical record documentation (Example: If the patient had CABG documented in a record from 2011, then the year 2011 can be utilized and coded as 01/01/2011).", "Target Value": "The first value between arrival and discharge", "Short Name": "CABGDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "232717009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "Coronary Artery Bypass Graft Date (10010) must be Greater than or Equal to Arrival Date (3000)\n\nCoronary Artery Bypass Graft Date (10010) must be Less than or Equal to Discharge Date (10100)\n\nCoronary Artery Bypass Graft Date (10010) must be Greater than or Equal to Birth Date (2050)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 10005, "Parent Element Name": "Coronary Artery Bypass Graft", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 10015, "Name": "Percutaneous Coronary Intervention", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if the patient had a percutaneous coronary intervention (PCI).", "Target Value": "Any occurrence between arrival and discharge", "Short Name": "PCI", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "415070008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 10020, "Name": "Percutaneous Coronary Intervention Date", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate the date of the percutaneous coronary intervention (PCI) procedure.\n\nNote(s):\nIf the month or day of the PCI is unknown, please code 01/01/YYYY. If the specific year is unknown in the current record, the year may be estimated based on timeframes found in prior medical record documentation (Example: If the patient had PCI documented in a record from 2011, then the year 2011 can be utilized and coded as 01/01/2011).", "Target Value": "The first value between arrival and discharge", "Short Name": "PCIDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "415070008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "Percutaneous Coronary Intervention Date (10020) must be Greater than or Equal to Birth Date (2050)\n\nPercutaneous Coronary Intervention Date (10020) must be Less than or Equal to Discharge Date (10100)\n\nPercutaneous Coronary Intervention Date (10020) must be Greater than or Equal to Arrival Date (3000)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 10015, "Parent Element Name": "Percutaneous Coronary Intervention", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 10100, "Name": "Discharge Date", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate the date on which the patient was discharged from your facility.", "Target Value": "The value on discharge", "Short Name": "DCDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Discharge Date (10100) must be greater than or equal to '04/01/2021' to participate in ICD v2.3.\n\nDischarge Date (10100) must be Greater than or Equal to Birth Date (2050)\n\nDischarge Date (10100) and Arrival Date and Time (3000) must not overlap on multiple episodes", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 10105, "Name": "Discharge Status", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate whether the patient was alive or deceased at discharge.", "Target Value": "The value on discharge", "Short Name": "DCStatus", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "75527-2", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": [{"Name": "Discharge Status", "Code": "438949009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Alive", "Selection Definition": null, "Display Order": 1}, {"Name": "Discharge Status", "Code": "20", "Code System": "2.16.840.1.113883.12.112", "Code System Name": "HL7 Discharge disposition", "Selection Name": "Deceased", "Selection Definition": null, "Display Order": 2}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 10110, "Name": "Discharge Location", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate the location to which the patient was discharged.", "Target Value": "The value on discharge", "Short Name": "DCLocation", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "75528-0", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": [{"Name": "Discharge Location", "Code": "01", "Code System": "2.16.840.1.113883.12.112", "Code System Name": "HL7 Discharge disposition", "Selection Name": "Home", "Selection Definition": null, "Display Order": 1}, {"Name": "Discharge Location", "Code": "62", "Code System": "2.16.840.1.113883.12.112", "Code System Name": "HL7 Discharge disposition", "Selection Name": "Discharged/transferred to an Extended care/TCU/rehab", "Selection Definition": "Continued \"non-acute\" care at an extended care facility, transitional care unit, or rehabilitation unit.", "Display Order": 2}, {"Name": "Discharge Location", "Code": "02", "Code System": "2.16.840.1.113883.12.112", "Code System Name": "HL7 Discharge disposition", "Selection Name": "Other acute care hospital", "Selection Definition": null, "Display Order": 3}, {"Name": "Discharge Location", "Code": "64", "Code System": "2.16.840.1.113883.12.112", "Code System Name": "HL7 Discharge disposition", "Selection Name": "Skilled Nursing facility", "Selection Definition": null, "Display Order": 4}, {"Name": "Discharge Location", "Code": "100001249", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Other Discharge Location", "Selection Definition": null, "Display Order": 5}, {"Name": "Discharge Location", "Code": "07", "Code System": "2.16.840.1.113883.12.112", "Code System Name": "HL7 Discharge disposition", "Selection Name": "Left against medical advice (AMA)", "Selection Definition": "The patient was discharged or eloped against medical advice.", "Display Order": 6}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 10105, "Parent Element Name": "Discharge Status", "Parent Element Selection Name": "Alive"}]}, {"Element Reference": 10120, "Name": "Death During the Procedure", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate if the patient expired during the procedure.\n\nNote(s): Make sure to only capture 'death during the procedure' in the procedure appropriate registry. \n \nFor example, if the patient had a CathPCI procedure and a TVT procedure in the same episode of care (hospitalization) but different cath lab visits and the death occurred during the TVT procedure, code 'Yes' only in the TVT Registry and not the CathPCI Registry.  If the CathPCI procedure and TVT procedure occurred during the same cath lab visit then code 'Yes' in both registries.", "Target Value": "Any occurrence on discharge", "Short Name": "DeathProcedure", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000923", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 10105, "Parent Element Name": "Discharge Status", "Parent Element Selection Name": "Deceased"}]}, {"Element Reference": 10125, "Name": "Cause of Death", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate the primary cause of death, i.e. the first significant abnormal event which ultimately led to death.", "Target Value": "The value on time of death", "Short Name": "DeathCause", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "184305005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Cause of Death", "Code": "100000960", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Acute myocardial infarction", "Selection Definition": "Death by any cardiovascular mechanism (e.g., arrhythmia, sudden death, heart failure, stroke, pulmonary embolus, peripheral arterial disease) within 30 days after an acute myocardial infarction, related to the immediate consequences of the MI, such as progressive HF or recalcitrant arrhythmia. There may be other assessable (attributable) mechanisms of cardiovascular death during this time period, but for simplicity, if the cardiovascular death occurs <=30 days of an acute myocardial infarction, it will be considered a death due to myocardial infarction.", "Display Order": 1}, {"Name": "Cause of Death", "Code": "100000978", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Sudden cardiac death", "Selection Definition": "Death that occurs unexpectedly, and not within 30 days of an acute MI.", "Display Order": 2}, {"Name": "Cause of Death", "Code": "100000964", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Heart failure", "Selection Definition": "Death associated with clinically worsening symptoms and/or signs of heart failure.", "Display Order": 3}, {"Name": "Cause of Death", "Code": "100000977", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Stroke", "Selection Definition": "Death after a stroke that is either a direct consequence of the stroke or a complication of the stroke.", "Display Order": 4}, {"Name": "Cause of Death", "Code": "100000962", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Cardiovascular procedure", "Selection Definition": "Death caused by the immediate complication(s) of a cardiovascular procedure.", "Display Order": 5}, {"Name": "Cause of Death", "Code": "100000961", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Cardiovascular hemorrhage", "Selection Definition": "Death related to hemorrhage such as a non-stroke intracranial hemorrhage, non-procedural or non-traumatic vascular rupture (e.g., aortic aneurysm), or hemorrhage causing cardiac tamponade.", "Display Order": 6}, {"Name": "Cause of Death", "Code": "100000972", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Other cardiovascular reason", "Selection Definition": "Cardiovascular death not included in the above categories but with a specific, known cause (e.g., pulmonary embolism, peripheral arterial disease).", "Display Order": 7}, {"Name": "Cause of Death", "Code": "100000975", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Pulmonary", "Selection Definition": "Non-cardiovascular death attributable to disease of the lungs (excludes malignancy).", "Display Order": 8}, {"Name": "Cause of Death", "Code": "100000976", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Renal", "Selection Definition": "Non-cardiovascular death attributable to renal failure.", "Display Order": 9}, {"Name": "Cause of Death", "Code": "100000963", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Gastrointestinal", "Selection Definition": "Non-cardiovascular death attributable to disease of the esophagus, stomach, or intestines (excludes malignancy).", "Display Order": 10}, {"Name": "Cause of Death", "Code": "100000966", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Hepatobiliary", "Selection Definition": "Non-cardiovascular death attributable to disease of the liver, gall bladder, or biliary ducts (exclude malignancy).", "Display Order": 11}, {"Name": "Cause of Death", "Code": "100000974", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Pancreatic", "Selection Definition": "Non-cardiovascular death attributable to disease of the pancreas (excludes malignancy).", "Display Order": 12}, {"Name": "Cause of Death", "Code": "100000967", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Infection", "Selection Definition": "Non-cardiovascular death attributable to an infectious disease.", "Display Order": 13}, {"Name": "Cause of Death", "Code": "100000968", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Inflammatory/Immunologic", "Selection Definition": "Non-cardiovascular death attributable to an inflammatory or immunologic disease process.", "Display Order": 14}, {"Name": "Cause of Death", "Code": "100000965", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Hemorrhage", "Selection Definition": "Non-cardiovascular death attributable to bleeding that is not considered cardiovascular hemorrhage or stroke per this classification.", "Display Order": 15}, {"Name": "Cause of Death", "Code": "100000971", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Non-cardiovascular procedure or surgery", "Selection Definition": "Death caused by the immediate complication(s) of a non-cardiovascular procedure or surgery.", "Display Order": 16}, {"Name": "Cause of Death", "Code": "100000980", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "<PERSON>rauma", "Selection Definition": "Non-cardiovascular death attributable to trauma.", "Display Order": 17}, {"Name": "Cause of Death", "Code": "100000979", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Suicide", "Selection Definition": "Non-cardiovascular death attributable to suicide.", "Display Order": 18}, {"Name": "Cause of Death", "Code": "100000970", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Neurological", "Selection Definition": "Non-cardiovascular death attributable to disease of the nervous system (excludes malignancy).", "Display Order": 19}, {"Name": "Cause of Death", "Code": "100000969", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Malignancy", "Selection Definition": "Non-cardiovascular death attributable to malignancy.", "Display Order": 20}, {"Name": "Cause of Death", "Code": "100000973", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Other non-cardiovascular reason", "Selection Definition": "Non-cardiovascular death attributable to a cause other than those listed in this classification (specify organ system).", "Display Order": 21}], "Ranges": null, "Definition": {"Title": "Cause of Death", "Definition": "Death is classified into 1 of 3 categories: 1) cardiovascular death;  2) non - cardiovascular death; and 3) undetermined cause of death. \n\nThe intent of the classification schema is to identify one, and only one, of the categories as the underlying cause of death. The key priority is differentiating between cardiovascular and non-cardiovascular causes of death.", "Source": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, et al. 2014 ACC/AHA Key Data Elements and Definitions for Cardiovascular Endpoint Events in Clinical Trials: A Report of the American College of Cardiology/American Heart Association Task Force on Clinical Data Standards (Writing Committee to Develop Cardiovascular Endpoints Data Standards). J Am Coll Cardiol. 2015;():. Doi:10.1016/j.jacc.2014.12.018."}, "Parent Child Validations": [{"Parent Element Reference": 10105, "Parent Element Name": "Discharge Status", "Parent Element Selection Name": "Deceased"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "<PERSON><PERSON>", "Section Display Name": "Discharge Medications", "Section Code": "DischargeMeds", "Section Type": "Repeater Section", "Cardinality": "0..n", "Table": "DischargeMeds", "Elements": [{"Element Reference": 10200, "Name": "Discharge Medication Code", "Section Display Name": "Discharge Medications", "Coding Instructions": "Indicate the assigned identification number associated with the medications the patient was prescribed upon discharge.\n\nNote(s):\nDischarge medications not required for patients who expired, discharged to \"Other acute care hospital\", \"Left against medical advice (AMA)\" or are receiving Hospice Care.\n\nThe medication(s) collected in this field are controlled by the Medication Master file. This file is maintained by the NCDR and will be made available on the internet for downloading and importing/updating into your application. Each medication in the Medication Master file is assigned to a value set. The value set is used to separate procedural medications from medications prescribed at discharge. The separation of these medications is depicted on the data collection form.", "Target Value": "N/A", "Short Name": "DC_MedID", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "Yes", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100013057", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "When Discharge Medications Prescribed (10205) is answered, Discharge Medication Code (10200) cannot be Null\n\nDischarge Medication Code (10200) should not be duplicated in an episode", "Selections": [{"Name": "Discharge Medication Code", "Code": "41549009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Angiotensin Converting Enzyme Inhibitor", "Selection Definition": null, "Display Order": 1}, {"Name": "Discharge Medication Code", "Code": "372603003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Aldosterone Antagonist", "Selection Definition": null, "Display Order": 2}, {"Name": "Discharge Medication Code", "Code": "112000001832", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Angiotensin Receptor-Neprilysin Inhibitor", "Selection Definition": null, "Display Order": 3}, {"Name": "Discharge Medication Code", "Code": "67507000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Antiarrhythmic Drug", "Selection Definition": null, "Display Order": 4}, {"Name": "Discharge Medication Code", "Code": "11289", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Warfarin", "Selection Definition": null, "Display Order": 5}, {"Name": "Discharge Medication Code", "Code": "372560006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Antiplatelet agent", "Selection Definition": null, "Display Order": 6}, {"Name": "Discharge Medication Code", "Code": "1191", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "<PERSON><PERSON><PERSON>", "Selection Definition": null, "Display Order": 7}, {"Name": "Discharge Medication Code", "Code": "372913009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Angiotensin II Receptor Blocker", "Selection Definition": null, "Display Order": 8}, {"Name": "Discharge Medication Code", "Code": "33252009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Beta Blocker", "Selection Definition": null, "Display Order": 9}, {"Name": "Discharge Medication Code", "Code": "1364430", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Apixaban", "Selection Definition": null, "Display Order": 10}, {"Name": "Discharge Medication Code", "Code": "1546356", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Dabigatran", "Selection Definition": null, "Display Order": 11}, {"Name": "Discharge Medication Code", "Code": "1599538", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Edoxaban", "Selection Definition": null, "Display Order": 12}, {"Name": "Discharge Medication Code", "Code": "1114195", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Rivaroxaban", "Selection Definition": null, "Display Order": 13}, {"Name": "Discharge Medication Code", "Code": "426228001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Renin Inhibitor", "Selection Definition": null, "Display Order": 14}, {"Name": "Discharge Medication Code", "Code": "112000001831", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Selective Sinus Node I/f Channel Inhibitor", "Selection Definition": null, "Display Order": 15}, {"Name": "Discharge Medication Code", "Code": "96302009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Statin", "Selection Definition": null, "Display Order": 16}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 10205, "Name": "Discharge Medication Prescribed", "Section Display Name": "Discharge Medications", "Coding Instructions": "Indicate if the medication was prescribed, not prescribed, or was not prescribed for either a medical or patient reason.\n\nNote(s): \nDischarge medications do not need to be recorded for patients who were discharged to \"Other acute care hospital\", \"Left against medical advice (AMA)\" or are receiving Hospice Care is 'Yes'. ", "Target Value": "The value on discharge", "Short Name": "DC_MedAdmin", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "432102000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "When Discharge Medication Code (10200) is answered, Discharge Medications Prescribed (10205) cannot be Null", "Selections": [{"Name": "Discharge Medication Prescribed", "Code": "100001247", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Yes - Prescribed", "Selection Definition": "Code 'Yes' if this medication was initiated (or prescribed) post procedure and for discharge.", "Display Order": 1}, {"Name": "Discharge Medication Prescribed", "Code": "100001048", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not Prescribed - No Reason", "Selection Definition": "Code 'No' if this medication was not prescribed post procedure or for discharge and there was no mention of a reason  why it was not ordered within the medical documentation.", "Display Order": 2}, {"Name": "Discharge Medication Prescribed", "Code": "100001034", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not Prescribed - Medical Reason", "Selection Definition": "Code 'No Medical Reason' if this medication was not prescribed post procedure or for discharge and there was a reason documented related to a medical issue or medical concern for not prescribing the medicine.", "Display Order": 3}, {"Name": "Discharge Medication Prescribed", "Code": "100001071", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not Prescribed - Patient Reason", "Selection Definition": "Code 'No, Patient Reason' if this medication was not prescribed post procedure or for discharge and there was a reason documented related to the patient's preference.", "Display Order": 4}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 10105, "Parent Element Name": "Discharge Status", "Parent Element Selection Name": "Alive"}, {"Parent Element Reference": 10110, "Parent Element Name": "Discharge Location", "Parent Element Selection Name": "Skilled Nursing facility"}, {"Parent Element Reference": 10110, "Parent Element Name": "Discharge Location", "Parent Element Selection Name": "Discharged/transferred to an Extended care/TCU/rehab"}, {"Parent Element Reference": 10110, "Parent Element Name": "Discharge Location", "Parent Element Selection Name": "Home"}, {"Parent Element Reference": 10110, "Parent Element Name": "Discharge Location", "Parent Element Selection Name": "Other Discharge Location"}]}]}, {"Container Class": "submissionInfoContainer", "Parent Section": "Root", "Section Display Name": "Z. Administration", "Section Code": "ADMIN", "Section Type": "Section", "Cardinality": "1..1", "Table": "ADMIN", "Elements": [{"Element Reference": 1000, "Name": "Participant ID", "Section Display Name": "Z. Administration", "Coding Instructions": "Indicate the participant ID of the submitting facility.", "Target Value": "N/A", "Short Name": "PartID", "Data Type": "NUM", "Precision": "8", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.836", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Participant ID", "Definition": "Participant ID is a unique number assigned to each database participant by NCDR. A database participant is defined as one entity that signs a Participation Agreement with the NCDR, submits one data submission file to the harvest, and receives one report on their data.\n\nEach participant's data if submitted to harvest must be in one data submission file for a quarter. If one participant keeps their data in more than one file (e.g. at two sites), then the data must be combined into a single data submission to the system to file for the harvest. If two or more participants share a single purchased software, and enter cases into one database, then the data must be exported into different data submission files, one for each participant ID.", "Source": "NCDR"}, "Parent Child Validations": null}, {"Element Reference": 1010, "Name": "Participant Name", "Section Display Name": "Z. Administration", "Coding Instructions": "Indicate the full name of the facility where the procedure was performed.\n\nNote(s):\nValues should be full, official hospital names with no abbreviations or variations in spelling.", "Target Value": "N/A", "Short Name": "PartName", "Data Type": "ST", "Precision": "100", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.836", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 1020, "Name": "Time Frame of Data Submission", "Section Display Name": "Z. Administration", "Coding Instructions": "Indicate the time frame of data included in the data submission. Format: YYYYQQ. e.g.,2016Q1", "Target Value": "N/A", "Short Name": "Timeframe", "Data Type": "ST", "Precision": "6", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "1.3.6.1.4.1.19376.1.4.1.6.5.45", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 1040, "Name": "Transmission Number", "Section Display Name": "Z. Administration", "Coding Instructions": "This is a unique number created, and automatically inserted by the software into export file. It identifies the number of times the software has created a data submission file. The transmission number should be incremented by one every time the data submission files are exported. The transmission number should never be repeated.", "Target Value": "N/A", "Short Name": "XmsnId", "Data Type": "NUM", "Precision": "9", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "1.3.6.1.4.1.19376.1.4.1.6.5.45", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": null, "Usual Range Min": null, "Usual Range Max": null, "Valid Range Min": 1.0, "Valid Range Max": "999,999,999"}], "Definition": null, "Parent Child Validations": null}, {"Element Reference": 1050, "Name": "Vendor Identifier", "Section Display Name": "Z. Administration", "Coding Instructions": "Vendor identification (agreed upon by mutual selection between the vendor and the NCDR) to identify software vendor. This is entered into the schema automatically by vendor software. Vendors must use consistent name identification across sites. Changes to vendor name identification must be approved by the NCDR.", "Target Value": "N/A", "Short Name": "VendorId", "Data Type": "ST", "Precision": "15", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.840", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 1060, "Name": "Vendor Software Version", "Section Display Name": "Z. Administration", "Coding Instructions": "Vendor's software product name and version number identifying the software which created this record (assigned by vendor). Vendor controls the value in this field. This is entered into the schema automatically by vendor software.", "Target Value": "N/A", "Short Name": "Vendor<PERSON><PERSON>", "Data Type": "ST", "Precision": "20", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.847", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 1070, "Name": "Registry Identifier", "Section Display Name": "Z. Administration", "Coding Instructions": "The NCDR registry identifier describes the data registry to which these records apply. It is implemented in the software at the time the data is collected and records are created. This is entered into the schema automatically by software.", "Target Value": "N/A", "Short Name": "RegistryId", "Data Type": "ST", "Precision": "30", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "ACC-NCDR-ICD-2.3", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.841", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 1090, "Name": "Patient Population", "Section Display Name": "Z. Administration", "Coding Instructions": "Indicate the population of patients and procedures that are included in the data submission.", "Target Value": "N/A", "Short Name": "PatientPop", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": "LDS", "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001856", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Patient Population", "Code": "100000930", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "All Patients", "Selection Definition": "All patients, all procedures, regardless of insurance payor, ICD indication, or procedure performed.", "Display Order": 1}, {"Name": "Patient Population", "Code": "100001239", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Medicare Primary Prevention Patients", "Selection Definition": "Patient procedures in which Insurance Payor is coded as 'Medicare', Procedure Performed is coded as 'Initial Implant', 'Generator Change' or 'Generator Explant' and ICD Indication is coded as 'Primary Prevention'.", "Display Order": 2}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 1071, "Name": "Registry Schema Version", "Section Display Name": "Z. Administration", "Coding Instructions": "Schema version describes the version number of the Registry Transmission Document (RTD) schema to which each record conforms. It is an attribute that includes a constant value indicating the version of schema file. This is entered into the schema automatically by software.", "Target Value": "N/A", "Short Name": "SchemaVersion", "Data Type": "NUM", "Precision": "3,1", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "1", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": null, "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code": "1000142438", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}]}]}}