{"Dictionary": {"Dataset": {"Code": "procedure", "Id": 162}, "Version": "1.0", "Type": "<PERSON><PERSON><PERSON>", "Sections": [{"Container Class": "defaultContainer", "Parent Section": "Root", "Section Display Name": "<PERSON><PERSON><PERSON>", "Section Code": "DEFAULT", "Section Type": "Section", "Cardinality": "1..1", "Table": "RAW", "Elements": [{"Short Name": "ServiceSiteName", "DB Data Type": "<PERSON><PERSON><PERSON>(100)", "Role": "HOSPITAL", "PHI": 0, "Element Reference": 2076411968, "Definition": null, "Missing Data": "No Action", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "ServiceSiteName", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"rwj": [], "wellspan": [], "trinity": [], "ucsf": [], "montefiore": [], "ucla": [], "umms": [], "bellin": [], "uci": [], "sutter": [], "ucd": [], "commonspirit": [], "ech": [], "ucsd": [], "sluh": [], "uva": [], "bjc": []}}, {"Short Name": "EncounterNumber", "DB Data Type": "<PERSON><PERSON><PERSON>(100)", "Role": "IDENTIFIER", "PHI": 1, "Element Reference": **********, "Definition": null, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "EncounterNumber", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"rwj": ["patient encounter - encounter record number"], "wellspan": ["encounternumber"], "trinity": ["patient account number"], "ucsf": ["patient_account"], "montefiore": ["patient encounter - encounter record number"], "ucla": ["encounter number"], "umms": ["encounternumber"], "bellin": ["encounternumber"], "uci": ["enc___patient_account"], "sutter": ["hsp_account_id"], "ucd": ["patientaccount"], "commonspirit": ["encounternumber"], "ech": ["encounternumber"], "ucsd": ["encounternumber"], "sluh": ["encounternumber"], "uva": ["hsp_account_id"], "bjc": ["ecd number (pe ecd extension)"]}}, {"Short Name": "procedureseqno", "DB Data Type": "<PERSON><PERSON><PERSON>(100)", "Role": null, "PHI": 0, "Element Reference": **********, "Definition": null, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "procedureseqno", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"rwj": ["icd10 px sequence number - sequence number"], "wellspan": ["procedureseqno"], "trinity": ["procedure coded sequence", "proc seq"], "ucsf": ["icd10_sequence_no"], "montefiore": ["all icd10 procedure sequence number - sequence number"], "ucla": ["secondary procedure sequence"], "umms": ["procedureseqno"], "bellin": ["procedureseqno"], "uci": ["px___sequence_number"], "sutter": ["px_code_line"], "ucd": ["icd10pxsequence"], "commonspirit": ["procedureseqno"], "ech": ["proceduresequencenumber"], "ucsd": ["icd10 px - sequence number"], "sluh": ["procedureseqno"], "uva": ["procedureseqno"], "bjc": ["sec proc coded sequence"]}}, {"Short Name": "procedurecode", "DB Data Type": "<PERSON><PERSON><PERSON>(255)", "Role": null, "PHI": 0, "Element Reference": 524868627, "Definition": null, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "procedurecode", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"rwj": ["icd10 px - icd10 px"], "wellspan": ["procedurecode"], "trinity": ["cpt4/hcpcs procedure code", "procedure code"], "ucsf": ["icd10_procedure_code"], "montefiore": ["all icd10 procedure - icd10 procedure"], "ucla": ["secondary procedure (icd-10) code"], "umms": ["procedurecode"], "bellin": ["procedurecode"], "uci": ["px___procedure_code"], "sutter": ["procedure_code_px_code"], "ucd": ["icd10pxcode"], "commonspirit": ["procedurecode"], "ech": ["procedurecode"], "ucsd": ["icd10 px - procedure code"], "sluh": ["procedurecode"], "uva": ["procedurecode"], "bjc": ["sec proc code"]}}, {"Short Name": "proceduredate", "DB Data Type": "<PERSON><PERSON><PERSON>(100)", "Role": "DATE", "PHI": 1, "Element Reference": 3914345237, "Definition": null, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": null, "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "proceduredate", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Source Fields": {"rwj": ["icd10 px service date - date"], "wellspan": ["proceduredate"], "trinity": ["procedure date", "proc date"], "ucsf": ["icd10_procedure_date"], "montefiore": [], "ucla": ["secondary procedure performed date"], "umms": ["proceduredate"], "bellin": ["proceduredate"], "uci": ["px___procedure_performed_date"], "sutter": [], "ucd": ["procedureperformeddate"], "commonspirit": ["proceduredate"], "ech": ["proceduredate"], "ucsd": ["icd10 px - procedure performed date"], "sluh": ["proceduredate"], "uva": ["proceduredate"], "bjc": ["sec proc date"]}}]}]}}