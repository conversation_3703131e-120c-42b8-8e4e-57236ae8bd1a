import os
import json


def dir_path():
    return os.path.dirname(os.path.abspath(__file__))


def read_data_dict(filename):
    file_path = os.path.join(dir_path(), filename)
    with open(file_path, 'r') as f:
        return json.load(f)


def write_data_dict(filename, data):
    file_path = os.path.join(dir_path(), filename)
    with open(file_path, 'w') as f:
        json.dump(data, f, indent=2)
