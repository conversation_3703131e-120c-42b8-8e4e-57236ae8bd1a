{"Dictionary": {"Dataset": {"Code": "cost", "Id": 157}, "Version": "1.0", "Type": "Biome", "Sections": [{"Container Class": "defaultContainer", "Parent Section": "Root", "Section Display Name": "<PERSON><PERSON><PERSON>", "Section Code": "DEFAULT", "Section Type": "Section", "Cardinality": "1..1", "Table": "RAW", "Elements": [{"Element Reference": 3373283960, "Short Name": "directcost", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": false, "Definition": {"Title": "Direct Cost", "Definition": "Direct Cost", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "directcost", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 895422438, "Short Name": "charges", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": false, "Definition": {"Title": "Charge", "Definition": "Charges per unit", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "charges", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 2918093021, "Short Name": "departmentcode", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": false, "Definition": {"Title": "Department Code", "Definition": "Service / department number (Optional)", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "departmentcode", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 3623208217, "Short Name": "svcitemdate", "DB Data Type": "<PERSON><PERSON><PERSON>(40)", "Role": "DATE", "PHI": true, "Definition": {"Title": "Service Date", "Definition": "Service date in ISO-8601 or ANSI INCITS 30-1997 (R2008) format (e.g. 2017-12-25)", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "svcitemdate", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 1754829493, "Short Name": "dayofstay", "DB Data Type": "<PERSON><PERSON><PERSON>(20)", "Role": null, "PHI": false, "Definition": {"Title": "Day of Stay", "Definition": "Number of days into a stay that the service occurred (e.g. 6)", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "dayofstay", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 225185267, "Short Name": "indirectcost", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": false, "Definition": {"Title": "Indirect Cost", "Definition": "Indirect Cost", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "indirectcost", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 1901469639, "Short Name": "ub92revname", "DB Data Type": "<PERSON><PERSON><PERSON>(50)", "Role": null, "PHI": false, "Definition": {"Title": "UB Revenue Name", "Definition": "UB92 / UB04 revenue name of the item", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "ub92revname", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 1076784334, "Short Name": "hospname", "DB Data Type": "<PERSON><PERSON><PERSON>(18)", "Role": null, "PHI": false, "Definition": {"Title": "Hospital Name", "Definition": "Hospital Name", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "hospname", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 331896894, "Short Name": "cpt4hcpcsname", "DB Data Type": "<PERSON><PERSON><PERSON>(150)", "Role": null, "PHI": false, "Definition": {"Title": "CPT4 or HCPCS description", "Definition": "CPT4 or HCPCS description", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "cpt4hcpcsname", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 743086825, "Short Name": "datasetname", "DB Data Type": "<PERSON><PERSON><PERSON>(18)", "Role": null, "PHI": false, "Definition": {"Title": "Dataset Name", "Definition": "Dataset Name", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "datasetname", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 2107800384, "Short Name": "chargecodename", "DB Data Type": "<PERSON><PERSON><PERSON>(150)", "Role": null, "PHI": false, "Definition": {"Title": "Service or supply item name", "Definition": "Service or supply item name", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "chargecodename", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 1435249931, "Short Name": "departmentname", "DB Data Type": "<PERSON><PERSON><PERSON>(80)", "Role": null, "PHI": false, "Definition": {"Title": "Department Name", "Definition": "Service / department description", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "departmentname", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 3584077987, "Short Name": "cpt4hcpcscode", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": false, "Definition": {"Title": "CPT4 or HCPCS code", "Definition": "Applicable CPT4 or HCPCS code of the item", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "cpt4hcpcscode", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 1213110851, "Short Name": "filename", "DB Data Type": "<PERSON><PERSON><PERSON>(81)", "Role": null, "PHI": false, "Definition": {"Title": "File Name", "Definition": "File Name", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "filename", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 4090799199, "Short Name": "clientfileid", "DB Data Type": "bigint", "Role": null, "PHI": false, "Definition": {"Title": "Client File ID", "Definition": "Client File ID", "Source": "Biome", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "clientfileid", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 90589963, "Short Name": "units", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": false, "Definition": {"Title": "Units", "Definition": "Units utilized, amount, quantity", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "units", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 3699837234, "Short Name": "encounternumber", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": "IDENTIFIER", "PHI": true, "Definition": {"Title": "Encounter Number", "Definition": "Encounter / visit / discharge number ", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "encounternumber", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 2441707984, "Short Name": "svcitemchargecode", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": false, "Definition": {"Title": "Service or supply item code", "Definition": "Service or supply item charged", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "svcitemchargecode", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 2670785740, "Short Name": "version", "DB Data Type": "<PERSON><PERSON><PERSON>(22)", "Role": null, "PHI": false, "Definition": {"Title": "File Version", "Definition": "File Version", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "version", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 4035169011, "Short Name": "tenantid", "DB Data Type": "<PERSON><PERSON><PERSON>(34)", "Role": "TENANT", "PHI": false, "Definition": {"Title": "Tenant ID", "Definition": "Tenant ID", "Source": "Biome", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "tenantid", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 376400603, "Short Name": "nationaldrugclasscode", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": false, "Definition": {"Title": "National Drug Class Code", "Definition": "National drug code (for pharmacy items only)", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "nationaldrugclasscode", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 3642437376, "Short Name": "ub92revcode", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": null, "PHI": false, "Definition": {"Title": "UB Revenue Code", "Definition": "UB92 / UB04 revenue code of the item", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "ub92revcode", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}]}]}}