{"Dictionary": {"Dataset": {"Code": "procedure", "Id": 162}, "Version": "1.0", "Type": "Biome", "Sections": [{"Container Class": "defaultContainer", "Parent Section": "Root", "Section Display Name": "<PERSON><PERSON><PERSON>", "Section Code": "DEFAULT", "Section Type": "Section", "Cardinality": "1..1", "Table": "RAW", "Elements": [{"Element Reference": 2692888758, "Short Name": "procedurename", "DB Data Type": "<PERSON><PERSON><PERSON>(100)", "Role": null, "PHI": false, "Definition": {"Title": "Procedure Name", "Definition": "Procedure Name", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "procedurename", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 1213110851, "Short Name": "filename", "DB Data Type": "<PERSON><PERSON><PERSON>(75)", "Role": null, "PHI": false, "Definition": {"Title": "File Name", "Definition": "File Name", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "filename", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 1374659514, "Short Name": "procedureseqno", "DB Data Type": "<PERSON><PERSON><PERSON>(20)", "Role": null, "PHI": false, "Definition": {"Title": "Procedure sequence number", "Definition": "Procedure sequence number", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "procedureseqno", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 4035169011, "Short Name": "tenantid", "DB Data Type": "<PERSON><PERSON><PERSON>(34)", "Role": "TENANT", "PHI": false, "Definition": {"Title": "Tenant ID", "Definition": "Tenant ID", "Source": "Biome", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "tenantid", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 2670785740, "Short Name": "version", "DB Data Type": "<PERSON><PERSON><PERSON>(22)", "Role": null, "PHI": false, "Definition": {"Title": "File Version", "Definition": "File Version", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "version", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 4090799199, "Short Name": "clientfileid", "DB Data Type": "int", "Role": null, "PHI": false, "Definition": {"Title": "Client File ID", "Definition": "Client File ID", "Source": "Biome", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "clientfileid", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 3699837234, "Short Name": "encounternumber", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": "IDENTIFIER", "PHI": true, "Definition": {"Title": "Encounter Number", "Definition": "Encounter / visit / discharge number ", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "encounternumber", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 1076784334, "Short Name": "hospname", "DB Data Type": "<PERSON><PERSON><PERSON>(18)", "Role": null, "PHI": false, "Definition": {"Title": "Hospital Name", "Definition": "Hospital Name", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "hospname", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 3914345237, "Short Name": "proceduredate", "DB Data Type": "<PERSON><PERSON><PERSON>(30)", "Role": "DATE", "PHI": true, "Definition": {"Title": "Procedure Date", "Definition": "Procedure date in ISO-8601 or ANSI INCITS 30-1997 (R2008) format", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "proceduredate", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 743086825, "Short Name": "datasetname", "DB Data Type": "<PERSON><PERSON><PERSON>(18)", "Role": null, "PHI": false, "Definition": {"Title": "Dataset Name", "Definition": "Dataset Name", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "datasetname", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}, {"Element Reference": 524868627, "Short Name": "procedurecode", "DB Data Type": "<PERSON><PERSON><PERSON>(10)", "Role": null, "PHI": false, "Definition": {"Title": "Procedure Code", "Definition": "Secondary procedure code (ICD-9/10 code) ", "Source": "Admin", "Display Order": 1}, "Missing Data": "Illegal", "Selection Type": "Single", "Selections": [{"Name": "", "Selection Name": "", "Selection Definition": "", "Display Order": 1}], "Precision": null, "Parent Child Validations": null, "Ranges": null, "Name": "procedurecode", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No"}]}]}}