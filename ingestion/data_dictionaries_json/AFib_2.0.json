{"Dictionary": {"Dataset": {"Code": "AFib", "Id": 117}, "Version": "2.0", "Type": "<PERSON><PERSON><PERSON>", "Sections": [{"Container Class": "patientContainer", "Parent Section": "Root", "Section Display Name": "Demographics", "Section Code": "DEMOGRAPHICS", "Section Type": "Section", "Cardinality": "1..1", "Table": "DEMOGRAPHICS", "Elements": [{"Element Reference": 2000, "Name": "Last Name", "Section Display Name": "Demographics", "Coding Instructions": "Indicate the patient's last name. Hyphenated names should be recorded with a hyphen.", "Target Value": "The value on arrival at this facility", "Short Name": "LastName", "Data Type": "LN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2010, "Name": "First Name", "Section Display Name": "Demographics", "Coding Instructions": "Indicate the patient's first name.", "Target Value": "The value on arrival at this facility", "Short Name": "FirstName", "Data Type": "FN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2020, "Name": "Middle Name", "Section Display Name": "Demographics", "Coding Instructions": "Indicate the patient's middle name.\n\nNote(s):\nIt is acceptable to specify the middle initial.\n\nIf there is no middle name given, leave field blank.\n\nIf there are multiple middle names, enter all of the middle names sequentially.\n\nIf the name exceeds 50 characters, enter the first 50 letters only.", "Target Value": "The value on arrival at this facility", "Short Name": "MidName", "Data Type": "MN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2050, "Name": "Birth Date", "Section Display Name": "Demographics", "Coding Instructions": "Indicate the patient's date of birth.", "Target Value": "The value on arrival at this facility", "Short Name": "DOB", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2030, "Name": "SSN", "Section Display Name": "Demographics", "Coding Instructions": "Indicate the patient's United States Social Security Number (SSN).\n\nNote(s):\nIf the patient does not have a US Social Security Number (SSN), leave blank and check 'SSN NA'.", "Target Value": "The value on arrival at this facility", "Short Name": "SSN", "Data Type": "ST", "Precision": "9", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code": "2.16.840.1.113883.4.1", "Code System": "2.16.840.1.113883.4.1", "Code System Name": "United States Social Security Number (SSN)", "Vendor Instruction": "SSN (2030) must be 9 numeric characters long", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2031, "Name": "SSN N/A", "Section Display Name": "Demographics", "Coding Instructions": "Indicate if the patient does not have a United States Social Security Number (SSN).", "Target Value": "The value on arrival at this facility", "Short Name": "SSNNA", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code": "2.16.840.1.113883.4.1", "Code System": "2.16.840.1.113883.4.1", "Code System Name": "United States Social Security Number (SSN)", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2040, "Name": "Patient ID", "Section Display Name": "Demographics", "Coding Instructions": "Indicate the number created and automatically inserted by the software that uniquely identifies this patient.\n\nNote(s):\nOnce assigned to a patient at the participating facility, this number will never be changed or reassigned to a different patient. If the patient returns to the same participating facility or for follow up, they will receive this same unique patient identifier.", "Target Value": "The value on arrival at this facility", "Short Name": "NCDRPatientID", "Data Type": "NUM", "Precision": "9", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": null, "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code": "2.16.840.1.113883.3.3478.4.842", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": null, "Usual Range Min": null, "Usual Range Max": null, "Valid Range Min": "1", "Valid Range Max": "999,999,999"}], "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2045, "Name": "Other ID", "Section Display Name": "Demographics", "Coding Instructions": "Indicate an optional patient identifier, such as medical record number, that can be associated with the patient.", "Target Value": "N/A", "Short Name": "OtherID", "Data Type": "ST", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code": "2.16.840.1.113883.3.3478.4.843", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2060, "Name": "Sex", "Section Display Name": "Demographics", "Coding Instructions": "Indicate the patient's sex at birth.", "Target Value": "The value on arrival at this facility", "Short Name": "Sex", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Sex", "Code": "M", "Code System": "2.16.840.1.113883.5.1", "Code System Name": "HL7 Administrative Gender", "Selection Name": "Male", "Selection Definition": null, "Display Order": 1}, {"Name": "Sex", "Code": "F", "Code System": "2.16.840.1.113883.5.1", "Code System Name": "HL7 Administrative Gender", "Selection Name": "Female", "Selection Definition": null, "Display Order": 2}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2065, "Name": "Patient Zip Code", "Section Display Name": "Demographics", "Coding Instructions": "Indicate the patient's United States Postal Service zip code of their primary residence.\n\nNote(s):\nIf the patient does not have a U.S. residence, or is homeless, leave blank and check 'Zip Code NA'.", "Target Value": "The value on arrival at this facility", "Short Name": "ZipCode", "Data Type": "ST", "Precision": "5", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Patient Zip Code (2065) must be 5 numeric characters long", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2066, "Name": "Zip Code N/A", "Section Display Name": "Demographics", "Coding Instructions": "Indicate if the patient does not have a United States Postal Service zip code.\n\nNote(s):\nThis includes patients who do not have a U.S. residence or are homeless.", "Target Value": "The value on arrival at this facility", "Short Name": "ZipCodeNA", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2070, "Name": "Race - White", "Section Display Name": "Demographics", "Coding Instructions": "Indicate if the patient is White as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "<PERSON><PERSON><PERSON><PERSON>", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2106-3", "Code System": "2.16.840.1.113883.5.104", "Code System Name": "HL7 Race", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "<PERSON> (race)", "Definition": "Having origins in any of the original peoples of Europe, the Middle East, or North Africa.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": null}, {"Element Reference": 2071, "Name": "Race - Black/African American", "Section Display Name": "Demographics", "Coding Instructions": "Indicate if the patient is Black or African American as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "RaceBlack", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2054-5", "Code System": "2.16.840.1.113883.5.104", "Code System Name": "HL7 Race", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Black/African American (race)", "Definition": "Having origins in any of the black racial groups of Africa. Terms such as \"Haitian\" or \"Negro\" can be used in addition to \"Black or African American.\"", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": null}, {"Element Reference": 2073, "Name": "Race - American Indian/Alaskan Native", "Section Display Name": "Demographics", "Coding Instructions": "Indicate if the patient is American Indian or Alaskan Native as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "RaceAmIndian", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "1002-5", "Code System": "2.16.840.1.113883.5.104", "Code System Name": "HL7 Race", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "American Indian or Alaskan Native (race)", "Definition": "Having origins in any of the original peoples of North and South America (including Central America), and who maintains tribal affiliation or community attachment.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": null}, {"Element Reference": 2072, "Name": "Race - Asian", "Section Display Name": "Demographics", "Coding Instructions": "Indicate if the patient is Asian as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "Race<PERSON>ian", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2028-9", "Code System": "2.16.840.1.113883.5.104", "Code System Name": "HL7 Race", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Asian (race)", "Definition": "Having origins in any of the original peoples of the Far East, Southeast Asia, or the Indian subcontinent including, for example, Cambodia, China, India, Japan, Korea, Malaysia, Pakistan, the Philippine Islands, Thailand, and Vietnam.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": null}, {"Element Reference": 2074, "Name": "Race - Native Hawaiian/Pacific Islander", "Section Display Name": "Demographics", "Coding Instructions": "Indicate if the patient is Native Hawaiian or Pacific Islander as determined by the patient/family.\n\nNote(s):\nIf the patient has multiple race origins, specify them using the other race selections in addition to this one.", "Target Value": "The value on arrival at this facility", "Short Name": "RaceNatHaw", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2076-8", "Code System": "2.16.840.1.113883.5.104", "Code System Name": "HL7 Race", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Race - Native Hawaiian/Pacific Islander - Native Hawaiian", "Definition": "Having origins in any of the original peoples of Hawaii, Guam, Samoa, or other Pacific Islands.", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": null}, {"Element Reference": 2076, "Name": "Hispanic or Latino Ethnicity", "Section Display Name": "Demographics", "Coding Instructions": "Indicate if the patient is of Hispanic or Latino ethnicity as determined by the patient/family.", "Target Value": "The value on arrival at this facility", "Short Name": "<PERSON><PERSON><PERSON><PERSON>", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2135-2", "Code System": "2.16.840.1.113883.5.50", "Code System Name": "HL7 Ethnicity", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Hispanic or Latino Ethnicity", "Definition": "A person of Mexican, Puerto Rican, Cuban, South or Central American, or other Spanish culture or origin, regardless of race. The term, \"Spanish origin,\" can be used in addition to \"Hispanic or Latino.\" ", "Source": "U.S. Office of Management and Budget. Classification of Federal Data on Race and Ethnicity"}, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "Episode of Care", "Section Code": "EOC", "Section Type": "Section", "Cardinality": "1..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 2999, "Name": "Episode Unique Key", "Section Display Name": "Episode of Care", "Coding Instructions": "Indicate the unique key associated with each patient episode record as assigned by the EMR/EHR or your software application.", "Target Value": "N/A", "Short Name": "<PERSON><PERSON><PERSON>", "Data Type": "ST", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": null, "Data Source": "Automatic", "Is Identifier": "Yes", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.855", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 3001, "Name": "Arrival Date and Time", "Section Display Name": "Episode of Care", "Coding Instructions": "Indicate the date and time the patient arrived at this facility for this visit.\n\nIf the arrival date and time are not specified, code the earliest date and time found in the medical record indicating the patient was at this facility.", "Target Value": "N/A", "Short Name": "ArrivalDateTime", "Data Type": "TS", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Patient must be at least 18 years old at the time of Arrival Date and Time (3001)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 3005, "Name": "Health Insurance", "Section Display Name": "Episode of Care", "Coding Instructions": "Indicate if the patient has health insurance.", "Target Value": "The value on arrival at this facility", "Short Name": "HealthIns", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "63513-6", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 3010, "Name": "Health Insurance Payment Source", "Section Display Name": "Episode of Care", "Coding Instructions": "Indicate the patient's health insurance payment type.\n\nNote(s):\nIf the patient has multiple insurance payors, select all payors.", "Target Value": "The value on arrival at this facility", "Short Name": "HIPS", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "*********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Health Insurance Payment Source", "Code": "5", "Code System": "2.16.840.1.113883.3.221.5", "Code System Name": "PHDSC", "Selection Name": "Private health insurance", "Selection Definition": "Private health insurance is coverage by a health plan provided through an employer or union or purchased by an individual from a private health insurance company. A health maintenance organization (HMO) is considered private health insurance.", "Display Order": 1}, {"Name": "Health Insurance Payment Source", "Code": "36", "Code System": "2.16.840.1.113883.3.221.5", "Code System Name": "PHDSC", "Selection Name": "State-specific plan (non-Medicaid)", "Selection Definition": "State Specific Plans - Some states have their own health insurance programs for low-income uninsured individuals. These health plans may be known by different names in different states.", "Display Order": 2}, {"Name": "Health Insurance Payment Source", "Code": "1", "Code System": "2.16.840.1.113883.3.221.5", "Code System Name": "PHDSC", "Selection Name": "Medicare (Part A or B)", "Selection Definition": "Medicare is a health insurance program for: people age 65 or older; people under age 65 with certain disabilities; and people of all ages with end-stage renal disease (permanent kidney failure requiring dialysis or a kidney transplant).\n\nMedicare Part A (Hospital Insurance) –\nPart A helps cover inpatient care in hospitals, including critical access hospitals, and skilled nursing facilities (not custodial or long-term care). It also helps cover hospice care and some home health care.\n\nMedicare Part B (Medical Insurance) –\nPart B helps cover doctors' services and outpatient care. It also covers some other medical services that Part A doesn't cover, such as some of the services of physical and occupational therapists, and some home health care. Part B helps pay for these covered services and supplies when they are medically necessary.", "Display Order": 3}, {"Name": "Health Insurance Payment Source", "Code": "112000002025", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Medicare Advantage (Part C)", "Selection Definition": "Medicare Part C (Medicare Advantage) –\nPart C is an alternative way to get Medicare coverage through private insurance companies instead of the federal government. Part C provides the same benefits as Medicare Part A and Part B, and may include additional benefits such as dental, vision, prescription drug and wellness programs coverage.", "Display Order": 4}, {"Name": "Health Insurance Payment Source", "Code": "2", "Code System": "2.16.840.1.113883.3.221.5", "Code System Name": "PHDSC", "Selection Name": "Medicaid", "Selection Definition": "Medicaid is a program administered at the state level, which provides medical assistance to the needy. Families with dependent children, the aged, blind, and disabled who are in financial need are eligible for Medicaid. It may be known by different names.", "Display Order": 5}, {"Name": "Health Insurance Payment Source", "Code": "31", "Code System": "2.16.840.1.113883.3.221.5", "Code System Name": "PHDSC", "Selection Name": "Military health care", "Selection Definition": "Military Health care - Military health care includes TRICARE/CHAMPUS (Civilian Health and Medical Program of the Uniformed Services) and CHAMPVA (Civilian Health and Medical Program of the Department of Veterans Affairs), as well as care provided by the Department of Veterans Affairs (VA).", "Display Order": 6}, {"Name": "Health Insurance Payment Source", "Code": "33", "Code System": "2.16.840.1.113883.3.221.5", "Code System Name": "PHDSC", "Selection Name": "Indian Health Service", "Selection Definition": "Indian Health Service (IHS) is a health care program through which the Department of Health and Human Services provides medical assistance to eligible American Indians at IHS facilities. In addition, the IHS helps pay the cost of selected health care services provided at non-IHS facilities.", "Display Order": 7}, {"Name": "Health Insurance Payment Source", "Code": "100000812", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Non-US insurance", "Selection Definition": "Non-US insurance refers to individuals with a payor that does not originate in the United States.", "Display Order": 8}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 3005, "Parent Element Name": "Health Insurance", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 12846, "Name": "Medicare Beneficiary Identifier", "Section Display Name": "Episode of Care", "Coding Instructions": "Indicate the patient's Medicare Beneficiary Identifier (MBI).\n\nNote(s):\nEnter the Medicare Beneficiary Identifier (MBI) for those patients insured by Medicare. Patients without Medicare will not have a MBI.", "Target Value": "The value on arrival at this facility", "Short Name": "MBI", "Data Type": "ST", "Precision": "11", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.4.927", "Code System": "2.16.840.1.113883.4.927", "Code System Name": "Centers for Medicare & Medicaid Services", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Medicare Beneficiary Identifier", "Definition": "The Medicare Access and CHIP Reauthorization Act (MACRA) of 2015, requires us to remove Social Security Numbers (SSNs) from all Medicare cards by April 2019. A new Medicare Beneficiary Identifier (MBI) will replace the SSN-based Health Insurance Claim Number (HICN) on the new Medicare cards for Medicare transactions like billing, eligibility status, and claim status.", "Source": "https://www.cms.gov/Medicare/New-Medicare-Card/index.html\n\n"}, "Parent Child Validations": [{"Parent Element Reference": 3010, "Parent Element Name": "Health Insurance Payment Source", "Parent Element Selection Name": "Medicare (Part A or B)"}, {"Parent Element Reference": 3010, "Parent Element Name": "Health Insurance Payment Source", "Parent Element Selection Name": "Medicare Advantage (Part C)"}]}, {"Element Reference": 3020, "Name": "<PERSON><PERSON> Enrolled in Research Study", "Section Display Name": "Episode of Care", "Coding Instructions": "Indicate if the patient is enrolled in an ongoing ACC-NCDR sponsored or associated research study relating to this registry.", "Target Value": "Any occurrence between arrival at this facility and discharge", "Short Name": "EnrolledStudy", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001095", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "<PERSON><PERSON> Enrolled in Research Study", "Definition": "A clinical or research study is one in which participants are assigned to receive one or more interventions (or no intervention) so that researchers can evaluate the effects of the interventions on biomedical or health-related outcomes. The assignments are determined by the study protocol. Participants may receive diagnostic, therapeutic, or other types of interventions.", "Source": "Clinicaltrials.gov Glossary of Common Site Terms retrieved from http://clinicaltrials.gov/ct2/about-studies/glossary#interventional-study"}, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Episode of Care", "Section Display Name": "Research Study", "Section Code": "RESEARCHSTUDY", "Section Type": "Repeater Section", "Cardinality": "0..n", "Table": "RESEARCHSTUDY", "Elements": [{"Element Reference": 3025, "Name": "Research Study Name", "Section Display Name": "Research Study", "Coding Instructions": "Indicate the research study name as provided by the research study protocol.\n\nNote(s):\nIf the patient is in more than one research study, list each separately.", "Target Value": "N/A", "Short Name": "StudyName", "Data Type": "ST", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001096", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Research Study Name (3025) must be a valid study name for the Registry.", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 3020, "Parent Element Name": "<PERSON><PERSON> Enrolled in Research Study", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 3030, "Name": "Research Study Patient ID", "Section Display Name": "Research Study", "Coding Instructions": "Indicate the research study patient identification number as assigned by the research protocol.\n\nNote(s):\nIf the patient is in more than one research study, list each separately.", "Target Value": "N/A", "Short Name": "StudyPtID", "Data Type": "ST", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.4.852", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 3020, "Parent Element Name": "<PERSON><PERSON> Enrolled in Research Study", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "Atrial Fibrillation Effect on Quality of Life", "Section Code": "AFEQT", "Section Type": "Section", "Cardinality": "0..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 4700, "Name": "AFEQT Patient Questionnaire Performed", "Section Display Name": "Atrial Fibrillation Effect on Quality of Life", "Coding Instructions": "Indicate if the baseline Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire was performed.", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "AFEQTBase", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001145", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 4705, "Name": "Are you currently in atrial fibrillation?", "Section Display Name": "Atrial Fibrillation Effect on Quality of Life", "Coding Instructions": "Indicate the patient's response to the Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire  Section 1 - Question 1 \"Are you currently in atrial fibrillation?\"", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "AFEQTS1Q1", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001146", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Section 1, Q1", "Definition": "Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire", "Source": "Development and Validation of the Atrial Fibrillation Effect on QualiTy-of-Life (AFEQT) Questionnaire in Patients With Atrial Fibrillation. Spertus J, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Circ Arrhythm Electrophysiol. 2011;4:15-25."}, "Parent Child Validations": [{"Parent Element Reference": 4700, "Parent Element Name": "AFEQT Patient Questionnaire Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4710, "Name": "When was the last time you were aware of having had an episode of atrial fibrillation?", "Section Display Name": "Atrial Fibrillation Effect on Quality of Life", "Coding Instructions": "Indicate the patient's response to the Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire  Section 1 - Question 2 \"When was the last time your were aware of having had an episode of atrial fibrillation?\"", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "AFEQTS1Q2", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001147", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "When was the last time you were aware of having had an episode of atrial fibrillation?", "Code": "100001148", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Earlier today", "Selection Definition": null, "Display Order": 1}, {"Name": "When was the last time you were aware of having had an episode of atrial fibrillation?", "Code": "100001149", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Within the past week", "Selection Definition": null, "Display Order": 2}, {"Name": "When was the last time you were aware of having had an episode of atrial fibrillation?", "Code": "100001150", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Within the past month", "Selection Definition": null, "Display Order": 3}, {"Name": "When was the last time you were aware of having had an episode of atrial fibrillation?", "Code": "100001151", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "1 month to 1 year ago", "Selection Definition": null, "Display Order": 4}, {"Name": "When was the last time you were aware of having had an episode of atrial fibrillation?", "Code": "100001152", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "More than 1 year ago", "Selection Definition": null, "Display Order": 5}, {"Name": "When was the last time you were aware of having had an episode of atrial fibrillation?", "Code": "100001153", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "I was never aware of having atrial fibrillation", "Selection Definition": null, "Display Order": 6}], "Ranges": null, "Definition": {"Title": "Section 1, Q2", "Definition": " Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire", "Source": "Development and Validation of the Atrial Fibrillation Effect on QualiTy-of-Life (AFEQT) Questionnaire in Patients With Atrial Fibrillation. Spertus J, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Circ Arrhythm Electrophysiol. 2011;4:15-25."}, "Parent Child Validations": [{"Parent Element Reference": 4705, "Parent Element Name": "Are you currently in atrial fibrillation?", "Parent Element Selection Name": "No"}]}, {"Element Reference": 4715, "Name": "Q1: Palpitations: Heart fluttering, skipping or racing", "Section Display Name": "Atrial Fibrillation Effect on Quality of Life", "Coding Instructions": "Indicate the patient's response to the Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire Section 2 - Question 1  \"Over the past four weeks, as a result of your atrial fibrillation, how much were you bothered by Palpitations: Heart fluttering, skipping or racing\"?", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "AFEQTS2Q1", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001154", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Q1: Palpitations: Heart fluttering, skipping or racing", "Code": "100001158", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not at all bothered or I did not have this symptom", "Selection Definition": null, "Display Order": 1}, {"Name": "Q1: Palpitations: Heart fluttering, skipping or racing", "Code": "100001159", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Hardly bothered ", "Selection Definition": null, "Display Order": 2}, {"Name": "Q1: Palpitations: Heart fluttering, skipping or racing", "Code": "100001160", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "A little bothered", "Selection Definition": null, "Display Order": 3}, {"Name": "Q1: Palpitations: Heart fluttering, skipping or racing", "Code": "100001161", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Moderately bothered", "Selection Definition": null, "Display Order": 4}, {"Name": "Q1: Palpitations: Heart fluttering, skipping or racing", "Code": "100001162", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Quite a bit bothered", "Selection Definition": null, "Display Order": 5}, {"Name": "Q1: Palpitations: Heart fluttering, skipping or racing", "Code": "100001163", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Very bothered", "Selection Definition": null, "Display Order": 6}, {"Name": "Q1: Palpitations: Heart fluttering, skipping or racing", "Code": "100001164", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extremely bothered", "Selection Definition": null, "Display Order": 7}], "Ranges": null, "Definition": {"Title": "Section 2, Q1", "Definition": " Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire", "Source": "Development and Validation of the Atrial Fibrillation Effect on QualiTy-of-Life (AFEQT) Questionnaire in Patients With Atrial Fibrillation. Spertus J, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Circ Arrhythm Electrophysiol. 2011;4:15-25."}, "Parent Child Validations": [{"Parent Element Reference": 4700, "Parent Element Name": "AFEQT Patient Questionnaire Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4720, "Name": "Q2: Irregular heart beat", "Section Display Name": "Atrial Fibrillation Effect on Quality of Life", "Coding Instructions": "Indicate the patient's response to the Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire Section 2 -Question 2.\"Over the past four weeks, as a result of your atrial fibrillation, how much were you bothered by irregular heart beat\"?", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "AFEQTS2Q2", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001155", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Q2: Irregular heart beat", "Code": "100001158", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not at all bothered or I did not have this symptom", "Selection Definition": null, "Display Order": 1}, {"Name": "Q2: Irregular heart beat", "Code": "100001159", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Hardly bothered ", "Selection Definition": null, "Display Order": 2}, {"Name": "Q2: Irregular heart beat", "Code": "100001160", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "A little bothered", "Selection Definition": null, "Display Order": 3}, {"Name": "Q2: Irregular heart beat", "Code": "100001161", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Moderately bothered", "Selection Definition": null, "Display Order": 4}, {"Name": "Q2: Irregular heart beat", "Code": "100001162", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Quite a bit bothered", "Selection Definition": null, "Display Order": 5}, {"Name": "Q2: Irregular heart beat", "Code": "100001163", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Very bothered", "Selection Definition": null, "Display Order": 6}, {"Name": "Q2: Irregular heart beat", "Code": "100001164", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extremely bothered", "Selection Definition": null, "Display Order": 7}], "Ranges": null, "Definition": {"Title": "Section 2, Q2", "Definition": " Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire", "Source": "Development and Validation of the Atrial Fibrillation Effect on QualiTy-of-Life (AFEQT) Questionnaire in Patients With Atrial Fibrillation. Spertus J, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Circ Arrhythm Electrophysiol. 2011;4:15-25."}, "Parent Child Validations": [{"Parent Element Reference": 4700, "Parent Element Name": "AFEQT Patient Questionnaire Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4725, "Name": "Q3: Pause in Heart Activity", "Section Display Name": "Atrial Fibrillation Effect on Quality of Life", "Coding Instructions": "Indicate the patient's response to the Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire  Section 2 - Question 3 \"Over the past four weeks, as a result of your atrial fibrillation, how much were you bothered by a pause in heart activity?\"", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "AFEQTS2Q3", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001156", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Q3: Pause in Heart Activity", "Code": "100001158", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not at all bothered or I did not have this symptom", "Selection Definition": null, "Display Order": 1}, {"Name": "Q3: Pause in Heart Activity", "Code": "100001159", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Hardly bothered ", "Selection Definition": null, "Display Order": 2}, {"Name": "Q3: Pause in Heart Activity", "Code": "100001160", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "A little bothered", "Selection Definition": null, "Display Order": 3}, {"Name": "Q3: Pause in Heart Activity", "Code": "100001161", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Moderately bothered", "Selection Definition": null, "Display Order": 4}, {"Name": "Q3: Pause in Heart Activity", "Code": "100001162", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Quite a bit bothered", "Selection Definition": null, "Display Order": 5}, {"Name": "Q3: Pause in Heart Activity", "Code": "100001163", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Very bothered", "Selection Definition": null, "Display Order": 6}, {"Name": "Q3: Pause in Heart Activity", "Code": "100001164", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extremely bothered", "Selection Definition": null, "Display Order": 7}], "Ranges": null, "Definition": {"Title": "Section 2, Q3", "Definition": " Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire", "Source": "Development and Validation of the Atrial Fibrillation Effect on QualiTy-of-Life (AFEQT) Questionnaire in Patients With Atrial Fibrillation. Spertus J, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Circ Arrhythm Electrophysiol. 2011;4:15-25."}, "Parent Child Validations": [{"Parent Element Reference": 4700, "Parent Element Name": "AFEQT Patient Questionnaire Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4730, "Name": "Q4: Lightheadedness or dizziness", "Section Display Name": "Atrial Fibrillation Effect on Quality of Life", "Coding Instructions": "Indicate the patient's response to the Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire Section 2 - Question 4 \"Over the past four weeks, as a result of your atrial fibrillation, how much were you bothered by lightheadedness or dizziness?\"", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "AFEQTS2Q4", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001157", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Q4: Lightheadedness or dizziness", "Code": "100001158", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not at all bothered or I did not have this symptom", "Selection Definition": null, "Display Order": 1}, {"Name": "Q4: Lightheadedness or dizziness", "Code": "100001159", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Hardly bothered ", "Selection Definition": null, "Display Order": 2}, {"Name": "Q4: Lightheadedness or dizziness", "Code": "100001160", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "A little bothered", "Selection Definition": null, "Display Order": 3}, {"Name": "Q4: Lightheadedness or dizziness", "Code": "100001161", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Moderately bothered", "Selection Definition": null, "Display Order": 4}, {"Name": "Q4: Lightheadedness or dizziness", "Code": "100001162", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Quite a bit bothered", "Selection Definition": null, "Display Order": 5}, {"Name": "Q4: Lightheadedness or dizziness", "Code": "100001163", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Very bothered", "Selection Definition": null, "Display Order": 6}, {"Name": "Q4: Lightheadedness or dizziness", "Code": "100001164", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extremely bothered", "Selection Definition": null, "Display Order": 7}], "Ranges": null, "Definition": {"Title": "Section 2, Q4", "Definition": " Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire", "Source": "Development and Validation of the Atrial Fibrillation Effect on QualiTy-of-Life (AFEQT) Questionnaire in Patients With Atrial Fibrillation. Spertus J, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Circ Arrhythm Electrophysiol. 2011;4:15-25."}, "Parent Child Validations": [{"Parent Element Reference": 4700, "Parent Element Name": "AFEQT Patient Questionnaire Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4735, "Name": "Q5: Ability to have recreational pastimes, sports, and hobbies", "Section Display Name": "Atrial Fibrillation Effect on Quality of Life", "Coding Instructions": "Indicate the patient's response to the Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire Section 2 - Question 5 \"Over the past four weeks, have you been limited by your atrial fibrillation in your: ability to have recreational pastimes, sports, and hobbies?\"", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "AFEQTS2Q5", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001165", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Q5: Ability to have recreational pastimes, sports, and hobbies", "Code": "100001167", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not at all limited", "Selection Definition": null, "Display Order": 1}, {"Name": "Q5: Ability to have recreational pastimes, sports, and hobbies", "Code": "100001168", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Hardly limited", "Selection Definition": null, "Display Order": 2}, {"Name": "Q5: Ability to have recreational pastimes, sports, and hobbies", "Code": "100001169", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "A little limited", "Selection Definition": null, "Display Order": 3}, {"Name": "Q5: Ability to have recreational pastimes, sports, and hobbies", "Code": "100001170", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Moderately limited", "Selection Definition": null, "Display Order": 4}, {"Name": "Q5: Ability to have recreational pastimes, sports, and hobbies", "Code": "100001171", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Quite a bit limited", "Selection Definition": null, "Display Order": 5}, {"Name": "Q5: Ability to have recreational pastimes, sports, and hobbies", "Code": "100001172", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Very limited", "Selection Definition": null, "Display Order": 6}, {"Name": "Q5: Ability to have recreational pastimes, sports, and hobbies", "Code": "100001173", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extremely limited", "Selection Definition": null, "Display Order": 7}], "Ranges": null, "Definition": {"Title": "Section 2, Q5", "Definition": " Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire", "Source": "Development and Validation of the Atrial Fibrillation Effect on QualiTy-of-Life (AFEQT) Questionnaire in Patients With Atrial Fibrillation. Spertus J, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Circ Arrhythm Electrophysiol. 2011;4:15-25."}, "Parent Child Validations": [{"Parent Element Reference": 4700, "Parent Element Name": "AFEQT Patient Questionnaire Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4740, "Name": "Q6: Ability to have a relationship and do things with friends and family", "Section Display Name": "Atrial Fibrillation Effect on Quality of Life", "Coding Instructions": "Indicate the patient's response to the Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire Section 2 - Question 6 \"Over the past four weeks, have you been limited by your atrial fibrillation in your: ability to do things with friends and family\"?", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "AFEQTS2Q6", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001166", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Q6: Ability to have a relationship and do things with friends and family", "Code": "100001167", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not at all limited", "Selection Definition": null, "Display Order": 1}, {"Name": "Q6: Ability to have a relationship and do things with friends and family", "Code": "100001168", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Hardly limited", "Selection Definition": null, "Display Order": 2}, {"Name": "Q6: Ability to have a relationship and do things with friends and family", "Code": "100001169", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "A little limited", "Selection Definition": null, "Display Order": 3}, {"Name": "Q6: Ability to have a relationship and do things with friends and family", "Code": "100001170", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Moderately limited", "Selection Definition": null, "Display Order": 4}, {"Name": "Q6: Ability to have a relationship and do things with friends and family", "Code": "100001171", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Quite a bit limited", "Selection Definition": null, "Display Order": 5}, {"Name": "Q6: Ability to have a relationship and do things with friends and family", "Code": "100001172", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Very limited", "Selection Definition": null, "Display Order": 6}, {"Name": "Q6: Ability to have a relationship and do things with friends and family", "Code": "100001173", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extremely limited", "Selection Definition": null, "Display Order": 7}], "Ranges": null, "Definition": {"Title": "Section 2, Q6", "Definition": " Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire", "Source": "Development and Validation of the Atrial Fibrillation Effect on QualiTy-of-Life (AFEQT) Questionnaire in Patients With Atrial Fibrillation. Spertus J, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Circ Arrhythm Electrophysiol. 2011;4:15-25."}, "Parent Child Validations": [{"Parent Element Reference": 4700, "Parent Element Name": "AFEQT Patient Questionnaire Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4745, "Name": "Q7: Difficulty doing any activity because you felt tired, fatigued, or low on energy", "Section Display Name": "Atrial Fibrillation Effect on Quality of Life", "Coding Instructions": "Indicate the patient's response to the Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire Section 2 - Question 7  \"Over the past four weeks, as a result of your atrial fibrillation, how much difficulty have you had in: doing any activity because you felt tired, fatigued, or low on energy?\"", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "AFEQTS2Q7", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001174", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Q7: Difficulty doing any activity because you felt tired, fatigued, or low on energy", "Code": "100001180", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "No difficulty at all", "Selection Definition": null, "Display Order": 1}, {"Name": "Q7: Difficulty doing any activity because you felt tired, fatigued, or low on energy", "Code": "100001181", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Hardly any difficulty", "Selection Definition": null, "Display Order": 2}, {"Name": "Q7: Difficulty doing any activity because you felt tired, fatigued, or low on energy", "Code": "100001182", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "A little difficulty", "Selection Definition": null, "Display Order": 3}, {"Name": "Q7: Difficulty doing any activity because you felt tired, fatigued, or low on energy", "Code": "100001183", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Moderate difficulty", "Selection Definition": null, "Display Order": 4}, {"Name": "Q7: Difficulty doing any activity because you felt tired, fatigued, or low on energy", "Code": "100001184", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Quite a bit of difficulty", "Selection Definition": null, "Display Order": 5}, {"Name": "Q7: Difficulty doing any activity because you felt tired, fatigued, or low on energy", "Code": "100001185", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "A lot of difficulty", "Selection Definition": null, "Display Order": 6}, {"Name": "Q7: Difficulty doing any activity because you felt tired, fatigued, or low on energy", "Code": "100001186", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extreme difficulty", "Selection Definition": null, "Display Order": 7}], "Ranges": null, "Definition": {"Title": "Section 2, Q7", "Definition": " Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire", "Source": "Development and Validation of the Atrial Fibrillation Effect on QualiTy-of-Life (AFEQT) Questionnaire in Patients With Atrial Fibrillation. Spertus J, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Circ Arrhythm Electrophysiol. 2011;4:15-25."}, "Parent Child Validations": [{"Parent Element Reference": 4700, "Parent Element Name": "AFEQT Patient Questionnaire Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4750, "Name": "Q8: Di<PERSON><PERSON>ulty doing physical activity because of shortness of breath", "Section Display Name": "Atrial Fibrillation Effect on Quality of Life", "Coding Instructions": "Indicate the patient's response to the Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire Section 2 - Question 8 \"Over the past four weeks, as a result of your atrial fibrillation, how much difficulty have you had in: doing physical activity because of shortness of breath?\"", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "AFEQTS2Q8", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001175", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Q8: Di<PERSON><PERSON>ulty doing physical activity because of shortness of breath", "Code": "100001180", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "No difficulty at all", "Selection Definition": null, "Display Order": 1}, {"Name": "Q8: Di<PERSON><PERSON>ulty doing physical activity because of shortness of breath", "Code": "100001181", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Hardly any difficulty", "Selection Definition": null, "Display Order": 2}, {"Name": "Q8: Di<PERSON><PERSON>ulty doing physical activity because of shortness of breath", "Code": "100001182", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "A little difficulty", "Selection Definition": null, "Display Order": 3}, {"Name": "Q8: Di<PERSON><PERSON>ulty doing physical activity because of shortness of breath", "Code": "100001183", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Moderate difficulty", "Selection Definition": null, "Display Order": 4}, {"Name": "Q8: Di<PERSON><PERSON>ulty doing physical activity because of shortness of breath", "Code": "100001184", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Quite a bit of difficulty", "Selection Definition": null, "Display Order": 5}, {"Name": "Q8: Di<PERSON><PERSON>ulty doing physical activity because of shortness of breath", "Code": "100001185", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "A lot of difficulty", "Selection Definition": null, "Display Order": 6}, {"Name": "Q8: Di<PERSON><PERSON>ulty doing physical activity because of shortness of breath", "Code": "100001186", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extreme difficulty", "Selection Definition": null, "Display Order": 7}], "Ranges": null, "Definition": {"Title": "Section 2, Q8", "Definition": " Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire", "Source": "Development and Validation of the Atrial Fibrillation Effect on QualiTy-of-Life (AFEQT) Questionnaire in Patients With Atrial Fibrillation. Spertus J, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Circ Arrhythm Electrophysiol. 2011;4:15-25."}, "Parent Child Validations": [{"Parent Element Reference": 4700, "Parent Element Name": "AFEQT Patient Questionnaire Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4755, "Name": "Q9: <PERSON><PERSON><PERSON><PERSON><PERSON> exercising", "Section Display Name": "Atrial Fibrillation Effect on Quality of Life", "Coding Instructions": "Indicate the patient's response to the Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire Section 2 - Question 9 \"Over the past four weeks, as a result of your atrial fibrillation, how much difficulty have you had in: exercising?\"", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "AFEQTS2Q9", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001176", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Q9: <PERSON><PERSON><PERSON><PERSON><PERSON> exercising", "Code": "100001180", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "No difficulty at all", "Selection Definition": null, "Display Order": 1}, {"Name": "Q9: <PERSON><PERSON><PERSON><PERSON><PERSON> exercising", "Code": "100001181", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Hardly any difficulty", "Selection Definition": null, "Display Order": 2}, {"Name": "Q9: <PERSON><PERSON><PERSON><PERSON><PERSON> exercising", "Code": "100001182", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "A little difficulty", "Selection Definition": null, "Display Order": 3}, {"Name": "Q9: <PERSON><PERSON><PERSON><PERSON><PERSON> exercising", "Code": "100001183", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Moderate difficulty", "Selection Definition": null, "Display Order": 4}, {"Name": "Q9: <PERSON><PERSON><PERSON><PERSON><PERSON> exercising", "Code": "100001184", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Quite a bit of difficulty", "Selection Definition": null, "Display Order": 5}, {"Name": "Q9: <PERSON><PERSON><PERSON><PERSON><PERSON> exercising", "Code": "100001185", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "A lot of difficulty", "Selection Definition": null, "Display Order": 6}, {"Name": "Q9: <PERSON><PERSON><PERSON><PERSON><PERSON> exercising", "Code": "100001186", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extreme difficulty", "Selection Definition": null, "Display Order": 7}], "Ranges": null, "Definition": {"Title": "Section 2, Q9", "Definition": " Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire", "Source": "Development and Validation of the Atrial Fibrillation Effect on QualiTy-of-Life (AFEQT) Questionnaire in Patients With Atrial Fibrillation. Spertus J, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Circ Arrhythm Electrophysiol. 2011;4:15-25."}, "Parent Child Validations": [{"Parent Element Reference": 4700, "Parent Element Name": "AFEQT Patient Questionnaire Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4760, "Name": "Q10: <PERSON><PERSON><PERSON><PERSON><PERSON> walking briskly", "Section Display Name": "Atrial Fibrillation Effect on Quality of Life", "Coding Instructions": "Indicate the patient's response to the Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire Section 2 - Question 10 \"Over the past four weeks, as a result of your atrial fibrillation, how much difficulty have you had in: walking briskly?\"", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "AFEQTS2Q10", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001177", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Q10: <PERSON><PERSON><PERSON><PERSON><PERSON> walking briskly", "Code": "100001180", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "No difficulty at all", "Selection Definition": null, "Display Order": 1}, {"Name": "Q10: <PERSON><PERSON><PERSON><PERSON><PERSON> walking briskly", "Code": "100001181", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Hardly any difficulty", "Selection Definition": null, "Display Order": 2}, {"Name": "Q10: <PERSON><PERSON><PERSON><PERSON><PERSON> walking briskly", "Code": "100001182", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "A little difficulty", "Selection Definition": null, "Display Order": 3}, {"Name": "Q10: <PERSON><PERSON><PERSON><PERSON><PERSON> walking briskly", "Code": "100001183", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Moderate difficulty", "Selection Definition": null, "Display Order": 4}, {"Name": "Q10: <PERSON><PERSON><PERSON><PERSON><PERSON> walking briskly", "Code": "100001184", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Quite a bit of difficulty", "Selection Definition": null, "Display Order": 5}, {"Name": "Q10: <PERSON><PERSON><PERSON><PERSON><PERSON> walking briskly", "Code": "100001185", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "A lot of difficulty", "Selection Definition": null, "Display Order": 6}, {"Name": "Q10: <PERSON><PERSON><PERSON><PERSON><PERSON> walking briskly", "Code": "100001186", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extreme difficulty", "Selection Definition": null, "Display Order": 7}], "Ranges": null, "Definition": {"Title": "Section 2, Q10", "Definition": " Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire", "Source": "Development and Validation of the Atrial Fibrillation Effect on QualiTy-of-Life (AFEQT) Questionnaire in Patients With Atrial Fibrillation. Spertus J, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Circ Arrhythm Electrophysiol. 2011;4:15-25."}, "Parent Child Validations": [{"Parent Element Reference": 4700, "Parent Element Name": "AFEQT Patient Questionnaire Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4765, "Name": "Q11: Difficulty walking briskly uphill or carrying groceries or other items, up a flight of stairs without stopping", "Section Display Name": "Atrial Fibrillation Effect on Quality of Life", "Coding Instructions": "Indicate the patient's response to the Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire Section 2 - Question 11 \"Over the past four weeks, as a result of your atrial fibrillation, how much difficulty have you had in: walking briskly uphill or carrying groceries or other items, up a flight of stairs without stopping?\"", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "AFEQTS2Q11", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001178", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Q11: Difficulty walking briskly uphill or carrying groceries or other items, up a flight of stairs without stopping", "Code": "100001180", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "No difficulty at all", "Selection Definition": null, "Display Order": 1}, {"Name": "Q11: Difficulty walking briskly uphill or carrying groceries or other items, up a flight of stairs without stopping", "Code": "100001181", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Hardly any difficulty", "Selection Definition": null, "Display Order": 2}, {"Name": "Q11: Difficulty walking briskly uphill or carrying groceries or other items, up a flight of stairs without stopping", "Code": "100001182", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "A little difficulty", "Selection Definition": null, "Display Order": 3}, {"Name": "Q11: Difficulty walking briskly uphill or carrying groceries or other items, up a flight of stairs without stopping", "Code": "100001183", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Moderate difficulty", "Selection Definition": null, "Display Order": 4}, {"Name": "Q11: Difficulty walking briskly uphill or carrying groceries or other items, up a flight of stairs without stopping", "Code": "100001184", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Quite a bit of difficulty", "Selection Definition": null, "Display Order": 5}, {"Name": "Q11: Difficulty walking briskly uphill or carrying groceries or other items, up a flight of stairs without stopping", "Code": "100001185", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "A lot of difficulty", "Selection Definition": null, "Display Order": 6}, {"Name": "Q11: Difficulty walking briskly uphill or carrying groceries or other items, up a flight of stairs without stopping", "Code": "100001186", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extreme difficulty", "Selection Definition": null, "Display Order": 7}], "Ranges": null, "Definition": {"Title": "Section 2, Q11", "Definition": " Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire", "Source": "Development and Validation of the Atrial Fibrillation Effect on QualiTy-of-Life (AFEQT) Questionnaire in Patients With Atrial Fibrillation. Spertus J, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Circ Arrhythm Electrophysiol. 2011;4:15-25."}, "Parent Child Validations": [{"Parent Element Reference": 4700, "Parent Element Name": "AFEQT Patient Questionnaire Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4770, "Name": "Q12: Di<PERSON><PERSON>ulty doing vigorous activities such as lifting or moving heavy furniture, running, or participating in strenuous sports like tennis or racquetball", "Section Display Name": "Atrial Fibrillation Effect on Quality of Life", "Coding Instructions": "Indicate the patient's response to the Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire Section 2 - Question 12  \"Over the past four weeks, as a result of your atrial fibrillation, how much difficulty have you had in: doing vigorous activities such as lifting or moving heavy furniture, running, or participating in strenuous sports like tennis or racquetball?\"", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "AFEQTS2Q12", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001179", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Q12: Di<PERSON><PERSON>ulty doing vigorous activities such as lifting or moving heavy furniture, running, or participating in strenuous sports like tennis or racquetball", "Code": "100001180", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "No difficulty at all", "Selection Definition": null, "Display Order": 1}, {"Name": "Q12: Di<PERSON><PERSON>ulty doing vigorous activities such as lifting or moving heavy furniture, running, or participating in strenuous sports like tennis or racquetball", "Code": "100001181", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Hardly any difficulty", "Selection Definition": null, "Display Order": 2}, {"Name": "Q12: Di<PERSON><PERSON>ulty doing vigorous activities such as lifting or moving heavy furniture, running, or participating in strenuous sports like tennis or racquetball", "Code": "100001182", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "A little difficulty", "Selection Definition": null, "Display Order": 3}, {"Name": "Q12: Di<PERSON><PERSON>ulty doing vigorous activities such as lifting or moving heavy furniture, running, or participating in strenuous sports like tennis or racquetball", "Code": "100001183", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Moderate difficulty", "Selection Definition": null, "Display Order": 4}, {"Name": "Q12: Di<PERSON><PERSON>ulty doing vigorous activities such as lifting or moving heavy furniture, running, or participating in strenuous sports like tennis or racquetball", "Code": "100001184", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Quite a bit of difficulty", "Selection Definition": null, "Display Order": 5}, {"Name": "Q12: Di<PERSON><PERSON>ulty doing vigorous activities such as lifting or moving heavy furniture, running, or participating in strenuous sports like tennis or racquetball", "Code": "100001185", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "A lot of difficulty", "Selection Definition": null, "Display Order": 6}, {"Name": "Q12: Di<PERSON><PERSON>ulty doing vigorous activities such as lifting or moving heavy furniture, running, or participating in strenuous sports like tennis or racquetball", "Code": "100001186", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extreme difficulty", "Selection Definition": null, "Display Order": 7}], "Ranges": null, "Definition": {"Title": "Section 2, Q12", "Definition": " Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire", "Source": "Development and Validation of the Atrial Fibrillation Effect on QualiTy-of-Life (AFEQT) Questionnaire in Patients With Atrial Fibrillation. Spertus J, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Circ Arrhythm Electrophysiol. 2011;4:15-25."}, "Parent Child Validations": [{"Parent Element Reference": 4700, "Parent Element Name": "AFEQT Patient Questionnaire Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4775, "Name": "Q13: Feeling worried or anxious that atrial fibrillation can start anytime", "Section Display Name": "Atrial Fibrillation Effect on Quality of Life", "Coding Instructions": "Indicate the patient's response to the Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire Section 2- Question 13 \"Over the past four weeks, as a result of your atrial fibrillation, how much did feeling worried or anxious that your atrial fibrillation can start anytime?\"", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "AFEQTS2Q13", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001187", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Q13: Feeling worried or anxious that atrial fibrillation can start anytime", "Code": "100001250", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not at all bothered", "Selection Definition": null, "Display Order": 1}, {"Name": "Q13: Feeling worried or anxious that atrial fibrillation can start anytime", "Code": "100001159", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Hardly bothered ", "Selection Definition": null, "Display Order": 2}, {"Name": "Q13: Feeling worried or anxious that atrial fibrillation can start anytime", "Code": "100001160", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "A little bothered", "Selection Definition": null, "Display Order": 3}, {"Name": "Q13: Feeling worried or anxious that atrial fibrillation can start anytime", "Code": "100001161", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Moderately bothered", "Selection Definition": null, "Display Order": 4}, {"Name": "Q13: Feeling worried or anxious that atrial fibrillation can start anytime", "Code": "100001162", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Quite a bit bothered", "Selection Definition": null, "Display Order": 5}, {"Name": "Q13: Feeling worried or anxious that atrial fibrillation can start anytime", "Code": "100001163", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Very bothered", "Selection Definition": null, "Display Order": 6}, {"Name": "Q13: Feeling worried or anxious that atrial fibrillation can start anytime", "Code": "100001164", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extremely bothered", "Selection Definition": null, "Display Order": 7}], "Ranges": null, "Definition": {"Title": "Section 2, Q13", "Definition": " Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire", "Source": "Development and Validation of the Atrial Fibrillation Effect on QualiTy-of-Life (AFEQT) Questionnaire in Patients With Atrial Fibrillation. Spertus J, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Circ Arrhythm Electrophysiol. 2011;4:15-25."}, "Parent Child Validations": [{"Parent Element Reference": 4700, "Parent Element Name": "AFEQT Patient Questionnaire Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4780, "Name": "Q14: Feeling worried that atrial fibrillation may worsen other medical conditions in the long run", "Section Display Name": "Atrial Fibrillation Effect on Quality of Life", "Coding Instructions": "Indicate the patient's response to the Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire Section 2-  Question 14  \"Over the past four weeks, as a result of your atrial fibrillation, how much did feeling worried that your atrial fibrillation may worsen other medical conditions in the long run?\"", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "AFEQTS2Q14", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001188", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Q14: Feeling worried that atrial fibrillation may worsen other medical conditions in the long run", "Code": "100001250", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not at all bothered", "Selection Definition": null, "Display Order": 1}, {"Name": "Q14: Feeling worried that atrial fibrillation may worsen other medical conditions in the long run", "Code": "100001159", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Hardly bothered ", "Selection Definition": null, "Display Order": 2}, {"Name": "Q14: Feeling worried that atrial fibrillation may worsen other medical conditions in the long run", "Code": "100001160", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "A little bothered", "Selection Definition": null, "Display Order": 3}, {"Name": "Q14: Feeling worried that atrial fibrillation may worsen other medical conditions in the long run", "Code": "100001161", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Moderately bothered", "Selection Definition": null, "Display Order": 4}, {"Name": "Q14: Feeling worried that atrial fibrillation may worsen other medical conditions in the long run", "Code": "100001162", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Quite a bit bothered", "Selection Definition": null, "Display Order": 5}, {"Name": "Q14: Feeling worried that atrial fibrillation may worsen other medical conditions in the long run", "Code": "100001163", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Very bothered", "Selection Definition": null, "Display Order": 6}, {"Name": "Q14: Feeling worried that atrial fibrillation may worsen other medical conditions in the long run", "Code": "100001164", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extremely bothered", "Selection Definition": null, "Display Order": 7}], "Ranges": null, "Definition": {"Title": "Section 2, Q14", "Definition": " Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire", "Source": "Development and Validation of the Atrial Fibrillation Effect on QualiTy-of-Life (AFEQT) Questionnaire in Patients With Atrial Fibrillation. Spertus J, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Circ Arrhythm Electrophysiol. 2011;4:15-25."}, "Parent Child Validations": [{"Parent Element Reference": 4700, "Parent Element Name": "AFEQT Patient Questionnaire Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4785, "Name": "Q15: Worrying about the treatment side effects from medications", "Section Display Name": "Atrial Fibrillation Effect on Quality of Life", "Coding Instructions": "Indicate the patient's response to the Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire Section 2 - Question 15  \"Over the past four weeks, as a result of your atrial fibrillation treatment, how much were you bothered by worrying about the treatment side effects from medication?\"", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "AFEQTS2Q15", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001189", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Q15: Worrying about the treatment side effects from medications", "Code": "100001250", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not at all bothered", "Selection Definition": null, "Display Order": 1}, {"Name": "Q15: Worrying about the treatment side effects from medications", "Code": "100001159", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Hardly bothered ", "Selection Definition": null, "Display Order": 2}, {"Name": "Q15: Worrying about the treatment side effects from medications", "Code": "100001160", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "A little bothered", "Selection Definition": null, "Display Order": 3}, {"Name": "Q15: Worrying about the treatment side effects from medications", "Code": "100001161", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Moderately bothered", "Selection Definition": null, "Display Order": 4}, {"Name": "Q15: Worrying about the treatment side effects from medications", "Code": "100001162", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Quite a bit bothered", "Selection Definition": null, "Display Order": 5}, {"Name": "Q15: Worrying about the treatment side effects from medications", "Code": "100001163", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Very bothered", "Selection Definition": null, "Display Order": 6}, {"Name": "Q15: Worrying about the treatment side effects from medications", "Code": "100001164", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extremely bothered", "Selection Definition": null, "Display Order": 7}], "Ranges": null, "Definition": {"Title": "Section 2, Q15", "Definition": " Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire", "Source": "Development and Validation of the Atrial Fibrillation Effect on QualiTy-of-Life (AFEQT) Questionnaire in Patients With Atrial Fibrillation. Spertus J, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Circ Arrhythm Electrophysiol. 2011;4:15-25."}, "Parent Child Validations": [{"Parent Element Reference": 4700, "Parent Element Name": "AFEQT Patient Questionnaire Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4790, "Name": "Q16: Worrying about complications or side effects from procedures like catheter ablation, surgery, or pacemakers therapy", "Section Display Name": "Atrial Fibrillation Effect on Quality of Life", "Coding Instructions": "Indicate the patient's response to the Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire Section 2 - Question 16  \"Over the past four weeks, as a result of your atrial fibrillation treatment, how much were you bothered by worrying about complications or side effects from procedures like catheter ablation, surgery or pacemaker therapy?\"", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "AFEQTS2Q16", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001190", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Q16: Worrying about complications or side effects from procedures like catheter ablation, surgery, or pacemakers therapy", "Code": "100001250", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not at all bothered", "Selection Definition": null, "Display Order": 1}, {"Name": "Q16: Worrying about complications or side effects from procedures like catheter ablation, surgery, or pacemakers therapy", "Code": "100001159", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Hardly bothered ", "Selection Definition": null, "Display Order": 2}, {"Name": "Q16: Worrying about complications or side effects from procedures like catheter ablation, surgery, or pacemakers therapy", "Code": "100001160", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "A little bothered", "Selection Definition": null, "Display Order": 3}, {"Name": "Q16: Worrying about complications or side effects from procedures like catheter ablation, surgery, or pacemakers therapy", "Code": "100001161", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Moderately bothered", "Selection Definition": null, "Display Order": 4}, {"Name": "Q16: Worrying about complications or side effects from procedures like catheter ablation, surgery, or pacemakers therapy", "Code": "100001162", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Quite a bit bothered", "Selection Definition": null, "Display Order": 5}, {"Name": "Q16: Worrying about complications or side effects from procedures like catheter ablation, surgery, or pacemakers therapy", "Code": "100001163", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Very bothered", "Selection Definition": null, "Display Order": 6}, {"Name": "Q16: Worrying about complications or side effects from procedures like catheter ablation, surgery, or pacemakers therapy", "Code": "100001164", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extremely bothered", "Selection Definition": null, "Display Order": 7}], "Ranges": null, "Definition": {"Title": "Section 2, Q16", "Definition": " Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire", "Source": "Development and Validation of the Atrial Fibrillation Effect on QualiTy-of-Life (AFEQT) Questionnaire in Patients With Atrial Fibrillation. Spertus J, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Circ Arrhythm Electrophysiol. 2011;4:15-25."}, "Parent Child Validations": [{"Parent Element Reference": 4700, "Parent Element Name": "AFEQT Patient Questionnaire Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4795, "Name": "Q17: Worrying about side effects of blood thinners such as nosebleeds, bleeding gums when brushing teeth, heavy bleeding from cuts, or bruising", "Section Display Name": "Atrial Fibrillation Effect on Quality of Life", "Coding Instructions": "Indicate the patient's response to the Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire Section 2 - Question 17 \"Over the past four weeks, as a result of your atrial fibrillation treatment, how much were you bothered by worrying about side effects of blood thinners such as nosebleeds, bleeding gums when brushing teeth, heavy bleeding from cuts, or bruising?\"", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "AFEQTS2Q17", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001191", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Q17: Worrying about side effects of blood thinners such as nosebleeds, bleeding gums when brushing teeth, heavy bleeding from cuts, or bruising", "Code": "100001250", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not at all bothered", "Selection Definition": null, "Display Order": 1}, {"Name": "Q17: Worrying about side effects of blood thinners such as nosebleeds, bleeding gums when brushing teeth, heavy bleeding from cuts, or bruising", "Code": "100001159", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Hardly bothered ", "Selection Definition": null, "Display Order": 2}, {"Name": "Q17: Worrying about side effects of blood thinners such as nosebleeds, bleeding gums when brushing teeth, heavy bleeding from cuts, or bruising", "Code": "100001160", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "A little bothered", "Selection Definition": null, "Display Order": 3}, {"Name": "Q17: Worrying about side effects of blood thinners such as nosebleeds, bleeding gums when brushing teeth, heavy bleeding from cuts, or bruising", "Code": "100001161", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Moderately bothered", "Selection Definition": null, "Display Order": 4}, {"Name": "Q17: Worrying about side effects of blood thinners such as nosebleeds, bleeding gums when brushing teeth, heavy bleeding from cuts, or bruising", "Code": "100001162", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Quite a bit bothered", "Selection Definition": null, "Display Order": 5}, {"Name": "Q17: Worrying about side effects of blood thinners such as nosebleeds, bleeding gums when brushing teeth, heavy bleeding from cuts, or bruising", "Code": "100001163", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Very bothered", "Selection Definition": null, "Display Order": 6}, {"Name": "Q17: Worrying about side effects of blood thinners such as nosebleeds, bleeding gums when brushing teeth, heavy bleeding from cuts, or bruising", "Code": "100001164", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extremely bothered", "Selection Definition": null, "Display Order": 7}], "Ranges": null, "Definition": {"Title": "Section 2, Q17", "Definition": " Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire", "Source": "Development and Validation of the Atrial Fibrillation Effect on QualiTy-of-Life (AFEQT) Questionnaire in Patients With Atrial Fibrillation. Spertus J, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Circ Arrhythm Electrophysiol. 2011;4:15-25."}, "Parent Child Validations": [{"Parent Element Reference": 4700, "Parent Element Name": "AFEQT Patient Questionnaire Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4800, "Name": "Q18: Worrying or feeling anxious that treatment interferes with daily activities", "Section Display Name": "Atrial Fibrillation Effect on Quality of Life", "Coding Instructions": "Indicate the patient's response to the Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire Section 2 - Question 18 \"Over the past four weeks, as a result of your atrial fibrillation treatment, how much were you bothered by worrying or feeling anxious that your treatment interferes with your daily activities?\"", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "AFEQTS2Q18", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001192", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Q18: Worrying or feeling anxious that treatment interferes with daily activities", "Code": "100001250", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not at all bothered", "Selection Definition": null, "Display Order": 1}, {"Name": "Q18: Worrying or feeling anxious that treatment interferes with daily activities", "Code": "100001159", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Hardly bothered ", "Selection Definition": null, "Display Order": 2}, {"Name": "Q18: Worrying or feeling anxious that treatment interferes with daily activities", "Code": "100001160", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "A little bothered", "Selection Definition": null, "Display Order": 3}, {"Name": "Q18: Worrying or feeling anxious that treatment interferes with daily activities", "Code": "100001161", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Moderately bothered", "Selection Definition": null, "Display Order": 4}, {"Name": "Q18: Worrying or feeling anxious that treatment interferes with daily activities", "Code": "100001162", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Quite a bit bothered", "Selection Definition": null, "Display Order": 5}, {"Name": "Q18: Worrying or feeling anxious that treatment interferes with daily activities", "Code": "100001163", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Very bothered", "Selection Definition": null, "Display Order": 6}, {"Name": "Q18: Worrying or feeling anxious that treatment interferes with daily activities", "Code": "100001164", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extremely bothered", "Selection Definition": null, "Display Order": 7}], "Ranges": null, "Definition": {"Title": "Section 2, Q18", "Definition": " Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire", "Source": "Development and Validation of the Atrial Fibrillation Effect on QualiTy-of-Life (AFEQT) Questionnaire in Patients With Atrial Fibrillation. Spertus J, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Circ Arrhythm Electrophysiol. 2011;4:15-25."}, "Parent Child Validations": [{"Parent Element Reference": 4700, "Parent Element Name": "AFEQT Patient Questionnaire Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4805, "Name": "Q19: How well current treatment controls atrial fibrillation", "Section Display Name": "Atrial Fibrillation Effect on Quality of Life", "Coding Instructions": "Indicate the patient's response to the Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire Section 2 - Question 19 \"Overall how satisfied are you at the present time with how well your current treatment controls your atrial fibrillation?\"", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "AFEQTS2Q19", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001193", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Q19: How well current treatment controls atrial fibrillation", "Code": "100001195", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extremely satisfied", "Selection Definition": null, "Display Order": 1}, {"Name": "Q19: How well current treatment controls atrial fibrillation", "Code": "100001196", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Very satisfied", "Selection Definition": null, "Display Order": 2}, {"Name": "Q19: How well current treatment controls atrial fibrillation", "Code": "100001197", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Somewhat satisfied", "Selection Definition": null, "Display Order": 3}, {"Name": "Q19: How well current treatment controls atrial fibrillation", "Code": "100001198", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Mixed with satisfied and dissatisfied", "Selection Definition": null, "Display Order": 4}, {"Name": "Q19: How well current treatment controls atrial fibrillation", "Code": "100001199", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Somewhat dissatisfied", "Selection Definition": null, "Display Order": 5}, {"Name": "Q19: How well current treatment controls atrial fibrillation", "Code": "100001228", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Very dissatisfied", "Selection Definition": null, "Display Order": 6}, {"Name": "Q19: How well current treatment controls atrial fibrillation", "Code": "100001200", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extremely dissatisfied", "Selection Definition": null, "Display Order": 7}], "Ranges": null, "Definition": {"Title": "Section 2, Q19", "Definition": " Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire", "Source": "Development and Validation of the Atrial Fibrillation Effect on QualiTy-of-Life (AFEQT) Questionnaire in Patients With Atrial Fibrillation. Spertus J, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Circ Arrhythm Electrophysiol. 2011;4:15-25."}, "Parent Child Validations": [{"Parent Element Reference": 4700, "Parent Element Name": "AFEQT Patient Questionnaire Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4810, "Name": "Q20: The extent to which treatment has relieved  symptoms of atrial fibrillation", "Section Display Name": "Atrial Fibrillation Effect on Quality of Life", "Coding Instructions": "Indicate the patient's response to the Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire Section 2- Question 20 \"Overall how satisfied are you at the present time with the extent to which your treatment has relieved your symptoms of atrial fibrillation?\"", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "AFEQTS2Q20", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001194", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Q20: The extent to which treatment has relieved  symptoms of atrial fibrillation", "Code": "100001195", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extremely satisfied", "Selection Definition": null, "Display Order": 1}, {"Name": "Q20: The extent to which treatment has relieved  symptoms of atrial fibrillation", "Code": "100001196", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Very satisfied", "Selection Definition": null, "Display Order": 2}, {"Name": "Q20: The extent to which treatment has relieved  symptoms of atrial fibrillation", "Code": "100001197", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Somewhat satisfied", "Selection Definition": null, "Display Order": 3}, {"Name": "Q20: The extent to which treatment has relieved  symptoms of atrial fibrillation", "Code": "100001198", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Mixed with satisfied and dissatisfied", "Selection Definition": null, "Display Order": 4}, {"Name": "Q20: The extent to which treatment has relieved  symptoms of atrial fibrillation", "Code": "100001199", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Somewhat dissatisfied", "Selection Definition": null, "Display Order": 5}, {"Name": "Q20: The extent to which treatment has relieved  symptoms of atrial fibrillation", "Code": "100001228", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Very dissatisfied", "Selection Definition": null, "Display Order": 6}, {"Name": "Q20: The extent to which treatment has relieved  symptoms of atrial fibrillation", "Code": "100001200", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Extremely dissatisfied", "Selection Definition": null, "Display Order": 7}], "Ranges": null, "Definition": {"Title": "Section 2, Q20", "Definition": " Atrial Fibrillation Effect on QualiTy-of-life (AFEQT) Questionnaire", "Source": "Development and Validation of the Atrial Fibrillation Effect on QualiTy-of-Life (AFEQT) Questionnaire in Patients With Atrial Fibrillation. Spertus J, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Circ Arrhythm Electrophysiol. 2011;4:15-25."}, "Parent Child Validations": [{"Parent Element Reference": 4700, "Parent Element Name": "AFEQT Patient Questionnaire Performed", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "Physical Exam and Labs", "Section Code": "PELABS", "Section Type": "Section", "Cardinality": "0..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 6000, "Name": "Height", "Section Display Name": "Physical Exam and Labs", "Coding Instructions": "Indicate the patient's height in centimeters.", "Target Value": "The last value prior to the start of the first procedure", "Short Name": "Height", "Data Type": "PQ", "Precision": "5,2", "Unit Of Measure": "cm", "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "8302-2", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "cm", "Usual Range Min": "100.00", "Usual Range Max": "225.00", "Valid Range Min": "20.00", "Valid Range Max": "260.00"}], "Definition": null, "Parent Child Validations": null}, {"Element Reference": 6005, "Name": "Weight", "Section Display Name": "Physical Exam and Labs", "Coding Instructions": "Indicate the patient's weight in kilograms. ", "Target Value": "The last value prior to the start of the first procedure", "Short Name": "Weight", "Data Type": "PQ", "Precision": "5,2", "Unit Of Measure": "kg", "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "3141-9", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "kg", "Usual Range Min": "40.00", "Usual Range Max": "200.00", "Valid Range Min": "10.00", "Valid Range Max": "700.00"}], "Definition": null, "Parent Child Validations": null}, {"Element Reference": 6010, "Name": "Pulse", "Section Display Name": "Physical Exam and Labs", "Coding Instructions": "Indicate the patient's heart rate (beats per minute).", "Target Value": "The last value prior to the start of the first procedure", "Short Name": "Pulse", "Data Type": "PQ", "Precision": "3,0", "Unit Of Measure": "bpm", "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "8867-4", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "bpm", "Usual Range Min": "30", "Usual Range Max": "250", "Valid Range Min": "0", "Valid Range Max": "300"}], "Definition": null, "Parent Child Validations": null}, {"Element Reference": 6015, "Name": "Systolic BP", "Section Display Name": "Physical Exam and Labs", "Coding Instructions": "Indicate the patient's systolic blood pressure in mmHg. ", "Target Value": "The last value prior to the start of the first procedure", "Short Name": "SystolicBP", "Data Type": "PQ", "Precision": "3,0", "Unit Of Measure": "mm[Hg]", "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "8480-6", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "mm[Hg]", "Usual Range Min": "50", "Usual Range Max": "220", "Valid Range Min": "1", "Valid Range Max": "300"}], "Definition": null, "Parent Child Validations": null}, {"Element Reference": 6020, "Name": "Diastolic BP", "Section Display Name": "Physical Exam and Labs", "Coding Instructions": "Indicate the patient's  diastolic blood pressure in mmHg. ", "Target Value": "The last value prior to the start of the first procedure", "Short Name": "DiastolicBP", "Data Type": "PQ", "Precision": "3,0", "Unit Of Measure": "mm[Hg]", "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "8462-4", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "mm[Hg]", "Usual Range Min": "30", "Usual Range Max": "110", "Valid Range Min": "1", "Valid Range Max": "200"}], "Definition": null, "Parent Child Validations": null}, {"Element Reference": 6045, "Name": "International Normalized Ratio (INR)", "Section Display Name": "Physical Exam and Labs", "Coding Instructions": "Indicate the international normalized ratio (INR) if the patient was on routine Warfarin/Coumadin therapy.\n\nNote(s):\nThis may include POC (Point of Care) testing results.\n\nMost recent values prior to the start of the procedure.", "Target Value": "The last value between 1 day prior to the procedure and the current procedure", "Short Name": "INR", "Data Type": "PQ", "Precision": "3,1", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "34714-6", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": null, "Usual Range Min": "0.9", "Usual Range Max": "1.3", "Valid Range Min": "0.5", "Valid Range Max": "30.0"}], "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 6046, "Parent Element Name": "International Normalized Ratio Not Drawn", "Parent Element Selection Name": "No (or Not Answered)"}]}, {"Element Reference": 6046, "Name": "International Normalized Ratio Not Drawn", "Section Display Name": "Physical Exam and Labs", "Coding Instructions": "Indicate if INR was not drawn.", "Target Value": "N/A", "Short Name": "INRND", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "34714-6", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 6050, "Name": "Creatinine", "Section Display Name": "Physical Exam and Labs", "Coding Instructions": "Indicate the creatinine (Cr) level mg/dL.\n\nNote(s):\nThis may include POC (Point of Care) testing results.", "Target Value": "The last value between 30 days prior to the procedure and the current procedure", "Short Name": "PreProcCreat", "Data Type": "PQ", "Precision": "4,2", "Unit Of Measure": "mg/dL", "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2160-0", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "mg/dL", "Usual Range Min": "0.10", "Usual Range Max": "5.00", "Valid Range Min": "0.10", "Valid Range Max": "30.00"}], "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 6051, "Parent Element Name": "<PERSON><PERSON><PERSON>ine Not Drawn", "Parent Element Selection Name": "No (or Not Answered)"}]}, {"Element Reference": 6051, "Name": "<PERSON><PERSON><PERSON>ine Not Drawn", "Section Display Name": "Physical Exam and Labs", "Coding Instructions": "Indicate if a creatinine level was not drawn.", "Target Value": "N/A", "Short Name": "PreProcCreatND", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2160-0", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 6030, "Name": "Hemoglobin", "Section Display Name": "Physical Exam and Labs", "Coding Instructions": "Indicate the hemoglobin (Hgb) value in g/dL.\n\nNote(s):  \nThis may include POC (Point of Care) testing results or results obtained prior to arrival at this facility.", "Target Value": "The last value within 30 days prior to the first procedure in this admission", "Short Name": "HGB", "Data Type": "PQ", "Precision": "4,2", "Unit Of Measure": "g/dL", "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "718-7", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "g/dL", "Usual Range Min": "5.00", "Usual Range Max": "20.00", "Valid Range Min": "1.00", "Valid Range Max": "50.00"}], "Definition": {"Title": "Hemoglobin", "Definition": "Hemoglobin (Hb or Hgb) is the iron-containing oxygen-transport metalloprotein in the red blood cells. It carries oxygen from the lungs to the rest of the body (i.e. the tissues) where it releases the oxygen to burn nutrients and provide energy. Hemoglobin concentration measurement is among the most commonly performed blood tests, usually as part of a complete blood count. If the concentration is below normal, this is called anemia. Anemias are classified by the size of red blood cells: \"microcytic\" if red cells are small, \"macrocytic\" if they are large, and \"normocytic\" if otherwise. Dehydration or hyperhydration can greatly influence measured hemoglobin levels.", "Source": "http://s.details.loinc.org/LOINC/718-7.html?sections=Simple"}, "Parent Child Validations": [{"Parent Element Reference": 6031, "Parent Element Name": "Hemoglobin Not Drawn", "Parent Element Selection Name": "No (or Not Answered)"}]}, {"Element Reference": 6031, "Name": "Hemoglobin Not Drawn", "Section Display Name": "Physical Exam and Labs", "Coding Instructions": "Indicate if the hemoglobin was not drawn.", "Target Value": "The last value within 30 days prior to the first procedure in this admission", "Short Name": "HGBND", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "718-7", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Hemoglobin", "Definition": "Hemoglobin (Hb or Hgb) is the iron-containing oxygen-transport metalloprotein in the red blood cells. It carries oxygen from the lungs to the rest of the body (i.e. the tissues) where it releases the oxygen to burn nutrients and provide energy. Hemoglobin concentration measurement is among the most commonly performed blood tests, usually as part of a complete blood count. If the concentration is below normal, this is called anemia. Anemias are classified by the size of red blood cells: \"microcytic\" if red cells are small, \"macrocytic\" if they are large, and \"normocytic\" if otherwise. Dehydration or hyperhydration can greatly influence measured hemoglobin levels.", "Source": "http://s.details.loinc.org/LOINC/718-7.html?sections=Simple"}, "Parent Child Validations": null}, {"Element Reference": 14280, "Name": "BNP", "Section Display Name": "Physical Exam and Labs", "Coding Instructions": "Indicate the B-type natriuretic peptide (BNP) value.", "Target Value": "The last value between 6 months prior to procedure and the start of the current procedure", "Short Name": "PreProc_BNPValue", "Data Type": "PQ", "Precision": "5,0", "Unit Of Measure": "pg/mL", "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "42637-9", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": "Either BNP (14280) or N-Terminal Pro B-Type Natriuretic Peptide Value (14279) can have a value, not both", "Selections": null, "Ranges": [{"Unit Of Measure": "pg/mL", "Usual Range Min": "5", "Usual Range Max": "1,000", "Valid Range Min": "1", "Valid Range Max": "10,000"}], "Definition": {"Title": "Natriuretic peptide B", "Definition": "Brain natriuretic peptide (BNP) is an active fragment (1-32) of ProBNP which is produced by myocardial cells. It increases in both right-sided and left-sided heart failure as well as in systolic and diastolic heart failure. Thus, it is used to diagnose and manage heart failure. When a patient is taking recombinant PBN (Natricor), BNP will reflect serum levels. NT-ProBNP, an inactive fragment (1-78) of ProBNP is used to assess the degree of failure. Both of these polypeptides have roughly the same predictive power. NT-ProBNP is commonly called ProBNP.", "Source": "http://s.details.loinc.org/LOINC/42637-9.html?sections=Simple"}, "Parent Child Validations": [{"Parent Element Reference": 13205, "Parent Element Name": "B-Type Natriuretic Peptide Not Drawn", "Parent Element Selection Name": "No (or Not Answered)"}]}, {"Element Reference": 13205, "Name": "B-Type Natriuretic Peptide Not Drawn", "Section Display Name": "Physical Exam and Labs", "Coding Instructions": "Indicate if a pre-procedure B-type natriuretic peptide (BNP) was not collected.", "Target Value": "N/A", "Short Name": "PreProcBNPNotDrawn", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "42637-9", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 14279, "Name": "N-Terminal Pro B-Type Natriuretic Peptide Value", "Section Display Name": "Physical Exam and Labs", "Coding Instructions": "Indicate the N-Terminal Pro B-Type Natriuretic Peptide (NT-proBNP) Value.", "Target Value": "The last value between 6 months prior to procedure and the start of the current procedure", "Short Name": "PreProcedureNTBNP", "Data Type": "PQ", "Precision": "5,0", "Unit Of Measure": "pg/mL", "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "33762-6", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": "Either BNP (14280) or N-Terminal Pro B-Type Natriuretic Peptide Value (14279) can have a value, not both", "Selections": null, "Ranges": [{"Unit Of Measure": "pg/mL", "Usual Range Min": "5", "Usual Range Max": "30,000", "Valid Range Min": "5", "Valid Range Max": "30,000"}], "Definition": {"Title": "N-Terminal Pro B-Type Natriuretic Peptide Value", "Definition": "ProBNP is the 108 amino acid pro-hormone of BNP (Brain Naturetic Peptide) that is produced mainly in the left ventricle. The prohormone splits into two polypeptides- the biologically active but shorter BNP (77-108) and the longer N terminal (1-76) fragment called NT-proBNP. Commercial assays are available for NT-proBNP because of its usefulness in predicting cardiovascular risk. In one study, it was the single best predictor of survival among patients with the acute coronary syndrome. It also declines with successful treatment of left ventricular dysfunctionand heart failure and is used by some to track the success of such treatment. No commercial assays exist for proBNP (the whole peptide)- though the trade name for one companies NT-proBNP is \"proBNP\" -- a misnomer. We include proBNP as the a related name for NT-proBNP so that people who call it proBNP will find it in LOINC. \n  Source: Regenstrief Help", "Source": "http://s.details.loinc.org/LOINC/33762-6.html?sections=Simple"}, "Parent Child Validations": [{"Parent Element Reference": 13206, "Parent Element Name": "N-Terminal Pro B-Type Natriuretic Peptide Not Drawn", "Parent Element Selection Name": "No (or Not Answered)"}]}, {"Element Reference": 13206, "Name": "N-Terminal Pro B-Type Natriuretic Peptide Not Drawn", "Section Display Name": "Physical Exam and Labs", "Coding Instructions": "Indicate if a pre-procedure N-terminal pro B-type natriuretic peptide (NT-proBNP) was not collected.", "Target Value": "N/A", "Short Name": "PreProcNTBNPNotDrawn", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "33762-6", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "History and Risk Factors", "Section Code": "HISTORYANDRISK", "Section Type": "Section", "Cardinality": "0..1", "Table": "EPISODEOFCARE", "Elements": null}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "History and Risk Factors", "Section Display Name": "CHA2DS2-VASC Risk Scores", "Section Code": "CHA2DS2", "Section Type": "Section", "Cardinality": "0..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 4005, "Name": "CHA2DS2-VASc Congestive Heart Failure", "Section Display Name": "CHA2DS2-VASC Risk Scores", "Coding Instructions": "Indicate if the patient has been diagnosed with heart failure according to the CHA2DS2-VASc definition.\n\nNote(s): A diagnosis of heart failure must be specifically documented in the medical record and not coded by the abstractor based upon patient symptoms. ", "Target Value": "Any occurrence between 30 days prior to the procedure and the procedure", "Short Name": "ChadCHF", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001203", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "CHA2DS2-VASc Congestive Heart Failure", "Definition": "The presence of signs and symptoms of either right (elevated central venous pressure, hepatomegaly, dependent edema) or left ventricular failure (exertionaldyspnea, cough, fatigue, orthopnea, paroxysmal nocturnal dyspnea, cardiac enlargement, rales, gallop rhythm, pulmonary venous congestion) or both, confirmed by non-invasive or invasive measurements demonstrating objective evidence of cardiac dysfunction.", "Source": "Refining clinical risk stratification for predicting stroke and thromboembolism in atrial fibrillation using a novel risk factor-based approach: the euro heart survey on atrial fibrillation Lip GH, Nieuwlaat R, Pisters R, Lane DA, Crijns HM. Chest.2010;137(2):263-272."}, "Parent Child Validations": null}, {"Element Reference": 4015, "Name": "CHA2DS2-VASc LV Dysfunction", "Section Display Name": "CHA2DS2-VASC Risk Scores", "Coding Instructions": "Indicate if the patient has been diagnosed with Left Ventricular (LV) Dysfunction according to the CHA2DS2-VASc definition.", "Target Value": "Any occurrence between 30 days prior to the procedure and the procedure", "Short Name": "ChadLVDysf", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001204", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "CHA2DS2 -VASc LV Dysfunction", "Definition": "Left Ventricular Ejection Fraction < 40%.", "Source": "Refining clinical risk stratification for predicting stroke and thromboembolism in atrial fibrillation using a novel risk factor-based approach: the euro heart survey on atrial fibrillation Lip GH, Nieuwlaat R, Pisters R, Lane DA, Crijns HM. Chest.2010;137(2):263-272."}, "Parent Child Validations": null}, {"Element Reference": 4020, "Name": "CHA2DS2-VASc Hypertension", "Section Display Name": "CHA2DS2-VASC Risk Scores", "Coding Instructions": "Indicate if the patient has been diagnosed with hypertension according to the CHA2DS2-VASc definition.", "Target Value": "Any occurrence between 30 days prior to the procedure and the procedure", "Short Name": "ChadHypertCont", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001205", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "CHA2DS2-VASc Hypertension", "Definition": "A resting blood pressure >140mmHg systolic and/or >90mmHg diastolic on at least 2 occasions or current antihypertensive pharmacologic treatment.", "Source": "Refining clinical risk stratification for predicting stroke and thromboembolism in atrial fibrillation using a novel risk factor-based approach: the euro heart survey on atrial fibrillation Lip GH, Nieuwlaat R, Pisters R, Lane DA, Crijns HM. Chest.2010;137(2):263-272."}, "Parent Child Validations": null}, {"Element Reference": 4025, "Name": "CHA2DS2-VASc Diabetes Mellitus", "Section Display Name": "CHA2DS2-VASC Risk Scores", "Coding Instructions": "Indicate if the patient has been diagnosed with diabetes mellitus according to the CHA2DS2-VASc definition.", "Target Value": "Any occurrence between 30 days prior to the procedure and the procedure", "Short Name": "ChadDM", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001206", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "CHA2DS2-VASc Diabetes Mellitus", "Definition": "Fasting plasma glucose level ≥ 7.0 mmol/L (126 mg/dL) or treatment with oral hypoglycaemic agent and/or insulin.", "Source": "Refining clinical risk stratification for predicting stroke and thromboembolism in atrial fibrillation using a novel risk factor-based approach: the euro heart survey on atrial fibrillation Lip GH, Nieuwlaat R, Pisters R, Lane DA, Crijns HM. Chest.2010;137(2):263-272. "}, "Parent Child Validations": null}, {"Element Reference": 4045, "Name": "CHA2DS2-VASc Vascular Disease", "Section Display Name": "CHA2DS2-VASC Risk Scores", "Coding Instructions": "Indicate if the patient has been diagnosed with vascular disease according to the CHA2DS2-VASc definition.", "Target Value": "Any occurrence between birth and the procedure", "Short Name": "ChadVascDis", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001210", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "CHA2DS2-VASc Vascular Disease", "Definition": "Coronary artery disease: Prior myocardial infarction, angina pectoris, percutaneous coronary intervention or coronary artery bypasses surgery. \n\nPeripheral vascular disease: The presence of any the following: intermittent claudication, previous surgery or percutaneous intervention on the abdominal aorta or the lower extremity vessels, abdominal or thoracic surgery, arterial and venous thrombosis.", "Source": "Refining clinical risk stratification for predicting stroke and thromboembolism in atrial fibrillation using a novel risk factor-based approach: the euro heart survey on atrial fibrillation Lip GH, Nieuwlaat R, Pisters R, Lane DA, Crijns HM. Chest.2010;137(2):263-272."}, "Parent Child Validations": null}, {"Element Reference": 4030, "Name": "CHA2DS2-VASc Stroke", "Section Display Name": "CHA2DS2-VASC Risk Scores", "Coding Instructions": "Indicate if the patient has been diagnosed with an ischemic stroke, according to the CHA2DS2-VASc definition, or a stroke with undetermined origin. \n\nNote: If the stroke was Hemorrhagic in origin code 'No.'", "Target Value": "Any occurrence between birth and the procedure", "Short Name": "ChadStroke", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001207", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "CHA2DS2-VASc Stroke", "Definition": "Ischemic stroke is defined as a focal neurologic deficit of sudden onset as diagnosed by a neurologist, lasting > 24 h and caused by ischemia.", "Source": "Refining clinical risk stratification for predicting stroke and thromboembolism in atrial fibrillation using a novel risk factor-based approach: the euro heart survey on atrial fibrillation Lip GH, Nieuwlaat R, Pisters R, Lane DA, Crijns HM. Chest.2010;137(2):263-272."}, "Parent Child Validations": null}, {"Element Reference": 4035, "Name": "CHA2DS2-VASc TIA", "Section Display Name": "CHA2DS2-VASC Risk Scores", "Coding Instructions": "Indicate if the patient has been diagnosed with a transient ischemic attack (TIA) according to the CHA2DS2-VASc definition.", "Target Value": "Any occurrence between birth and the procedure", "Short Name": "ChadTIA", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001208", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "CHA2DS2-VASc TIA", "Definition": "Transient ischemic attack (TIA) is defined as a focal neurologic deficit of sudden onset as diagnosed by a neurologist, lasting < 24 hr.", "Source": "Refining clinical risk stratification for predicting stroke and thromboembolism in atrial fibrillation using a novel risk factor-based approach: the euro heart survey on atrial fibrillation. Lip GH, <PERSON><PERSON><PERSON><PERSON><PERSON> R, Pisters R, Lane DA, Crijns HM. Chest. 2010;137(2):263-272."}, "Parent Child Validations": null}, {"Element Reference": 4040, "Name": "CHA2DS2-VASc Thromboembolic Event", "Section Display Name": "CHA2DS2-VASC Risk Scores", "Coding Instructions": "Indicate if the patient has been diagnosed with a thromboembolic event (TE) according to the CHA2DS2-VASc definition.", "Target Value": "Any occurrence between birth and the procedure", "Short Name": "ChadTE", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001209", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Thromboembolic Events", "Definition": "Thrombolembolic Event (TE) is defined as either an ischemic stroke, peripheral embolism, or pulmonary embolism. Peripheral embolism is defined as a TE outside the brain, heart, eyes, and lungs. Pulmonary embolism is defined by the responsible physician. ", "Source": null}, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "History and Risk Factors", "Section Display Name": "Condition History", "Section Code": "CONDHX", "Section Type": "Repeater Section", "Cardinality": "0..n", "Table": "CONDHX", "Elements": [{"Element Reference": 12903, "Name": "Condition History Name", "Section Display Name": "Condition History", "Coding Instructions": "Select from the following list medical conditions based on prior diagnoses (or orders, such as for medication) given to the patient.  Additional definitions below for those selections that may need additional clarification.", "Target Value": "N/A", "Short Name": "ConditionHx", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "312850006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Condition History Name", "Code": "418799008+106063007:=195080001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Symptoms During Afib/Aflutter", "Selection Definition": null, "Display Order": 1}, {"Name": "Condition History Name", "Code": "85898001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Cardiomyopathy", "Selection Definition": null, "Display Order": 2}, {"Name": "Condition History Name", "Code": "413839001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Chronic Lung Disease", "Selection Definition": "Coding requires a documented history or diagnosis of a chronic lung disease.  Examples of these are:   chronic inhalation reactive disease (asbestosis, mesothelioma, black lung disease or pneumoconiosis), Radiation induced pneumonitis or radiation fibrosis, chronic obstructive pulmonary disease, chronic bronchitis, or emphysema.\n\nIt can also include a patient who is currently being chronically treated with inhaled or oral pharmacological therapy (e.g., beta-adrenergic agonist, anti-inflammatory agent, leukotriene receptor antagonist, or steroid).\n\nPatients not included are: history of a transient condition, for example: atelectasis.  Patients with asthma or seasonal allergies are also not considered to have chronic lung disease.", "Display Order": 3}, {"Name": "Condition History Name", "Code": "53741008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Coronary Artery Disease", "Selection Definition": "Other documentation that can be used to support a history of CAD:  \n\nCoronary artery stenosis >=50% (by cardiac catheterization or other modality or of direct imaging of the coronary arteries)\n\n* Previous CABG surgery\n\n* Previous PCI\n* Previous MI", "Display Order": 4}, {"Name": "Condition History Name", "Code": "73430006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Sleep Apnea", "Selection Definition": "Sleep apnea must be diagnosed by a provider or by sleep study. \n\n*Do not capture suspected sleep apnea or that reported by family members as sleep apnea.\n\n*Both Obstructive and Central Sleep Apnea are captured.\n\n*Code \"No\" if sleep apnea has been surgically corrected.\n\n*CPAP or BiPAP therapy is not a requirement to code \"Yes\" for sleep apnea.", "Display Order": 5}, {"Name": "Condition History Name", "Code": "100001118", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Valvular Atrial Fibrillation", "Selection Definition": "Consider this selection if atrial fibrillation is present in the setting of valvular heart disease and believed to be, at least in part, directly attributable to valvular heart disease\n\n*Must be diagnosed by a provider.\n\n", "Display Order": 6}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 15510, "Name": "Condition History Occurrence", "Section Display Name": "Condition History", "Coding Instructions": "Please indicate whether the patient has or has not had a clinical diagnosis of the respective medical condition.\n\nPlease refer to \"Condition History 12903\" to view a list of selections and definitions.", "Target Value": "Any occurrence between birth and arrival at this facility", "Short Name": "ConditionHxOccurenceArrival", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "312850006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Any Value"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "History and Risk Factors", "Section Display Name": "Condition History Details", "Section Code": "CONDHXDET", "Section Type": "Section", "Cardinality": "0..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 15723, "Name": "Symptoms Experienced", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate the symptoms that are documented in the medical record that are due to atrial fibrillation or atrial flutter. ", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "SxExperienced", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": null, "Is Dynamic List": "Yes", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "418799008+106063007:=195080001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Symptoms Experienced", "Code": "48694002", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Anxiety", "Selection Definition": null, "Display Order": 1}, {"Name": "Symptoms Experienced", "Code": "29857009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Chest pain", "Selection Definition": null, "Display Order": 2}, {"Name": "Symptoms Experienced", "Code": "161941007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Dyspnea at rest", "Selection Definition": null, "Display Order": 3}, {"Name": "Symptoms Experienced", "Code": "60845006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Dyspnea on exertion", "Selection Definition": null, "Display Order": 4}, {"Name": "Symptoms Experienced", "Code": "84229001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Fatigue", "Selection Definition": null, "Display Order": 5}, {"Name": "Symptoms Experienced", "Code": "361137007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Irregular heartbeat", "Selection Definition": null, "Display Order": 6}, {"Name": "Symptoms Experienced", "Code": "386705008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Light-headedness", "Selection Definition": null, "Display Order": 7}, {"Name": "Symptoms Experienced", "Code": "80313002", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Palpitations", "Selection Definition": null, "Display Order": 8}, {"Name": "Symptoms Experienced", "Code": "112000003645", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Other", "Selection Definition": null, "Display Order": 9}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Symptoms During Afib/Aflutter"}, {"Parent Element Reference": 15510, "Parent Element Name": "Condition History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4570, "Name": "Cardiomyopathy Type", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate the type of cardiomyopathy experienced by the patient.\n\nNote(s):\nIf the patient has had multiple cardiomyopathies, select all applicable types.", "Target Value": "Any occurrence between birth and the procedure", "Short Name": "PriorCMType", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000953", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Cardiomyopathy Type", "Code": "233873004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Hypertrophic", "Selection Definition": "Characterized morphologically and defined by a hypertrophied, nondilated LV in the absence of another systemic or cardiac disease that is capable of producing the magnitude of wall thickening evident (eg, systemic hypertension, aortic valve stenosis). Clinical diagnosis is customarily made with 2-dimensional echocardiography (or alternatively with cardiac magnetic resonance imaging) by detection of otherwise unexplained LV wall thickening, usually in the presence of a small LV cavity, after suspicion is raised by the clinical profile or as part of family screening.", "Display Order": 1}, {"Name": "Cardiomyopathy Type", "Code": "426856002", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Ischemic", "Selection Definition": "Considered to be present in patients with HF who have had a myocardial infarction (MI) or have evidence of viable hibernating myocardium or, on angiography, severe coronary disease.  The term ischemic cardiomyopathy has been used to describe significantly impaired left ventricular function (left ventricular ejection fraction <=35 to 40 percent) that results from coronary artery disease. Despite the common clinical use of the term ischemic cardiomyopathy, ventricular dysfunction caused by coronary disease is not a cardiomyopathy as defined by the 2006 American Heart Association and 2008 European Society of Cardiology statements.", "Display Order": 2}, {"Name": "Cardiomyopathy Type", "Code": "111000119104", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Non-ischemic", "Selection Definition": "Includes cardiomyopathies resulting from volume or pressure overload, such as hypertension or valvular heart disease.", "Display Order": 3}, {"Name": "Cardiomyopathy Type", "Code": "415295002", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Restrictive", "Selection Definition": "Non Hypertrophied, Non Dilated: A rare form of heart muscle disease and a cause of heart failure that is characterized by normal or decreased volume of both ventricles associated with biatrial enlargement, normal LV wall thickness and AV valves, impaired ventricular filling with restrictive physiology, and normal (or near normal) systolic function.", "Display Order": 4}, {"Name": "Cardiomyopathy Type", "Code": "100001065", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Other", "Selection Definition": "The term \"unclassified cardiomyopathy\"  was included in the 2008 ESC classification system to describe disorders that do not readily fit into any of the above phenotypic categories [3]. Examples cited include LV noncompaction and stress-induced (takotsubo) cardiomyopathy.", "Display Order": 5}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Cardiomyopathy"}, {"Parent Element Reference": 15510, "Parent Element Name": "Condition History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4585, "Name": "Sleep Apnea Recommended Treatment Followed", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate if the patient followed the sleep apnea treatment plan recommended.", "Target Value": "Any occurrence between birth and the procedure", "Short Name": "SleepApneaRxFollowed", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001098", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Sleep Apnea"}, {"Parent Element Reference": 15510, "Parent Element Name": "Condition History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4390, "Name": "Mechanical Valve in Mitral Position", "Section Display Name": "Condition History Details", "Coding Instructions": "Indicate if the patient has a mechanical valve placed in the mitral position. ", "Target Value": "Any occurrence between birth and the procedure", "Short Name": "MechValveMitPos", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "431339008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 12903, "Parent Element Name": "Condition History Name", "Parent Element Selection Name": "Valvular Atrial Fibrillation"}, {"Parent Element Reference": 15510, "Parent Element Name": "Condition History Occurrence", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "History and Risk Factors", "Section Display Name": "History and Risk Factors", "Section Code": "HISTORYANDRISK2", "Section Type": "Section", "Cardinality": "0..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 4400, "Name": "Atrial Fibrillation Classification", "Section Display Name": "History and Risk Factors", "Coding Instructions": "Indicate the type of atrial fibrillation experienced by the patient.\n\nNote: If more than one Atrial Fibrillation Classification is documented, use the most recent classification that prompted the current ablation.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "AFibClass", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000935", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Atrial Fibrillation Classification", "Code": "26593000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Paroxysmal", "Selection Definition": "AF that terminates spontaneously or with intervention within 7 days of onset. Episodes may recur with variable frequency. Classification 3A\n", "Display Order": 1}, {"Name": "Atrial Fibrillation Classification", "Code": "62459000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Persistent", "Selection Definition": "Continuous AF that is sustained >7 days or with electrical or pharmacological termination. Classification 3B\n", "Display Order": 2}, {"Name": "Atrial Fibrillation Classification", "Code": "100001029", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "LS - Persistent", "Selection Definition": "Continuous AF of >12 months duration. Classification 3C\n", "Display Order": 3}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 4455, "Name": "Atrial Flutter Classification", "Section Display Name": "History and Risk Factors", "Coding Instructions": "Indicate the presence of, as well as the predominant type of atrial flutter experienced by the patient. \n\nNote:\n- In the absence of physician documentation identifying the Aflutter Classification, please select ‘Typical / CTI dependent’.\n- If both Classifications are documented, please select ‘Typical / CTI dependent’", "Target Value": "Any occurrence between birth and the procedure", "Short Name": "AFlutterType", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000938", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Atrial Flutter Classification", "Code": "100013073", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "No", "Selection Definition": null, "Display Order": 1}, {"Name": "Atrial Flutter Classification", "Code": "100000982", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Yes - Typical/CTI Dependent", "Selection Definition": "Typical atrial flutter is a macro-re-entrant atrial tachycardia that usually proceeds up the atrial septum, down the lateral atrial wall, and through the cavotricuspid (subeustachian) isthmus between the tricuspid valve annulus and inferior vena cava, where it is commonly targeted for ablation.", "Display Order": 2}, {"Name": "Atrial Flutter Classification", "Code": "112231000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Yes - Atypical", "Selection Definition": "Atypical flutter, or \"noncavotricuspid isthmus-dependent macro-re-entrant atrial tachycardia\", describes macro-re-entrant atrial tachycardias that are not one of the typical forms of atrial flutter that use the cavotricuspid isthmus.", "Display Order": 3}], "Ranges": null, "Definition": {"Title": "Atrial Flutter Type", "Definition": "Atrial flutter is further classified into typical or atypical dependent on whether or not re-entry is dependent upon conduction through the cavotricuspid isthmus.", "Source": "January CT, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Cleveland Jr JC, Cigarroa JE, Conti JB, Ellinor PT, Ezekowitz MD, Field ME, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, 2014 AHA/ACC/HRS Guideline for the Management of Patients With Atrial Fibrillation, Journal of the American College of Cardiology (2014), doi: 10.1016/j.jacc.2014.03.022."}, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "History and Risk Factors", "Section Display Name": "Procedure History", "Section Code": "PROCHX", "Section Type": "Repeater Section", "Cardinality": "0..n", "Table": "PROCHX", "Elements": [{"Element Reference": 12905, "Name": "Procedure History Name", "Section Display Name": "Procedure History", "Coding Instructions": "The procedures listed in this field are controlled by the Procedure History Master file. This file is maintained by the NCDR and will be made available for downloading and importing/updating into your application.", "Target Value": "N/A", "Short Name": "ProcedHxName", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "416940007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Procedure History Name", "Code": "428663009+307280005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "AV Node ablation with Pacemaker Implantation", "Selection Definition": null, "Display Order": 1}, {"Name": "Procedure History Name", "Code": "112000002070", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Left Atrial Appendage Occlusion", "Selection Definition": null, "Display Order": 2}, {"Name": "Procedure History Name", "Code": "100000936", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Atrial Fibrillation Termination Attempt", "Selection Definition": "Therapeutic options for conversion of AF to sinus rhythm includes: antiarrhythmic drugs, direct current cardioversion, catheter ablation and surgical ablation.", "Display Order": 3}, {"Name": "Procedure History Name", "Code": "100000937", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Atrial Flutter Termination Attempt", "Selection Definition": "Therapeutic options for conversion of atrial flutter to sinus rhythm includes: antiarrhythmic drugs, direct current cardioversion, and catheter ablation.", "Display Order": 4}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 14268, "Name": "Procedure History Occurrence", "Section Display Name": "Procedure History", "Coding Instructions": "Indicate if the patient does or does not have a history of the indicated medical procedure.", "Target Value": "Any occurrence between birth and the first procedure in this admission", "Short Name": "ProcHxOccur", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "416940007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 12905, "Parent Element Name": "Procedure History Name", "Parent Element Selection Name": "Any Value"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "History and Risk Factors", "Section Display Name": "Procedure History Details", "Section Code": "PROCHXDET", "Section Type": "Section", "Cardinality": "0..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 4415, "Name": "Atrial Fibrillation Termination - Pharmacologic Cardioversion", "Section Display Name": "Procedure History Details", "Coding Instructions": "Indicate if the patient has a history of pharmacological cardioversion. \n\nThese elements will be coded with successful as well as unsuccessful attempts.", "Target Value": "Any occurrence between birth and the procedure", "Short Name": "PrevAFibTermPC", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "440142000:363702006=49436004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Pharmacologic Cardioversion", "Definition": "Antiarrhythmic drugs can be administered for attempted conversion of AF to sinus rhythm or to facilitate electrical cardioversion.", "Source": "January CT, <PERSON><PERSON>, <PERSON><PERSON>, et al. 2014 AHA/ACC/HRS Guideline for the Management of Patients With Atrial Fibrillation: A Report of the American College of Cardiology/American Heart Association Task Force on Practice Guidelines and the Heart Rhythm Society. J Am Coll Cardiol 2014. DOI: 10.1016/j.jacc.2014.03.022"}, "Parent Child Validations": [{"Parent Element Reference": 12905, "Parent Element Name": "Procedure History Name", "Parent Element Selection Name": "Atrial Fibrillation Termination Attempt"}, {"Parent Element Reference": 14268, "Parent Element Name": "Procedure History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4420, "Name": "Atrial Fibrillation Termination - DC Cardioversion", "Section Display Name": "Procedure History Details", "Coding Instructions": "Indicate if the patient has a history of direct current (DC) cardioversion.\n\nThese elements will be coded with successful as well as unsuccessful attempts", "Target Value": "Any occurrence between birth and the procedure", "Short Name": "PrevAFibTermDC", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "180325003:363702006=49436004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "DC Cardioversion", "Definition": "Direct-current cardioversion involves the delivery of an electrical shock synchronized with the QRS complex to avoid inducing ventricular fibrillation as can occur by a shock applied during ventricular repolarization on the T wave.", "Source": "January CT, <PERSON><PERSON>, <PERSON><PERSON>, et al. 2014 AHA/ACC/HRS Guideline for the Management of Patients With Atrial Fibrillation: A Report of the American College of Cardiology/American Heart Association Task Force on Practice Guidelines and the Heart Rhythm Society. J Am Coll Cardiol 2014. DOI: 10.1016/j.jacc.2014.03.022"}, "Parent Child Validations": [{"Parent Element Reference": 12905, "Parent Element Name": "Procedure History Name", "Parent Element Selection Name": "Atrial Fibrillation Termination Attempt"}, {"Parent Element Reference": 14268, "Parent Element Name": "Procedure History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4425, "Name": "Atrial Fibrillation Termination - Catheter Ablation", "Section Display Name": "Procedure History Details", "Coding Instructions": "Indicate if the patient has a history of catheter ablation for termination of atrial fibrillation.\n\nThese elements will be coded with successful as well as unsuccessful attempts.", "Target Value": "Any occurrence between birth and the procedure", "Short Name": "PrevAFibTermCA", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "18286008:363702006=49436004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Catheter Ablation", "Definition": "Various methods of catheter ablation are used. Currently most techniques focus on isolating the triggers in the pulmonary veins (PVs) from the vulnerable substrate in the left atrium. The majority of ablations performed use radiofrequency energy or cryothermy (cryoballoon ablation).", "Source": "January CT, <PERSON><PERSON>, <PERSON><PERSON>, et al. 2014 AHA/ACC/HRS Guideline for the Management of Patients With Atrial Fibrillation: A Report of the American College of Cardiology/American Heart Association Task Force on Practice Guidelines and the Heart Rhythm Society. J Am Coll Cardiol. 2014;64(21):e1-e76."}, "Parent Child Validations": [{"Parent Element Reference": 12905, "Parent Element Name": "Procedure History Name", "Parent Element Selection Name": "Atrial Fibrillation Termination Attempt"}, {"Parent Element Reference": 14268, "Parent Element Name": "Procedure History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4430, "Name": "Atrial Fibrillation Most Recent Catheter Ablation Date", "Section Display Name": "Procedure History Details", "Coding Instructions": "Indicate the date of the most recent attempt to terminate the atrial fibrillation via catheter ablation. \n\nNote(s):\nIf the month or day is unknown, please code 01/01/YYYY. If the specific year is unknown in the current record, the year may be estimated based on timeframes found in prior medical record documentation (Example: If the patient had \"most recent ablation\" documented in a record from 2011, then the year 2011 can be utilized and coded as 01/01/2011).", "Target Value": "Any occurrence between birth and the procedure", "Short Name": "AFibCathAblDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "18286008:363702006=49436004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4425, "Parent Element Name": "Atrial Fibrillation Termination - Catheter Ablation", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4435, "Name": "Prior Catheter Ablation Strategy", "Section Display Name": "Procedure History Details", "Coding Instructions": "Indicate the previously attempted catheter ablation strategy/strategies used to treat the atrial fibrillation.\n\nNote(s):\nThe strategies that should be collected in your application are controlled by Ablation Strategy Master file. This file is maintained by the NCDR and will be made available on the internet for downloading and importing/updating into your application.", "Target Value": "Any occurrence between birth and the procedure", "Short Name": "AFibPriorAblStrategyCode", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "Yes", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "18286008:363702006=49436004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Prior Catheter Ablation Strategy", "Code": "100000910", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Complex Fractionated Atrial Electrogram", "Selection Definition": "An ablation strategy targeting areas of continuous high-frequency (complex fractionated) atrial electrograms.", "Display Order": 1}, {"Name": "Prior Catheter Ablation Strategy", "Code": "100000911", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Convergent Procedure", "Selection Definition": "The convergent procedure consists of epicardial (Epi) followed by endocardial (Endo) radio-frequency ablation in patients (pts) with atrial fibrillation (AF), deemed at high risk of recurrence with endo ablation only.", "Display Order": 2}, {"Name": "Prior Catheter Ablation Strategy", "Code": "233161001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Cryoablation", "Selection Definition": "Cryoablation, or freezing technology, involves a coolant being released into the catheter's balloon to freeze and ablate the tissue.", "Display Order": 3}, {"Name": "Prior Catheter Ablation Strategy", "Code": "*********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Empiric LA Linear Lesions", "Selection Definition": "An ablation strategy that can include adjunctive linear lesions (such as a roof line or mitral annular line) that may accompany WACA, PVI, or other approaches, with a goal of preventing development of subsequent left atrial flutter.", "Display Order": 4}, {"Name": "Prior Catheter Ablation Strategy", "Code": "*********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Focal Ablation", "Selection Definition": "An ablation strategy targeting one or more foci of putative triggers of atrial fibrillation. Ablation may be of a trigger of AF or just of a focal atrial tachycardia that accompanies AF or emerges following previous AF therapies (i.e. is a stand-alone rhythm).", "Display Order": 5}, {"Name": "Prior Catheter Ablation Strategy", "Code": "*********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Ganglion Plexus Ablation", "Selection Definition": "An ablation strategy targeting one or more regions of autonomic nerve plexi around the left atrium.", "Display Order": 6}, {"Name": "Prior Catheter Ablation Strategy", "Code": "100000915", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Pulmonary Vein Isolation", "Selection Definition": "An ablation strategy defined as electrical disconnection of atrial myocardium extending into the pulmonary veins from the body of the left atrium.", "Display Order": 7}, {"Name": "Prior Catheter Ablation Strategy", "Code": "112000003642", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Pulsed Field Ablation", "Selection Definition": "Pulsed field ablation uses electrical pulses to cause nonthermal irreversible electroporation and induce cardiac cell death.\n", "Display Order": 8}, {"Name": "Prior Catheter Ablation Strategy", "Code": "100000917", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Rotor Based Mapping", "Selection Definition": "An ablation strategy guided by mapping software technology employed to identify specific atrial fibrillation rotors.", "Display Order": 9}, {"Name": "Prior Catheter Ablation Strategy", "Code": "100000916", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Segmental PV Ablation", "Selection Definition": "An ablation strategy with the goal of electrical isolation of pulmonary venous atrial tachycardia triggers from the body of the left atrium by ablating segmentally and/or circumferentially within a vein or near the venous ostium.", "Display Order": 10}, {"Name": "Prior Catheter Ablation Strategy", "Code": "100000918", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Wide Area Circumferential Ablation", "Selection Definition": "An ablation strategy that includes placement of large circumferential ablation lesion sets encircling the right and left venous antra with the goal of either substrate modification, isolation of the pulmonary veins, or both. This approach generally implies that formal testing for entrance block and/or exit block is NOT performed.", "Display Order": 11}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4425, "Parent Element Name": "Atrial Fibrillation Termination - Catheter Ablation", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4440, "Name": "Atrial Fibrillation Termination - Surgical Ablation", "Section Display Name": "Procedure History Details", "Coding Instructions": "Indicate if the patient has a history of surgical ablation. ", "Target Value": "Any occurrence between birth and the procedure", "Short Name": "PrevAFibTermSA", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "233163003:363702006=49436004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Surgical Ablation", "Definition": "The Maze operation is one surgical ablation option treat patients with both paroxysmal and chronic AF refractory to antiarrhythmic therapy.", "Source": "The surgical treatment of atrial fibrillation. IV. Surgical technique. Cox JL .  J <PERSON>. 1991;101(4):584."}, "Parent Child Validations": [{"Parent Element Reference": 12905, "Parent Element Name": "Procedure History Name", "Parent Element Selection Name": "Atrial Fibrillation Termination Attempt"}, {"Parent Element Reference": 14268, "Parent Element Name": "Procedure History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4445, "Name": "Atrial Fibrillation, Most Recent Surgical Ablation Date", "Section Display Name": "Procedure History Details", "Coding Instructions": "Indicate the date of the most recent attempt to terminate the atrial fibrillation via surgical ablation. \n\nNote(s):\nIf the month or day is unknown, please code 01/01/YYYY. If the specific year is unknown in the current record, the year may be estimated based on timeframes found in prior medical record documentation (Example: If the patient had \"most recent surgical ablation\" documented in a record from 2011, then the year 2011 can be utilized and coded as 01/01/2011).", "Target Value": "Any occurrence between birth and the procedure", "Short Name": "AFibSurgAblDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "233163003:363702006=49436004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4440, "Parent Element Name": "Atrial Fibrillation Termination - Surgical Ablation", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4465, "Name": "Atrial Flutter Termination - Pharmacologic Cardioversion", "Section Display Name": "Procedure History Details", "Coding Instructions": "Indicate if the patient has a history of pharmacologic cardioversion to terminate the atrial flutter.", "Target Value": "Any occurrence between birth and the procedure", "Short Name": "PrevAFLTermPC", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "440142000:363702006=5370000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Pharmacologic Cardioversion", "Definition": "Antiarrhythmic drugs can be administered for attempted conversion of AF to sinus rhythm or to facilitate electrical cardioversion.", "Source": "January CT, <PERSON><PERSON>, <PERSON><PERSON>, et al. 2014 AHA/ACC/HRS Guideline for the Management of Patients With Atrial Fibrillation: A Report of the American College of Cardiology/American Heart Association Task Force on Practice Guidelines and the Heart Rhythm Society. J Am Coll Cardiol 2014. DOI: 10.1016/j.jacc.2014.03.022"}, "Parent Child Validations": [{"Parent Element Reference": 12905, "Parent Element Name": "Procedure History Name", "Parent Element Selection Name": "Atrial Flutter Termination Attempt"}, {"Parent Element Reference": 14268, "Parent Element Name": "Procedure History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4470, "Name": "Atrial Flutter Termination - DC Cardioversion", "Section Display Name": "Procedure History Details", "Coding Instructions": "Indicate if the patient has a history of DC cardioversion to terminate the atrial flutter.", "Target Value": "Any occurrence between birth and the procedure", "Short Name": "PrevAFLTermDC", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "180325003:363702006=5370000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "DC Cardioversion", "Definition": "Direct-current cardioversion involves the delivery of an electrical shock synchronized with the QRS complex to avoid inducing ventricular fibrillation as can occur by a shock applied during ventricular repolarization on the T wave. ", "Source": "January CT, <PERSON><PERSON>, <PERSON><PERSON>, et al. 2014 AHA/ACC/HRS Guideline for the Management of Patients With Atrial Fibrillation: A Report of the American College of Cardiology/American Heart Association Task Force on Practice Guidelines and the Heart Rhythm Society. J Am Coll Cardiol 2014. DOI: 10.1016/j.jacc.2014.03.022"}, "Parent Child Validations": [{"Parent Element Reference": 12905, "Parent Element Name": "Procedure History Name", "Parent Element Selection Name": "Atrial Flutter Termination Attempt"}, {"Parent Element Reference": 14268, "Parent Element Name": "Procedure History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4475, "Name": "Atrial Flutter Termination - Catheter Ablation", "Section Display Name": "Procedure History Details", "Coding Instructions": "Indicate if the patient has a history of catheter ablation to terminate the atrial flutter.", "Target Value": "Any occurrence between birth and the procedure", "Short Name": "PrevAFLTermCA", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "18286008:363702006=5370000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 12905, "Parent Element Name": "Procedure History Name", "Parent Element Selection Name": "Atrial Flutter Termination Attempt"}, {"Parent Element Reference": 14268, "Parent Element Name": "Procedure History Occurrence", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 4480, "Name": "Atrial Flutter Most Recent Catheter Ablation Date", "Section Display Name": "Procedure History Details", "Coding Instructions": "Indicate the date of the most recent catheter ablation.\n\nNote(s):\nIf the month or day is unknown, please code 01/01/YYYY. If the specific year is unknown in the current record, the year may be estimated based on timeframes found in prior medical record documentation (Example: If the patient had \"most recent ablation\" documented in a record from 2011, then the year 2011 can be utilized and coded as 01/01/2011).", "Target Value": "Any occurrence between birth and the procedure", "Short Name": "AFibFlutterCathAblDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "18286008:363702006=5370000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 4475, "Parent Element Name": "Atrial Flutter Termination - Catheter Ablation", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "Diagnostic Studies", "Section Code": "DIAGSTUDIES", "Section Type": "Section", "Cardinality": "0..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 5100, "Name": "Atrial Rhythm", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate the patient's atrial rhythm at the start of the procedure.\n\nNote(s):  If the patient has multiple atrial rhythms, select all that apply.  In the event that a patient is ventricular paced, indicate the underlying atrial rhythm.  Target value applies to the first procedure captured for this registry.  If no ECG is available, a pre-procedure 6 inch cardiac rhythm strip or clinician documentation may be utilized to obtain this information.", "Target Value": "The last value within 90 days of procedure start", "Short Name": "AtrialRhythm", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "106068003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Atrial Rhythm", "Code": "49436004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Atrial fibrillation", "Selection Definition": null, "Display Order": 1}, {"Name": "Atrial Rhythm", "Code": "5370000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Atrial flutter", "Selection Definition": null, "Display Order": 2}, {"Name": "Atrial Rhythm", "Code": "251268003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Atrial paced", "Selection Definition": null, "Display Order": 3}, {"Name": "Atrial Rhythm", "Code": "276796006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Atrial tachycardia", "Selection Definition": null, "Display Order": 4}, {"Name": "Atrial Rhythm", "Code": "106067008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Sinus", "Selection Definition": null, "Display Order": 5}, {"Name": "Atrial Rhythm", "Code": "5609005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Sinus arrest", "Selection Definition": null, "Display Order": 6}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 5110, "Name": "LVEF Assessed", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate if a left ejection fraction percentage has been assessed.\n\nNote(s):\nIf the LVEF is measured during the episode of care, and documented in the medical record, it\ncan be used. There is no requirement for the LVEF to be documented in a formal diagnostic\nreport.\nLVEF values obtained prior to first medical contact are not used for coding.\nEnter a percentage in the range of 1-99.\nIf a percentage range is reported, code the lowest number in the range (i.e. 50-55%, is\nreported as 50%).\nIn cases of conflicting measurements, the clinician should specify which value best represents\nthe LVEF closest to discharge and this should be noted in the medical record to support\ncoding.\nIf only a descriptive value is reported (i.e. normal), enter the corresponding percentage value\nfrom the list below:\nNormal = 60%\nGood function = 50%\nMildly reduced = 45%\nFair function = 40%\nModerately reduced = 30%\nPoor function = 25%\nSeverely reduced = 20%", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "LVEFAssessed", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001027", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 5115, "Name": "Most Recent LVEF %", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate the most recent left ventricular ejection fraction.  \n\nNote(s):\nEnter a percentage in the range of 01 - 99.  If a percentage range was reported, report the lowest number of the range (i.e. 50 - 55%, is reported as 50%).", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "LVEF", "Data Type": "PQ", "Precision": "2,0", "Unit Of Measure": "%", "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "10230-1", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "%", "Usual Range Min": "5", "Usual Range Max": "70", "Valid Range Min": "1", "Valid Range Max": "99"}], "Definition": {"Title": "Most Recent LVEF %", "Definition": "The left ventricular ejection fraction is the percentage of blood emptied from the left ventricle at the end of contraction.", "Source": "ACC Clinical Data Standards, Society for Thoracic Surgeons Adult Cardiac Surgery Database (STS)"}, "Parent Child Validations": [{"Parent Element Reference": 5110, "Parent Element Name": "LVEF Assessed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 5120, "Name": "Transthoracic Echo (TTE) Performed", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate if a transthoracic echocardiogram (TTE) was performed prior to the procedure.", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "TTEPerf", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "433236007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 5125, "Name": "Most Recent TTE Date", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate the date of the most recent transthoracic echocardiogram (TTE) performed and used to evaluate the patient for this intervention.", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "TTEDate", "Data Type": "DT", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "433236007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 5120, "Parent Element Name": "Transthoracic Echo (TTE) Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 15707, "Name": "Echocardiogram Results", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate the echocardiography results that were present during the most recent transthoracic echocardiogram.\n\nNotes: Include any enlargement or hypertrophy of the heart as well as the severity.\n\nEnter \"none\" if there was no hypertrophy identified.\n", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "EchocardiogramResults", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": null, "Is Dynamic List": "Yes", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "40701008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Echocardiogram Results", "Code": "396339007:123005000=59652004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Atrial thrombus detected", "Selection Definition": null, "Display Order": 1}, {"Name": "Echocardiogram Results", "Code": "100001231", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "LV hypertrophy - none", "Selection Definition": null, "Display Order": 2}, {"Name": "Echocardiogram Results", "Code": "255604002", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Mild LV hypertrophy", "Selection Definition": null, "Display Order": 3}, {"Name": "Echocardiogram Results", "Code": "6736007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Moderate LV hypertrophy", "Selection Definition": null, "Display Order": 4}, {"Name": "Echocardiogram Results", "Code": "24484000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Severe LV hypertrophy", "Selection Definition": null, "Display Order": 5}, {"Name": "Echocardiogram Results", "Code": "253352002:116676008=442021009,17621005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "LA Not enlarged", "Selection Definition": null, "Display Order": 6}, {"Name": "Echocardiogram Results", "Code": "253352002:116676008=442021009,255604002", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Mild LA Enlargement", "Selection Definition": null, "Display Order": 7}, {"Name": "Echocardiogram Results", "Code": "253352002:116676008=442021009,6736007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Moderate LA enlargement", "Selection Definition": null, "Display Order": 8}, {"Name": "Echocardiogram Results", "Code": "253352002:116676008=442021009,24484000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Severe LA enlargement", "Selection Definition": null, "Display Order": 9}, {"Name": "Echocardiogram Results", "Code": "253339007:116676008=442021009,17621005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "RA Not enlarged", "Selection Definition": null, "Display Order": 10}, {"Name": "Echocardiogram Results", "Code": "253339007:116676008=442021009,255604002", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Mild RA Enlargement", "Selection Definition": null, "Display Order": 11}, {"Name": "Echocardiogram Results", "Code": "253339007:116676008=442021009,6736007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Moderate RA enlargement", "Selection Definition": null, "Display Order": 12}, {"Name": "Echocardiogram Results", "Code": "253339007:116676008=442021009,24484000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Severe RA Enlargement", "Selection Definition": null, "Display Order": 13}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 5120, "Parent Element Name": "Transthoracic Echo (TTE) Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 5150, "Name": "Mitral Stenosis", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate if the patient has mitral valve stenosis.", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "MitralStenosis", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "79619009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 5145, "Name": "Mitral Regurgitation", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate  the severity of regurgitation through the mitral valve.\n\nNote(s):\nCode the highest value or most severe regurgitation when a range is reported.", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "MitralRegurg", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "48724000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Mitral Regurgitation", "Code": "100001231", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "None", "Selection Definition": null, "Display Order": 1}, {"Name": "Mitral Regurgitation", "Code": "100001111", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Trace/Trivial", "Selection Definition": null, "Display Order": 2}, {"Name": "Mitral Regurgitation", "Code": "255604002", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Mild", "Selection Definition": null, "Display Order": 3}, {"Name": "Mitral Regurgitation", "Code": "6736007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Moderate", "Selection Definition": null, "Display Order": 4}, {"Name": "Mitral Regurgitation", "Code": "100001045", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Moderate-Severe", "Selection Definition": null, "Display Order": 5}, {"Name": "Mitral Regurgitation", "Code": "24484000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Severe", "Selection Definition": null, "Display Order": 6}], "Ranges": null, "Definition": {"Title": "Mitral Regurgitation", "Definition": "The approach to the evaluation of mitral regurgitation (aka. Mitral insufficiency) severity ideally integrates multiple parameters rather than depends on a single measurement.", "Source": "Recommendations for Evaluation of the Severity of Native Valvular Regurgitation with Two-dimensional and Doppler Echocardiography: J Am <PERSON> Echocardiogr 2003;16:777-802"}, "Parent Child Validations": null}, {"Element Reference": 5170, "Name": "Baseline Imaging Performed", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate if pre-procedure imaging was performed.", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "BaselineImagingPerf", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "363679005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 5175, "Name": "Baseline CT Performed", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate if pre-procedure imaging was performed via CT.", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "CTPerformed", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "58744-4", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 5170, "Parent Element Name": "Baseline Imaging Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 5185, "Name": "Baseline MRI Performed", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate if pre-procedure imaging was performed via MRI.", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "MRPerformed", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "36482-8", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 5170, "Parent Element Name": "Baseline Imaging Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 5155, "Name": "Transesophageal Echocardiogram (TEE) Performed", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate if transesophageal echocardiogram (TEE) was performed prior to the procedure.", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "TEEPerf", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "105376000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 5170, "Parent Element Name": "Baseline Imaging Performed", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 5165, "Name": "Atrial Thrombus Detected", "Section Display Name": "Diagnostic Studies", "Coding Instructions": "Indicate if an atrial thrombus was detected.\n\nNote(s):\nCode 'Yes' for either probable or definitive diagnoses of thrombus.", "Target Value": "The last value between 90 days prior to the start of the current procedure and the start of procedure", "Short Name": "AtrialThromDetect", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "396339007:123005000=59652004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Atrial Thrombus Detected", "Definition": "Presence of thrombus is considered to be definite if 3 of the following 5 criteria are present: Clear borders, echogenicity from the surrounding structures, independent mobility, longest diameter > 15mm, seen in more than one echocardiographic plane.", "Source": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. ACC/AHA/HRS 2006 Key Data Elements and Definitions for Electrophysiological Studies and Procedures: A Report of the American College of Cardiology/American Heart Association Task Force on Clinical Data Standards (ACC/AHA/HRS Writing Committee to Develop Data Standards on Electrophysiology). J Am Coll Cardiol. 2006;48(11):2360-2396."}, "Parent Child Validations": [{"Parent Element Reference": 5155, "Parent Element Name": "Transesophageal Echocardiogram (TEE) Performed", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "Pre-Procedure Medications", "Section Code": "PREPROCMEDS", "Section Type": "Repeater Section", "Cardinality": "0..n", "Table": "PREPROCMEDS", "Elements": [{"Element Reference": 6985, "Name": "Pre-procedure Medication Code", "Section Display Name": "Pre-Procedure Medications", "Coding Instructions": "Indicate the prescribing history and administration status (past, current, held, never) of each medication.  \n\nNote(s): The medications that should be collected in your application are controlled by a Medication Master file. This file is maintained by the NCDR and will be made available on the internet for downloading and importing/updating into your application. Each medication in the Medication Master file is assigned a timing indicator. This indicator is used to separate procedural medications from medications prescribed at discharge. The separation of these medications is depicted on the data collection form.", "Target Value": "The value between 24 hours prior to the start of current procedure and end of current procedure", "Short Name": "MedID", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "Yes", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100013057", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Pre-procedure Medication Code (6985) should not be duplicated in an episode", "Selections": [{"Name": "Pre-procedure Medication Code", "Code": "703", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Amiodarone", "Selection Definition": null, "Display Order": 1}, {"Name": "Pre-procedure Medication Code", "Code": "41549009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Angiotensin converting enzyme inhibitor (ACE-I) (Any)", "Selection Definition": null, "Display Order": 2}, {"Name": "Pre-procedure Medication Code", "Code": "372913009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Angiotensin receptor blocker (ARB) (Any)", "Selection Definition": null, "Display Order": 3}, {"Name": "Pre-procedure Medication Code", "Code": "1656341", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Angiotensin II receptor blocker neprilysin inhibitor (ARNI)", "Selection Definition": null, "Display Order": 4}, {"Name": "Pre-procedure Medication Code", "Code": "1364430", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Apixaban", "Selection Definition": null, "Display Order": 5}, {"Name": "Pre-procedure Medication Code", "Code": "1191", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "<PERSON><PERSON><PERSON>", "Selection Definition": null, "Display Order": 6}, {"Name": "Pre-procedure Medication Code", "Code": "226718", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Aspirin, Extended-Release Dipyridamole", "Selection Definition": null, "Display Order": 7}, {"Name": "Pre-procedure Medication Code", "Code": "33252009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Beta blocker (Any)", "Selection Definition": null, "Display Order": 8}, {"Name": "Pre-procedure Medication Code", "Code": "1927851", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Betrixaban", "Selection Definition": null, "Display Order": 9}, {"Name": "Pre-procedure Medication Code", "Code": "1656052", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "<PERSON><PERSON><PERSON><PERSON>", "Selection Definition": null, "Display Order": 10}, {"Name": "Pre-procedure Medication Code", "Code": "32968", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Clopidogrel", "Selection Definition": null, "Display Order": 11}, {"Name": "Pre-procedure Medication Code", "Code": "1546356", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Dabigatran", "Selection Definition": null, "Display Order": 12}, {"Name": "Pre-procedure Medication Code", "Code": "3407", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Digoxin", "Selection Definition": null, "Display Order": 13}, {"Name": "Pre-procedure Medication Code", "Code": "3443", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Diltiazem", "Selection Definition": null, "Display Order": 14}, {"Name": "Pre-procedure Medication Code", "Code": "3541", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Disopyramide", "Selection Definition": null, "Display Order": 15}, {"Name": "Pre-procedure Medication Code", "Code": "49247", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Dofetilide", "Selection Definition": null, "Display Order": 16}, {"Name": "Pre-procedure Medication Code", "Code": "233698", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Dr<PERSON><PERSON><PERSON>", "Selection Definition": null, "Display Order": 17}, {"Name": "Pre-procedure Medication Code", "Code": "1599538", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Edoxaban", "Selection Definition": null, "Display Order": 18}, {"Name": "Pre-procedure Medication Code", "Code": "4441", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Flecainide", "Selection Definition": null, "Display Order": 19}, {"Name": "Pre-procedure Medication Code", "Code": "772985004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "GLP-1 agonist", "Selection Definition": null, "Display Order": 20}, {"Name": "Pre-procedure Medication Code", "Code": "100000921", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Heparin Derivative", "Selection Definition": null, "Display Order": 21}, {"Name": "Pre-procedure Medication Code", "Code": "373294004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Low Molecular Weight Heparin", "Selection Definition": null, "Display Order": 22}, {"Name": "Pre-procedure Medication Code", "Code": "613391", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Prasug<PERSON>", "Selection Definition": null, "Display Order": 23}, {"Name": "Pre-procedure Medication Code", "Code": "8700", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Procainamide", "Selection Definition": null, "Display Order": 24}, {"Name": "Pre-procedure Medication Code", "Code": "8754", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Propafenone", "Selection Definition": null, "Display Order": 25}, {"Name": "Pre-procedure Medication Code", "Code": "9068", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Quinidine", "Selection Definition": null, "Display Order": 26}, {"Name": "Pre-procedure Medication Code", "Code": "1114195", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Rivaroxaban", "Selection Definition": null, "Display Order": 27}, {"Name": "Pre-procedure Medication Code", "Code": "112000003634", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "SGLT inhibitor", "Selection Definition": null, "Display Order": 28}, {"Name": "Pre-procedure Medication Code", "Code": "9947", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Sotalol", "Selection Definition": null, "Display Order": 29}, {"Name": "Pre-procedure Medication Code", "Code": "1116632", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Ticagrelor", "Selection Definition": null, "Display Order": 30}, {"Name": "Pre-procedure Medication Code", "Code": "10594", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Ticlopidine", "Selection Definition": null, "Display Order": 31}, {"Name": "Pre-procedure Medication Code", "Code": "96382006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Unfractionated Heparin", "Selection Definition": null, "Display Order": 32}, {"Name": "Pre-procedure Medication Code", "Code": "11170", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Verapamil", "Selection Definition": null, "Display Order": 33}, {"Name": "Pre-procedure Medication Code", "Code": "1537034", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Vorapaxar", "Selection Definition": null, "Display Order": 34}, {"Name": "Pre-procedure Medication Code", "Code": "11289", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Warfarin", "Selection Definition": null, "Display Order": 35}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 6990, "Name": "Pre-procedure Medication Administered", "Section Display Name": "Pre-Procedure Medications", "Coding Instructions": "Indicate the prescribing history and administration status (past, current, held, never) of each medication.", "Target Value": "The value between 24 hours prior to the start of current procedure and end of current procedure", "Short Name": "MedAdmin", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "432102000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "When Pre-procedure Medication Code (6985) is answered, Pre-procedure Medication Administered (6990) cannot be Null.", "Selections": [{"Name": "Pre-procedure Medication Administered", "Code": "100001070", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Past", "Selection Definition": "Code 'Past' if the medication was tried previously prior this hospital visit, and then discontinued with no intent to resume the medication after recovering from the ablation procedure.", "Display Order": 1}, {"Name": "Pre-procedure Medication Administered", "Code": "100000987", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Current", "Selection Definition": "Code 'Current' if the patient is taking this medication prior to the procedure and the medication has neither been held nor stopped for the procedure or if the patient has an active or a new prescription and is taking this medication.", "Display Order": 2}, {"Name": "Pre-procedure Medication Administered", "Code": "100001010", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Held", "Selection Definition": "Code 'Held' if  the patient is routinely taking this medication, yet the medication was temporarily not administered prior to the procedure with an intent to resume the medication after recovering from the procedure.", "Display Order": 3}, {"Name": "Pre-procedure Medication Administered", "Code": "100001046", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Never", "Selection Definition": "Code 'Never' if this medication was never prescribed for this patient.", "Display Order": 4}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 6985, "Parent Element Name": "Pre-procedure Medication Code", "Parent Element Selection Name": "Any Value"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "Procedure Information", "Section Code": "PROCINFO", "Section Type": "Repeater Section", "Cardinality": "1..n", "Table": "PROCINFO", "Elements": [{"Element Reference": 7000, "Name": "Procedure Start Date and Time", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate the date and time the procedure started. The time of the procedure is the time that the skin incision, vascular access, or its equivalent, was made in order to start the procedure.\n\nNote(s):\nIndicate the date/time (mm/dd/yyyy hours:minutes) using the military 24-hour clock, beginning at midnight (0000 hours).", "Target Value": "Any occurrence on current procedure", "Short Name": "ProcedureStartDateTime", "Data Type": "TS", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "1000142460", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Procedure Start Date and Time (7000) must be Greater than or Equal to Arrival Date and Time (3001)\n\nProcedure Start Date and Time (7000) must be Greater than or Equal to Most Recent TTE Date (5125)\n\nProcedure Start Date and Time (7000) must be unique within an episode of care", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7025, "Name": "Procedure Status", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate the status of the procedure.", "Target Value": "The value on current procedure", "Short Name": "ProcStatus", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001218", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Procedure Status", "Code": "416800000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Inpatient", "Selection Definition": "Treatment/Billing status of the patient: Patient has been admitted to the hospital.", "Display Order": 1}, {"Name": "Procedure Status", "Code": "373864002", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Outpatient", "Selection Definition": "Patient/Billing status:  Patient is an outpatient admission.", "Display Order": 2}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7005, "Name": "Procedure End Date and Time", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate the ending date and time at which the operator completes the procedure and breaks scrub at the end of the procedure.\n\nNote(s):\nIf more than one operator is involved in the case then use the date and time the last operator breaks scrub for the last time.", "Target Value": "The value on current procedure", "Short Name": "ProcedureEndDateTime", "Data Type": "TS", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Procedure End Date and Time (7005) and Procedure Start Date and Time (7000) must not overlap on multiple procedures\n\nProcedure End Date and Time (7005) must be Greater than Procedure Start Date and Time (7000)\n\nProcedure End Date and Time (7005) must be Less than or Equal to Discharge Date and Time (10101)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7100, "Name": "Operator Last Name", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate the last name of operator.\n\nNote(s):\nIf the name exceeds 50 characters, enter the first 50 characters only.", "Target Value": "The value on current procedure", "Short Name": "OperA_LastName", "Data Type": "LN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001853", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7105, "Name": "Operator First Name", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate the first name of operator.\n\nNote(s):\nIf the name exceeds 50 characters, enter the first 50 characters only.", "Target Value": "The value on current procedure", "Short Name": "OperA_FirstName", "Data Type": "FN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001853", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7110, "Name": "Operator Middle Name", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate the middle name of operator.\n\nNote(s):\nIt is acceptable to specify the middle initial.\n\nIf there is no middle name given, leave field blank.\n\nIf there are multiple middle names, enter all of the middle names sequentially.\n\nIf the name exceeds 50 characters, enter the first 50 letters only.", "Target Value": "The value on current procedure", "Short Name": "OperA_MidName", "Data Type": "MN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001853", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7115, "Name": "Operator NPI", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate the National Provider Identifier (NPI) of the operator who is performing the procedure. NPI's, assigned by the Centers for Medicare and Medicaid Services (CMS), are used to uniquely identify physicians for Medicare billing purposes.", "Target Value": "The value on current procedure", "Short Name": "OperA_NPI", "Data Type": "NUM", "Precision": "10", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.4.6", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 15433, "Name": "Fellow Last Name", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate the last name of the Fellow-in-Training operator.\n\nNote(s):\nIf the name exceeds 50 characters, enter the first 50 characters only.", "Target Value": "The value on current procedure", "Short Name": "FIT_LastName", "Data Type": "LN", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "No Action", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000003534", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 15434, "Name": "Fellow First Name", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate the first name of the Fellow-in-Training operator.\n\nNote(s):\nIf the name exceeds 50 characters, enter the first 50 characters only.", "Target Value": "The value on current procedure", "Short Name": "FIT_FirstName", "Data Type": "FN", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "No Action", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000003534", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 15435, "Name": "Fellow Middle Name", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate the middle name of the Fellow-in-Training operator.\n\nNote(s):\nIf the name exceeds 50 characters, enter the first 50 characters only.", "Target Value": "The value on current procedure", "Short Name": "FIT_MidName", "Data Type": "MN", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "No Action", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000003534", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 15436, "Name": "Fellow NPI", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate the National Provider Identifier (NPI) of the Fellow-in-Training operator who is performing the procedure. NPI's, assigned by the Centers for Medicare and Medicaid Services (CMS), are used to uniquely identify physicians for Medicare billing purposes.", "Target Value": "The value on current procedure", "Short Name": "FIT_NPI", "Data Type": "NUM", "Precision": "10", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "No Action", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000003534", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 15431, "Name": "Fellowship Program Identification Number", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate the institution's Accreditation Council for Graduate Medical Education (ACGME) number for the program in which the Fellow is participating.", "Target Value": "The value on current procedure", "Short Name": "FITProgID", "Data Type": "ST", "Precision": "15", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "No Action", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "224873004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Fellowship Program Identification Number", "Definition": "The institution's Accreditation Council for Graduate Medical Education (ACGME) number for the program in which the Fellow is participating.\n\nACGME oversees the accreditation of fellowship programs in the US. Each accredited training program is assigned a program ID.", "Source": "A list of programs by specialty can be found here: ACGME - Accreditation Data System (ADS): https://apps.acgme.org/ads/Public/Reports/Report/1 ."}, "Parent Child Validations": [{"Parent Element Reference": 15436, "Parent Element Name": "Fellow NPI", "Parent Element Selection Name": "Any Value"}]}, {"Element Reference": 7130, "Name": "Sedation", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate the type of sedation used for the intervention.", "Target Value": "The value on current procedure", "Short Name": "Anesthesia", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "72641008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Sedation", "Code": "427255001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Minimal Sedation/Anxiolysis", "Selection Definition": null, "Display Order": 1}, {"Name": "Sedation", "Code": "314271007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Moderate Sedation/Analgesia", "Selection Definition": null, "Display Order": 2}, {"Name": "Sedation", "Code": "426155000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Deep Sedation/Analgesia", "Selection Definition": null, "Display Order": 3}, {"Name": "Sedation", "Code": "420653000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "General Anesthesia", "Selection Definition": null, "Display Order": 4}], "Ranges": null, "Definition": {"Title": "Sedation", "Definition": "1. Minimal Sedation (Anxiolysis) is a drug-induced state during which patients respond normally to verbal commands. Although cognitive function and physical coordination may be impaired, airway reflexes, and ventilatory and cardiovascular functions are unaffected.\n\n2. Moderate Sedation/Analgesia (\"Conscious Sedation\") is a drug-induced depression of consciousness during which patients respond purposefully to verbal commands, either alone or accompanied by light tactile stimulation. No interventions are required to maintain a patent airway, and spontaneous ventilation is adequate. Cardiovascular function is usually maintained. Reflex withdrawal from a painful stimulus is NOT considered a purposeful response.\n\n3. Deep Sedation/Analgesia is a drug-induced depression of consciousness during which patients cannot be easily aroused but respond purposefully following repeated or painful stimulation. The ability to independently maintain ventilatory function may be impaired. Patients may require assistance in maintaining a patent airway, and spontaneous ventilation may be inadequate. Cardiovascular function is usually maintained.\n\n4. General Anesthesia is a drug-induced loss of consciousness during which patients are not arousable, even by painful stimulation. The ability to independently maintain ventilatory function is often impaired. Patients often require assistance in maintaining a patent airway, and positive pressure ventilation may be required because of depressed spontaneous ventilation or drug-induced depression of neuromuscular function. Cardiovascular function may be impaired.", "Source": "Committee on Quality Management and Departmental Administration. \"Statement on Continuum of Depth of Sedation: Definition of General Anesthesia and Levels of Sedation/Analgesia.\" Last Amended: October 23, 2019 (original approval: October 13, 1999). American Society of Anesthesiologists. \"Position on Monitored Anesthesia Care.\" Last amended on October 17, 2018.\n\nhttps://www.asahq.org/standards-and-practice-parameters/statement-on-continuum-of-depth-of-sedation-definition-of-general-anesthesia-and-levels-of-sedation-analgesia"}, "Parent Child Validations": null}, {"Element Reference": 7175, "Name": "Transseptal Catheterization", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate if the procedure was performed with a single or a double transseptal catheterization.", "Target Value": "The value on current procedure", "Short Name": "TransseptCath", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001112", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Transseptal Catheterization", "Code": "50607009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Single", "Selection Definition": null, "Display Order": 1}, {"Name": "Transseptal Catheterization", "Code": "1305003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Double", "Selection Definition": "Double may include either a single-puncture and double wiring of the transseptal catheterization technique  or a second transseptal puncture for catheter access.", "Display Order": 2}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 15726, "Name": "Intracardiac Echocardiography", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate if imaging was performed via intracardiac echo (ICE).", "Target Value": "The value on current procedure", "Short Name": "PreProcICEPerf", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "448761005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Intracardiac Echocardiography", "Code": "100013073", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "No", "Selection Definition": null, "Display Order": 1}, {"Name": "Intracardiac Echocardiography", "Code": "112000003651", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Yes - 2D", "Selection Definition": null, "Display Order": 2}, {"Name": "Intracardiac Echocardiography", "Code": "448761005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Yes - 3D", "Selection Definition": null, "Display Order": 3}, {"Name": "Intracardiac Echocardiography", "Code": "112000003652", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Yes - 4D", "Selection Definition": null, "Display Order": 4}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 15714, "Name": "Pulmonary Vein Isolation", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate if a pulmonary vein isolation was performed during this procedure. \n", "Target Value": "The value on current procedure", "Short Name": "PVI", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001854", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 15722, "Name": "Pulmonary Vein Isolation Energy Source", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate the energy source used during the pulmonary vein isolation. ", "Target Value": "The value on current procedure", "Short Name": "PVIEnergySource", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": null, "Is Dynamic List": "Yes", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000915", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Pulmonary Vein Isolation Energy Source", "Code": "112000003639", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Cryoenergy", "Selection Definition": "Cryoablation, or freezing technology, involves a coolant being released into the catheter’s balloon to freeze and ablate the tissue.\n", "Display Order": 1}, {"Name": "Pulmonary Vein Isolation Energy Source", "Code": "112000003640", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Ethanol", "Selection Definition": "Ethanol infusion used during catheter ablation \n", "Display Order": 2}, {"Name": "Pulmonary Vein Isolation Energy Source", "Code": "112000003641", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Laser", "Selection Definition": "The laser balloon catheter comprises an inflatable balloon mounted on a catheter shaft, an endoscope lumen, and an optical fiber that can deliver laser energy\n", "Display Order": 3}, {"Name": "Pulmonary Vein Isolation Energy Source", "Code": "112000003642", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Pulsed Field Ablation", "Selection Definition": "Pulsed field ablation uses electrical pulses to cause nonthermal irreversible electroporation and induce cardiac cell death.\n", "Display Order": 4}, {"Name": "Pulmonary Vein Isolation Energy Source", "Code": "112000003643", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Radiofrequency", "Selection Definition": "Radiofrequency uses heat energy generated by radiofrequency waves to create lesions or scars on the heart tissue, disrupting abnormal electrical signals.\n", "Display Order": 5}, {"Name": "Pulmonary Vein Isolation Energy Source", "Code": "112000003644", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Other", "Selection Definition": "Any energy used during the procedure that is not listed\n", "Display Order": 6}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15714, "Parent Element Name": "Pulmonary Vein Isolation", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Procedure Information", "Section Display Name": "Adjunctive Ablation Lesions", "Section Code": "ABLLESIONS", "Section Type": "Section", "Cardinality": "0..1", "Table": "PROCINFO", "Elements": [{"Element Reference": 7165, "Name": "Adjunctive Ablation Lesions", "Section Display Name": "Adjunctive Ablation Lesions", "Coding Instructions": "Indicate whether additional lesions were created during the current ablation procedure, regardless of the arrythmia being treated with the additional lesions.\n\nIntent: This element is intended to identify what additional targeted areas are ablated beyond the primary pulmonary vein isolation (PVI). Creating additional lesions are intended to enhance the success of the procedure by addressing other potential sources of arrythmia. Additional lesions may also be associated with longer procedure time and more opportunity for complications to occur.", "Target Value": "The value on current procedure", "Short Name": "AblLesion", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000926", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Adjunctive Ablation Lesions", "Definition": "Additional locations treated with ablation to increase the efficacy or safety of the primary procedure.", "Source": "NCDR"}, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Adjunctive Ablation Lesions", "Section Display Name": "Ablation Location", "Section Code": "ABLLOC", "Section Type": "Repeater Section", "Cardinality": "0..n", "Table": "ABLLOC", "Elements": [{"Element Reference": 15725, "Name": "Adjunctive Ablation Location", "Section Display Name": "Ablation Location", "Coding Instructions": "Indicate the location targeted for ablation during this procedure.\n\nNote(s):\nIf the patient has multiple locations select all location targeted for ablation.", "Target Value": "The value on current procedure", "Short Name": "AblLesionLocSingSel", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001854", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Adjunctive Ablation Location", "Code": "48345005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "SVC isolation", "Selection Definition": null, "Display Order": 1}, {"Name": "Adjunctive Ablation Location", "Code": "90219004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Coronary sinus isolation", "Selection Definition": null, "Display Order": 2}, {"Name": "Adjunctive Ablation Location", "Code": "100000981", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Cavotricuspid isthmus (CTI)", "Selection Definition": null, "Display Order": 3}, {"Name": "Adjunctive Ablation Location", "Code": "5208200", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Ligament/vein of marshall", "Selection Definition": null, "Display Order": 4}, {"Name": "Adjunctive Ablation Location", "Code": "112000003647", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "LA roof line", "Selection Definition": null, "Display Order": 5}, {"Name": "Adjunctive Ablation Location", "Code": "112000002380", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Left auricular appendage", "Selection Definition": null, "Display Order": 6}, {"Name": "Adjunctive Ablation Location", "Code": "112000003648", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "LA floor line", "Selection Definition": null, "Display Order": 7}, {"Name": "Adjunctive Ablation Location", "Code": "112000003650", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Mitral isthmus line", "Selection Definition": null, "Display Order": 8}, {"Name": "Adjunctive Ablation Location", "Code": "112000003649", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Posterior wall isolation", "Selection Definition": null, "Display Order": 9}, {"Name": "Adjunctive Ablation Location", "Code": "100001063", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Other", "Selection Definition": null, "Display Order": 10}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7165, "Parent Element Name": "Adjunctive Ablation Lesions", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 15708, "Name": "Adjuctive Ablation Lesion Occurrence", "Section Display Name": "Ablation Location", "Coding Instructions": "Indicate if additional lesions were created at the specified location during the ablation procedure. \n", "Target Value": "The value on current procedure", "Short Name": "AblLesionOcc", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000003637", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15725, "Parent Element Name": "Adjunctive Ablation Location", "Parent Element Selection Name": "Any Value"}]}, {"Element Reference": 15709, "Name": "Adjunctive Ablation Lesion Energy Source", "Section Display Name": "Ablation Location", "Coding Instructions": "Indicate the energy source used to create the lesion.\n", "Target Value": "The value on current procedure", "Short Name": "AblLesionEnergy", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": null, "Is Dynamic List": "Yes", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000003637", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Adjunctive Ablation Lesion Energy Source", "Code": "112000003639", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Cryoenergy", "Selection Definition": "Cryoablation, or freezing technology, involves a coolant being released into the catheter’s balloon to freeze and ablate the tissue.\n", "Display Order": 1}, {"Name": "Adjunctive Ablation Lesion Energy Source", "Code": "112000003640", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Ethanol", "Selection Definition": "Ethanol infusion used during catheter ablation \n", "Display Order": 2}, {"Name": "Adjunctive Ablation Lesion Energy Source", "Code": "112000003641", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Laser", "Selection Definition": "The laser balloon catheter comprises an inflatable balloon mounted on a catheter shaft, an endoscope lumen, and an optical fiber that can deliver laser energy\n", "Display Order": 3}, {"Name": "Adjunctive Ablation Lesion Energy Source", "Code": "112000003642", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Pulsed Field Ablation", "Selection Definition": "Pulsed field ablation uses electrical pulses to cause nonthermal irreversible electroporation and induce cardiac cell death.\n", "Display Order": 4}, {"Name": "Adjunctive Ablation Lesion Energy Source", "Code": "112000003643", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Radiofrequency", "Selection Definition": "Radiofrequency uses heat energy generated by radiofrequency waves to create lesions or scars on the heart tissue, disrupting abnormal electrical signals.\n", "Display Order": 5}, {"Name": "Adjunctive Ablation Lesion Energy Source", "Code": "112000003644", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Other", "Selection Definition": "Any energy used during the procedure that is not listed\n", "Display Order": 6}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15725, "Parent Element Name": "Adjunctive Ablation Location", "Parent Element Selection Name": "Any Value"}, {"Parent Element Reference": 15708, "Parent Element Name": "Adjuctive Ablation Lesion Occurrence", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Procedure Information", "Section Display Name": "Additional Ablations Attempted", "Section Code": "ADDLABLS", "Section Type": "Section", "Cardinality": "0..1", "Table": "PROCINFO", "Elements": [{"Element Reference": 15710, "Name": "Additional Ablation", "Section Display Name": "Additional Ablations Attempted", "Coding Instructions": "Indicate if additional ablations, other than PVI (pulmonary vein isolation), were performed or attempted during the procedure.\n\nIntent: This element, and the child fields, are meant to capture a comprehensive view of the ablation strategies, approaches, techniques utilized during atrial fibrillation (AF) ablation procedures. While pulmonary vein isolation is the primary and most common approach to AF ablation, additional ablation techniques may be employed depending on the patient's specific condition and the complexity of the AF. Understanding whether additional ablations were performed helps to document procedural variability, assess outcomes, and potentially guide future treatment protocols.\n", "Target Value": "The value on current procedure", "Short Name": "AddAbl", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000003637", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Additional Ablations Attempted", "Section Display Name": "Ablation Approach", "Section Code": "ABLAPPR", "Section Type": "Repeater Section", "Cardinality": "0..n", "Table": "ABLAPPR", "Elements": [{"Element Reference": 15711, "Name": "Additional Ablation Approach", "Section Display Name": "Ablation Approach", "Coding Instructions": "Indicate the technique, strategy or approach used to perform the additional ablation. \n", "Target Value": "The value on current procedure", "Short Name": "AddAblTech", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000001854", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Additional Ablation Approach", "Code": "100000910", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Complex fractionated electrogram", "Selection Definition": null, "Display Order": 1}, {"Name": "Additional Ablation Approach", "Code": "*********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Focal/trigger ablation", "Selection Definition": null, "Display Order": 2}, {"Name": "Additional Ablation Approach", "Code": "*********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Ganglion plexus ablation", "Selection Definition": null, "Display Order": 3}, {"Name": "Additional Ablation Approach", "Code": "100000917", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Rotor-based mapping", "Selection Definition": null, "Display Order": 4}, {"Name": "Additional Ablation Approach", "Code": "112000003656", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Temporo-spatial dispersion mapping/ablation", "Selection Definition": null, "Display Order": 5}, {"Name": "Additional Ablation Approach", "Code": "100000351", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Other", "Selection Definition": null, "Display Order": 6}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15710, "Parent Element Name": "Additional Ablation", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 15712, "Name": "Additional Ablation Occurrence", "Section Display Name": "Ablation Approach", "Coding Instructions": "Indicate the occurrence of each additional ablation technique.\n", "Target Value": "The value on current procedure", "Short Name": "AddAblOcc", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000003637", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15711, "Parent Element Name": "Additional Ablation Approach", "Parent Element Selection Name": "Any Value"}]}, {"Element Reference": 15713, "Name": "Additional Ablation Energy Source", "Section Display Name": "Ablation Approach", "Coding Instructions": "Indicate the energy source used during the additional ablation.", "Target Value": "The value on current procedure", "Short Name": "AddAblEnergy", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": null, "Is Dynamic List": "Yes", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "112000003637", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Additional Ablation Energy Source", "Code": "112000003639", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Cryoenergy", "Selection Definition": "Cryoablation, or freezing technology, involves a coolant being released into the catheter’s balloon to freeze and ablate the tissue.\n", "Display Order": 1}, {"Name": "Additional Ablation Energy Source", "Code": "112000003640", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Ethanol", "Selection Definition": "Ethanol infusion used during catheter ablation \n", "Display Order": 2}, {"Name": "Additional Ablation Energy Source", "Code": "112000003641", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Laser", "Selection Definition": "The laser balloon catheter comprises an inflatable balloon mounted on a catheter shaft, an endoscope lumen, and an optical fiber that can deliver laser energy\n", "Display Order": 3}, {"Name": "Additional Ablation Energy Source", "Code": "112000003642", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Pulsed Field Ablation", "Selection Definition": "Pulsed field ablation uses electrical pulses to cause nonthermal irreversible electroporation and induce cardiac cell death.\n", "Display Order": 4}, {"Name": "Additional Ablation Energy Source", "Code": "112000003643", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Radiofrequency", "Selection Definition": "Radiofrequency uses heat energy generated by radiofrequency waves to create lesions or scars on the heart tissue, disrupting abnormal electrical signals.\n", "Display Order": 5}, {"Name": "Additional Ablation Energy Source", "Code": "112000003644", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Other", "Selection Definition": "Any energy used during the procedure that is not listed\n", "Display Order": 6}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 15711, "Parent Element Name": "Additional Ablation Approach", "Parent Element Selection Name": "Any Value"}, {"Parent Element Reference": 15712, "Parent Element Name": "Additional Ablation Occurrence", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Procedure Information", "Section Display Name": "Procedure Information 2", "Section Code": "PROCINFO2", "Section Type": "Section", "Cardinality": "0..1", "Table": "PROCINFO", "Elements": [{"Element Reference": 7120, "Name": "Phrenic Nerve Evaluation", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate if the phrenic nerve was evaluated.", "Target Value": "The value on current procedure", "Short Name": "PhrenicNerveEval", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001078", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 15724, "Name": "Cardioversion Performed During Procedure and Type", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate if cardioversion was performed during this procedure.\n", "Target Value": "The value on current procedure", "Short Name": "CVandType", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "250980009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Cardioversion Performed During Procedure and Type", "Code": "100013073", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "No", "Selection Definition": null, "Display Order": 1}, {"Name": "Cardioversion Performed During Procedure and Type", "Code": "440142000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Yes - Pharmacologic", "Selection Definition": null, "Display Order": 2}, {"Name": "Cardioversion Performed During Procedure and Type", "Code": "180325003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Yes - DC", "Selection Definition": null, "Display Order": 3}, {"Name": "Cardioversion Performed During Procedure and Type", "Code": "112000003646", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Both", "Selection Definition": null, "Display Order": 4}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 15717, "Name": "Atrial Flutter Observed During Procedure", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate if atrial flutter was observed during the procedure.\n\nNote(s):\nCode 'Yes' if atrial flutter was induced during the procedure.", "Target Value": "The value on current procedure", "Short Name": "AFObserved", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "5370000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Atrial Flutter Observed During Procedure", "Code": "100013073", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "No", "Selection Definition": null, "Display Order": 1}, {"Name": "Atrial Flutter Observed During Procedure", "Code": "112000003653", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Yes - Ablated", "Selection Definition": null, "Display Order": 2}, {"Name": "Atrial Flutter Observed During Procedure", "Code": "112000003654", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Yes - Not Ablated", "Selection Definition": null, "Display Order": 3}], "Ranges": null, "Definition": {"Title": "Atrial Flutter", "Definition": "Atrial flutter is defined as a cardiac arrhythmia arising in the atrium which has a regular rate typically between 250 and 350 bpm (cycle length 240-170 ms) in the absence of antiarrhythmic drugs.", "Source": "January CT, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Cleveland Jr JC, Cigarroa JE, Conti JB, Ellinor PT, Ezekowitz MD, Field ME, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, 2014 AHA/ACC/HRS Guideline for the Management of Patients With Atrial Fibrillation, Journal of the American College of Cardiology (2014), doi: 10.1016/j.jacc.2014.03.022."}, "Parent Child Validations": null}, {"Element Reference": 15718, "Name": "Atrial Tachycardia Observed During Procedure", "Section Display Name": "Procedure Information", "Coding Instructions": "Indicate if atrial tachycardia was observed during the procedure.\n\nNote(s):\nCode 'Yes' if atrial tachycardia was induced during the procedure.\n", "Target Value": "The value on current procedure", "Short Name": "ATObserved", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "276796006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Atrial Tachycardia Observed During Procedure", "Code": "100013073", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "No", "Selection Definition": null, "Display Order": 1}, {"Name": "Atrial Tachycardia Observed During Procedure", "Code": "112000003653", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Yes - Ablated", "Selection Definition": null, "Display Order": 2}, {"Name": "Atrial Tachycardia Observed During Procedure", "Code": "112000003654", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Yes - Not Ablated", "Selection Definition": null, "Display Order": 3}], "Ranges": null, "Definition": null, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Procedure Information", "Section Display Name": "<PERSON><PERSON>", "Section Code": "DEVICE", "Section Type": "Section", "Cardinality": "0..1", "Table": "PROCINFO", "Elements": [{"Element Reference": 7205, "Name": "Catheter Manipulation", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate the method used for catheter manipulation during the procedure.", "Target Value": "The value on current procedure", "Short Name": "CathManipulation", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "103712006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Catheter Manipulation", "Code": "100000958", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Manual", "Selection Definition": null, "Display Order": 1}, {"Name": "Catheter Manipulation", "Code": "112000003635", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Magnetic/Robotic", "Selection Definition": null, "Display Order": 2}, {"Name": "Catheter Manipulation", "Code": "112000003636", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Other", "Selection Definition": null, "Display Order": 3}], "Ranges": null, "Definition": null, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "<PERSON><PERSON>", "Section Display Name": "Catheter Ablation Devices", "Section Code": "CATHABLDEV", "Section Type": "Repeater Section", "Cardinality": "0..n", "Table": "CATHABLDEV", "Elements": [{"Element Reference": 7255, "Name": "Catheter Ablation Device", "Section Display Name": "Catheter Ablation Devices", "Coding Instructions": "Indicate the assigned identification number associated with the catheter ablation device.\n\nNote(s):\nThe devices that should be collected in your application are controlled by a Catheter Ablation Device Master file. This file is maintained by the NCDR and will be made available on the internet for downloading and importing/updating into your application.", "Target Value": "Any occurrence on current procedure", "Short Name": "DevID", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "Yes", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3478.6.1.22", "Code System": "2.16.840.1.113883.3.3478.6.1.22", "Code System Name": "ACC NCDR Catheter Ablation Devices", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7260, "Name": "Catheter Ablation Unique Device Identifier", "Section Display Name": "Catheter Ablation Devices", "Coding Instructions": "Indicate the direct identifier portion of the Unique Device Identifier (UDI) associated with the device. This ID is provided by the device manufacturer, and is either a GTIN or HIBC number.", "Target Value": "Any occurrence on current procedure", "Short Name": "CathAblationUDI", "Data Type": "ST", "Precision": "150", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "2.16.840.1.113883.3.3719", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Unique Device Identifier (UDI)", "Definition": "An identifier that is the main (primary) lookup for a medical device product and meets the requirements to uniquely identify a device through its distribution and use. This value is supplied to the FDA by the manufacturer.", "Source": "US FDA"}, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "<PERSON><PERSON>", "Section Display Name": "Electroanatomical Mapping System", "Section Code": "ELECTROMAPSYS", "Section Type": "Repeater Section", "Cardinality": "0..n", "Table": "ELECTROMAPSYS", "Elements": [{"Element Reference": 15715, "Name": "Electroanatomic Mapping System", "Section Display Name": "Electroanatomical Mapping System", "Coding Instructions": " Indicate the electroanatomic mapping system used. If no mapping system was used, leave this field blank.\n\nNote(s):\n\nElectroanatomic mapping systems combine information of the anatomy and electrical properties of the cardiac structures under evaluation. These systems create a three-dimensional anatomical map used to help localize critical sites for ablation. To request a mapping system be added to this list please contact NCDR.", "Target Value": "Any occurrence on current procedure", "Short Name": "MappingDevID", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "Yes", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "707833003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Procedure Information", "Section Display Name": "Radiation Exposure", "Section Code": "RADEXP", "Section Type": "Section", "Cardinality": "0..1", "Table": "PROCINFO", "Elements": [{"Element Reference": 7210, "Name": "Cumulative Air Kerma", "Section Display Name": "Radiation Exposure", "Coding Instructions": "Indicate the total radiation dose (Cumulative Air Kerma, or Reference Air Kerma) recorded to the nearest milligray (mGy) or gray (Gy). The value recorded should include the total dose for the lab visit. Cumulative air kerma is the total air kerma accrued from the beginning of an examination or procedure and includes all contributions from fluoroscopic and radiographic irradiation.", "Target Value": "The total between start of current procedure and end of current procedure", "Short Name": "FluoroDoseKerm", "Data Type": "PQ", "Precision": "5,0", "Unit Of Measure": "mGy, Gy", "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "228850003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "Gy", "Usual Range Min": "1", "Usual Range Max": "10", "Valid Range Min": "1", "Valid Range Max": "50"}, {"Unit Of Measure": "mGy", "Usual Range Min": "1", "Usual Range Max": "10,000", "Valid Range Min": "1", "Valid Range Max": "50,000"}], "Definition": {"Title": "Cumulative (Reference) Air kerma", "Definition": "Cumulative air kerma (also known as reference, reference dose, cumulative dose, or cumulative dose at a reference point) is the air kerma accumulated at a specific point in space (the patient entrance reference point) relative to the gantry of the fluoroscopy system.\n\nThe quantity, kerma, originated from the acronym, KERMA, for Kinetic Energy Released per unit Mass (of air).", "Source": "<PERSON>, et al. Radiation doses in interventional radiology procedures. (J Vasc Interv Radiol 2003;14:711-727.)"}, "Parent Child Validations": [{"Parent Element Reference": 15719, "Parent Element Name": "No Radiation Kerma", "Parent Element Selection Name": "No (or Not Answered)"}]}, {"Element Reference": 15719, "Name": "No Radiation Kerma", "Section Display Name": "Radiation Exposure", "Coding Instructions": "Indicate if no radiation was used during the procedure", "Target Value": "The value on current procedure", "Short Name": "NoRadiationKerm", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "228850003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 14278, "Name": "Dose Area Product", "Section Display Name": "Radiation Exposure", "Coding Instructions": "Indicate the total fluoroscopy dose to the nearest integer. The value recorded should include the total dose for the lab visit.", "Target Value": "The total between start of current procedure and end of current procedure", "Short Name": "FluoroDoseDAP2", "Data Type": "PQ", "Precision": "7,0", "Unit Of Measure": "Gy·cm², dGy·cm², cGy·cm², mGy·cm², µGy·M²", "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000994", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "Gy·cm²", "Usual Range Min": "1", "Usual Range Max": "700", "Valid Range Min": "1", "Valid Range Max": "5,000"}, {"Unit Of Measure": "dGy·cm²", "Usual Range Min": "10", "Usual Range Max": "7,000", "Valid Range Min": "10", "Valid Range Max": "50,000"}, {"Unit Of Measure": "cGy·cm²", "Usual Range Min": "100", "Usual Range Max": "70,000", "Valid Range Min": "100", "Valid Range Max": "500,000"}, {"Unit Of Measure": "µGy·M²", "Usual Range Min": "100", "Usual Range Max": "70,000", "Valid Range Min": "100", "Valid Range Max": "500,000"}, {"Unit Of Measure": "mGy·cm²", "Usual Range Min": "1,000", "Usual Range Max": "700,000", "Valid Range Min": "1,000", "Valid Range Max": "5,000,000"}], "Definition": {"Title": "Dose Area Product", "Definition": "Dose Area Product is the integral of air kerma (the energy extracted from an x-ray beam per unit mass of air in a small irradiated air volume; for diagnostic x-rays, the dose delivered to that volume of air) across the entire x-ray beam emitted from the x-ray tube. It is a surrogate measure of the amount of energy delivered to the patient.\n\nAlso known as KAP (Kerma Area Product).", "Source": "<PERSON>, et al. Radiation doses in interventional radiology procedures. (J Vasc Interv Radiol 2003; 14:711-727.)"}, "Parent Child Validations": [{"Parent Element Reference": 15720, "Parent Element Name": "No Fluoro Used", "Parent Element Selection Name": "No (or Not Answered)"}]}, {"Element Reference": 15720, "Name": "No Fluoro Used", "Section Display Name": "Radiation Exposure", "Coding Instructions": "Indicate if no fluoroscopy was used during the procedure.", "Target Value": "The value on current procedure", "Short Name": "NoFluoroUsed", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "53438000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 7214, "Name": "Fluoroscopy Time", "Section Display Name": "Radiation Exposure", "Coding Instructions": "Indicate the total fluoroscopy time recorded to the nearest 0.1-minute.  The time recorded should include the total time for the lab visit.", "Target Value": "The total between start of current procedure and end of current procedure", "Short Name": "FluoroTime", "Data Type": "PQ", "Precision": "4,1", "Unit Of Measure": "min", "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100014077", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "min", "Usual Range Min": "0.1", "Usual Range Max": "30.0", "Valid Range Min": "0.1", "Valid Range Max": "300.0"}], "Definition": null, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Procedure Information", "Section Display Name": "Intraprocedure Anticoagulation Strategy", "Section Code": "INTRAPROCANTICOAG", "Section Type": "Section", "Cardinality": "0..1", "Table": "PROCINFO", "Elements": [{"Element Reference": 7225, "Name": "Intraprocedure Anticoagulation", "Section Display Name": "Intraprocedure Anticoagulation Strategy", "Coding Instructions": "Indicate if intraprocedure anticoagulation therapy was provided.", "Target Value": "The value on current procedure", "Short Name": "IntraProcAnticoag", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "81839001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 15775, "Name": "Uninterrupted Anticoagulation Therapy", "Section Display Name": "Intraprocedure Anticoagulation Strategy", "Coding Instructions": "Indicate if the patient continued on warfarin, heparin, bivalirudin therapy or another anticoagulation therapy and it was not held for the procedure. ", "Target Value": "The value on current procedure", "Short Name": "UnintAnticoagTx", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001238", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 7225, "Parent Element Name": "Intraprocedure Anticoagulation", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Procedure Information", "Section Display Name": "Intra or Post-Proc Events", "Section Code": "INTPOSTEVENT", "Section Type": "Section", "Cardinality": "0..1", "Table": "PROCINFO", "Elements": null}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Intra or Post-Procedure Events", "Section Display Name": "Intra or Post-Procedure Events", "Section Code": "IPPEVENTS", "Section Type": "Repeater Section", "Cardinality": "0..n", "Table": "IPPEVENTS", "Elements": [{"Element Reference": 9001, "Name": "Intra/Post-Procedure Events", "Section Display Name": "Intra or Post-Procedure Events", "Coding Instructions": "Indicate the event that occurred between the start of the procedure and the next procedure or discharge.\n\n\n", "Target Value": "Any occurrence between start of procedure and until next procedure or discharge", "Short Name": "PostProcEvent", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "Yes", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "1000142478", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Intra/Post-Procedure Events (9001) should not be duplicated in a lab visit", "Selections": [{"Name": "Intra/Post-Procedure Events", "Code": "14669001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Acute kidney injury", "Selection Definition": null, "Display Order": 1}, {"Name": "Intra/Post-Procedure Events", "Code": "439470001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "A-V fistula requiring intervention", "Selection Definition": null, "Display Order": 2}, {"Name": "Intra/Post-Procedure Events", "Code": "100001237", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Bleeding - access site (transfusion)", "Selection Definition": null, "Display Order": 3}, {"Name": "Intra/Post-Procedure Events", "Code": "48867003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Bradycardia adverse events", "Selection Definition": null, "Display Order": 4}, {"Name": "Intra/Post-Procedure Events", "Code": "410429000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Cardiac arrest", "Selection Definition": null, "Display Order": 5}, {"Name": "Intra/Post-Procedure Events", "Code": "64915003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Cardiac surgery (unplanned emergent)", "Selection Definition": null, "Display Order": 6}, {"Name": "Intra/Post-Procedure Events", "Code": "128053003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Deep vein thrombosis", "Selection Definition": null, "Display Order": 7}, {"Name": "Intra/Post-Procedure Events", "Code": "417941003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "GU Bleeding", "Selection Definition": null, "Display Order": 8}, {"Name": "Intra/Post-Procedure Events", "Code": "84114007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Heart failure", "Selection Definition": null, "Display Order": 9}, {"Name": "Intra/Post-Procedure Events", "Code": "385494008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Hematoma at access site", "Selection Definition": null, "Display Order": 10}, {"Name": "Intra/Post-Procedure Events", "Code": "73320003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Hemolysis", "Selection Definition": null, "Display Order": 11}, {"Name": "Intra/Post-Procedure Events", "Code": "50960005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Hemorrhage (non access site)", "Selection Definition": null, "Display Order": 12}, {"Name": "Intra/Post-Procedure Events", "Code": "31892009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Hemothorax", "Selection Definition": null, "Display Order": 13}, {"Name": "Intra/Post-Procedure Events", "Code": "22298006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Myocardial infarction", "Selection Definition": null, "Display Order": 14}, {"Name": "Intra/Post-Procedure Events", "Code": "100001073", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Pericardial effusion requiring intervention", "Selection Definition": null, "Display Order": 15}, {"Name": "Intra/Post-Procedure Events", "Code": "100001074", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Pericardial effusion resulting in cardiac tamponade", "Selection Definition": null, "Display Order": 16}, {"Name": "Intra/Post-Procedure Events", "Code": "100001076", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Phrenic nerve damage", "Selection Definition": null, "Display Order": 17}, {"Name": "Intra/Post-Procedure Events", "Code": "60046008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Pleural effusion", "Selection Definition": null, "Display Order": 18}, {"Name": "Intra/Post-Procedure Events", "Code": "233604007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Pneumonia", "Selection Definition": null, "Display Order": 19}, {"Name": "Intra/Post-Procedure Events", "Code": "36118008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Pneumothorax", "Selection Definition": null, "Display Order": 20}, {"Name": "Intra/Post-Procedure Events", "Code": "443089001", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Pseudoaneurysm requiring intervention", "Selection Definition": null, "Display Order": 21}, {"Name": "Intra/Post-Procedure Events", "Code": "59282003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Pulmonary embolism", "Selection Definition": null, "Display Order": 22}, {"Name": "Intra/Post-Procedure Events", "Code": "60366008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Pulmonary vein damage/dissection", "Selection Definition": null, "Display Order": 23}, {"Name": "Intra/Post-Procedure Events", "Code": "409622000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Respiratory failure", "Selection Definition": null, "Display Order": 24}, {"Name": "Intra/Post-Procedure Events", "Code": "91302008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "<PERSON><PERSON>", "Selection Definition": null, "Display Order": 25}, {"Name": "Intra/Post-Procedure Events", "Code": "230690007", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Stroke", "Selection Definition": null, "Display Order": 26}, {"Name": "Intra/Post-Procedure Events", "Code": "266257000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Transient ischemic attack (TIA)", "Selection Definition": null, "Display Order": 27}, {"Name": "Intra/Post-Procedure Events", "Code": "30904006:363702006=57662003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Vascular injury requiring surgical intervention", "Selection Definition": null, "Display Order": 28}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 9002, "Name": "Intra/Post-Procedure Events Occurred", "Section Display Name": "Intra or Post-Procedure Events", "Coding Instructions": "Indicate the event that occurred between the procedure and the next procedure or discharge.", "Target Value": "Any occurrence between start of procedure and until next procedure or discharge", "Short Name": "PostProcOccurred", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "1000142479", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "When Intra/Post-Procedure Events (9001) are provided then Intra/Post-Procedure Events Occurred (9002) cannot be Null", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 9001, "Parent Element Name": "Intra/Post-Procedure Events", "Parent Element Selection Name": "Any Value"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Intra or Post-Procedure Events", "Section Display Name": "Intra or Post-Procedure Event Details", "Section Code": "IPPEVENTDET", "Section Type": "Section", "Cardinality": "0..1", "Table": "IPPEVENTS", "Elements": [{"Element Reference": 9030, "Name": "Brady<PERSON>ia Requiring Permanent Pacemaker", "Section Display Name": "Intra or Post-Procedure Event Details", "Coding Instructions": "Indicate if the patient required a permanent pacemaker. ", "Target Value": "Any occurrence between start of procedure and until next procedure or discharge", "Short Name": "ReqPermPacing", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "233182007:363702006=48867003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 9001, "Parent Element Name": "Intra/Post-Procedure Events", "Parent Element Selection Name": "Bradycardia adverse events"}, {"Parent Element Reference": 9002, "Parent Element Name": "Intra/Post-Procedure Events Occurred", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 9210, "Name": "Hemothorax Requiring Drainage", "Section Display Name": "Intra or Post-Procedure Event Details", "Coding Instructions": "Indicate if the patient was diagnosed with a hemothorax that required drainage.", "Target Value": "Any occurrence between start of procedure and until next procedure or discharge", "Short Name": "HemothoraxReqDrng", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001011", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 9001, "Parent Element Name": "Intra/Post-Procedure Events", "Parent Element Selection Name": "Hemothorax"}, {"Parent Element Reference": 9002, "Parent Element Name": "Intra/Post-Procedure Events Occurred", "Parent Element Selection Name": "Yes"}]}, {"Element Reference": 9220, "Name": "Pneumothorax Requiring Drainage", "Section Display Name": "Intra or Post-Procedure Event Details", "Coding Instructions": "Indicate if a chest tube or any form of drainage was required for patients experiencing a pneumothorax. ", "Target Value": "Any occurrence between start of procedure and until next procedure or discharge", "Short Name": "PneumothoraxReqDrng", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100001079", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 9001, "Parent Element Name": "Intra/Post-Procedure Events", "Parent Element Selection Name": "Pneumothorax"}, {"Parent Element Reference": 9002, "Parent Element Name": "Intra/Post-Procedure Events Occurred", "Parent Element Selection Name": "Yes"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "Discharge", "Section Code": "DISCHARGE", "Section Type": "Section", "Cardinality": "1..1", "Table": "EPISODEOFCARE", "Elements": [{"Element Reference": 10025, "Name": "Discharge Atrial Rhythm", "Section Display Name": "Discharge", "Coding Instructions": "Indicate the patient's atrial rhythm at the time of discharge.\n\nNote(s):\nIf the patient has multiple atrial rhythms, select all that apply.\n\nIn the event that a patient is ventricular paced, indicate the underlying atrial rhythm.", "Target Value": "Any occurrence between start of procedure and until next procedure or discharge", "Short Name": "DCAtrialRhythm", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Multiple", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "106068003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Discharge Atrial Rhythm", "Code": "49436004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Atrial fibrillation", "Selection Definition": null, "Display Order": 1}, {"Name": "Discharge Atrial Rhythm", "Code": "5370000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Atrial flutter", "Selection Definition": null, "Display Order": 2}, {"Name": "Discharge Atrial Rhythm", "Code": "251268003", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Atrial paced", "Selection Definition": null, "Display Order": 3}, {"Name": "Discharge Atrial Rhythm", "Code": "276796006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Atrial tachycardia", "Selection Definition": null, "Display Order": 4}, {"Name": "Discharge Atrial Rhythm", "Code": "106067008", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Sinus", "Selection Definition": null, "Display Order": 5}, {"Name": "Discharge Atrial Rhythm", "Code": "5609005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Sinus arrest", "Selection Definition": null, "Display Order": 6}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 14871, "Name": "Post Procedure Hemoglobin", "Section Display Name": "Discharge", "Coding Instructions": "Indicate the lowest hemoglobin (Hgb) value (g/dL), obtained via lab assay or point of care assay, between the end of the procedure and the time of discharge.", "Target Value": "The lowest value between end of current procedure and discharge", "Short Name": "PostProcHgb2", "Data Type": "PQ", "Precision": "3,1", "Unit Of Measure": "g/dL", "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "718-7", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": "g/dL", "Usual Range Min": "5.0", "Usual Range Max": "20.0", "Valid Range Min": "0.1", "Valid Range Max": "50.0"}], "Definition": {"Title": "Hemoglobin", "Definition": "Hemoglobin (Hb or Hgb) is the iron-containing oxygen-transport metalloprotein in the red blood cells. It carries oxygen from the lungs to the rest of the body (i.e. the tissues) where it releases the oxygen to burn nutrients and provide energy. Hemoglobin concentration measurement is among the most commonly performed blood tests, usually as part of a complete blood count. If the concentration is below normal, this is called anemia. Anemias are classified by the size of red blood cells: \"microcytic\" if red cells are small, \"macrocytic\" if they are large, and \"normocytic\" if otherwise. Dehydration or hyperhydration can greatly influence measured hemoglobin levels.", "Source": "http://s.details.loinc.org/LOINC/718-7.html?sections=Simple"}, "Parent Child Validations": [{"Parent Element Reference": 14872, "Parent Element Name": "Post Procedure Hemoglobin Not Drawn", "Parent Element Selection Name": "No (or Not Answered)"}]}, {"Element Reference": 14872, "Name": "Post Procedure Hemoglobin Not Drawn", "Section Display Name": "Discharge", "Coding Instructions": "Indicate if the post-procedure hemoglobin was not drawn.", "Target Value": "N/A", "Short Name": "PostProcHgbND2", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "718-7", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 10101, "Name": "Discharge Date and Time", "Section Display Name": "Discharge", "Coding Instructions": "Indicate the date and time the patient was discharged from your facility as identified in the medical record.\n\nNote(s): \nIndicate the time (hours:minutes) using the military 24-hour clock, beginning at midnight (00:00 hours). \n\nIf the exact discharge time is not specified in the medical record, then code the appropriate time as below.\n\n0000 - 0559 (midnight to before 6AM) code  0300\n0600 - 1159 (6AM - before noon) code 0900\n1200 - 1759 (noon before 6PM) code 1500\n1800 - 2359 (6PM to before midnight) code 2100", "Target Value": "The value on discharge", "Short Name": "DCDateTime", "Data Type": "TS", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Discharge Date and Time (10101) must be Greater than or Equal to 10/01/2024\n\nDischarge Date and Time (10101) and Arrival Date and Time (3001) must not overlap on multiple episodes\n\nDischarge Date and Time (10101) must be Greater than or Equal to Arrival Date and Time (3001)\n\nDischarge Date and Time (10101) must be Greater than or Equal to Procedure Start Date and Time (7000)", "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 10105, "Name": "Discharge Status", "Section Display Name": "Discharge", "Coding Instructions": "Indicate whether the patient was alive or deceased at discharge.", "Target Value": "The value on discharge", "Short Name": "DCStatus", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "75527-2", "Code System": "2.16.840.1.113883.6.1", "Code System Name": "LOINC", "Vendor Instruction": null, "Selections": [{"Name": "Discharge Status", "Code": "438949009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Alive", "Selection Definition": null, "Display Order": 1}, {"Name": "Discharge Status", "Code": "20", "Code System": "2.16.840.1.113883.12.112", "Code System Name": "HL7 Discharge disposition", "Selection Name": "Deceased", "Selection Definition": null, "Display Order": 2}], "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 10120, "Name": "Death During the Procedure", "Section Display Name": "Discharge", "Coding Instructions": "Indicate if the patient expired during the procedure.", "Target Value": "Any occurrence on discharge", "Short Name": "DeathProcedure", "Data Type": "BL", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100000923", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 10105, "Parent Element Name": "Discharge Status", "Parent Element Selection Name": "Deceased"}]}, {"Element Reference": 10125, "Name": "Cause of Death", "Section Display Name": "Discharge", "Coding Instructions": "Indicate the primary cause of death, i.e. the first significant abnormal event which ultimately led to death.", "Target Value": "The value on time of death", "Short Name": "DeathCause", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "184305005", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": null, "Selections": [{"Name": "Cause of Death", "Code": "100014107", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Cardiac", "Selection Definition": "Attribution of death to a cardiovascular etiology are acute myocardial infarction, sudden cardiac death, heart failure, stroke, cardiovascular procedure, cardiovascular hemorrhage, and other cardiovascular causes.\n\n“Death due to other cardiovascular causes” refers to a cardiovascular death not included in the above categories but with a specific known cause, such as a pulmonary embolism or peripheral arterial disease.\n\nIn addition, “death due to cardiovascular hemorrhage” refers to a death related to hemorrhage such as a non-stroke intracranial hemorrhage, non-procedural or non-traumatic vascular rupture (e.g., aortic aneurysm), or pulmonary hemorrhage from a pulmonary embolism.\n\nIn contrast, if a pulmonary hemorrhage were a result of a contusion from a motor vehicle accident, the cause of death would be non-cardiovascular (death due to trauma).", "Display Order": 1}, {"Name": "Cause of Death", "Code": "112000000343", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Non-Cardiac", "Selection Definition": "Mortality attributed to any non-cardiovascular organ, system, infection, malignancy, trauma or drug reaction/overdose.", "Display Order": 2}, {"Name": "Cause of Death", "Code": "112000000342", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Undetermined", "Selection Definition": "Attribution of causality may be limited or impossible if information available at the time of death is minimal or nonexistent. In such cases, the date of death may be the only data element captured and 'undetermined' should be selected for cause of death.\n", "Display Order": 3}], "Ranges": null, "Definition": {"Title": "Cause of Death", "Definition": "Death is classified into 1 of 3 categories: 1) cardiovascular death;  2) non - cardiovascular death; and 3) undetermined cause of death. \n\nThe intent of the classification schema is to identify one, and only one, of the categories as the underlying cause of death. The key priority is differentiating between cardiovascular and non-cardiovascular causes of death.", "Source": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, et al. 2014 ACC/AHA Key Data Elements and Definitions for Cardiovascular Endpoint Events in Clinical Trials: A Report of the American College of Cardiology/American Heart Association Task Force on Clinical Data Standards (Writing Committee to Develop Cardiovascular Endpoints Data Standards). J Am Coll Cardiol. 2015;():. Doi:10.1016/j.jacc.2014.12.018."}, "Parent Child Validations": [{"Parent Element Reference": 10105, "Parent Element Name": "Discharge Status", "Parent Element Selection Name": "Deceased"}]}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Discharge", "Section Display Name": "Discharge Medications", "Section Code": "DCMEDS", "Section Type": "Repeater Section", "Cardinality": "0..n", "Table": "DCMEDS", "Elements": [{"Element Reference": 10200, "Name": "Discharge Medication Code", "Section Display Name": "Discharge Medications", "Coding Instructions": "Indicate the assigned identification number associated with the medications the patient was prescribed upon discharge.\n\nNote(s):\nDischarge medications not required for patients who expired, discharged to \"Other acute care hospital\", \"Left against medical advice (AMA)\" or are receiving Hospice Care.\n\nThe medication(s) collected in this field are controlled by the Medication Master file. This file is maintained by the NCDR and will be made available on the internet for downloading and importing/updating into your application. Each medication in the Medication Master file is assigned to a value set. The value set is used to separate procedural medications from medications prescribed at discharge. The separation of these medications is depicted on the data collection form.", "Target Value": "N/A", "Short Name": "DC_MedID", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "Yes", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "100013057", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": "Discharge Medication Code (10200) should not be duplicated in an episode", "Selections": [{"Name": "Discharge Medication Code", "Code": "703", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Amiodarone", "Selection Definition": null, "Display Order": 1}, {"Name": "Discharge Medication Code", "Code": "41549009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Angiotensin converting enzyme inhibitor (ACE-I) (Any)", "Selection Definition": null, "Display Order": 2}, {"Name": "Discharge Medication Code", "Code": "372913009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Angiotensin receptor blocker (ARB) (Any)", "Selection Definition": null, "Display Order": 3}, {"Name": "Discharge Medication Code", "Code": "1656341", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Angiotensin II receptor blocker neprilysin inhibitor (ARNI)", "Selection Definition": null, "Display Order": 4}, {"Name": "Discharge Medication Code", "Code": "1364430", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Apixaban", "Selection Definition": null, "Display Order": 5}, {"Name": "Discharge Medication Code", "Code": "1191", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "<PERSON><PERSON><PERSON>", "Selection Definition": null, "Display Order": 6}, {"Name": "Discharge Medication Code", "Code": "226718", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Aspirin, Extended-Release Dipyridamole", "Selection Definition": null, "Display Order": 7}, {"Name": "Discharge Medication Code", "Code": "33252009", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Beta blocker (Any)", "Selection Definition": null, "Display Order": 8}, {"Name": "Discharge Medication Code", "Code": "1927851", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Betrixaban", "Selection Definition": null, "Display Order": 9}, {"Name": "Discharge Medication Code", "Code": "1656052", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "<PERSON><PERSON><PERSON><PERSON>", "Selection Definition": null, "Display Order": 10}, {"Name": "Discharge Medication Code", "Code": "32968", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Clopidogrel", "Selection Definition": null, "Display Order": 11}, {"Name": "Discharge Medication Code", "Code": "1546356", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Dabigatran", "Selection Definition": null, "Display Order": 12}, {"Name": "Discharge Medication Code", "Code": "3407", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Digoxin", "Selection Definition": null, "Display Order": 13}, {"Name": "Discharge Medication Code", "Code": "3443", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Diltiazem", "Selection Definition": null, "Display Order": 14}, {"Name": "Discharge Medication Code", "Code": "3541", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Disopyramide", "Selection Definition": null, "Display Order": 15}, {"Name": "Discharge Medication Code", "Code": "49247", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Dofetilide", "Selection Definition": null, "Display Order": 16}, {"Name": "Discharge Medication Code", "Code": "233698", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Dr<PERSON><PERSON><PERSON>", "Selection Definition": null, "Display Order": 17}, {"Name": "Discharge Medication Code", "Code": "1599538", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Edoxaban", "Selection Definition": null, "Display Order": 18}, {"Name": "Discharge Medication Code", "Code": "4441", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Flecainide", "Selection Definition": null, "Display Order": 19}, {"Name": "Discharge Medication Code", "Code": "772985004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "GLP-1 agonist", "Selection Definition": null, "Display Order": 20}, {"Name": "Discharge Medication Code", "Code": "100000921", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Heparin Derivative", "Selection Definition": null, "Display Order": 21}, {"Name": "Discharge Medication Code", "Code": "373294004", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Low Molecular Weight Heparin", "Selection Definition": null, "Display Order": 22}, {"Name": "Discharge Medication Code", "Code": "613391", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Prasug<PERSON>", "Selection Definition": null, "Display Order": 23}, {"Name": "Discharge Medication Code", "Code": "8700", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Procainamide", "Selection Definition": null, "Display Order": 24}, {"Name": "Discharge Medication Code", "Code": "8754", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Propafenone", "Selection Definition": null, "Display Order": 25}, {"Name": "Discharge Medication Code", "Code": "9068", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Quinidine", "Selection Definition": null, "Display Order": 26}, {"Name": "Discharge Medication Code", "Code": "1114195", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Rivaroxaban", "Selection Definition": null, "Display Order": 27}, {"Name": "Discharge Medication Code", "Code": "112000003634", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "SGLT inhibitor", "Selection Definition": null, "Display Order": 28}, {"Name": "Discharge Medication Code", "Code": "9947", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Sotalol", "Selection Definition": null, "Display Order": 29}, {"Name": "Discharge Medication Code", "Code": "1116632", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Ticagrelor", "Selection Definition": null, "Display Order": 30}, {"Name": "Discharge Medication Code", "Code": "10594", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Ticlopidine", "Selection Definition": null, "Display Order": 31}, {"Name": "Discharge Medication Code", "Code": "96382006", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Selection Name": "Unfractionated Heparin", "Selection Definition": null, "Display Order": 32}, {"Name": "Discharge Medication Code", "Code": "11170", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Verapamil", "Selection Definition": null, "Display Order": 33}, {"Name": "Discharge Medication Code", "Code": "1537034", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Vorapaxar", "Selection Definition": null, "Display Order": 34}, {"Name": "Discharge Medication Code", "Code": "11289", "Code System": "2.16.840.1.113883.6.88", "Code System Name": "RxNorm", "Selection Name": "Warfarin", "Selection Definition": null, "Display Order": 35}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 10105, "Parent Element Name": "Discharge Status", "Parent Element Selection Name": "Alive"}]}, {"Element Reference": 10205, "Name": "Discharge Medication Prescribed", "Section Display Name": "Discharge Medications", "Coding Instructions": "Indicate if the medication was prescribed, not prescribed, or was not prescribed for either a medical or patient reason.", "Target Value": "The value on discharge", "Short Name": "DC_MedAdmin", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": null, "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "No", "Code": "432102000", "Code System": "2.16.840.1.113883.6.96", "Code System Name": "SNOMED CT", "Vendor Instruction": "When Discharge Medication Code (10200) is answered, Discharge Medication Prescribed (10205) cannot be Null", "Selections": [{"Name": "Discharge Medication Prescribed", "Code": "100001247", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Yes - Prescribed", "Selection Definition": "Code 'Yes' if this medication was initiated (or prescribed) post procedure and for discharge.", "Display Order": 1}, {"Name": "Discharge Medication Prescribed", "Code": "100001048", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not Prescribed - No Reason", "Selection Definition": "Code 'No' if this medication was not prescribed post procedure or for discharge and there was no mention of a reason  why it was not ordered within the medical documentation.", "Display Order": 2}, {"Name": "Discharge Medication Prescribed", "Code": "100001034", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not Prescribed - Medical Reason", "Selection Definition": "Code 'No Medical Reason' if this medication was not prescribed post procedure or for discharge and there was a reason documented related to a medical issue or medical concern for not prescribing the medicine.", "Display Order": 3}, {"Name": "Discharge Medication Prescribed", "Code": "100001071", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Not Prescribed - Patient Reason", "Selection Definition": "Code 'No, Patient Reason' if this medication was not prescribed post procedure or for discharge and there was a reason documented related to the patient's preference.", "Display Order": 4}], "Ranges": null, "Definition": null, "Parent Child Validations": [{"Parent Element Reference": 10200, "Parent Element Name": "Discharge Medication Code", "Parent Element Selection Name": "Any Value"}]}]}, {"Container Class": "submissionInfoContainer", "Parent Section": "Root", "Section Display Name": "Administration", "Section Code": "ADMIN", "Section Type": "Section", "Cardinality": "1..1", "Table": "ADMIN", "Elements": [{"Element Reference": 1000, "Name": "Participant ID", "Section Display Name": "Administration", "Coding Instructions": "Indicate the participant ID of the submitting facility.", "Target Value": "N/A", "Short Name": "PartID", "Data Type": "NUM", "Precision": "8", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": null, "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code": "2.16.840.1.113883.3.3478.4.836", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": {"Title": "Participant ID", "Definition": "Participant ID is a unique number assigned to each database participant by NCDR. A database participant is defined as one entity that signs a Participation Agreement with the NCDR, submits one data submission file to the harvest, and receives one report on their data.\n\nEach participant's data if submitted to harvest must be in one data submission file for a quarter. If one participant keeps their data in more than one file (e.g. at two sites), then the data must be combined into a single data submission to the system to file for the harvest. If two or more participants share a single purchased software, and enter cases into one database, then the data must be exported into different data submission files, one for each participant ID.", "Source": "NCDR"}, "Parent Child Validations": null}, {"Element Reference": 1010, "Name": "Participant Name", "Section Display Name": "Administration", "Coding Instructions": "Indicate the full name of the facility where the procedure was performed.\n\nNote(s):\nValues should be full, official hospital names with no abbreviations or variations in spelling.", "Target Value": "N/A", "Short Name": "PartName", "Data Type": "ST", "Precision": "100", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": null, "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code": "2.16.840.1.113883.3.3478.4.836", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 1020, "Name": "Time Frame of Data Submission", "Section Display Name": "Administration", "Coding Instructions": "Indicate the time frame of data included in the data submission. Format: YYYYQQ. e.g.,2016Q1", "Target Value": "N/A", "Short Name": "Timeframe", "Data Type": "ST", "Precision": "6", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": null, "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code": "1.3.6.1.4.1.19376.1.4.1.6.5.45", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 1040, "Name": "Transmission Number", "Section Display Name": "Administration", "Coding Instructions": "This is a unique number created, and automatically inserted by the software into export file. It identifies the number of times the software has created a data submission file. The transmission number should be incremented by one every time the data submission files are exported. The transmission number should never be repeated.", "Target Value": "N/A", "Short Name": "XmsnId", "Data Type": "NUM", "Precision": "9", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": null, "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code": "1.3.6.1.4.1.19376.1.4.1.6.5.45", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": [{"Unit Of Measure": null, "Usual Range Min": null, "Usual Range Max": null, "Valid Range Min": "1", "Valid Range Max": "999,999,999"}], "Definition": null, "Parent Child Validations": null}, {"Element Reference": 1050, "Name": "Vendor Identifier", "Section Display Name": "Administration", "Coding Instructions": "Vendor identification (agreed upon by mutual selection between the vendor and the NCDR) to identify software vendor. This is entered into the schema automatically by vendor software. Vendors must use consistent name identification across sites. Changes to vendor name identification must be approved by the NCDR.", "Target Value": "N/A", "Short Name": "VendorId", "Data Type": "ST", "Precision": "15", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": null, "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code": "2.16.840.1.113883.3.3478.4.840", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 1060, "Name": "Vendor Software Version", "Section Display Name": "Administration", "Coding Instructions": "Vendor's software product name and version number identifying the software which created this record (assigned by vendor). Vendor controls the value in this field. This is entered into the schema automatically by vendor software.", "Target Value": "N/A", "Short Name": "Vendor<PERSON><PERSON>", "Data Type": "ST", "Precision": "20", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": null, "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code": "2.16.840.1.113883.3.3478.4.847", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 1070, "Name": "Registry Identifier", "Section Display Name": "Administration", "Coding Instructions": "The NCDR registry identifier describes the data registry to which these records apply. It is implemented in the software at the time the data is collected and records are created. This is entered into the schema automatically by software.", "Target Value": "N/A", "Short Name": "RegistryId", "Data Type": "ST", "Precision": "30", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "ACC-NCDR-AFib-2.0", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": null, "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code": "2.16.840.1.113883.3.3478.4.841", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 1071, "Name": "Registry Schema Version", "Section Display Name": "Administration", "Coding Instructions": "Schema version describes the version number of the Registry Transmission Document (RTD) schema to which each record conforms. It is an attribute that includes a constant value indicating the version of schema file. This is entered into the schema automatically by software.", "Target Value": "N/A", "Short Name": "SchemaVersion", "Data Type": "NUM", "Precision": "3,1", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "1", "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": null, "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 1085, "Name": "Submission Type", "Section Display Name": "Administration", "Coding Instructions": "Indicate if the data contained in the harvest/data file contains either standard patient episode of care records (arrival date to discharge only) or if it contains patient follow-up records. \n\nA transmission file with all episode of care records (from Arrival to Discharge only) is considered a 'Base Registry Record'.\n\nA file with patient follow-up records (any follow-up assessments performed during the quarter selected) is considered a 'Follow-Up Record'.\n\nNote(s):\nSelecting 'Follow-Up Records Only' will transmit all patient records with Follow-up Assessment Dates (Element Ref# 11000) contained in the selected timeframe, regardless of the procedure or discharge date. For example, if a patient has a procedure on 3/30/2017, is discharged on 3/31/2017, and has a follow-up assessment on 5/6/2017, the patient's episode of care data will be transmitted in the 2017Q1 Base Registry Record file, but the Follow-up data will be transmitted in the 2017Q2 Follow-Up File.", "Target Value": "N/A", "Short Name": "SubmissionType", "Data Type": "CD", "Precision": null, "Unit Of Measure": null, "Selection Type": "Single", "Default Value": null, "Is Dynamic List": "No", "Missing Data": "Illegal", "Is Harvested": "Yes", "Dataset": null, "Data Source": "Automatic", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": [{"Name": "Submission Type", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Episode of Care Records Only", "Selection Definition": null, "Display Order": 1}, {"Name": "Submission Type", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Selection Name": "Follow-Up Records Only", "Selection Definition": null, "Display Order": 2}], "Ranges": null, "Definition": null, "Parent Child Validations": null}]}]}}