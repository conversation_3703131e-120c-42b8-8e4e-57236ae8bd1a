{"TranslatorConfig": {"Version": "2.0", "Source": {"Dataset": {"Code": "AFib", "Id": 117}, "Version": "2.0", "Type": "<PERSON><PERSON><PERSON>"}, "Target": {"Dataset": {"Code": "AFib", "Id": 117}, "Version": "2.0", "Type": "Biome"}, "Translations": [{"Target": "datavrsn", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tenantid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "careentityid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "casesequencenumber", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "biomeencounterid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "yearmonth", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "distfromhospital", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "age", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "devicename", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "proceduretype", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": "Catheter Ablation (Afib)", "custom string": 1}, "is_computation": null}]}, {"Target": "originalotherid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hospname", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "filename", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "version", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "datasetname", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "clientfileid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "biomeimportdt", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lastname", "Source": "lastname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "firstname", "Source": "firstname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "midname", "Source": "midname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ssn", "Source": "ssn", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": null, "custom string": 1}, "is_computation": false}]}, {"Target": "ssnna", "Source": "ssnna", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ncdrpatientid", "Source": "ncdrpatientid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherid", "Source": "otherid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dob", "Source": "dob", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gender", "Source": "sex", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postalcode", "Source": "zipcode", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "zipcodena", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racewhite", "Source": "racewhite", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceblack", "Source": "raceblack", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceamindian", "Source": "raceamindian", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceasian", "Source": "raceasian", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racenathaw", "Source": "racenathaw", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "episodeid", "Source": "episodekey", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "healthinsurance", "Source": "healthins", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON>udy", "Source": "<PERSON><PERSON>udy", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "studyname", "Source": "studyname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rstudy<PERSON><PERSON>", "Source": "studyptid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cha2ds2vascchf", "Source": "chadchf", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "chadlvdysf", "Source": "chadlvdysf", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cha2ds2vaschypertension", "Source": "chad<PERSON><PERSON>cont", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cha2ds2vascdiabetesmellitus", "Source": "chaddm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "chadstroke", "Source": "chadstroke", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cha2ds2vasctia", "Source": "chad<PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cha2ds2vascthromboembolicevent", "Source": "chadte", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afeqtperformed", "Source": "afeqtbase", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "currentatrialfib", "Source": "afeqts1q1", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "currentatrialfibwhen", "Source": "afeqts1q2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion1", "Source": "afeqts2q1", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion2", "Source": "afeqts2q2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion3", "Source": "afeqts2q3", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion4", "Source": "afeqts2q4", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion5", "Source": "afeqts2q5", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion6", "Source": "afeqts2q6", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion7", "Source": "afeqts2q7", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion8", "Source": "afeqts2q8", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion9", "Source": "afeqts2q9", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion10", "Source": "afeqts2q10", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion11", "Source": "afeqts2q11", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion12", "Source": "afeqts2q12", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion13", "Source": "afeqts2q13", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion14", "Source": "afeqts2q14", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion15", "Source": "afeqts2q15", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion16", "Source": "afeqts2q16", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion17", "Source": "afeqts2q17", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion18", "Source": "afeqts2q18", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion19", "Source": "afeqts2q19", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion20", "Source": "afeqts2q20", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "atrialfibrillationclassification", "Source": "afibclass", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mechanicalvalveinmitralposition", "Source": "mechvalvemitpos", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibpharmacologiccardioversion", "Source": "prevafibtermpc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibdccardioversion", "Source": "prevafibtermdc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibcatheterablation", "Source": "prevafibtermca", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibcatheterablationdate", "Source": "afibcathabldate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibsurgicalablation", "Source": "prevafibtermsa", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibsurgicalablationdate", "Source": "afibsurgabldate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "atrialflutterclassification", "Source": "afluttertype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "atrialflutterpharmacologiccardioversion", "Source": "prevafltermpc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "atrialflutterdccardioversion", "Source": "prevafltermdc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "atrialfluttercatheterablation", "Source": "prevafltermca", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "atrialfluttercatheterablationdate", "Source": "afibfluttercathabldate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cardiomyopathytype", "Source": "priorcmtype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "sleepapnearxfollowed", "Source": "sleepapnearxfollowed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preproclvefassessed", "Source": "lvefassessed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preproclvef", "Source": "lvef", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "transthoracicechocardiography", "Source": "tteperf", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "transthoracicechocardiographydate", "Source": "ttedate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mitralvalveregurgitation", "Source": "mitralre<PERSON>rg", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mitralvalvestenosis", "Source": "mitralstenosis", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "transesophagealechocardiography", "Source": "teeperf", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preprocatrialthrombusdetected", "Source": "atrialthromdetect", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "imaging", "Source": "baselineimagingperf", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ctperformed", "Source": "ctperformed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "heartmriwocontrast", "Source": "mrperformed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "height", "Source": "height", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "weight", "Source": "weight", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preprocpulse", "Source": "pulse", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "systolicbp", "Source": "systolicbp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "diastolicbp", "Source": "diastolicbp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "inr", "Source": "inr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "inrnd", "Source": "inrnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preproccreat", "Source": "preproccreat", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preproccreatnd", "Source": "preproccreatnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "proceduredate", "Source": "procedurestartdatetime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "proceduretime", "Source": "procedurestartdatetime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "procedurestatus", "Source": "procstatus", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "opera_lastname", "Source": "opera_lastname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "opera_firstname", "Source": "opera_firstname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "opera_midname", "Source": "opera_midname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "opera_npi", "Source": "opera_npi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "phrenicnerveevaluation", "Source": "phrenicnerveeval", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "administrationofsedative", "Source": "anesthesia", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ablatedadjunctivelesion", "Source": "abllesion", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "transseptalcatheterization", "Source": "transseptcath", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON>ker<PERSON>", "Source": "fluorodosekerm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "procmedanticoagulant", "Source": "intraprocanticoag", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "deviceid", "Source": "devid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "deviceudi", "Source": "cathablationudi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postbradycardiarxpermanentpacemaker", "Source": "reqpermpacing", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hemothoraxrequiringdrainage", "Source": "hemothoraxreqdrng", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postpneumothoraxrequiringdrainage", "Source": "pneumothoraxreqdrng", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dischargestatus", "Source": "dcstatus", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "deathprocedure", "Source": "deathprocedure", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "deathcause", "Source": "deathcause", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dischmedprescribed", "Source": "dc_medadmin", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "particid", "Source": "partid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "partname", "Source": "partname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vendorid", "Source": "vendorid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vendorver", "Source": "vendorver", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "registryid", "Source": "registryid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doseareaproduct", "Source": "fluorodosedap2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doseareaproductunits", "Source": "fluorodosedap2_unit", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "arrivaldate", "Source": "arrivaldatetime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dischargedate", "Source": "dcdatetime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cardioversion", "Source": "cvandtype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "insihs", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "Indian Health Service", "Derivative Value Code": "33", "Value Map": {"|Indian Health Service|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "insmedicaid", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "Medicaid", "Derivative Value Code": "2", "Value Map": {"|Medicaid|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "insmedicare", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "Medicare (Part A or B)", "Derivative Value Code": "1", "Value Map": {"|Medicare (Part A or B)|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "insmedicareadvantage", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "Medicare Advantage (Part C)", "Derivative Value Code": "112000002025", "Value Map": {"|Medicare Advantage (Part C)|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "insmilitary", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "Military health care", "Derivative Value Code": "31", "Value Map": {"|Military health care|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON>us", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "Non-US insurance", "Derivative Value Code": "100000812", "Value Map": {"|Non-US insurance|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "insprivate", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "Private health insurance", "Derivative Value Code": "5", "Value Map": {"|Private health insurance|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "insstate", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "State-specific plan (non-Medicaid)", "Derivative Value Code": "36", "Value Map": {"|State-specific plan (non-Medicaid)|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "priorcatheterablationstrategy", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_concat", "Args": {"field_map": {"priorablstratcomplexfracatrialelectrogram": "Complex Fractionated Atrial Electrogram (100000910)", "priorablstratconvergentprocedure": "Convergent Procedure (100000911)", "priorablstratctcryoablation": "Cryoablation (233161001)", "priorablstratempiriclalinearlesions": "Empiric LA Linear Lesions (100000912)", "priorablstratganglionplexusablation": "Ganglion Plexus Ablation (100000914)", "priorablstratpulmonaryveinisolation": "Pulmonary Vein Isolation (100000915)", "priorablstratpulsedfieldablation": "Pulsed Field Ablation (112000003642)", "priorablstratrotorbasedmapping": "Rotor Based Mapping (100000917)", "priorablstratsegmentalpvablation": "Segmental PV Ablation (100000916)", "priorablstratwideareacircumablation": "Wide Area Circumferential Ablation (100000918)", "priorablstratfocalablation": "Focal Ablation (100000913)"}}, "is_computation": null}]}, {"Target": "priorablstratcomplexfracatrialelectrogram", "Source": null, "Transformation": {"Reference Field": "AFibPriorAblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Complex Fractionated Atrial Electrogram", "Derivative Value Code": "100000910", "Value Map": {"|Complex Fractionated Atrial Electrogram|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "priorablstratconvergentprocedure", "Source": null, "Transformation": {"Reference Field": "AFibPriorAblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Convergent Procedure", "Derivative Value Code": "100000911", "Value Map": {"|Convergent Procedure|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "priorablstratctcryoablation", "Source": null, "Transformation": {"Reference Field": "AFibPriorAblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Cryoablation", "Derivative Value Code": "233161001", "Value Map": {"|Cryoablation|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "priorablstratempiriclalinearlesions", "Source": null, "Transformation": {"Reference Field": "AFibPriorAblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Empiric LA Linear Lesions", "Derivative Value Code": "100000912", "Value Map": {"|Empiric LA Linear Lesions|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "priorablstratfocalablation", "Source": null, "Transformation": {"Reference Field": "AFibPriorAblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Focal Ablation", "Derivative Value Code": "100000913", "Value Map": {"|Focal Ablation|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "priorablstratganglionplexusablation", "Source": null, "Transformation": {"Reference Field": "AFibPriorAblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Ganglion Plexus Ablation", "Derivative Value Code": "100000914", "Value Map": {"|Ganglion Plexus Ablation|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "priorablstratpulmonaryveinisolation", "Source": null, "Transformation": {"Reference Field": "AFibPriorAblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Pulmonary Vein Isolation", "Derivative Value Code": "100000915", "Value Map": {"|Pulmonary Vein Isolation|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "priorablstratpulsedfieldablation", "Source": null, "Transformation": {"Reference Field": "AFibPriorAblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Pulsed Field Ablation", "Derivative Value Code": "112000003642", "Value Map": {"|Pulsed Field Ablation|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "priorablstratrotorbasedmapping", "Source": null, "Transformation": {"Reference Field": "AFibPriorAblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Rotor Based Mapping", "Derivative Value Code": "100000917", "Value Map": {"|Rotor Based Mapping|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "priorablstratsegmentalpvablation", "Source": null, "Transformation": {"Reference Field": "AFibPriorAblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Segmental PV Ablation", "Derivative Value Code": "100000916", "Value Map": {"|Segmental PV Ablation|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "priorablstratwideareacircumablation", "Source": null, "Transformation": {"Reference Field": "AFibPriorAblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Wide Area Circumferential Ablation", "Derivative Value Code": "100000918", "Value Map": {"|Wide Area Circumferential Ablation|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "atrialrhythmafib", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Atrial fibrillation", "Derivative Value Code": "49436004", "Value Map": {"|Atrial fibrillation|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "atrialrhythmaflutter", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Atrial flutter", "Derivative Value Code": "5370000", "Value Map": {"|Atrial flutter|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "atrialrhythmap", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Atrial paced", "Derivative Value Code": "251268003", "Value Map": {"|Atrial paced|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "atrialrhythmatrialtach", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Atrial tachycardia", "Derivative Value Code": "276796006", "Value Map": {"|Atrial tachycardia|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "atrialrhythmsinusnode", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Sinus", "Derivative Value Code": "106067008", "Value Map": {"|Sinus|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "atrialrhythmsinusarrest", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Sinus arrest", "Derivative Value Code": "5609005", "Value Map": {"|Sinus arrest|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "dischatrialrhythmafib", "Source": null, "Transformation": {"Reference Field": "DCAtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "DCAtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Atrial fibrillation", "Derivative Value Code": "49436004", "Value Map": {"|Atrial fibrillation|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "dischatrialrhythmaflutter", "Source": null, "Transformation": {"Reference Field": "DCAtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "DCAtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Atrial flutter", "Derivative Value Code": "5370000", "Value Map": {"|Atrial flutter|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "dischatrialrhythmap", "Source": null, "Transformation": {"Reference Field": "DCAtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "DCAtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Atrial paced", "Derivative Value Code": "251268003", "Value Map": {"|Atrial paced|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "dischatrialrhythmatrialtach", "Source": null, "Transformation": {"Reference Field": "DCAtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "DCAtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Atrial tachycardia", "Derivative Value Code": "276796006", "Value Map": {"|Atrial tachycardia|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "dischatrialrhythmsinusnode", "Source": null, "Transformation": {"Reference Field": "DCAtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "DCAtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Sinus", "Derivative Value Code": "106067008", "Value Map": {"|Sinus|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "dischatrialrhythmsinusarrest", "Source": null, "Transformation": {"Reference Field": "DCAtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "DCAtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Sinus arrest", "Derivative Value Code": "5609005", "Value Map": {"|Sinus arrest|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "manipulationofcatheter", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_concat", "Args": {"field_map": {"manipulationofcathetermanual": "Catheter Manipulation - Manual", "manipulationofcathetermagneticrobotic": "Catheter Manipulation - Magnetic/Robotic", "manipulationofcatheterother": "Catheter Manipulation - Other"}}, "is_computation": null}]}, {"Target": "manipulationofcathetermagneticrobotic", "Source": null, "Transformation": {"Reference Field": "CathManipulation", "Reference Field Code": 103712006, "Derivative Field": "CathManipulation", "Derivative Field Code": "103712006", "Derivative Value": "Magnetic/Robotic", "Derivative Value Code": "112000003635", "Value Map": {"|Magnetic/Robotic|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "manipulationofcathetermanual", "Source": null, "Transformation": {"Reference Field": "CathManipulation", "Reference Field Code": 103712006, "Derivative Field": "CathManipulation", "Derivative Field Code": "103712006", "Derivative Value": "Manual", "Derivative Value Code": "100000958", "Value Map": {"|Manual|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "manipulationofcatheterother", "Source": null, "Transformation": {"Reference Field": "CathManipulation", "Reference Field Code": 103712006, "Derivative Field": "CathManipulation", "Derivative Field Code": "103712006", "Derivative Value": "Other", "Derivative Value Code": "112000003636", "Value Map": {"|Other|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "preprocamiodarone", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Amiodarone", "Derivative Value Code": "703", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocace", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Angiotensin converting enzyme inhibitor (ACE-I) (Any)", "Derivative Value Code": "41549009", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preproc_arb_ni", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Angiotensin II receptor blocker neprilysin inhibitor (ARNI)", "Derivative Value Code": "1656341", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocangiotensiniireceptorantagonist", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Angiotensin receptor blocker (ARB) (Any)", "Derivative Value Code": "372913009", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocapixaban", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Apixaban", "Derivative Value Code": "1364430", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocaspirin", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "<PERSON><PERSON><PERSON>", "Derivative Value Code": "1191", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocaggrenox", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Aspirin, Extended-Release Dipyridamole", "Derivative Value Code": "226718", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preproc<PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Beta blocker (Any)", "Derivative Value Code": "33252009", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocbetrixaban", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Betrixaban", "Derivative Value Code": "1927851", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preproccangrelor", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "<PERSON><PERSON><PERSON><PERSON>", "Derivative Value Code": "1656052", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocclopidogrel", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Clopidogrel", "Derivative Value Code": "32968", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocdabigatran", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Dabigatran", "Derivative Value Code": "1546356", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocdigoxin", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Digoxin", "Derivative Value Code": "3407", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocdiltiazem", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Diltiazem", "Derivative Value Code": "3443", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocdisopyramide", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Disopyramide", "Derivative Value Code": "3541", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocdofetilide", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Dofetilide", "Derivative Value Code": "49247", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocdronedarone", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Dr<PERSON><PERSON><PERSON>", "Derivative Value Code": "233698", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocedoxaban", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Edoxaban", "Derivative Value Code": "1599538", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocflecainide", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Flecainide", "Derivative Value Code": "4441", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocglp1agonist", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "GLP-1 agonist", "Derivative Value Code": "772985004", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocheparinderivative", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Heparin Derivative", "Derivative Value Code": "100000921", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preproclowmolecularweightheparin", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Low Molecular Weight Heparin", "Derivative Value Code": "373294004", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocprasugrel", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Prasug<PERSON>", "Derivative Value Code": "613391", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocprocainamide", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Procainamide", "Derivative Value Code": "8700", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocpropafenone", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Propafenone", "Derivative Value Code": "8754", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocquinidine", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Quinidine", "Derivative Value Code": "9068", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocrivaroxaban", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Rivaroxaban", "Derivative Value Code": "1114195", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocsgltinhibitor", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "SGLT inhibitor", "Derivative Value Code": "112000003634", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocsotalol", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Sotalol", "Derivative Value Code": "9947", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocticagrelor", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Ticagrelor", "Derivative Value Code": "1116632", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocticlopidine", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Ticlopidine", "Derivative Value Code": "10594", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocunfractionatedheparin", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Unfractionated Heparin", "Derivative Value Code": "96382006", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocverapamil", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Verapamil", "Derivative Value Code": "11170", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocvorapaxar", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Vorapaxar", "Derivative Value Code": "1537034", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocwarfarin", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Warfarin", "Derivative Value Code": "11289", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargeamiodarone", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Amiodarone", "Derivative Value Code": "703", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargeace", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Angiotensin converting enzyme inhibitor (ACE-I) (Any)", "Derivative Value Code": "41549009", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_arb_any", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Angiotensin receptor blocker (ARB) (Any)", "Derivative Value Code": "372913009", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_arb_ni", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Angiotensin II receptor blocker neprilysin inhibitor (ARNI)", "Derivative Value Code": "1656341", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_apixaban", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Apixaban", "Derivative Value Code": "1364430", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_asa", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "<PERSON><PERSON><PERSON>", "Derivative Value Code": "1191", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargeaggrenox", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Aspirin, Extended-Release Dipyridamole", "Derivative Value Code": "226718", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargebetablocker", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Beta blocker (Any)", "Derivative Value Code": "33252009", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge<PERSON>rixaba<PERSON>", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Betrixaban", "Derivative Value Code": "1927851", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargecangrelor", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "<PERSON><PERSON><PERSON><PERSON>", "Derivative Value Code": "1656052", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_clopidogrel", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Clopidogrel", "Derivative Value Code": "32968", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_dabigatran", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Dabigatran", "Derivative Value Code": "1546356", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargedigoxin", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Digoxin", "Derivative Value Code": "3407", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargediltiazem", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Diltiazem", "Derivative Value Code": "3443", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargedisopyramide", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Disopyramide", "Derivative Value Code": "3541", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargedofetilide", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Dofetilide", "Derivative Value Code": "49247", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargedronedarone", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Dr<PERSON><PERSON><PERSON>", "Derivative Value Code": "233698", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_edoxaban", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Edoxaban", "Derivative Value Code": "1599538", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargeflecainide", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Flecainide", "Derivative Value Code": "4441", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargeglp1agonist", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "GLP-1 agonist", "Derivative Value Code": "772985004", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargeheparinderivative", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Heparin Derivative", "Derivative Value Code": "100000921", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargelowmolecularweightheparin", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Low Molecular Weight Heparin", "Derivative Value Code": "373294004", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_prasugrel", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Prasug<PERSON>", "Derivative Value Code": "613391", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargeprocainamide", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Procainamide", "Derivative Value Code": "8700", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargepropafenone", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Propafenone", "Derivative Value Code": "8754", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargequinidine", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Quinidine", "Derivative Value Code": "9068", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_rivaroxaban", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Rivaroxaban", "Derivative Value Code": "1114195", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargesgltinhibitor", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "SGLT inhibitor", "Derivative Value Code": "112000003634", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargesotalol", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Sotalol", "Derivative Value Code": "9947", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_ticagrelor", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Ticagrelor", "Derivative Value Code": "1116632", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_ticlopidine", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Ticlopidine", "Derivative Value Code": "10594", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargeunfractionatedheparin", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Unfractionated Heparin", "Derivative Value Code": "96382006", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargeverapamil", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Verapamil", "Derivative Value Code": "11170", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_vorapaxar", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Vorapaxar", "Derivative Value Code": "1537034", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_warfarin", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Warfarin", "Derivative Value Code": "11289", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "afasymptomsexperienced", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurenceArrival", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Symptoms During Afib/Aflutter", "Derivative Value Code": "418799008+106063007:=195080001", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "cardiomyopathy", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurenceArrival", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Cardiomyopathy", "Derivative Value Code": "85898001", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "chroniclungdisease", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurenceArrival", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Chronic Lung Disease", "Derivative Value Code": "413839001", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priorcad", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurenceArrival", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Coronary Artery Disease", "Derivative Value Code": "53741008", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "sleep<PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurenceArrival", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Sleep Apnea", "Derivative Value Code": "73430006", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "valvularatrialfibrillation", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurenceArrival", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Valvular Atrial Fibrillation", "Derivative Value Code": "100001118", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "atrialfibrillationtermination", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": 416940007, "Derivative Field": "ProcedHxName", "Derivative Field Code": "416940007", "Derivative Value": "Atrial Fibrillation Termination Attempt", "Derivative Value Code": "100000936", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "atrialfluttertermination", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": 416940007, "Derivative Field": "ProcedHxName", "Derivative Field Code": "416940007", "Derivative Value": "Atrial Flutter Termination Attempt", "Derivative Value Code": "100000937", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "avnodeablationwithpacemakerimplantation", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": 416940007, "Derivative Field": "ProcedHxName", "Derivative Field Code": "416940007", "Derivative Value": "AV Node ablation with Pacemaker Implantation", "Derivative Value Code": "428663009+307280005", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "leftatrialappendageocclusion", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": 416940007, "Derivative Field": "ProcedHxName", "Derivative Field Code": "416940007", "Derivative Value": "Left Atrial Appendage Occlusion", "Derivative Value Code": "112000000000", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "postacutekidneyinjury", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Acute kidney injury", "Derivative Value Code": "14669001", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "postarteriovenous<PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "A-V fistula requiring intervention", "Derivative Value Code": "439470001", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "postaccesssitebleedingreqtransfusion", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Bleeding - access site (transfusion)", "Derivative Value Code": "100001237", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "postbradycardia", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Bradycardia adverse events", "Derivative Value Code": "48867003", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "postcardiacarrest", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Cardiac arrest", "Derivative Value Code": "410429000", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "postcardiacsurgery", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Cardiac surgery (unplanned emergent)", "Derivative Value Code": "64915003", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "postdeepvenous<PERSON><PERSON><PERSON>is", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Deep vein thrombosis", "Derivative Value Code": "128053003", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "postgenitourinarytracthemorrhage", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "GU Bleeding", "Derivative Value Code": "417941003", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "posthf", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Heart failure", "Derivative Value Code": "84114007", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "postbleedhematoma", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Hematoma at access site", "Derivative Value Code": "385494008", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "posthemolysis", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Hemolysis", "Derivative Value Code": "73320003", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "posthemorrhagemorphologicabnormality", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Hemorrhage (non access site)", "Derivative Value Code": "50960005", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "posthemothorax", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Hemothorax", "Derivative Value Code": "31892009", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "postmi", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Myocardial infarction", "Derivative Value Code": "22298006", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "postpericardialeffusionreqintervention", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Pericardial effusion requiring intervention", "Derivative Value Code": "100001073", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "posttamponade", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Pericardial effusion resulting in cardiac tamponade", "Derivative Value Code": "100001074", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "postphrenicnervedamage", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Phrenic nerve damage", "Derivative Value Code": "100001076", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "postpleuraleffusion", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Pleural effusion", "Derivative Value Code": "60046008", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "postpneumonia", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Pneumonia", "Derivative Value Code": "233604007", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "postpneumothorax", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Pneumothorax", "Derivative Value Code": "36118008", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "postpseudoaneurysm", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Pseudoaneurysm requiring intervention", "Derivative Value Code": "443089001", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "postpulmonaryembolism", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Pulmonary embolism", "Derivative Value Code": "59282003", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "postpulmonaryveindamagedissection", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Pulmonary vein damage/dissection", "Derivative Value Code": "60366008", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "postrespiratoryfailure", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Respiratory failure", "Derivative Value Code": "409622000", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "postsepsis", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "<PERSON><PERSON>", "Derivative Value Code": "91302008", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "postcerebrovascularaccident", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Stroke", "Derivative Value Code": "230690007", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "posttia", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Transient ischemic attack (TIA)", "Derivative Value Code": "266257000", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "postvascularinjuryrequiringsurgicalintervention", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Vascular injury requiring surgical intervention", "Derivative Value Code": "30904006:363702006=57662003", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "afibepisodetimingtoday", "Source": null, "Transformation": {"Reference Field": "AFEQTS1Q2", "Reference Field Code": 100001147, "Derivative Field": "AFEQTS1Q2", "Derivative Field Code": "100001147", "Derivative Value": "Earlier today", "Derivative Value Code": "100001148", "Value Map": {"Earlier today": "Yes"}, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "afibepisodetimingpastweek", "Source": null, "Transformation": {"Reference Field": "AFEQTS1Q2", "Reference Field Code": 100001147, "Derivative Field": "AFEQTS1Q2", "Derivative Field Code": "100001147", "Derivative Value": "Within the past week", "Derivative Value Code": "100001149", "Value Map": {"Within the past week": "Yes"}, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "afibepisode<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "AFEQTS1Q2", "Reference Field Code": 100001147, "Derivative Field": "AFEQTS1Q2", "Derivative Field Code": "100001147", "Derivative Value": "Within the past month", "Derivative Value Code": "100001150", "Value Map": {"Within the past month": "Yes"}, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "afibepisodetimingpastyear", "Source": null, "Transformation": {"Reference Field": "AFEQTS1Q2", "Reference Field Code": 100001147, "Derivative Field": "AFEQTS1Q2", "Derivative Field Code": "100001147", "Derivative Value": "1 month to 1 year ago", "Derivative Value Code": "100001151", "Value Map": {"1 month to 1 year ago": "Yes"}, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "afibepisodetimingmorethan1year", "Source": null, "Transformation": {"Reference Field": "AFEQTS1Q2", "Reference Field Code": 100001147, "Derivative Field": "AFEQTS1Q2", "Derivative Field Code": "100001147", "Derivative Value": "More than 1 year ago", "Derivative Value Code": "100001152", "Value Map": {"More than 1 year ago": "Yes"}, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "afibepisodetimingnever", "Source": null, "Transformation": {"Reference Field": "AFEQTS1Q2", "Reference Field Code": 100001147, "Derivative Field": "AFEQTS1Q2", "Derivative Field Code": "100001147", "Derivative Value": "I was never aware of having atrial fibrillation", "Derivative Value Code": "100001153", "Value Map": {"I was never aware of having atrial fibrillation": "Yes"}, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "mbi", "Source": "mbi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hgb", "Source": "hgb", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hgbnd", "Source": "hgbnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preproc_bnpvalue", "Source": "preproc_bnpvalue", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preprocbnpnotdrawn", "Source": "preprocbnpnotdrawn", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preprocedurentbnp", "Source": "preprocedurentbnp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preprocntbnpnotdrawn", "Source": "preprocntbnpnotdrawn", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "sxexperienced", "Source": "sxexperienced", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "echocardiogramresults", "Source": "echocardiogramresults", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "procedureenddatetime", "Source": "procedureenddatetime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fit_lastname", "Source": "fit_lastname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fit_firstname", "Source": "fit_firstname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fit_midname", "Source": "fit_midname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fit_npi", "Source": "fit_npi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preprociceperf", "Source": "preprociceperf", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pvi", "Source": "pvi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pvienergysource", "Source": "pvienergysource", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ab<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "ab<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "abllesionocc", "Source": "abllesionocc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "abllesionenergy", "Source": "abllesionenergy", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "addabl", "Source": "addabl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "addabltech", "Source": "addabltech", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "addablocc", "Source": "addablocc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "addablenergy", "Source": "addablenergy", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afobserved", "Source": "afobserved", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "atobserved", "Source": "atobserved", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "noradiationkerm", "Source": "noradiationkerm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "nofluoroused", "Source": "nofluoroused", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fluorotime", "Source": "fluorotime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "unintanticoagtx", "Source": "unintanticoagtx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postprochgb2", "Source": "postprochgb2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postprochgbnd2", "Source": "postprochgbnd2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cumulativeairkermaunits", "Source": "fluorodosekerm_unit", "Transformation": null, "Value Map": null, "Computation": null}]}}