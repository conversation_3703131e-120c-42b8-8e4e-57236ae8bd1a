{"TranslatorConfig": {"Version": "1.0", "Source": {"Dataset": {"Code": "Cost", "Id": 157}, "Version": "1", "Type": "<PERSON><PERSON><PERSON>"}, "Target": {"Dataset": {"Code": "Cost", "Id": 157}, "Version": "1.0", "Type": "Biome"}, "Translations": [{"Target": "biomeencounterid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "biomeimportdt", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "chargecodename", "Source": "chargecodename", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "charges", "Source": "charges", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "clientfileid", "Source": "clientfileid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cpt4hcpcscode", "Source": "cpt4hcpcscode", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "split_join", "Args": {"source_field": "cpt4hcpcscode", "split_char": "-", "extract_part": "prefix"}, "is_computation": null}]}, {"Target": "cpt4hcpcsname", "Source": "cpt4hcpcsname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "datasetname", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dayofstay", "Source": "dayofstay", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "departmentname", "Source": "departmentname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "directcost", "Source": "directcost", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "scientific_to_decimal", "Args": {"default": "0", "format_str": ".20f"}, "is_computation": null}, {"Transform_Type": "subtract", "Args": {"fields": ["totalcost", "indirectcost"], "format_str": ".6f"}, "is_computation": true}]}, {"Target": "directcostperunit", "Source": "directcostperunit", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "encounternumber", "Source": "encounternumber", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "filename", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "indirectcost", "Source": "indirectcost", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "scientific_to_decimal", "Args": {"default": "0", "format_str": ".20f"}, "is_computation": null}, {"Transform_Type": "subtract", "Args": {"fields": ["totalcost", "directcost"], "format_str": ".6f"}, "is_computation": true}]}, {"Target": "medrecn", "Source": "medrecn", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "nationaldrugclasscode", "Source": "nationaldrugclasscode", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "originalmrn", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "servicesitename", "Source": "servicesitename", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "svcitemchargecode", "Source": "svcitemchargecode", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": "chargecodename"}, "is_computation": true}]}, {"Target": "svcitemdate", "Source": "svcitemdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tenantid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "totalcost", "Source": "totalcost", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "scientific_to_decimal", "Args": {"default": "0", "format_str": ".20f"}, "is_computation": null}, {"Transform_Type": "add", "Args": {"fields": ["directcost", "indirectcost"], "format_str": ".6f"}, "is_computation": true}]}, {"Target": "ub92revcode", "Source": "ub92revcode", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ub92revname", "Source": "ub92revname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "units", "Source": "units", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "version", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "chargeunit", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}]}}