{"TranslatorConfig": {"Version": "1.4", "Source": {"Dataset": {"Code": "LAAOFU", "Id": 164}, "Version": "1.4", "Type": "<PERSON><PERSON><PERSON>"}, "Target": {"Dataset": {"Code": "LAAOFU", "Id": 164}, "Version": "1.4", "Type": "Biome"}, "Translations": [{"Target": "ncdrpatientid", "Source": "ncdrpatientid", "Transformation": null, "Value Map": null}, {"Target": "partid", "Source": "partid", "Transformation": null, "Value Map": null}, {"Target": "followupid", "Source": "followupkey", "Transformation": null, "Value Map": null}, {"Target": "partname", "Source": "partname", "Transformation": null, "Value Map": null}, {"Target": "timeframe", "Source": "timeframe", "Transformation": null, "Value Map": null}, {"Target": "registryid", "Source": "registryid", "Transformation": null, "Value Map": null}, {"Target": "<PERSON>ver", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "otherid", "Source": "otherid", "Transformation": null, "Value Map": null}, {"Target": "gender", "Source": "sex", "Transformation": null, "Value Map": null}, {"Target": "dob", "Source": "dob", "Transformation": null, "Value Map": null}, {"Target": "arrivaldate", "Source": "arrivaldate", "Transformation": null, "Value Map": null}, {"Target": "dischargedate", "Source": "fu_refdischargedate", "Transformation": null, "Value Map": null}, {"Target": "procedurestartdate", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "assessmentdate", "Source": "f_assessmentdate", "Transformation": null, "Value Map": null}, {"Target": "followupinterval", "Source": "fuinterv", "Transformation": null, "Value Map": null}, {"Target": "method_office", "Source": null, "Transformation": {"Reference Field": "F_Method", "Reference Field Code": 100014059, "Derivative Field": "F_Method", "Derivative Field Code": 100014059, "Derivative Value": "Office Visit", "Derivative Value Code": 183654001, "Value Map": {"Office Visit": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "method_medrecord", "Source": null, "Transformation": {"Reference Field": "F_Method", "Reference Field Code": 100014059, "Derivative Field": "F_Method", "Derivative Field Code": 100014059, "Derivative Value": "Medical Records", "Derivative Value Code": 100014060, "Value Map": {"Medical Records": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "method_medprovider", "Source": null, "Transformation": {"Reference Field": "F_Method", "Reference Field Code": 100014059, "Derivative Field": "F_Method", "Derivative Field Code": 100014059, "Derivative Value": "Letter from Medical Provider", "Derivative Value Code": 100014061, "Value Map": {"Letter from Medical Provider": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "method_phone", "Source": null, "Transformation": {"Reference Field": "F_Method", "Reference Field Code": 100014059, "Derivative Field": "F_Method", "Derivative Field Code": 100014059, "Derivative Value": "Phone Call", "Derivative Value Code": 100014062, "Value Map": {"Phone Call": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "method_ssfile", "Source": null, "Transformation": {"Reference Field": "F_Method", "Reference Field Code": 100014059, "Derivative Field": "F_Method", "Derivative Field Code": 100014059, "Derivative Value": "Social Security Death Master File", "Derivative Value Code": **********, "Value Map": {"Social Security Death Master File": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "method_hospital", "Source": null, "Transformation": {"Reference Field": "F_Method", "Reference Field Code": 100014059, "Derivative Field": "F_Method", "Derivative Field Code": 100014059, "Derivative Value": "Hospitalized", "Derivative Value Code": 1000142363, "Value Map": {"Hospitalized": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "method_other", "Source": null, "Transformation": {"Reference Field": "F_Method", "Reference Field Code": 100014059, "Derivative Field": "F_Method", "Derivative Field Code": 100014059, "Derivative Value": "Other", "Derivative Value Code": 100000351, "Value Map": {"Other": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "dischargestatus", "Source": "f_status", "Transformation": null, "Value Map": null}, {"Target": "lvefassessed", "Source": "lvefassessed", "Transformation": null, "Value Map": null}, {"Target": "lvef", "Source": "lvef", "Transformation": null, "Value Map": null}, {"Target": "tteperf", "Source": "tteperf", "Transformation": null, "Value Map": null}, {"Target": "ttedate", "Source": "ttedate", "Transformation": null, "Value Map": null}, {"Target": "f_teeperf", "Source": "f_teeperf", "Transformation": null, "Value Map": null}, {"Target": "f_teedate", "Source": "f_teedate", "Transformation": null, "Value Map": null}, {"Target": "f_cardiacctperf", "Source": "f_cardiacctperf", "Transformation": null, "Value Map": null}, {"Target": "f_cardiacctdate", "Source": "f_cardiacctdate", "Transformation": null, "Value Map": null}, {"Target": "f_cardiacmriperf", "Source": "f_cardiacmriperf", "Transformation": null, "Value Map": null}, {"Target": "f_cardiacmridate", "Source": "f_cardiacmridate", "Transformation": null, "Value Map": null}, {"Target": "f_iceperformed", "Source": "f_iceperformed", "Transformation": null, "Value Map": null}, {"Target": "f_icedate", "Source": "f_icedate", "Transformation": null, "Value Map": null}, {"Target": "f_atrialthromdetect", "Source": "f_atrialthromdetect", "Transformation": null, "Value Map": null}, {"Target": "residualleak", "Source": "residualleak", "Transformation": null, "Value Map": null}, {"Target": "residualleakna", "Source": "residualleakna", "Transformation": null, "Value Map": null}, {"Target": "f_creat", "Source": "creat_fu", "Transformation": null, "Value Map": null}, {"Target": "f_creatna", "Source": "f_creatnd", "Transformation": null, "Value Map": null}, {"Target": "f_hgb", "Source": "lowhgbvalue_f", "Transformation": null, "Value Map": null}, {"Target": "f_hgbna", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "f_rankinscale", "Source": "f_rankinscore", "Transformation": null, "Value Map": null}, {"Target": "f_rankinscalena", "Source": "f_mrs_na", "Transformation": null, "Value Map": null}, {"Target": "bi<PERSON><PERSON>", "Source": "f_bieperf", "Transformation": null, "Value Map": null}, {"Target": "biefeeding", "Source": "f_biefeeding", "Transformation": null, "Value Map": null}, {"Target": "biebathing", "Source": "f_biebathing", "Transformation": null, "Value Map": null}, {"Target": "biegrooming", "Source": "f_biegrooming", "Transformation": null, "Value Map": null}, {"Target": "biedressing", "Source": "f_biedressing", "Transformation": null, "Value Map": null}, {"Target": "biebowels", "Source": "f_biebowels", "Transformation": null, "Value Map": null}, {"Target": "biebladder", "Source": "f_biebladder", "Transformation": null, "Value Map": null}, {"Target": "bietoiletuse", "Source": "f_bietoilet", "Transformation": null, "Value Map": null}, {"Target": "bietransfers", "Source": "f_bietransfers", "Transformation": null, "Value Map": null}, {"Target": "biemobility", "Source": "f_biemobility", "Transformation": null, "Value Map": null}, {"Target": "biestairs", "Source": "f_biestairs", "Transformation": null, "Value Map": null}, {"Target": "warfarindiscontinued", "Source": "f_warfarindiscontinued", "Transformation": null, "Value Map": null}, {"Target": "warfarindiscontinueddate", "Source": "f_warfarindiscontinueddate", "Transformation": null, "Value Map": null}, {"Target": "warfarinresumed", "Source": "f_warfarinresumed", "Transformation": null, "Value Map": null}, {"Target": "warfarinresumeddate", "Source": "f_warfarinresumeddate", "Transformation": null, "Value Map": null}, {"Target": "noactherapydiscontinued", "Source": "f_doactherapydiscontinued", "Transformation": null, "Value Map": null}, {"Target": "noactherapydiscontinueddate", "Source": "f_doactherapydiscontinueddate", "Transformation": null, "Value Map": null}, {"Target": "noactherapyresumed", "Source": "f_doactherapyresumed", "Transformation": null, "Value Map": null}, {"Target": "noactherapyresumeddate", "Source": "f_doactherapyresumeddate", "Transformation": null, "Value Map": null}, {"Target": "f_aspirindiscontinued", "Source": "f_aspirintherapydiscontinued", "Transformation": null, "Value Map": null}, {"Target": "f_aspirindiscontinueddate", "Source": "f_aspirintherapydiscontinueddate", "Transformation": null, "Value Map": null}, {"Target": "f_aspirinresumed", "Source": "f_aspirintherapyresumed", "Transformation": null, "Value Map": null}, {"Target": "f_aspirinresumeddate", "Source": "f_aspirintherapyresumeddate", "Transformation": null, "Value Map": null}, {"Target": "f_p2y12discontinued", "Source": "f_p2y12therapydiscontinued", "Transformation": null, "Value Map": null}, {"Target": "f_p2y12discontinueddate", "Source": "f_p2y12therapydiscontinueddate", "Transformation": null, "Value Map": null}, {"Target": "f_p2y12resumed", "Source": "f_p2y12therapyresumed", "Transformation": null, "Value Map": null}, {"Target": "f_p2y12date", "Source": "f_p2y12therapyresumeddate", "Transformation": null, "Value Map": null}, {"Target": "medid_fondaparinux", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin2", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Fondaparinux", "Derivative Value Code": 321208, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_heparin_derivative", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin2", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Heparin Derivative", "Derivative Value Code": 100000921, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_low_molecular_wt_heparin", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin2", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Low Molecular Weight Heparin", "Derivative Value Code": 373294004, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_unfractionated_heparin", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin2", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Unfractionated Heparin", "Derivative Value Code": 96382006, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_warfain", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin2", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Warfarin", "Derivative Value Code": 11289, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_dose", "Source": null, "Transformation": {"Reference Field": "F_MedDose2", "Reference Field Code": 100014233, "Derivative Field": "F_MedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON>", "Derivative Value Code": 1191, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_aspirin", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin2", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON>", "Derivative Value Code": 1191, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_aspirin_dipyridamole", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin2", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Aspirin/Dipyridamole", "Derivative Value Code": 226716, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_vorapaxar", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin2", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Vorapaxar", "Derivative Value Code": 1537034, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_apixaban", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin2", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Apixaban", "Derivative Value Code": 1364430, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_dabigatran", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin2", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Dabigatran", "Derivative Value Code": 1546356, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_edoxaban", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin2", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Edoxaban", "Derivative Value Code": 1599538, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_rivaroxaban", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin2", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Rivaroxaban", "Derivative Value Code": 1114195, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_cangrelor", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin2", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON><PERSON>", "Derivative Value Code": 1656052, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_clopidogrel", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin2", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Clopidogrel", "Derivative Value Code": 32968, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_other_p2y12_inhibitor", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin2", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Other P2Y12", "Derivative Value Code": 112000001003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_prasugrel", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin2", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Prasug<PERSON>", "Derivative Value Code": 613391, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_ticagrelor", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin2", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Ticagrelor", "Derivative Value Code": 1116632, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_ticlopidine", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin2", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Ticlopidine", "Derivative Value Code": 10594, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "adj_<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "adj_bleeddeathdate", "Source": "adj_bleeddeathdate", "Transformation": null, "Value Map": null}, {"Target": "adj_bleedin<PERSON>ter", "Source": "adj_bleedin<PERSON>ter", "Transformation": null, "Value Map": null}, {"Target": "adj_bleedrbctransfusion", "Source": "adj_bleedrbctransfusion", "Transformation": null, "Value Map": null}, {"Target": "adj_<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "adj_<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "adj_bleedpretranshgb", "Source": "adj_bleedpretranshgb", "Transformation": null, "Value Map": null}, {"Target": "adj_bleedimageperf", "Source": "adj_bleedimageperf", "Transformation": null, "Value Map": null}, {"Target": "adj_bleedendorgandamage", "Source": "adj_bleedendorgandamage", "Transformation": null, "Value Map": null}, {"Target": "adj_bleedprimarydc", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "adj_bleedmajorsurgery", "Source": "adj_bleedmajorsurgery", "Transformation": null, "Value Map": null}, {"Target": "adj_<PERSON><PERSON><PERSON>", "Source": "adj_<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "adj_bleedprocrelated", "Source": "adj_bleedprocrelated", "Transformation": null, "Value Map": {"Certain": "1", "Probable": "2", "Possible": "3", "Unlikely": "4", "Unclassifiable": "5"}}, {"Target": "adj_<PERSON><PERSON><PERSON><PERSON>", "Source": "adj_<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": {"Certain": "1", "Probable": "2", "Possible": "3", "Unlikely": "4", "Unclassifiable": "5"}}, {"Target": "adj_systhromboadj<PERSON>tus", "Source": "adj_systhromboadj<PERSON>tus", "Transformation": null, "Value Map": null}, {"Target": "adj_systhrombodeathdate", "Source": "adj_systhrombodeathdate", "Transformation": null, "Value Map": null}, {"Target": "adj_systhrombodeathcause", "Source": "adj_systhrombodeathcause", "Transformation": null, "Value Map": null}, {"Target": "adj_systhrombohypoperfusion", "Source": "adj_systhrombohypoperfusion", "Transformation": null, "Value Map": null}, {"Target": "adj_systhromboimagevidence", "Source": "adj_systhromboimagevidence", "Transformation": null, "Value Map": null}, {"Target": "adj_systhrom<PERSON><PERSON><PERSON>terv", "Source": "adj_systhrom<PERSON><PERSON><PERSON>terv", "Transformation": null, "Value Map": null}, {"Target": "adj_neuroadj<PERSON><PERSON>", "Source": "adj_neuroadj<PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "aj_deathdate", "Source": "f_adj_deathdate", "Transformation": null, "Value Map": null}, {"Target": "adj_neurosxonset", "Source": "adj_neurosxonset", "Transformation": null, "Value Map": null}, {"Target": "aj_neurodef", "Source": "fu_adj_neurodeficit", "Transformation": null, "Value Map": null}, {"Target": "adj_neuroclinicpresent", "Source": "adj_neuroclinicpresent", "Transformation": null, "Value Map": null}, {"Target": "adj_neurodxconfirmed", "Source": "adj_neurodxconfirmed", "Transformation": null, "Value Map": null}, {"Target": "aj_neuroimag", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "adj_neuroivrtpa", "Source": "adj_neuroivrtpa", "Transformation": null, "Value Map": null}, {"Target": "adj_neuroendotherainter", "Source": "adj_neuroendotherainter", "Transformation": null, "Value Map": null}, {"Target": "aj_neurosxduration", "Source": "fu_adj_neurosxduration", "Transformation": null, "Value Map": null}, {"Target": "adj_neurotrauma", "Source": "adj_neurotrauma", "Transformation": null, "Value Map": null}, {"Target": "adj_rankinscale", "Source": "adj_rankinscale", "Transformation": null, "Value Map": null}, {"Target": "adj_rankins<PERSON>na", "Source": "adj_rankins<PERSON>na", "Transformation": null, "Value Map": null}, {"Target": "adj_neuroprocrelated", "Source": "adj_neuroprocrelated", "Transformation": null, "Value Map": null}, {"Target": "adj_neurodevrelated", "Source": "adj_neurodevrelated", "Transformation": null, "Value Map": null}, {"Target": "eventid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "f_eventname", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "eventoccurred", "Source": "fupevoccurred", "Transformation": null, "Value Map": null}, {"Target": "eventdate", "Source": "fupeventdate", "Transformation": null, "Value Map": null}, {"Target": "originalotherid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "hospname", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "filename", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "version", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "datasetname", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "clientfileid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "biomeimportdt", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "careentityid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "tenantid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "yearmonth", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "casesequencenumber", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "therainterv_other", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_SysThromboTheraInterv", "Reference Field Code": 100013063, "Derivative Field": "FU_ADJ_SysThromboIntervType", "Derivative Field Code": 100013063, "Derivative Value": "Other", "Derivative Value Code": 112000000172, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "adj_neurodeficittype", "Source": "adj_neurodeficittype", "Transformation": null, "Value Map": null}, {"Target": "hemorrhage_intracerebral", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_NeuroIntracranType", "Reference Field Code": 230706003, "Derivative Field": "FU_ADJ_NeuroIntracranType", "Derivative Field Code": 230706003, "Derivative Value": "Intracerebral", "Derivative Value Code": 274100004, "Value Map": {"Intracerebral": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "hemorrhage_subarachnoid", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_NeuroIntracranType", "Reference Field Code": 230706003, "Derivative Field": "FU_ADJ_NeuroIntracranType", "Derivative Field Code": 230706003, "Derivative Value": "Subarachnoid", "Derivative Value Code": 21454007, "Value Map": {"Subarachnoid": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "hemorrhage_subdural", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_NeuroIntracranType", "Reference Field Code": 230706003, "Derivative Field": "FU_ADJ_NeuroIntracranType", "Derivative Field Code": 230706003, "Derivative Value": "Subdural", "Derivative Value Code": 35486000, "Value Map": {"Subdural": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "imagingmethod_angiography", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_SysThromboImagEvidence", "Reference Field Code": 365853002, "Derivative Field": "FU_ADJ_SysThromboImagMethod", "Derivative Field Code": 112000000960, "Derivative Value": "Angiography", "Derivative Value Code": 77343006, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "imagingmethod_ctscan", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_SysThromboImagEvidence", "Reference Field Code": 365853002, "Derivative Field": "FU_ADJ_SysThromboImagMethod", "Derivative Field Code": 112000000960, "Derivative Value": "Computed Tomography", "Derivative Value Code": 77477000, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "imagingmethod_mri", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_SysThromboImagEvidence", "Reference Field Code": 365853002, "Derivative Field": "FU_ADJ_SysThromboImagMethod", "Derivative Field Code": 112000000960, "Derivative Value": "Magnetic Resonance Imaging", "Derivative Value Code": 113091000, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "imagingmethod_ultrasound", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_SysThromboImagEvidence", "Reference Field Code": 365853002, "Derivative Field": "FU_ADJ_SysThromboImagMethod", "Derivative Field Code": 112000000960, "Derivative Value": "Ultrasound", "Derivative Value Code": 112000001042, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "imagingmethod_other", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_SysThromboImagEvidence", "Reference Field Code": 365853002, "Derivative Field": "FU_ADJ_SysThromboImagMethod", "Derivative Field Code": 112000000960, "Derivative Value": "Other Imaging", "Derivative Value Code": 112000001862, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "therainterv_catheter", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_SysThromboTheraInterv", "Reference Field Code": 100013063, "Derivative Field": "FU_ADJ_SysThromboIntervType", "Derivative Field Code": 100013063, "Derivative Value": "Catheter", "Derivative Value Code": 276272002, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "therainterv_pharmacological", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_SysThromboTheraInterv", "Reference Field Code": 100013063, "Derivative Field": "FU_ADJ_SysThromboIntervType", "Derivative Field Code": 100013063, "Derivative Value": "Pharmacological", "Derivative Value Code": 182832007, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "therainterv_surgical", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_SysThromboTheraInterv", "Reference Field Code": 100013063, "Derivative Field": "FU_ADJ_SysThromboIntervType", "Derivative Field Code": 100013063, "Derivative Value": "Surgical", "Derivative Value Code": 387713003, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "eventname", "Source": "f_event", "Transformation": null, "Value Map": null}, {"Target": "adj_medid_aspirin_81mg", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "FU_ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Aspirin 81 to 100 mg", "Derivative Value Code": 96382006, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_aspirin_325mg", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "FU_ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Aspirin 325 mg", "Derivative Value Code": 373294004, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_vorapaxar", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "FU_ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Vorapaxar", "Derivative Value Code": 1116632, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_clopidogrel", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "FU_ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Clopidogrel", "Derivative Value Code": 112000002081, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_prasugrel", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "FU_ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Prasug<PERSON>", "Derivative Value Code": 1114195, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_ticlopidine", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "FU_ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Ticlopidine", "Derivative Value Code": 112000001003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_ticagrelor", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "FU_ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Ticagrelor", "Derivative Value Code": 32968, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_warfain", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "FU_ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Warfarin", "Derivative Value Code": 10594, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_apixaban", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "FU_ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Apixaban", "Derivative Value Code": 321208, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_dabigatran", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "FU_ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Dabigatran", "Derivative Value Code": 317300, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_rivaroxaban", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "FU_ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Rivaroxaban", "Derivative Value Code": 1656052, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_ed<PERSON><PERSON>n", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "FU_ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Edoxaban", "Derivative Value Code": 226716, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_unfractionated_heparin", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "FU_ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Unfractionated Heparin", "Derivative Value Code": 613391, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_aggrenox", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "medid_durlaza", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "medid_aspirin_81mg", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "medid_aspirin_325mg", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "adj_medid_fond<PERSON><PERSON>ux", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "FU_ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Fondaparinux", "Derivative Value Code": 1537034, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_low_molecular_wt_heparin", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "FU_ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Low Molecular Weight Heparin", "Derivative Value Code": 1546356, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_heparin_derivative", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "FU_ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Heparin Derivative", "Derivative Value Code": 1364430, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_aggrenox", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "adj_medid_durlaza", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "f_creatnd", "Source": "f_creatnd", "Transformation": null, "Value Map": null}, {"Target": "f_followupeventoccurred", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "adj_medid_aspirin_101_324mg", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "FU_ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Aspirin 101 to 324 mg", "Derivative Value Code": 100000921, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_aspirin_81_100_mg", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "FU_ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Aspirin 81 to 100 mg", "Derivative Value Code": 96382006, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_aspirin_dipyridamole", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "FU_ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Aspirin/Dipyridamole", "Derivative Value Code": 11289, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_cangrelor", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "FU_ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON><PERSON>", "Derivative Value Code": 112000002080, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_other_p2y12_inhibitor", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "FU_ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Other P2Y12", "Derivative Value Code": 1599538, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "deathcause", "Source": "deathcause", "Transformation": null, "Value Map": null}, {"Target": "procedurestopdate", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "procedurestoptime", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "f_deathdate", "Source": "f_deathdate", "Transformation": null, "Value Map": null}, {"Target": "f_hgbnd", "Source": "hgbnd_fu", "Transformation": null, "Value Map": null}, {"Target": "submissiontype", "Source": "submissiontype", "Transformation": null, "Value Map": null}, {"Target": "imagingtype_angiography", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_NeuroBrainImaging", "Reference Field Code": 441986001, "Derivative Field": "FU_ADJ_NeuroBrainImagingType", "Derivative Field Code": 441986001, "Derivative Value": "Cerebral Angiography", "Derivative Value Code": 3258003, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "imagingtype_ctscan", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_NeuroBrainImaging", "Reference Field Code": 441986001, "Derivative Field": "FU_ADJ_NeuroBrainImagingType", "Derivative Field Code": 441986001, "Derivative Value": "Computed Tomography", "Derivative Value Code": 77477000, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "imagingtype_mri", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_NeuroBrainImaging", "Reference Field Code": 441986001, "Derivative Field": "FU_ADJ_NeuroBrainImagingType", "Derivative Field Code": 441986001, "Derivative Value": "Magnetic Resonance Imaging", "Derivative Value Code": 113091000, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "imagingtype_other", "Source": null, "Transformation": {"Reference Field": "FU_ADJ_NeuroBrainImaging", "Reference Field Code": 441986001, "Derivative Field": "FU_ADJ_NeuroBrainImagingType", "Derivative Field Code": 441986001, "Derivative Value": "Other", "Derivative Value Code": 112000001862, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "biomeencounterid", "Source": null, "Transformation": null, "Value Map": null}]}}