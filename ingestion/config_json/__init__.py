import os
import json


def dir_path():
    return os.path.dirname(os.path.abspath(__file__))


def read_config(filename):
    file_path = os.path.join(dir_path(), filename)
    with open(file_path, 'r') as f:
        return json.load(f)


def write_config(filename, data):
    file_path = os.path.join(dir_path(), filename)
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
