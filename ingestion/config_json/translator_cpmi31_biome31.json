{"TranslatorConfig": {"Version": "3.1", "Source": {"Dataset": {"Code": "CPMI", "Id": 148}, "Version": "3.1", "Type": "<PERSON><PERSON><PERSON>"}, "Target": {"Dataset": {"Code": "CPMI", "Id": 148}, "Version": "3.1", "Type": "Biome"}, "Translations": [{"Target": "<PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "ncdrpatientid", "Source": "ncdrpatientid", "Transformation": null, "Value Map": null}, {"Target": "partid", "Source": "partid", "Transformation": null, "Value Map": null}, {"Target": "partname", "Source": "partname", "Transformation": null, "Value Map": null}, {"Target": "lastname", "Source": "lastname", "Transformation": null, "Value Map": null}, {"Target": "firstname", "Source": "firstname", "Transformation": null, "Value Map": null}, {"Target": "midname", "Source": "midname", "Transformation": null, "Value Map": null}, {"Target": "dob", "Source": "dob", "Transformation": null, "Value Map": null}, {"Target": "ssn", "Source": "ssn", "Transformation": null, "Value Map": null}, {"Target": "ssnna", "Source": "ssnna", "Transformation": null, "Value Map": null}, {"Target": "otherid", "Source": "otherid", "Transformation": null, "Value Map": null}, {"Target": "gender", "Source": "sex", "Transformation": null, "Value Map": null}, {"Target": "patzip", "Source": "zipcode", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "zipcodena", "Transformation": null, "Value Map": null}, {"Target": "racewhite", "Source": "racewhite", "Transformation": null, "Value Map": null}, {"Target": "raceblack", "Source": "raceblack", "Transformation": null, "Value Map": null}, {"Target": "raceamindian", "Source": "raceamindian", "Transformation": null, "Value Map": null}, {"Target": "raceasian", "Source": "raceasian", "Transformation": null, "Value Map": null}, {"Target": "racenathaw", "Source": "racenathaw", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "episode<PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "episodeid", "Source": "episodekey", "Transformation": null, "Value Map": null}, {"Target": "arrivaldatetime", "Source": "arrivaldatetime", "Transformation": null, "Value Map": null}, {"Target": "admitdatetime", "Source": "admitdatetime", "Transformation": null, "Value Map": null}, {"Target": "healthins", "Source": "healthins", "Transformation": null, "Value Map": null}, {"Target": "insprivate", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Private Health Insurance", "Derivative Value Code": 5, "Value Map": {"|Private Health Insurance|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "insmedicare", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Medicare", "Derivative Value Code": 1, "Value Map": {"Medicare": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "patienttype", "Source": "pttype", "Transformation": null, "Value Map": {"Unstable angina": "Unstable <PERSON><PERSON>", "Low-risk chest pain": "Low-Risk Chest Pain"}}, {"Target": "<PERSON><PERSON>t", "Source": "<PERSON><PERSON>t", "Transformation": null, "Value Map": null}, {"Target": "admittingdx", "Source": "admittingdx", "Transformation": null, "Value Map": null}, {"Target": "nonatheromi", "Source": "nonatheromi", "Transformation": null, "Value Map": null}, {"Target": "mimechanism", "Source": "mimechanism", "Transformation": null, "Value Map": null}, {"Target": "firstfacmeanstrans", "Source": "firstfacmeanstrans", "Transformation": null, "Value Map": {"EMS - Ambulance": "Ambulance", "EMS - Air": "Air"}}, {"Target": "ems911calldatetime", "Source": "ems911calldatetime", "Transformation": null, "Value Map": null}, {"Target": "emsdispatchdatetime", "Source": "emsdispatchdatetime", "Transformation": null, "Value Map": null}, {"Target": "firstmedcondatetime", "Source": "firstmedcondatetime", "Transformation": null, "Value Map": null}, {"Target": "emsleavingscenedatetime", "Source": "emsleavingscenedatetime", "Transformation": null, "Value Map": null}, {"Target": "emsfirstmedconreasonfordelay", "Source": "emsfirstmedconreasonfordelay", "Transformation": null, "Value Map": null}, {"Target": "emss<PERSON><PERSON><PERSON>", "Source": "emss<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "emsstemialertdatetime", "Source": "emsstemialertdatetime", "Transformation": null, "Value Map": null}, {"Target": "emsnpi", "Source": "emsnpi", "Transformation": null, "Value Map": null}, {"Target": "emsrunnumber", "Source": "emsrunnumber", "Transformation": null, "Value Map": null}, {"Target": "tranoutsidefac", "Source": "tranoutsidefac", "Transformation": null, "Value Map": null}, {"Target": "arrouthospdatetime", "Source": "arrouthospdatetime", "Transformation": null, "Value Map": null}, {"Target": "tranoutfacdatetime", "Source": "tranoutfacdatetime", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "tranfacname", "Source": "tranfacname", "Transformation": null, "Value Map": null}, {"Target": "tranfacahanumber", "Source": "tranfacahanumber", "Transformation": null, "Value Map": null}, {"Target": "facnamesameparent", "Source": "facnamesameparent", "Transformation": null, "Value Map": null}, {"Target": "tranfacnameunavail", "Source": "tranfacnameunavail", "Transformation": null, "Value Map": null}, {"Target": "caouthospital", "Source": "caouthospital", "Transformation": null, "Value Map": null}, {"Target": "cawitness", "Source": "cawitness", "Transformation": null, "Value Map": null}, {"Target": "bystandcpr", "Source": "bystandcpr", "Transformation": null, "Value Map": null}, {"Target": "capostems", "Source": "capostems", "Transformation": null, "Value Map": null}, {"Target": "initcarhythm", "Source": "initcarhythm", "Transformation": null, "Value Map": {"Not shockable": "Not Shockable"}}, {"Target": "initcarhythmunk", "Source": "initcarhythmunk", "Transformation": null, "Value Map": null}, {"Target": "resuscdatetime", "Source": "resuscdatetime", "Transformation": null, "Value Map": null}, {"Target": "resuscitationtimeunknown", "Source": "resuscitationtimeunknown", "Transformation": null, "Value Map": null}, {"Target": "catransferfac", "Source": "catransferfac", "Transformation": null, "Value Map": null}, {"Target": "uncon", "Source": "uncon", "Transformation": null, "Value Map": null}, {"Target": "height", "Source": "heightad", "Transformation": null, "Value Map": null}, {"Target": "weight", "Source": "weightad", "Transformation": null, "Value Map": null}, {"Target": "hxcvd", "Source": "hxcvd", "Transformation": null, "Value Map": null}, {"Target": "strokeba", "Source": "strokeba", "Transformation": null, "Value Map": null}, {"Target": "hxtia", "Source": "hxtia", "Transformation": null, "Value Map": null}, {"Target": "diabetesba", "Source": "diabetesba", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "priorhfff", "Source": "priorhfff", "Transformation": null, "Value Map": null}, {"Target": "hypertension", "Source": "hypertension", "Transformation": null, "Value Map": null}, {"Target": "tobaccouse", "Source": "tobaccouse", "Transformation": null, "Value Map": {"Unknown": "Unknown if ever smoked"}}, {"Target": "e<PERSON><PERSON><PERSON>", "Source": "e<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "hxafib", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurenceArrival", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": 312850006, "Derivative Value": "Atrial Fibrillation", "Derivative Value Code": 49436004, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "atrialflutter", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurenceArrival", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": 312850006, "Derivative Value": "Atrial Flutter", "Derivative Value Code": 5370000, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "cancer", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurenceArrival", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": 312850006, "Derivative Value": "Cancer", "Derivative Value Code": 363346000, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dyslipidemia", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurenceArrival", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": 312850006, "Derivative Value": "Dyslipidemia", "Derivative Value Code": 370992007, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "<PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurenceArrival", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": 312850006, "Derivative Value": "Myocardial Infarction", "Derivative Value Code": 22298006, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "priorpad", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurenceArrival", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": 312850006, "Derivative Value": "Peripheral Arterial Disease", "Derivative Value Code": 399957001, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "priorcabg", "Source": null, "Transformation": {"Reference Field": "ProcHxOccurrenceArrival", "Reference Field Code": 416940007, "Derivative Field": "ProcedHxName", "Derivative Field Code": 416940007, "Derivative Value": "Coronary Artery Bypass Graft", "Derivative Value Code": 232717009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "priorcabgdate", "Source": null, "Transformation": {"Reference Field": "ProcHistDateArrival", "Reference Field Code": 416940007, "Derivative Field": "ProcedHxName", "Derivative Field Code": 416940007, "Derivative Value": "Coronary Artery Bypass Graft", "Derivative Value Code": 232717009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "priorpci", "Source": null, "Transformation": {"Reference Field": "ProcHxOccurrenceArrival", "Reference Field Code": 416940007, "Derivative Field": "ProcedHxName", "Derivative Field Code": 416940007, "Derivative Value": "Percutaneous Coronary Intervention", "Derivative Value Code": 415070008, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "lastpcidate", "Source": null, "Transformation": {"Reference Field": "ProcHistDateArrival", "Reference Field Code": 416940007, "Derivative Field": "ProcedHxName", "Derivative Field Code": 416940007, "Derivative Value": "Percutaneous Coronary Intervention", "Derivative Value Code": 415070008, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "locfirsteval", "Source": "locfirsteval", "Transformation": null, "Value Map": {"Emergency department (ED)": "ED", "Cath lab": "Cath Lab", "Observation unit": "Observation"}}, {"Target": "hrfirstmedcon", "Source": "hrfirstmedcon", "Transformation": null, "Value Map": null}, {"Target": "sbpfirstmedcon", "Source": "sbpfirstmedcon", "Transformation": null, "Value Map": null}, {"Target": "shockfirstmedcon", "Source": "shockfirstmedcon", "Transformation": null, "Value Map": null}, {"Target": "hffirstmedcon", "Source": "hffirstmedcon", "Transformation": null, "Value Map": null}, {"Target": "cshascalearrival", "Source": "cshascalearrival", "Transformation": null, "Value Map": {"7: Severely frail": " 7: Severely frail"}}, {"Target": "chestpainsymp", "Source": "chestpainsymp", "Transformation": null, "Value Map": null}, {"Target": "symptomdate24", "Source": "symptomdate24", "Transformation": null, "Value Map": null}, {"Target": "symptomtime24", "Source": "symptomtime24", "Transformation": null, "Value Map": null}, {"Target": "timeunknownsymptompriorarrival", "Source": "timeunknownsymptompriorarrival", "Transformation": null, "Value Map": null}, {"Target": "datechestpainsymptomafterar<PERSON>val", "Source": "datechestpainsymptomafterar<PERSON>val", "Transformation": null, "Value Map": null}, {"Target": "timechestpainsymptoma<PERSON><PERSON><PERSON>val", "Source": "timechestpainsymptoma<PERSON><PERSON><PERSON>val", "Transformation": null, "Value Map": null}, {"Target": "timeunknownsymptomafterarrival", "Source": "timeunknownsymptomafterarrival", "Transformation": null, "Value Map": null}, {"Target": "tropnotdrawn", "Source": "tropnotdrawn", "Transformation": null, "Value Map": null}, {"Target": "troponinprotocol", "Source": "troponinprotocol", "Transformation": null, "Value Map": null}, {"Target": "cathlabact", "Source": "cathlabact", "Transformation": null, "Value Map": null}, {"Target": "cathlabactdatetime", "Source": "cathlabactdatetime", "Transformation": null, "Value Map": null}, {"Target": "cat<PERSON><PERSON><PERSON><PERSON>", "Source": "cat<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "pcioparrdatetime", "Source": "pcioparrdatetime", "Transformation": null, "Value Map": null}, {"Target": "cathlabstaffarrdatetime", "Source": "cathlabstaffarrdatetime", "Transformation": null, "Value Map": null}, {"Target": "cathlabactcanc", "Source": "cathlabactcanc", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "riskstratification", "Source": "riskstratification", "Transformation": null, "Value Map": null}, {"Target": "riskstranotdocumented", "Source": "riskstranotdocumented", "Transformation": null, "Value Map": null}, {"Target": "riskstratperftransferfacility", "Source": "riskstratperftransferfacility", "Transformation": null, "Value Map": null}, {"Target": "riskassessmenttool", "Source": "riskassessmenttool", "Transformation": null, "Value Map": null}, {"Target": "riskassessmenttoolnotdocumented", "Source": "riskassessmenttoolnotdocumented", "Transformation": null, "Value Map": null}, {"Target": "functest<PERSON>ult", "Source": "functest<PERSON>ult", "Transformation": null, "Value Map": null}, {"Target": "anatomicalimaging<PERSON>ult", "Source": "anatomicalimaging<PERSON>ult", "Transformation": null, "Value Map": null}, {"Target": "cadtype", "Source": "cadtype", "Transformation": null, "Value Map": null}, {"Target": "sdmtool", "Source": "sdmtool", "Transformation": null, "Value Map": null}, {"Target": "ischemiaeval", "Source": "ischemiaeval", "Transformation": null, "Value Map": {"No - No reason": "No - No Reason", "No - Medical reason": "No - Medical Reason", "No - Patient reason": "No - Patient Reason"}}, {"Target": "ischemiaevalmethod", "Source": "ischemiaevalmethod", "Transformation": null, "Value Map": null}, {"Target": "ischemiaevalorddatetime", "Source": "ischemiaevalorddatetime", "Transformation": null, "Value Map": null}, {"Target": "ischemiaevalperfdatetime", "Source": "ischemiaevalperfdatetime", "Transformation": null, "Value Map": null}, {"Target": "ischemiaassessmentresult", "Source": "ischemiaassessmentresult", "Transformation": null, "Value Map": null}, {"Target": "ctaperformed", "Source": "ctaperformed", "Transformation": null, "Value Map": {"No - No reason": "No - No Reason", "No - Medical reason": "No - Medical Reason", "No - Patient reason": "No - Patient Reason"}}, {"Target": "ctaorddatetime", "Source": "ctaorddatetime", "Transformation": null, "Value Map": null}, {"Target": "ctaperfdatetime", "Source": "ctaperfdatetime", "Transformation": null, "Value Map": null}, {"Target": "homemedacei", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "ACE Inhibitors", "Derivative Value Code": 41549009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemedangiotensinii", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "ARB (Angiotensin Receptor Blockers)", "Derivative Value Code": 372913009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "ARNI", "Derivative Value Code": 112000001832, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "home<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "Beta Blocker", "Derivative Value Code": 33252009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemedprasugrel", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "Prasug<PERSON>", "Derivative Value Code": 613391, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "preprocasa", "Source": "medsarrival", "Transformation": null, "Value Map": null}, {"Target": "fmccreat", "Source": "fmccreat", "Transformation": null, "Value Map": null}, {"Target": "creatnotdrawn", "Source": "creatnotdrawn", "Transformation": null, "Value Map": null}, {"Target": "peakcreat", "Source": "creatpeak", "Transformation": null, "Value Map": null}, {"Target": "creatpeaknotdrawn", "Source": "creatpeaknotdrawn", "Transformation": null, "Value Map": null}, {"Target": "creatpeakdatetime", "Source": "creatpeakdatetime", "Transformation": null, "Value Map": null}, {"Target": "inithgbvalue", "Source": "inithgbvalue", "Transformation": null, "Value Map": null}, {"Target": "hgbnotdrawn", "Source": "hgbnotdrawn", "Transformation": null, "Value Map": null}, {"Target": "lowhgbvalue", "Source": "lowhgbvalue", "Transformation": null, "Value Map": null}, {"Target": "lowhgbnotdrawn", "Source": "lowhgbnotdrawn", "Transformation": null, "Value Map": null}, {"Target": "lowhgbvaluedatetime", "Source": "lowhgbvaluedatetime", "Transformation": null, "Value Map": null}, {"Target": "hba1cvalue", "Source": "hba1cvalue", "Transformation": null, "Value Map": null}, {"Target": "hba1cnotdrawn", "Source": "hba1cnotdrawn", "Transformation": null, "Value Map": null}, {"Target": "initinrvalue", "Source": "initinrvalue", "Transformation": null, "Value Map": null}, {"Target": "inrnotdrawn", "Source": "inrnotdrawn", "Transformation": null, "Value Map": null}, {"Target": "lipidstc6mfmc", "Source": "lipidstc6mfmc", "Transformation": null, "Value Map": null}, {"Target": "totalcholnotdrawn", "Source": "totalcholnotdrawn", "Transformation": null, "Value Map": null}, {"Target": "lipidshdl6mfmc", "Source": "lipidshdl6mfmc", "Transformation": null, "Value Map": null}, {"Target": "hdlnotdrawn", "Source": "hdlnotdrawn", "Transformation": null, "Value Map": null}, {"Target": "lipidsldl", "Source": "lipidsldl", "Transformation": null, "Value Map": null}, {"Target": "ldlnotdrawn", "Source": "ldlnotdrawn", "Transformation": null, "Value Map": null}, {"Target": "lipidstrig", "Source": "lipidstrig", "Transformation": null, "Value Map": null}, {"Target": "triglycnotdrawn", "Source": "triglycnotdrawn", "Transformation": null, "Value Map": null}, {"Target": "diagcorangiofirst", "Source": "diagcorangio1st", "Transformation": null, "Value Map": {"No - No reason": "No - No Reason", "No - Medical reason": "No - Medical Reason", "No - Patient reason": "No - Patient Reason", "No - System reason": "No - System Reason"}}, {"Target": "dcathlname", "Source": "dcathlname", "Transformation": null, "Value Map": null}, {"Target": "dcathfname", "Source": "dcathfname", "Transformation": null, "Value Map": null}, {"Target": "dcathmname", "Source": "dcathmname", "Transformation": null, "Value Map": null}, {"Target": "dcathnpi", "Source": "dcathnpi", "Transformation": null, "Value Map": null}, {"Target": "catharrivaldatetime", "Source": "catharrivaldatetime", "Transformation": null, "Value Map": null}, {"Target": "diagcorangiodatetime", "Source": "diagcorangiodatetime", "Transformation": null, "Value Map": null}, {"Target": "angio<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "angio<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "angio<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "angio<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "cardiacangioresults", "Source": "cardiacangioresults", "Transformation": null, "Value Map": null}, {"Target": "cadtype2", "Source": "cadtype2", "Transformation": null, "Value Map": null}, {"Target": "thromtherapyfmc", "Source": "thromtherapyfmc", "Transformation": null, "Value Map": {"Yes ": "Yes", "No - No reason": "No - No Reason", "No - Medical reason": "No - Medical Reason", "No - Patient reason": "No - Patient Reason"}}, {"Target": "thromdatetimefmc", "Source": "thromdatetimefmc", "Transformation": null, "Value Map": null}, {"Target": "medreadelay", "Source": "medreadelay", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "pciarrivdc2", "Source": "pciarrivdc2", "Transformation": null, "Value Map": {"No - No reason": "No - No Reason", "No - Medical reason": "No - Medical Reason", "No - Patient reason": "No - Patient Reason"}}, {"Target": "cabgfirst2", "Source": "cabgfirst2", "Transformation": null, "Value Map": {"No - No reason": "No - No Reason", "No - Medical reason": "No - Medical Reason", "No - Patient reason": "No - Patient Reason"}}, {"Target": "cabgdatetime", "Source": "cabgdatetime", "Transformation": null, "Value Map": null}, {"Target": "pcidatetime", "Source": "pcidatetime", "Transformation": null, "Value Map": null}, {"Target": "pcilname", "Source": "pcilname", "Transformation": null, "Value Map": null}, {"Target": "pcifname", "Source": "pcifname", "Transformation": null, "Value Map": null}, {"Target": "pcimname", "Source": "pcimname", "Transformation": null, "Value Map": null}, {"Target": "pcinpi", "Source": "pcinpi", "Transformation": null, "Value Map": null}, {"Target": "fit_lastname", "Source": "fit_lastname", "Transformation": null, "Value Map": null}, {"Target": "fit_firstname", "Source": "fit_firstname", "Transformation": null, "Value Map": null}, {"Target": "fit_midname", "Source": "fit_midname", "Transformation": null, "Value Map": null}, {"Target": "fit_npi", "Source": "fit_npi", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "pciindication", "Source": "pci_indication", "Transformation": null, "Value Map": {"STEMI - Immediate PCI for acute STEMI": "STEMI – Primary PCI for Acute STEMI", "Unstable angina": "Unstable <PERSON><PERSON>"}}, {"Target": "mechventsupp", "Source": "mechventsupp", "Transformation": null, "Value Map": null}, {"Target": "mvsupportdevice", "Source": "mvsupportdevice", "Transformation": null, "Value Map": null}, {"Target": "accesssite", "Source": "accesssite", "Transformation": null, "Value Map": null}, {"Target": "stentsimplanted", "Source": "stentsimplanted", "Transformation": null, "Value Map": null}, {"Target": "stenttype", "Source": "stenttypefv", "Transformation": null, "Value Map": {"Bare Metal Stent": "BMS", "Drug-Eluting Stent": "DES"}}, {"Target": "currstenttype<PERSON>", "Source": "currstenttype<PERSON>", "Transformation": null, "Value Map": null}, {"Target": "cerbc", "Source": "ce_rbc", "Transformation": null, "Value Map": null}, {"Target": "cerbcdate", "Source": "ce_rbcdate", "Transformation": null, "Value Map": null}, {"Target": "cerbccabg", "Source": "ce_rbccabg", "Transformation": null, "Value Map": null}, {"Target": "nsaidadmin", "Source": "nsaidadmin", "Transformation": null, "Value Map": null}, {"Target": "medreanotadminnsaid", "Source": "medreanotadminnsaid", "Transformation": null, "Value Map": null}, {"Target": "dcdatetime", "Source": "dcdatetime", "Transformation": null, "Value Map": null}, {"Target": "dischargestatus", "Source": "dcstatus", "Transformation": null, "Value Map": null}, {"Target": "deathcause", "Source": "deathcause", "Transformation": null, "Value Map": null}, {"Target": "dclname", "Source": "dclname", "Transformation": null, "Value Map": null}, {"Target": "dcfname", "Source": "dcfname", "Transformation": null, "Value Map": null}, {"Target": "dcmname", "Source": "dcmname", "Transformation": null, "Value Map": null}, {"Target": "dcnpi", "Source": "dcnpi", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON>udy", "Source": "<PERSON><PERSON>udy", "Transformation": null, "Value Map": null}, {"Target": "dc_lvef", "Source": "dc_lvef", "Transformation": null, "Value Map": {"No - No reason": "No - No Reason", "No - Medical reason": "No - Medical Reason", "No - Patient reason": "No - Patient Reason"}}, {"Target": "lvef<PERSON><PERSON>", "Source": "lvef<PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "lvefplandc", "Source": "lvefplandc", "Transformation": null, "Value Map": null}, {"Target": "lvefplandcnotindicated", "Source": "lvefplandcnotindicated", "Transformation": null, "Value Map": null}, {"Target": "cpcscore", "Source": "cpcscore", "Transformation": null, "Value Map": null}, {"Target": "hospclinictrial", "Source": "hospclinictrial", "Transformation": null, "Value Map": null}, {"Target": "typeclintrial", "Source": "typeclintrial", "Transformation": null, "Value Map": null}, {"Target": "dischargecomfort", "Source": "dc_comfort", "Transformation": null, "Value Map": null}, {"Target": "dccomfortdatetime", "Source": "commeasdatetime", "Transformation": null, "Value Map": null}, {"Target": "dchospice", "Source": "dchospice", "Transformation": null, "Value Map": null}, {"Target": "hospcaredatetime", "Source": "hospcaredatetime", "Transformation": null, "Value Map": null}, {"Target": "dischargecardrehab", "Source": "dc_cardrehab", "Transformation": null, "Value Map": {"No - Reason not documented": "No - Reason Not Documented", "No - Medical reason documented": "No - Medical Reason Documented", "No - Health care system reason documented": "No - Health Care System Reason Documented", "No - Patient-oriented reason": "No - Patient - Oriented Reason"}}, {"Target": "dischargelocation", "Source": "dclocation", "Transformation": null, "Value Map": {"Skilled nursing facility": "Skilled Nursing facility", "Extended care/transitional care unit/Rehab": "Discharged/transferred to an Extended care/TCU/rehab", "Other": "Other Discharge Location"}}, {"Target": "transdatetime", "Source": "transdatetime", "Transformation": null, "Value Map": null}, {"Target": "transdelayptcenteredreason", "Source": "transdelayptcenteredreason", "Transformation": null, "Value Map": null}, {"Target": "cardiacevaluation", "Source": "cardiacevaluation", "Transformation": null, "Value Map": null}, {"Target": "transferpci", "Source": "transpci", "Transformation": null, "Value Map": null}, {"Target": "cabgtransferdc", "Source": "transcabg", "Transformation": null, "Value Map": null}, {"Target": "cshascaledc", "Source": "cshascaledc", "Transformation": null, "Value Map": {" 7: Severely frail": "7: Severely frail"}}, {"Target": "dischargeasa", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON> (Any)", "Derivative Value Code": 1191, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": {"Yes": "Yes - Prescribed", "No - No Reason": "Not Prescribed - No Reason"}}, {"Target": "dc_asa_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON> (Any)", "Derivative Value Code": 1191, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_clopidogrel", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Clopidogrel", "Derivative Value Code": 32968, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": {"Yes": "Yes - Prescribed", "No - No Reason": "Not Prescribed - No Reason", "No - Medical Reason": "Not Prescribed - Medical Reason"}}, {"Target": "dc_clop<PERSON>g<PERSON>_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Clopidogrel", "Derivative Value Code": 32968, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_prasugrel", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Prasug<PERSON>", "Derivative Value Code": 613391, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": {"No - No Reason": "Not Prescribed - No Reason", "No - Medical Reason": "Not Prescribed - Medical Reason", "Yes": "Yes - Prescribed"}}, {"Target": "dc_prasug<PERSON>_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Prasug<PERSON>", "Derivative Value Code": 613391, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_ticagrelor", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Ticagrelor", "Derivative Value Code": 1116632, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": {"No - No Reason": "Not Prescribed - No Reason", "Yes": "Yes - Prescribed", "No - Medical Reason": "Not Prescribed - Medical Reason"}}, {"Target": "dc_ticag<PERSON><PERSON>_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Ticagrelor", "Derivative Value Code": 1116632, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dischargebetablocker", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Beta blocker (Any)", "Derivative Value Code": 33252009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": {"Yes": "Yes - Prescribed", "No - Medical Reason": "Not Prescribed - Medical Reason"}}, {"Target": "dc_betablocker_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Beta blocker (Any)", "Derivative Value Code": 33252009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_ace_inhibitor_any", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Angiotensin converting enzyme inhibitor (ACE-I) (Any)", "Derivative Value Code": 41549009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": {"No - No Reason": "Not Prescribed - No Reason", "Yes": "Yes - Prescribed", "No - Medical Reason": "Not Prescribed - Medical Reason"}}, {"Target": "dc_aceinhibitors_any_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Angiotensin converting enzyme inhibitor (ACE-I) (Any)", "Derivative Value Code": 41549009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_arb_any", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Angiotensin receptor blocker (ARB) (Any)", "Derivative Value Code": 372913009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": {"Yes": "Yes - Prescribed", "No - No Reason": "Not Prescribed - No Reason", "No - Medical Reason": "Not Prescribed - Medical Reason"}}, {"Target": "dc_arb_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Angiotensin receptor blocker (ARB) (Any)", "Derivative Value Code": 372913009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_arni", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Angiotensin II receptor blocker neprilysin inhibitor (ARNI)", "Derivative Value Code": 112000001832, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": {"No - No Reason": "Not Prescribed - No Reason", "No - Medical Reason": "Not Prescribed - Medical Reason", "Yes": "Yes - Prescribed"}}, {"Target": "dc_arni_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Angiotensin II receptor blocker neprilysin inhibitor (ARNI)", "Derivative Value Code": 112000001832, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_ara_any", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Aldosterone receptor antagonist (Any)", "Derivative Value Code": 372603003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": {"No - No Reason": "Not Prescribed - No Reason", "Yes": "Yes - Prescribed"}}, {"Target": "dc_ara_any_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Aldosterone receptor antagonist (Any)", "Derivative Value Code": 372603003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_doac_any", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Direct oral anticoagulants (DOAC) (Any)", "Derivative Value Code": 112000001416, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": {"No - No Reason": "Not Prescribed - No Reason", "Yes": "Yes - Prescribed"}}, {"Target": "dc_doac_any_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Direct oral anticoagulants (DOAC) (Any)", "Derivative Value Code": 112000001416, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_warfarin", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Warfarin", "Derivative Value Code": 11289, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": {"No - No Reason": "Not Prescribed - No Reason", "Yes": "Yes - Prescribed"}}, {"Target": "dc_warfarin_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Warfarin", "Derivative Value Code": 11289, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dischargestatinany", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON> (Any)", "Derivative Value Code": 96302009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": {"Yes": "Yes - Prescribed", "No - Medical Reason": "Not Prescribed - Medical Reason"}}, {"Target": "dc_statin_any_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON> (Any)", "Derivative Value Code": 96302009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": {"High": "High Intensity Dose", "Moderate": "Moderate Intensity Dose", "Low": "Low Intensity Dose"}}, {"Target": "dc_aspirin100mgplus", "Source": "dc_aspirin100mgplus", "Transformation": null, "Value Map": null}, {"Target": "reasonnohighdose", "Source": "reasonnohighdose", "Transformation": null, "Value Map": null}, {"Target": "eddisposition", "Source": "eddisposition", "Transformation": null, "Value Map": null}, {"Target": "tranouteddatetime", "Source": "tranouteddatetime", "Transformation": null, "Value Map": null}, {"Target": "obserorderdatetime", "Source": "obserorderdatetime", "Transformation": null, "Value Map": null}, {"Target": "stemifirstnotedfmc1stecg", "Source": "stemifirstnotedfmc1stecg", "Transformation": null, "Value Map": null}, {"Target": "firstdevactidatetime", "Source": "firstdevactidatetime", "Transformation": null, "Value Map": null}, {"Target": "ptp<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "ptp<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": {"Difficult vascular access": "Difficult Vascular Access", "Emergent placement of left ventricular support device": "Emergent placement of LV support device before PCI", "Cardiac arrest and/or need for intubation": "Cardiac Arrest and/or need for intubation before PCI"}}, {"Target": "insmedicaid", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Medicaid", "Derivative Value Code": 2, "Value Map": {"|Medicaid|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "hypothermiainducedfmcdc", "Source": "hypothermiainducedfmcdc", "Transformation": null, "Value Map": null}, {"Target": "hypothermiainduceddatetime", "Source": "hypothermiainduceddatetime", "Transformation": null, "Value Map": null}, {"Target": "ptloctempmanagement", "Source": "ptloctempmanagement", "Transformation": null, "Value Map": {"Emergency Department": "ED"}}, {"Target": "initialtargettempgoal", "Source": "initialtargettempgoal", "Transformation": null, "Value Map": null}, {"Target": "targettempachieveddatetime", "Source": "targettempachieveddatetime", "Transformation": null, "Value Map": null}, {"Target": "rewarminginitiateddatetime", "Source": "rewarminginitiateddatetime", "Transformation": null, "Value Map": null}, {"Target": "nonusinsurance", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Non-US Insurance", "Derivative Value Code": 100000812, "Value Map": {"|Non-US Insurance|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "bleedingatrialfibrillation", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Atrial fibrillation", "Derivative Value Code": 49436004, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedingatrialfibdatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Atrial fibrillation", "Derivative Value Code": 49436004, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedingaccesssite", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Access site", "Derivative Value Code": 1000142440, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedingaccesssitedatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Access site", "Derivative Value Code": 1000142440, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedinggastrointestinal", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Gastrointestinal", "Derivative Value Code": 74474003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedinggastrointestinaldatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Gastrointestinal", "Derivative Value Code": 74474003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedinggenitourinary", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Genitourinary", "Derivative Value Code": 417941003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedinggenitourinarydatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Genitourinary", "Derivative Value Code": 417941003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedinghematomaatsite", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Hematoma at access site", "Derivative Value Code": 385494008, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedinghematomaatsitedatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Hematoma at access site", "Derivative Value Code": 385494008, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedingother", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Other", "Derivative Value Code": 1000142371, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedingotherdatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Other", "Derivative Value Code": 1000142371, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedingretroperitoneal", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Retroperitoneal", "Derivative Value Code": 95549001, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedingretrodatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Retroperitoneal", "Derivative Value Code": 95549001, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedingsurgicalproc", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Surgical procedure or intervention required", "Derivative Value Code": 112000000213, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedingsurgprocdatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Surgical procedure or intervention required", "Derivative Value Code": 112000000213, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "cardiac<PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Cardiac arrest", "Derivative Value Code": 410429000, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "cardiacarrestdatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Cardiac arrest", "Derivative Value Code": 410429000, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "cardiogenicshock", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Cardiogenic shock", "Derivative Value Code": 89138009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "cardiogenicshockdatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Cardiogenic shock", "Derivative Value Code": 89138009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "heartfailure", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Heart failure", "Derivative Value Code": 84114007, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "heartfailuredatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Heart failure", "Derivative Value Code": 84114007, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "myocardialinfarction", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Myocardial infarction", "Derivative Value Code": 22298006, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "myocardialinfarctiondatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Myocardial infarction", "Derivative Value Code": 22298006, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "newrequirementfordialysis", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "New requirement for dialysis", "Derivative Value Code": 100014076, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dialysisdatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "New requirement for dialysis", "Derivative Value Code": 100014076, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bipap", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Respiratory support - Bi-PAP", "Derivative Value Code": 243142003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bipapdatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Respiratory support - Bi-PAP", "Derivative Value Code": 243142003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "highflowoxygen", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Respiratory support - High-flow oxygen", "Derivative Value Code": 426854004, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "highflowoxygendatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Respiratory support - High-flow oxygen", "Derivative Value Code": 426854004, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "intubation", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Respiratory support - Intubation", "Derivative Value Code": 52765003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "intubationdatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Respiratory support - Intubation", "Derivative Value Code": 52765003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "strokehemorrhagic", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Stroke - Hemorrhagic", "Derivative Value Code": 230706003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "strokehemorrhagicdatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Stroke - Hemorrhagic", "Derivative Value Code": 230706003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "strokeischemic", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Stroke - Ischemic", "Derivative Value Code": 422504002, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "strokeischemicdatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Stroke - Ischemic", "Derivative Value Code": 422504002, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "strokeundetermined", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Stroke - Undetermined", "Derivative Value Code": 230713003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "strokeundetermineddatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Stroke - Undetermined", "Derivative Value Code": 230713003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "transischemicattack", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Transient ischemic attack (TIA)", "Derivative Value Code": 266257000, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "transischemicattackdatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Transient ischemic attack (TIA)", "Derivative Value Code": 266257000, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "ventfib", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Ventricular fibrillation", "Derivative Value Code": 71908006, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "ventfibdatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Ventricular fibrillation", "Derivative Value Code": 71908006, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "venttachy", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Sustained ventricular tachycardia", "Derivative Value Code": 25569003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "venttachydatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Sustained ventricular tachycardia", "Derivative Value Code": 25569003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "electrocardiorowid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "ecgcounter", "Source": "ecgcounter", "Transformation": null, "Value Map": null}, {"Target": "ecgdatetime", "Source": "ecgdatetime", "Transformation": null, "Value Map": null}, {"Target": "stemifirstnotedfmc", "Source": "stemifirstnotedfmc", "Transformation": null, "Value Map": null}, {"Target": "troponinrowid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "tropcount", "Source": "tropcount", "Transformation": null, "Value Map": null}, {"Target": "tropcolldatetime", "Source": "tropcolldatetime", "Transformation": null, "Value Map": null}, {"Target": "tropresdatetime", "Source": "tropresdatetime", "Transformation": null, "Value Map": null}, {"Target": "troptestloc", "Source": "troptestloc", "Transformation": null, "Value Map": null}, {"Target": "labtropassay", "Source": "labtropassay", "Transformation": null, "Value Map": null}, {"Target": "poctropassay", "Source": "poctropassay", "Transformation": null, "Value Map": null}, {"Target": "troponinvalue", "Source": "troponinvalue", "Transformation": null, "Value Map": null}, {"Target": "datavrsn", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "originalotherid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "hospname", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "filename", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "version", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "datasetname", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "clientfileid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "biomeimportdt", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "careentityid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "tenantid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "diabetes", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "arrivaltime", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "priorhf", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "ednpi", "Source": "ednpi", "Transformation": null, "Value Map": null}, {"Target": "statespecificplan", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "State-Specific Plan (non-Medicaid)", "Derivative Value Code": 36, "Value Map": {"|State-Specific Plan (non-Medicaid)|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "symptomdate", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "symptomtime", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "privatehealthinsurance", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Private Health Insurance", "Derivative Value Code": 5, "Value Map": {"|Private Health Insurance|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "raceamericanindianalaskannative", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "raceblackafricanamerican", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "indianhealthservice", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Indian Health Service", "Derivative Value Code": 33, "Value Map": {"|Indian Health Service|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "medicaid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "medicare", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "miltaryhealthcare", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Military Health Care", "Derivative Value Code": 31, "Value Map": {"|Military Health Care|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "tropassayurl", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "initinrnd", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "lipidstrignd", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "dc_aldo<PERSON><PERSON>_meddose", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "edfname", "Source": "edfname", "Transformation": null, "Value Map": null}, {"Target": "ed<PERSON><PERSON>", "Source": "ed<PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "edmname", "Source": "edmname", "Transformation": null, "Value Map": null}, {"Target": "lvefafterdischarge", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "transfercabg", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "causeofdeath", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "lvefplandafterdc", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "currentdialysis", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "insihs", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "stroke", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "biomeencounterid", "Source": null, "Transformation": null, "Value Map": null}]}}