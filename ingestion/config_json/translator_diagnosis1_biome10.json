{"TranslatorConfig": {"Version": "1.0", "Source": {"Dataset": {"Code": "DIAGNOSIS", "Id": 163}, "Version": "1", "Type": "<PERSON><PERSON><PERSON>"}, "Target": {"Dataset": {"Code": "DIAGNOSIS", "Id": 163}, "Version": "1.0", "Type": "Biome"}, "Translations": [{"Target": "clientfileid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "filename", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "version", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "poa", "Source": "poa", "Transformation": null, "Value Map": {"0 - Not Specified": "Not Specified", "1": "Y", "1 - Yes": "Y", "2": "N", "2 - No": "N", "3": "U", "3 - Unknown": "U", "4": "W", "5": "E", "5 - Exempt": "E", "5 - Exempt from POA ": "E", "5 - Exempt from POA reporting": "E", "E - Condition is exempt from POA reporting (assigned by OSHPD).": "E", "E - Exempt": "E", "N - Condition was NOT present at time of inpatient admission": "N", "N - No": "N", "U - Documentation is insufficient to determine if the condition is present at time of admission": "U", "U - Unknown": "U", "W - Provider is unable to clin": "W", "W - Provider is unable to clinically determine whether the condition was present at time of admission": "W", "Y - Condition was present at time of inpatient admission": "Y", "Y - Yes": "Y"}, "Computation": null}, {"Target": "datasetname", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "diagtype", "Source": "diagtype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "diagseqno", "Source": "diagseqno", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "encounternumber", "Source": "encounternumber", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "diagcode", "Source": "diagcode", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tenantid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "biomeencounterid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "biomeimportdt", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}]}}