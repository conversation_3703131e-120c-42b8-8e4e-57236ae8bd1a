{"TranslatorConfig": {"Version": "1.0", "Source": {"Dataset": {"Code": "Discharges", "Id": 108}, "Version": "1", "Type": "<PERSON><PERSON><PERSON>"}, "Target": {"Dataset": {"Code": "Discharges", "Id": 108}, "Version": "1.0", "Type": "Biome"}, "Translations": [{"Target": "dischargestatus", "Source": "dischargestatus", "Transformation": null, "Value Map": {"STEMI - Unstable (>12 hrs from Sx)": "STEMI - Unstable (> 12 hrs from Sx)"}, "Computation": null}, {"Target": "careentityid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "los", "Source": "los", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "datasetname", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "prindx9code", "Source": "prindx9code", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tenantid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "indirectcost", "Source": "indirectcost", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "scientific_to_decimal", "Args": {"default": "0", "format_str": ".20f"}, "is_computation": null}, {"Transform_Type": "subtract", "Args": {"fields": ["totalcost", "directcost"], "format_str": ".20f"}, "is_computation": true}]}, {"Target": "gender", "Source": "gender", "Transformation": null, "Value Map": {"1": "Male", "2": "Female", "F": "Female", "M": "Male", "U": "Unknown", "MALE": "Male", "FEMALE": "Female", "UNKNOWN": "Unknown", "N": "Unknown"}, "Computation": [{"Transform_Type": "simple_clean", "Args": {"empty_values": [""]}, "is_computation": null}]}, {"Target": "originalmrn", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": "medrecn"}, "is_computation": true}]}, {"Target": "medrecn", "Source": "medrecn", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "secondarypayor", "Source": "secondarypayor", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "prindx10code", "Source": "prindx10code", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "admitdx9code", "Source": "admitdx9code", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "biomeencounterid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dob", "Source": "dob", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "inoutcode", "Source": "inoutcode", "Transformation": null, "Value Map": {"I": "Inpatient", "O": "Outpatient", "CCU": "Inpatient", "SNF": "Unknown", "MICU": "Inpatient", "SICU": "Inpatient", "OP-ER": "Outpatient", "Other": "Unknown", "Trauma": "Unknown", "Private": "Inpatient", "[Blank]": "Unknown", "Oncology": "Unknown", "EMERGENCY": "Unknown", "INPATIENT": "Inpatient", "Inpatient": "Inpatient", "Isolation": "Unknown", "Burn Acute": "Unknown", "IP REGULAR": "Inpatient", "OUTPATIENT": "Outpatient", "Outpatient": "Outpatient", "Acute Rehab": "Unknown", "GIP Hospice": "Unknown", "IP-DECEASED": "Inpatient", "Medical ICU": "Inpatient", "Observation": "Unknown", "Psych Adult": "Unknown", "EMERGENCY-IP": "Inpatient", "EMERGENCY-OP": "Outpatient", "OP-RECURRING": "Outpatient", "REHAB IN BED": "Unknown", "Semi-Private": "Inpatient", "Surgical ICU": "Inpatient", "Trauma Acute": "Unknown", "IP-DISCHARGED": "Inpatient", "OP-NONPATIENT": "Outpatient", "OP-OUTPATIENT": "Outpatient", "SURGERY ADMIT": "Inpatient", "TRANSPLANT-IP": "Inpatient", "Cardiology ICU": "Inpatient", "Emergency Room": "Unknown", "Monitored Care": "Unknown", "EXP ORGAN DONOR": "Inpatient", "REHAB INPATIENT": "Inpatient", "IP MENTAL HEALTH": "Inpatient", "Same Day Surgery": "Unknown", "Medical -Surgical": "Inpatient", "Ambulatory Surgery": "Unknown", "IP SKILLED NURSING": "Inpatient", "OBS IN BED/CHARGED": "Unknown", "OUT PATIENT IN BED": "Outpatient", "OUTPATIENT SURGERY": "Outpatient", "Inpatient Admission": "Inpatient", "OP SURGERY (NO BED)": "Outpatient", "Step-Down-Monitored": "Inpatient", "Higher Nursing Ratio": "Inpatient", "Recurring Outpatient": "Outpatient", "EXT RECOV/OBS IN BED/NC": "Unknown", "Burn Inpatient Admission": "Inpatient", "OBS NOT IN BED/NOT CHARG": "Unknown", "Trauma Inpatient Admission": "Inpatient", "Inpatients": "Inpatient", "Outpatients": "Outpatient", "inp": "Inpatient", "out": "Outpatient", "ER": "Outpatient", "Emergency Department - Encount": "ED", "Hospital Outpatient Lab": "Outpatient", "Hospital Outpatient Surgery": "Outpatient", "Lab Referred Specimen": "Outpatient", "Outpatient Burn Clinic Visit -": "Outpatient", "Outpatient Burn Therapy - Seri": "Outpatient", "Outpatient Cardiac Rehab - Ser": "Outpatient", "Outpatient Clinic Visit - Enco": "Outpatient", "Outpatient Infusion - Series": "Outpatient", "Outpatient Labor and Delivery": "Outpatient", "Outpatient Occupational Therap": "Outpatient", "Outpatient Other - Encounter": "Outpatient", "Outpatient Radiation Oncology": "Outpatient", "Outpatient Radiology - Encount": "Outpatient", "Outpatient Speech Therapy - Se": "Outpatient", "Unknown": "Unknown", "Outpatient Nuclear Medicine - ": "Outpatient", "Outpatient Physical Therapy - ": "Outpatient", "Emergency Department - Encounter": "ED", "Outpatient Burn Therapy - Series": "Outpatient", "Outpatient Cardiac Rehab - Series": "Outpatient", "Outpatient Clinic Visit - Encounter": "Outpatient", "Outpatient Nuclear Medicine - Series": "Outpatient", "Outpatient Occupational Therapy - Series": "Outpatient", "Outpatient Physical Therapy - Series": "Outpatient", "Outpatient Radiation Oncology - Series": "Outpatient", "Outpatient Radiology - Encounter": "Outpatient", "INPATIENTS": "Inpatient", "OUTPATIENTS": "Outpatient", "OUT": "Outpatient", "INP": "Inpatient"}, "Computation": [{"Transform_Type": "fallback_fields", "Args": {"fallback_fields": ["patienttype", "patienttypecode"], "default": "Outpatient"}, "is_computation": true}]}, {"Target": "admittype", "Source": "admittype", "Transformation": null, "Value Map": {"Delivery C Sec/IOL Scheduled": "Delivery", "Delivery C Sec/IOL Unsched": "Delivery", "ELECTIVE": "Elective", "NEWBORN": "Newborn", "Routine/Elective": "Elective", "Transplant/Donor": "Transplant", "Transplant/Donor (All < 24hrs)": "Transplant", "TRAUMA": "<PERSON>rauma", "TRAUMA CENTER": "<PERSON>rauma", "Unknown": "Not Specified", "UNSCHEDULED ADMIT EMERGENT": "Emergent", "URGENT": "<PERSON><PERSON>", "Involuntary Emergent NPH": "Emergency", "Involuntary Urgent NPH": "<PERSON><PERSON>", "Obstetrics - Elective": "Elective", "Voluntary Elective NPH": "Elective", "Info Not Available": "Information Not Available", "Unscheduled Admit Emergent": "Emergent", "Trauma Center--Elective": "Trauma--Elective", "Trauma Center--Emergency": "Trauma--Emergency", "Emergency--Trauma Center--Elective": "Emergency--Trauma--Elective", "Trauma Center": "<PERSON>rauma", "Newborn--Trauma Center": "Trauma--Newborn", "Urgent--Trauma Center--Elective": "Urgent--Trauma--Elective", "Trauma Center--Information Not Available": "Trauma--Information Not Available", "Urgent--Trauma Center": "Urgent--Trauma", "Trauma Center--Elective--Information Not Available": "Trauma--Elective--Information Not Available", "Newborn--Trauma Center--Elective": "Trauma--Newborn--Elective"}, "Computation": [{"Transform_Type": "fallback_fields", "Args": {"fallback_fields": ["admittypecode"]}, "is_computation": true}]}, {"Target": "primaryinsurancegroup", "Source": "primaryinsurancegroup", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dctime", "Source": "dctime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "admitdate", "Source": "admitdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "icudays", "Source": "icudays", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "directcost", "Source": "directcost", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "scientific_to_decimal", "Args": {"default": "0", "format_str": ".20f"}, "is_computation": null}, {"Transform_Type": "subtract", "Args": {"fields": ["totalcost", "indirectcost"], "format_str": ".20f"}, "is_computation": true}]}, {"Target": "age", "Source": "age", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "encounternumber", "Source": "encounternumber", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "patzip", "Source": "patzip", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "patienttype", "Source": "patienttype", "Transformation": null, "Value Map": {"I": "Inpatient", "O": "OutPatient"}, "Computation": [{"Transform_Type": "fallback_fields", "Args": {"fallback_fields": ["inoutcode", "patienttypecode"], "default": "Outpatient"}, "is_computation": true}]}, {"Target": "race", "Source": "race", "Transformation": null, "Value Map": {"Asian": "Asian", "Black": "Black or African American", "OTHER": "Other", "White": "White", "KOREAN": "Asian", "SAMOAN": "Hawaiian or Pacific Islander", "CHINESE": "Asian", "LAOTIAN": "Asian", "Unknown": "Unknown", "Declined": "Unknown", "FILIPINO": "Asian", "HAWAIIAN": "Hawaiian or Pacific Islander", "JAPANESE": "Asian", "CAMBODIAN": "Asian", "CAUCASIAN": "White", "GUAMANIAN": "Hawaiian or Pacific Islander", "PAKISTANI": "Asian/Indian", "Other Race": "Other", "VIETNAMESE": "Asian", "Asian Other": "Asian", "Asian: Thai": "Asian", "ASIAN INDIAN": "Asian/Indian", "Asian Indian": "Asian/Indian", "Asian: Other": "Asian", "Alaska Native": "American Indian or Alaska Native", "Asian: Korean": "Asian", "LAOTIAN/HMONG": "Asian", "NOT AVAILABLE": "Unknown", "Asian: Chinese": "Asian", "MIDDLE EASTERN": "Asian/ME", "OTHER HISPANIC": "Hawaiian or Pacific Islander", "OTHER SE ASIAN": "Asian", "American Indian": "American Indian or Alaska Native", "Asian: Filipino": "Asian", "Asian: Japanese": "Asian", "Pacific-Hawaiin": "Hawaiian or Pacific Islander", "Pacific/Hawaiin": "Hawaiian or Pacific Islander", "Patient Refused": "Unknown", "Asian: Pakistani": "Asian/Indian", "Asian: Taiwanese": "Asian", "DECLINE TO STATE": "Unknown", "MEXICAN AMERICAN": "American Indian or Alaska Native", "PACIFIC ISLANDER": "Hawaiian or Pacific Islander", "Unknown/Declined": "Unknown", "Asian: Indonesian": "Asian", "Asian: Vietnamese": "Asian", "White or Caucasian": "White", "Asian: Asian Indian": "Asian/Indian", "Declined to Specify": "Unknown", "OTHER ASIAN/MIDEAST": "Asian/ME", "Guamanian or Chamorro": "Hawaiian or Pacific Islander", "BLACK/AFRICAN AMERICAN": "Black or African American", "Pacific Islander Other": "Hawaiian or Pacific Islander", "UNAVAILABLE OR UNKNOWN": "Unknown", "AMERICAN INDIAN/ALASKAN": "American Indian or Alaska Native", "Black: African American": "Black or African American", "Pacific Islander: Other": "Hawaiian or Pacific Islander", "OTHER RACE OR MIXED RACE": "Other", "Pacific Islander: Samoan": "Hawaiian or Pacific Islander", "AFRICAN AMERICAN OR BLACK": "Black or African American", "Black or African American": "Black or African American", "PACIFIC ISLANDER/HAWAIIAN": "Hawaiian or Pacific Islander", "AMERICAN INDIAN OR ALASKAN": "American Indian or Alaska Native", "Native Hawaiian or Other Pacif": "Hawaiian or Pacific Islander", "Unknown (Patient cannot or ref": "Unknown", "AMERICAN INDIAN OR ALASKA NATIVE": "American Indian or Alaska Native", "American Indian or Alaska Native": "American Indian or Alaska Native", "Pacific Islander: Native Hawaiian": "Hawaiian or Pacific Islander", "Pacific Islander: Guamanian or Chamorro": "Hawaiian or Pacific Islander", "NATIVE HAWAIIAN OR OTHER PACIFIC ISLANDER": "Hawaiian or Pacific Islander", "UNKNOWN (PATIENT CANNOT OR REFUSES TO DECLARE RACE": "Unknown", "CHINESE (INACTIVE)": "Asian", "FILIPINO (INACTIVE)": "Asian", "IRANIAN (INACTIVE)": "Other", "JAPANESE (INACTIVE)": "Asian", "LAOTIAN/HMONG (INACTIVE)": "Asian", "OTHER HISPANIC (INACTIVE)": "Other Hispanic", "PAKISTANI (INACTIVE)": "Asian/ME", "UNABLE TO RESPOND": "Unknown(<PERSON><PERSON> cannot or refuses to declare race)", "UNKNOWN (INACTIVE)": "Unknown", "VIETNAMESE (INACTIVE)": "Asian"}, "Computation": null}, {"Target": "admittime", "Source": "admittime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dischargedate", "Source": "dischargedate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "drgcode", "Source": "drgcode", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "totalcost", "Source": "totalcost", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "scientific_to_decimal", "Args": {"default": "0", "format_str": ".20f"}, "is_computation": null}, {"Transform_Type": "add", "Args": {"fields": ["directcost", "indirectcost"], "format_str": ".20f"}, "is_computation": true}]}, {"Target": "admitdx10code", "Source": "admitdx10code", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "filename", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "expectedpayment", "Source": "expectedpayment", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "prinicd10proccode", "Source": "prinicd10proccode", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "netpatientrevenue", "Source": "netpatientrevenue", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hospname", "Source": "hospname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "version", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "admitsource", "Source": "admitsource", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "primarypayor", "Source": "primarypayor", "Transformation": null, "Value Map": {"Medicare - FFS": "Medicare", "Medicare - Risk": "Medicare", "County": "Medicare", "Commercial - FFS": "Commercial", "Exchange - FFS": "Commercial", "Commercial - Risk": "Commercial", "Medi-Cal - FFS": "MediCal", "Medi-Cal - Risk": "MediCal", "Other (excludes County)": "Other", "ALAMEDA ALLIANCE COVERED CALIFORNIA MANAGED MEDI-C": "MediCal", "ALAMEDA ALLIANCE MANAGED MEDI-CAL": "MediCal", "COMMERCIAL": "Commercial", "COVERED CALIFORNIA MEDI-CAL": "MediCal", "MEDI-CAL MANAGED CARE": "MediCal", "MEDI-CAL STANDARD": "MediCal", "MEDICAID/MIA/CMSP": "Medicaid", "MEDICARE": "Medicare", "MEDICARE ADVANTAGE": "Medicare", "MEDICARE ADVANTAGE HMO/SENIOR": "Medicare", "OTHER GOVERNMENT": "Other", "PARTNERSHIP COVERED CALIFORNIA MANAGED MEDI-CAL": "MediCal", "PARTNERSHIP MANAGED MEDI-CAL": "MediCal", "SELF-PAY": "Self Pay", "AETNA": "Commercial", "BLUE CROSS": "Commercial", "BLUE SHIELD": "Commercial", "CAPITATION": "Commercial", "CAPITATION SENIOR": "Commercial", "CCS/GHPP": "Commercial", "CIGNA": "Commercial", "COVERED CALIFORNIA": "Commercial", "HEALTHNET": "Commercial", "KAISER": "Commercial", "UNITED HEALTH CARE": "Commercial", "WORKERS COMP": "Commercial", "INSTITUTIONAL": "Commercial", "RESEARCH": "Commercial", "Subcap": "Commercial", "Covered CA": "Other", "Capitated Contracts": "Commercial", "Domestic": "Commercial", "Public Aid": "Medicaid", "Uninsured": "Self Pay", "Auto": "Other", "HMO SENIOR": "Commercial", "INDEMNITY": "Other", "PPO": "Commercial", "TRICARE": "Other", "AETNA HMO/PPO": "Commercial", "BC ALLIANCE": "Commercial", "BJC EMPLOYEE HEALTH PLANS": "Commercial", "CIGNA HMO/PPO": "Commercial", "GLOBAL/TRANSPLANT": "Commercial", "HEALTHCARE EXCHANGE": "Commercial", "HEALTHLINK HMO/PPO": "Commercial", "ORGANIZATIONAL BILLING": "Other", "SECURE HORIZONS": "Commercial", "UHC HMO/PPO": "Commercial", "WORKERS COMPENSATION": "Other"}, "Computation": [{"Transform_Type": "pattern_match", "Args": {"mappings": [{"match": "MEDICARE", "result": "Medicare"}, {"match": "MEDICAL|MEDI-CAL", "result": "MediCal"}, {"match": "SELF|UNINSURED", "result": "Self Pay"}, {"match": "MEDICAID", "result": "Medicaid"}, {"match": "OTHER|PUBLIC AID", "result": "Other"}, {"match": "COMMERCIAL", "result": "Commercial"}], "default_value": "Not available"}, "is_computation": null}]}, {"Target": "prinicd9proccode", "Source": "prinicd9proccode", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "referringmdrole", "Source": "referringmdrole", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "financialpayorclass", "Source": "financialpayorclass", "Transformation": null, "Value Map": {"COMMERCIAL/HMO/PPO/UCSD": "Commercial", "MEDI-CAL & MEDI-CAL MANAGED CARE": "Medical", "MEDICARE": "Medicare", "SELF PAY & CHARITY": "Self Pay", "Blue Cross Blue Shield": "Commercial", "Medicaid Managed Care": "Medicaid", "Medicaid Out of State": "Medicaid", "Medicare Replacement": "Medicare", "Self-Pay": "Self Pay", "Tricare": "Commercial", "Worker's Comp": "Other"}, "Computation": [{"Transform_Type": "split_join", "Args": {"source_field": "financialpayorclasscode", "split_char": "-", "extract_part": "suffix"}, "is_computation": true}]}, {"Target": "servicesitename", "Source": "servicesitename", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ethnicity", "Source": "ethnicity", "Transformation": null, "Value Map": {"other Asian/Mideastern": "Other Asian/Mideastern", "Unreported (Patient refused or chose not to disclose)": "Unknown (Patient cannot or refuses to declare ethnicity)", "Other Hispanic Latino(a) or Spanish origin": "Hispanic", "Not Specified": "Unknown", "Not Hispanic Latino-a or Spanish origin": "Not Hispanic or Latino"}, "Computation": null}, {"Target": "attendingmdname", "Source": "attendingmdname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "refferingphyname", "Source": "refferingphyname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "attendingmdnpi", "Source": "attendingmdnpi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "servicelinerollup", "Source": "servicelinerollup", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "clientfileid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "biomeimportdt", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "distressscore", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "distressquintile", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "yearmonth", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "distfromhospital", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "charges", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}]}}