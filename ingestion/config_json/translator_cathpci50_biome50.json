{"TranslatorConfig": {"Version": "5.0", "Source": {"Dataset": {"Code": "CathPCI", "Id": 100}, "Version": "5.0", "Type": "<PERSON><PERSON><PERSON>"}, "Target": {"Dataset": {"Code": "CathPCI", "Id": 100}, "Version": "5.0", "Type": "Biome"}, "Translations": [{"Target": "ncdrpatientid", "Source": "ncdrpatientid", "Transformation": null, "Value Map": null}, {"Target": "partid", "Source": "partid", "Transformation": null, "Value Map": null}, {"Target": "partname", "Source": "partname", "Transformation": null, "Value Map": null}, {"Target": "firstname", "Source": "firstname", "Transformation": null, "Value Map": null}, {"Target": "lastname", "Source": "lastname", "Transformation": null, "Value Map": null}, {"Target": "midname", "Source": "midname", "Transformation": null, "Value Map": null}, {"Target": "ssnna", "Source": "ssnna", "Transformation": null, "Value Map": null}, {"Target": "ssn", "Source": "ssn", "Transformation": null, "Value Map": null}, {"Target": "otherid", "Source": "otherid", "Transformation": null, "Value Map": null}, {"Target": "dob", "Source": "dob", "Transformation": null, "Value Map": null}, {"Target": "gender", "Source": "sex", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "zipcodena", "Transformation": null, "Value Map": null}, {"Target": "patzip", "Source": "zipcode", "Transformation": null, "Value Map": null}, {"Target": "racewhite", "Source": "racewhite", "Transformation": null, "Value Map": null}, {"Target": "raceblackafricanamerican", "Source": "raceblack", "Transformation": null, "Value Map": null}, {"Target": "raceamericanindianalaskannative", "Source": "raceamindian", "Transformation": null, "Value Map": null}, {"Target": "raceasian", "Source": "raceasian", "Transformation": null, "Value Map": null}, {"Target": "raceasianindian", "Source": "raceasianindian", "Transformation": null, "Value Map": null}, {"Target": "racechinese", "Source": "racechinese", "Transformation": null, "Value Map": null}, {"Target": "racefilipino", "Source": "racefilipino", "Transformation": null, "Value Map": null}, {"Target": "racejapanese", "Source": "racejapanese", "Transformation": null, "Value Map": null}, {"Target": "racekorean", "Source": "racekorean", "Transformation": null, "Value Map": null}, {"Target": "racevietnamese", "Source": "racevietnamese", "Transformation": null, "Value Map": null}, {"Target": "raceotherasian", "Source": "raceasianother", "Transformation": null, "Value Map": null}, {"Target": "racenathawpacificislander", "Source": "racenathaw", "Transformation": null, "Value Map": null}, {"Target": "racenathaw", "Source": "racenathaw", "Transformation": null, "Value Map": null}, {"Target": "raceguamanianorchamorro", "Source": "raceguamchamorro", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "raceother<PERSON>ci<PERSON><PERSON>lander", "Source": "race<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "hispanicethnicitytypemexicanmexicanamericanchicano", "Source": "hispethnicitymexican", "Transformation": null, "Value Map": null}, {"Target": "hispanicethnicitytypepuertorican", "Source": "hispethnicitypuertorico", "Transformation": null, "Value Map": null}, {"Target": "hispanicethnicitytypecuban", "Source": "hispethnicitycuban", "Transformation": null, "Value Map": null}, {"Target": "hispanicethnicity<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "hispethnicityotherorigin", "Transformation": null, "Value Map": null}, {"Target": "episodeid", "Source": "episodekey", "Transformation": null, "Value Map": null}, {"Target": "arrivaldatetime", "Source": "arrivaldatetime", "Transformation": null, "Value Map": null}, {"Target": "admfname", "Source": "admfname", "Transformation": null, "Value Map": null}, {"Target": "admlname", "Source": "admlname", "Transformation": null, "Value Map": null}, {"Target": "admmidname", "Source": "admmname", "Transformation": null, "Value Map": null}, {"Target": "admnpi", "Source": "admnpi", "Transformation": null, "Value Map": null}, {"Target": "healthins", "Source": "healthins", "Transformation": null, "Value Map": null}, {"Target": "medicare", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Medicare", "Derivative Value Code": 1, "Value Map": {"|Medicare|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "medicaid", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Medicaid", "Derivative Value Code": 2, "Value Map": {"|Medicaid|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "privatehealthinsurance", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Private Health Insurance", "Derivative Value Code": 5, "Value Map": {"|Private Health Insurance|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "miltaryhealthcare", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Military Health Care", "Derivative Value Code": 31, "Value Map": {"|Military Health Care|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "statespecificplan", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "State-Specific Plan (non-Medicaid)", "Derivative Value Code": 36, "Value Map": {"|State-Specific Plan (non-Medicaid)|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "indianhealthservice", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Indian Health Service", "Derivative Value Code": 33, "Value Map": {"|Indian Health Service|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "nonusinsurance", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Non-US Insurance", "Derivative Value Code": "100000812", "Value Map": {"|Non-US Insurance|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "hic", "Source": "hic", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON>udy", "Source": "<PERSON><PERSON>udy", "Transformation": null, "Value Map": null}, {"Target": "ptrestriction2", "Source": "ptrestriction2", "Transformation": null, "Value Map": null}, {"Target": "attnlname", "Source": "attlname", "Transformation": null, "Value Map": null}, {"Target": "attnfname", "Source": "attfname", "Transformation": null, "Value Map": null}, {"Target": "attnmidname", "Source": "attmname", "Transformation": null, "Value Map": null}, {"Target": "attnnpi", "Source": "attnpi", "Transformation": null, "Value Map": null}, {"Target": "studyname", "Source": "studyname", "Transformation": null, "Value Map": null}, {"Target": "studypatientid", "Source": "studyptid", "Transformation": null, "Value Map": null}, {"Target": "hypertension", "Source": "hypertension", "Transformation": null, "Value Map": null}, {"Target": "dyslipidemia", "Source": "dyslipidemia", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON>", "Source": "hxmi", "Transformation": null, "Value Map": null}, {"Target": "priormidate", "Source": "hxmidate", "Transformation": null, "Value Map": null}, {"Target": "priorpci", "Source": "priorpci", "Transformation": null, "Value Map": null}, {"Target": "priorpcidate", "Source": "hxpcidate", "Transformation": null, "Value Map": null}, {"Target": "lmpci", "Source": "lmpci", "Transformation": null, "Value Map": null}, {"Target": "lmpciunk", "Source": "lmpciunk", "Transformation": null, "Value Map": null}, {"Target": "height", "Source": "height", "Transformation": null, "Value Map": null}, {"Target": "weight", "Source": "weight", "Transformation": null, "Value Map": null}, {"Target": "familyhxcad", "Source": "familyhxcad", "Transformation": null, "Value Map": null}, {"Target": "hxcvd", "Source": "hxcvd", "Transformation": null, "Value Map": null}, {"Target": "priorpad", "Source": "priorpad", "Transformation": null, "Value Map": null}, {"Target": "hxchroniclungdisease", "Source": "hxchroniclungdisease", "Transformation": null, "Value Map": null}, {"Target": "hxpriorcabg", "Source": "priorcabg", "Transformation": null, "Value Map": null}, {"Target": "hxcabgdate", "Source": "hxcabgdate", "Transformation": null, "Value Map": null}, {"Target": "tobaccouse", "Source": "tobaccouse", "Transformation": null, "Value Map": null}, {"Target": "tobaccotype", "Source": "tobaccotype", "Transformation": null, "Value Map": null}, {"Target": "smokingamount", "Source": "smokeamount", "Transformation": null, "Value Map": null}, {"Target": "caouthospital", "Source": "caouthospital", "Transformation": null, "Value Map": null}, {"Target": "cawitness", "Source": "cawitness", "Transformation": null, "Value Map": null}, {"Target": "capostems", "Source": "capostems", "Transformation": null, "Value Map": null}, {"Target": "initcarhythmunk", "Source": "initcarhythmunk", "Transformation": null, "Value Map": null}, {"Target": "initcarhythm", "Source": "initcarhythm", "Transformation": null, "Value Map": null}, {"Target": "catransferfac", "Source": "catransferfac", "Transformation": null, "Value Map": null}, {"Target": "diabetes", "Source": "diabetes", "Transformation": null, "Value Map": null}, {"Target": "currentdialysis", "Source": "currentdialysis", "Transformation": null, "Value Map": null}, {"Target": "cshascale", "Source": "cshascale", "Transformation": null, "Value Map": null}, {"Target": "proceduredate", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "procedureenddatetime", "Source": "procedureenddatetime", "Transformation": null, "Value Map": null}, {"Target": "diagcorangio", "Source": "diagcorangio", "Transformation": null, "Value Map": null}, {"Target": "dcathlname", "Source": "dcathlname", "Transformation": null, "Value Map": null}, {"Target": "dcathfname", "Source": "dcathfname", "Transformation": null, "Value Map": null}, {"Target": "dcathmid<PERSON>", "Source": "dcathmname", "Transformation": null, "Value Map": null}, {"Target": "dcathnpi", "Source": "dcathnpi", "Transformation": null, "Value Map": null}, {"Target": "pciproc", "Source": "pciproc", "Transformation": null, "Value Map": null}, {"Target": "pcilname", "Source": "pcilname", "Transformation": null, "Value Map": null}, {"Target": "pcifname", "Source": "pcifname", "Transformation": null, "Value Map": null}, {"Target": "pcimidname", "Source": "pcimname", "Transformation": null, "Value Map": null}, {"Target": "pcinpi", "Source": "pcinpi", "Transformation": null, "Value Map": null}, {"Target": "leftheartcath", "Source": "leftheartcath", "Transformation": null, "Value Map": null}, {"Target": "prepcilvef", "Source": "prepcilvef", "Transformation": null, "Value Map": null}, {"Target": "concomproc", "Source": "concomproc", "Transformation": null, "Value Map": null}, {"Target": "concomproctype", "Source": "concomproctype", "Transformation": null, "Value Map": null}, {"Target": "accesssite", "Source": "accesssite", "Transformation": null, "Value Map": null}, {"Target": "crossover", "Source": "crossover", "Transformation": null, "Value Map": null}, {"Target": "closuremethodna", "Source": "closuremethodna", "Transformation": null, "Value Map": null}, {"Target": "venousaccess", "Source": "venousaccess", "Transformation": null, "Value Map": null}, {"Target": "procsystolicbp", "Source": "procsystolicbp", "Transformation": null, "Value Map": null}, {"Target": "cainhosp", "Source": "cainhosp", "Transformation": null, "Value Map": null}, {"Target": "fluorotime", "Source": "fluorotime", "Transformation": null, "Value Map": null}, {"Target": "contrastvol", "Source": "contrastvol", "Transformation": null, "Value Map": null}, {"Target": "fluorodosekerm", "Source": "fluorodosekerm", "Transformation": null, "Value Map": null}, {"Target": "fluorodosedap", "Source": "fluorodosedap", "Transformation": null, "Value Map": null}, {"Target": "hxhf", "Source": "hxhf", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "hfnewdiag", "Source": "hfnewdiag", "Transformation": null, "Value Map": null}, {"Target": "hftype", "Source": "hftype", "Transformation": null, "Value Map": null}, {"Target": "hftypeunk", "Source": "hftypeunk", "Transformation": null, "Value Map": null}, {"Target": "preprocantiarrhythmicagentother", "Source": null, "Transformation": {"Reference Field": "PreProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "PreProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Antiarrhythmic Agent Other", "Derivative Value Code": 100014162, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "preprocasa", "Source": null, "Transformation": {"Reference Field": "PreProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "PreProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON>", "Derivative Value Code": 1191, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "preproc<PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "PreProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "PreProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Beta Blocker", "Derivative Value Code": 33252009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "preproccalciumchannelblocking", "Source": null, "Transformation": {"Reference Field": "PreProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "PreProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Calcium Channel Blocking Agent", "Derivative Value Code": 48698004, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "preproclongactingnitrate", "Source": null, "Transformation": {"Reference Field": "PreProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "PreProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON> Acting Nitrate", "Derivative Value Code": 31970009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "preprocnonstatin", "Source": null, "Transformation": {"Reference Field": "PreProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "PreProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Non-Statin", "Derivative Value Code": 100014161, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "preprocranolazine", "Source": null, "Transformation": {"Reference Field": "PreProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "PreProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Ranolazine", "Derivative Value Code": 35829, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "preprocstatinany", "Source": null, "Transformation": {"Reference Field": "PreProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "PreProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Statin", "Derivative Value Code": 96302009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "preprocpcsk9", "Source": null, "Transformation": {"Reference Field": "PreProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "PreProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Proprotein Convertase Subtilisin Kexin Type 9 Inhibitor", "Derivative Value Code": 112000000694, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "ecas<PERSON><PERSON><PERSON><PERSON>", "Source": "ecas<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "ecgresults", "Source": "ecgresults", "Transformation": null, "Value Map": null}, {"Target": "antiarrhytherapy", "Source": "antiarrhytherapy", "Transformation": null, "Value Map": null}, {"Target": "ecgfindings", "Source": "ecgfindings", "Transformation": null, "Value Map": null}, {"Target": "hr", "Source": "hr", "Transformation": null, "Value Map": null}, {"Target": "nonsustainedvttype", "Source": "nsvttype", "Transformation": null, "Value Map": null}, {"Target": "stressperformed", "Source": "stressperformed", "Transformation": null, "Value Map": null}, {"Target": "cardiaccta", "Source": "cardiaccta", "Transformation": null, "Value Map": null}, {"Target": "cardiacctadate", "Source": "cardiacctadate", "Transformation": null, "Value Map": null}, {"Target": "cardiacctaresults", "Source": "cardiacctaresults", "Transformation": null, "Value Map": null}, {"Target": "cardiacctaresultsunk", "Source": "cardiacctaresultsunk", "Transformation": null, "Value Map": null}, {"Target": "calciumscoreassessed", "Source": "calciumscoreassessed", "Transformation": null, "Value Map": null}, {"Target": "calciumscore", "Source": "calciumscore", "Transformation": null, "Value Map": null}, {"Target": "calciumscoredate", "Source": "calciumscoredate", "Transformation": null, "Value Map": null}, {"Target": "preproclvefassessed", "Source": "preproclvefassessed", "Transformation": null, "Value Map": null}, {"Target": "preproclvef", "Source": "preproclvef", "Transformation": null, "Value Map": null}, {"Target": "priordxangioproc", "Source": "priordxangioproc", "Transformation": null, "Value Map": null}, {"Target": "priordxangiodate", "Source": "priordxangiodate", "Transformation": null, "Value Map": null}, {"Target": "priordxangioresults", "Source": "priordxangioresults", "Transformation": null, "Value Map": null}, {"Target": "priordxcathresultsunk", "Source": "priordxcathresultsunk", "Transformation": null, "Value Map": null}, {"Target": "exercisestresstest", "Source": null, "Transformation": {"Reference Field": "StressTestType", "Reference Field Code": 1000142432, "Derivative Field": "StressTestType", "Derivative Field Code": 1000142432, "Derivative Value": "Exercise Stress Test (w/o imaging)", "Derivative Value Code": "18752-6", "Value Map": {"Exercise Stress Test (w/o imaging)": "Yes"}, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "stressecho", "Source": null, "Transformation": {"Reference Field": "StressTestType", "Reference Field Code": 1000142432, "Derivative Field": "StressTestType", "Derivative Field Code": 1000142432, "Derivative Value": "Stress Echocardiogram", "Derivative Value Code": "18107-3", "Value Map": {"Stress Echocardiogram": "Yes"}, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "stressnuclear", "Source": null, "Transformation": {"Reference Field": "StressTestType", "Reference Field Code": 1000142432, "Derivative Field": "StressTestType", "Derivative Field Code": 1000142432, "Derivative Value": "Stress Nuclear", "Derivative Value Code": "49569-7", "Value Map": {"Stress Nuclear": "Yes"}, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "cmrstresstest", "Source": null, "Transformation": {"Reference Field": "StressTestType", "Reference Field Code": 1000142432, "Derivative Field": "StressTestType", "Derivative Field Code": 1000142432, "Derivative Value": "Stress Imaging with CMR", "Derivative Value Code": "58750-1", "Value Map": {"Stress Imaging with CMR": "Yes"}, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "exercisetestresult", "Source": null, "Transformation": {"Reference Field": "StressTestResult", "Reference Field Code": 10001424303, "Derivative Field": "StressTestType", "Derivative Field Code": 1000142432, "Derivative Value": "Exercise Stress Test (w/o imaging)", "Derivative Value Code": "18752-6", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "exercisetestresultdate", "Source": null, "Transformation": {"Reference Field": "StressTestDate", "Reference Field Code": 1000142431, "Derivative Field": "StressTestType", "Derivative Field Code": 1000142432, "Derivative Value": "Exercise Stress Test (w/o imaging)", "Derivative Value Code": "18752-6", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "exercisetestrisk", "Source": null, "Transformation": {"Reference Field": "StressTestRisk", "Reference Field Code": 1000142434, "Derivative Field": "StressTestType", "Derivative Field Code": 1000142432, "Derivative Value": "Exercise Stress Test (w/o imaging)", "Derivative Value Code": "18752-6", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "stressechoresult", "Source": null, "Transformation": {"Reference Field": "StressTestResult", "Reference Field Code": 10001424303, "Derivative Field": "StressTestType", "Derivative Field Code": 1000142432, "Derivative Value": "Stress Echocardiogram", "Derivative Value Code": "18107-3", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "stressechoresultdate", "Source": null, "Transformation": {"Reference Field": "StressTestDate", "Reference Field Code": 1000142431, "Derivative Field": "StressTestType", "Derivative Field Code": 1000142432, "Derivative Value": "Stress Echocardiogram", "Derivative Value Code": "18107-3", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "stressechorisk", "Source": null, "Transformation": {"Reference Field": "StressTestRisk", "Reference Field Code": 1000142434, "Derivative Field": "StressTestType", "Derivative Field Code": 1000142432, "Derivative Value": "Stress Echocardiogram", "Derivative Value Code": "18107-3", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "StressTestResult", "Reference Field Code": 10001424303, "Derivative Field": "StressTestType", "Derivative Field Code": 1000142432, "Derivative Value": "Stress Nuclear", "Derivative Value Code": "49569-7", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "stressnuclearresultdate", "Source": null, "Transformation": {"Reference Field": "StressTestDate", "Reference Field Code": 1000142431, "Derivative Field": "StressTestType", "Derivative Field Code": 1000142432, "Derivative Value": "Stress Nuclear", "Derivative Value Code": "49569-7", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "StressTestRisk", "Reference Field Code": 1000142434, "Derivative Field": "StressTestType", "Derivative Field Code": 1000142432, "Derivative Value": "Stress Nuclear", "Derivative Value Code": "49569-7", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "cmrstressresults", "Source": null, "Transformation": {"Reference Field": "StressTestResult", "Reference Field Code": 10001424303, "Derivative Field": "StressTestType", "Derivative Field Code": 1000142432, "Derivative Value": "Stress Imaging with CMR", "Derivative Value Code": "58750-1", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "cmrstresstestresultdate", "Source": null, "Transformation": {"Reference Field": "StressTestDate", "Reference Field Code": 1000142431, "Derivative Field": "StressTestType", "Derivative Field Code": 1000142432, "Derivative Value": "Stress Imaging with CMR", "Derivative Value Code": "58750-1", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "cmrstressrisk", "Source": null, "Transformation": {"Reference Field": "StressTestRisk", "Reference Field Code": 1000142434, "Derivative Field": "StressTestType", "Derivative Field Code": 1000142432, "Derivative Value": "Stress Imaging with CMR", "Derivative Value Code": "58750-1", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "closurecounter", "Source": "closurecounter", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON>", "Source": "<PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "preproctnind", "Source": "preproctnind", "Transformation": null, "Value Map": null}, {"Target": "<PERSON>p<PERSON><PERSON><PERSON>", "Source": "<PERSON>p<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "preproctntnd", "Source": "preproctntnd", "Transformation": null, "Value Map": null}, {"Target": "preproctnt", "Source": "preproctnt", "Transformation": null, "Value Map": null}, {"Target": "preproccreatnd", "Source": "preproccreatnd", "Transformation": null, "Value Map": null}, {"Target": "preproccreat", "Source": "preproccreat", "Transformation": null, "Value Map": null}, {"Target": "hgbnd", "Source": "hgbnd", "Transformation": null, "Value Map": null}, {"Target": "hgb", "Source": "hgb", "Transformation": null, "Value Map": null}, {"Target": "lipidstcnd", "Source": "lipidstcnd", "Transformation": null, "Value Map": null}, {"Target": "lipidstc", "Source": "lipidstc", "Transformation": null, "Value Map": null}, {"Target": "lipidshdlnd", "Source": "lipidshdlnd", "Transformation": null, "Value Map": null}, {"Target": "lipidshdl", "Source": "lipidshdl", "Transformation": null, "Value Map": null}, {"Target": "postproctnind", "Source": "postproctnind", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "postproctntnd", "Source": "postproctntnd", "Transformation": null, "Value Map": null}, {"Target": "postproctnt", "Source": "postproctnt", "Transformation": null, "Value Map": null}, {"Target": "postproccreatnd", "Source": "postproccreatnd", "Transformation": null, "Value Map": null}, {"Target": "postp<PERSON>cc<PERSON>t", "Source": "postp<PERSON>cc<PERSON>t", "Transformation": null, "Value Map": null}, {"Target": "postprochgbnd", "Source": "postprochgbnd", "Transformation": null, "Value Map": null}, {"Target": "postprochgb", "Source": "postprochgb", "Transformation": null, "Value Map": null}, {"Target": "acs_gt_24_hrs", "Source": null, "Transformation": {"Reference Field": "CathLabVisitIndication", "Reference Field Code": 100014000, "Derivative Field": "CathLabVisitIndication", "Derivative Field Code": 100014000, "Derivative Value": "ACS > 24 hrs", "Derivative Value Code": 1000142359, "Value Map": {"ACS > 24 hrs": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "lv_dysfunction", "Source": null, "Transformation": {"Reference Field": "CathLabVisitIndication", "Reference Field Code": 100014000, "Derivative Field": "CathLabVisitIndication", "Derivative Field Code": 100014000, "Derivative Value": "LV Dysfunction", "Derivative Value Code": 134401001, "Value Map": {"LV Dysfunction": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "new_onset_angina_lte_2months", "Source": null, "Transformation": {"Reference Field": "CathLabVisitIndication", "Reference Field Code": 100014000, "Derivative Field": "CathLabVisitIndication", "Derivative Field Code": 100014000, "Derivative Value": "New Onset Angina <= 2 months", "Derivative Value Code": 233821000, "Value Map": {"New Onset Angina <= 2 months": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "suspected_cad", "Source": null, "Transformation": {"Reference Field": "CathLabVisitIndication", "Reference Field Code": 100014000, "Derivative Field": "CathLabVisitIndication", "Derivative Field Code": 100014000, "Derivative Value": "Suspected CAD", "Derivative Value Code": 100014003, "Value Map": {"Suspected CAD": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "cpsxa<PERSON>s", "Source": "cpsxa<PERSON>s", "Transformation": null, "Value Map": null}, {"Target": "cvinstability", "Source": "cvinstability", "Transformation": null, "Value Map": null}, {"Target": "cvinstabilitytype", "Source": "cvinstabilitytype", "Transformation": null, "Value Map": null}, {"Target": "vsupport", "Source": "vsupport", "Transformation": null, "Value Map": null}, {"Target": "pharm<PERSON><PERSON><PERSON><PERSON>", "Source": "pharm<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "mechventsupp", "Source": "mechventsupp", "Transformation": null, "Value Map": null}, {"Target": "mvsupporttiming", "Source": "mvsupporttiming", "Transformation": null, "Value Map": null}, {"Target": "mvsupportdevice", "Source": "mvsupportdevice", "Transformation": null, "Value Map": null}, {"Target": "preopevalforsurgtype", "Source": "preopeval", "Transformation": null, "Value Map": null}, {"Target": "funccapacityna", "Source": "funccapacityna", "Transformation": null, "Value Map": null}, {"Target": "funccapacity", "Source": "funccapacity", "Transformation": null, "Value Map": null}, {"Target": "surgrisk", "Source": "surgrisk", "Transformation": null, "Value Map": null}, {"Target": "organtransplantsurg", "Source": "organtransplantsurg", "Transformation": null, "Value Map": null}, {"Target": "organtransplantdonor", "Source": "organtransplantdonor", "Transformation": null, "Value Map": null}, {"Target": "organtransplanttype", "Source": "organtransplanttype", "Transformation": null, "Value Map": {"|Other Organ|": "| Other Organ|"}}, {"Target": "cardiac_arrhythmia", "Source": null, "Transformation": {"Reference Field": "CathLabVisitIndication", "Reference Field Code": 100014000, "Derivative Field": "CathLabVisitIndication", "Derivative Field Code": 100014000, "Derivative Value": "Cardiac arrhythmia", "Derivative Value Code": 698247007, "Value Map": {"Cardiac arrhythmia": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "cardiolvsd", "Source": null, "Transformation": {"Reference Field": "CathLabVisitIndication", "Reference Field Code": 100014000, "Derivative Field": "CathLabVisitIndication", "Derivative Field Code": 100014000, "Derivative Value": "Cardiomyopathy", "Derivative Value Code": 85898001, "Value Map": {"Cardiomyopathy": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "acs_lte_24_hrs", "Source": null, "Transformation": {"Reference Field": "CathLabVisitIndication", "Reference Field Code": 100014000, "Derivative Field": "CathLabVisitIndication", "Derivative Field Code": 100014000, "Derivative Value": "ACS <= 24 hrs", "Derivative Value Code": 1000142358, "Value Map": {"ACS <= 24 hrs": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "worsening_angina", "Source": null, "Transformation": {"Reference Field": "CathLabVisitIndication", "Reference Field Code": 100014000, "Derivative Field": "CathLabVisitIndication", "Derivative Field Code": 100014000, "Derivative Value": "Worsening Angina", "Derivative Value Code": 10001424790, "Value Map": {"Worsening Angina": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "periope<PERSON>", "Source": null, "Transformation": {"Reference Field": "CathLabVisitIndication", "Reference Field Code": 100014000, "Derivative Field": "CathLabVisitIndication", "Derivative Field Code": 100014000, "Derivative Value": "Pre-operative Evaluation", "Derivative Value Code": 1000142360, "Value Map": {"Pre-operative Evaluation": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "syncope", "Source": null, "Transformation": {"Reference Field": "CathLabVisitIndication", "Reference Field Code": 100014000, "Derivative Field": "CathLabVisitIndication", "Derivative Field Code": 100014000, "Derivative Value": "Syncope", "Derivative Value Code": 271594007, "Value Map": {"Syncope": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "stable_known_cad", "Source": null, "Transformation": {"Reference Field": "CathLabVisitIndication", "Reference Field Code": 100014000, "Derivative Field": "CathLabVisitIndication", "Derivative Field Code": 100014000, "Derivative Value": "Stable Known CAD", "Derivative Value Code": 100014001, "Value Map": {"Stable Known CAD": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "postcardiactransplant", "Source": null, "Transformation": {"Reference Field": "CathLabVisitIndication", "Reference Field Code": 100014000, "Derivative Field": "CathLabVisitIndication", "Derivative Field Code": 100014000, "Derivative Value": "Post Cardiac Transplant", "Derivative Value Code": 100014002, "Value Map": {"Post Cardiac Transplant": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "valvular_disease", "Source": null, "Transformation": {"Reference Field": "CathLabVisitIndication", "Reference Field Code": 100014000, "Derivative Field": "CathLabVisitIndication", "Derivative Field Code": 100014000, "Derivative Value": "Valvular Disease", "Derivative Value Code": 368009, "Value Map": {"Valvular Disease": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "resuscitated_cardiac_arrest", "Source": null, "Transformation": {"Reference Field": "CathLabVisitIndication", "Reference Field Code": 100014000, "Derivative Field": "CathLabVisitIndication", "Derivative Field Code": 100014000, "Derivative Value": "Resuscitated <PERSON><PERSON>st", "Derivative Value Code": 233927002, "Value Map": {"Resuscitated Cardiac Arrest": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "other", "Source": null, "Transformation": {"Reference Field": "CathLabVisitIndication", "Reference Field Code": 100014000, "Derivative Field": "CathLabVisitIndication", "Derivative Field Code": 100014000, "Derivative Value": "Other", "Derivative Value Code": 100000351, "Value Map": {"Other": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "valvulardzstenosistype", "Source": "valvulardzstenosistype", "Transformation": null, "Value Map": null}, {"Target": "valvulardzstenosissev", "Source": "valvulardzstenosissev", "Transformation": null, "Value Map": null}, {"Target": "valvulardzregurgtype", "Source": "valvulardzregurgtype", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "regurgseverity", "Transformation": null, "Value Map": {"Severe (4+)": "Severe", "Moderate (2+)": "Moderate", "Mild (1+)": "Mild", "Moderately Severe (3+)": "Moderately Severe"}}, {"Target": "dominance", "Source": "dominance", "Transformation": null, "Value Map": null}, {"Target": "nvstenosis", "Source": "nvstenosis", "Transformation": null, "Value Map": null}, {"Target": "graftstenosis", "Source": "graftstenosis", "Transformation": null, "Value Map": null}, {"Target": "nvsegmentid", "Source": "nvsegmentid", "Transformation": null, "Value Map": {"11b-  Mid-LM": "11b- Mid-LM", "11c -  Distal LM": "11c - Distal LM"}}, {"Target": "nvcorovesselstenosis", "Source": "nvcorovesselstenosis", "Transformation": null, "Value Map": null}, {"Target": "nvadjuncmeasobtained", "Source": "nvadjuncmeasobtained", "Transformation": null, "Value Map": null}, {"Target": "ffrnative", "Source": "nv_ffr", "Transformation": null, "Value Map": null}, {"Target": "nv_ifr_native", "Source": "nv_ifr", "Transformation": null, "Value Map": null}, {"Target": "ivusnative", "Source": "nv_ivus", "Transformation": null, "Value Map": null}, {"Target": "nv_oct_native", "Source": "nv_oct", "Transformation": null, "Value Map": null}, {"Target": "graftsegmentid", "Source": "graftsegmentid", "Transformation": null, "Value Map": {"11a - Ostial LM": "11a - Ost<PERSON> L", "11b-  Mid-LM": "11b- Mid-LM", "11c -  Distal LM": "11c - Distal LM"}}, {"Target": "graftcorovesselstenosis", "Source": "graftcorovesselstenosis", "Transformation": null, "Value Map": null}, {"Target": "cabggraftvesselunk", "Source": "cabggraftvesselunk", "Transformation": null, "Value Map": null}, {"Target": "cabggraftvessel", "Source": "cabggraftvessel", "Transformation": null, "Value Map": null}, {"Target": "graftadjuncmeasobtained", "Source": "graftadjuncmeasobtained", "Transformation": null, "Value Map": null}, {"Target": "ffrgraft", "Source": "graft_ffr", "Transformation": null, "Value Map": null}, {"Target": "gt_ifr", "Source": "graft_ifr", "Transformation": null, "Value Map": null}, {"Target": "ivusgraft", "Source": "graft_ivus", "Transformation": null, "Value Map": null}, {"Target": "gt_oct", "Source": "graft_oct", "Transformation": null, "Value Map": null}, {"Target": "p<PERSON><PERSON>", "Source": "p<PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "hypothermiainduced", "Source": "hypothermiainduced", "Transformation": null, "Value Map": null}, {"Target": "hypothermiainducedtiming", "Source": "hypothermiainducedtiming", "Transformation": null, "Value Map": {"Initiated Pre-PCI, <=  6 hrs post cardiac arrest": "Initiated Pre-PCI, <= 6 hrs post cardiac arrest", "Initiated Pre-PCI,  >  6 hrs post cardiac arrest": "Initiated Pre-PCI, > 6 hrs post cardiac arrest"}}, {"Target": "proc_loc", "Source": "locproc", "Transformation": null, "Value Map": null}, {"Target": "pcidecision", "Source": "pcidecision", "Transformation": null, "Value Map": null}, {"Target": "cvtxdecision", "Source": "cvtxdecision", "Transformation": null, "Value Map": null}, {"Target": "multivesseldz", "Source": "multivesseldz", "Transformation": null, "Value Map": null}, {"Target": "multivessproctype", "Source": "multivessproctype", "Transformation": null, "Value Map": null}, {"Target": "pciindication", "Source": "pciindication", "Transformation": null, "Value Map": {"CAD (without ischemic Sx)": "CAD (without Ischemic Sx)", "STEMI - Rescue (After unsuccessful lytics)": "STEMI - Rescue (after unsuccessful lytics)"}}, {"Target": "symptomdate", "Source": "symptomdate", "Transformation": null, "Value Map": null}, {"Target": "symptomtimeunk", "Source": "symptomtimeunk", "Transformation": null, "Value Map": null}, {"Target": "symptomtime", "Source": "symptomtime", "Transformation": null, "Value Map": null}, {"Target": "thrombolytics", "Source": "thromtherapy", "Transformation": null, "Value Map": null}, {"Target": "thrombolytictherapydatetime", "Source": "thromdatetime", "Transformation": null, "Value Map": null}, {"Target": "syntaxscoreunk", "Source": "syntaxscoreunk", "Transformation": null, "Value Map": null}, {"Target": "syntaxscore", "Source": "syntaxscore", "Transformation": null, "Value Map": null}, {"Target": "stemifirstnoted", "Source": "stemifirstnoted", "Transformation": null, "Value Map": null}, {"Target": "subecgdatetime", "Source": "subecgdatetime", "Transformation": null, "Value Map": null}, {"Target": "subecged", "Source": "subecged", "Transformation": null, "Value Map": null}, {"Target": "patienttranspci", "Source": "patienttranspci", "Transformation": null, "Value Map": null}, {"Target": "edpresentdatetime", "Source": "edpresentdatetime", "Transformation": null, "Value Map": null}, {"Target": "firstdevactidatetime", "Source": "firstdevactidatetime", "Transformation": null, "Value Map": null}, {"Target": "ptp<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "ptp<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "procedure_bivalirudin", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Derivative Value Code": 400610005, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_fondapa<PERSON>ux", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Fondaparinux", "Derivative Value Code": 321208, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_low_molecular_weight_heparin_any", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Low Molecular Weight Heparin", "Derivative Value Code": 373294004, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_unfractionated_heparin_any", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Unfractionated Heparin", "Derivative Value Code": 96382006, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_warfarin", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Warfarin", "Derivative Value Code": 11289, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_vorapaxar", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Vorapaxar", "Derivative Value Code": 1537034, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_gpinhibitors_any", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Glycoprotein IIb IIIa Inhibitors", "Derivative Value Code": 1000142427, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_apixaban", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Apixaban", "Derivative Value Code": 1364430, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_dabigatran", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Dabigatran", "Derivative Value Code": 1546356, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_edoxaban", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Edoxaban", "Derivative Value Code": 1599538, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_rivaroxaban", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Rivaroxaban", "Derivative Value Code": 1114195, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_cangrelor", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON><PERSON>", "Derivative Value Code": 1656052, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_clopidogrel", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Clopidogrel", "Derivative Value Code": 32968, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_prasugrel", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Prasug<PERSON>", "Derivative Value Code": 613391, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_ticagrelor", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Ticagrelor", "Derivative Value Code": 1116632, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_arga<PERSON>ban", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON><PERSON>", "Derivative Value Code": 15202, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "lesioncounter", "Source": "lesioncounter", "Transformation": null, "Value Map": null}, {"Target": "segmentid", "Source": "segmentid", "Transformation": null, "Value Map": {"11a - Ostial LM": "11a - Ost<PERSON> L", "11b-  Mid-LM": "11b- Mid-LM", "11c -  Distal LM": "11c - Distal LM"}}, {"Target": "culpritartery", "Source": "culpritartery", "Transformation": null, "Value Map": null}, {"Target": "culpritarteryunk", "Source": "culpritarteryunk", "Transformation": null, "Value Map": null}, {"Target": "sten<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "sten<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "chronicocclusion", "Source": "chronicocclusion", "Transformation": null, "Value Map": null}, {"Target": "chronicocclusionunk", "Source": "chronicocclusionunk", "Transformation": null, "Value Map": null}, {"Target": "prep<PERSON>ct<PERSON>i", "Source": "prep<PERSON>ct<PERSON>i", "Transformation": null, "Value Map": null}, {"Target": "prevtreatedlesion", "Source": "prevtreatedlesion", "Transformation": null, "Value Map": null}, {"Target": "prevtreatedlesiondate", "Source": "prevtreatedlesiondate", "Transformation": null, "Value Map": null}, {"Target": "previousstent", "Source": "previousstent", "Transformation": null, "Value Map": null}, {"Target": "inrestenosis", "Source": "inrestenosis", "Transformation": null, "Value Map": null}, {"Target": "inthrombosis", "Source": "inthrombosis", "Transformation": null, "Value Map": null}, {"Target": "stenttype", "Source": "stenttype", "Transformation": null, "Value Map": {"BMS": "Bare Metal Stent (BMS)"}}, {"Target": "sten<PERSON><PERSON><PERSON>", "Source": "sten<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "lesiongraft", "Source": "lesiongraft", "Transformation": null, "Value Map": null}, {"Target": "lesiongrafttype", "Source": "lesiongrafttype", "Transformation": null, "Value Map": null}, {"Target": "locgraft", "Source": "locgraft", "Transformation": null, "Value Map": null}, {"Target": "navgraftnatles", "Source": "navgraftnatles", "Transformation": null, "Value Map": null}, {"Target": "lesioncomplexity", "Source": "lesioncomplexity", "Transformation": null, "Value Map": null}, {"Target": "lesionlength", "Source": "lesionlength", "Transformation": null, "Value Map": null}, {"Target": "severecalcification", "Source": "severecalcification", "Transformation": null, "Value Map": null}, {"Target": "bifurcationlesion", "Source": "bifurcationlesion", "Transformation": null, "Value Map": null}, {"Target": "guidewirelesion", "Source": "guidewirelesion", "Transformation": null, "Value Map": null}, {"Target": "devicedeployed", "Source": "devicedeployed", "Transformation": null, "Value Map": null}, {"Target": "stenosispostproc", "Source": "stenosispostproc", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "icdevcounter", "Source": "icdevcounter", "Transformation": null, "Value Map": null}, {"Target": "ic<PERSON><PERSON>", "Source": "ic<PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "icunide<PERSON>", "Source": "icdevu<PERSON>", "Transformation": null, "Value Map": null}, {"Target": "icdevcounterassn", "Source": "icdevcounterassn", "Transformation": null, "Value Map": null}, {"Target": "devicediameter", "Source": "devicediameter", "Transformation": null, "Value Map": null}, {"Target": "devicelength", "Source": "devicelength", "Transformation": null, "Value Map": null}, {"Target": "perfseg", "Source": "perfseg", "Transformation": null, "Value Map": null}, {"Target": "dissectionseg", "Source": "dissectionseg", "Transformation": null, "Value Map": null}, {"Target": "posttransfusion", "Source": "posttransfusion", "Transformation": null, "Value Map": null}, {"Target": "prbcunits", "Source": "prbcunits", "Transformation": null, "Value Map": null}, {"Target": "transfuspostpci", "Source": "transfuspostpci", "Transformation": null, "Value Map": null}, {"Target": "transfusionpostsurg", "Source": "transfusionpostsurg", "Transformation": null, "Value Map": null}, {"Target": "bleedingaccesssite", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Access Site", "Derivative Value Code": 1000142440, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedingaccesssitedatetime", "Source": null, "Transformation": {"Reference Field": "PostProcDateTime", "Reference Field Code": 10001424780, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Access Site", "Derivative Value Code": 1000142440, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedinggastrointestinal", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Gastrointestinal", "Derivative Value Code": 74474003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedinggastrointestinaldatetime", "Source": null, "Transformation": {"Reference Field": "PostProcDateTime", "Reference Field Code": 10001424780, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Gastrointestinal", "Derivative Value Code": 74474003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedinggenitourinary", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Genitourinary", "Derivative Value Code": 417941003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedinggenitourinarydatetime", "Source": null, "Transformation": {"Reference Field": "PostProcDateTime", "Reference Field Code": 10001424780, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Genitourinary", "Derivative Value Code": 417941003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedinghematomaaccesssite", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Hematoma at Access Site", "Derivative Value Code": 385494008, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedinghematomaasdatetime", "Source": null, "Transformation": {"Reference Field": "PostProcDateTime", "Reference Field Code": 10001424780, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Hematoma at Access Site", "Derivative Value Code": 385494008, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedingother", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Other", "Derivative Value Code": 1000142371, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedingotherdatetime", "Source": null, "Transformation": {"Reference Field": "PostProcDateTime", "Reference Field Code": 10001424780, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Other", "Derivative Value Code": 1000142371, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedingretroperitoneal", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Retroperitoneal", "Derivative Value Code": 95549001, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedingretrodatetime", "Source": null, "Transformation": {"Reference Field": "PostProcDateTime", "Reference Field Code": 10001424780, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Retroperitoneal", "Derivative Value Code": 95549001, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "cardiac<PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Cardiac Arrest", "Derivative Value Code": 410429000, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "cardiacarrestdatetime", "Source": null, "Transformation": {"Reference Field": "PostProcDateTime", "Reference Field Code": 10001424780, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Cardiac Arrest", "Derivative Value Code": 410429000, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "cardiactamponade", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Cardiac Tamponade", "Derivative Value Code": 35304003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "cardiactamponadedatetime", "Source": null, "Transformation": {"Reference Field": "PostProcDateTime", "Reference Field Code": 10001424780, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Cardiac Tamponade", "Derivative Value Code": 35304003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "cardiogenicshock", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Cardiogenic Shock", "Derivative Value Code": 89138009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "cardiogenicshockdatetime", "Source": null, "Transformation": {"Reference Field": "PostProcDateTime", "Reference Field Code": 10001424780, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Cardiogenic Shock", "Derivative Value Code": 89138009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "heartfailure", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Heart Failure", "Derivative Value Code": 84114007, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "heartfailuredatetime", "Source": null, "Transformation": {"Reference Field": "PostProcDateTime", "Reference Field Code": 10001424780, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Heart Failure", "Derivative Value Code": 84114007, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "myocardialinfarction", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Myocardial Infarction", "Derivative Value Code": 22298006, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "myocardialinfarctiondatetime", "Source": null, "Transformation": {"Reference Field": "PostProcDateTime", "Reference Field Code": 10001424780, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Myocardial Infarction", "Derivative Value Code": 22298006, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "newrequirementfordialysis", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "New Requirement for Dialysis", "Derivative Value Code": 100014076, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dialysisdatetime", "Source": null, "Transformation": {"Reference Field": "PostProcDateTime", "Reference Field Code": 10001424780, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "New Requirement for Dialysis", "Derivative Value Code": 100014076, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "othervascompreqtreatment", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Other Vascular Complications Requiring Treatment", "Derivative Value Code": 1000142419, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "othervascompreqtreatmentdatetime", "Source": null, "Transformation": {"Reference Field": "PostProcDateTime", "Reference Field Code": 10001424780, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Other Vascular Complications Requiring Treatment", "Derivative Value Code": 1000142419, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "strokehemorrhagic", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Stroke - Hemorrhagic", "Derivative Value Code": 230706003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "strokehemorrhagicdatetime", "Source": null, "Transformation": {"Reference Field": "PostProcDateTime", "Reference Field Code": 10001424780, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Stroke - Hemorrhagic", "Derivative Value Code": 230706003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "strokeischemic", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Stroke - Ischemic", "Derivative Value Code": 422504002, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "strokeischemicdatetime", "Source": null, "Transformation": {"Reference Field": "PostProcDateTime", "Reference Field Code": 10001424780, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Stroke - Ischemic", "Derivative Value Code": 422504002, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "strokeundetermined", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Stroke - Undetermined", "Derivative Value Code": 230713003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "strokeundetermineddatetime", "Source": null, "Transformation": {"Reference Field": "PostProcDateTime", "Reference Field Code": 10001424780, "Derivative Field": "PostProcEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Stroke - Undetermined", "Derivative Value Code": 230713003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "hospintervention", "Source": "hospintervention", "Transformation": null, "Value Map": null}, {"Target": "cabg", "Source": null, "Transformation": {"Reference Field": "HospInterventionType", "Reference Field Code": 100001284, "Derivative Field": "HospInterventionType", "Derivative Field Code": 100001284, "Derivative Value": "|CABG|", "Derivative Value Code": "232717009", "Value Map": {"|CABG|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "valvular_intervention", "Source": null, "Transformation": {"Reference Field": "HospInterventionType", "Reference Field Code": 100001284, "Derivative Field": "HospInterventionType", "Derivative Field Code": 100001284, "Derivative Value": "|Valvular Intervention|", "Derivative Value Code": "100014071", "Value Map": {"|Valvular Intervention|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "cardiac_surgery_non_cabg", "Source": null, "Transformation": {"Reference Field": "HospInterventionType", "Reference Field Code": 100001284, "Derivative Field": "HospInterventionType", "Derivative Field Code": 100001284, "Derivative Value": "|Cardiac Surgery (non CABG)|", "Derivative Value Code": "100014068", "Value Map": {"|Cardiac Surgery (non CABG)|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "structural_heart_intervention_non_valvular", "Source": null, "Transformation": {"Reference Field": "HospInterventionType", "Reference Field Code": 100001284, "Derivative Field": "HospInterventionType", "Derivative Field Code": 100001284, "Derivative Value": "|Structural Heart Intervention (non-valvular)|", "Derivative Value Code": "100014072", "Value Map": {"|Structural Heart Intervention (non-valvular)|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "non_cardiac_surgery", "Source": null, "Transformation": {"Reference Field": "HospInterventionType", "Reference Field Code": 100001284, "Derivative Field": "HospInterventionType", "Derivative Field Code": 100001284, "Derivative Value": "|Surgery (Non Cardiac)|", "Derivative Value Code": "100014022", "Value Map": {"|Surgery (Non Cardiac)|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "ep_study", "Source": null, "Transformation": {"Reference Field": "HospInterventionType", "Reference Field Code": 100001284, "Derivative Field": "HospInterventionType", "Derivative Field Code": 100001284, "Derivative Value": "|EP Study|", "Derivative Value Code": "252425004", "Value Map": {"|EP Study|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "hospinterventiontype_other", "Source": null, "Transformation": {"Reference Field": "HospInterventionType", "Reference Field Code": 100001284, "Derivative Field": "HospInterventionType", "Derivative Field Code": 100001284, "Derivative Value": "|Other|", "Derivative Value Code": "10001424811", "Value Map": {"|Other|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "cabgstatus", "Source": "cabgstatus", "Transformation": null, "Value Map": null}, {"Target": "cabgindication", "Source": "cabgindication", "Transformation": null, "Value Map": null}, {"Target": "cabgdatetime", "Source": "cabgdatetime", "Transformation": null, "Value Map": null}, {"Target": "d<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "d<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "dccreatinine", "Source": "dccreatinine", "Transformation": null, "Value Map": null}, {"Target": "dchgbnd", "Source": "dchgbnd", "Transformation": null, "Value Map": null}, {"Target": "dchgb", "Source": "dchgb", "Transformation": null, "Value Map": null}, {"Target": "dcdatetime", "Source": "dcdatetime", "Transformation": null, "Value Map": null}, {"Target": "dclname", "Source": "dclname", "Transformation": null, "Value Map": null}, {"Target": "dcfname", "Source": "dcfname", "Transformation": null, "Value Map": null}, {"Target": "dcmname", "Source": "dcmname", "Transformation": null, "Value Map": null}, {"Target": "dcnpi", "Source": "dcnpi", "Transformation": null, "Value Map": null}, {"Target": "dischargestatus", "Source": "dcstatus", "Transformation": null, "Value Map": null}, {"Target": "dischargecomfort", "Source": "dc_comfort", "Transformation": null, "Value Map": null}, {"Target": "dischargelocation", "Source": "dclocation", "Transformation": null, "Value Map": null}, {"Target": "cabgtransferdc", "Source": "cabgtransfer", "Transformation": null, "Value Map": null}, {"Target": "cabgplanneddc", "Source": "cabgplanneddc", "Transformation": null, "Value Map": null}, {"Target": "dchospice", "Source": "dchospice", "Transformation": null, "Value Map": null}, {"Target": "dischargecardrehab", "Source": "dc_cardrehab", "Transformation": null, "Value Map": {"No - Medical Reason Documented": "No -Medical Reason Documented", "No - Health Care System Reason Documented": "No -Health Care System Reason Documented"}}, {"Target": "dc_loc", "Source": "dc_loc", "Transformation": null, "Value Map": null}, {"Target": "deathprocedure", "Source": "deathprocedure", "Transformation": null, "Value Map": null}, {"Target": "deathcause", "Source": "deathcause", "Transformation": null, "Value Map": null}, {"Target": "dc_medreconcompleted", "Source": "dc_medreconcompleted", "Transformation": null, "Value Map": null}, {"Target": "dc_medreconciled", "Source": "dc_medreconciled", "Transformation": null, "Value Map": null}, {"Target": "discharge_ace_inhibitor_any", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Angiotensin Converting Enzyme Inhibitor", "Derivative Value Code": 41549009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_aceinhibitors_any_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Angiotensin Converting Enzyme Inhibitor", "Derivative Value Code": 41549009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_aceinhibitors_any_rationale", "Source": null, "Transformation": {"Reference Field": "DC_PtRationale", "Reference Field Code": 100013080, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Angiotensin Converting Enzyme Inhibitor", "Derivative Value Code": 41549009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_warfarin", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Warfarin", "Derivative Value Code": 11289, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_warfarin_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Warfarin", "Derivative Value Code": 11289, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_warfarin_rationale", "Source": null, "Transformation": {"Reference Field": "DC_PtRationale", "Reference Field Code": 100013080, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Warfarin", "Derivative Value Code": 11289, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_aspirin_any", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON>", "Derivative Value Code": 1191, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_aspirin_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON>", "Derivative Value Code": 1191, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_aspirin_rationale", "Source": null, "Transformation": {"Reference Field": "DC_PtRationale", "Reference Field Code": 100013080, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON>", "Derivative Value Code": 1191, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_vorapaxar", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Vorapaxar", "Derivative Value Code": 1537034, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_vorapa<PERSON>r_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Vorapaxar", "Derivative Value Code": 1537034, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_vorapaxar_rationale", "Source": null, "Transformation": {"Reference Field": "DC_PtRationale", "Reference Field Code": 100013080, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Vorapaxar", "Derivative Value Code": 1537034, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_arb_any", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Angiotensin II Receptor Blocker", "Derivative Value Code": 372913009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_arb_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Angiotensin II Receptor Blocker", "Derivative Value Code": 372913009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_arb_rationale", "Source": null, "Transformation": {"Reference Field": "DC_PtRationale", "Reference Field Code": 100013080, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Angiotensin II Receptor Blocker", "Derivative Value Code": 372913009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_non_statin_any", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Non-Statin", "Derivative Value Code": 100014161, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_non_statin_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Non-Statin", "Derivative Value Code": 100014161, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_non_statin_rationale", "Source": null, "Transformation": {"Reference Field": "DC_PtRationale", "Reference Field Code": 100013080, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Non-Statin", "Derivative Value Code": 100014161, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_apixaban", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Apixaban", "Derivative Value Code": 1364430, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_apixaban_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Apixaban", "Derivative Value Code": 1364430, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_apixaban_rationale", "Source": null, "Transformation": {"Reference Field": "DC_PtRationale", "Reference Field Code": 100013080, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Apixaban", "Derivative Value Code": 1364430, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_dabigatran", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Dabigatran", "Derivative Value Code": 1546356, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_da<PERSON><PERSON><PERSON>_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Dabigatran", "Derivative Value Code": 1546356, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_dabigatran_rationale", "Source": null, "Transformation": {"Reference Field": "DC_PtRationale", "Reference Field Code": 100013080, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Dabigatran", "Derivative Value Code": 1546356, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_edoxaban", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Edoxaban", "Derivative Value Code": 1599538, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_ed<PERSON><PERSON><PERSON>_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Edoxaban", "Derivative Value Code": 1599538, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_edoxaban_rationale", "Source": null, "Transformation": {"Reference Field": "DC_PtRationale", "Reference Field Code": 100013080, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Edoxaban", "Derivative Value Code": 1599538, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_prasugrel", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Prasug<PERSON>", "Derivative Value Code": 613391, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_prasug<PERSON>_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Prasug<PERSON>", "Derivative Value Code": 613391, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_prasugrel_rationale", "Source": null, "Transformation": {"Reference Field": "DC_PtRationale", "Reference Field Code": 100013080, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Prasug<PERSON>", "Derivative Value Code": 613391, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_ticagrelor", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Ticagrelor", "Derivative Value Code": 1116632, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_ticag<PERSON><PERSON>_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Ticagrelor", "Derivative Value Code": 1116632, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_ticagrelor_rationale", "Source": null, "Transformation": {"Reference Field": "DC_PtRationale", "Reference Field Code": 100013080, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Ticagrelor", "Derivative Value Code": 1116632, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_ticlopidine", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Ticlopidine", "Derivative Value Code": 10594, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_ticlop<PERSON><PERSON>_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Ticlopidine", "Derivative Value Code": 10594, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_ticlopidine_rationale", "Source": null, "Transformation": {"Reference Field": "DC_PtRationale", "Reference Field Code": 100013080, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Ticlopidine", "Derivative Value Code": 10594, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_alirocumab", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON><PERSON>", "Derivative Value Code": 1659152, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_aliro<PERSON><PERSON>_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON><PERSON>", "Derivative Value Code": 1659152, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_alirocumab_rationale", "Source": null, "Transformation": {"Reference Field": "DC_PtRationale", "Reference Field Code": 100013080, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON><PERSON>", "Derivative Value Code": 1659152, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_evolocumab", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Evolocumab", "Derivative Value Code": 1665684, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_evolocumab_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Evolocumab", "Derivative Value Code": 1665684, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_evolocumab_rationale", "Source": null, "Transformation": {"Reference Field": "DC_PtRationale", "Reference Field Code": 100013080, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Evolocumab", "Derivative Value Code": 1665684, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dischargestatinany", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Statin", "Derivative Value Code": 96302009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_statin_any_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Statin", "Derivative Value Code": 96302009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_statin_any_rationale", "Source": null, "Transformation": {"Reference Field": "DC_PtRationale", "Reference Field Code": 100013080, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Statin", "Derivative Value Code": 96302009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_clopidogrel", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Clopidogrel", "Derivative Value Code": 32968, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_clop<PERSON>g<PERSON>_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Clopidogrel", "Derivative Value Code": 32968, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_clopidogrel_rationale", "Source": null, "Transformation": {"Reference Field": "DC_PtRationale", "Reference Field Code": 100013080, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Clopidogrel", "Derivative Value Code": 32968, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dischargebetablocker", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Beta Blocker", "Derivative Value Code": 33252009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_betablocker_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Beta Blocker", "Derivative Value Code": 33252009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_betablocker_rationale", "Source": null, "Transformation": {"Reference Field": "DC_PtRationale", "Reference Field Code": 100013080, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Beta Blocker", "Derivative Value Code": 33252009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_rivaroxaban", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Rivaroxaban", "Derivative Value Code": 1114195, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_rivaro<PERSON><PERSON>_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Rivaroxaban", "Derivative Value Code": 1114195, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_rivaroxaban_rationale", "Source": null, "Transformation": {"Reference Field": "DC_PtRationale", "Reference Field Code": 100013080, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Rivaroxaban", "Derivative Value Code": 1114195, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "datavrsn", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "procedurestartdatetime", "Source": "procedurestartdatetime", "Transformation": null, "Value Map": null}, {"Target": "arrivaldate", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "dischargedate", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "originalotherid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "hospname", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "filename", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "version", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "datasetname", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "clientfileid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "biomeimportdt", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "yearmonth", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "casesequencenumber", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "age", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "distfromhospital", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "pericardial_disease", "Source": null, "Transformation": {"Reference Field": "CathLabVisitIndication", "Reference Field Code": 100014000, "Derivative Field": "CathLabVisitIndication", "Derivative Field Code": 100014000, "Derivative Value": "Pericardial Disease", "Derivative Value Code": 100014002, "Value Map": {"Pericardial Disease": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_heparinderivative", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Heparin Derivative", "Derivative Value Code": 100000921, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "evalexerciseclearance", "Source": null, "Transformation": {"Reference Field": "CathLabVisitIndication", "Reference Field Code": 100014000, "Derivative Field": "CathLabVisitIndication", "Derivative Field Code": 100014000, "Derivative Value": "Evaluation for Exercise Clearance", "Derivative Value Code": 10001424791, "Value Map": {"Evaluation for Exercise Clearance": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "preprocsacubitrilvalsartan", "Source": null, "Transformation": {"Reference Field": "PreProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "PreProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Sacubitril and Valsartan", "Derivative Value Code": 1656341, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "biomeencounterid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "tenantid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "careentityid", "Source": null, "Transformation": null, "Value Map": null}]}}