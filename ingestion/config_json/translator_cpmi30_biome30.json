{"TranslatorConfig": {"Version": "3.0", "Source": {"Dataset": {"Code": "CPMI", "Id": 148}, "Version": "3.0", "Type": "<PERSON><PERSON><PERSON>"}, "Target": {"Dataset": {"Code": "CPMI", "Id": 148}, "Version": "3.0", "Type": "Biome"}, "Translations": [{"Target": "<PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "ncdrpatientid", "Source": "ncdrpatientid", "Transformation": null, "Value Map": null}, {"Target": "partid", "Source": "partid", "Transformation": null, "Value Map": null}, {"Target": "partname", "Source": "partname", "Transformation": null, "Value Map": null}, {"Target": "firstname", "Source": "firstname", "Transformation": null, "Value Map": null}, {"Target": "lastname", "Source": "lastname", "Transformation": null, "Value Map": null}, {"Target": "midname", "Source": "midname", "Transformation": null, "Value Map": null}, {"Target": "ssnna", "Source": "ssnna", "Transformation": null, "Value Map": null}, {"Target": "ssn", "Source": "ssn", "Transformation": null, "Value Map": null}, {"Target": "otherid", "Source": "otherid", "Transformation": null, "Value Map": null}, {"Target": "dob", "Source": "dob", "Transformation": null, "Value Map": null}, {"Target": "gender", "Source": "sex", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "zipcodena", "Transformation": null, "Value Map": null}, {"Target": "patzip", "Source": "zipcode", "Transformation": null, "Value Map": null}, {"Target": "racewhite", "Source": "racewhite", "Transformation": null, "Value Map": null}, {"Target": "raceblackafricanamerican", "Source": "raceblack", "Transformation": null, "Value Map": null}, {"Target": "raceamericanindianalaskannative", "Source": "raceamindian", "Transformation": null, "Value Map": null}, {"Target": "raceasian", "Source": "raceasian", "Transformation": null, "Value Map": null}, {"Target": "raceasianindian", "Source": "raceasianindian", "Transformation": null, "Value Map": null}, {"Target": "racechinese", "Source": "racechinese", "Transformation": null, "Value Map": null}, {"Target": "racefilipino", "Source": "racefilipino", "Transformation": null, "Value Map": null}, {"Target": "racejapanese", "Source": "racejapanese", "Transformation": null, "Value Map": null}, {"Target": "racekorean", "Source": "racekorean", "Transformation": null, "Value Map": null}, {"Target": "racevietnamese", "Source": "racevietnamese", "Transformation": null, "Value Map": null}, {"Target": "raceotherasian", "Source": "raceasianother", "Transformation": null, "Value Map": null}, {"Target": "racenathawpacificislander", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "racenathaw", "Source": "racenathaw", "Transformation": null, "Value Map": null}, {"Target": "raceguamanianorchamorro", "Source": "raceguamchamorro", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "raceother<PERSON>ci<PERSON><PERSON>lander", "Source": "race<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "hispanicethnicitytypemexicanmexicanamericanchicano", "Source": "hispethnicitymexican", "Transformation": null, "Value Map": null}, {"Target": "hispanicethnicitytypepuertorican", "Source": "hispethnicitypuertorico", "Transformation": null, "Value Map": null}, {"Target": "hispanicethnicitytypecuban", "Source": "hispethnicitycuban", "Transformation": null, "Value Map": null}, {"Target": "hispanicethnicity<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "hispethnicityotherorigin", "Transformation": null, "Value Map": null}, {"Target": "episode<PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "episodeid", "Source": "episodekey", "Transformation": null, "Value Map": null}, {"Target": "arrivaldatetime", "Source": "arrivaldatetime", "Transformation": null, "Value Map": null}, {"Target": "admitdatetime", "Source": "admitdatetime", "Transformation": null, "Value Map": null}, {"Target": "admfname", "Source": "admfname", "Transformation": null, "Value Map": null}, {"Target": "admlname", "Source": "admlname", "Transformation": null, "Value Map": null}, {"Target": "admmidname", "Source": "admmname", "Transformation": null, "Value Map": null}, {"Target": "admnpi", "Source": "admnpi", "Transformation": null, "Value Map": null}, {"Target": "healthins", "Source": "healthins", "Transformation": null, "Value Map": null}, {"Target": "medicare", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Medicare", "Derivative Value Code": 1, "Value Map": {"|Medicare|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "hic", "Source": "hic", "Transformation": null, "Value Map": null}, {"Target": "miltaryhealthcare", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Military Health Care", "Derivative Value Code": 31, "Value Map": {"|Military Health Care|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "hicnumber", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "medicarebeneficiaryid", "Source": "mbi", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON>udy", "Source": "<PERSON><PERSON>udy", "Transformation": null, "Value Map": null}, {"Target": "ptrestriction2", "Source": "ptrestriction2", "Transformation": null, "Value Map": null}, {"Target": "edfname", "Source": "edfname", "Transformation": null, "Value Map": null}, {"Target": "ed<PERSON><PERSON>", "Source": "ed<PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "edmname", "Source": "edmname", "Transformation": null, "Value Map": null}, {"Target": "ednpi", "Source": "ednpi", "Transformation": null, "Value Map": null}, {"Target": "attnlname", "Source": "attlname", "Transformation": null, "Value Map": null}, {"Target": "attnfname", "Source": "attfname", "Transformation": null, "Value Map": null}, {"Target": "attnmidname", "Source": "attmname", "Transformation": null, "Value Map": null}, {"Target": "attnnpi", "Source": "attnpi", "Transformation": null, "Value Map": null}, {"Target": "studyname", "Source": "studyname", "Transformation": null, "Value Map": null}, {"Target": "studypatientid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "height", "Source": "heightad", "Transformation": null, "Value Map": null}, {"Target": "weight", "Source": "weightad", "Transformation": null, "Value Map": null}, {"Target": "hxafib", "Source": "hxafib", "Transformation": null, "Value Map": null}, {"Target": "atrialflutter", "Source": "aflutterba", "Transformation": null, "Value Map": null}, {"Target": "hypertension", "Source": "hypertension", "Transformation": null, "Value Map": null}, {"Target": "dyslipidemia", "Source": "dyslipidemia", "Transformation": null, "Value Map": null}, {"Target": "currentdialysis", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "cancer", "Source": "cancer", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON>", "Source": "hxmiff", "Transformation": null, "Value Map": null}, {"Target": "diabetes", "Source": "diabetesba", "Transformation": null, "Value Map": null}, {"Target": "priorhf", "Source": "priorhfff", "Transformation": null, "Value Map": null}, {"Target": "priorpci", "Source": "priorpci", "Transformation": null, "Value Map": null}, {"Target": "lastpcidate", "Source": "lastpcidate_ba", "Transformation": null, "Value Map": null}, {"Target": "priorcabg", "Source": "priorcabg", "Transformation": null, "Value Map": null}, {"Target": "priorcabgdate", "Source": "priorcabgdate", "Transformation": null, "Value Map": null}, {"Target": "hxcvd", "Source": "hxcvd", "Transformation": null, "Value Map": null}, {"Target": "stroke", "Source": "strokeba", "Transformation": null, "Value Map": null}, {"Target": "hxtia", "Source": "hxtia", "Transformation": null, "Value Map": null}, {"Target": "priorpad", "Source": "priorpad", "Transformation": null, "Value Map": null}, {"Target": "tobaccouse", "Source": "tobaccouse", "Transformation": null, "Value Map": {"Smoker - Current Status Unknown": "Smoker, current status unknown"}}, {"Target": "tobaccotype", "Source": "tobaccotype", "Transformation": null, "Value Map": null}, {"Target": "smokingamount", "Source": "smokeamount", "Transformation": null, "Value Map": null}, {"Target": "walking", "Source": "walking", "Transformation": null, "Value Map": null}, {"Target": "walkingunk", "Source": "walkingunk", "Transformation": null, "Value Map": null}, {"Target": "cognition", "Source": "cognition", "Transformation": null, "Value Map": null}, {"Target": "cognitionunk", "Source": "cognitionunk", "Transformation": null, "Value Map": null}, {"Target": "basicadlsunk", "Source": "basicadlsunk", "Transformation": null, "Value Map": null}, {"Target": "basicadls", "Source": "basicadls", "Transformation": null, "Value Map": null}, {"Target": "homemedclopidogrel", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "Clopidogrel", "Derivative Value Code": 32968, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemedasa", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON>", "Derivative Value Code": 1191, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemedprasugrel", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "Prasug<PERSON>", "Derivative Value Code": 613391, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemedticagrelor", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "Ticagrelor", "Derivative Value Code": 1116632, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemed<PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "Warfarin", "Derivative Value Code": 11289, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemeddabigatran", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "Dabigatran", "Derivative Value Code": 1546356, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemedrivaro<PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "Rivaroxaban", "Derivative Value Code": 1114195, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemedapixaban", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "Apixaban", "Derivative Value Code": 1364430, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemededoxaban", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "Edoxaban", "Derivative Value Code": 1599538, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemedevolocumab", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "Evolocumab", "Derivative Value Code": 1665684, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemedalirocumab", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON><PERSON>", "Derivative Value Code": 1659152, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "home<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "Beta Blocker", "Derivative Value Code": 33252009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemedacei", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "Angiotensin Converting Enzyme Inhibitor", "Derivative Value Code": 41549009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemedsacubitrilvalsartan", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "Sacubitril and Valsartan", "Derivative Value Code": 1656341, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemedezetimibe", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "Ezetimibe ", "Derivative Value Code": 341248, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemedfenofibrate", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "Fenofibrate", "Derivative Value Code": 8703, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemedangiotensinii", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "Angiotensin II Receptor Blocker", "Derivative Value Code": 372913009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemedaldosteroneantagonist", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "Aldosterone Antagonist", "Derivative Value Code": 372603003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemedstatin", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "Statin", "Derivative Value Code": 96302009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemeddpp4inhibitor", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "DPP-4 inhibitor", "Derivative Value Code": 112000000263, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemedglp1receptoragonist", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "GLP-1 Receptor Agonist", "Derivative Value Code": 112000000264, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemedmetformin", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "Metformin", "Derivative Value Code": 6809, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemedotheroralhypoglyc", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "Other Oral Hypoglycemic", "Derivative Value Code": 112000000265, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemedpioglitazone", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "Pioglitazone", "Derivative Value Code": 33738, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemedsglt2inhibitor", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "Sodium glucose cotransporter subtype 2 inhibitor", "Derivative Value Code": 703673007, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemedsulfonylurea", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "Sulfonylurea", "Derivative Value Code": 372711004, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "homemedinsulin", "Source": null, "Transformation": {"Reference Field": "HomeMedPrescrib", "Reference Field Code": 432102000, "Derivative Field": "HomeMeds", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON>", "Derivative Value Code": 67866001, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "thromtherapyfmc", "Source": "thromtherapyfmc", "Transformation": null, "Value Map": null}, {"Target": "thromdatetimefmc", "Source": "thromdatetimefmc", "Transformation": null, "Value Map": null}, {"Target": "emsdispatchdatetime", "Source": "emsdispatchdatetime", "Transformation": null, "Value Map": null}, {"Target": "emsleavingscenedatetime", "Source": "emsleavingscenedatetime", "Transformation": null, "Value Map": null}, {"Target": "emsagencynumber", "Source": "emsagencynumber", "Transformation": null, "Value Map": null}, {"Target": "emsrunnumber", "Source": "emsrunnumber", "Transformation": null, "Value Map": null}, {"Target": "ecgperfems", "Source": "ecgperfems", "Transformation": null, "Value Map": null}, {"Target": "emss<PERSON><PERSON><PERSON>", "Source": "emss<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "tranoutsidefac", "Source": "tranoutsidefac", "Transformation": null, "Value Map": null}, {"Target": "transferhospmeanstransfer", "Source": "transferhospmeanstransfer", "Transformation": null, "Value Map": null}, {"Target": "arrouthospdatetime", "Source": "arrouthospdatetime", "Transformation": null, "Value Map": null}, {"Target": "tranoutfacdatetime", "Source": "tranoutfacdatetime", "Transformation": null, "Value Map": null}, {"Target": "tranfacname", "Source": "tranfacname", "Transformation": null, "Value Map": null}, {"Target": "tranfacahanumber", "Source": "tranfacahanumber", "Transformation": null, "Value Map": null}, {"Target": "tranfacnameunavail", "Source": "tranfacnameunavail", "Transformation": null, "Value Map": null}, {"Target": "lvefassessedad", "Source": "lvefassessedad", "Transformation": null, "Value Map": null}, {"Target": "lvef<PERSON><PERSON>", "Source": "lvef<PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "lvefafterdischarge", "Source": "lvefplandc", "Transformation": null, "Value Map": null}, {"Target": "diagcorangiofirst", "Source": "diagcorangio1st", "Transformation": null, "Value Map": {"No - Pt. Reason": "No - Patient Reason"}}, {"Target": "catharrivaldatetime", "Source": "catharrivaldatetime", "Transformation": null, "Value Map": null}, {"Target": "dcathlname", "Source": "dcathlname", "Transformation": null, "Value Map": null}, {"Target": "dcathfname", "Source": "dcathfname", "Transformation": null, "Value Map": null}, {"Target": "dcathmname", "Source": "dcathmname", "Transformation": null, "Value Map": null}, {"Target": "dcathnpi", "Source": "dcathnpi", "Transformation": null, "Value Map": null}, {"Target": "diagcorangiodatetime", "Source": "diagcorangiodatetime", "Transformation": null, "Value Map": null}, {"Target": "nvstenosis", "Source": "nvstenosis", "Transformation": null, "Value Map": null}, {"Target": "gvstenosis", "Source": "graftstenosis", "Transformation": null, "Value Map": null}, {"Target": "cabgfirst", "Source": "cabgfirst", "Transformation": null, "Value Map": null}, {"Target": "cabgdatetime", "Source": "cabgdatetime", "Transformation": null, "Value Map": null}, {"Target": "privatehealthinsurance", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Private Health Insurance", "Derivative Value Code": 5, "Value Map": {"|Private Health Insurance|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "medicaid", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Medicaid", "Derivative Value Code": 2, "Value Map": {"|Medicaid|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "cardiacpressrowid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "patienttype", "Source": "pttype", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON>t", "Source": "<PERSON><PERSON>t", "Transformation": null, "Value Map": null}, {"Target": "firstfacmeanstrans", "Source": "firstfacmeanstrans", "Transformation": null, "Value Map": null}, {"Target": "firstmedcondatetime", "Source": "firstmedcondatetime", "Transformation": null, "Value Map": null}, {"Target": "emsfirstmedconreasonfordelay", "Source": "emsfirstmedconreasonfordelay", "Transformation": null, "Value Map": null}, {"Target": "hffirstmedcon", "Source": "hffirstmedcon", "Transformation": null, "Value Map": null}, {"Target": "shockfirstmedcon", "Source": "shockfirstmedcon", "Transformation": null, "Value Map": null}, {"Target": "hrfirstmedcon", "Source": "hrfirstmedcon", "Transformation": null, "Value Map": null}, {"Target": "sbpfirstmedcon", "Source": "sbpfirstmedcon", "Transformation": null, "Value Map": null}, {"Target": "caouthospital", "Source": "caouthospital", "Transformation": null, "Value Map": null}, {"Target": "cawitness", "Source": "cawitness", "Transformation": null, "Value Map": null}, {"Target": "capostems", "Source": "capostems", "Transformation": null, "Value Map": null}, {"Target": "bystandcpr", "Source": "bystandcpr", "Transformation": null, "Value Map": null}, {"Target": "initcarhythm", "Source": "initcarhythm", "Transformation": null, "Value Map": null}, {"Target": "initcarhythmunk", "Source": "initcarhythmunk", "Transformation": null, "Value Map": null}, {"Target": "resuscdatetime", "Source": "resuscdatetime", "Transformation": null, "Value Map": null}, {"Target": "catransferfac", "Source": "catransferfac", "Transformation": null, "Value Map": null}, {"Target": "locfirsteval", "Source": "locfirsteval", "Transformation": null, "Value Map": null}, {"Target": "tranouteddatetime", "Source": "tranouteddatetime", "Transformation": null, "Value Map": null}, {"Target": "eddisposition", "Source": "eddisposition", "Transformation": null, "Value Map": null}, {"Target": "obserorderdatetime", "Source": "obserorderdatetime", "Transformation": null, "Value Map": null}, {"Target": "symptomdate", "Source": "symptomdate24", "Transformation": null, "Value Map": null}, {"Target": "symptomtime", "Source": "symptomtime24", "Transformation": null, "Value Map": null}, {"Target": "riskscoreperf", "Source": "riskscoreperf", "Transformation": null, "Value Map": null}, {"Target": "nameriskscore", "Source": "nameriskscore", "Transformation": null, "Value Map": null}, {"Target": "timiscore", "Source": "timiscore", "Transformation": null, "Value Map": null}, {"Target": "gracescore", "Source": "gracescore", "Transformation": null, "Value Map": null}, {"Target": "ischemsymresolv", "Source": "ischemsymresolv", "Transformation": null, "Value Map": null}, {"Target": "cxrperf", "Source": "cxrperf", "Transformation": null, "Value Map": null}, {"Target": "noninvasperformed", "Source": "noninvasperformed", "Transformation": null, "Value Map": {"No - Pt. Reason": "No - Patient Reason"}}, {"Target": "noninvasplnddc", "Source": "noninvasplnddc", "Transformation": null, "Value Map": null}, {"Target": "preprocasa", "Source": null, "Transformation": {"Reference Field": "MedsArrival", "Reference Field Code": 432102000, "Derivative Field": "ArriMedCode", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON>", "Derivative Value Code": 1191, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "preprocasadose", "Source": null, "Transformation": {"Reference Field": "MedArrivalDose", "Reference Field Code": 100014233, "Derivative Field": "ArriMedCode", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON>", "Derivative Value Code": 1191, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "preprocasastartdate", "Source": null, "Transformation": {"Reference Field": "DrugStartDT", "Reference Field Code": 432102000, "Derivative Field": "ArriMedCode", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON>", "Derivative Value Code": 1191, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "preproc<PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "MedsArrival", "Reference Field Code": 432102000, "Derivative Field": "ArriMedCode", "Derivative Field Code": 100013057, "Derivative Value": "Beta Blocker", "Derivative Value Code": 33252009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "prepro<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "MedArrivalDose", "Reference Field Code": 100014233, "Derivative Field": "ArriMedCode", "Derivative Field Code": 100013057, "Derivative Value": "Beta Blocker", "Derivative Value Code": 33252009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "preprocbetablockerstartdate", "Source": null, "Transformation": {"Reference Field": "DrugStartDT", "Reference Field Code": 432102000, "Derivative Field": "ArriMedCode", "Derivative Field Code": 100013057, "Derivative Value": "Beta Blocker", "Derivative Value Code": 33252009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "preprocclopidogrel", "Source": null, "Transformation": {"Reference Field": "MedsArrival", "Reference Field Code": 432102000, "Derivative Field": "ArriMedCode", "Derivative Field Code": 100013057, "Derivative Value": "Clopidogrel", "Derivative Value Code": 32968, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "preprocclopidogreldose", "Source": null, "Transformation": {"Reference Field": "MedArrivalDose", "Reference Field Code": 100014233, "Derivative Field": "ArriMedCode", "Derivative Field Code": 100013057, "Derivative Value": "Clopidogrel", "Derivative Value Code": 32968, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "preprocclopidogrelstartdate", "Source": null, "Transformation": {"Reference Field": "DrugStartDT", "Reference Field Code": 432102000, "Derivative Field": "ArriMedCode", "Derivative Field Code": 100013057, "Derivative Value": "Clopidogrel", "Derivative Value Code": 32968, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "preprocprasugrel", "Source": null, "Transformation": {"Reference Field": "MedsArrival", "Reference Field Code": 432102000, "Derivative Field": "ArriMedCode", "Derivative Field Code": 100013057, "Derivative Value": "Prasug<PERSON>", "Derivative Value Code": 613391, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "preprocprasugreldose", "Source": null, "Transformation": {"Reference Field": "MedArrivalDose", "Reference Field Code": 100014233, "Derivative Field": "ArriMedCode", "Derivative Field Code": 100013057, "Derivative Value": "Prasug<PERSON>", "Derivative Value Code": 613391, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "preprocprasugrelstartdate", "Source": null, "Transformation": {"Reference Field": "DrugStartDT", "Reference Field Code": 432102000, "Derivative Field": "ArriMedCode", "Derivative Field Code": 100013057, "Derivative Value": "Prasug<PERSON>", "Derivative Value Code": 613391, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "preprocticagrelor", "Source": null, "Transformation": {"Reference Field": "MedsArrival", "Reference Field Code": 432102000, "Derivative Field": "ArriMedCode", "Derivative Field Code": 100013057, "Derivative Value": "Ticagrelor", "Derivative Value Code": 1116632, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "preprocticagrelordose", "Source": null, "Transformation": {"Reference Field": "MedArrivalDose", "Reference Field Code": 100014233, "Derivative Field": "ArriMedCode", "Derivative Field Code": 100013057, "Derivative Value": "Ticagrelor", "Derivative Value Code": 1116632, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "preprocticagrelorstartdate", "Source": null, "Transformation": {"Reference Field": "DrugStartDT", "Reference Field Code": 432102000, "Derivative Field": "ArriMedCode", "Derivative Field Code": 100013057, "Derivative Value": "Ticagrelor", "Derivative Value Code": 1116632, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "electrocardiorowid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "ecgcounter", "Source": "ecgcounter", "Transformation": null, "Value Map": null}, {"Target": "ecgdatetime", "Source": "ecgdatetime", "Transformation": null, "Value Map": null}, {"Target": "stemifirstnotedfmc", "Source": "stemifirstnotedfmc", "Transformation": null, "Value Map": null}, {"Target": "stemiecgfindings", "Source": "stemiecgfindings", "Transformation": null, "Value Map": null}, {"Target": "otherecgfindings", "Source": "otherecgfindings", "Transformation": null, "Value Map": null}, {"Target": "noninvasiverowid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "noninvasivetesttype", "Source": "noninvastesttype", "Transformation": null, "Value Map": null}, {"Target": "noninvastestmeth", "Source": "noninvastestmeth", "Transformation": null, "Value Map": {"Rest ": "Rest", "Stress ": "Stress"}}, {"Target": "labrowid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "troponinrowid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "tropcount", "Source": "tropcount", "Transformation": null, "Value Map": null}, {"Target": "tropcolldatetime", "Source": "tropcolldatetime", "Transformation": null, "Value Map": null}, {"Target": "tropresdatetime", "Source": "tropresdatetime", "Transformation": null, "Value Map": null}, {"Target": "troptestloc", "Source": "troptestloc", "Transformation": null, "Value Map": null}, {"Target": "labtropassay", "Source": "labtropassay", "Transformation": null, "Value Map": null}, {"Target": "tropassayurl", "Source": "poctropassay", "Transformation": null, "Value Map": null}, {"Target": "inittropvalue", "Source": "inittropvalue", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "fmccreat", "Source": "fmccreat", "Transformation": null, "Value Map": null}, {"Target": "fmccreatnd", "Source": "fmccreatnd", "Transformation": null, "Value Map": null}, {"Target": "inithgbvalue", "Source": "inithgbvalue", "Transformation": null, "Value Map": null}, {"Target": "inithgbnd", "Source": "inithgbnd", "Transformation": null, "Value Map": null}, {"Target": "inithemovalue", "Source": "inithemovalue", "Transformation": null, "Value Map": null}, {"Target": "inithemond", "Source": "inithemond", "Transformation": null, "Value Map": null}, {"Target": "initinrvalue", "Source": "initinrvalue", "Transformation": null, "Value Map": null}, {"Target": "initinrdatetime", "Source": "initinrdatetime", "Transformation": null, "Value Map": null}, {"Target": "initinrnd", "Source": "initinrnd", "Transformation": null, "Value Map": null}, {"Target": "lowp<PERSON>bsrowid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "peakcreat", "Source": "creatpeak", "Transformation": null, "Value Map": null}, {"Target": "creatpeakdatetime", "Source": "creatpeakdatetime", "Transformation": null, "Value Map": null}, {"Target": "creatpeaknd", "Source": "creatpeaknd", "Transformation": null, "Value Map": null}, {"Target": "lowhgbvalue", "Source": "lowhgbvalue", "Transformation": null, "Value Map": null}, {"Target": "lowhgbvaluedatetime", "Source": "lowhgbvaluedatetime", "Transformation": null, "Value Map": null}, {"Target": "lowhgbnd", "Source": "lowhgbnd", "Transformation": null, "Value Map": null}, {"Target": "lipidsrowid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "lipidstc6mfmc", "Source": "lipidstc6mfmc", "Transformation": null, "Value Map": null}, {"Target": "lipidstcnd6mfmc", "Source": "lipidstcnd6mfmc", "Transformation": null, "Value Map": null}, {"Target": "lipidshdl6mfmc", "Source": "lipidshdl6mfmc", "Transformation": null, "Value Map": null}, {"Target": "lipidshdlnd6mfmc", "Source": "lipidshdlnd6mfmc", "Transformation": null, "Value Map": null}, {"Target": "lipidsldl", "Source": "lipidsldl", "Transformation": null, "Value Map": null}, {"Target": "ldlnd", "Source": "ldlnd", "Transformation": null, "Value Map": null}, {"Target": "lipidstrig", "Source": "lipidstrig", "Transformation": null, "Value Map": null}, {"Target": "lipidstrignd", "Source": "lipidstrignd", "Transformation": null, "Value Map": null}, {"Target": "nvesselrowid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "nvsegmentid", "Source": "nvsegmentid", "Transformation": null, "Value Map": null}, {"Target": "nvcorovesselstenosis", "Source": "nvcorovesselstenosis", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "graftsegmentid", "Source": "graftsegmentid", "Transformation": null, "Value Map": null}, {"Target": "graftcorovesselstenosis", "Source": "graftcorovesselstenosis", "Transformation": null, "Value Map": null}, {"Target": "cabggraftvessel", "Source": "cabggraftvessel", "Transformation": null, "Value Map": null}, {"Target": "cabggraftvesselunk", "Source": "cabggraftvesselunk", "Transformation": null, "Value Map": null}, {"Target": "pciprocrowid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "pciarrivdc", "Source": "pciarrivdc", "Transformation": null, "Value Map": null}, {"Target": "pcifname", "Source": "pcifname", "Transformation": null, "Value Map": null}, {"Target": "pcilname", "Source": "pcilname", "Transformation": null, "Value Map": null}, {"Target": "pcimname", "Source": "pcimname", "Transformation": null, "Value Map": null}, {"Target": "pcinpi", "Source": "pcinpi", "Transformation": null, "Value Map": null}, {"Target": "stentsimplanted", "Source": "stentsimplanted", "Transformation": null, "Value Map": null}, {"Target": "stenttype", "Source": "stenttypefv", "Transformation": null, "Value Map": null}, {"Target": "accesssite", "Source": "accesssite", "Transformation": null, "Value Map": null}, {"Target": "pciindication", "Source": "pci_indication", "Transformation": null, "Value Map": null}, {"Target": "reason<PERSON><PERSON>otperf", "Source": "primpcinotperm", "Transformation": null, "Value Map": null}, {"Target": "mechventsupp", "Source": "mechventsupp", "Transformation": null, "Value Map": null}, {"Target": "mvsupportdevice", "Source": "mvsupportdevice", "Transformation": null, "Value Map": null}, {"Target": "cathlabact", "Source": "cathlabact", "Transformation": null, "Value Map": null}, {"Target": "cathlabactdatetime", "Source": "cathlabactdatetime", "Transformation": null, "Value Map": null}, {"Target": "cathlabactcanc", "Source": "cathlabactcanc", "Transformation": null, "Value Map": null}, {"Target": "firstdevactidatetime", "Source": "firstdevactidatetime", "Transformation": null, "Value Map": null}, {"Target": "ptp<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "ptp<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "currstenttype<PERSON>", "Source": "currstenttype<PERSON>", "Transformation": null, "Value Map": null}, {"Target": "procedure_low_molecular_weight_heparin_any", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Low Molecular Weight Heparin", "Derivative Value Code": 373294004, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_edoxaban", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Edoxaban", "Derivative Value Code": 1599538, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_clopidogrel", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Clopidogrel", "Derivative Value Code": 32968, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_unfractionated_heparin_any", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Unfractionated Heparin", "Derivative Value Code": 96382006, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_rivaroxaban", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Rivaroxaban", "Derivative Value Code": 1114195, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_prasugrel", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Prasug<PERSON>", "Derivative Value Code": 613391, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_warfarin", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Warfarin", "Derivative Value Code": 11289, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_cangrelor", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON><PERSON>", "Derivative Value Code": 1656052, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_ticagrelor", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Ticagrelor", "Derivative Value Code": 1116632, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_vorapaxar", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Vorapaxar", "Derivative Value Code": 1537034, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_bivalirudin", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Derivative Value Code": 400610005, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_gpinhibitors_any", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Glycoprotein IIb IIIa Inhibitors", "Derivative Value Code": 1000142427, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_fondapa<PERSON>ux", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Fondaparinux", "Derivative Value Code": 321208, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_apixaban", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Apixaban", "Derivative Value Code": 1364430, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "procedure_dabigatran", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Dabigatran", "Derivative Value Code": 1546356, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "eventsrowid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "cerbc", "Source": "ce_rbc", "Transformation": null, "Value Map": null}, {"Target": "cerbcdate", "Source": "ce_rbcdate", "Transformation": null, "Value Map": null}, {"Target": "cerbccabg", "Source": "ce_rbccabg", "Transformation": null, "Value Map": null}, {"Target": "nsaidadmin", "Source": "nsaidadmin", "Transformation": null, "Value Map": null}, {"Target": "hypothermiainducedfmcdc", "Source": "hypothermiainducedfmcdc", "Transformation": null, "Value Map": null}, {"Target": "hypothermiainduceddatetime", "Source": "hypothermiainduceddatetime", "Transformation": null, "Value Map": null}, {"Target": "hypoprotloc", "Source": "hypoprotloc", "Transformation": null, "Value Map": null}, {"Target": "locfmcdc", "Source": "loc_fmcdc", "Transformation": null, "Value Map": null}, {"Target": "newrequirementfordialysis", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "New Requirement for Dialysis", "Derivative Value Code": 100014076, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dialysisdatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "New Requirement for Dialysis", "Derivative Value Code": 100014076, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedingother", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Other", "Derivative Value Code": 1000142371, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedingotherdatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Other", "Derivative Value Code": 1000142371, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedingaccesssite", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Access Site", "Derivative Value Code": 1000142440, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedingaccesssitedatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Access Site", "Derivative Value Code": 1000142440, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedingsurgicalproc", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Surgical Procedure or Intervention Required for Bleeding Event", "Derivative Value Code": 112000000213, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedingsurgprocdatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Surgical Procedure or Intervention Required for Bleeding Event", "Derivative Value Code": 112000000213, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "myocardialinfarction", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Myocardial Infarction", "Derivative Value Code": 22298006, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "myocardialinfarctiondatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Myocardial Infarction", "Derivative Value Code": 22298006, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "strokehemorrhagic", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Stroke - Hemorrhagic", "Derivative Value Code": 230706003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "strokehemorrhagicdatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Stroke - Hemorrhagic", "Derivative Value Code": 230706003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "strokeundetermined", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Stroke - Undetermined", "Derivative Value Code": 230713003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "strokeundetermineddatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Stroke - Undetermined", "Derivative Value Code": 230713003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "venttachy", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Ventricular Tachycardia", "Derivative Value Code": 25569003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "venttachydatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Ventricular Tachycardia", "Derivative Value Code": 25569003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "transischemicattack", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Transient Ischemic Attack (TIA)", "Derivative Value Code": 266257000, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "transischemicattackdatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Transient Ischemic Attack (TIA)", "Derivative Value Code": 266257000, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "cardiac<PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Cardiac Arrest", "Derivative Value Code": 410429000, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "cardiacarrestdatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Cardiac Arrest", "Derivative Value Code": 410429000, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedinggenitourinary", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Genitourinary", "Derivative Value Code": 417941003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedinggenitourinarydatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Genitourinary", "Derivative Value Code": 417941003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "strokeischemic", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Stroke - Ischemic", "Derivative Value Code": 422504002, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "strokeischemicdatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Stroke - Ischemic", "Derivative Value Code": 422504002, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedingatrialfibrillation", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Atrial Fibrillation", "Derivative Value Code": 49436004, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedingatrialfibdatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Atrial Fibrillation", "Derivative Value Code": 49436004, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "intubation", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Intubation", "Derivative Value Code": 52765003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "intubationdatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Intubation", "Derivative Value Code": 52765003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "ventfib", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Ventricular Fibrillation", "Derivative Value Code": 71908006, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "ventfibdatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Ventricular Fibrillation", "Derivative Value Code": 71908006, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedinggastrointestinal", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Gastrointestinal", "Derivative Value Code": 74474003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedinggastrointestinaldatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Gastrointestinal", "Derivative Value Code": 74474003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "heartfailure", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Heart Failure", "Derivative Value Code": 84114007, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "heartfailuredatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Heart Failure", "Derivative Value Code": 84114007, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "cardiogenicshock", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Cardiogenic Shock", "Derivative Value Code": 89138009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "cardiogenicshockdatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Cardiogenic Shock", "Derivative Value Code": 89138009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedingretroperitoneal", "Source": null, "Transformation": {"Reference Field": "EpiEventOccurred", "Reference Field Code": 1000142479, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Retroperitoneal", "Derivative Value Code": 95549001, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedingretrodatetime", "Source": null, "Transformation": {"Reference Field": "EpiEventDateTime", "Reference Field Code": 10001424780, "Derivative Field": "EpiEvent", "Derivative Field Code": 1000142478, "Derivative Value": "Bleeding - Retroperitoneal", "Derivative Value Code": 95549001, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "dcdatetime", "Source": "dcdatetime", "Transformation": null, "Value Map": null}, {"Target": "dclname", "Source": "dclname", "Transformation": null, "Value Map": null}, {"Target": "dcfname", "Source": "dcfname", "Transformation": null, "Value Map": null}, {"Target": "dcmname", "Source": "dcmname", "Transformation": null, "Value Map": null}, {"Target": "dcnpi", "Source": "dcnpi", "Transformation": null, "Value Map": null}, {"Target": "dischargecomfort", "Source": "dc_comfort", "Transformation": null, "Value Map": null}, {"Target": "dccomfortdatetime", "Source": "commeasdatetime", "Transformation": null, "Value Map": null}, {"Target": "hospclinictrial", "Source": "hospclinictrial", "Transformation": null, "Value Map": null}, {"Target": "typeclintrial", "Source": "typeclintrial", "Transformation": null, "Value Map": null}, {"Target": "dischargestatus", "Source": "dcstatus", "Transformation": null, "Value Map": null}, {"Target": "dischargecardrehab", "Source": "dc_cardrehab", "Transformation": null, "Value Map": null}, {"Target": "dischargelocation", "Source": "dclocation", "Transformation": null, "Value Map": null}, {"Target": "transdatetime", "Source": "transdatetime", "Transformation": null, "Value Map": null}, {"Target": "transferpci", "Source": "transpci", "Transformation": null, "Value Map": null}, {"Target": "cabgtransferdc", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "dchospice", "Source": "dchospice", "Transformation": null, "Value Map": null}, {"Target": "hospcaredatetime", "Source": "hospcaredatetime", "Transformation": null, "Value Map": null}, {"Target": "deathcause", "Source": "deathcause", "Transformation": null, "Value Map": null}, {"Target": "discharge_ace_inhibitor_any", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Angiotensin Converting Enzyme Inhibitor", "Derivative Value Code": 41549009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_aceinhibitors_any_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Angiotensin Converting Enzyme Inhibitor", "Derivative Value Code": 41549009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_warfarin", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Warfarin", "Derivative Value Code": 11289, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_warfarin_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Warfarin", "Derivative Value Code": 11289, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dischargeasa", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON>", "Derivative Value Code": 1191, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_asa_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON>", "Derivative Value Code": 1191, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_aldoantag", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Aldosterone Antagonist", "Derivative Value Code": 372603003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_aldo<PERSON><PERSON>_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Aldosterone Antagonist", "Derivative Value Code": 372603003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_arb_any", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Angiotensin II Receptor Blocker", "Derivative Value Code": 372913009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_arb_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Angiotensin II Receptor Blocker", "Derivative Value Code": 372913009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_non_statin_any", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Non-Statin", "Derivative Value Code": 100014161, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_non_statin_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Non-Statin", "Derivative Value Code": 100014161, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_apixaban", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Apixaban", "Derivative Value Code": 1364430, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_apixaban_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Apixaban", "Derivative Value Code": 1364430, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_dabigatran", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Dabigatran", "Derivative Value Code": 1546356, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_da<PERSON><PERSON><PERSON>_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Dabigatran", "Derivative Value Code": 1546356, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_edoxaban", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Edoxaban", "Derivative Value Code": 1599538, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_ed<PERSON><PERSON><PERSON>_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Edoxaban", "Derivative Value Code": 1599538, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_prasugrel", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Prasug<PERSON>", "Derivative Value Code": 613391, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_prasug<PERSON>_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Prasug<PERSON>", "Derivative Value Code": 613391, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_ticagrelor", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Ticagrelor", "Derivative Value Code": 1116632, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_ticag<PERSON><PERSON>_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Ticagrelor", "Derivative Value Code": 1116632, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_sacubvalsar", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Sacubitril and Valsartan", "Derivative Value Code": 1656341, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_sacub<PERSON><PERSON>_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Sacubitril and Valsartan", "Derivative Value Code": 1656341, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dischargestatinany", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Statin", "Derivative Value Code": 96302009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_statin_any_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Statin", "Derivative Value Code": 96302009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_clopidogrel", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Clopidogrel", "Derivative Value Code": 32968, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_clop<PERSON>g<PERSON>_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Clopidogrel", "Derivative Value Code": 32968, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dischargebetablocker", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Beta Blocker", "Derivative Value Code": 33252009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_betablocker_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Beta Blocker", "Derivative Value Code": 33252009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_rivaroxaban", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Rivaroxaban", "Derivative Value Code": 1114195, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_rivaro<PERSON><PERSON>_meddose", "Source": null, "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Rivaroxaban", "Derivative Value Code": 1114195, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "datavrsn", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "hospclinictrialtype", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "originalotherid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "hospname", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "filename", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "version", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "datasetname", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "clientfileid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "biomeimportdt", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "careentityid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "tenantid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "distfromhospital", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "indianhealthservice", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Indian Health Service", "Derivative Value Code": 33, "Value Map": {"|Indian Health Service|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "statespecificplan", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "State-Specific Plan (non-Medicaid)", "Derivative Value Code": 36, "Value Map": {"|State-Specific Plan (non-Medicaid)|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "nonusinsurance", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Non-US Insurance", "Derivative Value Code": 100000812, "Value Map": {"|Non-US Insurance|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "procedure_heparinderivative", "Source": null, "Transformation": {"Reference Field": "ProcMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ProcMedID", "Derivative Field Code": 100013057, "Derivative Value": "Heparin Derivative", "Derivative Value Code": 100000921, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "vendorver", "Source": "vendorver", "Transformation": null, "Value Map": null}, {"Target": "vendorid", "Source": "vendorid", "Transformation": null, "Value Map": null}, {"Target": "biomeencounterid", "Source": null, "Transformation": null, "Value Map": null}]}}