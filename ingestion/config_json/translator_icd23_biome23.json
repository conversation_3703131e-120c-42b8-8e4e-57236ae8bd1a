{"TranslatorConfig": {"Version": "2.3", "Source": {"Dataset": {"Code": "ICD", "Id": 107}, "Version": "2.3", "Type": "<PERSON><PERSON><PERSON>"}, "Target": {"Dataset": {"Code": "ICD", "Id": 107}, "Version": "2.3", "Type": "Biome"}, "Translations": [{"Target": "<PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "hospname", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "ncdrpatientid", "Source": "ncdrpatientid", "Transformation": null, "Value Map": null}, {"Target": "episodeid", "Source": "episodekey", "Transformation": null, "Value Map": null}, {"Target": "otherid", "Source": "otherid", "Transformation": null, "Value Map": null}, {"Target": "patzip", "Source": "zipcode", "Transformation": null, "Value Map": null}, {"Target": "firstname", "Source": "firstname", "Transformation": null, "Value Map": null}, {"Target": "midname", "Source": "midname", "Transformation": null, "Value Map": null}, {"Target": "lastname", "Source": "lastname", "Transformation": null, "Value Map": null}, {"Target": "gender", "Source": "sex", "Transformation": null, "Value Map": null}, {"Target": "dob", "Source": "dob", "Transformation": null, "Value Map": null}, {"Target": "racewhite", "Source": "racewhite", "Transformation": null, "Value Map": null}, {"Target": "raceblack", "Source": "raceblack", "Transformation": null, "Value Map": null}, {"Target": "raceamindian", "Source": "raceamindian", "Transformation": null, "Value Map": null}, {"Target": "raceasianindian", "Source": "raceasianindian", "Transformation": null, "Value Map": null}, {"Target": "racechinese", "Source": "racechinese", "Transformation": null, "Value Map": null}, {"Target": "racefilipino", "Source": "racefilipino", "Transformation": null, "Value Map": null}, {"Target": "racejapanese", "Source": "racejapanese", "Transformation": null, "Value Map": null}, {"Target": "racekorean", "Source": "racekorean", "Transformation": null, "Value Map": null}, {"Target": "racevietnamese", "Source": "racevietnamese", "Transformation": null, "Value Map": null}, {"Target": "raceother", "Source": "raceasianother", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "racenathaw", "Transformation": null, "Value Map": null}, {"Target": "racenathaw", "Source": "racenathaw", "Transformation": null, "Value Map": null}, {"Target": "raceguamchamo", "Source": "raceguamchamorro", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "raceotherisland", "Source": "race<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "raceasian", "Source": "raceasian", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "racemexicanamchicano", "Source": "hispethnicitymexican", "Transformation": null, "Value Map": null}, {"Target": "racepuertorican", "Source": "hispethnicitypuertorico", "Transformation": null, "Value Map": null}, {"Target": "racecuban", "Source": "hispethnicitycuban", "Transformation": null, "Value Map": null}, {"Target": "raceotherhispanic", "Source": "hispethnicityotherorigin", "Transformation": null, "Value Map": null}, {"Target": "enccomb<PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "episodeuid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "arrivaldate", "Source": "arrivaldate", "Transformation": null, "Value Map": null}, {"Target": "dischargedate", "Source": "dcdate", "Transformation": null, "Value Map": null}, {"Target": "cabg", "Source": "cabg", "Transformation": null, "Value Map": null}, {"Target": "cabgdate", "Source": "cabgdate", "Transformation": null, "Value Map": null}, {"Target": "pci", "Source": "pci", "Transformation": null, "Value Map": null}, {"Target": "pcidate", "Source": "pcidate", "Transformation": null, "Value Map": null}, {"Target": "reasonforadmit", "Source": "reasonforadmit", "Transformation": null, "Value Map": null}, {"Target": "healthinsurance", "Source": "healthins", "Transformation": null, "Value Map": null}, {"Target": "insmedicare", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Medicare", "Derivative Value Code": 1, "Value Map": {"|Medicare|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "insmedicaid", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Medicaid", "Derivative Value Code": 2, "Value Map": {"|Medicaid|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "insprivate", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Private Health Insurance", "Derivative Value Code": 5, "Value Map": {"|Private Health Insurance|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "insmilitary", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Military Health Care", "Derivative Value Code": 31, "Value Map": {"|Military Health Care|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "insstate", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "State-Specific Plan (non-Medicaid)", "Derivative Value Code": 36, "Value Map": {"|State-Specific Plan (non-Medicaid)|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "insihs", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Indian Health Service", "Derivative Value Code": 33, "Value Map": {"|Indian Health Service|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "<PERSON><PERSON>us", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Non-US Insurance", "Derivative Value Code": 100000812, "Value Map": {"|Non-US Insurance|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "<PERSON><PERSON>udy", "Source": "<PERSON><PERSON>udy", "Transformation": null, "Value Map": null}, {"Target": "studyname", "Source": "studyname", "Transformation": null, "Value Map": null}, {"Target": "patientrestriction", "Source": "ptrestriction", "Transformation": null, "Value Map": null}, {"Target": "hic", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "mbi", "Source": "mbi", "Transformation": null, "Value Map": null}, {"Target": "dischargestatus", "Source": "dcstatus", "Transformation": null, "Value Map": null}, {"Target": "dischargelocation", "Source": "dclocation", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON>", "Derivative Value Code": 1191, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "warfarin", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Warfarin", "Derivative Value Code": 11289, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "rivaroxaban", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Rivaroxaban", "Derivative Value Code": 1114195, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "apixaban", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Apixaban", "Derivative Value Code": 1364430, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dabigat<PERSON>", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Dabigatran", "Derivative Value Code": 1546356, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "edoxaban", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Edoxaban", "Derivative Value Code": 1599538, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "betablocker", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Beta Blocker", "Derivative Value Code": 33252009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "ace", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Angiotensin Converting Enzyme Inhibitor", "Derivative Value Code": 41549009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "antiarrhythmicagents", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Antiarrhythmic Drug", "Derivative Value Code": 67507000, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "statin", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Statin", "Derivative Value Code": 96302009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "antiplateletagents", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Antiplatelet agent", "Derivative Value Code": 372560006, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "aldosteroneantagonist", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Aldosterone Antagonist", "Derivative Value Code": 372603003, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "arb", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Angiotensin II Receptor Blocker", "Derivative Value Code": 372913009, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "coronaryangiography", "Source": "coronaryangio", "Transformation": null, "Value Map": null}, {"Target": "priorpci", "Source": "priorpci", "Transformation": null, "Value Map": null}, {"Target": "priorpcidate", "Source": "priorpcidate", "Transformation": null, "Value Map": null}, {"Target": "priorcabg", "Source": "priorcabg", "Transformation": null, "Value Map": null}, {"Target": "priorcabgdate", "Source": "priorcabgdate", "Transformation": null, "Value Map": null}, {"Target": "epstudy", "Source": "epstudy", "Transformation": null, "Value Map": null}, {"Target": "epstudydate", "Source": "epstudydate", "Transformation": null, "Value Map": null}, {"Target": "ecg", "Source": "ecg", "Transformation": null, "Value Map": null}, {"Target": "ecgdate", "Source": "ecgdate", "Transformation": null, "Value Map": null}, {"Target": "hgbnd", "Source": "hgbnd", "Transformation": null, "Value Map": null}, {"Target": "hgb", "Source": "hgb", "Transformation": null, "Value Map": null}, {"Target": "sodiumnd", "Source": "sodiumnd", "Transformation": null, "Value Map": null}, {"Target": "sodium", "Source": "sodium", "Transformation": null, "Value Map": null}, {"Target": "bunnd", "Source": "bunnd", "Transformation": null, "Value Map": null}, {"Target": "bun", "Source": "bun", "Transformation": null, "Value Map": null}, {"Target": "ecgnormal", "Source": "ecgnormal", "Transformation": null, "Value Map": null}, {"Target": "vpqrs", "Source": "vpqrs", "Transformation": null, "Value Map": null}, {"Target": "nvpqrs", "Source": "nvpqrs", "Transformation": null, "Value Map": null}, {"Target": "abconduction", "Source": "abconduction", "Transformation": null, "Value Map": null}, {"Target": "abconductiontype", "Source": "intraventconductiontype", "Transformation": null, "Value Map": null}, {"Target": "abconductionlbbb", "Source": null, "Transformation": {"Reference Field": "AbConduction", "Reference Field Code": 4554005, "Derivative Field": "IntraVentConductionType", "Derivative Field Code": 100001142, "Derivative Value": "Left bundle branch block", "Derivative Value Code": 164909002, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "atrialrhythm", "Source": "atrialrhythm", "Transformation": null, "Value Map": null}, {"Target": "atrialrhythmsinusnode", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": 106068003, "Derivative Value": "Sinus node rhythm", "Derivative Value Code": 106067008, "Value Map": {"Sinus node rhythm": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "vpace", "Source": "vpaced", "Transformation": null, "Value Map": null}, {"Target": "hf", "Source": "hf", "Transformation": null, "Value Map": null}, {"Target": "nyha", "Source": "nyha", "Transformation": null, "Value Map": null}, {"Target": "lvefassessed", "Source": "priorlvefassessed", "Transformation": null, "Value Map": null}, {"Target": "mstreclvefdate", "Source": "priorlvefdate", "Transformation": null, "Value Map": null}, {"Target": "lvef", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "famhxsdeath", "Source": "familialsyndsuddendeath", "Transformation": null, "Value Map": null}, {"Target": "familialhxnicm", "Source": "familialhxnicm", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "iscm", "Transformation": null, "Value Map": null}, {"Target": "nidcm", "Source": "nicm", "Transformation": null, "Value Map": null}, {"Target": "nidcmtimeframe", "Source": "nicmtimeframe", "Transformation": null, "Value Map": null}, {"Target": "nidcmmxdose", "Source": "nicmgdmtdose", "Transformation": null, "Value Map": null}, {"Target": "oninotsupport", "Source": "inotropicsupport", "Transformation": null, "Value Map": null}, {"Target": "cardiac<PERSON><PERSON>", "Source": "cardiac<PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "cardiacarrestdate", "Source": "cardiacarrestdate", "Transformation": null, "Value Map": null}, {"Target": "vtarrest", "Source": "vtacharrest", "Transformation": null, "Value Map": null}, {"Target": "vfibarrest", "Source": "vfibarrest", "Transformation": null, "Value Map": null}, {"Target": "vfib", "Source": "vfib", "Transformation": null, "Value Map": null}, {"Target": "vfibdatebradycardiaarrest", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "vt", "Source": "vt", "Transformation": null, "Value Map": null}, {"Target": "vtdate", "Source": "vtdate", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON>", "Source": "<PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "priormidate", "Source": "priormidate", "Transformation": null, "Value Map": null}, {"Target": "syncope", "Source": "syncope", "Transformation": null, "Value Map": null}, {"Target": "cad", "Source": "cad", "Transformation": null, "Value Map": null}, {"Target": "currentvad", "Source": "currentlyonvad", "Transformation": null, "Value Map": null}, {"Target": "vadcandidate", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "priorcied", "Source": "priorcied", "Transformation": null, "Value Map": null}, {"Target": "indicationsforpermanentpacemaker", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "transplantcandidate", "Source": "transplantcandidate", "Transformation": null, "Value Map": null}, {"Target": "currentlyonlvad", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "paroxysmalsvthistory", "Source": "paroxysvthistory", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "afibflutter", "Source": "afib", "Transformation": null, "Value Map": null}, {"Target": "afibflutterclass", "Source": "afibclass", "Transformation": null, "Value Map": null}, {"Target": "afibfluttercardioplans", "Source": "afibfluttercardioplans", "Transformation": null, "Value Map": null}, {"Target": "primaryvalvularhd", "Source": "primaryvalvularhd", "Transformation": null, "Value Map": null}, {"Target": "otherstructabn", "Source": "otherstructabn", "Transformation": null, "Value Map": null}, {"Target": "structabntype", "Source": "structabntype", "Transformation": null, "Value Map": null}, {"Target": "structab<PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "OtherStructAbn", "Reference Field Code": 100000949, "Derivative Field": "StructAbnType", "Derivative Field Code": 100000949, "Derivative Value": "Congenital heart disease associated with sudden cardiac arrest", "Derivative Value Code": 13213009, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "priorcvd", "Source": "priorcvd", "Transformation": null, "Value Map": null}, {"Target": "diabetes", "Source": "diabetes", "Transformation": null, "Value Map": null}, {"Target": "currentdialysis", "Source": "currentdialysis", "Transformation": null, "Value Map": null}, {"Target": "chroniclungdisease", "Source": "chroniclungdisease", "Transformation": null, "Value Map": null}, {"Target": "syndromerisktype", "Source": "syndromerisktype", "Transformation": null, "Value Map": null}, {"Target": "guiddiremedthermaxdose", "Source": "iscmgdmtdose", "Transformation": null, "Value Map": null}, {"Target": "ischcardiotimeframe", "Source": "iscmtimeframe", "Transformation": null, "Value Map": null}, {"Target": "brady<PERSON><PERSON>", "Source": "brady<PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "vttype", "Source": "vttype", "Transformation": null, "Value Map": null}, {"Target": "postcardiacsurgery", "Source": "vtpostcardiacsurgery", "Transformation": null, "Value Map": null}, {"Target": "bradycardiadependent", "Source": "bradydependent", "Transformation": null, "Value Map": null}, {"Target": "reversiblecause", "Source": "vtreversecause", "Transformation": null, "Value Map": null}, {"Target": "hemoinstability", "Source": "hemoinstability", "Transformation": null, "Value Map": null}, {"Target": "vfibdate", "Source": "vfibdate", "Transformation": null, "Value Map": null}, {"Target": "perfafterrecentca", "Source": "perfafterrecentca", "Transformation": null, "Value Map": null}, {"Target": "resultsofangiography", "Source": "coronaryangioresults", "Transformation": null, "Value Map": null}, {"Target": "revascularperformed", "Source": "revascperf", "Transformation": null, "Value Map": null}, {"Target": "revascularizationoutcome", "Source": "revascoutcome", "Transformation": null, "Value Map": null}, {"Target": "priorpcicardiopresent", "Source": "priorpcicardiopresent", "Transformation": null, "Value Map": null}, {"Target": "priorpcielective", "Source": "priorpcielective", "Transformation": null, "Value Map": null}, {"Target": "prioravproc", "Source": "prior_avproc", "Transformation": null, "Value Map": null}, {"Target": "avpdate", "Source": "avp_date", "Transformation": null, "Value Map": null}, {"Target": "priorcabgcardiopresent", "Source": "priorcabgcardiopresent", "Transformation": null, "Value Map": null}, {"Target": "avpelective", "Source": "avp_elective", "Transformation": null, "Value Map": null}, {"Target": "priorcabgelective", "Source": "priorcabgelective", "Transformation": null, "Value Map": null}, {"Target": "vpacedqrs", "Source": "vpacedqrs", "Transformation": null, "Value Map": null}, {"Target": "atrialrhythmafib", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": 106068003, "Derivative Value": "Atrial fibrillation", "Derivative Value Code": 49436004, "Value Map": {"Atrial fibrillation": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "deathcause", "Source": "deathcause", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Angiotensin Receptor-Neprilysin Inhibitor", "Derivative Value Code": 112000001832, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "disc<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Renin Inhibitor", "Derivative Value Code": 426228001, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dischmedselectivesinusnodeifchannelinhibitor", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Selective Sinus Node I/f Channel Inhibitor", "Derivative Value Code": 112000001831, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "abcon<PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "AbConduction", "Reference Field Code": 4554005, "Derivative Field": "IntraVentConductionType", "Derivative Field Code": 100001142, "Derivative Value": "Delay, Non-specific", "Derivative Value Code": 698252002, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "atrialrhythmap", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": 106068003, "Derivative Value": "Atrial paced", "Derivative Value Code": 251268003, "Value Map": {"Atrial paced": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "reasonforreimplantation", "Source": "reimplant<PERSON>son", "Transformation": null, "Value Map": null}, {"Target": "abconductionrbbb", "Source": null, "Transformation": {"Reference Field": "AbConduction", "Reference Field Code": 4554005, "Derivative Field": "IntraVentConductionType", "Derivative Field Code": 100001142, "Derivative Value": "Right bundle branch block", "Derivative Value Code": 164907000, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "atrialrhythmaflutter", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": 106068003, "Derivative Value": "Atrial flutter", "Derivative Value Code": 5370000, "Value Map": {"Atrial flutter": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "proceduretype", "Source": "proceduretype", "Transformation": null, "Value Map": null}, {"Target": "proceduredate", "Source": "procedurestartdatetime", "Transformation": null, "Value Map": null}, {"Target": "procedureenddate", "Source": "procedureenddatetime", "Transformation": null, "Value Map": null}, {"Target": "genopnpi", "Source": "genopnpi", "Transformation": null, "Value Map": null}, {"Target": "genopfname", "Source": "genopfname", "Transformation": null, "Value Map": null}, {"Target": "genoplname", "Source": "genoplname", "Transformation": null, "Value Map": null}, {"Target": "genopmname", "Source": "genopmname", "Transformation": null, "Value Map": null}, {"Target": "deviceimplanted", "Source": "deviceimplanted", "Transformation": null, "Value Map": null}, {"Target": "finaldevicetype", "Source": "finaldevicetype", "Transformation": null, "Value Map": null}, {"Target": "cslvlead", "Source": "cslvlead", "Transformation": null, "Value Map": null}, {"Target": "icdimpserno", "Source": "icdimpserno", "Transformation": null, "Value Map": null}, {"Target": "icdimpid", "Source": "icdimpid", "Transformation": null, "Value Map": null}, {"Target": "icdimpmodelname", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "leadopnpi", "Source": "leadopnpi", "Transformation": null, "Value Map": null}, {"Target": "leadopfname", "Source": "leadopfname", "Transformation": null, "Value Map": null}, {"Target": "leadoplname", "Source": "leadoplname", "Transformation": null, "Value Map": null}, {"Target": "leadopmname", "Source": "leadopmname", "Transformation": null, "Value Map": null}, {"Target": "icdindication", "Source": "icdindication", "Transformation": null, "Value Map": null}, {"Target": "clinicaltrial", "Source": "clinicaltrial", "Transformation": null, "Value Map": null}, {"Target": "carrest", "Source": "carrest", "Transformation": null, "Value Map": null}, {"Target": "postmi", "Source": "postmi", "Transformation": null, "Value Map": null}, {"Target": "cardiacperf", "Source": "cardiacperf", "Transformation": null, "Value Map": null}, {"Target": "cvdissect", "Source": "cvdissect", "Transformation": null, "Value Map": null}, {"Target": "tamponade", "Source": "tamponade", "Transformation": null, "Value Map": null}, {"Target": "stroke", "Source": "stroke", "Transformation": null, "Value Map": null}, {"Target": "posttia", "Source": "posttia", "Transformation": null, "Value Map": null}, {"Target": "hematoma", "Source": "hematoma", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "hemothorax", "Source": "hemothorax", "Transformation": null, "Value Map": null}, {"Target": "pneumothorax", "Source": "pneumothorax", "Transformation": null, "Value Map": null}, {"Target": "urgentsurgery", "Source": "urgentsurgery", "Transformation": null, "Value Map": null}, {"Target": "setscrew", "Source": "setscrew", "Transformation": null, "Value Map": null}, {"Target": "leaddislodge", "Source": "leaddislodge", "Transformation": null, "Value Map": null}, {"Target": "sdmproc", "Source": "sdm_proc", "Transformation": null, "Value Map": null}, {"Target": "sdmtool", "Source": "sdm_tool", "Transformation": null, "Value Map": null}, {"Target": "sdmtoolname", "Source": "sdm_tool_name", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "bradindpres", "Source": "bradindpres", "Transformation": null, "Value Map": null}, {"Target": "primbradindpres", "Source": "primbradindpres", "Transformation": null, "Value Map": null}, {"Target": "primtachindpres", "Source": "primtachindpres", "Transformation": null, "Value Map": null}, {"Target": "reasonpacingindicated", "Source": "reasonpacindic", "Transformation": null, "Value Map": null}, {"Target": "reasonpacindicsicksinussyndrome", "Source": null, "Transformation": {"Reference Field": "ReasonPacIndic", "Reference Field Code": 100001097, "Derivative Field": "ReasonPacIndic", "Derivative Field Code": 100001097, "Derivative Value": "Sick sinus syndrome", "Derivative Value Code": 36083008, "Value Map": {"Sick sinus syndrome": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "pripacmode", "Source": "pripacmode", "Transformation": null, "Value Map": null}, {"Target": "reimpbattery", "Source": null, "Transformation": {"Reference Field": "ReImplantReason", "Reference Field Code": 100000991, "Derivative Field": "ReImplantReason", "Derivative Field Code": 100000991, "Derivative Value": "Reimplant Reason - End of Battery Life", "Derivative Value Code": 100001088, "Value Map": {"Reimplant Reason - End of Battery Life": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "deviceexplant", "Source": "deviceexplant", "Transformation": null, "Value Map": null}, {"Target": "explantdate", "Source": "explantdate", "Transformation": null, "Value Map": null}, {"Target": "explanttreatrecommend", "Source": "explanttreatment", "Transformation": null, "Value Map": null}, {"Target": "explantdevicename", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "icdexpid", "Source": "icdexpid", "Transformation": null, "Value Map": null}, {"Target": "icdexpserno", "Source": "icdexpserno", "Transformation": null, "Value Map": null}, {"Target": "reasonpacindiccompleteheartblock", "Source": null, "Transformation": {"Reference Field": "ReasonPacIndic", "Reference Field Code": 100001097, "Derivative Field": "ReasonPacIndic", "Derivative Field Code": 100001097, "Derivative Value": "Complete heart block", "Derivative Value Code": 27885002, "Value Map": {"Complete heart block": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "reimpupgrade", "Source": null, "Transformation": {"Reference Field": "ReImplantReason", "Reference Field Code": 100000991, "Derivative Field": "ReImplantReason", "Derivative Field Code": 100000991, "Derivative Value": "Reimplant Reason - Upgrade", "Derivative Value Code": 100001094, "Value Map": {"Reimplant Reason - Upgrade": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "reasonpacindicanticipatedrequirementofgt40prvpacing", "Source": null, "Transformation": {"Reference Field": "ReasonPacIndic", "Reference Field Code": 100001097, "Derivative Field": "ReasonPacIndic", "Derivative Field Code": 100001097, "Derivative Value": "Anticipated requirement of > 40% RV pacing", "Derivative Value Code": 100000931, "Value Map": {"Anticipated requirement of > 40% RV pacing": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "reimple<PERSON><PERSON>v", "Source": null, "Transformation": {"Reference Field": "ReImplantReason", "Reference Field Code": 100000991, "Derivative Field": "ReImplantReason", "Derivative Field Code": 100000991, "Derivative Value": "Reimplant Reason - Replaced At Time of Lead Revision", "Derivative Value Code": 100001092, "Value Map": {"Reimplant Reason - Replaced At Time of Lead Revision": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "reasonpacindicchronotropicincompetence", "Source": null, "Transformation": {"Reference Field": "ReasonPacIndic", "Reference Field Code": 100001097, "Derivative Field": "ReasonPacIndic", "Derivative Field Code": 100001097, "Derivative Value": "Chronotropic incompetence", "Derivative Value Code": 427989008, "Value Map": {"Chronotropic incompetence": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "reasonpacindichfunresponsivetogdmt", "Source": null, "Transformation": {"Reference Field": "ReasonPacIndic", "Reference Field Code": 100001097, "Derivative Field": "ReasonPacIndic", "Derivative Field Code": 100001097, "Derivative Value": "HF Unresponsive to GDMT", "Derivative Value Code": 112000002017, "Value Map": {"HF Unresponsive to GDMT": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "leadrowid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "leadsern<PERSON>", "Source": "leadsern<PERSON>", "Transformation": null, "Value Map": null}, {"Target": "leadtype", "Source": "leadtype", "Transformation": null, "Value Map": null}, {"Target": "leadlocation", "Source": "leadlocation", "Transformation": null, "Value Map": null}, {"Target": "exleaddate", "Source": "exleaddate", "Transformation": null, "Value Map": null}, {"Target": "exleadstat", "Source": "exleadstat", "Transformation": null, "Value Map": null}, {"Target": "leadid", "Source": "leadid", "Transformation": null, "Value Map": null}, {"Target": "leadmodelname", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "leadcounter", "Source": "leadcounter", "Transformation": null, "Value Map": null}, {"Target": "datavrsn", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "proceduretime", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "originalotherid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "filename", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "version", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "datasetname", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "clientfileid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "biomeimportdt", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "careentityid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "tenantid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "yearmonth", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "casesequencenumber", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "age", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "distfromhospital", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "mib", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "reasonpacindic<PERSON>", "Source": null, "Transformation": {"Reference Field": "ReasonPacIndic", "Reference Field Code": 100001097, "Derivative Field": "ReasonPacIndic", "Derivative Field Code": 100001097, "Derivative Value": "Mobitz Type II", "Derivative Value Code": 28189009, "Value Map": {"Mobitz Type II": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "reasonpacindic21avblock", "Source": null, "Transformation": {"Reference Field": "ReasonPacIndic", "Reference Field Code": 100001097, "Derivative Field": "ReasonPacIndic", "Derivative Field Code": 100001097, "Derivative Value": "2:1 AV Block", "Derivative Value Code": 54016002, "Value Map": {"2:1 AV Block": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "reasonpacindicatrioventricularnodeablation", "Source": null, "Transformation": {"Reference Field": "ReasonPacIndic", "Reference Field Code": 100001097, "Derivative Field": "ReasonPacIndic", "Derivative Field Code": 100001097, "Derivative Value": "Atrioventricular Node Ablation", "Derivative Value Code": 428663009, "Value Map": {"Atrioventricular Node Ablation": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "zipcodena", "Transformation": null, "Value Map": null}, {"Target": "icdimpmfr", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "icdexpmfr", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "partname", "Source": "partname", "Transformation": null, "Value Map": null}, {"Target": "particid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "vendorid", "Source": "vendorid", "Transformation": null, "Value Map": null}, {"Target": "structabntype_arvc", "Source": null, "Transformation": {"Reference Field": "OtherStructAbn", "Reference Field Code": 100000949, "Derivative Field": "StructAbnType", "Derivative Field Code": 100000949, "Derivative Value": "Arrhythmogenic right ventricular cardiomyopathy (ARVC)", "Derivative Value Code": 281170005, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "structabntype_hcm", "Source": null, "Transformation": {"Reference Field": "OtherStructAbn", "Reference Field Code": 100000949, "Derivative Field": "StructAbnType", "Derivative Field Code": 100000949, "Derivative Value": "Hypertrophic cardiomyopathy (HCM) with high risk features", "Derivative Value Code": 233873004, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "structabntype_infiltrative", "Source": null, "Transformation": {"Reference Field": "OtherStructAbn", "Reference Field Code": 100000949, "Derivative Field": "StructAbnType", "Derivative Field Code": 100000949, "Derivative Value": "Infiltrative", "Derivative Value Code": 100001018, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "structabntype_lvc", "Source": null, "Transformation": {"Reference Field": "OtherStructAbn", "Reference Field Code": 100000949, "Derivative Field": "StructAbnType", "Derivative Field Code": 100000949, "Derivative Value": "LV structural abnormality associated with risk for sudden cardiac arrest", "Derivative Value Code": 87878005, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "rstudy<PERSON><PERSON>", "Source": "studyptid", "Transformation": null, "Value Map": null}, {"Target": "class1class2brady", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "reimpinfection", "Source": null, "Transformation": {"Reference Field": "ReImplantReason", "Reference Field Code": 100000991, "Derivative Field": "ReImplantReason", "Derivative Field Code": 100000991, "Derivative Value": "Reimplant Reason - Infection", "Derivative Value Code": 100001091, "Value Map": {"Reimplant Reason - Infection": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "reimprecall", "Source": null, "Transformation": {"Reference Field": "ReImplantReason", "Reference Field Code": 100000991, "Derivative Field": "ReImplantReason", "Derivative Field Code": 100000991, "Derivative Value": "Reimplant Reason - Under Manufacturer Advisory/Recall", "Derivative Value Code": 100001093, "Value Map": {"Reimplant Reason - Under Manufacturer Advisory/Recall": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "reimpfaulty", "Source": null, "Transformation": {"Reference Field": "ReImplantReason", "Reference Field Code": 100000991, "Derivative Field": "ReImplantReason", "Derivative Field Code": 100000991, "Derivative Value": "Reimplant Reason - <PERSON><PERSON><PERSON> Connector/Header", "Derivative Value Code": 100001089, "Value Map": {"Reimplant Reason - Faulty Connector/Header": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "reimprelocation", "Source": null, "Transformation": {"Reference Field": "ReImplantReason", "Reference Field Code": 100000991, "Derivative Field": "ReImplantReason", "Derivative Field Code": 100000991, "Derivative Value": "Reimplant Reason - Device Relocation", "Derivative Value Code": 100001087, "Value Map": {"Reimplant Reason - Device Relocation": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "reimpmalfx", "Source": null, "Transformation": {"Reference Field": "ReImplantReason", "Reference Field Code": 100000991, "Derivative Field": "ReImplantReason", "Derivative Field Code": 100000991, "Derivative Value": "Reimplant Reason - Generator Malfunction", "Derivative Value Code": 100001090, "Value Map": {"Reimplant Reason - Generator Malfunction": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "leaddislodgeloc", "Source": "leaddislodgeloc", "Transformation": null, "Value Map": null}, {"Target": "mineralocorticoidreceptorantagonist", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "dischmedcode", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "dischmedprescribed", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "icdimpudi", "Source": "icdimpudi", "Transformation": null, "Value Map": null}, {"Target": "leadidentification3", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "leadidentification2", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "leadid2", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "leadid1", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "leadudi3", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "leadserno3", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "leadserno2", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "lead<PERSON>", "Source": "lead<PERSON>", "Transformation": null, "Value Map": null}, {"Target": "leadudi2", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "leadid3", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "exleaddate2", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "exleadstat2", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "deathprocedure", "Source": "deathprocedure", "Transformation": null, "Value Map": null}, {"Target": "procedureendtime", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "atrialrhythmatrialtach", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": 106068003, "Derivative Value": "Atrial tachycardia", "Derivative Value Code": 276796006, "Value Map": {"Atrial tachycardia": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "epstudydateunk", "Source": "epstudydateunk", "Transformation": null, "Value Map": null}, {"Target": "ventarrythinduced", "Source": "ventarrythinduced", "Transformation": null, "Value Map": null}, {"Target": "abconductionrbbbandlbbb", "Source": null, "Transformation": {"Reference Field": "AbConduction", "Reference Field Code": 4554005, "Derivative Field": "IntraVentConductionType", "Derivative Field Code": 100001142, "Derivative Value": "Alternating RBBB and LBBB", "Derivative Value Code": 32758004, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "atrialrhythmundocdatrial", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "atrialrhythmsinusarrest", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": 106068003, "Derivative Value": "Sinus arrest", "Derivative Value Code": 5609005, "Value Map": {"Sinus arrest": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "discharge_aspirin_any", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON>", "Derivative Value Code": 1191, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "partnpi", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "timeframe", "Source": "timeframe", "Transformation": null, "Value Map": null}, {"Target": "transmissionnumber", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "structabntype_otherstructabn", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "raceasianother", "Source": "raceasianother", "Transformation": null, "Value Map": null}, {"Target": "biomeencounterid", "Source": null, "Transformation": null, "Value Map": null}]}}