{"TranslatorConfig": {"Version": "1.0", "Source": {"Dataset": {"Code": "AFib", "Id": 117}, "Version": "1.0", "Type": "<PERSON><PERSON><PERSON>"}, "Target": {"Dataset": {"Code": "AFib", "Id": 117}, "Version": "1.0", "Type": "Biome"}, "Translations": [{"Target": "ncdrpatientid", "Source": "ncdrpatientid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "particid", "Source": "partid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "partname", "Source": "partname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "firstname", "Source": "firstname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lastname", "Source": "lastname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "midname", "Source": "midname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ssnna", "Source": "ssnna", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ssn", "Source": "ssn", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": null, "custom string": 1}, "is_computation": null}]}, {"Target": "otherid", "Source": "otherid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dob", "Source": "dob", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gender", "Source": "sex", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "zipcodena", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postalcode", "Source": "zipcode", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racewhite", "Source": "racewhite", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceasian", "Source": "raceasian", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceasianindian", "Source": "raceasianindian", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racechinese", "Source": "racechinese", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racefilipino", "Source": "racefilipino", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racejapanese", "Source": "racejapanese", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racekorean", "Source": "racekorean", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racevietnamese", "Source": "racevietnamese", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceotherasian", "Source": "raceasianother", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racenathaw", "Source": "racenathaw", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceotherisland", "Source": "race<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dischargedate", "Source": "dcdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "episodeid", "Source": "episodekey", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "arrivaldate", "Source": "arrivaldate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "healthinsurance", "Source": "healthins", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "insprivate", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "Private Health Insurance", "Derivative Value Code": "5", "Value Map": {"|Private Health Insurance|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "hic", "Source": "hic", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "insmedicare", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "Medicare", "Derivative Value Code": "1", "Value Map": {"|Medicare|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON>udy", "Source": "<PERSON><PERSON>udy", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "patientrestriction", "Source": "ptrestriction", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "studyname", "Source": "studyname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afeqtperformed", "Source": "afeqtbase", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "priorablstratcomplexfracatrialelectrogram", "Source": null, "Transformation": {"Reference Field": "AFibPriorAblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Complex Fractionated Atrial Electrogram", "Derivative Value Code": "100000910", "Value Map": {"|Complex Fractionated Atrial Electrogram|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "priorablstratconvergentprocedure", "Source": null, "Transformation": {"Reference Field": "AFibPriorAblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Convergent Procedure", "Derivative Value Code": "100000911", "Value Map": {"|Convergent Procedure|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "priorablstratctcryoablation", "Source": null, "Transformation": {"Reference Field": "AFibPriorAblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Cryoablation", "Derivative Value Code": "233161001", "Value Map": {"|Cryoablation|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "priorablstratempiriclalinearlesions", "Source": null, "Transformation": {"Reference Field": "AFibPriorAblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Empiric LA Linear Lesions", "Derivative Value Code": "100000912", "Value Map": {"|Empiric LA Linear Lesions|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "priorablstratganglionplexusablation", "Source": null, "Transformation": {"Reference Field": "AFibPriorAblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Ganglion Plexus Ablation", "Derivative Value Code": "100000914", "Value Map": {"|Ganglion Plexus Ablation|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "priorablstratpulmonaryveinisolation", "Source": null, "Transformation": {"Reference Field": "AFibPriorAblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Pulmonary Vein Isolation", "Derivative Value Code": "100000915", "Value Map": {"|Pulmonary Vein Isolation|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "priorablstratrotorbasedmapping", "Source": null, "Transformation": {"Reference Field": "AFibPriorAblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Rotor Based Mapping", "Derivative Value Code": "100000917", "Value Map": {"|Rotor Based Mapping|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "priorablstratsegmentalpvablation", "Source": null, "Transformation": {"Reference Field": "AFibPriorAblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Segmental PV Ablation", "Derivative Value Code": "100000916", "Value Map": {"|Segmental PV Ablation|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "priorablstratwideareacircumablation", "Source": null, "Transformation": {"Reference Field": "AFibPriorAblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Wide Area Circumferential Ablation", "Derivative Value Code": "100000918", "Value Map": {"|Wide Area Circumferential Ablation|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "priorablstratfocalablation", "Source": null, "Transformation": {"Reference Field": "AFibPriorAblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Focal Ablation", "Derivative Value Code": "100000913", "Value Map": {"|Focal Ablation|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "atrialfibrillationtermination", "Source": "prevafibterm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibpharmacologiccardioversion", "Source": "prevafibtermpc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibdccardioversion", "Source": "prevafibtermdc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibcatheterablation", "Source": "prevafibtermca", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibcatheterablationdate", "Source": "afibcathabldate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibsurgicalablation", "Source": "prevafibtermsa", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibsurgicalablationdate", "Source": "afibsurgabldate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "atrialfluttertermination", "Source": "prevaflterm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "atrialflutterpharmacologiccardioversion", "Source": "prevafltermpc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "atrialflutterdccardioversion", "Source": "prevafltermdc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "atrialfluttercatheterablation", "Source": "prevafltermca", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "atrialfluttercatheterablationdate", "Source": "afibfluttercathabldate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "avnodeablationwithpacemakerimplantation", "Source": "avnodepace", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "atrialflutter", "Source": "aflutter", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "valvularatrialfibrillation", "Source": "valvularaf", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hxmitralvalvereplace", "Source": "hxmvreplace", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hxmitralvalverepair", "Source": "hxmvrepair", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mechanicalvalveinmitralposition", "Source": "mechvalvemitpos", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afasymptomsexperienced", "Source": "sympduringafflutter", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "transthoracicechocardiography", "Source": "tteperf", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "transthoracicechocardiographydate", "Source": "ttedate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "transesophagealechocardiography", "Source": "teeperf", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "transesophagealechocardiographydate", "Source": "teedate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "imaging", "Source": "baselineimagingperf", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ctperformed", "Source": "ctperformed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ctimagingdate", "Source": "ctimagingdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "heartmriwocontrast", "Source": "mrperformed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "heartmriwocontrastdate", "Source": "mrdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preproclvef", "Source": "lvef", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preproclvefassessed", "Source": "lvefassessed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preprocatrialthrombusdetected", "Source": "atrialthromdetect", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "currentatrialfib", "Source": "afeqts1q1", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lvhypertrophy", "Source": "lvhyper", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lasize", "Source": "lasize", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rasize", "Source": "rasize", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mitralvalveregurgitation", "Source": "mitralre<PERSON>rg", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mitralvalvestenosis", "Source": "mitralstenosis", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "atrialrhythmafib", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Atrial fibrillation", "Derivative Value Code": "49436004", "Value Map": {"|Atrial fibrillation|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "atrialrhythmaflutter", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Atrial flutter", "Derivative Value Code": "5370000", "Value Map": {"|Atrial flutter|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "cardiomyopathytype", "Source": "priorcmtype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vasculardiseasetype", "Source": "priorvd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preprocprothrombinnd", "Source": "ptnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preprocprothrombintime", "Source": "pt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "inrnd", "Source": "inrnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "inr", "Source": "inr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preproccreatnd", "Source": "preproccreatnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preproccreat", "Source": "preproccreat", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preprocbilirubinnd", "Source": "bilirubinnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preprocbilirubin", "Source": "bilirubin", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preprocaspartateaminotransferasend", "Source": "astnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preprocaspartateaminotransferase", "Source": "ast", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preprocalanineaminotransferasend", "Source": "altnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preprocalanineaminotransferase", "Source": "alt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preprocalkalinephosphatasend", "Source": "alkphosnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preprocalkalinephosphatase", "Source": "alkphos", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "height", "Source": "height", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "weight", "Source": "weight", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preprocpulse", "Source": "pulse", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "systolicbp", "Source": "systolicbp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "diastolicbp", "Source": "diastolicbp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "sleep<PERSON><PERSON>", "Source": "sleep<PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "sleepapnearxfollowed", "Source": "sleepapnearxfollowed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "chroniclungdisease", "Source": "chroniclungdisease", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cardiomyopathy", "Source": "cm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "priorcad", "Source": "cad", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cha2ds2vascchf", "Source": "chadchf", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "chadlvdysf", "Source": "chadlvdysf", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cha2ds2vaschypertension", "Source": "chad<PERSON><PERSON>cont", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cha2ds2vascdiabetesmellitus", "Source": "chaddm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "chadstroke", "Source": "chadstroke", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cha2ds2vasctia", "Source": "chad<PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cha2ds2vascthromboembolicevent", "Source": "chadte", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hasbleduncontrolledhypertension", "Source": "hb<PERSON><PERSON>un<PERSON>t", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hasbledabnormalrenalfunction", "Source": "hbabnrenal", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hasbledabnormalliverfunction", "Source": "hbabnliver", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hasbledstroke", "Source": "hbstroke", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hasbledbleeding", "Source": "hbbleed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "has<PERSON><PERSON>r", "Source": "h<PERSON>binr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hasbledalcohol", "Source": "hbalcohol", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hasbledantiplatelet", "Source": "hbdrugap", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hasblednsaids", "Source": "hbdrugnsaid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "nyha", "Source": "nyha", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "atrialfibrillationclassification", "Source": "afibclass", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "atrialflutterclassification", "Source": "afluttertype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preprocdigoxin", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Digoxin", "Derivative Value Code": "3407", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocdiltiazem", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Diltiazem", "Derivative Value Code": "3443", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocverapamil", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Verapamil", "Derivative Value Code": "11170", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocamiodarone", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Amiodarone", "Derivative Value Code": "703", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocdisopyramide", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Disopyramide", "Derivative Value Code": "3541", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocdofetilide", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Dofetilide", "Derivative Value Code": "49247", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocdronedarone", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Dr<PERSON><PERSON><PERSON>", "Derivative Value Code": "233698", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocflecainide", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Flecainide", "Derivative Value Code": "4441", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocprocainamide", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Procainamide", "Derivative Value Code": "8700", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocpropafenone", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Propafenone", "Derivative Value Code": "8754", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocquinidine", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Quinidine", "Derivative Value Code": "9068", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocsotalol", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Sotalol", "Derivative Value Code": "9947", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocaggrenox", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Aggrenox", "Derivative Value Code": "226718", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocaspirin", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "<PERSON><PERSON><PERSON>", "Derivative Value Code": "1191", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocvorapaxar", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Vorapaxar", "Derivative Value Code": "1537034", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocclopidogrel", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Clopidogrel", "Derivative Value Code": "32968", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocprasugrel", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Prasug<PERSON>", "Derivative Value Code": "613391", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocticagrelor", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Ticagrelor", "Derivative Value Code": "1116632", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocticlopidine", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Ticlopidine", "Derivative Value Code": "10594", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocfondaparinux", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Fondaparinux", "Derivative Value Code": "321208", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocheparinderivative", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Heparin Derivative", "Derivative Value Code": "100000921", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preproclowmolecularweightheparin", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Low Molecular Weight Heparin", "Derivative Value Code": "373294004", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocunfractionatedheparin", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Unfractionated Heparin", "Derivative Value Code": "96382006", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocwarfarin", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Warfarin", "Derivative Value Code": "11289", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocapixaban", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Apixaban", "Derivative Value Code": "1364430", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocdabigatran", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Dabigatran", "Derivative Value Code": "1546356", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocedoxaban", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Edoxaban", "Derivative Value Code": "1599538", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocrivaroxaban", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Rivaroxaban", "Derivative Value Code": "1114195", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocace", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Angiotensin Converting Enzyme Inhibitor", "Derivative Value Code": "41549009", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprocangiotensiniireceptorantagonist", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Angiotensin II Receptor Blocker", "Derivative Value Code": "372913009", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preproc<PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Beta Blocker", "Derivative Value Code": "33252009", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "preprochydroxymethylglutarylcoareductaseinhibitors", "Source": null, "Transformation": {"Reference Field": "MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": "100013057", "Derivative Value": "Statin", "Derivative Value Code": "96302009", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "proceduretype", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": "Catheter Ablation (Afib)", "custom string": 1}, "is_computation": null}]}, {"Target": "proceduredate", "Source": "procedurestartdatetime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "currablstratcomplexfracatrialelectrogram", "Source": null, "Transformation": {"Reference Field": "AblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Complex Fractionated Atrial Electrogram", "Derivative Value Code": "100000910", "Value Map": {"|Complex Fractionated Atrial Electrogram|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "currablstratconvergentprocedure", "Source": null, "Transformation": {"Reference Field": "AblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Convergent Procedure", "Derivative Value Code": "100000911", "Value Map": {"|Convergent Procedure|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "currablstratctcryoablation", "Source": null, "Transformation": {"Reference Field": "AblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Cryoablation", "Derivative Value Code": "233161001", "Value Map": {"|Cryoablation|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "currablstratempiriclalinearlesions", "Source": null, "Transformation": {"Reference Field": "AblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Empiric LA Linear Lesions", "Derivative Value Code": "100000912", "Value Map": {"|Empiric LA Linear Lesions|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "currablstratfocalablation", "Source": null, "Transformation": {"Reference Field": "AblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Focal Ablation", "Derivative Value Code": "100000913", "Value Map": {"|Focal Ablation|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "currablstratganglionplexusablation", "Source": null, "Transformation": {"Reference Field": "AblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Ganglion Plexus Ablation", "Derivative Value Code": "100000914", "Value Map": {"|Ganglion Plexus Ablation|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "currablstratpulmonaryveinisolation", "Source": null, "Transformation": {"Reference Field": "AblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Pulmonary Vein Isolation", "Derivative Value Code": "100000915", "Value Map": {"|Pulmonary Vein Isolation|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "currablstratrotorbasedmapping", "Source": null, "Transformation": {"Reference Field": "AblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Rotor Based Mapping", "Derivative Value Code": "100000917", "Value Map": {"|Rotor Based Mapping|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "currablstratsegmentalpvablation", "Source": null, "Transformation": {"Reference Field": "AblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Segmental PV Ablation", "Derivative Value Code": "100000916", "Value Map": {"|Segmental PV Ablation|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "currablstratwideareacircumablation", "Source": null, "Transformation": {"Reference Field": "AblStrategyCode", "Reference Field Code": "18286008:363702006=49436004", "Derivative Field": "AblStrategyCode", "Derivative Field Code": "18286008:363702006=49436004", "Derivative Value": "Wide Area Circumferential Ablation", "Derivative Value Code": "100000918", "Value Map": {"|Wide Area Circumferential Ablation|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "opera_npi", "Source": "opera_npi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "opera_lastname", "Source": "opera_lastname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "opera_firstname", "Source": "opera_firstname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "opera_midname", "Source": "opera_midname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "procmedanticoagulant", "Source": "intraprocanticoag", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "procmeduninterruptedwarfarin", "Source": "warfarin", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "procheparin", "Source": "procheparin", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "procheparininitadmin", "Source": "procheparininitadmin", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pro<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "pro<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "procmedotheranticoagulant", "Source": "procotheranticoag", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "phrenicnerveevaluation", "Source": "phrenicnerveeval", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "evaluatedbypacingmaneuver", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "administrationofsedative", "Source": "anesthesia", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pviassessedwithcircumferentialveincatheter", "Source": "pviassescircveincath", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "numberofveinspresent", "Source": "numveinspres", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "numberofveinstargeted", "Source": "numveinstarg", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "numberofveinsisolated", "Source": "numveinsiso", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "isolationconfirmation", "Source": "isolationconfirm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ablatedadjunctivelesion", "Source": "abllesion", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ablatedadjunctive<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "AblLesion", "Reference Field Code": 100000926, "Derivative Field": "AblLesionLoc", "Derivative Field Code": "112000001854", "Derivative Value": "CTI", "Derivative Value Code": "100000981", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "transseptalcatheterization", "Source": "transseptcath", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cardioversion", "Source": "cvtype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cardioversionusingmedication", "Source": "pharmacv", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "directcurrentcardioversion", "Source": "dccv", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "atrialfluttertachycardiapresent", "Source": "aftachpresent", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "guidancemethod", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_concat", "Args": {"field_map": {"guidancemethodcardiacfluoroscopy": 82327001, "guidancemethodelectroanatomicmapping": 100000908, "guidancemethodintracardiacthreedimensionalechocardiography": 448761005, "guidancemethodfluoroscopy": 44491008}}, "is_computation": null}]}, {"Target": "guidancemethodelectroanatomicmapping", "Source": null, "Transformation": {"Reference Field": "GuidanceMethodID", "Reference Field Code": 100000919, "Derivative Field": "GuidanceMethodID", "Derivative Field Code": "100000919", "Derivative Value": "Electro Anatomic Mapping", "Derivative Value Code": "100000908", "Value Map": {"|Electro Anatomic Mapping|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "guidancemethodintracardiacthreedimensionalechocardiography", "Source": null, "Transformation": {"Reference Field": "GuidanceMethodID", "Reference Field Code": 100000919, "Derivative Field": "GuidanceMethodID", "Derivative Field Code": "100000919", "Derivative Value": "Intracardiac three dimensional echocardiography", "Derivative Value Code": "448761005", "Value Map": {"|Intracardiac three dimensional echocardiography|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "manipulationofcatheter", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_concat", "Args": {"field_map": {"manipulationofcathetermanual": "Catheter Manipulation - Manual", "manipulationofcathetermagnetic": "Catheter Manipulation - Magnetic", "manipulationofcatheterrobotic": "Catheter Manipulation - Robotic"}}, "is_computation": null}]}, {"Target": "manipulationofcathetermanual", "Source": null, "Transformation": {"Reference Field": "CathManipulation", "Reference Field Code": 103712006, "Derivative Field": "CathManipulation", "Derivative Field Code": "103712006", "Derivative Value": "Catheter Manipulation - Manual", "Derivative Value Code": "100000958", "Value Map": {"|Catheter Manipulation - Manual|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON>ker<PERSON>", "Source": "fluorodosekerm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cumulativeairkermaunits", "Source": "fluorodosekerm_unit", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doseareaproduct", "Source": "fluorodosedap", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doseareaproductunits", "Source": "fluorodosedap_unit", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "procedurestatus", "Source": "procstatus", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postcardiacarrest", "Source": "carrest", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postmi", "Source": "postmi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postairembolism", "Source": "airembol", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postbradycardia", "Source": "bradyevent", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postbradycardiarxpermanentpacemaker", "Source": "reqpermpacing", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postcardiothromboembolic", "Source": "cardte", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "posthf", "Source": "posthf", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postheartvalvedisorder", "Source": "valvedam", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postleftatrialthrombus", "Source": "lathrombus", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "posttamponade", "Source": "perieffusiontamp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postpericardialeffusionreqintervention", "Source": "perieffusioninterv", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postcardiacsurgery", "Source": "csurgery", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postanaphy<PERSON><PERSON>", "Source": "anaphylaxis", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "posthemorrhagemorphologicabnormality", "Source": "hemorrhage", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postsepsis", "Source": "sepsis", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postrenalfailure", "Source": "acuterf", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postgenitourinarytracthemorrhage", "Source": "postgubleed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postphrenicnervedamage", "Source": "<PERSON>hren<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postperipheralnerveinjury", "Source": "peripnerveinjury", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postcerebrovascularaccident", "Source": "stroke", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "posttia", "Source": "posttia", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postaccesssitebleedingreqtransfusion", "Source": "accessbleed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postarterialthrombosis", "Source": "artthromb", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postarteriovenous<PERSON><PERSON><PERSON>", "Source": "avfistula", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postdeepvenous<PERSON><PERSON><PERSON>is", "Source": "dvt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postbleedhematoma", "Source": "postbleedhematoma", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postpseudoaneurysm", "Source": "pseudoaneurysm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postvascularinjuryrequiringsurgicalintervention", "Source": "vascularinjury", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "posthemothorax", "Source": "hemothorax", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hemothoraxrequiringdrainage", "Source": "hemothoraxreqdrng", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postpneumothorax", "Source": "pneumothorax", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postpneumothoraxrequiringdrainage", "Source": "pneumothoraxreqdrng", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postrespiratoryfailure", "Source": "respfailure", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postpleuraleffusion", "Source": "pleuraleffusion", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postpneumonia", "Source": "pneumonia", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postpulmonaryembolism", "Source": "pulmonaryembolism", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postpulmonaryveindamagedissection", "Source": "pvdd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dischatrialrhythmsinusnode", "Source": null, "Transformation": {"Reference Field": "DCAtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "DCAtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Sinus node rhythm", "Derivative Value Code": "106067008", "Value Map": {"|Sinus node rhythm|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "dischargestatus", "Source": "dcstatus", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dischargelocation", "Source": "dclocation", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "deathcause", "Source": "deathcause", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dchospice", "Source": "dchospice", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "deathprocedure", "Source": "deathprocedure", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dischargedigoxin", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Digoxin", "Derivative Value Code": "3407", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargediltiazem", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Diltiazem", "Derivative Value Code": "3443", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargeverapamil", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Verapamil", "Derivative Value Code": "11170", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargeamiodarone", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Amiodarone", "Derivative Value Code": "703", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargedisopyramide", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Disopyramide", "Derivative Value Code": "3541", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargedofetilide", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Dofetilide", "Derivative Value Code": "49247", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargedronedarone", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Dr<PERSON><PERSON><PERSON>", "Derivative Value Code": "233698", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargeflecainide", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Flecainide", "Derivative Value Code": "4441", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargeprocainamide", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Procainamide", "Derivative Value Code": "8700", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargepropafenone", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Propafenone", "Derivative Value Code": "8754", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargequinidine", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Quinidine", "Derivative Value Code": "9068", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargesotalol", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Sotalol", "Derivative Value Code": "9947", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargeaggrenox", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Aggrenox", "Derivative Value Code": "226718", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_asa", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "<PERSON><PERSON><PERSON>", "Derivative Value Code": "1191", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_vorapaxar", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Vorapaxar", "Derivative Value Code": "1537034", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_clopidogrel", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Clopidogrel", "Derivative Value Code": "32968", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_prasugrel", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Prasug<PERSON>", "Derivative Value Code": "613391", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_ticagrelor", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Ticagrelor", "Derivative Value Code": "1116632", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_ticlopidine", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Ticlopidine", "Derivative Value Code": "10594", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargefondaparinux", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Fondaparinux", "Derivative Value Code": "321208", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargeheparinderivative", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Heparin Derivative", "Derivative Value Code": "100000921", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargelowmolecularweightheparin", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Low Molecular Weight Heparin", "Derivative Value Code": "373294004", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargeunfractionatedheparin", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Unfractionated Heparin", "Derivative Value Code": "96382006", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_warfarin", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Warfarin", "Derivative Value Code": "11289", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_apixaban", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Apixaban", "Derivative Value Code": "1364430", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_dabigatran", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Dabigatran", "Derivative Value Code": "1546356", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_edoxaban", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Edoxaban", "Derivative Value Code": "1599538", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_rivaroxaban", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Rivaroxaban", "Derivative Value Code": "1114195", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargeace", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Angiotensin Converting Enzyme Inhibitor", "Derivative Value Code": "41549009", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_arb_any", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Angiotensin II Receptor Blocker", "Derivative Value Code": "372913009", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargebetablocker", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Beta Blocker", "Derivative Value Code": "33252009", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargestatinany", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Statin", "Derivative Value Code": "96302009", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "atrialrhythmsinusnode", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Sinus node rhythm", "Derivative Value Code": "106067008", "Value Map": {"|Sinus node rhythm|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "cardiomyopathytypenonischcardiomyopathy", "Source": null, "Transformation": {"Reference Field": "CM", "Reference Field Code": 85898001, "Derivative Field": "PriorCMType", "Derivative Field Code": "100000953", "Derivative Value": "Non-ischemic cardiomyopathy", "Derivative Value Code": "111000119104", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "ablatedadjunctivelesion<PERSON>other", "Source": null, "Transformation": {"Reference Field": "AblLesion", "Reference Field Code": 100000926, "Derivative Field": "AblLesionLoc", "Derivative Field Code": "112000001854", "Derivative Value": "Other", "Derivative Value Code": "100001063", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "manipulationofcathetermagnetic", "Source": null, "Transformation": {"Reference Field": "CathManipulation", "Reference Field Code": 103712006, "Derivative Field": "CathManipulation", "Derivative Field Code": "103712006", "Derivative Value": "Catheter Manipulation - Magnetic", "Derivative Value Code": "100000957", "Value Map": {"|Catheter Manipulation - Magnetic|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "atrialrhythmap", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Atrial paced", "Derivative Value Code": "251268003", "Value Map": {"|Atrial paced|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "cardiomyopathytypehypertrophiccardiomyopathy", "Source": null, "Transformation": {"Reference Field": "CM", "Reference Field Code": 85898001, "Derivative Field": "PriorCMType", "Derivative Field Code": "100000953", "Derivative Value": "Hypertrophic cardiomyopathy", "Derivative Value Code": "233873004", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischatrialrhythmap", "Source": null, "Transformation": {"Reference Field": "DCAtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "DCAtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Atrial paced", "Derivative Value Code": "251268003", "Value Map": {"|Atrial paced|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "vasculardiseasetypemyocardialinfarction", "Source": null, "Transformation": {"Reference Field": "ChadVascDis", "Reference Field Code": 100001210, "Derivative Field": "PriorVD", "Derivative Field Code": "27550009", "Derivative Value": "Myocardial infarction", "Derivative Value Code": "22298006", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischatrialrhythmafib", "Source": null, "Transformation": {"Reference Field": "DCAtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "DCAtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Atrial fibrillation", "Derivative Value Code": "49436004", "Value Map": {"|Atrial fibrillation|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "vasculardiseasetypeperipheralarterialocclusivedisease", "Source": null, "Transformation": {"Reference Field": "ChadVascDis", "Reference Field Code": 100001210, "Derivative Field": "PriorVD", "Derivative Field Code": "27550009", "Derivative Value": "Peripheral arterial occlusive disease", "Derivative Value Code": "399957001", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "ablatedadjunctivelesionlocationatypicalatrialflutterlines", "Source": null, "Transformation": {"Reference Field": "AblLesion", "Reference Field Code": 100000926, "Derivative Field": "AblLesionLoc", "Derivative Field Code": "112000001854", "Derivative Value": "Atypical Atrial Flutter Lines", "Derivative Value Code": "100000943", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "ablatedadjunctivelesionlocationcorona<PERSON>sin<PERSON>", "Source": null, "Transformation": {"Reference Field": "AblLesion", "Reference Field Code": 100000926, "Derivative Field": "AblLesionLoc", "Derivative Field Code": "112000001854", "Derivative Value": "Coronary sinus", "Derivative Value Code": "90219004", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "insmilitary", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "Military Health Care", "Derivative Value Code": "31", "Value Map": {"|Military Health Care|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "ablatedadjunctivelesionlocationsvc", "Source": null, "Transformation": {"Reference Field": "AblLesion", "Reference Field Code": 100000926, "Derivative Field": "AblLesionLoc", "Derivative Field Code": "112000001854", "Derivative Value": "SVC", "Derivative Value Code": "48345005", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "insstate", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "State-Specific Plan (non-Medicaid)", "Derivative Value Code": "36", "Value Map": {"|State-Specific Plan (non-Medicaid)|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "insmedicaid", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "Medicaid", "Derivative Value Code": "2", "Value Map": {"|Medicaid|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "cardiomyopathytypeischcardiomyopathy", "Source": null, "Transformation": {"Reference Field": "CM", "Reference Field Code": 85898001, "Derivative Field": "PriorCMType", "Derivative Field Code": "100000953", "Derivative Value": "Ischemic cardiomyopathy", "Derivative Value Code": "426856002", "Value Map": null, "Exact Match": 0, "Case Match": "1", "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "cardiomyopathytyperestrictivecardiomyopathy", "Source": null, "Transformation": {"Reference Field": "CM", "Reference Field Code": 85898001, "Derivative Field": "PriorCMType", "Derivative Field Code": "100000953", "Derivative Value": "Restrictive cardiomyopathy", "Derivative Value Code": "415295002", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "deviceudi", "Source": "cathablationudi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "deviceid", "Source": "devid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "devicename", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibepisodetimingpastyear", "Source": null, "Transformation": {"Reference Field": "AFEQTS1Q2", "Reference Field Code": 100001147, "Derivative Field": "AFEQTS1Q2", "Derivative Field Code": "100001147", "Derivative Value": "1 month to 1 year ago", "Derivative Value Code": "100001151", "Value Map": {"1 month to 1 year ago": "Yes"}, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "afibepisodetimingtoday", "Source": null, "Transformation": {"Reference Field": "AFEQTS1Q2", "Reference Field Code": 100001147, "Derivative Field": "AFEQTS1Q2", "Derivative Field Code": "100001147", "Derivative Value": "Earlier today", "Derivative Value Code": "100001148", "Value Map": {"Earlier today": "Yes"}, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "afibepisodetimingnever", "Source": null, "Transformation": {"Reference Field": "AFEQTS1Q2", "Reference Field Code": 100001147, "Derivative Field": "AFEQTS1Q2", "Derivative Field Code": "100001147", "Derivative Value": "Never aware of having atrial fibrillation", "Derivative Value Code": "100001153", "Value Map": {"Never aware of having atrial fibrillation": "Yes"}, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "afibepisodetimingmorethan1year", "Source": null, "Transformation": {"Reference Field": "AFEQTS1Q2", "Reference Field Code": 100001147, "Derivative Field": "AFEQTS1Q2", "Derivative Field Code": "100001147", "Derivative Value": "More than 1 year ago", "Derivative Value Code": "100001152", "Value Map": {"More than 1 year ago": "Yes"}, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "afibepisode<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "AFEQTS1Q2", "Reference Field Code": 100001147, "Derivative Field": "AFEQTS1Q2", "Derivative Field Code": "100001147", "Derivative Value": "Within the past month", "Derivative Value Code": "100001150", "Value Map": {"Within the past month": "Yes"}, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "afibepisodetimingpastweek", "Source": null, "Transformation": {"Reference Field": "AFEQTS1Q2", "Reference Field Code": 100001147, "Derivative Field": "AFEQTS1Q2", "Derivative Field Code": "100001147", "Derivative Value": "Within the past week", "Derivative Value Code": "100001149", "Value Map": {"Within the past week": "Yes"}, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "currentatrialfibwhen", "Source": "afeqts1q2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "priorcatheterablationstrategy", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_concat", "Args": {"field_map": {"priorablstratcomplexfracatrialelectrogram": "Complex Fractionated Atrial Electrogram (100000910)", "priorablstratconvergentprocedure": "Convergent Procedure (100000911)", "priorablstratctcryoablation": "Cryoablation (233161001)", "priorablstratempiriclalinearlesions": "Empiric LA Linear Lesions (100000912)", "priorablstratganglionplexusablation": "Ganglion Plexus Ablation (100000914)", "priorablstratpulmonaryveinisolation": "Pulmonary Vein Isolation (100000915)", "priorablstratrotorbasedmapping": "Rotor Based Mapping (100000917)", "priorablstratsegmentalpvablation": "Segmental PV Ablation (100000916)", "priorablstratwideareacircumablation": "Wide Area Circumferential Ablation (100000918)", "priorablstratfocalablation": "Focal Ablation (100000913)"}}, "is_computation": null}]}, {"Target": "currentablationstrategy", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_concat", "Args": {"field_map": {"currablstratcomplexfracatrialelectrogram": "Complex Fractionated Atrial Electrogram (100000910)", "currablstratconvergentprocedure": "Convergent Procedure (100000911)", "currablstratctcryoablation": "Cryoablation (233161001)", "currablstratempiriclalinearlesions": "Empiric LA Linear Lesions (100000912)", "currablstratganglionplexusablation": "Ganglion Plexus Ablation (100000914)", "currablstratpulmonaryveinisolation": "Pulmonary Vein Isolation (100000915)", "currablstratrotorbasedmapping": "Rotor Based Mapping (100000917)", "currablstratsegmentalpvablation": "Segmental PV Ablation (100000916)", "currablstratwideareacircumablation": "Wide Area Circumferential Ablation (100000918)", "currablstratfocalablation": "Focal Ablation (100000913)"}}, "is_computation": null}]}, {"Target": "proceduretime", "Source": "procedurestartdatetime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "originalotherid", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": "otherid"}, "is_computation": true}]}, {"Target": "hospname", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "filename", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "version", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "datasetname", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "clientfileid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "biomeimportdt", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "careentityid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tenantid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "yearmonth", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "casesequencenumber", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "distfromhospital", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "age", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceother", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceblack", "Source": "raceblack", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceamindian", "Source": "raceamindian", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "insihs", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "Indian Health Service", "Derivative Value Code": "33", "Value Map": {"|Indian Health Service|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON>us", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "Non-US Insurance", "Derivative Value Code": "100000812", "Value Map": {"|Non-US Insurance|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "vendorid", "Source": "vendorid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rstudy<PERSON><PERSON>", "Source": "studyptid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dischmedprescribed", "Source": "dc_medadmin", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "registryid", "Source": "registryid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vendorver", "Source": "vendorver", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceguamchamo", "Source": "raceguamchamorro", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racemexicanamchicano", "Source": "hispethnicitymexican", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racepuertorican", "Source": "hispethnicitypuertorico", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racecuban", "Source": "hispethnicitycuban", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceotherhispanic", "Source": "hispethnicityotherorigin", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "atrialrhythmatrialtach", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Atrial tachycardia", "Derivative Value Code": "276796006", "Value Map": {"|Atrial tachycardia|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "atrialrhythmundocdatrial", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Undocumented atrial rhythm", "Derivative Value Code": "100001116", "Value Map": {"|Undocumented atrial rhythm|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "atrialrhythmsinusarrest", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Sinus arrest", "Derivative Value Code": "5609005", "Value Map": {"|Sinus arrest|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "dischatrialrhythmatrialtach", "Source": null, "Transformation": {"Reference Field": "DCAtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "DCAtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Atrial tachycardia", "Derivative Value Code": "276796006", "Value Map": {"|Atrial tachycardia|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "dischatrialrhythmaflutter", "Source": null, "Transformation": {"Reference Field": "DCAtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "DCAtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Atrial flutter", "Derivative Value Code": "5370000", "Value Map": {"|Atrial flutter|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "dischatrialrhythmsinusarrest", "Source": null, "Transformation": {"Reference Field": "DCAtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "DCAtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Sinus arrest", "Derivative Value Code": "5609005", "Value Map": {"|Sinus arrest|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "dischatrialrhythmundocdatrial", "Source": null, "Transformation": {"Reference Field": "DCAtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "DCAtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Undocumented atrial rhythm", "Derivative Value Code": "100001116", "Value Map": {"|Undocumented atrial rhythm|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "cardiomyopathytypeothercardiomyopathy", "Source": null, "Transformation": {"Reference Field": "CM", "Reference Field Code": 85898001, "Derivative Field": "PriorCMType", "Derivative Field Code": "100000953", "Derivative Value": "Other Cardiomyopathy Type", "Derivative Value Code": "100001065", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "manipulationofcatheterrobotic", "Source": null, "Transformation": {"Reference Field": "CathManipulation", "Reference Field Code": 103712006, "Derivative Field": "CathManipulation", "Derivative Field Code": "103712006", "Derivative Value": "Catheter Manipulation - Robotic", "Derivative Value Code": "100000959", "Value Map": {"|Catheter Manipulation - Robotic|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "ablatedadjunctivelesionlocationmarshalvein", "Source": null, "Transformation": {"Reference Field": "AblLesion", "Reference Field Code": 100000926, "Derivative Field": "AblLesionLoc", "Derivative Field Code": "112000001854", "Derivative Value": "Ligament/Vein of Marshall", "Derivative Value Code": "5208200", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "vasculardiseasetypeknownaorticplaque", "Source": null, "Transformation": {"Reference Field": "ChadVascDis", "Reference Field Code": 100001210, "Derivative Field": "PriorVD", "Derivative Field Code": "27550009", "Derivative Value": "Known Aortic Plaque", "Derivative Value Code": "1522000|15825003", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "guidancemethodfluoroscopy", "Source": null, "Transformation": {"Reference Field": "GuidanceMethodID", "Reference Field Code": 100000919, "Derivative Field": "GuidanceMethodID", "Derivative Field Code": "100000919", "Derivative Value": "Fluoroscopy", "Derivative Value Code": "44491008", "Value Map": {"|Fluoroscopy|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "guidancemethodcardiacfluoroscopy", "Source": null, "Transformation": {"Reference Field": "GuidanceMethodID", "Reference Field Code": 100000919, "Derivative Field": "GuidanceMethodID", "Derivative Field Code": "100000919", "Derivative Value": "Cardiac Fluoroscopy", "Derivative Value Code": "82327001", "Value Map": {"|Cardiac Fluoroscopy|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "postgastrointestinalhypomotility", "Source": "gihypo", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "phrenicnervedamageconfirmationtype", "Source": "damageconfby", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "modifiedrankinscale", "Source": "postproc_rankinscale", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "modifiedrankinscalena", "Source": "postproc_rankinscalena", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion1", "Source": "afeqts2q1", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion2", "Source": "afeqts2q2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion3", "Source": "afeqts2q3", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion4", "Source": "afeqts2q4", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion5", "Source": "afeqts2q5", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion6", "Source": "afeqts2q6", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion7", "Source": "afeqts2q7", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion8", "Source": "afeqts2q8", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion9", "Source": "afeqts2q9", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion10", "Source": "afeqts2q10", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion11", "Source": "afeqts2q11", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion12", "Source": "afeqts2q12", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion13", "Source": "afeqts2q13", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion14", "Source": "afeqts2q14", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion15", "Source": "afeqts2q15", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion16", "Source": "afeqts2q16", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion17", "Source": "afeqts2q17", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion18", "Source": "afeqts2q18", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion19", "Source": "afeqts2q19", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibquestion20", "Source": "afeqts2q20", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "biomeencounterid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}]}}