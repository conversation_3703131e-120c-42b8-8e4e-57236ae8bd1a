{"TranslatorConfig": {"Version": "3.0", "Source": {"Dataset": {"Code": "TVT", "Id": 105}, "Version": "3.0", "Type": "<PERSON><PERSON><PERSON>"}, "Target": {"Dataset": {"Code": "TVT", "Id": 105}, "Version": "3.0", "Type": "Biome"}, "Translations": [{"Target": "ncdrpatientid", "Source": "ncdrpatientid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "particid", "Source": "partid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "partname", "Source": "partname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "firstname", "Source": "firstname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lastname", "Source": "lastname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "midname", "Source": "midname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dob", "Source": "dob", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ssnna", "Source": "ssnna", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ssn", "Source": "ssn", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": null, "custom string": 1}, "is_computation": false}]}, {"Target": "otherid", "Source": "otherid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gender", "Source": "sex", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "zipcodena", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "patzip", "Source": "zipcode", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racewhite", "Source": "racewhite", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceblack", "Source": "raceblack", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceamindian", "Source": "raceamindian", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceasian", "Source": "raceasian", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceasianindian", "Source": "raceasianindian", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racechinese", "Source": "racechinese", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racefilipino", "Source": "racefilipino", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racejapanese", "Source": "racejapanese", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racekorean", "Source": "racekorean", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racevietnamese", "Source": "racevietnamese", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceasianother", "Source": "raceasianother", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racenathaw", "Source": "racenathaw", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceguamchamo", "Source": "raceguamchamorro", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceotherisland", "Source": "race<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hispethnicitymexican", "Source": "hispethnicitymexican", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hispethnicitypuertorico", "Source": "hispethnicitypuertorico", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hispethnicitycuban", "Source": "hispethnicitycuban", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hispethnicityotherorigin", "Source": "hispethnicityotherorigin", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "episodeid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "arrivaldate", "Source": "arrivaldatetime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "arrivaltime", "Source": "arrivaldatetime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "healthins", "Source": "healthins", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "insprivate", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "|Private Health Insurance|", "Derivative Value Code": "5", "Value Map": {"|Private Health Insurance|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "insmedicare", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "|Medicare Fee-For-Service|", "Derivative Value Code": "1", "Value Map": {"|Medicare Fee-For-Service|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "residence", "Source": "residence", "Transformation": null, "Value Map": {"Home with No Health Aid": "Home with No Health-Aid"}, "Computation": null}, {"Target": "residencend", "Source": "residencend", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mbi", "Source": "mbi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON>udy", "Source": "<PERSON><PERSON>udy", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ptrestriction2", "Source": "ptrestriction", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvtpathway", "Source": "tvtpathway", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "admfname", "Source": "admfname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "admlname", "Source": "admlname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "admmidname", "Source": "admmname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "admnpi", "Source": "admnpi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "height", "Source": "height", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "weight", "Source": "weight", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "numprevcardsurg", "Source": "numprevcardsurg", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "priorhfadmit", "Source": "priorhfadmit1year", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "priorhfadmit1yearnd", "Source": "priorhfadmit1yearnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lifelessthan1yrnd", "Source": "lifelessthan1yrnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hmo2", "Source": "hmo2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "immsupp", "Source": "immsupp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "currentdialysis", "Source": "currentlyondialysis", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tobaccotype", "Source": "tobaccotype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tobaccouse", "Source": "tobaccouse", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "smokeamount", "Source": "smokeamount", "Transformation": null, "Value Map": {"Heavy tobacco use (>= 10/day)": "Heavy tobacco use (>=10 day)"}, "Computation": null}, {"Target": "priormed_ace_i_or_arb_any", "Source": null, "Transformation": {"Reference Field": "PriorMedAdmin_Hom", "Reference Field Code": 33633005, "Derivative Field": "HomeMeds", "Derivative Field Code": "100013057", "Derivative Value": "Angiotensin Converting Enzyme Inhibitor", "Derivative Value Code": "41549009", "Value Map": {"Not Prescribed - No Reason": "Not Prescribed - No Reason", "Yes": "Yes - Prescribed"}, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priormed_aldosterone_antagonists", "Source": null, "Transformation": {"Reference Field": "PriorMedAdmin_Hom", "Reference Field Code": 33633005, "Derivative Field": "HomeMeds", "Derivative Field Code": "100013057", "Derivative Value": "Aldosterone Antagonist", "Derivative Value Code": "372603003", "Value Map": {"Not Prescribed - No Reason": "Not Prescribed - No Reason", "Yes": "Yes - Prescribed"}, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priormed_angiorecepneprilinhib", "Source": null, "Transformation": {"Reference Field": "PriorMedAdmin_Hom", "Reference Field Code": 33633005, "Derivative Field": "HomeMeds", "Derivative Field Code": "100013057", "Derivative Value": "Angiotensin Receptor-Neprilysin Inhibitor", "Derivative Value Code": "112000001832", "Value Map": {"Not Prescribed - No Reason": "Not Prescribed - No Reason", "Yes": "Yes - Prescribed"}, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priormed_anticoagulants_any", "Source": null, "Transformation": {"Reference Field": "PriorMedAdmin_Hom", "Reference Field Code": 33633005, "Derivative Field": "HomeMeds", "Derivative Field Code": "100013057", "Derivative Value": "Anticoagulant", "Derivative Value Code": "112000001416", "Value Map": {"Not Prescribed - No Reason": "Not Prescribed - No Reason", "Yes": "Yes - Prescribed"}, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priormed_aspirin", "Source": null, "Transformation": {"Reference Field": "PriorMedAdmin_Hom", "Reference Field Code": 33633005, "Derivative Field": "HomeMeds", "Derivative Field Code": "100013057", "Derivative Value": "<PERSON><PERSON><PERSON>", "Derivative Value Code": "1191", "Value Map": {"Not Prescribed - No Reason": "Not Prescribed - No Reason", "Yes": "Yes - Prescribed"}, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priormedangiotensiniireceptorblocker", "Source": null, "Transformation": {"Reference Field": "PriorMedAdmin_Hom", "Reference Field Code": 33633005, "Derivative Field": "HomeMeds", "Derivative Field Code": "100013057", "Derivative Value": "Angiotensin II Receptor Blocker", "Derivative Value Code": "372913009", "Value Map": {"Not Prescribed - No Reason": "Not Prescribed - No Reason", "Yes": "Yes - Prescribed"}, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priormed_beta_blocker_any", "Source": null, "Transformation": {"Reference Field": "PriorMedAdmin_Hom", "Reference Field Code": 33633005, "Derivative Field": "HomeMeds", "Derivative Field Code": "100013057", "Derivative Value": "Beta Blocker", "Derivative Value Code": "33252009", "Value Map": {"Not Prescribed - No Reason": "Not Prescribed - No Reason", "Yes": "Yes - Prescribed"}, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priormed_diuretics_other", "Source": null, "Transformation": {"Reference Field": "PriorMedAdmin_Hom", "Reference Field Code": 33633005, "Derivative Field": "HomeMeds", "Derivative Field Code": "100013057", "Derivative Value": "Diuretics Not Otherwise Specified", "Derivative Value Code": "112000001417", "Value Map": {"Not Prescribed - No Reason": "Not Prescribed - No Reason", "Yes": "Yes - Prescribed"}, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priormed_loop_diuretic", "Source": null, "Transformation": {"Reference Field": "PriorMedAdmin_Hom", "Reference Field Code": 33633005, "Derivative Field": "HomeMeds", "Derivative Field Code": "100013057", "Derivative Value": "Loop Diuretics", "Derivative Value Code": "29051009", "Value Map": {"Not Prescribed - No Reason": "Not Prescribed - No Reason", "Yes": "Yes - Prescribed"}, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priormed_thiazides", "Source": null, "Transformation": {"Reference Field": "PriorMedAdmin_Hom", "Reference Field Code": 33633005, "Derivative Field": "HomeMeds", "Derivative Field Code": "100013057", "Derivative Value": "<PERSON><PERSON><PERSON><PERSON>", "Derivative Value Code": "372747003", "Value Map": {"Not Prescribed - No Reason": "Not Prescribed - No Reason", "Yes": "Yes - Prescribed"}, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priormed_p2y12antagonist", "Source": null, "Transformation": {"Reference Field": "PriorMedAdmin_Hom", "Reference Field Code": 33633005, "Derivative Field": "HomeMeds", "Derivative Field Code": "100013057", "Derivative Value": "P2Y12 Antagonist", "Derivative Value Code": "112000001003", "Value Map": {"Not Prescribed - No Reason": "Not Prescribed - No Reason", "Yes": "Yes - Prescribed"}, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priormed_selectivesinusnode", "Source": null, "Transformation": {"Reference Field": "PriorMedAdmin_Hom", "Reference Field Code": 33633005, "Derivative Field": "HomeMeds", "Derivative Field Code": "100013057", "Derivative Value": "Selective Sinus Node I/f Channel Inhibitor", "Derivative Value Code": "112000001831", "Value Map": {"Not Prescribed - No Reason": "Not Prescribed - No Reason", "Yes": "Yes - Prescribed"}, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "afibclass", "Source": "afibclassification", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afib30days", "Source": "afib30days", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "diabetescontrol", "Source": "diabcontrol", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "miwhen", "Source": "miwhen", "Transformation": null, "Value Map": {"Prior Myocardial Infarction Less than 30 days": "< 30 Days", "Prior Myocardial Infarction Greater than or Equal to 30 days": ">= 30 Days"}, "Computation": null}, {"Target": "prioratrialfib", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Atrial Fibrillation", "Derivative Value Code": "49436004", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "aflutter", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Atrial Flutter", "Derivative Value Code": "5370000", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priorcarotidstenosis", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Carotid Artery Stenosis", "Derivative Value Code": "64586002", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priorstroke", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Cerebrovascular Accident", "Derivative Value Code": "230690007", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priorstrokedate", "Source": null, "Transformation": {"Reference Field": "CondHistDate", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Cerebrovascular Accident", "Derivative Value Code": "230690007", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priorcvd", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Cerebrovascular Disease", "Derivative Value Code": "62914000", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "chroniclungdisease", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Chronic Lung Disease", "Derivative Value Code": "413839001", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "conductiondefect", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Conduction Defect", "Derivative Value Code": "44808001", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priordementia", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Dementia - Moderate to Severe", "Derivative Value Code": "112000001493", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "diabetes", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Diabe<PERSON>", "Derivative Value Code": "73211009", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "infendo", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Endocarditis", "Derivative Value Code": "56819008", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priorhf", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Heart Failure", "Derivative Value Code": "84114007", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "hostilechest", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Hostile Chest", "Derivative Value Code": "112000001489", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "hypertension", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Hypertension", "Derivative Value Code": "38341003", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priorliverdisease", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Liver Disease", "Derivative Value Code": "235856003", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Myocardial Infarction", "Derivative Value Code": "22298006", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priorpad", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Peripheral Arterial Disease", "Derivative Value Code": "399957001", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "porcelainaorta", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Porcelain Aort<PERSON>", "Derivative Value Code": "112000001175", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "cvdtia", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Transient Ischemic Attack (TIA)", "Derivative Value Code": "266257000", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "prioraorticvalve", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "Aortic Valve Procedure", "Derivative Value Code": "112000001755", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "prioraorticvalvedate", "Source": null, "Transformation": {"Reference Field": "ProcHistDate", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "Aortic Valve Procedure", "Derivative Value Code": "112000001755", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "prevprocavball", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "Aortic Valve Balloon Valvuloplasty", "Derivative Value Code": "77166000", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "prevprocavrepair", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "Aortic Valve Repair Surgery", "Derivative Value Code": "112816004", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "prevprocavreplace", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "Aortic Valve Replacement Surgery", "Derivative Value Code": "725351001", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "prevproctcvrep", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "Aortic Valve Replacement - Transcatheter", "Derivative Value Code": "41873006", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "prevproctcvint", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "Aortic Valve Transcatheter Intervention", "Derivative Value Code": "112000001768", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priorcabg", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "Coronary Artery Bypass Graft", "Derivative Value Code": "232717009", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priorcabgdate", "Source": null, "Transformation": {"Reference Field": "ProcHistDate", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "Coronary Artery Bypass Graft", "Derivative Value Code": "232717009", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "previcd", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "Implantable Cardioverter Defibrillator", "Derivative Value Code": "447365002", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priormvproc", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "Mitral Valve Procedure", "Derivative Value Code": "112000001940", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priormvprocdate", "Source": null, "Transformation": {"Reference Field": "ProcHistDate", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "Mitral Valve Procedure", "Derivative Value Code": "112000001940", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priormaringsurg", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "Mitral Valve Annuloplasty Ring Surgery", "Derivative Value Code": "232744004", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "prevprocmvrepair", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "Mitral Valve Repair Surgery", "Derivative Value Code": "384641003", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "prevprocmvreplace", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "Mitral Valve Replacement Surgery", "Derivative Value Code": "53059001", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priortmvr", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "Mitral Valve Transcatheter Intervention", "Derivative Value Code": "112000001773", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priorpci", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "PCI", "Derivative Value Code": "415070008", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priorpcidate", "Source": null, "Transformation": {"Reference Field": "ProcHistDate", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "PCI", "Derivative Value Code": "415070008", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "pacemaker", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "Permanent Pacemaker", "Derivative Value Code": "449397007", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priorpacerdate", "Source": null, "Transformation": {"Reference Field": "ProcHistDate", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "Permanent Pacemaker", "Derivative Value Code": "449397007", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priorpulmonicproc", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "Pulmonic Valve Procedure", "Derivative Value Code": "112000001769", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priortricuspidproc", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "Tricuspid Valve Procedure", "Derivative Value Code": "112000001941", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "tricuspidvalveproceduredate", "Source": null, "Transformation": {"Reference Field": "ProcHistDate", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "Tricuspid Valve Procedure", "Derivative Value Code": "112000001941", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dischargedate", "Source": "dcdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dclname", "Source": "dclname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dcfname", "Source": "dcfname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dcmname", "Source": "dcmname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dcnpi", "Source": "dcnpi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dischargestatus", "Source": "dcstatus", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dischargecardrehab", "Source": "dc_cardrehab", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dischargelocation", "Source": "dclocation", "Transformation": null, "Value Map": {"Left Against Medical Advice (AMA)": "Left Against Medical Advice"}, "Computation": null}, {"Target": "dchospice", "Source": "dchospice", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "deathlocation", "Source": "deathprocedure", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "deathcause", "Source": "deathcause", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dc_rbc", "Source": "posttransfusion", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dc_rbcunit", "Source": "dc_rbcunit", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "discharge_direct_thrombin_inhibitor", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Direct thrombin inhibitor", "Derivative Value Code": "414010005", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_warfarin", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Warfarin", "Derivative Value Code": "11289", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_aspirin_any", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "<PERSON><PERSON><PERSON>", "Derivative Value Code": "1191", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_factor_xa_inhibitor", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Direct Factor Xa Inhibitor", "Derivative Value Code": "112000000696", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_p2y12_any", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "P2Y12 Antagonist", "Derivative Value Code": "112000001003", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "savrimplantid", "Source": "savrimplantid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "savrimplantdia", "Source": "savrimplantdia", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "prevprocavtype", "Source": "prevprocavtype", "Transformation": null, "Value Map": {"Stented Valve Replacement": "Bioprosthetic stented", "Stentless Valve Replacement": "Bioprosthetic stentless"}, "Computation": null}, {"Target": "avreplacementtypend", "Source": "avreplacementtypend", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "prevproctcvmodelid", "Source": "tavrimplantid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tavrimplantdia", "Source": "tavrimplantdia", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "crtd", "Source": "crtd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "priormvringsurg", "Source": "priormvringsurg", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "priormvringsurgnd", "Source": "priormvringsurgnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mvringimplantid", "Source": "mvringimplantid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mvringimplantdia", "Source": "mvringimplantdia", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "prevprocmvreplacetype", "Source": "prevmvreplacetype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "prevmvreplacetypend", "Source": "prevmvreplacetypend", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "smvrimplantid", "Source": "smvrimplantid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "smvrimplantdia", "Source": "smvrimplantdia", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "crt", "Source": "crt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pretvaring", "Source": "pretvaring", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "prett<PERSON><PERSON>", "Source": "prett<PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stvrimplantid", "Source": "stvrimplantid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stvrimplantdia", "Source": "stvrimplantdia", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ttvrimplantid", "Source": "ttvrimplantid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ttvrimplantdia", "Source": "ttvrimplantdia", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "medicareadvantage", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "|Medicare Advantage|", "Derivative Value Code": "112000002025", "Value Map": {"|Medicare Advantage|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "insstate", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "|State-Specific Plan (non-Medicaid)|", "Derivative Value Code": "36", "Value Map": {"|State-Specific Plan (non-Medicaid)|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "aflutter30days", "Source": "aflutter30days", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "priormeddose_home", "Source": null, "Transformation": {"Reference Field": "HomeMed_LoopDiureticDose", "Reference Field Code": 112000001975, "Derivative Field": "HomeMeds", "Derivative Field Code": "100013057", "Derivative Value": "Loop Diuretics", "Derivative Value Code": null, "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "cardiomyopathy", "Source": "priorcmtype", "Transformation": null, "Value Map": {"Ischemic cardiomyopathy": "Yes - Ischemic", "Non-ischemic cardiomyopathy": "Yes - Non-ischemic"}, "Computation": null}, {"Target": "priorcardiomyopathy", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Cardiomyopathy", "Derivative Value Code": "85898001", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_ace_inhibitor_any", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Angiotensin Converting Enzyme Inhibitor", "Derivative Value Code": "41549009", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_aldosterone_antagonists", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Aldosterone Antagonist", "Derivative Value Code": "372603003", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_arb_any", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Angiotensin II Receptor Blocker", "Derivative Value Code": "372913009", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_diuretics_other", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Diuretics Not Otherwise Specified", "Derivative Value Code": "112000001417", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_loop_diuretic", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Loop Diuretics", "Derivative Value Code": "29051009", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "meddose_discharge", "Source": null, "Transformation": {"Reference Field": "DischMed_LoopDiureticDose", "Reference Field Code": 112000001975, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Loop Diuretics", "Derivative Value Code": "29051009", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_thiazides", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "<PERSON><PERSON><PERSON><PERSON>", "Derivative Value Code": "372747003", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "chrl<PERSON>d", "Source": "chronlungdisseverity", "Transformation": null, "Value Map": {"Mild Lung Disease": "Mild", "Moderate Lung Disease": "Moderate", "Severe Lung Disease": "Severe"}, "Computation": null}, {"Target": "chronlungdisseveritynd", "Source": "chronlungdisseverity_nd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "insmedicaid", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "|Medicaid|", "Derivative Value Code": "2", "Value Map": {"|Medicaid|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "currend<PERSON>", "Source": "currend<PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cvd<PERSON><PERSON>", "Source": "cvd<PERSON><PERSON>", "Transformation": null, "Value Map": {"Right Carotid Artery Stenosis": "Right", "Left Carotid Artery Stenosis": "Left", "Bilateral Carotid Artery Stenosis": "Both"}, "Computation": null}, {"Target": "cvdcarstelocnd", "Source": "cvdcarstelocnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "priortricuspidvalverepairsurgery", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "Tricuspid Valve Repair Surgery", "Derivative Value Code": "384643000", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priortricuspidvalvereplacesurgery", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "Tricuspid Valve Replacement Surgery", "Derivative Value Code": "25236004", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priortricuspidvalvereplacetranscatheter", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "Tricuspid Valve Replacement - Transcatheter", "Derivative Value Code": "112000001977", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priortricuspidvalvetranscathinterven", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": *********, "Derivative Field": "ProcedHxName", "Derivative Field Code": "*********", "Derivative Value": "Tricuspid Valve Transcatheter Intervention", "Derivative Value Code": "112000001779", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "attlname", "Source": "attlname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "attfname", "Source": "attfname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "attmname", "Source": "attmname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "attnpi", "Source": "attnpi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvtprotype", "Source": "tvtprotype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "proctavr", "Source": null, "Transformation": {"Reference Field": "TVTProType", "Reference Field Code": 112000001167, "Derivative Field": "TVTProType", "Derivative Field Code": "112000001167", "Derivative Value": "|TAVR|", "Derivative Value Code": "41873006", "Value Map": {"|TAVR|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "proctmvrepair", "Source": null, "Transformation": {"Reference Field": "TVTProType", "Reference Field Code": 112000001167, "Derivative Field": "TVTProType", "Derivative Field Code": "112000001167", "Derivative Value": "|TMVr|", "Derivative Value Code": "112000001801", "Value Map": {"|TMVr|": "Yes"}, "Exact Match": 0, "Case Match": "1", "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "proctmvr", "Source": null, "Transformation": {"Reference Field": "TVTProType", "Reference Field Code": 112000001167, "Derivative Field": "TVTProType", "Derivative Field Code": "112000001167", "Derivative Value": "|TMVR|", "Derivative Value Code": "112000001458", "Value Map": {"|TMVR|": "Yes"}, "Exact Match": 0, "Case Match": "1", "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "procttvp", "Source": null, "Transformation": {"Reference Field": "TVTProType", "Reference Field Code": 112000001167, "Derivative Field": "TVTProType", "Derivative Field Code": "112000001167", "Derivative Value": "|Tricuspid Valve Procedure|", "Derivative Value Code": "112000001977", "Value Map": {"|Tricuspid Valve Procedure|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "tvtprocedureentrytime", "Source": "tvtprocedureentrytime", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_or", "Args": {"true_val": null, "false_val": "tvtprocedureentrytime", "fields": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": ["YES"]}]}, "is_computation": null}]}, {"Target": "procedurestartdate", "Source": "procedurestartdatetime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvtprocedurestopdate", "Source": "procedureenddatetime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvtprocedurestoptime", "Source": "tvtprocedurestoptime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvtprocedureexittime", "Source": "tvtprocedurestoptime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cadpresentation", "Source": "cadpresentation", "Transformation": null, "Value Map": {"No Symptoms, No Angina": "No Sxs, no angina", "Symptoms Unlikely to be Ischemic": "Sx unlikely to be ischemic"}, "Computation": null}, {"Target": "prior2weekshf", "Source": "prior2wkshf", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "prior2weeknyha", "Source": "prior2weeknyha", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "prior<PERSON><PERSON><PERSON><PERSON>", "Source": "prior<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "priorcardiacar<PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "sxas", "Source": "sxas", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "sxasnd", "Source": "sxasnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fivemwalktest", "Source": "fivemwalktest", "Transformation": null, "Value Map": {"Test Not Performed": "Not Performed", "Test Performed": "Yes"}, "Computation": null}, {"Target": "stsriskscore", "Source": null, "Transformation": {"Reference Field": "STSRiskScoreValue", "Reference Field Code": 112000001797, "Derivative Field": "STSRiskScoreType", "Derivative Field Code": "112000001412", "Derivative Value": "Society of Thoracic Surgeons Risk Score for Aortic Valve Replacement", "Derivative Value Code": "112000001796", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "kccq12_performed", "Source": "kccq12_performed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "kccq12_1a", "Source": "kccq12_1a", "Transformation": null, "Value Map": {"1 - Extremely Limited": "Extremely Limited", "2 - Quite a Bit Limited": "Quite a Bit Limited", "3 - Moderately Limited": "Moderately Limited", "4 - Slightly Limited": "Slightly Limited", "5 - Not at All Limited": "Not at All Limited", "6 - Limited for Other Reasons or Did Not Do These Activities": "Limited for Other Reasons or Did Not Do These Activities"}, "Computation": null}, {"Target": "kccq12_1b", "Source": "kccq12_1b", "Transformation": null, "Value Map": {"1 - Extremely Limited": "Extremely Limited", "2 - Quite a Bit Limited": "Quite a Bit Limited", "3 - Moderately Limited": "Moderately Limited", "4 - Slightly Limited": "Slightly Limited", "5 - Not at All Limited": "Not at All Limited", "6 - Limited for Other Reasons or Did Not Do These Activities": "Limited for Other Reasons or Did Not Do These Activities"}, "Computation": null}, {"Target": "kccq12_1c", "Source": "kccq12_1c", "Transformation": null, "Value Map": {"1 - Extremely Limited": "Extremely Limited", "2 - Quite a Bit Limited": "Quite a Bit Limited", "3 - Moderately Limited": "Moderately Limited", "4 - Slightly Limited": "Slightly Limited", "5 - Not at All Limited": "Not at All Limited", "6 - Limited for Other Reasons or Did Not Do These Activities": "Limited for other reasons or did not do the activity"}, "Computation": null}, {"Target": "kccq12_2", "Source": "kccq12_2", "Transformation": null, "Value Map": {"1 - Every Morning": "Every Morning", "2 - Three or More Times Per Week But Not Everyday": "3 or More Times Per Week But Not Everyday", "3 - One to Two Times Per Week": "1-2 Times Per Week", "4 - Less Than Once a Week": "Less Than Once a Week", "5 - Never Over the Past Two Weeks": "Never Over the Past 2 Weeks"}, "Computation": null}, {"Target": "kccq12_3", "Source": "kccq12_3", "Transformation": null, "Value Map": {"1 - All the Time": "All the Time", "2 - Several Times Per Day": "Several Times Per Day", "3 - At Least Once Per Day": "At Least Once Per Day", "4 - Three or More Times Per Week But Not Everyday": "3 or More Times Per Week But Not Everyday", "5 - One to Two Times Per Week": "1-2 Times Per Week", "6 - Less Than Once a Week": "Less Than Once a Week", "7 - Never Over the Past Two Weeks": "Never Over the Past 2 Weeks"}, "Computation": null}, {"Target": "kccq12_4", "Source": "kccq12_4", "Transformation": null, "Value Map": {"1 - All the Time": "All the Time", "2 - Several Times Per Day": "Several Times Per Day", "3 - At Least Once Per Day": "At Least Once Per Day", "4 - Three or More Times Per Week But Not Everyday": "3 or More Times Per Week But Not Everyday", "5 - One to Two Times Per Week": "1-2 Times Per Week", "6 - Less Than Once a Week": "Less Than Once a Week", "7 - Never Over the Past Two Weeks": "Never Over the Past 2 Weeks"}, "Computation": null}, {"Target": "kccq12_5", "Source": "kccq12_5", "Transformation": null, "Value Map": {"1 - Every Night": "Every Night", "2 - Three or More Times Per Week But Not Everyday": "3 or More Times Per Week But Not Everyday", "3 - One to Two Times Per Week": "1-2 Times Per Week", "4 - Less Than Once a Week": "Less Than Once a Week", "5 - Never Over the Past Two Weeks": "Never Over the Past 2 Weeks"}, "Computation": null}, {"Target": "kccq12_6", "Source": "kccq12_6", "Transformation": null, "Value Map": {"1 - It Has Extremely Limited My Enjoyment of Life": "It Has Extremely Limited My Enjoyment of Life", "2 - It Has Limited My Enjoyment of Life Quite a Bit": "It Has Limited My Enjoyment of Life Quite a Bit", "3 - It Has Moderately Limited My Enjoyment of Life": "It Has Moderately Limited My Enjoyment of Life", "4 - It Has Slightly Limited My Enjoyment of Life": "It Has Slightly Limited My Enjoyment of Life", "5 - It Has Not Limited My Enjoyment of Life at All": "It Has Not Limited My Enjoyment of Life at All"}, "Computation": null}, {"Target": "kccq12_7", "Source": "kccq12_7", "Transformation": null, "Value Map": {"1 - Not At All Satisfied": "Not At All Satisfied", "2 - Mostly Dissatisfied": "Mostly Dissatisfied", "3 - Somewhat Satisfied": "Somewhat Satisfied", "4 - Mostly Satisfied": "Mostly Satisfied", "5 - Completely Satisfied": "Completely Satisfied"}, "Computation": null}, {"Target": "kccq12_8a", "Source": "kccq12_8a", "Transformation": null, "Value Map": {"1 - Severely Limited": "Severely Limited", "2 - Limited Quite a Bit": "Limited Quite a Bit", "3 - Moderately Limited": "Moderately Limited", "4 - Slightly Limited": "Slightly Limited", "5 - Did Not Limit at All": "Did Not Limit at All", "6 - Does Not Apply or Did Not Do for Other Reasons": "Does Not Apply or Did Not Do for Other Reasons"}, "Computation": null}, {"Target": "kccq12_8b", "Source": "kccq12_8b", "Transformation": null, "Value Map": {"1 - Severely Limited": "Severely Limited", "2 - Limited Quite a Bit": "Limited Quite a Bit", "3 - Moderately Limited": "Moderately Limited", "4 - Slightly Limited": "Slightly Limited", "5 - Did Not Limit at All": "Did Not Limit at All", "6 - Does Not Apply or Did Not Do for Other Reasons": "Does Not Apply or Did Not Do for Other Reasons"}, "Computation": null}, {"Target": "kccq12_8c", "Source": "kccq12_8c", "Transformation": null, "Value Map": {"1 - Severely Limited": "Severely Limited", "2 - Limited Quite a Bit": "Limited Quite a Bit", "3 - Moderately Limited": "Moderately Limited", "4 - Slightly Limited": "Slightly Limited", "5 - Did Not Limit at All": "Did Not Limit at All", "6 - Does Not Apply or Did Not Do for Other Reasons": "Does Not Apply or Did Not Do for Other Reasons"}, "Computation": null}, {"Target": "kccq12_overall", "Source": "kccq12_overall", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preprochgbnd", "Source": "hgbnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "sodium", "Source": "sodium", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "sodiumnd", "Source": "sodiumnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preproccreat", "Source": "preproccreat", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preproccreatnd", "Source": "preproccreatnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "totblrbn", "Source": "bilirubin", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "totblrbnnd", "Source": "bilirubinnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "totalbumin", "Source": "albumin", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "totalbuminnd", "Source": "albumin_nd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "platelets", "Source": "plateletct", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "plateletnd", "Source": "plateletctnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "inr", "Source": "inrtvt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "inrnd", "Source": "inrnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "bnp", "Source": "preproc_bnpvalue", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "bnpnd", "Source": "preprocbnpnotdrawn", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ntprobnp", "Source": "preprocedurentbnp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preprocntbnpnotdrawn", "Source": "preprocntbnpnotdrawn", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fev1", "Source": "fev1", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fev1na", "Source": "fev1nd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dl<PERSON><PERSON><PERSON>", "Source": "dl<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dlcona", "Source": "dlcond", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "nvpqrs", "Source": "nvpqrs", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vpqrs", "Source": "vpqrs", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pre_procedure_anticoagulants_any", "Source": "preprocanticoag", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pre_procedure_inotrope_positive", "Source": "preopinotropes", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dx<PERSON><PERSON><PERSON>", "Source": "dx<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dxcathdt", "Source": "dxcathdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "numdisv", "Source": "numdisv", "Transformation": null, "Value Map": {"Two": "2", "One": "1", "Three": "3"}, "Computation": [{"Transform_Type": "replace", "Args": {"old_values": ".0", "new_values": ""}, "is_computation": null}]}, {"Target": "numdisvnd", "Source": "numdisvnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "l<PERSON><PERSON><PERSON>", "Source": "l<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lmaindisnd", "Source": "lmaindisnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "proxlad", "Source": "proxlad", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "proxladnd", "Source": "proxladnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "syntax", "Source": "syntax", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "syntaxnd", "Source": "syntaxnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cardiacoutput", "Source": "cardiacoutput", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cona", "Source": "cardiacoutput_nd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pcwp", "Source": "pcwp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pcwpnm", "Source": "pcwp_nd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "papmean", "Source": "papmean", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "papmeannm", "Source": "papmean_nd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "papsys", "Source": "papsys", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "papsysnm", "Source": "papsys_nd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pvr", "Source": "pvr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pvrnd", "Source": "pvrnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rapmean", "Source": "rap", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rapnm", "Source": "meanrap_nd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rvsys", "Source": "rvsp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rvsysnd", "Source": "rvsysnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "avdannulussizemethod", "Source": "avdannulussizemethod", "Transformation": null, "Value Map": {"Computed Tomography Angiography": "CTA", "Transthoracic Echo (TTE)": "TTE", "Transesophageal Echocardiogram (TEE)": "TEE"}, "Computation": null}, {"Target": "avannulusdia", "Source": "avannulusdia", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "avannulusmaxdia", "Source": "avannulusmaxdia", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "a<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "a<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "avcalc", "Source": "avcalc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "avcalcnd", "Source": "avcalcnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lvef", "Source": "lvefmeasure", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lvefna", "Source": "lvefna", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vdaoet", "Source": "vdaoet", "Transformation": null, "Value Map": {"Rheumatic": "Rheumatic fever"}, "Computation": null}, {"Target": "avdmorphology", "Source": "avmorphology", "Transformation": null, "Value Map": {"Bicuspid": "Bicuspid Aortic Valve", "Tricuspid": "Tricuspid Valve"}, "Computation": null}, {"Target": "avdmorphology", "Source": "avmorphology", "Transformation": null, "Value Map": {"Bicuspid Aortic Valve": "Bicuspid", "Tricuspid Valve": "Tricuspid"}, "Computation": null}, {"Target": "aasize", "Source": "aasize", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aasizend", "Source": "aasizend", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "avdannularcalc", "Source": "avannularcalc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vdinsufa", "Source": "vdinsufa", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vdstena", "Source": "vdstena", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vdaova", "Source": "vdaova", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vdgrada", "Source": "vdgrada", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "svi", "Source": "svi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "svi_nd", "Source": "svi_nd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "avdstenosispeakgradient", "Source": "avpeakgrad", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "avdpeakvelocity", "Source": "avdpeakvelocity", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vdmit", "Source": "mvd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vdinsufm", "Source": "preprocmr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vdinsufmpara", "Source": "vdinsufmpara", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vdinsufmparand", "Source": "vdinsufmpara_nd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vdinsuffmcentral", "Source": "vdinsuffmcentral", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vdinsuffmcentralnd", "Source": "vdinsuffmcentral_nd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vdmiteoa", "Source": "vdmiteoa", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vdmiteoa_moa", "Source": "vdmiteoa_moa", "Transformation": null, "Value Map": {"Proximal Isovelocity Surface Area": "PISA"}, "Computation": null}, {"Target": "vdstenm", "Source": "vdstenm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vdmva", "Source": "vdmva", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vdgradm", "Source": "vdgradm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mvdetiodmr", "Source": null, "Transformation": {"Reference Field": "MVDEtio", "Reference Field Code": 11851006, "Derivative Field": "MVDEtio", "Derivative Field Code": "11851006", "Derivative Value": "Degenerative MR (Primary)", "Derivative Value Code": "112000001277", "Value Map": {"Degenerative MR (Primary)": "Yes"}, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "mvdetioendoc", "Source": null, "Transformation": {"Reference Field": "MVDEtio", "Reference Field Code": 11851006, "Derivative Field": "MVDEtio", "Derivative Field Code": "11851006", "Derivative Value": "Endocarditis", "Derivative Value Code": "56819008", "Value Map": {"Endocarditis": "Yes"}, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "mvdetioinflam", "Source": null, "Transformation": {"Reference Field": "MVDEtio", "Reference Field Code": 11851006, "Derivative Field": "MVDEtio", "Derivative Field Code": "11851006", "Derivative Value": "Post Inflammatory", "Derivative Value Code": "112000001441", "Value Map": {"Post Inflammatory": "Yes"}, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "mvdetiofmr", "Source": null, "Transformation": {"Reference Field": "MVDEtio", "Reference Field Code": 11851006, "Derivative Field": "MVDEtio", "Derivative Field Code": "11851006", "Derivative Value": "Functional MR (Secondary)", "Derivative Value Code": "112000001276", "Value Map": {"Functional MR (Secondary)": "Yes"}, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "mv<PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "MVDEtio", "Reference Field Code": 11851006, "Derivative Field": "MVDEtio", "Derivative Field Code": "11851006", "Derivative Value": "Other", "Derivative Value Code": "100000351", "Value Map": {"Other": "Yes"}, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "mvdetionone", "Source": null, "Transformation": {"Reference Field": "MVDEtio", "Reference Field Code": 11851006, "Derivative Field": "MVDEtio", "Derivative Field Code": "11851006", "Derivative Value": "None", "Derivative Value Code": "100001231", "Value Map": {"None": "Yes"}, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "fmrtype", "Source": "fmrtype", "Transformation": null, "Value Map": {"Ischemic-Chronic": "Ischemic Chronic", "Pure Annular Dilation (with Normal Left Ventricular Systolic Function)": "Pure Annular Dilation with Normal Left Ventricular Systolic Function"}, "Computation": null}, {"Target": "fmrtypend", "Source": "fmrtype_nd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mvdleafpro", "Source": "mvdleafpro", "Transformation": null, "Value Map": {"Anterior Leaflet": "Anterior", "Posterior Leaflet": "Posterior", "Bileaflet": "Bi-leaflet"}, "Computation": null}, {"Target": "mvdleafprond", "Source": "mvdleafpro_nd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mv<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "mv<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": {"Anterior Leaflet": "Anterior", "Posterior Leaflet": "Posterior", "Bileaflet": "Bi-leaflet"}, "Computation": null}, {"Target": "mvdleafflailnd", "Source": "mvdleafflail_nd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "inflamtype", "Source": "inflamtype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "inflamtypend", "Source": "inflamtype_nd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mvdleafteth", "Source": "mvdleafteth", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mvdleaftethnd", "Source": "mvdleafteth_nd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "manncalc", "Source": "mvdan<PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mvcalcnd", "Source": "mvcalcnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mleafcalc", "Source": "mleafcalc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mleafcalcnd", "Source": "mleafcalc_nd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvdisetio", "Source": "tvdisetio", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vdinsuft", "Source": "preproctr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvdgrad", "Source": "tvdgrad", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvdgradnd", "Source": "tvdgradnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvannulusnd", "Source": "tvannulusnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "midrvdia", "Source": "midrvdia", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "midrvdiand", "Source": "midrvdiand", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "basaldia", "Source": "basaldia", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "basaldiand", "Source": "basaldiand", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "do<PERSON><PERSON>", "Source": "do<PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "flowres", "Source": "flowres", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "astype", "Source": "astype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "astypend", "Source": "astypend", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherproc", "Source": "concomproc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "concomproctype", "Source": "concomproctype", "Transformation": null, "Value Map": {"PCI": "Percutaneous Coronary Intervention (PCI)"}, "Computation": null}, {"Target": "status", "Source": "procstatus", "Transformation": null, "Value Map": {"Elective Procedure": "Elective", "Urgent Procedure": "<PERSON><PERSON>", "Emergency Procedure": "Emergency", "Salvage Procedure": "Salvage"}, "Computation": null}, {"Target": "eval<PERSON><PERSON><PERSON>", "Source": "eval<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvtlocation", "Source": "procedurelocation", "Transformation": null, "Value Map": {"Cardiac Catheterization Laboratory": "Cath Lab", "Hybrid Catheterization Laboratory Suite": "Hybrid Cath Lab Suite", "Hybrid Operating Room Suite": "Hybrid OR Suite"}, "Computation": null}, {"Target": "anesthesiatype", "Source": "anesthesiatype", "Transformation": null, "Value Map": {"General Anesthesia": "General anesthesia", "Moderate Sedation/Analgesia (Conscious Sedation)": "Moderate sedation"}, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "tvtprocedureabort", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_or", "Args": {"true_val": null, "false_val": "<PERSON><PERSON><PERSON>", "fields": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": ["YES"]}, {"name": "procttvp", "values": ["YES"]}, {"name": "proctmvr", "values": ["YES"]}]}, "is_computation": null}]}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_or", "Args": {"true_val": null, "false_val": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fields": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": ["YES"]}, {"name": "procttvp", "values": ["YES"]}, {"name": "proctmvr", "values": ["YES"]}]}, "is_computation": null}]}, {"Target": "procedureabortaction", "Source": "procedureabortaction", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_or", "Args": {"true_val": null, "false_val": "procedureabortaction", "fields": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": ["YES"]}, {"name": "procttvp", "values": ["YES"]}, {"name": "proctmvr", "values": ["YES"]}]}, "is_computation": null}]}, {"Target": "operatorreason", "Source": "operatorreason", "Transformation": null, "Value Map": {"Extreme Risk": "Inoperable/Extreme Risk"}, "Computation": [{"Transform_Type": "conditional_or", "Args": {"true_val": null, "false_val": "operatorreason", "fields": [{"name": "procttvp", "values": ["YES"]}, {"name": "proctmvr", "values": ["YES"]}]}, "is_computation": null}]}, {"Target": "convsurgaccess", "Source": "convsurgaccess", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_or", "Args": {"true_val": null, "false_val": "convsurgaccess", "fields": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": ["YES"]}, {"name": "procttvp", "values": ["YES"]}, {"name": "proctmvr", "values": ["YES"]}]}, "is_computation": null}]}, {"Target": "conv<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "conv<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_or", "Args": {"true_val": null, "false_val": "conv<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fields": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": ["YES"]}, {"name": "procttvp", "values": ["YES"]}, {"name": "proctmvr", "values": ["YES"]}]}, "is_computation": null}]}, {"Target": "mvsupport", "Source": "mechventsupp", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_or", "Args": {"true_val": null, "false_val": "mvsupport", "fields": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": ["YES"]}, {"name": "procttvp", "values": ["YES"]}, {"name": "proctmvr", "values": ["YES"]}]}, "is_computation": null}]}, {"Target": "mvsupportdevice", "Source": "mvsupportdevice", "Transformation": null, "Value Map": {"Intra-aortic balloon pump (IABP)": "IABP"}, "Computation": [{"Transform_Type": "conditional_or", "Args": {"true_val": null, "false_val": "mvsupportdevice", "fields": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": ["YES"]}, {"name": "procttvp", "values": ["YES"]}, {"name": "proctmvr", "values": ["YES"]}]}, "is_computation": null}]}, {"Target": "mvsupporttiming", "Source": "mvsupporttiming", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_or", "Args": {"true_val": null, "false_val": "mvsupporttiming", "fields": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": ["YES"]}, {"name": "procttvp", "values": ["YES"]}, {"name": "proctmvr", "values": ["YES"]}]}, "is_computation": null}]}, {"Target": "cpb", "Source": "cpb", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cpbstatus", "Source": "cpbstatus", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "perfustm", "Source": "perfustm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "deliveryremoved", "Source": "deliveryremoved", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "intra_procedure_inotrope_positive", "Source": "procinotropesadmin", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvtopa_firstname", "Source": null, "Transformation": {"Reference Field": "TVT_Oper_FirstName", "Reference Field Code": 112000001955, "Derivative Field": "IncrementalId", "Derivative Field Code": "112000001955", "Derivative Value": 1, "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "tvtopa_lastname", "Source": null, "Transformation": {"Reference Field": "TVT_Oper_LastName", "Reference Field Code": 112000001955, "Derivative Field": "IncrementalId", "Derivative Field Code": "112000001955", "Derivative Value": 1, "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "tvtopa_midname", "Source": null, "Transformation": {"Reference Field": "TVT_Oper_MidName", "Reference Field Code": 112000001955, "Derivative Field": "IncrementalId", "Derivative Field Code": "112000001955", "Derivative Value": 1, "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "tvtopa_npi", "Source": null, "Transformation": {"Reference Field": "TVT_Oper_NPI", "Reference Field Code": 112000001955, "Derivative Field": "IncrementalId", "Derivative Field Code": "112000001955", "Derivative Value": 1, "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "tvtopb_firstname", "Source": null, "Transformation": {"Reference Field": "TVT_Oper_FirstName", "Reference Field Code": 112000001955, "Derivative Field": "IncrementalId", "Derivative Field Code": "112000001955", "Derivative Value": 2, "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "tvtopb_lastname", "Source": null, "Transformation": {"Reference Field": "TVT_Oper_LastName", "Reference Field Code": 112000001955, "Derivative Field": "IncrementalId", "Derivative Field Code": "112000001955", "Derivative Value": 2, "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "tvtopb_midname", "Source": null, "Transformation": {"Reference Field": "TVT_Oper_MidName", "Reference Field Code": 112000001955, "Derivative Field": "IncrementalId", "Derivative Field Code": "112000001955", "Derivative Value": 2, "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "tvtopb_npi", "Source": null, "Transformation": {"Reference Field": "TVT_Oper_NPI", "Reference Field Code": 112000001955, "Derivative Field": "IncrementalId", "Derivative Field Code": "112000001955", "Derivative Value": 2, "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "fluorodosedap", "Source": "fluorodosedap2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fluorodosedapunit", "Source": "fluorodosedap2_unit", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fluorodosekerm", "Source": "fluorodosekerm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "flurotime", "Source": "fluorotime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "contrastvol_allprocs", "Source": "contrastvol_x", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvtprocedureindication", "Source": "primtavrprocind", "Transformation": null, "Value Map": {"Aortic Regurgitation": "Primary Aortic Regurgitation", "Aortic Stenosis": "Primary Aortic Stenosis"}, "Computation": null}, {"Target": "valveinvalve", "Source": "valveinvalve", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "bvfattempt", "Source": "bvfattempt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "bvftiming", "Source": "bvftiming", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "valvefractured", "Source": "valvefractured", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvtaccesssite", "Source": "tvtaccesssite", "Transformation": null, "Value Map": {"Axillary Artery": "Axillary", "Carotid": "Transcarotid", "Transaortic": "Direct Aortic", "Femoral Artery": "Femoral", "Transiliac": "Iliac", "Subclavian Artery": "Subclavian", "Transseptal via Femoral Vein": "Transeptal"}, "Computation": null}, {"Target": "tvtaccessmethod", "Source": "tvtaccessmethod", "Transformation": null, "Value Map": {"Percutaneous Approach": "Percutaneous", "Mini sternotomy": "Mini Sternotomy", "Mini thoracotomy": "Mini Thoracotomy"}, "Computation": null}, {"Target": "valvesheathdelivery", "Source": "valvesheathdelivery", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "embprot", "Source": "embprot", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "embprotdevice", "Source": "embprotdevice", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "avrpostar", "Source": "avr_post_ar", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "post_meanavgrad", "Source": "postimplant_avmeangrad", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postprochgb", "Source": "postprochgb1", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postprochgbnd", "Source": "pprochgbnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "popekg<PERSON><PERSON>", "Source": "popekg", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "popekg", "Source": "popekg", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dc_creat", "Source": "dccreatinine", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dc_creatnd", "Source": "d<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postp<PERSON>cc<PERSON>t", "Source": "poproc_creat", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postproccreatnd", "Source": "highcrea_nd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": {"Transesophageal Echocardiogram (TEE)": "Yes - TEE", "Transthoracic Echo (TTE)": "Yes - TTE"}, "Computation": null}, {"Target": "echond", "Source": "echond", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "popttechdate", "Source": "popttechdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "post_aorticvalvearea", "Source": "pp_avarea", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "post_aorticvalvemeangradient", "Source": "pp_avmeangradient", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "pp_ar", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "popttmr", "Source": "pp_mr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pptr", "Source": "pp_tr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "post_mveoa", "Source": "pp_mv_eoa", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "post_eoamethod", "Source": "pp_mv_eoa_moa", "Transformation": null, "Value Map": {"Proximal Isovelocity Surface Area": "PISA"}, "Computation": null}, {"Target": "post_mvmeangrad", "Source": "pp_mvmeangradient", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "post_mvarea", "Source": "pp_mvarea", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "post_lvot", "Source": "pp_lvot", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "post_sam", "Source": "pp_sam", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pptvdgrad", "Source": "pp_tvdgrad", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pptvdgradnd", "Source": "pp_tvdgradnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pptvannulus", "Source": "pp_tvannulus", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pptvannulusnd", "Source": "pp_tvannulusnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ppmidrvdia", "Source": "pp_midrvdia", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ppmidrvdiand", "Source": "pp_midrvdiand", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ppbasalrvdia", "Source": "pp_basalrvdia", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ppbasalrvdiand", "Source": "pp_basalrvdiand", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pprvsp", "Source": "pp_rvsp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pprvsysnd", "Source": "pp_rvsysnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fivemwalk1", "Source": null, "Transformation": {"Reference Field": "FiveMWTTime", "Reference Field Code": 112000001184, "Derivative Field": "FiveMWTCounter", "Derivative Field Code": "112000002003", "Derivative Value": "1", "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "fivemwalk2", "Source": null, "Transformation": {"Reference Field": "FiveMWTTime", "Reference Field Code": 112000001184, "Derivative Field": "FiveMWTCounter", "Derivative Field Code": "112000002003", "Derivative Value": "2", "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "fivemwalk3", "Source": null, "Transformation": {"Reference Field": "FiveMWTTime", "Reference Field Code": 112000001184, "Derivative Field": "FiveMWTCounter", "Derivative Field Code": "112000002003", "Derivative Value": "3", "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "tvtopc_firstname", "Source": null, "Transformation": {"Reference Field": "TVT_Oper_FirstName", "Reference Field Code": 112000001955, "Derivative Field": "IncrementalId", "Derivative Field Code": "112000001955", "Derivative Value": 3, "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "tvtopc_lastname", "Source": null, "Transformation": {"Reference Field": "TVT_Oper_LastName", "Reference Field Code": 112000001955, "Derivative Field": "IncrementalId", "Derivative Field Code": "112000001955", "Derivative Value": 3, "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "tvtopc_midname", "Source": null, "Transformation": {"Reference Field": "TVT_Oper_MidName", "Reference Field Code": 112000001955, "Derivative Field": "IncrementalId", "Derivative Field Code": "112000001955", "Derivative Value": 3, "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "tvtopc_npi", "Source": null, "Transformation": {"Reference Field": "TVT_Oper_NPI", "Reference Field Code": 112000001955, "Derivative Field": "IncrementalId", "Derivative Field Code": "112000001955", "Derivative Value": 3, "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "stsriskmvrepair", "Source": null, "Transformation": {"Reference Field": "STSRiskScoreValue", "Reference Field Code": 112000001797, "Derivative Field": "STSRiskScoreType", "Derivative Field Code": "112000001412", "Derivative Value": "Society of Thoracic Surgeons Risk Score for Mitral Valve Repair", "Derivative Value Code": "112000001795", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "stsriskmvreplace", "Source": null, "Transformation": {"Reference Field": "STSRiskScoreValue", "Reference Field Code": 112000001797, "Derivative Field": "STSRiskScoreType", "Derivative Field Code": "112000001412", "Derivative Value": "Society of Thoracic Surgeons Risk Score for Mitral Valve Replacement", "Derivative Value Code": "112000001793", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "sixminwalkdate", "Source": "sixminwalkdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "sixminwalkdist", "Source": "sixminwalkdist", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": {"Non-Cardiac Reason": "Non Cardiac Reason"}, "Computation": null}, {"Target": "sixmin<PERSON>perf", "Source": "sixmin<PERSON>perf", "Transformation": {"Reference Field": "SixMinWalkReason", "Reference Field Code": 252478000, "Derivative Field": "SixMinWalkPerf", "Derivative Field Code": "252478000", "Derivative Value": "No", "Derivative Value Code": null, "Value Map": {"Non-Cardiac Reason": "Not performed - Non Cardiac Reason", "Cardiac Reason": "Not performed - <PERSON><PERSON>", "Patient Not Willing to Walk": "Not performed - <PERSON><PERSON> Not Will<PERSON> to Walk", "Not Performed by Site": "Not performed by site"}, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "Performed", "false_val": "sixmin<PERSON>perf", "fields": [{"name": "sixminwalkperf_x", "values": ["Yes"]}]}, "is_computation": null}]}, {"Target": "l<PERSON>s", "Source": "l<PERSON>s", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lvidsnm", "Source": "lvids_nm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lvidd", "Source": "lvidd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lviddnm", "Source": "lvidd_nm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lvesv", "Source": "lvesv", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lvesvnm", "Source": "lvesv_nm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lvedv", "Source": "lvedv", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lvedvnm", "Source": "lvedv_nm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lavol", "Source": "lavol", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lavolnm", "Source": "lavol_nm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lavolindex", "Source": "lavolindex", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lavolindexnm", "Source": "lavolindex_nm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mvr_post_mr", "Source": "intraproc_post_mr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mvr_post_meanmvgrad", "Source": "mvr_post_meanmvgrad", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mrrindoptimalmedicaltherapy", "Source": null, "Transformation": {"Reference Field": "MRRIndication", "Reference Field Code": 112000000000, "Derivative Field": "MRRIndication", "Derivative Field Code": "112000000482", "Derivative Value": "|Refractory to Guideline Determined Optimal Medical Therapy|", "Derivative Value Code": "112000001944", "Value Map": {"|Refractory to Guideline Determined Optimal Medical Therapy|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "No", "false_val": "mrrindoptimalmedicaltherapy", "fields": [{"name": "mrrindoptimalmedicaltherapy", "values": ["None", "nan"]}, {"name": "leafaccess", "values": ["Other Vein", "<PERSON><PERSON><PERSON>", "Left Femoral Vein", "Right <PERSON><PERSON><PERSON>ein"]}]}, "is_computation": null}]}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "MRRIndication", "Reference Field Code": 112000000000, "Derivative Field": "MRRIndication", "Derivative Field Code": "112000000482", "Derivative Value": "|<PERSON><PERSON><PERSON>|", "Derivative Value Code": "248279007", "Value Map": {"|Frailty|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "No", "false_val": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fields": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": ["None", "nan"]}, {"name": "leafaccess", "values": ["Other Vein", "<PERSON><PERSON><PERSON>", "Left Femoral Vein", "Right <PERSON><PERSON><PERSON>ein"]}]}, "is_computation": null}]}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "MRRIndication", "Reference Field Code": 112000000000, "Derivative Field": "MRRIndication", "Derivative Field Code": "112000000482", "Derivative Value": "|Hostile Chest|", "Derivative Value Code": "112000001489", "Value Map": {"|Hostile Chest|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "No", "false_val": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fields": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": ["None", "nan"]}, {"name": "leafaccess", "values": ["Other Vein", "<PERSON><PERSON><PERSON>", "Left Femoral Vein", "Right <PERSON><PERSON><PERSON>ein"]}]}, "is_computation": null}]}, {"Target": "mrrindseverepulmonaryhypertension", "Source": null, "Transformation": {"Reference Field": "MRRIndication", "Reference Field Code": 112000000000, "Derivative Field": "MRRIndication", "Derivative Field Code": "112000000482", "Derivative Value": "|Severe Pulmonary Hypertension|", "Derivative Value Code": "112000001490", "Value Map": {"|Severe Pulmonary Hypertension|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "No", "false_val": "mrrindseverepulmonaryhypertension", "fields": [{"name": "mrrindseverepulmonaryhypertension", "values": ["None", "nan"]}, {"name": "leafaccess", "values": ["Other Vein", "<PERSON><PERSON><PERSON>", "Left Femoral Vein", "Right <PERSON><PERSON><PERSON>ein"]}]}, "is_computation": null}]}, {"Target": "m<PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "MRRIndication", "Reference Field Code": 112000000000, "Derivative Field": "MRRIndication", "Derivative Field Code": "112000000482", "Derivative Value": "|Porcelain Aorta|", "Derivative Value Code": "112000001175", "Value Map": {"|Porcelain Aorta|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "No", "false_val": "m<PERSON><PERSON><PERSON>", "fields": [{"name": "m<PERSON><PERSON><PERSON>", "values": ["None", "nan"]}, {"name": "leafaccess", "values": ["Other Vein", "<PERSON><PERSON><PERSON>", "Left Femoral Vein", "Right <PERSON><PERSON><PERSON>ein"]}]}, "is_computation": null}]}, {"Target": "mrrindopm6", "Source": null, "Transformation": {"Reference Field": "MRRIndication", "Reference Field Code": 112000000000, "Derivative Field": "MRRIndication", "Derivative Field Code": "112000000482", "Derivative Value": "|Predicted STS MV Repair ROM Greater than or Equal to 6 Percent|", "Derivative Value Code": "112000001483", "Value Map": {"|Predicted STS MV Repair ROM Greater than or Equal to 6 Percent|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "No", "false_val": "mrrindopm6", "fields": [{"name": "mrrindopm6", "values": ["None", "nan"]}, {"name": "leafaccess", "values": ["Other Vein", "<PERSON><PERSON><PERSON>", "Left Femoral Vein", "Right <PERSON><PERSON><PERSON>ein"]}]}, "is_computation": null}]}, {"Target": "mrrindopm8", "Source": null, "Transformation": {"Reference Field": "MRRIndication", "Reference Field Code": 112000000000, "Derivative Field": "MRRIndication", "Derivative Field Code": "112000000482", "Derivative Value": "|Predicted STS MV Replacement ROM Greater than or Equal to 8 Percent|", "Derivative Value Code": "112000001484", "Value Map": {"|Predicted STS MV Replacement ROM Greater than or Equal to 8 Percent|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "No", "false_val": "mrrindopm8", "fields": [{"name": "mrrindopm8", "values": ["None", "nan"]}, {"name": "leafaccess", "values": ["Other Vein", "<PERSON><PERSON><PERSON>", "Left Femoral Vein", "Right <PERSON><PERSON><PERSON>ein"]}]}, "is_computation": null}]}, {"Target": "mrrindrvdysfx", "Source": null, "Transformation": {"Reference Field": "MRRIndication", "Reference Field Code": 112000000000, "Derivative Field": "MRRIndication", "Derivative Field Code": "112000000482", "Derivative Value": "|RVD with Severe TR|", "Derivative Value Code": "112000001486", "Value Map": {"|RVD with Severe TR|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "No", "false_val": "mrrindrvdysfx", "fields": [{"name": "mrrindrvdysfx", "values": ["None", "nan"]}, {"name": "leafaccess", "values": ["Other Vein", "<PERSON><PERSON><PERSON>", "Left Femoral Vein", "Right <PERSON><PERSON><PERSON>ein"]}]}, "is_computation": null}]}, {"Target": "mrrind<PERSON>ed", "Source": null, "Transformation": {"Reference Field": "MRRIndication", "Reference Field Code": 112000000000, "Derivative Field": "MRRIndication", "Derivative Field Code": "112000000482", "Derivative Value": "|Major Bleeding Diathesis|", "Derivative Value Code": "112000001487", "Value Map": {"|Major Bleeding Diathesis|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "No", "false_val": "mrrind<PERSON>ed", "fields": [{"name": "mrrind<PERSON>ed", "values": ["None", "nan"]}, {"name": "leafaccess", "values": ["Other Vein", "<PERSON><PERSON><PERSON>", "Left Femoral Vein", "Right <PERSON><PERSON><PERSON>ein"]}]}, "is_computation": null}]}, {"Target": "mrrindchemo", "Source": null, "Transformation": {"Reference Field": "MRRIndication", "Reference Field Code": 112000000000, "Derivative Field": "MRRIndication", "Derivative Field Code": "112000000482", "Derivative Value": "|Chemotherapy for Malignancy|", "Derivative Value Code": "112000001491", "Value Map": {"|Chemotherapy for Malignancy|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "No", "false_val": "mrrindchemo", "fields": [{"name": "mrrindchemo", "values": ["None", "nan"]}, {"name": "leafaccess", "values": ["Other Vein", "<PERSON><PERSON><PERSON>", "Left Femoral Vein", "Right <PERSON><PERSON><PERSON>ein"]}]}, "is_computation": null}]}, {"Target": "m<PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "MRRIndication", "Reference Field Code": 112000000000, "Derivative Field": "MRRIndication", "Derivative Field Code": "112000000482", "Derivative Value": "|AIDS|", "Derivative Value Code": "62479008", "Value Map": {"|AIDS|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "No", "false_val": "m<PERSON><PERSON><PERSON>", "fields": [{"name": "m<PERSON><PERSON><PERSON>", "values": ["None", "nan"]}, {"name": "leafaccess", "values": ["Other Vein", "<PERSON><PERSON><PERSON>", "Left Femoral Vein", "Right <PERSON><PERSON><PERSON>ein"]}]}, "is_computation": null}]}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "MRRIndication", "Reference Field Code": 112000000000, "Derivative Field": "MRRIndication", "Derivative Field Code": "112000000482", "Derivative Value": "|High Risk of Aspiration|", "Derivative Value Code": "112000001488", "Value Map": {"|High Risk of Aspiration|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "No", "false_val": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fields": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": ["None", "nan"]}, {"name": "leafaccess", "values": ["Other Vein", "<PERSON><PERSON><PERSON>", "Left Femoral Vein", "Right <PERSON><PERSON><PERSON>ein"]}]}, "is_computation": null}]}, {"Target": "m<PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "MRRIndication", "Reference Field Code": 112000000000, "Derivative Field": "MRRIndication", "Derivative Field Code": "112000000482", "Derivative Value": "|Severe De<PERSON>ia|", "Derivative Value Code": "112000001914", "Value Map": {"|Severe Dementia|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "No", "false_val": "m<PERSON><PERSON><PERSON><PERSON>", "fields": [{"name": "m<PERSON><PERSON><PERSON><PERSON>", "values": ["None", "nan"]}, {"name": "leafaccess", "values": ["Other Vein", "<PERSON><PERSON><PERSON>", "Left Femoral Vein", "Right <PERSON><PERSON><PERSON>ein"]}]}, "is_computation": null}]}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "MRRIndication", "Reference Field Code": 112000000000, "Derivative Field": "MRRIndication", "Derivative Field Code": "112000000482", "Derivative Value": "|IMA at High Risk of Injury|", "Derivative Value Code": "112000001494", "Value Map": {"|IMA at High Risk of Injury|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "No", "false_val": "<PERSON><PERSON><PERSON><PERSON>", "fields": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "values": ["None", "nan"]}, {"name": "leafaccess", "values": ["Other Vein", "<PERSON><PERSON><PERSON>", "Left Femoral Vein", "Right <PERSON><PERSON><PERSON>ein"]}]}, "is_computation": null}]}, {"Target": "m<PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "MRRIndication", "Reference Field Code": 112000000000, "Derivative Field": "MRRIndication", "Derivative Field Code": "112000000482", "Derivative Value": "|Other|", "Derivative Value Code": "100000351", "Value Map": {"|Other|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "No", "false_val": "m<PERSON><PERSON><PERSON>", "fields": [{"name": "m<PERSON><PERSON><PERSON>", "values": ["None", "nan"]}, {"name": "leafaccess", "values": ["Other Vein", "<PERSON><PERSON><PERSON>", "Left Femoral Vein", "Right <PERSON><PERSON><PERSON>ein"]}]}, "is_computation": null}]}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "MRRIndication", "Reference Field Code": 112000000000, "Derivative Field": "MRRIndication", "Derivative Field Code": "112000000482", "Derivative Value": "|Immobility|", "Derivative Value Code": "112000001492", "Value Map": {"|Immobility|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "No", "false_val": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fields": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": ["None", "nan"]}, {"name": "leafaccess", "values": ["Other Vein", "<PERSON><PERSON><PERSON>", "Left Femoral Vein", "Right <PERSON><PERSON><PERSON>ein"]}]}, "is_computation": null}]}, {"Target": "m<PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "MRRIndication", "Reference Field Code": 112000000000, "Derivative Field": "MRRIndication", "Derivative Field Code": "112000000482", "Derivative Value": "|Severe Liver Disease (Cirrhosis or MELD score >12)|", "Derivative Value Code": "112000001482", "Value Map": {"|Severe Liver Disease (Cirrhosis or MELD score >12)|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "No", "false_val": "m<PERSON><PERSON><PERSON><PERSON>", "fields": [{"name": "m<PERSON><PERSON><PERSON><PERSON>", "values": ["None", "nan"]}, {"name": "leafaccess", "values": ["Other Vein", "<PERSON><PERSON><PERSON>", "Left Femoral Vein", "Right <PERSON><PERSON><PERSON>ein"]}]}, "is_computation": null}]}, {"Target": "leafaccess", "Source": "leafaccess", "Transformation": null, "Value Map": {"Right Femoral Vein": "Right femoral vein", "Left Femoral Vein": "Left femoral vein", "Jugular Vein": "Jugular vein", "Other Vein": "Other vein"}, "Computation": null}, {"Target": "steerableguideused", "Source": "sgcdeviceid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mrr_<PERSON><PERSON>o", "Source": "mrr_<PERSON><PERSON>o", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvproctype", "Source": "tvproctype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvlocation", "Source": "tvlocation", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvprocedureind", "Source": "tvprocedureind", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvaccess", "Source": "tvaccess", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rvlead", "Source": "rvlead", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rvleadstrat", "Source": "rvleadstrat", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rvleadfx", "Source": "rvleadfx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "svdpre", "Source": "svdpre", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "svdprend", "Source": "svdprend", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivcpre", "Source": "ivcpre", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivcprend", "Source": "ivcprend", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rappre", "Source": "rappre", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rapprend", "Source": "rapprend", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rvsppre", "Source": "rvsppre", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rvspprend", "Source": "rvspprend", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvdgradpre", "Source": "tvdgradpre", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvdgradprend", "Source": "tvdgradprend", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "svdpost", "Source": "svdpost", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "svdpostnd", "Source": "svdpostnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivcpost", "Source": "ivcpost", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivcpostnd", "Source": "ivcpostnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rappost", "Source": "rappost", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rappostnd", "Source": "rappostnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rvsppost", "Source": "rvsppost", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rvsppostnd", "Source": "rvsppostnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvdgradpost", "Source": "tvdgradpost", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvdgradpostnd", "Source": "tvdgradpostnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvtdevicecounter", "Source": "tavrdevcounter", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvtdeviceid", "Source": "tav<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tavrdevicedia", "Source": "tavrdevicedia", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvtdevicerepositioning", "Source": "tvtdevicerepositioning", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvtdevicerepositioningna", "Source": "tvtdevicerepositioningna", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "deviceimplantsuccessful", "Source": "tavrdeviceimplantsuccessful", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tavr_unsuccessful", "Source": "tavr_unsuccessful", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvtdeviceserno", "Source": "tavrdevicesn", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "valve_udidirectid", "Source": "tav_udi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mrr_counter", "Source": "mrepairdevcounter", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mvr_deviceid", "Source": "mrepairdeviceid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mrr_leafletclipnum", "Source": "mrepairnum", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mrr_udidirectid", "Source": "mrepair_udi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mrr_loc", "Source": "mrr_loc", "Transformation": null, "Value Map": {"A1/P1": "A1P1", "A2/P2": "A2P2", "A3/P3": "A3P3"}, "Computation": null}, {"Target": "mrr_leafletclipdeploy", "Source": "mrr_leafletclipdeploy", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mrr_leafletclipreasonnotdeploy", "Source": "mrr_leafletclipreasonnotdeploy", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mrr_clipremoved", "Source": "mrr_clipremoved", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvdevcounter", "Source": "tvdevcounter", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ttvdeviceid", "Source": "ttvdeviceid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ttvdevicedia", "Source": "ttvdevicedia", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvdevicesn", "Source": "tvdevicesn", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ttvudi", "Source": "ttv_udi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvdeviceimplantsuccessful", "Source": "tvdeviceimplantsuccessful", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tvunsuccessful", "Source": "tv_unsuccessful", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ce_eventoccurred", "Source": "postprococcurred", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ce_eventid", "Source": "procevents", "Transformation": null, "Value Map": {"Annular Rupture": "E007", "Aortic Dissection": "E008", "ASD Defect Closure due to Transseptal Catheterization": "E054", "Atrial Fibrillation": "E006", "Bleeding - Access Site": "E017", "Bleeding - Gastrointestinal": "E020", "Bleeding - Genitourinary": "E021", "Bleeding - Hematoma at Access Site": "E018", "Bleeding - Other": "E022", "Bleeding - Retroperitoneal": "E019", "Cardiac Arrest": "E005", "Cardiac Perforation": "E009", "Cardiac Surgery or Intervention - Other Unplanned": "E031", "Complete Leaflet Clip Detachment": "E051", "Coronary Artery Compression": "E002", "Delivery System Component Embolization": "E058", "Device Embolization": "E050", "Device Migration": "E023", "Device Related Event - Other": "E028", "Device Thrombosis": "E027", "Dialysis (New Requirement)": "E029", "Endocarditis": "E003", "ICD": "E040", "Left Ventricular Outflow Tract Obstruction": 253546004, "Mitral Leaflet or Subvalvular Injury": 112000001886, "Myocardial Infarction": "E059", "Pacemaker Lead Dislodgement or Dysfunction": 112000001884, "Percutaneous Coronary Intervention": "E033", "Permanent Pacemaker": "E039", "Pulmonary Embolism": 59282003, "Reintervention - Aortic Valve": "E030", "Reintervention - Mitral Valve": "E053", "Reintervention - Tricuspid Valve": 112000001820, "Single Leaflet Device Attachment": "E049", "Stroke - Hemorrhagic": "E012", "Stroke - Ischemic": "E011", "Stroke - Undetermined": "E013", "Transient Ischemic Attack (TIA)": "E010", "Transseptal Complication": "E052", "Vascular Complication - Major": "E041", "Vascular Complication - Minor": "E042", "Vascular Surgery or Intervention - Unplanned": "E032", "Readmission - Cardiac (Not Heart Failure)": "E056", "Readmission - Heart Failure": "E055", "Readmission - Non-Cardiac": "E057", "COVID-19 Positive": 112000001982, "Deep Vein Thrombosis": 128053003}, "Computation": null}, {"Target": "ce_eventname", "Source": "procevents", "Transformation": null, "Value Map": {"COVID-19 Positive": "COVID-19"}, "Computation": null}, {"Target": "ce_eventdate", "Source": "intrapostproceventdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_adjudevent", "Source": "aj_adjudevent", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_eventdate", "Source": "aj_eventdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_status", "Source": "aj_status", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_deathdate", "Source": "aj_deathdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ajcommentsinhosp", "Source": "aj_commentsin<PERSON>p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_sxonset", "Source": "aj_sxonset", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_neurodef", "Source": "aj_neurodef", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_neuroclinpresent", "Source": "aj_neuroclinpresent", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_neurosxduration", "Source": "aj_neurosymptduration", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ajbrainimag", "Source": "aj_brainimag", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ajbrainimagetype", "Source": "aj_brainimagetype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "bifind", "Source": "bi_find", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "adjers", "Source": "adj_ers", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "a<PERSON><PERSON><PERSON>", "Source": "aj_dlae", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ajpriorliving", "Source": "aj_priorliving", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ajautdxstroke", "Source": "aj_autdxstroke", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "datavrsn", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "priormed_aspirin_alone", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "Yes - Prescribed", "false_val": null, "fields": [{"name": "priormed_aspirin", "values": ["YES"]}, {"name": "priormed_p2y12antagonist", "values": ["No"]}]}, "is_computation": null}]}, {"Target": "smoker", "Source": null, "Transformation": {"Reference Field": "TobaccoUse", "Reference Field Code": 110483000, "Derivative Field": "TobaccoUse", "Derivative Field Code": "110483000", "Derivative Value": "CURRENT - SOME DAYS|CURRENT - EVERY DAY", "Derivative Value Code": null, "Value Map": {"Current - Every Day": "Yes", "Current - Some Days": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 1.0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priormed_aspirin_dual_antiplatelet", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "Yes - Prescribed", "false_val": null, "fields": [{"name": "priormed_aspirin", "values": ["YES"]}, {"name": "priormed_p2y12antagonist", "values": ["YES"]}]}, "is_computation": null}]}, {"Target": "afibflutter", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_or", "Args": {"fields": [{"name": "prioratrialfib", "values": ["YES"]}, {"name": "aflutter", "values": ["YES"]}]}, "is_computation": null}]}, {"Target": "discharge_ace_or_arb_any", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_or", "Args": {"fields": [{"name": "discharge_ace_inhibitor_any", "values": ["YES"]}, {"name": "discharge_arb_any", "values": ["YES"]}]}, "is_computation": null}]}, {"Target": "discharge_aspirin_alone", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "Yes", "false_val": null, "fields": [{"name": "discharge_aspirin_any", "values": ["YES"]}, {"name": "discharge_p2y12_any", "values": ["NO"]}]}, "is_computation": null}]}, {"Target": "discharge_aspirin_dual_antiplatelet_therapy", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "Yes", "false_val": null, "fields": [{"name": "discharge_aspirin_any", "values": ["YES"]}, {"name": "discharge_p2y12_any", "values": ["YES"]}]}, "is_computation": null}]}, {"Target": "post_valvmr", "Source": "pp_mr", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": "popttmr"}, "is_computation": null}]}, {"Target": "contrastvol", "Source": "contrastvol", "Transformation": {"Reference Field": "ContrastVol", "Reference Field Code": "80242-1", "Derivative Field": "TVTProType", "Derivative Field Code": "112000001167", "Derivative Value": "|TAVR|", "Derivative Value Code": null, "Value Map": null, "Exact Match": 0, "Case Match": "0", "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "mvr_contrastvol", "Source": null, "Transformation": {"Reference Field": "ContrastVol", "Reference Field Code": "80242-1", "Derivative Field": "TVTProType", "Derivative Field Code": "112000001167", "Derivative Value": "|TMVR|", "Derivative Value Code": null, "Value Map": null, "Exact Match": 0, "Case Match": "0", "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "mrr_procrmarrivaltime", "Source": null, "Transformation": {"Reference Field": "TVTPRocedureEntryTime", "Reference Field Code": 112000001197, "Derivative Field": "ProcLeafClip", "Derivative Field Code": "112000000208", "Derivative Value": "Yes", "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "vdinsufmv", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": "vdinsufm"}, "is_computation": null}]}, {"Target": "hic", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": "mbi"}, "is_computation": null}]}, {"Target": "supporttype", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": "mvsupportdevice"}, "is_computation": null}]}, {"Target": "preprocmechassist", "Source": null, "Transformation": {"Reference Field": "MVSupportDevice", "Reference Field Code": 100001278, "Derivative Field": "MVSupportTiming", "Derivative Field Code": "100014009", "Derivative Value": "In place at start of procedure", "Derivative Value Code": "100001280", "Value Map": {"Cardiopulmonary Support (CPS)": "Yes - Cardiopulmonary Support (CPS)", "Extracorporeal membrane oxygenation (ECMO)": "Yes - Extracorporeal membrane oxygenation (ECMO)", "Impella: Left Ventricular Support": "Yes - Impella: Left Ventricular Support", "Impella: Right Ventricular Support": "Yes - Impella: Right Ventricular Support", "Intra-aortic balloon pump (IABP)": "Yes - IABP", "Isolated Right Ventricular Support": "Yes - Isolated Right Ventricular Support", "Left ventricular assist device (LVAD)": "Yes - Left ventricular assist device (LVAD)", "Right Ventricular Assist Device (RVAD)": "Yes - Right Ventricular Assist Device (RVAD)", "Percutaneous Heart Pump (PHP)": "Yes - Percutaneous Heart Pump (PHP)", "TandemHeart": "Yes - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Other": "Yes - Other"}, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "mrr_convsurgaccess", "Source": null, "Transformation": {"Reference Field": "ConvSurgAccess", "Reference Field Code": 112000001327, "Derivative Field": "ProcLeafClip", "Derivative Field Code": "112000000208", "Derivative Value": "Yes", "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "mrr_convsurga<PERSON><PERSON><PERSON>son", "Source": null, "Transformation": {"Reference Field": "ConvSurgAccessReason", "Reference Field Code": 112000001327, "Derivative Field": "ProcLeafClip", "Derivative Field Code": "112000000208", "Derivative Value": "Yes", "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "mrr_mvsupport", "Source": null, "Transformation": {"Reference Field": "MechVentSupp", "Reference Field Code": 100014009, "Derivative Field": "ProcLeafClip", "Derivative Field Code": "112000000208", "Derivative Value": "Yes", "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "mrr_supporttype", "Source": null, "Transformation": {"Reference Field": "MVSupportDevice", "Reference Field Code": 100001278, "Derivative Field": "ProcLeafClip", "Derivative Field Code": "112000000208", "Derivative Value": "Yes", "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "mrr_supporttiming", "Source": null, "Transformation": {"Reference Field": "MVSupportTiming", "Reference Field Code": 100014009, "Derivative Field": "ProcLeafClip", "Derivative Field Code": "112000000208", "Derivative Value": "Yes", "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "mrr_procrmarrivaldate", "Source": null, "Transformation": {"Reference Field": "TVTPRocedureEntryTime", "Reference Field Code": 112000001197, "Derivative Field": "ProcLeafClip", "Derivative Field Code": "112000000208", "Derivative Value": "Yes", "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "mrr_procedureabort", "Source": null, "Transformation": {"Reference Field": "TVTProcedureAbort", "Reference Field Code": 112000000515, "Derivative Field": "ProcLeafClip", "Derivative Field Code": "112000000208", "Derivative Value": "Yes", "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "mrr_<PERSON><PERSON><PERSON><PERSON>son", "Source": null, "Transformation": {"Reference Field": "ProcedureAbortReason", "Reference Field Code": 112000001292, "Derivative Field": "ProcLeafClip", "Derivative Field Code": "112000000208", "Derivative Value": "Yes", "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "mrr_procedureabortaction", "Source": null, "Transformation": {"Reference Field": "ProcedureAbortAction", "Reference Field Code": 112000001468, "Derivative Field": "ProcLeafClip", "Derivative Field Code": "112000000208", "Derivative Value": "Yes", "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "mvr_convsurgmitral", "Source": null, "Transformation": {"Reference Field": "ConvSurgAccess", "Reference Field Code": 112000001327, "Derivative Field": "TVTProType", "Derivative Field Code": "112000001167", "Derivative Value": "|TMVR|", "Derivative Value Code": null, "Value Map": null, "Exact Match": 0, "Case Match": "1", "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "mvr_convsurgaccess", "Source": null, "Transformation": {"Reference Field": "ConvSurgAccessReason", "Reference Field Code": 112000001327, "Derivative Field": "TVTProType", "Derivative Field Code": "112000001167", "Derivative Value": "|TMVR|", "Derivative Value Code": null, "Value Map": null, "Exact Match": 0, "Case Match": "1", "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "mvr_mvsupport", "Source": null, "Transformation": {"Reference Field": "MechVentSupp", "Reference Field Code": 100014009, "Derivative Field": "TVTProType", "Derivative Field Code": "112000001167", "Derivative Value": "|TMVR|", "Derivative Value Code": null, "Value Map": null, "Exact Match": 0, "Case Match": "1", "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "mvr_mvsupporttype", "Source": null, "Transformation": {"Reference Field": "MVSupportDevice", "Reference Field Code": 100001278, "Derivative Field": "TVTProType", "Derivative Field Code": "112000001167", "Derivative Value": "|TMVR|", "Derivative Value Code": null, "Value Map": null, "Exact Match": 0, "Case Match": "1", "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "mvr_mvsupporttiming", "Source": null, "Transformation": {"Reference Field": "MVSupportTiming", "Reference Field Code": 100014009, "Derivative Field": "TVTProType", "Derivative Field Code": "112000001167", "Derivative Value": "|TMVR|", "Derivative Value Code": null, "Value Map": null, "Exact Match": 0, "Case Match": "1", "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "mvr_operatorreason", "Source": null, "Transformation": {"Reference Field": "OperatorReason", "Reference Field Code": 112000001281, "Derivative Field": "TVTProType", "Derivative Field Code": "112000001167", "Derivative Value": "|TMVR|", "Derivative Value Code": null, "Value Map": null, "Exact Match": 0, "Case Match": "1", "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "mvr_procedureabort", "Source": null, "Transformation": {"Reference Field": "TVTProcedureAbort", "Reference Field Code": 112000000515, "Derivative Field": "TVTProType", "Derivative Field Code": "112000001167", "Derivative Value": "|TMVR|", "Derivative Value Code": null, "Value Map": null, "Exact Match": 0, "Case Match": "1", "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "mvr_procedureabortaction", "Source": null, "Transformation": {"Reference Field": "ProcedureAbortAction", "Reference Field Code": 112000001468, "Derivative Field": "TVTProType", "Derivative Field Code": "112000001167", "Derivative Value": "|TMVR|", "Derivative Value Code": null, "Value Map": null, "Exact Match": 0, "Case Match": "1", "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "mvr_<PERSON><PERSON><PERSON><PERSON>son", "Source": null, "Transformation": {"Reference Field": "ProcedureAbortReason", "Reference Field Code": 112000001292, "Derivative Field": "TVTProType", "Derivative Field Code": "112000001167", "Derivative Value": "|TMVR|", "Derivative Value Code": null, "Value Map": null, "Exact Match": 0, "Case Match": "1", "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "ttvp_convsurgmitral", "Source": null, "Transformation": {"Reference Field": "ConvSurgAccess", "Reference Field Code": 112000001327, "Derivative Field": "TVTProType", "Derivative Field Code": "112000001167", "Derivative Value": "Tricuspid Valve Procedure", "Derivative Value Code": null, "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "ttvp_convsurgaccessreason", "Source": null, "Transformation": {"Reference Field": "ConvSurgAccessReason", "Reference Field Code": 112000001327, "Derivative Field": "TVTProType", "Derivative Field Code": "112000001167", "Derivative Value": "Tricuspid Valve Procedure", "Derivative Value Code": null, "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "ttvp_operatorreason", "Source": null, "Transformation": {"Reference Field": "OperatorReason", "Reference Field Code": 112000001281, "Derivative Field": "TVTProType", "Derivative Field Code": "112000001167", "Derivative Value": "Tricuspid Valve Procedure", "Derivative Value Code": null, "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "ttvp_procedureabort", "Source": null, "Transformation": {"Reference Field": "TVTProcedureAbort", "Reference Field Code": 112000000515, "Derivative Field": "TVTProType", "Derivative Field Code": "112000001167", "Derivative Value": "Tricuspid Valve Procedure", "Derivative Value Code": null, "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "ttvp_procedureabortreason", "Source": null, "Transformation": {"Reference Field": "ProcedureAbortReason", "Reference Field Code": 112000001292, "Derivative Field": "TVTProType", "Derivative Field Code": "112000001167", "Derivative Value": "Tricuspid Valve Procedure", "Derivative Value Code": null, "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "ttvp_procedureabortaction", "Source": null, "Transformation": {"Reference Field": "ProcedureAbortAction", "Reference Field Code": 112000001468, "Derivative Field": "TVTProType", "Derivative Field Code": "112000001167", "Derivative Value": "Tricuspid Valve Procedure", "Derivative Value Code": null, "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "ttvp_mvsupport", "Source": null, "Transformation": {"Reference Field": "MechVentSupp", "Reference Field Code": 100014009, "Derivative Field": "TVTProType", "Derivative Field Code": "112000001167", "Derivative Value": "Tricuspid Valve Procedure", "Derivative Value Code": null, "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "ttvp_supporttiming", "Source": null, "Transformation": {"Reference Field": "MVSupportTiming", "Reference Field Code": 100014009, "Derivative Field": "TVTProType", "Derivative Field Code": "112000001167", "Derivative Value": "Tricuspid Valve Procedure", "Derivative Value Code": null, "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "originalotherid", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": "otherid"}, "is_computation": null}]}, {"Target": "hospname", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "filename", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "version", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "datasetname", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "clientfileid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "biomeimportdt", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "careentityid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tenantid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "distfromhospital", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "biomeencounterid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "age", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "infendty", "Source": "infendty", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vendorid", "Source": "vendorid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "insmilitary", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "|Military Health Care|", "Derivative Value Code": "31", "Value Map": {"|Military Health Care|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "insihs", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "|Indian Health Service|", "Derivative Value Code": "33", "Value Map": {"|Indian Health Service|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON>us", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "|Non-US Insurance|", "Derivative Value Code": "100000812", "Value Map": {"|Non-US Insurance|": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "preprochgb", "Source": "hgb", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dischargebetablockerany", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Beta Blocker", "Derivative Value Code": "33252009", "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "timeframe", "Source": "timeframe", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vendorver", "Source": "vendorver", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "registryid", "Source": "registryid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "studyname", "Source": "studyname", "Transformation": {"Reference Field": "studyname", "Reference Field Code": 100001096, "Derivative Field": "IncrementalId", "Derivative Field Code": "100001096", "Derivative Value": 1, "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "aj_aisev", "Source": "aj_aisev", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_censev", "Source": "aj_censev", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_mvind", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_primaryind", "Source": "aj_primaryind", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_pvsev", "Source": "aj_pvsev", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_reinttype", "Source": "aj_reinttype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mvaccesssite", "Source": "mvaccesssite", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mvr_deviceimplantsuccessful", "Source": "mv_unsuccessful", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mvr_mvhemdet", "Source": "mvr_mvhemdet", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mvr_mvpostballoon", "Source": "mvr_mvpostballoon", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mvr_mvpreballoon", "Source": "mvr_mvpreballoon", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "post_aorticvalveinsuffcent", "Source": "pp_centralar", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "post_aorticvalveinsuffperi", "Source": "pp_paraar", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "post_paramr", "Source": "pp_paramr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "prevprocavmodelid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "priortmvrtype", "Source": "priortmvrtype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "studyname2", "Source": null, "Transformation": {"Reference Field": "studyname", "Reference Field Code": 100001096, "Derivative Field": "IncrementalId", "Derivative Field Code": "100001096", "Derivative Value": 2, "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "studyname3", "Source": null, "Transformation": {"Reference Field": "studyname", "Reference Field Code": 100001096, "Derivative Field": "IncrementalId", "Derivative Field Code": "100001096", "Derivative Value": 3, "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "studyname4", "Source": null, "Transformation": {"Reference Field": "studyname", "Reference Field Code": 100001096, "Derivative Field": "IncrementalId", "Derivative Field Code": "100001096", "Derivative Value": 4, "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "studyname5", "Source": null, "Transformation": {"Reference Field": "studyname", "Reference Field Code": 100001096, "Derivative Field": "IncrementalId", "Derivative Field Code": "100001096", "Derivative Value": 5, "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "studyptid", "Source": "studyptid", "Transformation": {"Reference Field": "studyptid", "Reference Field Code": "2.16.840.1.113883.3.3478.4.852", "Derivative Field": "IncrementalId", "Derivative Field Code": "2.16.840.1.113883.3.3478.4.852", "Derivative Value": 1, "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "studyptid2", "Source": null, "Transformation": {"Reference Field": "studyptid", "Reference Field Code": "2.16.840.1.113883.3.3478.4.852", "Derivative Field": "IncrementalId", "Derivative Field Code": "2.16.840.1.113883.3.3478.4.852", "Derivative Value": 2, "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "studyptid3", "Source": null, "Transformation": {"Reference Field": "studyptid", "Reference Field Code": "2.16.840.1.113883.3.3478.4.852", "Derivative Field": "IncrementalId", "Derivative Field Code": "2.16.840.1.113883.3.3478.4.852", "Derivative Value": 3, "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "studyptid4", "Source": null, "Transformation": {"Reference Field": "studyptid", "Reference Field Code": "2.16.840.1.113883.3.3478.4.852", "Derivative Field": "IncrementalId", "Derivative Field Code": "2.16.840.1.113883.3.3478.4.852", "Derivative Value": 4, "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "studyptid5", "Source": null, "Transformation": {"Reference Field": "studyptid", "Reference Field Code": "2.16.840.1.113883.3.3478.4.852", "Derivative Field": "IncrementalId", "Derivative Field Code": "2.16.840.1.113883.3.3478.4.852", "Derivative Value": 5, "Derivative Value Code": null, "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "mvr_devicecounter", "Source": "mvdevcounter", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mvr_<PERSON>erno", "Source": "tmvreplacementdevicesn", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mvr_valve_udidirectid", "Source": "tmv_udi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "submissiontype", "Source": "submissiontype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "arrivaldatetime", "Source": "arrivaldatetime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ttvp_supporttype", "Source": null, "Transformation": {"Reference Field": "MVSupportDevice", "Reference Field Code": 100001278, "Derivative Field": "TVTProType", "Derivative Field Code": "112000001167", "Derivative Value": "Tricuspid Valve Procedure", "Derivative Value Code": null, "Value Map": null, "Exact Match": 0, "Case Match": null, "Regex Match": null, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "tmvrdeviceid", "Source": "tmvrdeviceid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "sdmproc", "Source": "sdm_proc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "sdmtool", "Source": "sdm_tool", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "sdmtoolname", "Source": "sdm_tool_name", "Transformation": null, "Value Map": null, "Computation": null}]}}