{"TranslatorConfig": {"Version": "3.0", "Source": {"Dataset": {"Code": "TVTFU", "Id": 160}, "Version": "3.0", "Type": "<PERSON><PERSON><PERSON>"}, "Target": {"Dataset": {"Code": "TVTFU", "Id": 160}, "Version": "3.0", "Type": "Biome"}, "Translations": [{"Target": "ncdrpatientid", "Source": "ncdrpatientid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "partid", "Source": "partid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "partname", "Source": "partname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "firstname", "Source": "firstname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lastname", "Source": "lastname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "midname", "Source": "midname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dob", "Source": "dob", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ssnna", "Source": "ssnna", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ssn", "Source": "ssn", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": null, "custom string": 1}, "is_computation": false}]}, {"Target": "otherid", "Source": "otherid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gender", "Source": "sex", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "zipcodena", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "patzip", "Source": "zipcode", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racewhite", "Source": "racewhite", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceblack", "Source": "raceblack", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceamindian", "Source": "raceamindian", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceasian", "Source": "raceasian", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceasianindian", "Source": "raceasianindian", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racechinese", "Source": "racechinese", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racefilipino", "Source": "racefilipino", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racejapanese", "Source": "racejapanese", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racekorean", "Source": "racekorean", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racevietnamese", "Source": "racevietnamese", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceotherasian", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racenathaw", "Source": "racenathaw", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceguamchamorro", "Source": "raceguamchamorro", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceother<PERSON>ci<PERSON><PERSON>lander", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hispethnicitymexican", "Source": "hispethnicitymexican", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hispethnicitypuertorico", "Source": "hispethnicitypuertorico", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hispethnicitycuban", "Source": "hispethnicitycuban", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hispethnicityotherorigin", "Source": "hispethnicityotherorigin", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "followupkey", "Source": "followupkey", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "assessmentdate", "Source": "f_assessmentdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "refarrivaldatetime", "Source": "refarrivaldatetime", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": "arrivaltime"}, "is_computation": null}]}, {"Target": "dischargedate", "Source": "fu_refdischargedate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "refprocstartdatetime", "Source": "refprocstartdatetime", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": "<PERSON><PERSON><PERSON><PERSON>"}, "is_computation": null}]}, {"Target": "f_refprotype", "Source": "f_refprotype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_assessmentmethod", "Source": "f_method", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_status", "Source": "f_status", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_deathcause", "Source": "f_deathcause", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_deathdate", "Source": "f_deathdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_residence", "Source": "f_residence", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_residencend", "Source": "f_residencend", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_hgb", "Source": "fu_prochgb1", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_hgbnd", "Source": "fuhgbnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_cr", "Source": "follow_creat", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_crnd", "Source": "followcreatininenotdrawn", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_nyha", "Source": "f_nyha", "Transformation": null, "Value Map": {"Class I": "I", "Class II": "II", "Class III": "III", "Class IV": "IV"}, "Computation": null}, {"Target": "f_nyhand", "Source": "f_nyhand", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_12leadekgflag", "Source": "f_12leadekg", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_ekgchange", "Source": "f_ekgchange", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_popttech", "Source": "f_popttech", "Transformation": null, "Value Map": {"Transthoracic Echo (TTE)": "Yes - TTE", "Transesophageal Echocardiogram (TEE)": "Yes - TEE"}, "Computation": null}, {"Target": "f_echond", "Source": "f_echond", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_popttechdate", "Source": "f_popttechdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_lvef", "Source": "f_lvef", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_lvefna", "Source": "f_lvefna", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_post_aorticvalvemeangradient", "Source": "f_avmeangradient", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_avarea", "Source": "f_avarea", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_popttar", "Source": "f_ar", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_post_aiperiseverity", "Source": "f_paraar", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_post_aiperiseveritynd", "Source": "f_paraarnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_post_aicentralseverity", "Source": "f_centar", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_post_aicentralseveritynd", "Source": "f_centarnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_post_tr", "Source": "f_post_tr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_paratr", "Source": "f_paratr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_paratrnd", "Source": "f_paratrnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_centr", "Source": "f_centr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_centrnd", "Source": "f_centrnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_4dct", "Source": "f_4dct", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_4dctdate", "Source": "f_4dctdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_vthromb", "Source": "f_vthromb", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_leafdysfx", "Source": "f_leafdysfx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_kccq12_performed", "Source": "f_kccq12_performed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_kccq12_date", "Source": "f_kccq12_date", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_kccq12_1a", "Source": "f_kccq12_1a", "Transformation": null, "Value Map": {"1 - Extremely Limited": "Extremely Limited", "2 - Quite a Bit Limited": "Quite a Bit Limited", "3 - Moderately Limited": "Moderately Limited", "4 - Slightly Limited": "Slightly Limited", "5 - Not at All Limited": "Not at All Limited", "6 - Limited for Other Reasons or Did Not Do These Activities": "Limited for Other Reasons or Did Not Do These Activities"}, "Computation": null}, {"Target": "f_kccq12_1b", "Source": "f_kccq12_1b", "Transformation": null, "Value Map": {"1 - Extremely Limited": "Extremely Limited", "2 - Quite a Bit Limited": "Quite a Bit Limited", "3 - Moderately Limited": "Moderately Limited", "4 - Slightly Limited": "Slightly Limited", "5 - Not at All Limited": "Not at All Limited", "6 - Limited for Other Reasons or Did Not Do These Activities": "Limited for Other Reasons or Did Not Do These Activities"}, "Computation": null}, {"Target": "f_kccq12_1c", "Source": "f_kccq12_1c", "Transformation": null, "Value Map": {"1 - Extremely Limited": "Extremely Limited", "2 - Quite a Bit Limited": "Quite a Bit Limited", "3 - Moderately Limited": "Moderately Limited", "4 - Slightly Limited": "Slightly Limited", "5 - Not at All Limited": "Not at All Limited", "6 - Limited for Other Reasons or Did Not Do These Activities": "Limited for Other Reasons or Did Not Do These Activities"}, "Computation": null}, {"Target": "f_kccq12_2", "Source": "f_kccq12_2", "Transformation": null, "Value Map": {"1 - Every Morning": "Every Morning", "2 - Three or More Times Per Week But Not Everyday": "3 or More Times Per Week But Not Everyday", "3 - One to Two Times Per Week": "1-2 Times Per Week", "4 - Less Than Once a Week": "Less Than Once a Week", "5 - Never Over the Past Two Weeks": "Never Over the Past 2 Weeks"}, "Computation": null}, {"Target": "f_kccq12_3", "Source": "f_kccq12_3", "Transformation": null, "Value Map": {"1 - All the Time": "All the Time", "2 - Several Times Per Day": "Several Times Per Day", "3 - At Least Once Per Day": "At Least Once Per Day", "4 - Three or More Times Per Week But Not Everyday": "3 or More Times Per Week But Not Everyday", "5 - One to Two Times Per Week": "1-2 Times Per Week", "6 - Less Than Once a Week": "Less Than Once a Week", "7 - Never Over the Past Two Weeks": "Never Over the Past 2 Weeks"}, "Computation": null}, {"Target": "f_kccq12_4", "Source": "f_kccq12_4", "Transformation": null, "Value Map": {"1 - All the Time": "All the Time", "2 - Several Times Per Day": "Several Times Per Day", "3 - At Least Once Per Day": "At Least Once Per Day", "4 - Three or More Times Per Week But Not Everyday": "3 or More Times Per Week But Not Everyday", "5 - One to Two Times Per Week": "1-2 Times Per Week", "6 - Less Than Once a Week": "Less Than Once a Week", "7 - Never Over the Past Two Weeks": "Never Over the Past 2 Weeks"}, "Computation": null}, {"Target": "f_kccq12_5", "Source": "f_kccq12_5", "Transformation": null, "Value Map": {"1 - Every Night": "Every Night", "2 - Three or More Times Per Week But Not Everyday": "3 or More Times Per Week But Not Everyday", "3 - One to Two Times Per Week": "1-2 Times Per Week", "4 - Less Than Once a Week": "Less Than Once a Week", "5 - Never Over the Past Two Weeks": "Never Over the Past 2 Weeks"}, "Computation": null}, {"Target": "f_kccq12_6", "Source": "f_kccq12_6", "Transformation": null, "Value Map": {"1 - It Has Extremely Limited My Enjoyment of Life": "It Has Extremely Limited My Enjoyment of Life", "2 - It Has Limited My Enjoyment of Life Quite a Bit": "It Has Limited My Enjoyment of Life Quite a Bit", "3 - It Has Moderately Limited My Enjoyment of Life": "It Has Moderately Limited My Enjoyment of Life", "4 - It Has Slightly Limited My Enjoyment of Life": "It Has Slightly Limited My Enjoyment of Life", "5 - It Has Not Limited My Enjoyment of Life at All": "It Has Not Limited My Enjoyment of Life at All"}, "Computation": null}, {"Target": "f_kccq12_7", "Source": "f_kccq12_7", "Transformation": null, "Value Map": {"1 - Not At All Satisfied": "Not At All Satisfied", "2 - Mostly Dissatisfied": "Mostly Dissatisfied", "3 - Somewhat Satisfied": "Somewhat Satisfied", "4 - Mostly Satisfied": "Mostly Satisfied", "5 - Completely Satisfied": "Completely Satisfied"}, "Computation": null}, {"Target": "f_kccq12_8a", "Source": "f_kccq12_8a", "Transformation": null, "Value Map": {"1 - Severely Limited": "Severely Limited", "2 - Limited Quite a Bit": "Limited Quite a Bit", "3 - Moderately Limited": "Moderately Limited", "4 - Slightly Limited": "Slightly Limited", "5 - Did Not Limit at All": "Did Not Limit at All", "6 - Does Not Apply or Did Not Do for Other Reasons": "Does Not Apply or Did Not Do for Other Reasons"}, "Computation": null}, {"Target": "f_kccq12_8b", "Source": "f_kccq12_8b", "Transformation": null, "Value Map": {"1 - Severely Limited": "Severely Limited", "2 - Limited Quite a Bit": "Limited Quite a Bit", "3 - Moderately Limited": "Moderately Limited", "4 - Slightly Limited": "Slightly Limited", "5 - Did Not Limit at All": "Did Not Limit at All", "6 - Does Not Apply or Did Not Do for Other Reasons": "Does Not Apply or Did Not Do for Other Reasons"}, "Computation": null}, {"Target": "f_kccq12_8c", "Source": "f_kccq12_8c", "Transformation": null, "Value Map": {"1 - Severely Limited": "Severely Limited", "2 - Limited Quite a Bit": "Limited Quite a Bit", "3 - Moderately Limited": "Moderately Limited", "4 - Slightly Limited": "Slightly Limited", "5 - Did Not Limit at All": "Did Not Limit at All", "6 - Does Not Apply or Did Not Do for Other Reasons": "Does Not Apply or Did Not Do for Other Reasons"}, "Computation": null}, {"Target": "f_kccq12_overall", "Source": "f_kccq12_overall", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "followup_direct_thrombin_inhibitor", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin1", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Direct thrombin inhibitor", "Derivative Value Code": "414010005", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "followup_warfarin", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin1", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Warfarin", "Derivative Value Code": "11289", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "followup_aspirin_any", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin1", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": "100013057", "Derivative Value": "<PERSON><PERSON><PERSON>", "Derivative Value Code": "1191", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "followup_factor_xa_inhibitor", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin1", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Direct Factor Xa Inhibitor", "Derivative Value Code": "112000000696", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "followup_p2y12_any", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin1", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": "100013057", "Derivative Value": "P2Y12 Antagonist", "Derivative Value Code": "112000001003", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "f_post_mvmeangrad", "Source": "f_meanmvgrad", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_post_mveoa", "Source": "f_mv_eoa", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_post_mveoamethod", "Source": "f_mv_eoa_moa", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_post_mvarea", "Source": "f_mva", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_post_lvot", "Source": "f_lvot", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_post_sam", "Source": "f_sam", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_post_lvisd", "Source": "f_lvids", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_post_lvisdnm", "Source": "f_lvids_nm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_post_lvidd", "Source": "f_lvidd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_post_lviddnm", "Source": "f_lvidd_nm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_post_lvesv", "Source": "f_lvesv", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_post_lvesvnm", "Source": "f_lvesv_nm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_post_lvedv", "Source": "f_lvedv", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_post_lvedvnm", "Source": "f_lvedv_nm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_post_lavol", "Source": "f_lavol", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_lavol_nm", "Source": "f_lavol_nm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_post_lavolindex", "Source": "f_lavolindex", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_lavolindex_nm", "Source": "f_lavolindex_nm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_post_mr", "Source": "f_mr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_post_mrpara", "Source": "f_paramr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_paramrnd", "Source": "f_paramrnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_centralmr", "Source": "f_centralmr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_centralmrnd", "Source": "f_centralmrnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_sixminwalkperf", "Source": "f_sixminwalkperf", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_sixminwalkperfreason", "Source": "f_sixminwalkperfreason", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_sixminwalkdate", "Source": "f_sixminwalkdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_sixminwalkdist", "Source": "f_sixminwalkdist", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "followup_ace_inhibitor_any", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin1", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Angiotensin Converting Enzyme Inhibitor", "Derivative Value Code": "41549009", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "followup_aldosterone_antagonists", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin1", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Aldosterone Antagonist", "Derivative Value Code": "372603003", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "followup_arb_any", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin1", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Angiotensin II Receptor Blocker", "Derivative Value Code": "372913009", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "followup_beta_blocker_any", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin1", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Beta Blocker", "Derivative Value Code": "33252009", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "followup_diuretics_other", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin1", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Diuretics Not Otherwise Specified", "Derivative Value Code": "112000001417", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "followup_loop_diuretic", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin1", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Loop Diuretics", "Derivative Value Code": "29051009", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "followup_thiazides", "Source": null, "Transformation": {"Reference Field": "F_MedAdmin1", "Reference Field Code": 432102000, "Derivative Field": "F_MedID", "Derivative Field Code": "100013057", "Derivative Value": "<PERSON><PERSON><PERSON><PERSON>", "Derivative Value Code": "372747003", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "aj_mvreinttype", "Source": "f_aj_mvreintype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_mvind", "Source": "f_aj_m<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_hospitalization", "Source": "f_aj_hospital", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_sshf", "Source": "f_aj_sshf", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_hftreatment", "Source": "f_aj_hftreatment", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_meddose", "Source": "fumed_loopdiureticdose", "Transformation": {"Reference Field": "FUMed_LoopDiureticDose", "Reference Field Code": 112000001975, "Derivative Field": "F_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Loop Diuretics", "Derivative Value Code": "29051009", "Value Map": null, "Exact Match": 1, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "f_eventname", "Source": "f_condition_event", "Transformation": null, "Value Map": {"COVID-19 Positive": "COVID-19"}, "Computation": null}, {"Target": "f_eventoccurred", "Source": "fupevoccurred", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_eventdate", "Source": "fupeventdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_adjudevent", "Source": "aj_adjudevent", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_eventdate", "Source": "aj_eventdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_status", "Source": "aj_status", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_deathdate", "Source": "aj_deathdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_mvcommentsreint", "Source": "aj_commentsfu", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "arrivaltime", "Source": "refarrivaldatetime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "arrivaldate", "Source": "refarrivaldatetime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "refprocstartdatetime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "procedurestartdate", "Source": "refprocstartdatetime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "datavrsn", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_refprotypetricuspidvalvproc", "Source": null, "Transformation": {"Reference Field": "F_RefProType", "Reference Field Code": 112000001167, "Derivative Field": "F_RefProType", "Derivative Field Code": "112000001167", "Derivative Value": "Tricuspid Valve Procedure", "Derivative Value Code": "112000001977", "Value Map": {"Tricuspid Valve Procedure": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "f_refprotypetavr", "Source": null, "Transformation": {"Reference Field": "F_RefProType", "Reference Field Code": 112000001167, "Derivative Field": "F_RefProType", "Derivative Field Code": "112000001167", "Derivative Value": "TAVR", "Derivative Value Code": "41873006", "Value Map": {"TAVR": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "f_refprotypetmvrepair", "Source": null, "Transformation": {"Reference Field": "F_RefProType", "Reference Field Code": 112000001167, "Derivative Field": "F_RefProType", "Derivative Field Code": "112000001167", "Derivative Value": "TMVr", "Derivative Value Code": "112000001801", "Value Map": {"TMVr": "Yes"}, "Exact Match": 0, "Case Match": "1", "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "f_refprotypetmvreplace", "Source": null, "Transformation": {"Reference Field": "F_RefProType", "Reference Field Code": 112000001167, "Derivative Field": "F_RefProType", "Derivative Field Code": "112000001167", "Derivative Value": "TMVR", "Derivative Value Code": "112000001458", "Value Map": {"TMVR": "Yes"}, "Exact Match": 0, "Case Match": "1", "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "f_12leadekg", "Source": "f_12leadekg", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "Not performed", "false_val": null, "fields": [{"name": "f_12leadekgflag", "values": ["NO"]}]}, "is_computation": null}, {"Transform_Type": "conditional_and", "Args": {"true_val": "No significant changes", "false_val": "f_12leadekg", "fields": [{"name": "f_ekgchange", "values": ["NO SIGNIFICANT CHANGE"]}]}, "is_computation": null}, {"Transform_Type": "conditional_and", "Args": {"true_val": "New changes noted", "false_val": "f_12leadekg", "fields": [{"name": "f_12leadekgflag", "values": ["YES"]}, {"name": "f_ekgchange", "values": ["Cardiac Arrhythmia", "New Left Bundle Branch Block", "Pathological Q Wave"]}]}, "is_computation": null}]}, {"Target": "followup_ace_i_or_arb_any", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_or", "Args": {"true_val": "Yes", "false_val": null, "fields": [{"name": "followup_arb_any", "values": ["YES"]}, {"name": "followup_ace_inhibitor_any", "values": ["YES"]}]}, "is_computation": null}]}, {"Target": "followup_aspirin_alone", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "Yes", "false_val": null, "fields": [{"name": "followup_aspirin_any", "values": ["YES"]}, {"name": "followup_p2y12_any", "values": ["NOT"]}]}, "is_computation": null}]}, {"Target": "followup_aspirin_dual_antiplatelet", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "Yes", "false_val": null, "fields": [{"name": "followup_aspirin_any", "values": ["Yes"]}, {"name": "followup_p2y12_any", "values": ["Yes"]}]}, "is_computation": null}]}, {"Target": "aj_commentsreint", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": "aj_mvcommentsreint"}, "is_computation": null}]}, {"Target": "aj_commentsstroketia", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": "aj_mvcommentsreint"}, "is_computation": null}]}, {"Target": "f_eventid", "Source": "f_condition_event", "Transformation": null, "Value Map": {"Annular Rupture": "E007", "Aortic Dissection": "E008", "ASD Defect Closure due to Transseptal Catheterization": "E054", "Atrial Fibrillation": "E006", "Bleeding - Access Site": "E017", "Bleeding - Gastrointestinal": "E020", "Bleeding - Genitourinary": "E021", "Bleeding - Hematoma at Access Site": "E018", "Bleeding - Other": "E022", "Bleeding - Retroperitoneal": "E019", "Cardiac Arrest": "E005", "Cardiac Perforation": "E009", "Cardiac Surgery or Intervention - Other Unplanned": "E031", "Complete Leaflet Clip Detachment": "E051", "Coronary Artery Compression": "E002", "Delivery System Component Embolization": "E058", "Device Embolization": "E050", "Device Migration": "E023", "Device Related Event - Other": "E028", "Device Thrombosis": "E027", "Dialysis (New Requirement)": "E029", "Endocarditis": "E003", "ICD": "E040", "Left Ventricular Outflow Tract Obstruction": 253546004, "Mitral Leaflet or Subvalvular Injury": 112000001886, "Myocardial Infarction": "E059", "Pacemaker Lead Dislodgement or Dysfunction": 112000001884, "Percutaneous Coronary Intervention": "E033", "Permanent Pacemaker": "E039", "Pulmonary Embolism": 59282003, "Reintervention - Aortic Valve": "E030", "Reintervention - Mitral Valve": "E053", "Reintervention - Tricuspid Valve": 112000001820, "Single Leaflet Device Attachment": "E049", "Stroke - Hemorrhagic": "E012", "Stroke - Ischemic": "E011", "Stroke - Undetermined": "E013", "Transient Ischemic Attack (TIA)": "E010", "Transseptal Complication": "E052", "Vascular Complication - Major": "E041", "Vascular Complication - Minor": "E042", "Vascular Surgery or Intervention - Unplanned": "E032", "Readmission - Cardiac (Not Heart Failure)": "E056", "Readmission - Heart Failure": "E055", "Readmission - Non-Cardiac": "E057", "COVID-19 Positive": 112000001982, "Deep Vein Thrombosis": 128053003, "Device Fracture": "E038", "Bleeding - Life Threatening": "E037", "Bleeding - Major": "E043", "Readmission (Valve Related)": "E034", "Readmission - (Non-Valve Related)": "E035"}, "Computation": null}, {"Target": "originalotherid", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": "otherid"}, "is_computation": null}]}, {"Target": "hospname", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "filename", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "version", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "datasetname", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "clientfileid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "biomeimportdt", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "careentityid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tenantid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "yearmonth", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_aisev", "Source": "aj_aisev", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_censev", "Source": "aj_censev", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_tvdgrad", "Source": "f_tvdgrad", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_tvdgradnd", "Source": "f_tvdgradnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_tvannulus", "Source": "f_tvannulus", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_tvannulusnd", "Source": "f_tvannulusnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_midrvdia", "Source": "f_midrvdia", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_midrvdiand", "Source": "f_midrvdiand", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_basalrvdia", "Source": "f_basalrvdia", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_basaldiand", "Source": "f_basaldiand", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_rvsp", "Source": "f_rvsp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_rvspnd", "Source": "f_rvspnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_aj_brainimage", "Source": "f_aj_brainimag", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_aj_brainimagetype", "Source": "f_aj_brainimagetype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_bi_find", "Source": "f_bi_find", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_adj_ers_death", "Source": null, "Transformation": {"Reference Field": "F_Adj_ERS", "Reference Field Code": 362977000, "Derivative Field": "F_Adj_ERS", "Derivative Field Code": "362977000", "Derivative Value": "Death", "Derivative Value Code": "419620001", "Value Map": {"Death": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "f_adj_ers_pervegestate", "Source": null, "Transformation": {"Reference Field": "F_Adj_ERS", "Reference Field Code": 362977000, "Derivative Field": "F_Adj_ERS", "Derivative Field Code": "362977000", "Derivative Value": "Permanent Vegetative State", "Derivative Value Code": "723151005", "Value Map": {"Permanent Vegetative State": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "f_adj_ers_altconscious", "Source": null, "Transformation": {"Reference Field": "F_Adj_ERS", "Reference Field Code": 362977000, "Derivative Field": "F_Adj_ERS", "Derivative Field Code": "362977000", "Derivative Value": "Altered Consciousness", "Derivative Value Code": "3006004", "Value Map": {"Altered Consciousness": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "f_adj_ers_blind", "Source": null, "Transformation": {"Reference Field": "F_Adj_ERS", "Reference Field Code": 362977000, "Derivative Field": "F_Adj_ERS", "Derivative Field Code": "362977000", "Derivative Value": "Blindness", "Derivative Value Code": "193699007", "Value Map": {"Blindness": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "f_adj_ers_aphasia", "Source": null, "Transformation": {"Reference Field": "F_Adj_ERS", "Reference Field Code": 362977000, "Derivative Field": "F_Adj_ERS", "Derivative Field Code": "362977000", "Derivative Value": "Aphasia", "Derivative Value Code": "87486003", "Value Map": {"Aphasia": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "f_adj_ers_motorfuncloss", "Source": null, "Transformation": {"Reference Field": "F_Adj_ERS", "Reference Field Code": 362977000, "Derivative Field": "F_Adj_ERS", "Derivative Field Code": "362977000", "Derivative Value": "Loss of Motor Function", "Derivative Value Code": "112000001936", "Value Map": {"Loss of Motor Function": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "f_adj_ers_<PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "F_Adj_ERS", "Reference Field Code": 362977000, "Derivative Field": "F_Adj_ERS", "Derivative Field Code": "362977000", "Derivative Value": "Loss of Sensory Function", "Derivative Value Code": "33653009", "Value Map": {"Loss of Sensory Function": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "f_adj_ers_facialparalysis", "Source": null, "Transformation": {"Reference Field": "F_Adj_ERS", "Reference Field Code": 362977000, "Derivative Field": "F_Adj_ERS", "Derivative Field Code": "362977000", "Derivative Value": "Facial Paralysis", "Derivative Value Code": "280816001", "Value Map": {"Facial Paralysis": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "f_adj_ers_<PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "F_Adj_ERS", "Reference Field Code": 362977000, "Derivative Field": "F_Adj_ERS", "Derivative Field Code": "362977000", "Derivative Value": "Prolonged Length of Stay", "Derivative Value Code": "112000001937", "Value Map": {"Prolonged Length of Stay": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "f_adj_ers_other", "Source": null, "Transformation": {"Reference Field": "F_Adj_ERS", "Reference Field Code": 362977000, "Derivative Field": "F_Adj_ERS", "Derivative Field Code": "362977000", "Derivative Value": "Other", "Derivative Value Code": "100000351", "Value Map": {"Other": "Yes"}, "Exact Match": 0, "Case Match": null, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "f_aj_dlae", "Source": "f_aj_dlae", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_aj_priorliving", "Source": "f_aj_priorliving", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_aj_autdxstroke", "Source": "f_aj_autdxstroke", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_aj_ava", "Source": "f_aj_ava", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_aj_avg", "Source": "f_aj_avg", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_aj_tvrein", "Source": "f_aj_tvrein", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_aj_tvind", "Source": "f_aj_tvind", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_aj_tr", "Source": "f_aj_tr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "casesequencenumber", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_neuroclinpresent", "Source": "aj_neuroclinpresent", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_neurodef", "Source": "aj_neurodef", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_neurosxduration", "Source": "f_aj_neurosymptduration", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_primaryind", "Source": "aj_primaryind", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_pvsev", "Source": "aj_pvsev", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_reinttype", "Source": "aj_reinttype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aj_sxonset", "Source": "aj_sxonset", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_medadmin", "Source": "f_medadmin1", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "f_medid", "Source": "f_medid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "followup_antiarrhythmic_any", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "followup_anticoagulants_any", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "followup_dabigatran", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "procedureenddatetime", "Source": "procedureenddatetime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "biomeencounterid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}]}}