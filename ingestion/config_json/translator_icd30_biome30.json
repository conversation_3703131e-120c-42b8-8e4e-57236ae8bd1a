{"TranslatorConfig": {"Version": "3.0", "Source": {"Dataset": {"Code": "ICD", "Id": 107}, "Version": "3.0", "Type": "<PERSON><PERSON><PERSON>"}, "Target": {"Dataset": {"Code": "ICD", "Id": 107}, "Version": "3.0", "Type": "Biome"}, "Translations": [{"Target": "age", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "biomeencounterid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "biomeimportdt", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "careentityid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "casesequencenumber", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "clientfileid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "datasetname", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "datavrsn", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "distfromhospital", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "explantdevicename", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "filename", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hospname", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "icdimpmodelname", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "leadmodelname", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "originalotherid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "particid", "Source": "partid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "procedureendtime", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": "procedureenddate"}, "is_computation": true}]}, {"Target": "proceduretime", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": "proceduredate"}, "is_computation": true}]}, {"Target": "tenantid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "version", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "yearmonth", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "partname", "Source": "partname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "timeframe", "Source": "timeframe", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vendorid", "Source": "vendorid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "registryid", "Source": "registryid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lastname", "Source": "lastname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "firstname", "Source": "firstname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "midname", "Source": "midname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ncdrpatientid", "Source": "ncdrpatientid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherid", "Source": "otherid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dob", "Source": "dob", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gender", "Source": "sex", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "patzip", "Source": "zipcode", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "zipcodena", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racewhite", "Source": "racewhite", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceblack", "Source": "raceblack", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceasian", "Source": "raceasian", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceamindian", "Source": "raceamindian", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "racenathaw", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "episodeid", "Source": "episodekey", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "arrivaldate", "Source": "arrivaldate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "healthinsurance", "Source": "healthins", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "insihs", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "Indian Health Service", "Derivative Value Code": "33", "Value Map": {"|Indian Health Service|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "insmedicaid", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "Medicaid", "Derivative Value Code": "2", "Value Map": {"|Medicaid|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "insmedicare", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "Medicare (Part A or B)", "Derivative Value Code": "1", "Value Map": {"|Medicare (Part A or B)|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "insmedicareadvantage", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "Medicare Advantage (Part C)", "Derivative Value Code": "112000002025", "Value Map": {"|Medicare Advantage (Part C)|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "insmilitary", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "Military health care", "Derivative Value Code": "31", "Value Map": {"|Military health care|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON>us", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "Non-US insurance", "Derivative Value Code": "100000812", "Value Map": {"|Non-US insurance|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "insprivate", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "Private health insurance", "Derivative Value Code": "5", "Value Map": {"|Private health insurance|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "insstate", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": "100001072", "Derivative Value": "State-specific plan (non-Medicaid)", "Derivative Value Code": "36", "Value Map": {"|State-specific plan (non-Medicaid)|": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": "|"}, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON>udy", "Source": "<PERSON><PERSON>udy", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "studyname", "Source": "studyname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rstudy<PERSON><PERSON>", "Source": "studyptid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "reasonforadmit", "Source": "reasonforadmit", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "nyha", "Source": "nyha", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lvefassessed", "Source": "priorlvefassessed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mstreclvefdate", "Source": "priorlvefdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lvef", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "syndromerisktype", "Source": "syndromerisktype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ischcardiotimeframe", "Source": "iscmtimeframe", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "guiddiremedthermaxdose", "Source": "iscmgdmtdose", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "nidcmtimeframe", "Source": "nicmtimeframe", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "nidcmmxdose", "Source": "nicmgdmtdose", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cardiacarrestdate", "Source": "cardiacarrestdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vtarrest", "Source": "vtacharrest", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vfibarrest", "Source": "vfibarrest", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "brady<PERSON><PERSON>", "Source": "brady<PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vtdate", "Source": "vtdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postcardiacsurgery", "Source": "vtpostcardiacsurgery", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "bradycardiadependent", "Source": "bradydependent", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "reversiblecause", "Source": "vtreversecause", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hemoinstability", "Source": "hemoinstability", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vttype", "Source": "vttype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "priormidate", "Source": "priormidate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "perfafterrecentca", "Source": "perfafterrecentca", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "resultsofangiography", "Source": "coronaryangioresults", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "revascularperformed", "Source": "revascperf", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "revascularizationoutcome", "Source": "revascoutcome", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibflutterclass", "Source": "afibclass", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "afibfluttercardioplans", "Source": "afibfluttercardioplans", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "priorpcicardiopresent", "Source": "priorpcicardiopresent", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "structabntype", "Source": "structabntype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "epstudy", "Source": "epstudy", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "epstudydate", "Source": "epstudydate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "epstudydateunk", "Source": "epstudydateunk", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ventarrythinduced", "Source": "ventarrythinduced", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ecg", "Source": "ecg", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ecgnormal", "Source": "ecgnormal", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vpqrs", "Source": "vpqrs", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vpacedqrs", "Source": "vpacedqrs", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "nvpqrs", "Source": "nvpqrs", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "abconductiontype", "Source": "intraventconductiontype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "abconduction", "Source": "abconduction", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "abcon<PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "AbConduction", "Reference Field Code": 4554005, "Derivative Field": "IntraVentConductionType", "Derivative Field Code": "100001142", "Derivative Value": "Delay, nonspecific", "Derivative Value Code": "698252002", "Value Map": null, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "abconductionlbbb", "Source": null, "Transformation": {"Reference Field": "AbConduction", "Reference Field Code": 4554005, "Derivative Field": "IntraVentConductionType", "Derivative Field Code": "100001142", "Derivative Value": "Left bundle branch block (LBBB)", "Derivative Value Code": "164909002", "Value Map": null, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "abconductionrbbb", "Source": null, "Transformation": {"Reference Field": "AbConduction", "Reference Field Code": 4554005, "Derivative Field": "IntraVentConductionType", "Derivative Field Code": "100001142", "Derivative Value": "Right bundle branch block (RBBB)", "Derivative Value Code": "164907000", "Value Map": null, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "abconductionrbbbandlbbb", "Source": null, "Transformation": {"Reference Field": "AbConduction", "Reference Field Code": 4554005, "Derivative Field": "IntraVentConductionType", "Derivative Field Code": "100001142", "Derivative Value": "Alternating RBBB and LBBB", "Derivative Value Code": "32758004", "Value Map": null, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "atrialrhythm", "Source": "atrialrhythm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "atrialrhythmafib", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Atrial fibrillation", "Derivative Value Code": "49436004", "Value Map": {"Atrial fibrillation": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "atrialrhythmaflutter", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Atrial flutter", "Derivative Value Code": "5370000", "Value Map": {"Atrial flutter": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "atrialrhythmap", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Atrial paced", "Derivative Value Code": "251268003", "Value Map": {"Atrial paced": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "atrialrhythmatrialtach", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Atrial tachycardia", "Derivative Value Code": "276796006", "Value Map": {"Atrial fibrillation": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "atrialrhythmsinusarrest", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Sinus arrest", "Derivative Value Code": "5609005", "Value Map": {"Sinus arrest": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "atrialrhythmsinusnode", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": "106068003", "Derivative Value": "Sinus", "Derivative Value Code": "106067008", "Value Map": {"Sinus": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "vpace", "Source": "vpaced", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "bun", "Source": "bun", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "bunnd", "Source": "bunnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hgb", "Source": "hgb", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hgbnd", "Source": "hgbnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "sodium", "Source": "sodium", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "sodiumnd", "Source": "sodiumnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "proceduredate", "Source": "procedurestartdatetime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "procedureenddate", "Source": "procedureenddatetime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "proceduretype", "Source": "proceduretype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "icdindication", "Source": "icdindication", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "clinicaltrial", "Source": "clinicaltrial", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "genoplname", "Source": "genoplname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "genopfname", "Source": "genopfname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "genopmname", "Source": "genopmname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "genopnpi", "Source": "genopnpi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "deviceimplanted", "Source": "deviceimplanted", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cslvlead", "Source": "cslvlead", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "icdimpid", "Source": "icdimpid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "icdimpserno", "Source": "icdimpserno", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "icdimpudi", "Source": "icdimpudi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "reasonforreimplantation", "Source": "reimplant<PERSON>son", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "reimpbattery", "Source": null, "Transformation": {"Reference Field": "ReImplantReason", "Reference Field Code": 100000991, "Derivative Field": "ReImplantReason", "Derivative Field Code": "100000991", "Derivative Value": "End of expected battery life", "Derivative Value Code": "100001088", "Value Map": {"End of expected battery life": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "reimpfaulty", "Source": null, "Transformation": {"Reference Field": "ReImplantReason", "Reference Field Code": 100000991, "Derivative Field": "ReImplantReason", "Derivative Field Code": "100000991", "Derivative Value": "Faulty connector/header", "Derivative Value Code": "100001089", "Value Map": {"Faulty connector/header": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "reimpinfection", "Source": null, "Transformation": {"Reference Field": "ReImplantReason", "Reference Field Code": 100000991, "Derivative Field": "ReImplantReason", "Derivative Field Code": "100000991", "Derivative Value": "Infection", "Derivative Value Code": "100001091", "Value Map": {"Infection": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "reimple<PERSON><PERSON>v", "Source": null, "Transformation": {"Reference Field": "ReImplantReason", "Reference Field Code": 100000991, "Derivative Field": "ReImplantReason", "Derivative Field Code": "100000991", "Derivative Value": "Replaced at time of lead revision", "Derivative Value Code": "100001092", "Value Map": {"Replaced at time of lead revision": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "reimpmalfx", "Source": null, "Transformation": {"Reference Field": "ReImplantReason", "Reference Field Code": 100000991, "Derivative Field": "ReImplantReason", "Derivative Field Code": "100000991", "Derivative Value": "Malfunction", "Derivative Value Code": "100001090", "Value Map": {"Malfunction": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "reimpother", "Source": null, "Transformation": {"Reference Field": "ReImplantReason", "Reference Field Code": 100000991, "Derivative Field": "ReImplantReason", "Derivative Field Code": "100000991", "Derivative Value": "Other", "Derivative Value Code": "112000003710", "Value Map": {"Other": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "reimprecall", "Source": null, "Transformation": {"Reference Field": "ReImplantReason", "Reference Field Code": 100000991, "Derivative Field": "ReImplantReason", "Derivative Field Code": "100000991", "Derivative Value": "Under manufacturer advisory/recall", "Derivative Value Code": "100001093", "Value Map": {"Under manufacturer advisory/recall": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "reimprelocation", "Source": null, "Transformation": {"Reference Field": "ReImplantReason", "Reference Field Code": 100000991, "Derivative Field": "ReImplantReason", "Derivative Field Code": "100000991", "Derivative Value": "Device relocation", "Derivative Value Code": "100001087", "Value Map": {"Device relocation": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "reimpupgrade", "Source": null, "Transformation": {"Reference Field": "ReImplantReason", "Reference Field Code": 100000991, "Derivative Field": "ReImplantReason", "Derivative Field Code": "100000991", "Derivative Value": "Upgrade", "Derivative Value Code": "100001094", "Value Map": {"Upgrade": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "deviceexplant", "Source": "deviceexplant", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "explanttreatrecommend", "Source": "explanttreatment", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "icdexpid", "Source": "icdexpid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "icdexpserno", "Source": "icdexpserno", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "leadoplname", "Source": "leadoplname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "leadopfname", "Source": "leadopfname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "leadopmname", "Source": "leadopmname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "leadopnpi", "Source": "leadopnpi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "leadcounter", "Source": "leadcounter", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "leadtype", "Source": "leadtype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "leadid", "Source": "leadid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "leadsern<PERSON>", "Source": "leadsern<PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lead<PERSON>", "Source": "lead<PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "leadlocation", "Source": "leadlocation", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "exleaddate", "Source": "exleaddate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "exleadstat", "Source": "exleadstat", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "setscrew", "Source": "setscrew", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "leaddislodge", "Source": "leaddislodge", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "leaddislodgeloc", "Source": "leaddislodgeloc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cabg", "Source": "cabg", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cabgdate", "Source": "cabgdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pci", "Source": "pci", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pcidate", "Source": "pcidate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dischargedate", "Source": "dcdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dischargestatus", "Source": "dcstatus", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dischargelocation", "Source": "dclocation", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "deathprocedure", "Source": "deathprocedure", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "deathcause", "Source": "deathcause", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ace", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Angiotensin Converting Enzyme Inhibitor", "Derivative Value Code": "41549009", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "aldosteroneantagonist", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Aldosterone Antagonist", "Derivative Value Code": "372603003", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "antiarrhythmicagents", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Antiarrhythmic Drug", "Derivative Value Code": "67507000", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "antiplateletagents", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Antiplatelet Agent", "Derivative Value Code": "372560006", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "apixaban", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Apixaban", "Derivative Value Code": "1364430", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "arb", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Angiotensin II Receptor Blocker", "Derivative Value Code": "372913009", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "betablocker", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Beta Blocker", "Derivative Value Code": "33252009", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "bet<PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Betrixaban", "Derivative Value Code": "1927851", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "dabigat<PERSON>", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Dabigatran", "Derivative Value Code": "1546356", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "discharge_aspirin_any", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "<PERSON><PERSON><PERSON>", "Derivative Value Code": "1191", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Angiotensin Receptor-Neprilysin Inhibitor", "Derivative Value Code": "112000001832", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "disc<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Renin Inhibitor", "Derivative Value Code": "426228001", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "edoxaban", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Edoxaban", "Derivative Value Code": "1599538", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "rivaroxaban", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Rivaroxaban", "Derivative Value Code": "1114195", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "warfarin", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": "100013057", "Derivative Value": "Warfarin", "Derivative Value Code": "11289", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "mbi", "Source": "mbi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vfibdate", "Source": "vfibdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "bradindpres", "Source": "bradindpres", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "reasonpacingindicated", "Source": "reasonpacindic", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "reasonpacindic21avblock", "Source": null, "Transformation": {"Reference Field": "ReasonPacIndic", "Reference Field Code": 100001097, "Derivative Field": "ReasonPacIndic", "Derivative Field Code": "100001097", "Derivative Value": "2:1 AV Block", "Derivative Value Code": "54016002", "Value Map": {"2:1 AV Block": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "reasonpacindicanticipatedrequirementofgt40prvpacing", "Source": null, "Transformation": {"Reference Field": "ReasonPacIndic", "Reference Field Code": 100001097, "Derivative Field": "ReasonPacIndic", "Derivative Field Code": "100001097", "Derivative Value": "Anticipated requirement of > 40% RV pacing", "Derivative Value Code": "100000931", "Value Map": {"Anticipated requirement of > 40% RV pacing": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "reasonpacindicatrioventricularnodeablation", "Source": null, "Transformation": {"Reference Field": "ReasonPacIndic", "Reference Field Code": 100001097, "Derivative Field": "ReasonPacIndic", "Derivative Field Code": "100001097", "Derivative Value": "Atrioventricular Node Ablation", "Derivative Value Code": "428663009", "Value Map": {"Atrioventricular Node Ablation": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "reasonpacindicchronotropicincompetence", "Source": null, "Transformation": {"Reference Field": "ReasonPacIndic", "Reference Field Code": 100001097, "Derivative Field": "ReasonPacIndic", "Derivative Field Code": "100001097", "Derivative Value": "Chronotropic incompetence", "Derivative Value Code": "427989008", "Value Map": {"Chronotropic incompetence": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "reasonpacindiccompleteheartblock", "Source": null, "Transformation": {"Reference Field": "ReasonPacIndic", "Reference Field Code": 100001097, "Derivative Field": "ReasonPacIndic", "Derivative Field Code": "100001097", "Derivative Value": "Complete heart block", "Derivative Value Code": "27885002", "Value Map": {"Complete heart block": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "reasonpacindichfunresponsivetogdmt", "Source": null, "Transformation": {"Reference Field": "ReasonPacIndic", "Reference Field Code": 100001097, "Derivative Field": "ReasonPacIndic", "Derivative Field Code": "100001097", "Derivative Value": "HF unresponsive to GDMT", "Derivative Value Code": "112000002017", "Value Map": {"HF unresponsive to GDMT": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "reasonpacindic<PERSON>", "Source": null, "Transformation": {"Reference Field": "ReasonPacIndic", "Reference Field Code": 100001097, "Derivative Field": "ReasonPacIndic", "Derivative Field Code": "100001097", "Derivative Value": "Mobitz Type II", "Derivative Value Code": "28189009", "Value Map": {"Mobitz Type II": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "ReasonPacIndic", "Reference Field Code": 100001097, "Derivative Field": "ReasonPacIndic", "Derivative Field Code": "100001097", "Derivative Value": "Other", "Derivative Value Code": "100000351", "Value Map": {"Other": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "reasonpacindicsicksinussyndrome", "Source": null, "Transformation": {"Reference Field": "ReasonPacIndic", "Reference Field Code": 100001097, "Derivative Field": "ReasonPacIndic", "Derivative Field Code": "100001097", "Derivative Value": "Sick sinus syndrome", "Derivative Value Code": "36083008", "Value Map": {"Sick sinus syndrome": "Yes"}, "Exact Match": 0, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "sdmproc", "Source": "sdm_proc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "sdmtool", "Source": "sdm_tool", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "sdmtoolname", "Source": "sdm_tool_name", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "finaldevicetype", "Source": "final_device_type", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "baccesssite", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Bleeding - Access Site", "Derivative Value Code": "1000142440", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "bgastrointestinal", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Bleeding - Gastrointestinal", "Derivative Value Code": "74474003", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "bretroperitoneal", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Bleeding - Retroperitoneal", "Derivative Value Code": "95549001", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "carrest", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Cardiac arrest", "Derivative Value Code": "410429000", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "cardiacperf", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Cardiac perforation", "Derivative Value Code": "36191001:123005000=302509004", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "tamponade", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Cardiac tamponade", "Derivative Value Code": "35304003", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "cvdissect", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Coronary venous dissection", "Derivative Value Code": "100000029", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "deviceembolization", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Device embolization", "Derivative Value Code": "112000001324", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "hematoma", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Hematoma (Re-op, evac, or transfusion)", "Derivative Value Code": "385494008", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "hemothorax", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Hemothorax", "Derivative Value Code": "31892009", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Infection requiring antibiotics", "Derivative Value Code": "100001017", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "postmi", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Myocardial infarction", "Derivative Value Code": "22298006", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "pericardialeffusion", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Pericardial effusion", "Derivative Value Code": "373945007", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "pneumothorax", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Pneumothorax", "Derivative Value Code": "36118008", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "stroke", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Stroke (Any)", "Derivative Value Code": "100000977", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "transfusion", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Transfusion", "Derivative Value Code": "5447007", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "posttia", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Transient ischemic attack (TIA)", "Derivative Value Code": "266257000", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "urgentsurgery", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Urgent cardiac surgery", "Derivative Value Code": "64915003:260870009=103391001", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "vascularcomplications", "Source": null, "Transformation": {"Reference Field": "PostProcOccurred", "Reference Field Code": 1000142479, "Derivative Field": "PostProcEvent", "Derivative Field Code": "1000142478", "Derivative Value": "Vascular complications", "Derivative Value Code": "213217008", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "afibflutter", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Atrial fibrillation", "Derivative Value Code": "49436004", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "cardiac<PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Cardiac arrest", "Derivative Value Code": "410429000", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Cardiomyopathy - ischemic", "Derivative Value Code": "426856002", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "nidcm", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Cardiomyopathy - non-ischemic", "Derivative Value Code": "111000119104", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priorcvd", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Cerebrovascular disease", "Derivative Value Code": "62914000", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "chroniclungdisease", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Chronic lung disease", "Derivative Value Code": "413839001", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "cad", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Coronary artery disease", "Derivative Value Code": "53741008", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "currentdialysis", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Currently on dialysis", "Derivative Value Code": "108241001", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "diabetes", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Diabetes mellitus", "Derivative Value Code": "73211009", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "familialhxnicm", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Familial history of non-ischemic cardiomyopathy", "Derivative Value Code": "281666001:246090004=399020009", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "famhxsdeath", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Familial syndrome-risk of sudden death", "Derivative Value Code": "100001006", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "hf", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Heart failure", "Derivative Value Code": "84114007", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "oninotsupport", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Inotropic support", "Derivative Value Code": "100001061", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Myocardial infarction", "Derivative Value Code": "22298006", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "paroxysmalsvthistory", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Paroxysmal SVT history", "Derivative Value Code": "67198005", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "structuralabnormalities", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Structural abnormalities", "Derivative Value Code": "100000949", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "syncope", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Syncope", "Derivative Value Code": "271594007", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Syndromes of sudden death", "Derivative Value Code": "100001202", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "primaryvalvularhd", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Valvular heart disease", "Derivative Value Code": "368009", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "vfib", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Ventricular fibrillation (not due to reversible cause)", "Derivative Value Code": "71908006", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "vt", "Source": null, "Transformation": {"Reference Field": "ConditionHxOccurence", "Reference Field Code": 312850006, "Derivative Field": "ConditionHx", "Derivative Field Code": "312850006", "Derivative Value": "Ventricular tachycardia", "Derivative Value Code": "25569003", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "prioravproc", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": 416940007, "Derivative Field": "ProcedHxName", "Derivative Field Code": "416940007", "Derivative Value": "Aortic valve procedure", "Derivative Value Code": "112000001755", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "avpdate", "Source": null, "Transformation": {"Reference Field": "ProcHistDate", "Reference Field Code": 416940007, "Derivative Field": "ProcedHxName", "Derivative Field Code": "416940007", "Derivative Value": "Aortic valve procedure", "Derivative Value Code": "112000001755", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "transplantcandidate", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": 416940007, "Derivative Field": "ProcedHxName", "Derivative Field Code": "416940007", "Derivative Value": "Candidate for transplant", "Derivative Value Code": "100000821", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "vadcandidate", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": 416940007, "Derivative Field": "ProcedHxName", "Derivative Field Code": "416940007", "Derivative Value": "Candidate for VAD", "Derivative Value Code": "112000002045", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "coronaryangiography", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": 416940007, "Derivative Field": "ProcedHxName", "Derivative Field Code": "416940007", "Derivative Value": "Coronary angiography", "Derivative Value Code": "33367005", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priorangiographydate", "Source": null, "Transformation": {"Reference Field": "ProcHistDate", "Reference Field Code": 416940007, "Derivative Field": "ProcedHxName", "Derivative Field Code": "416940007", "Derivative Value": "Coronary angiography", "Derivative Value Code": "33367005", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "currentvad", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": 416940007, "Derivative Field": "ProcedHxName", "Derivative Field Code": "416940007", "Derivative Value": "Currently on VAD", "Derivative Value Code": "112000002046", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priorcied", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": 416940007, "Derivative Field": "ProcedHxName", "Derivative Field Code": "416940007", "Derivative Value": "CV implantable electronic device", "Derivative Value Code": "100000954", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priorcieddate", "Source": null, "Transformation": {"Reference Field": "ProcHistDate", "Reference Field Code": 416940007, "Derivative Field": "ProcedHxName", "Derivative Field Code": "416940007", "Derivative Value": "CV implantable electronic device", "Derivative Value Code": "100000954", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": 416940007, "Derivative Field": "ProcedHxName", "Derivative Field Code": "416940007", "Derivative Value": "On Heart Transplant Waiting List", "Derivative Value Code": "471300007", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priorcabg", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": 416940007, "Derivative Field": "ProcedHxName", "Derivative Field Code": "416940007", "Derivative Value": "Prior coronary artery bypass graft", "Derivative Value Code": "232717009", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priorcabgdate", "Source": null, "Transformation": {"Reference Field": "ProcHistDate", "Reference Field Code": 416940007, "Derivative Field": "ProcedHxName", "Derivative Field Code": "416940007", "Derivative Value": "Prior coronary artery bypass graft", "Derivative Value Code": "232717009", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priorpci", "Source": null, "Transformation": {"Reference Field": "ProcHxOccur", "Reference Field Code": 416940007, "Derivative Field": "ProcedHxName", "Derivative Field Code": "416940007", "Derivative Value": "Prior PCI", "Derivative Value Code": "415070008", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "priorpcidate", "Source": null, "Transformation": {"Reference Field": "ProcHistDate", "Reference Field Code": 416940007, "Derivative Field": "ProcedHxName", "Derivative Field Code": "416940007", "Derivative Value": "Prior PCI", "Derivative Value Code": "415070008", "Value Map": null, "Exact Match": 1, "Case Match": 0, "Regex Match": 0, "Delimiter": null}, "Value Map": null, "Computation": null}, {"Target": "inr", "Source": "inr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "inrnd", "Source": "inrnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preproccreat", "Source": "preproccreat", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "preproccreatnd", "Source": "preproccreatnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "perieffusioninterv", "Source": "perieffusioninterv", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hemothoraxreqdrng", "Source": "hemothoraxreqdrng", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fit_lastname", "Source": "fit_lastname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fit_firstname", "Source": "fit_firstname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fit_midname", "Source": "fit_midname", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fit_npi", "Source": "fit_npi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "procedureentrytime", "Source": "procedureentrytime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "procedurestoptime", "Source": "procedurestoptime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aftpr", "Source": "aftpr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "blcodev", "Source": "blcodev", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vascomint", "Source": "vascomint", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vascominttyp", "Source": "vascominttyp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vascomloc", "Source": "vascomloc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "infiltrastruct", "Source": "infiltrastruct", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "postmarsur", "Source": "postmarsur", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "unipacqrsmorph", "Source": "unipacqrsmorph", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tampinttyp", "Source": "tampinttyp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pneureqint", "Source": "pneureqint", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "finpacedqrsdur", "Source": "finpacedqrsdur", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rwavpeaktimdur", "Source": "rwavpeaktimdur", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pricieddevtyp", "Source": "pricieddevtyp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "elecdevimppath", "Source": "elecdevimppath", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "leftbunlead", "Source": "leftbunlead", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "finpacedqrsdu<PERSON>tass", "Source": "finpacedqrsdu<PERSON>tass", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rwavpeaktimdurnotass", "Source": "rwavpeaktimdurnotass", "Transformation": null, "Value Map": null, "Computation": null}]}}