{"TranslatorConfig": {"Version": "1.4", "Source": {"Dataset": {"Code": "LAAO", "Id": 110}, "Version": "1.4", "Type": "<PERSON><PERSON><PERSON>"}, "Target": {"Dataset": {"Code": "LAAO", "Id": 110}, "Version": "1.4", "Type": "Biome"}, "Translations": [{"Target": "ncdrpatientid", "Source": "ncdrpatientid", "Transformation": null, "Value Map": null}, {"Target": "episodeid", "Source": "episodekey", "Transformation": null, "Value Map": null}, {"Target": "partid", "Source": "partid", "Transformation": null, "Value Map": null}, {"Target": "partname", "Source": "partname", "Transformation": null, "Value Map": null}, {"Target": "timeframe", "Source": "timeframe", "Transformation": null, "Value Map": null}, {"Target": "registryid", "Source": "registryid", "Transformation": null, "Value Map": null}, {"Target": "<PERSON>ver", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "otherid", "Source": "otherid", "Transformation": null, "Value Map": null}, {"Target": "patzip", "Source": "zipcode", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "zipcodena", "Transformation": null, "Value Map": null}, {"Target": "firstname", "Source": "firstname", "Transformation": null, "Value Map": null}, {"Target": "midname", "Source": "midname", "Transformation": null, "Value Map": null}, {"Target": "lastname", "Source": "lastname", "Transformation": null, "Value Map": null}, {"Target": "gender", "Source": "sex", "Transformation": null, "Value Map": null}, {"Target": "dob", "Source": "dob", "Transformation": null, "Value Map": null}, {"Target": "racewhite", "Source": "racewhite", "Transformation": null, "Value Map": null}, {"Target": "raceblack", "Source": "raceblack", "Transformation": null, "Value Map": null}, {"Target": "raceamindian", "Source": "raceamindian", "Transformation": null, "Value Map": null}, {"Target": "raceasianindian", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "racechinese", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "racefilipino", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "racejapanese", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "racekorean", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "racevietnamese", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "raceother", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "racenathaw", "Transformation": null, "Value Map": null}, {"Target": "racenathaw", "Source": "racenathaw", "Transformation": null, "Value Map": null}, {"Target": "raceguamchamo", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "raceotherisland", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "raceasian", "Source": "raceasian", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "racemexicanamchicano", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "racepuertorican", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "racecuban", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "raceotherhispanic", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "arrivaldate", "Source": "arrivaldate", "Transformation": null, "Value Map": null}, {"Target": "mbi", "Source": "mbi", "Transformation": null, "Value Map": null}, {"Target": "healthinsurance", "Source": "healthins", "Transformation": null, "Value Map": null}, {"Target": "insprivate", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Private Health Insurance", "Derivative Value Code": 5, "Value Map": {"|Private Health Insurance|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "hic", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "insmedicare", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Medicare", "Derivative Value Code": 1, "Value Map": {"Medicare": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "<PERSON><PERSON>udy", "Source": "<PERSON><PERSON>udy", "Transformation": null, "Value Map": null}, {"Target": "laaoadmission", "Source": "laao_adm", "Transformation": null, "Value Map": null}, {"Target": "chadchf", "Source": "chadchf", "Transformation": null, "Value Map": null}, {"Target": "nyha", "Source": "nyha", "Transformation": null, "Value Map": null}, {"Target": "chadlvdysf", "Source": "chadlvdysf", "Transformation": null, "Value Map": null}, {"Target": "chad<PERSON><PERSON>cont", "Source": "chad<PERSON><PERSON>cont", "Transformation": null, "Value Map": null}, {"Target": "chaddm", "Source": "chaddm", "Transformation": null, "Value Map": null}, {"Target": "chadstroke", "Source": "chadstroke", "Transformation": null, "Value Map": null}, {"Target": "chad<PERSON>", "Source": "chad<PERSON>", "Transformation": null, "Value Map": null}, {"Target": "chadte", "Source": "chadte", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "hb<PERSON><PERSON>un<PERSON>t", "Source": "hb<PERSON><PERSON>un<PERSON>t", "Transformation": null, "Value Map": null}, {"Target": "hbabnrenal", "Source": "hbabnrenal", "Transformation": null, "Value Map": null}, {"Target": "hbabnliver", "Source": "hbabnliver", "Transformation": null, "Value Map": null}, {"Target": "hbstroke", "Source": "hbstroke", "Transformation": null, "Value Map": null}, {"Target": "hemorrhagicstroke", "Source": null, "Transformation": {"Reference Field": "HBStroke", "Reference Field Code": 100001211, "Derivative Field": "HBStrokeType", "Derivative Field Code": 100001211, "Derivative Value": "Hemorrhagic Stroke", "Derivative Value Code": 230706003, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "ischemicstroke", "Source": null, "Transformation": {"Reference Field": "HBStroke", "Reference Field Code": 100001211, "Derivative Field": "HBStrokeType", "Derivative Field Code": 100001211, "Derivative Value": "Ischemic Stroke", "Derivative Value Code": 422504002, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "undeterminedstroke", "Source": null, "Transformation": {"Reference Field": "HBStroke", "Reference Field Code": 100001211, "Derivative Field": "HBStrokeType", "Derivative Field Code": 100001211, "Derivative Value": "Undetermined Stroke", "Derivative Value Code": 230713003, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "hbbleed", "Source": "hbbleed", "Transformation": null, "Value Map": null}, {"Target": "h<PERSON>binr", "Source": "h<PERSON>binr", "Transformation": null, "Value Map": null}, {"Target": "hbalcohol", "Source": "hbalcohol", "Transformation": null, "Value Map": null}, {"Target": "hbdrugap", "Source": "hbdrugap", "Transformation": null, "Value Map": null}, {"Target": "hbdrugnsaid", "Source": "hbdrugnsaid", "Transformation": null, "Value Map": null}, {"Target": "increasedfallrisk", "Source": "incrfallrisk", "Transformation": null, "Value Map": null}, {"Target": "clinicbleedevent", "Source": "clinicbleedevent", "Transformation": null, "Value Map": null}, {"Target": "geneticcoag", "Source": "geneticcoag", "Transformation": null, "Value Map": null}, {"Target": "conanticoagtherapy", "Source": "conanticoagtx", "Transformation": null, "Value Map": null}, {"Target": "afibflutter", "Source": "a<PERSON><PERSON>d", "Transformation": null, "Value Map": null}, {"Target": "valvularaf", "Source": "valvularaf", "Transformation": null, "Value Map": null}, {"Target": "hxrhvd", "Source": "hxrhvd", "Transformation": null, "Value Map": null}, {"Target": "hxmvreplace", "Source": "hxmvreplace", "Transformation": null, "Value Map": null}, {"Target": "hxmvrepair", "Source": "hxmvrepair", "Transformation": null, "Value Map": null}, {"Target": "mechvalvemitpos", "Source": "mechvalvemitpos", "Transformation": null, "Value Map": null}, {"Target": "afibclass", "Source": "afibclass", "Transformation": null, "Value Map": null}, {"Target": "prevafibterm", "Source": "prevafibterm", "Transformation": null, "Value Map": null}, {"Target": "prevafibtermpc", "Source": "prevafibtermpc", "Transformation": null, "Value Map": null}, {"Target": "prevafltermdc", "Source": "prevafltermdc", "Transformation": null, "Value Map": null}, {"Target": "prevafibtermsa", "Source": "prevafibtermsa", "Transformation": null, "Value Map": null}, {"Target": "afibsurgabldate", "Source": "afibsurgabldate", "Transformation": null, "Value Map": null}, {"Target": "prevafibtermca", "Source": "prevafibtermca", "Transformation": null, "Value Map": null}, {"Target": "afibcathabldate", "Source": "afibcathabldate", "Transformation": null, "Value Map": null}, {"Target": "aflutter", "Source": "aflutter", "Transformation": null, "Value Map": null}, {"Target": "afluttertype", "Source": "afluttertype", "Transformation": null, "Value Map": null}, {"Target": "prevaflterm", "Source": "prevaflterm", "Transformation": null, "Value Map": null}, {"Target": "prevafltermpc", "Source": "prevafltermpc", "Transformation": null, "Value Map": null}, {"Target": "prevafltermca", "Source": "prevafltermca", "Transformation": null, "Value Map": null}, {"Target": "afibfluttercathabldate", "Source": "afibfluttercathabldate", "Transformation": null, "Value Map": null}, {"Target": "strucintervention", "Source": "cardstrucinterv", "Transformation": null, "Value Map": null}, {"Target": "laaintervention", "Source": "la<PERSON>interv", "Transformation": null, "Value Map": null}, {"Target": "priorcm", "Source": "cm", "Transformation": null, "Value Map": null}, {"Target": "chroniclungdisease", "Source": "chroniclungdisease", "Transformation": null, "Value Map": null}, {"Target": "cad", "Source": "cad", "Transformation": null, "Value Map": null}, {"Target": "sleep<PERSON><PERSON>", "Source": "sleep<PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "sleepapnearxfollowed", "Source": "sleepapnearxfollowed", "Transformation": null, "Value Map": null}, {"Target": "epicardialapproach", "Source": "epicardialappcons", "Transformation": null, "Value Map": null}, {"Target": "epicardiacsurgery", "Source": null, "Transformation": {"Reference Field": "EpicardialAppCons", "Reference Field Code": 112000002078, "Derivative Field": "MedCond", "Derivative Field Code": 362965005, "Derivative Value": "Cardiac Surgery", "Derivative Value Code": 64915003, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "epipericarditis", "Source": null, "Transformation": {"Reference Field": "EpicardialAppCons", "Reference Field Code": 112000002078, "Derivative Field": "MedCond", "Derivative Field Code": 362965005, "Derivative Value": "<PERSON><PERSON><PERSON><PERSON>", "Derivative Value Code": 3238004, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "epiautoimmunedisease", "Source": null, "Transformation": {"Reference Field": "EpicardialAppCons", "Reference Field Code": 112000002078, "Derivative Field": "MedCond", "Derivative Field Code": 362965005, "Derivative Value": "Autoimmune Disease", "Derivative Value Code": 85828009, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "epithorradtherapy", "Source": null, "Transformation": {"Reference Field": "EpicardialAppCons", "Reference Field Code": 112000002078, "Derivative Field": "MedCond", "Derivative Field Code": 362965005, "Derivative Value": "Thoracic Radiation Therapy", "Derivative Value Code": 112000002090, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "epipectusexcavatum", "Source": null, "Transformation": {"Reference Field": "EpicardialAppCons", "Reference Field Code": 112000002078, "Derivative Field": "MedCond", "Derivative Field Code": 362965005, "Derivative Value": "<PERSON><PERSON><PERSON> Excavatum", "Derivative Value Code": 391987005, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "epiepigastricsurg", "Source": null, "Transformation": {"Reference Field": "EpicardialAppCons", "Reference Field Code": 112000002078, "Derivative Field": "MedCond", "Derivative Field Code": 362965005, "Derivative Value": "Epigastric Surgery", "Derivative Value Code": 112000002091, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "epihepatomegaly", "Source": null, "Transformation": {"Reference Field": "EpicardialAppCons", "Reference Field Code": 112000002078, "Derivative Field": "MedCond", "Derivative Field Code": 362965005, "Derivative Value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Derivative Value Code": 80515008, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "epihiatalhernia", "Source": null, "Transformation": {"Reference Field": "EpicardialAppCons", "Reference Field Code": 112000002078, "Derivative Field": "MedCond", "Derivative Field Code": 362965005, "Derivative Value": "Hiatal Hernia", "Derivative Value Code": 84089009, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "atrialrhythmap", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": 106068003, "Derivative Value": "Atrial paced", "Derivative Value Code": 251268003, "Value Map": {"Atrial paced": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "lvefassessed", "Source": "lvefassessed", "Transformation": null, "Value Map": null}, {"Target": "lvef", "Source": "lvef", "Transformation": null, "Value Map": null}, {"Target": "tteperf", "Source": "tteperf", "Transformation": null, "Value Map": null}, {"Target": "ttedate", "Source": "ttedate", "Transformation": null, "Value Map": null}, {"Target": "baselineimagingperf", "Source": "baselineimagingperf", "Transformation": null, "Value Map": null}, {"Target": "ctperformed", "Source": "ctperformed", "Transformation": null, "Value Map": null}, {"Target": "ctimagingdate", "Source": "ctimagingdate", "Transformation": null, "Value Map": null}, {"Target": "mrperformed", "Source": "mrperformed", "Transformation": null, "Value Map": null}, {"Target": "mrdate", "Source": "mrdate", "Transformation": null, "Value Map": null}, {"Target": "ieperformed", "Source": "iceperf", "Transformation": null, "Value Map": null}, {"Target": "icedate", "Source": "icedate", "Transformation": null, "Value Map": null}, {"Target": "height", "Source": "height", "Transformation": null, "Value Map": null}, {"Target": "weight", "Source": "weight", "Transformation": null, "Value Map": null}, {"Target": "pulse", "Source": "pulse", "Transformation": null, "Value Map": null}, {"Target": "systolicbp", "Source": "systolicbp", "Transformation": null, "Value Map": null}, {"Target": "diastolicbp", "Source": "diastolicbp", "Transformation": null, "Value Map": null}, {"Target": "hgb", "Source": "hgb", "Transformation": null, "Value Map": null}, {"Target": "hgbnd", "Source": "hgbnd", "Transformation": null, "Value Map": null}, {"Target": "pt", "Source": "pt", "Transformation": null, "Value Map": null}, {"Target": "ptnd", "Source": "ptnd", "Transformation": null, "Value Map": null}, {"Target": "inr", "Source": "inr", "Transformation": null, "Value Map": null}, {"Target": "inrnd", "Source": "inrnd", "Transformation": null, "Value Map": null}, {"Target": "preproccreat", "Source": "preproccreat", "Transformation": null, "Value Map": null}, {"Target": "preproccreatnd", "Source": "preproccreatnd", "Transformation": null, "Value Map": null}, {"Target": "totalbumin", "Source": "albumin", "Transformation": null, "Value Map": null}, {"Target": "totalbuminnd", "Source": "albumin_nd", "Transformation": null, "Value Map": null}, {"Target": "pltcount", "Source": "plateletct", "Transformation": null, "Value Map": null}, {"Target": "pltcountnd", "Source": "plateletctnd", "Transformation": null, "Value Map": null}, {"Target": "rankinscale", "Source": "rankinscale", "Transformation": null, "Value Map": null}, {"Target": "rankinscalena", "Source": "postproc_rankinscalena", "Transformation": null, "Value Map": null}, {"Target": "medid_fondaparinux", "Source": null, "Transformation": {"Reference Field": "PreMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": 100013057, "Derivative Value": "Fondaparinux", "Derivative Value Code": "321208", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_heparin_derivative", "Source": null, "Transformation": {"Reference Field": "PreMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": 100013057, "Derivative Value": "Heparin Derivative", "Derivative Value Code": "100000921", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_low_molecular_wt_heparin", "Source": null, "Transformation": {"Reference Field": "PreMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": 100013057, "Derivative Value": "Low Molecular Weight Heparin", "Derivative Value Code": "373294004", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_unfractionated_heparin", "Source": null, "Transformation": {"Reference Field": "PreMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": 100013057, "Derivative Value": "Unfractionated Heparin", "Derivative Value Code": "96382006", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_warfain", "Source": null, "Transformation": {"Reference Field": "PreMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": 100013057, "Derivative Value": "Warfarin", "Derivative Value Code": "11289", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_aspirin_81_100_mg", "Source": null, "Transformation": {"Reference Field": "PreMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": 100013057, "Derivative Value": "Aspirin 81 to 100 mg", "Derivative Value Code": "112000002080", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_aspirin_101_324mg", "Source": null, "Transformation": {"Reference Field": "PreMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": 100013057, "Derivative Value": "Aspirin 101 to 324 mg", "Derivative Value Code": "112000002081", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_aspirin_325mg", "Source": null, "Transformation": {"Reference Field": "PreMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": 100013057, "Derivative Value": "Aspirin 325 mg", "Derivative Value Code": "317300", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_aspirin_dipyridamole", "Source": null, "Transformation": {"Reference Field": "PreMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": 100013057, "Derivative Value": "Aspirin/Dipyridamole", "Derivative Value Code": "226716", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_vorapaxar", "Source": null, "Transformation": {"Reference Field": "PreMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": 100013057, "Derivative Value": "Vorapaxar", "Derivative Value Code": "1537034", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_apixaban", "Source": null, "Transformation": {"Reference Field": "PreMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": 100013057, "Derivative Value": "Apixaban", "Derivative Value Code": "1364430", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_dabigatran", "Source": null, "Transformation": {"Reference Field": "PreMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": 100013057, "Derivative Value": "Dabigatran", "Derivative Value Code": "1546356", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_edoxaban", "Source": null, "Transformation": {"Reference Field": "PreMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": 100013057, "Derivative Value": "Edoxaban", "Derivative Value Code": "1599538", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_rivaroxaban", "Source": null, "Transformation": {"Reference Field": "PreMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": 100013057, "Derivative Value": "Rivaroxaban", "Derivative Value Code": "1114195", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_cangrelor", "Source": null, "Transformation": {"Reference Field": "PreMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON><PERSON>", "Derivative Value Code": "1656052", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_clopidogrel", "Source": null, "Transformation": {"Reference Field": "PreMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": 100013057, "Derivative Value": "Clopidogrel", "Derivative Value Code": "32968", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_other_p2y12_inhibitor", "Source": null, "Transformation": {"Reference Field": "PreMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": 100013057, "Derivative Value": "Other P2Y12", "Derivative Value Code": "112000001003", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_prasugrel", "Source": null, "Transformation": {"Reference Field": "PreMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": 100013057, "Derivative Value": "Prasug<PERSON>", "Derivative Value Code": "613391", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_ticagrelor", "Source": null, "Transformation": {"Reference Field": "PreMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": 100013057, "Derivative Value": "Ticagrelor", "Derivative Value Code": "1116632", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "medid_ticlopidine", "Source": null, "Transformation": {"Reference Field": "PreMedAdmin", "Reference Field Code": 432102000, "Derivative Field": "MedID", "Derivative Field Code": 100013057, "Derivative Value": "Ticlopidine", "Derivative Value Code": "10594", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "bleedeventtypeintracranial", "Source": null, "Transformation": {"Reference Field": "ClinicBleedEvent", "Reference Field Code": 112000002066, "Derivative Field": "BleedEventType", "Derivative Field Code": *********, "Derivative Value": "Intracranial Bleed", "Derivative Value Code": 1386000, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "pulmonaryveinisolation", "Source": null, "Transformation": {"Reference Field": "PrevAFibTermCA", "Reference Field Code": "*********:*********=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:*********=49436004", "Derivative Value": "Pulmonary Vein Isolation", "Derivative Value Code": 100000915, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "atrialrhythmsinusnode", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": 106068003, "Derivative Value": "Sinus node rhythm", "Derivative Value Code": 106067008, "Value Map": {"Sinus node rhythm": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "bleedeventtypegastrointestinal", "Source": null, "Transformation": {"Reference Field": "ClinicBleedEvent", "Reference Field Code": 112000002066, "Derivative Field": "BleedEventType", "Derivative Field Code": *********, "Derivative Value": "Gastrointestinal Bleed", "Derivative Value Code": 74474003, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "surgicalligation", "Source": null, "Transformation": {"Reference Field": "LAAOInterv", "Reference Field Code": "112000002070", "Derivative Field": "LAAOType", "Derivative Field Code": "112000002070", "Derivative Value": "Surgical Ligation", "Derivative Value Code": 112000002074, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "cad_1", "Source": null, "Transformation": {"Reference Field": "ChadVascDis", "Reference Field Code": *********, "Derivative Field": "PriorVD", "Derivative Field Code": 27550009, "Derivative Value": "Coronary Artery Disease (CAD)*", "Derivative Value Code": 53741008, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "pci", "Source": null, "Transformation": {"Reference Field": "ChadVascDis", "Reference Field Code": *********, "Derivative Field": "PriorVD", "Derivative Field Code": 27550009, "Derivative Value": "Percutaneous Coronary Intervention (PCI)*", "Derivative Value Code": 415070008, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "atrialrhythmafib", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": 106068003, "Derivative Value": "Atrial fibrillation", "Derivative Value Code": 49436004, "Value Map": {"Atrial fibrillation": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "insstate", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "State-Specific Plan (non-Medicaid)", "Derivative Value Code": 36, "Value Map": {"|State-Specific Plan (non-Medicaid)|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "nonischemiccardiomyopathy", "Source": null, "Transformation": {"Reference Field": "CM", "Reference Field Code": 85898001, "Derivative Field": "PriorCMType", "Derivative Field Code": 100000953, "Derivative Value": "Non-ischemic cardiomyopathy", "Derivative Value Code": 111000119104, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "cabg", "Source": null, "Transformation": {"Reference Field": "ChadVascDis", "Reference Field Code": *********, "Derivative Field": "PriorVD", "Derivative Field Code": 27550009, "Derivative Value": "Coronary Artery Bypass Graft (CABG)*", "Derivative Value Code": 232717009, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "strucintervention_tavr", "Source": null, "Transformation": {"Reference Field": "CardStrucInterv", "Reference Field Code": "112000002068", "Derivative Field": "CardStrucIntervType", "Derivative Field Code": "112000002068", "Derivative Value": "Transcatheter Aortic Valve Replacement (TAVR)", "Derivative Value Code": "41873006", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "strucintervention_tmvr", "Source": null, "Transformation": {"Reference Field": "CardStrucInterv", "Reference Field Code": "112000002068", "Derivative Field": "CardStrucIntervType", "Derivative Field Code": "112000002068", "Derivative Value": " Transcatheter Mitral Valve Repair (TMVR)", "Derivative Value Code": "112000001801", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "carotiddis<PERSON>", "Source": null, "Transformation": {"Reference Field": "ChadVascDis", "Reference Field Code": *********, "Derivative Field": "PriorVD", "Derivative Field Code": 27550009, "Derivative Value": "Carotid Artery Disease*", "Derivative Value Code": 371160000, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "atrialrhythmaflutter", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": 106068003, "Derivative Value": "Atrial flutter", "Derivative Value Code": 5370000, "Value Map": {"Atrial flutter": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "atrialrhythmundocdatrial", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": 106068003, "Derivative Value": "Not Documented", "Derivative Value Code": 100001116, "Value Map": {"Not Documented": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "empiriclalinearlesions", "Source": null, "Transformation": {"Reference Field": "PrevAFibTermCA", "Reference Field Code": "*********:*********=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:*********=49436004", "Derivative Value": "Empiric LA Linear Lesions", "Derivative Value Code": 100000912, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "bleedeventtypeother", "Source": null, "Transformation": {"Reference Field": "ClinicBleedEvent", "Reference Field Code": 112000002066, "Derivative Field": "BleedEventType", "Derivative Field Code": *********, "Derivative Value": "Other", "Derivative Value Code": 1000142371, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "hypertrophiccardiomyopathy", "Source": null, "Transformation": {"Reference Field": "CM", "Reference Field Code": 85898001, "Derivative Field": "PriorCMType", "Derivative Field Code": 100000953, "Derivative Value": "Hypertrophic cardiomyopathy", "Derivative Value Code": 233873004, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "ischemiccardiomyopathy", "Source": null, "Transformation": {"Reference Field": "CM", "Reference Field Code": 85898001, "Derivative Field": "PriorCMType", "Derivative Field Code": 100000953, "Derivative Value": "Ischemic cardiomyopathy", "Derivative Value Code": 426856002, "Value Map": null, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "wideareacircumferentialablation", "Source": null, "Transformation": {"Reference Field": "PrevAFibTermCA", "Reference Field Code": "*********:*********=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:*********=49436004", "Derivative Value": "Wide Area Circumferential Ablation", "Derivative Value Code": 100000918, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "strucintervention_mitralvalvereplacementsurgical", "Source": null, "Transformation": {"Reference Field": "CardStrucInterv", "Reference Field Code": "112000002068", "Derivative Field": "CardStrucIntervType", "Derivative Field Code": "112000002068", "Derivative Value": "MV Replacement - Surgical", "Derivative Value Code": "53059001", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "<PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "ChadVascDis", "Reference Field Code": *********, "Derivative Field": "PriorVD", "Derivative Field Code": 27550009, "Derivative Value": "Prior Myocardial Infarction (MI)", "Derivative Value Code": 399211009, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "proct<PERSON><PERSON><PERSON>", "Source": "teeperflaao", "Transformation": null, "Value Map": null}, {"Target": "procteedate", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "procatrialthromdetect", "Source": "procatrialthromdetect", "Transformation": null, "Value Map": null}, {"Target": "laaorificemaxwidth", "Source": "<PERSON><PERSON>_orwid", "Transformation": null, "Value Map": null}, {"Target": "procedurestartdate", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "procedurestopdate", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "procedurestoptime", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "proclocation", "Source": "procedurelocation", "Transformation": null, "Value Map": null}, {"Target": "opera_lastname", "Source": "opera_lastname2", "Transformation": null, "Value Map": null}, {"Target": "opera_lastname", "Source": "opera_lastname2", "Transformation": null, "Value Map": null}, {"Target": "opera_firstname", "Source": "opera_firstname2", "Transformation": null, "Value Map": null}, {"Target": "opera_npi", "Source": "opera_npi2", "Transformation": null, "Value Map": null}, {"Target": "opera_npi", "Source": "opera_npi2", "Transformation": null, "Value Map": null}, {"Target": "anesthesia", "Source": "anesthesia", "Transformation": null, "Value Map": null}, {"Target": "proccanceled", "Source": "proccanceled", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "procaborted", "Transformation": null, "Value Map": null}, {"Target": "residualleak", "Source": "residualleak", "Transformation": null, "Value Map": null}, {"Target": "residualleakna", "Source": "residualleakna", "Transformation": null, "Value Map": null}, {"Target": "concomitantprocperf", "Source": "concomitantprocperf", "Transformation": null, "Value Map": null}, {"Target": "highfallrisk", "Source": null, "Transformation": {"Reference Field": "ProcLAAOInd", "Reference Field Code": 100001024, "Derivative Field": "ProcLAAOInd", "Derivative Field Code": 112000000482, "Derivative Value": "High fall risk", "Derivative Value Code": 112000000398, "Value Map": {"High fall risk": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "increasedthromboembolicstrokerisk", "Source": null, "Transformation": {"Reference Field": "ProcLAAOInd", "Reference Field Code": 100001024, "Derivative Field": "ProcLAAOInd", "Derivative Field Code": 112000000482, "Derivative Value": "Increased thromboembolic stroke risk", "Derivative Value Code": 112000002106, "Value Map": {"Increased thromboembolic stroke risk": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "fluoroscopy", "Source": null, "Transformation": {"Reference Field": "GuidanceMethodID", "Reference Field Code": 100000919, "Derivative Field": "GuidanceMethodID", "Derivative Field Code": 100000919, "Derivative Value": "Fluoroscopy", "Derivative Value Code": 44491008, "Value Map": {"Fluoroscopy": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "transesophagealechocardiography", "Source": null, "Transformation": {"Reference Field": "GuidanceMethodID", "Reference Field Code": 100000919, "Derivative Field": "GuidanceMethodID", "Derivative Field Code": 100000919, "Derivative Value": "Transesophageal Echocardiogram (TEE)", "Derivative Value Code": 105376000, "Value Map": {"Transesophageal Echocardiogram (TEE)": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "ohsconversion", "Source": "ohsconversion", "Transformation": null, "Value Map": null}, {"Target": "contrastvol", "Source": "contrastvol", "Transformation": null, "Value Map": null}, {"Target": "fluorodosedap", "Source": "fluorodosedap2", "Transformation": null, "Value Map": null}, {"Target": "fluorodosekerm", "Source": "fluorodosekerm", "Transformation": null, "Value Map": null}, {"Target": "intraprocanticoag", "Source": "intraprocanticoag", "Transformation": null, "Value Map": null}, {"Target": "procwarfarin", "Source": "warfarin", "Transformation": null, "Value Map": null}, {"Target": "procheparin", "Source": "procheparin2", "Transformation": null, "Value Map": null}, {"Target": "procheparininitadmin", "Source": "procheparininitadmintime", "Transformation": null, "Value Map": null}, {"Target": "pro<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "procbivalirudin2", "Transformation": null, "Value Map": null}, {"Target": "procotheranticoag", "Source": "procotheranticoag2", "Transformation": null, "Value Map": null}, {"Target": "anticoagreversal", "Source": "anticoagreversal", "Transformation": null, "Value Map": null}, {"Target": "postprocpeakcreat", "Source": "postprocpeakcreat", "Transformation": null, "Value Map": null}, {"Target": "postprocpeakcreatnd", "Source": "postprocpeakcreatnd", "Transformation": null, "Value Map": null}, {"Target": "postprochgb", "Source": "postprochgb2", "Transformation": null, "Value Map": null}, {"Target": "postprochgbnd", "Source": "postprochgbnd2", "Transformation": null, "Value Map": null}, {"Target": "postp<PERSON>cc<PERSON>t", "Source": "postproccreat2", "Transformation": null, "Value Map": null}, {"Target": "postproccreatnd", "Source": "postproccreatnd2", "Transformation": null, "Value Map": null}, {"Target": "historyofmajorbleed", "Source": null, "Transformation": {"Reference Field": "ProcLAAOInd", "Reference Field Code": 100001024, "Derivative Field": "ProcLAAOInd", "Derivative Field Code": 112000000482, "Derivative Value": "History of major bleed", "Derivative Value Code": 112000002105, "Value Map": {"History of major bleed": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "highbleedingrisk", "Source": null, "Transformation": {"Reference Field": "ProcLAAOInd", "Reference Field Code": 100001024, "Derivative Field": "ProcLAAOInd", "Derivative Field Code": 112000000482, "Derivative Value": "Clinically significant bleeding risk (Other than those listed here)", "Derivative Value Code": 711536002, "Value Map": {"Clinically significant bleeding risk (Other than those listed here)": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "abortedcatheterizationchallenge", "Source": null, "Transformation": {"Reference Field": "ProcAborted", "Reference Field Code": "112000000515", "Derivative Field": "ProcAbortedReason", "Derivative Field Code": "112000000504", "Derivative Value": "Catherization challenge", "Derivative Value Code": "112000002094", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "abortedunanticipatedpatientcondition", "Source": null, "Transformation": {"Reference Field": "ProcAborted", "Reference Field Code": "112000000515", "Derivative Field": "ProcAbortedReason", "Derivative Field Code": "112000000504", "Derivative Value": "Unanticipated patient condition", "Derivative Value Code": "112000002099", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "accesssyscounter", "Source": "accesssyscounter", "Transformation": null, "Value Map": null}, {"Target": "accesssysid", "Source": "accesssysid", "Transformation": null, "Value Map": null}, {"Target": "devcounter", "Source": "devcounter", "Transformation": null, "Value Map": null}, {"Target": "la<PERSON><PERSON>", "Source": "la<PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "laaisolationapproach", "Source": "laaisolationapproach", "Transformation": null, "Value Map": null}, {"Target": "deviceoutcome", "Source": "outdevunsucdepl", "Transformation": null, "Value Map": null}, {"Target": "dev_udidirectid", "Source": "dev_udidirectid", "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "eventid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "eventname", "Source": "procevents", "Transformation": null, "Value Map": null}, {"Target": "eventoccurred", "Source": "postprococcurred", "Transformation": null, "Value Map": null}, {"Target": "eventdate", "Source": "intrapostproceventdate", "Transformation": null, "Value Map": null}, {"Target": "dischargedate", "Source": "dcdate", "Transformation": null, "Value Map": null}, {"Target": "eocsurgery", "Source": "sx_f", "Transformation": null, "Value Map": null}, {"Target": "p<PERSON><PERSON>", "Source": "p<PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "dischargestatus", "Source": "dcstatus", "Transformation": null, "Value Map": null}, {"Target": "dischargelocation", "Source": "dclocation", "Transformation": null, "Value Map": null}, {"Target": "dchospice", "Source": "dchospice", "Transformation": null, "Value Map": null}, {"Target": "deathprocedure", "Source": "deathprocedure", "Transformation": null, "Value Map": null}, {"Target": "deathcause", "Source": "deathcause", "Transformation": null, "Value Map": null}, {"Target": "dc_medid_fond<PERSON><PERSON>ux", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Fondaparinux", "Derivative Value Code": "321208", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_medid_heparin_derivative", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Heparin Derivative", "Derivative Value Code": "100000921", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_medid_low_molecular_wt_heparin", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Low Molecular Weight Heparin", "Derivative Value Code": "373294004", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_medid_unfractionated_heparin", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Unfractionated Heparin", "Derivative Value Code": "96382006", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_medid_warfain", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Warfarin", "Derivative Value Code": "11289", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_medid_dose", "Source": "dc_meddose", "Transformation": {"Reference Field": "DC_MedDose", "Reference Field Code": 100014233, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON>", "Derivative Value Code": 1191, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_medid_aspirin", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON>", "Derivative Value Code": "1191", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_medid_aspirin_dipyridamole", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Aspirin/Dipyridamole", "Derivative Value Code": "226716", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_medid_vorapaxar", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Vorapaxar", "Derivative Value Code": "1537034", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_medid_apixaban", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Apixaban", "Derivative Value Code": "1364430", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_medid_dabigatran", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Dabigatran", "Derivative Value Code": "1546356", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_medid_edoxaban", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Edoxaban", "Derivative Value Code": "1599538", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_medid_rivaroxaban", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Rivaroxaban", "Derivative Value Code": "1114195", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_medid_cangrelor", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON><PERSON>", "Derivative Value Code": "1656052", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_medid_clopidogrel", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Clopidogrel", "Derivative Value Code": "32968", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_medid_other_p2y12_inhibitor", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Other P2Y12", "Derivative Value Code": "112000001003", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_medid_prasugrel", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Prasug<PERSON>", "Derivative Value Code": "613391", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_medid_ticagrelor", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Ticagrelor", "Derivative Value Code": "1116632", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "dc_medid_ticlopidine", "Source": null, "Transformation": {"Reference Field": "DC_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "DC_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Ticlopidine", "Derivative Value Code": "10594", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "originalotherid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "hospname", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "filename", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "version", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "datasetname", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "clientfileid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "biomeimportdt", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "careentityid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "tenantid", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "yearmonth", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "casesequencenumber", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "age", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "distfromhospital", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "insmilitary", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Military Health Care", "Derivative Value Code": 31, "Value Map": {"|Military Health Care|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "insihs", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Indian Health Service", "Derivative Value Code": 33, "Value Map": {"|Indian Health Service|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "insmedicaid", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Medicaid", "Derivative Value Code": 2, "Value Map": {"|Medicaid|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "<PERSON><PERSON>us", "Source": null, "Transformation": {"Reference Field": "HIPS", "Reference Field Code": 100001072, "Derivative Field": "HIPS", "Derivative Field Code": 100001072, "Derivative Value": "Non-US Insurance", "Derivative Value Code": 100000812, "Value Map": {"|Non-US Insurance|": "Yes"}, "Exact Match": 0, "Delimiter": "|"}, "Value Map": null}, {"Target": "priorpad", "Source": null, "Transformation": {"Reference Field": "ChadVascDis", "Reference Field Code": *********, "Derivative Field": "PriorVD", "Derivative Field Code": 27550009, "Derivative Value": "Peripheral Arterial Occlusive Disease (PAD)", "Derivative Value Code": 399957001, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "studyname", "Source": "studyname", "Transformation": null, "Value Map": null}, {"Target": "patientrestriction", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "atrialrhythmsinusarrest", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": 106068003, "Derivative Value": "Sinus arrest", "Derivative Value Code": 5609005, "Value Map": {"Sinus arrest": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "atrialrhythmatrialtach", "Source": null, "Transformation": {"Reference Field": "AtrialRhythm", "Reference Field Code": 106068003, "Derivative Field": "AtrialRhythm", "Derivative Field Code": 106068003, "Derivative Value": "Atrial tachycardia", "Derivative Value Code": 276796006, "Value Map": {"Atrial tachycardia": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "warfarin", "Source": "warfarin", "Transformation": null, "Value Map": null}, {"Target": "reasonforreimplantation", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "aj_deathdate", "Source": "adj_deathdate", "Transformation": null, "Value Map": null}, {"Target": "aj_neurodef", "Source": "adj_neurodeficit", "Transformation": null, "Value Map": null}, {"Target": "aj_neuroimag", "Source": "adj_neurobrainimaging", "Transformation": null, "Value Map": null}, {"Target": "aj_neurosxduration", "Source": "adj_neurosxduration", "Transformation": null, "Value Map": null}, {"Target": "fluorodosedapunit", "Source": "fluorodosedap2_unit", "Transformation": null, "Value Map": null}, {"Target": "studyname2", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "studyname3", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "studyname4", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "studyname5", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "studyptid", "Source": "studyptid", "Transformation": null, "Value Map": null}, {"Target": "studyptid2", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "studyptid3", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "studyptid4", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "studyptid5", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "myocardialinfarction", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "raceasianother", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "hispethnicitymexican", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "hispethnicitypuertorico", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "hispethnicitycuban", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "hispethnicityotherorigin", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "aj_adjudevent", "Source": "aj_adjudevent", "Transformation": null, "Value Map": null}, {"Target": "aj_eventdate", "Source": "aj_eventdate", "Transformation": null, "Value Map": null}, {"Target": "adj_neuroadj<PERSON><PERSON>", "Source": "adj_neuroadj<PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "adj_neurosxonset", "Source": "adj_neurosxonset", "Transformation": null, "Value Map": null}, {"Target": "adj_neuroclinicpresent", "Source": "adj_neuroclinicpresent", "Transformation": null, "Value Map": null}, {"Target": "adj_neurodxconfirmed", "Source": "adj_neurodxconfirmed", "Transformation": null, "Value Map": null}, {"Target": "adj_neuroivrtpa", "Source": "adj_neuroivrtpa", "Transformation": null, "Value Map": null}, {"Target": "adj_neuroendotherainter", "Source": "adj_neuroendotherainter", "Transformation": null, "Value Map": null}, {"Target": "adj_neurotrauma", "Source": "adj_neurotrauma", "Transformation": null, "Value Map": null}, {"Target": "adj_rankinscale", "Source": "adj_rankinscale", "Transformation": null, "Value Map": null}, {"Target": "adj_rankins<PERSON>na", "Source": "adj_rankins<PERSON>na", "Transformation": null, "Value Map": null}, {"Target": "adj_neuroprocrelated", "Source": "adj_neuroprocrelated", "Transformation": null, "Value Map": null}, {"Target": "adj_neurodevrelated", "Source": "adj_neurodevrelated", "Transformation": null, "Value Map": null}, {"Target": "adj_<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "adj_<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "adj_bleeddeathdate", "Source": "adj_bleeddeathdate", "Transformation": null, "Value Map": null}, {"Target": "adj_bleedin<PERSON>ter", "Source": "adj_bleedin<PERSON>ter", "Transformation": null, "Value Map": null}, {"Target": "adj_bleedrbctransfusion", "Source": "adj_bleedrbctransfusion", "Transformation": null, "Value Map": null}, {"Target": "adj_<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "adj_<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "adj_bleedpretranshgb", "Source": "adj_bleedpretranshgb", "Transformation": null, "Value Map": null}, {"Target": "adj_bleedimageperf", "Source": "adj_bleedimageperf", "Transformation": null, "Value Map": null}, {"Target": "adj_bleedendorgandamage", "Source": "adj_bleedendorgandamage", "Transformation": null, "Value Map": null}, {"Target": "adj_bleedmajorsurgery", "Source": "adj_bleedmajorsurgery", "Transformation": null, "Value Map": null}, {"Target": "adj_<PERSON><PERSON><PERSON>", "Source": "adj_<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null}, {"Target": "adj_bleedprocrelated", "Source": "adj_bleedprocrelated", "Transformation": null, "Value Map": {"Certain": "1", "Probable": "2", "Possible": "3", "Unlikely": "4", "Unclassifiable": "5"}}, {"Target": "adj_<PERSON><PERSON><PERSON><PERSON>", "Source": "adj_<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": {"Certain": "1", "Probable": "2", "Possible": "3", "Unlikely": "4", "Unclassifiable": "5"}}, {"Target": "adj_systhromboadj<PERSON>tus", "Source": "adj_systhromboadj<PERSON>tus", "Transformation": null, "Value Map": null}, {"Target": "adj_systhrombodeathdate", "Source": "adj_systhrombodeathdate", "Transformation": null, "Value Map": null}, {"Target": "adj_systhrombodeathcause", "Source": "adj_systhrombodeathcause", "Transformation": null, "Value Map": null}, {"Target": "adj_systhrombohypoperfusion", "Source": "adj_systhrombohypoperfusion", "Transformation": null, "Value Map": null}, {"Target": "prevafibtermdc", "Source": "prevafibtermdc", "Transformation": null, "Value Map": null}, {"Target": "epiaccess", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "epia<PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "knownaorticplaque", "Source": null, "Transformation": {"Reference Field": "ChadVascDis", "Reference Field Code": *********, "Derivative Field": "PriorVD", "Derivative Field Code": 27550009, "Derivative Value": "Known Aortic Plaque", "Derivative Value Code": "1522000|15825003", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "bleedeventtypeepistaxis", "Source": null, "Transformation": {"Reference Field": "ClinicBleedEvent", "Reference Field Code": 112000002066, "Derivative Field": "BleedEventType", "Derivative Field Code": *********, "Derivative Value": "Epistaxis", "Derivative Value Code": *********, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "complexfractionatedatrialelectrogram", "Source": null, "Transformation": {"Reference Field": "PrevAFibTermCA", "Reference Field Code": "*********:*********=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:*********=49436004", "Derivative Value": "Complex Fractionated Atrial Electrogram", "Derivative Value Code": *********, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "cryoablation", "Source": null, "Transformation": {"Reference Field": "PrevAFibTermCA", "Reference Field Code": "*********:*********=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:*********=49436004", "Derivative Value": "Cryoablation", "Derivative Value Code": *********, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "convergentprocedure", "Source": null, "Transformation": {"Reference Field": "PrevAFibTermCA", "Reference Field Code": "*********:*********=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:*********=49436004", "Derivative Value": "Convergent Procedure", "Derivative Value Code": 100000911, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "focalablation", "Source": null, "Transformation": {"Reference Field": "PrevAFibTermCA", "Reference Field Code": "*********:*********=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:*********=49436004", "Derivative Value": "Focal Ablation", "Derivative Value Code": 100000913, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "ganglionplexusablation", "Source": null, "Transformation": {"Reference Field": "PrevAFibTermCA", "Reference Field Code": "*********:*********=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:*********=49436004", "Derivative Value": "Ganglion Plexus Ablation", "Derivative Value Code": 100000914, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "segmentalpvablation", "Source": null, "Transformation": {"Reference Field": "PrevAFibTermCA", "Reference Field Code": "*********:*********=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:*********=49436004", "Derivative Value": "Segmental PV Ablation", "Derivative Value Code": 100000916, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "rotorbasedmapping", "Source": null, "Transformation": {"Reference Field": "PrevAFibTermCA", "Reference Field Code": "*********:*********=49436004", "Derivative Field": "AFibPriorAblStrategyCode", "Derivative Field Code": "18286008:*********=49436004", "Derivative Value": "Rotor Based Mapping", "Derivative Value Code": 100000917, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "strucintervention_aorticballoonvalvuloplasty", "Source": null, "Transformation": {"Reference Field": "CardStrucInterv", "Reference Field Code": "112000002068", "Derivative Field": "CardStrucIntervType", "Derivative Field Code": "112000002068", "Derivative Value": "Aortic Balloon Valvuloplasty", "Derivative Value Code": "77166000", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "strucintervention_aorticvalvereplacementsurgical", "Source": null, "Transformation": {"Reference Field": "CardStrucInterv", "Reference Field Code": "112000002068", "Derivative Field": "CardStrucIntervType", "Derivative Field Code": "112000002068", "Derivative Value": "AV Replacement - Surgical", "Derivative Value Code": "725351001", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "strucintervention_aorticvalverepairsurgical", "Source": null, "Transformation": {"Reference Field": "CardStrucInterv", "Reference Field Code": "112000002068", "Derivative Field": "CardStrucIntervType", "Derivative Field Code": "112000002068", "Derivative Value": "AV Repair - Surgical", "Derivative Value Code": "112816004", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "strucintervention_mitralballoonvalvuloplasty", "Source": null, "Transformation": {"Reference Field": "CardStrucInterv", "Reference Field Code": "112000002068", "Derivative Field": "CardStrucIntervType", "Derivative Field Code": "112000002068", "Derivative Value": "Mitral Balloon Valvuloplasty", "Derivative Value Code": "112000001951", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "strucintervention_mitralvalverepairsurgical", "Source": null, "Transformation": {"Reference Field": "CardStrucInterv", "Reference Field Code": "112000002068", "Derivative Field": "CardStrucIntervType", "Derivative Field Code": "112000002068", "Derivative Value": "MV Repair - Surgical", "Derivative Value Code": "384641003", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "strucintervention_mitralannuloplastyringsurgical", "Source": null, "Transformation": {"Reference Field": "CardStrucInterv", "Reference Field Code": "112000002068", "Derivative Field": "CardStrucIntervType", "Derivative Field Code": "112000002068", "Derivative Value": "Mitral Annuloplasty Ring - Surgical", "Derivative Value Code": "232744004", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "strucintervention_mitraltranscathetervalveinvalve", "Source": null, "Transformation": {"Reference Field": "CardStrucInterv", "Reference Field Code": "112000002068", "Derivative Field": "CardStrucIntervType", "Derivative Field Code": "112000002068", "Derivative Value": "Mitral Transcatheter - Valve-in-valve", "Derivative Value Code": "112000002069", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "strucintervention_atrialseptaldefectclosure", "Source": null, "Transformation": {"Reference Field": "CardStrucInterv", "Reference Field Code": "112000002068", "Derivative Field": "CardStrucIntervType", "Derivative Field Code": "112000002068", "Derivative Value": "ASD Closure", "Derivative Value Code": "112811009", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "strucintervention_patentforamenovaleclosure", "Source": null, "Transformation": {"Reference Field": "CardStrucInterv", "Reference Field Code": "112000002068", "Derivative Field": "CardStrucIntervType", "Derivative Field Code": "112000002068", "Derivative Value": "PFO Closure", "Derivative Value Code": "41817002", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "strucintervention_pulmonicreplacement", "Source": null, "Transformation": {"Reference Field": "CardStrucInterv", "Reference Field Code": "112000002068", "Derivative Field": "CardStrucIntervType", "Derivative Field Code": "112000002068", "Derivative Value": "Pulmonic Replacement", "Derivative Value Code": "88045004", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "strucintervention_pulmonicrepair", "Source": null, "Transformation": {"Reference Field": "CardStrucInterv", "Reference Field Code": "112000002068", "Derivative Field": "CardStrucIntervType", "Derivative Field Code": "112000002068", "Derivative Value": "Pulmonic Repair", "Derivative Value Code": "386749005", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "strucintervention_tricuspidreplacement", "Source": null, "Transformation": {"Reference Field": "CardStrucInterv", "Reference Field Code": "112000002068", "Derivative Field": "CardStrucIntervType", "Derivative Field Code": "112000002068", "Derivative Value": "Tricuspid Replacement", "Derivative Value Code": "25236004", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "strucintervention_tricuspidrepair", "Source": null, "Transformation": {"Reference Field": "CardStrucInterv", "Reference Field Code": "112000002068", "Derivative Field": "CardStrucIntervType", "Derivative Field Code": "112000002068", "Derivative Value": "Tricuspid Repair", "Derivative Value Code": "384643000", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "epicardialligation", "Source": null, "Transformation": {"Reference Field": "LAAOInterv", "Reference Field Code": "112000002070", "Derivative Field": "LAAOType", "Derivative Field Code": "112000002070", "Derivative Value": "Epicardial Ligation", "Derivative Value Code": 112000002072, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "percutaneousocclusion", "Source": null, "Transformation": {"Reference Field": "LAAOInterv", "Reference Field Code": "112000002070", "Derivative Field": "LAAOType", "Derivative Field Code": "112000002070", "Derivative Value": "Percutaneous Occlusion", "Derivative Value Code": 112000002076, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "surgicalamputation", "Source": null, "Transformation": {"Reference Field": "LAAOInterv", "Reference Field Code": "112000002070", "Derivative Field": "LAAOType", "Derivative Field Code": "112000002070", "Derivative Value": "Surgical Amputation", "Derivative Value Code": 112000002073, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "laainterventiontypesurgicalclosuredevice", "Source": null, "Transformation": {"Reference Field": "LAAOInterv", "Reference Field Code": "112000002070", "Derivative Field": "LAAOType", "Derivative Field Code": 112000002070, "Derivative Value": "Surgical Closure Device", "Derivative Value Code": "112000002077", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "surgicalstapling", "Source": null, "Transformation": {"Reference Field": "LAAOInterv", "Reference Field Code": "112000002070", "Derivative Field": "LAAOType", "Derivative Field Code": "112000002070", "Derivative Value": "Surgical Stapling", "Derivative Value Code": 112000002075, "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "restrictivecardiomyopathy", "Source": null, "Transformation": {"Reference Field": "CM", "Reference Field Code": 85898001, "Derivative Field": "PriorCMType", "Derivative Field Code": 100000953, "Derivative Value": "Restrictive cardiomyopathy", "Derivative Value Code": 415295002, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "othercardiomyopathytype", "Source": null, "Transformation": {"Reference Field": "CM", "Reference Field Code": 85898001, "Derivative Field": "PriorCMType", "Derivative Field Code": 100000953, "Derivative Value": "Other cardiomyopathy type", "Derivative Value Code": 100001065, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "proceventoccurred", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "dc_medid_aspirin_81mg", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "medid_aspirin_81mg", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "medid_aggrenox", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "medid_durlaza", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "labileinr", "Source": null, "Transformation": {"Reference Field": "ProcLAAOInd", "Reference Field Code": 100001024, "Derivative Field": "ProcLAAOInd", "Derivative Field Code": 112000000482, "Derivative Value": "Labile INR", "Derivative Value Code": 100001024, "Value Map": {"Labile INR": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "patientpreference", "Source": null, "Transformation": {"Reference Field": "ProcLAAOInd", "Reference Field Code": 100001024, "Derivative Field": "ProcLAAOInd", "Derivative Field Code": 112000000482, "Derivative Value": "Patient preference", "Derivative Value Code": 112000002107, "Value Map": {"Patient preference": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "noncompliancewithanticoagulationtherapy", "Source": null, "Transformation": {"Reference Field": "ProcLAAOInd", "Reference Field Code": 112000000482, "Derivative Field": "ProcLAAOInd", "Derivative Field Code": 112000000482, "Derivative Value": "Non-compliance with anticoagulation therapy", "Derivative Value Code": 112000002108, "Value Map": {"Non-compliance with anticoagulation therapy": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "canceledanatomynotconduciveforimplant", "Source": null, "Transformation": {"Reference Field": "ProcCanceled", "Reference Field Code": "112000002120", "Derivative Field": "ProcCanceledReason", "Derivative Field Code": "112000002102", "Derivative Value": "Anatomy not conducive for implant", "Derivative Value Code": "112000002093", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "canceledappendagetoolargefordeviceimplant", "Source": null, "Transformation": {"Reference Field": "ProcCanceled", "Reference Field Code": "112000002120", "Derivative Field": "ProcCanceledReason", "Derivative Field Code": "112000002102", "Derivative Value": "Appendage too large (for device implant)", "Derivative Value Code": "112000002096", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "canceledappendagetoosmallfordeviceimplant", "Source": null, "Transformation": {"Reference Field": "ProcCanceled", "Reference Field Code": "112000002120", "Derivative Field": "ProcCanceledReason", "Derivative Field Code": "112000002102", "Derivative Value": "Appendage too small (for device implant)", "Derivative Value Code": "112000002097", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "canceledcatheterizationchallenge", "Source": null, "Transformation": {"Reference Field": "ProcCanceled", "Reference Field Code": "112000002120", "Derivative Field": "ProcCanceledReason", "Derivative Field Code": "112000002102", "Derivative Value": "Catherization challenge", "Derivative Value Code": "112000002094", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "canceleddecompensationinpatientcondition", "Source": null, "Transformation": {"Reference Field": "ProcCanceled", "Reference Field Code": "112000002120", "Derivative Field": "ProcCanceledReason", "Derivative Field Code": "112000002102", "Derivative Value": "Decompensation in patient condition", "Derivative Value Code": "112000002098", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "canceledepicardialaccessissue", "Source": null, "Transformation": {"Reference Field": "ProcCanceled", "Reference Field Code": "112000002120", "Derivative Field": "ProcCanceledReason", "Derivative Field Code": "112000002102", "Derivative Value": "Epicardial access issue", "Derivative Value Code": "112000002100", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "canceledthrombusdetected", "Source": null, "Transformation": {"Reference Field": "ProcCanceled", "Reference Field Code": "112000002120", "Derivative Field": "ProcCanceledReason", "Derivative Field Code": "112000002102", "Derivative Value": "<PERSON><PERSON><PERSON><PERSON> detected", "Derivative Value Code": "112000002095", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "canceledunanticipatedpatientcondition", "Source": null, "Transformation": {"Reference Field": "ProcCanceled", "Reference Field Code": "112000002120", "Derivative Field": "ProcCanceledReason", "Derivative Field Code": "112000002102", "Derivative Value": "Unanticipated patient condition", "Derivative Value Code": "112000002099", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "canceledpatientfamilychoice", "Source": null, "Transformation": {"Reference Field": "ProcCanceled", "Reference Field Code": "112000002120", "Derivative Field": "ProcCanceledReason", "Derivative Field Code": "112000002102", "Derivative Value": " Patient/Family choice", "Derivative Value Code": "112000002101", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "abortedanatomynotconduciveforimplant", "Source": null, "Transformation": {"Reference Field": "ProcAborted", "Reference Field Code": "112000000515", "Derivative Field": "ProcAbortedReason", "Derivative Field Code": "112000000504", "Derivative Value": "Anatomy not conducive for implant", "Derivative Value Code": "112000002093", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "abortedappendagetoolargefordeviceimplant", "Source": null, "Transformation": {"Reference Field": "ProcAborted", "Reference Field Code": "112000000515", "Derivative Field": "ProcAbortedReason", "Derivative Field Code": "112000000504", "Derivative Value": "Appendage too large (for device implant)", "Derivative Value Code": "112000002096", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "abortedappendagetoosmallfordeviceimplant", "Source": null, "Transformation": {"Reference Field": "ProcAborted", "Reference Field Code": "112000000515", "Derivative Field": "ProcAbortedReason", "Derivative Field Code": "112000000504", "Derivative Value": "Appendage too small (for device implant)", "Derivative Value Code": "112000002097", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "aborteddecompensationinpatientcondition", "Source": null, "Transformation": {"Reference Field": "ProcAborted", "Reference Field Code": "112000000515", "Derivative Field": "ProcAbortedReason", "Derivative Field Code": "112000000504", "Derivative Value": "Decompensation in patient condition", "Derivative Value Code": "112000002098", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "aborteddevicerelated", "Source": null, "Transformation": {"Reference Field": "ProcAborted", "Reference Field Code": "112000000515", "Derivative Field": "ProcAbortedReason", "Derivative Field Code": "112000000504", "Derivative Value": " Device related", "Derivative Value Code": "112000001828", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "aborted<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": {"Reference Field": "ProcAborted", "Reference Field Code": "112000000515", "Derivative Field": "ProcAbortedReason", "Derivative Field Code": "112000000504", "Derivative Value": "Transcatheter device retrieval", "Derivative Value Code": "112000002124", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "aborteddevicereleasecriterianotmet", "Source": null, "Transformation": {"Reference Field": "ProcAborted", "Reference Field Code": "112000000515", "Derivative Field": "ProcAbortedReason", "Derivative Field Code": "112000000504", "Derivative Value": "Device release criteria not met", "Derivative Value Code": "112000002104", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "abortedepicardialaccessissue", "Source": null, "Transformation": {"Reference Field": "ProcAborted", "Reference Field Code": "112000000515", "Derivative Field": "ProcAbortedReason", "Derivative Field Code": "112000000504", "Derivative Value": "Epicardial access issue", "Derivative Value Code": "112000002100", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "abortedopenheartdeviceretrieval", "Source": null, "Transformation": {"Reference Field": "ProcAborted", "Reference Field Code": "112000000515", "Derivative Field": "ProcAbortedReason", "Derivative Field Code": "112000000504", "Derivative Value": "Surgical device retrieval", "Derivative Value Code": "112000001838", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "abortedthrombusdetected", "Source": null, "Transformation": {"Reference Field": "ProcAborted", "Reference Field Code": "112000000515", "Derivative Field": "ProcAbortedReason", "Derivative Field Code": "112000000504", "Derivative Value": "Device associated thrombus developed during procedure", "Derivative Value Code": "112000002103", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "abortedpatientfamilychoice", "Source": null, "Transformation": {"Reference Field": "ProcAborted", "Reference Field Code": "112000000515", "Derivative Field": "ProcAbortedReason", "Derivative Field Code": "112000000504", "Derivative Value": "Patient/Family choice", "Derivative Value Code": "112000002101", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "intracardiacthreedimensionalechocardiography", "Source": null, "Transformation": {"Reference Field": "GuidanceMethodID", "Reference Field Code": 100000919, "Derivative Field": "GuidanceMethodID", "Derivative Field Code": 100000919, "Derivative Value": "Intracardiac three dimensional echocardiography", "Derivative Value Code": 448761005, "Value Map": {"Intracardiac three dimensional echocardiography": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "electroanatomicmapping", "Source": null, "Transformation": {"Reference Field": "GuidanceMethodID", "Reference Field Code": 100000919, "Derivative Field": "GuidanceMethodID", "Derivative Field Code": 100000919, "Derivative Value": "Electro Anatomic Mapping", "Derivative Value Code": 100000908, "Value Map": {"Electro Anatomic Mapping": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "concomtype_afibablation", "Source": null, "Transformation": {"Reference Field": "ConcomitantProcPerf", "Reference Field Code": "100001271", "Derivative Field": "ConcomitantProcType", "Derivative Field Code": "100013075", "Derivative Value": "AFib Ablation", "Derivative Value Code": "18286008:*********=49436004", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "concomtype_icd", "Source": null, "Transformation": {"Reference Field": "ConcomitantProcPerf", "Reference Field Code": "100001271", "Derivative Field": "ConcomitantProcType", "Derivative Field Code": "100013075", "Derivative Value": "ICD", "Derivative Value Code": "ACC-NCDR-ICD", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "concomtype_pci", "Source": null, "Transformation": {"Reference Field": "ConcomitantProcPerf", "Reference Field Code": "100001271", "Derivative Field": "ConcomitantProcType", "Derivative Field Code": "100013075", "Derivative Value": "PCI", "Derivative Value Code": "415070008", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "concomtype_tavr", "Source": null, "Transformation": {"Reference Field": "ConcomitantProcPerf", "Reference Field Code": "100001271", "Derivative Field": "ConcomitantProcType", "Derivative Field Code": "100013075", "Derivative Value": "TAVR", "Derivative Value Code": "441873006", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "concomtype_tmvr", "Source": null, "Transformation": {"Reference Field": "ConcomitantProcPerf", "Reference Field Code": "100001271", "Derivative Field": "ConcomitantProcType", "Derivative Field Code": "100013075", "Derivative Value": "TMVR", "Derivative Value Code": "112000001801", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "concomtype_asdclosurecongenital", "Source": null, "Transformation": {"Reference Field": "ConcomitantProcPerf", "Reference Field Code": "100001271", "Derivative Field": "ConcomitantProcType", "Derivative Field Code": "100013075", "Derivative Value": "ASD Closure Congenital", "Derivative Value Code": "112811009", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "concomtype_asdclosureiatrogenic", "Source": null, "Transformation": {"Reference Field": "ConcomitantProcPerf", "Reference Field Code": "100001271", "Derivative Field": "ConcomitantProcType", "Derivative Field Code": "100013075", "Derivative Value": "ASD Closure Iatrogenic", "Derivative Value Code": "112000001885", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "concomtype_pfoclosurecongenital", "Source": null, "Transformation": {"Reference Field": "ConcomitantProcPerf", "Reference Field Code": "100001271", "Derivative Field": "ConcomitantProcType", "Derivative Field Code": "100013075", "Derivative Value": "PFO Closure Congenital", "Derivative Value Code": "41817002", "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "dc_medid_aspirin_325mg", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "dc_medid_aggrenox", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "dc_medid_durlaza", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "adj_neurodeficittype", "Source": "adj_neurodeficittype", "Transformation": null, "Value Map": null}, {"Target": "hemorrhage_intracerebral", "Source": null, "Transformation": {"Reference Field": "ADJ_NeuroIntracranType", "Reference Field Code": 230706003, "Derivative Field": "ADJ_NeuroIntracranType", "Derivative Field Code": 230706003, "Derivative Value": "Intracerebral", "Derivative Value Code": "274100004", "Value Map": {"Intracerebral": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "hemorrhage_subarachnoid", "Source": null, "Transformation": {"Reference Field": "ADJ_NeuroIntracranType", "Reference Field Code": 230706003, "Derivative Field": "ADJ_NeuroIntracranType", "Derivative Field Code": 230706003, "Derivative Value": "Subarachnoid", "Derivative Value Code": "21454007", "Value Map": {"Subarachnoid": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "hemorrhage_subdural", "Source": null, "Transformation": {"Reference Field": "ADJ_NeuroIntracranType", "Reference Field Code": 230706003, "Derivative Field": "ADJ_NeuroIntracranType", "Derivative Field Code": 230706003, "Derivative Value": "Subdural", "Derivative Value Code": "35486000", "Value Map": {"Subdural": "Yes"}, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "adj_systhromboimagevidence", "Source": "adj_systhromboimagevidence", "Transformation": null, "Value Map": null}, {"Target": "imagingmethod_angiography", "Source": null, "Transformation": {"Reference Field": "ADJ_SysThromboImagEvidence", "Reference Field Code": 365853002, "Derivative Field": "ADJ_SysThromboImagMethod", "Derivative Field Code": 112000000960, "Derivative Value": "Angiography", "Derivative Value Code": 77343006, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "imagingmethod_ctscan", "Source": null, "Transformation": {"Reference Field": "ADJ_SysThromboImagEvidence", "Reference Field Code": 365853002, "Derivative Field": "ADJ_SysThromboImagMethod", "Derivative Field Code": 112000000960, "Derivative Value": "Computed Tomography", "Derivative Value Code": 77477000, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "imagingmethod_mri", "Source": null, "Transformation": {"Reference Field": "ADJ_SysThromboImagEvidence", "Reference Field Code": 365853002, "Derivative Field": "ADJ_SysThromboImagMethod", "Derivative Field Code": 112000000960, "Derivative Value": "Magnetic Resonance Imaging", "Derivative Value Code": 113091000, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "imagingmethod_ultrasound", "Source": null, "Transformation": {"Reference Field": "ADJ_SysThromboImagEvidence", "Reference Field Code": 365853002, "Derivative Field": "ADJ_SysThromboImagMethod", "Derivative Field Code": 112000000960, "Derivative Value": "Ultrasound", "Derivative Value Code": 112000001042, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "imagingmethod_other", "Source": null, "Transformation": {"Reference Field": "ADJ_SysThromboImagEvidence", "Reference Field Code": 365853002, "Derivative Field": "ADJ_SysThromboImagMethod", "Derivative Field Code": 112000000960, "Derivative Value": "Other Imaging", "Derivative Value Code": 112000001862, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "adj_systhrom<PERSON><PERSON><PERSON>terv", "Source": "adj_systhrom<PERSON><PERSON><PERSON>terv", "Transformation": null, "Value Map": null}, {"Target": "therainterv_catheter", "Source": null, "Transformation": {"Reference Field": "ADJ_SysThromboTheraInterv", "Reference Field Code": 100013063, "Derivative Field": "ADJ_SysThromboIntervType", "Derivative Field Code": 100013063, "Derivative Value": "Catheter", "Derivative Value Code": 276272002, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "therainterv_pharmacological", "Source": null, "Transformation": {"Reference Field": "ADJ_SysThromboTheraInterv", "Reference Field Code": 100013063, "Derivative Field": "ADJ_SysThromboIntervType", "Derivative Field Code": 100013063, "Derivative Value": "Pharmacological", "Derivative Value Code": 182832007, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "therainterv_surgical", "Source": null, "Transformation": {"Reference Field": "ADJ_SysThromboTheraInterv", "Reference Field Code": 100013063, "Derivative Field": "ADJ_SysThromboIntervType", "Derivative Field Code": 100013063, "Derivative Value": "Surgical", "Derivative Value Code": 387713003, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "therainterv_other", "Source": null, "Transformation": {"Reference Field": "ADJ_SysThromboTheraInterv", "Reference Field Code": 100013063, "Derivative Field": "ADJ_SysThromboIntervType", "Derivative Field Code": 100013063, "Derivative Value": "Other", "Derivative Value Code": 112000000172, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "opera_midname", "Source": "opera_midname2", "Transformation": null, "Value Map": null}, {"Target": "fluorodosekerm_units", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "ohscon<PERSON><PERSON>son", "Source": "ohscon<PERSON><PERSON>son", "Transformation": null, "Value Map": null}, {"Target": "adj_medid_aspirin_81mg", "Source": null, "Transformation": {"Reference Field": "ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Aspirin 81 to 100 mg", "Derivative Value Code": "96382006", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_aspirin_325mg", "Source": null, "Transformation": {"Reference Field": "ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Aspirin 325 mg", "Derivative Value Code": "373294004", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_vorapaxar", "Source": null, "Transformation": {"Reference Field": "ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Vorapaxar", "Derivative Value Code": "1116632", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_clopidogrel", "Source": null, "Transformation": {"Reference Field": "ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Clopidogrel", "Derivative Value Code": "112000002081", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_prasugrel", "Source": null, "Transformation": {"Reference Field": "ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Prasug<PERSON>", "Derivative Value Code": "1114195", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_ticlopidine", "Source": null, "Transformation": {"Reference Field": "ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Ticlopidine", "Derivative Value Code": "112000001003", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_ticagrelor", "Source": null, "Transformation": {"Reference Field": "ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Ticagrelor", "Derivative Value Code": "32968", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_warfain", "Source": null, "Transformation": {"Reference Field": "ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Warfarin", "Derivative Value Code": "10594", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_apixaban", "Source": null, "Transformation": {"Reference Field": "ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Apixaban", "Derivative Value Code": "321208", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_dabigatran", "Source": null, "Transformation": {"Reference Field": "ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Dabigatran", "Derivative Value Code": "317300", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_rivaroxaban", "Source": null, "Transformation": {"Reference Field": "ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Rivaroxaban", "Derivative Value Code": "1656052", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_ed<PERSON><PERSON>n", "Source": null, "Transformation": {"Reference Field": "ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Edoxaban", "Derivative Value Code": "226716", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_unfractionated_heparin", "Source": null, "Transformation": {"Reference Field": "ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Unfractionated Heparin", "Derivative Value Code": "613391", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_fond<PERSON><PERSON>ux", "Source": null, "Transformation": {"Reference Field": "ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Fondaparinux", "Derivative Value Code": "1537034", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_low_molecular_wt_heparin", "Source": null, "Transformation": {"Reference Field": "ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Low Molecular Weight Heparin", "Derivative Value Code": "1546356", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_heparin_derivative", "Source": null, "Transformation": {"Reference Field": "ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Heparin Derivative", "Derivative Value Code": "1364430", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_aggrenox", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "adj_medid_durlaza", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "postprocmedstrategy_aspirin_81_100mg", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "postprocmedstrategy_aspirin_101_324mg", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "postprocmedstrategy_aspirin_325mg", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "doac", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "p2y12", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "reasonforfdalabeldosingregimen", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "reasonforhemorrhagiccomp", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "reasonfornonhemorrhagiccomp", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "reasonforknowndrugresistance", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "reasonfordrugintolerence", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "reasonforcost", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "reasonforpatientpreference", "Source": null, "Transformation": null, "Value Map": null}, {"Target": "adj_medid_aspirin_101_324mg", "Source": null, "Transformation": {"Reference Field": "ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Aspirin 101 to 324 mg", "Derivative Value Code": "100000921", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_aspirin_81_100_mg", "Source": null, "Transformation": {"Reference Field": "ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Aspirin 81 to 100 mg", "Derivative Value Code": "96382006", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_aspirin_dipyridamole", "Source": null, "Transformation": {"Reference Field": "ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Aspirin/Dipyridamole", "Derivative Value Code": "11289", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_cangrelor", "Source": null, "Transformation": {"Reference Field": "ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "<PERSON><PERSON><PERSON><PERSON>", "Derivative Value Code": "112000002080", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "adj_medid_other_p2y12_inhibitor", "Source": null, "Transformation": {"Reference Field": "ADJ_MedAdmin", "Reference Field Code": 432102000, "Derivative Field": "ADJ_MedID", "Derivative Field Code": 100013057, "Derivative Value": "Other P2Y12", "Derivative Value Code": "1599538", "Value Map": null, "Exact Match": 1, "Delimiter": null}, "Value Map": null}, {"Target": "imagingtype_angiography", "Source": null, "Transformation": {"Reference Field": "ADJ_NeuroBrainImaging", "Reference Field Code": 441986001, "Derivative Field": "ADJ_NeuroBrainImagingType", "Derivative Field Code": 441986001, "Derivative Value": "Cerebral Angiography", "Derivative Value Code": 3258003, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "imagingtype_ctscan", "Source": null, "Transformation": {"Reference Field": "ADJ_NeuroBrainImaging", "Reference Field Code": 441986001, "Derivative Field": "ADJ_NeuroBrainImagingType", "Derivative Field Code": 441986001, "Derivative Value": "Computed Tomography", "Derivative Value Code": 77477000, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "imagingtype_mri", "Source": null, "Transformation": {"Reference Field": "ADJ_NeuroBrainImaging", "Reference Field Code": 441986001, "Derivative Field": "ADJ_NeuroBrainImagingType", "Derivative Field Code": 441986001, "Derivative Value": "Magnetic Resonance Imaging", "Derivative Value Code": 113091000, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "imagingtype_other", "Source": null, "Transformation": {"Reference Field": "ADJ_NeuroBrainImaging", "Reference Field Code": 441986001, "Derivative Field": "ADJ_NeuroBrainImagingType", "Derivative Field Code": 441986001, "Derivative Value": "Other", "Derivative Value Code": 112000001862, "Value Map": null, "Exact Match": 0, "Delimiter": null}, "Value Map": null}, {"Target": "biomeencounterid", "Source": null, "Transformation": null, "Value Map": null}]}}