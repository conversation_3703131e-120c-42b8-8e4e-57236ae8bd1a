{"TranslatorConfig": {"Version": "1.0", "Source": {"Dataset": {"Code": "Stroke", "Id": 125}, "Version": "1", "Type": "<PERSON><PERSON><PERSON>"}, "Target": {"Dataset": {"Code": "Stroke", "Id": 125}, "Version": "1.0", "Type": "Biome"}, "Translations": [{"Target": "version", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "filename", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "i10dschstkdx_description", "Source": "i10dschstkdx-description", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "i10prindx_description", "Source": "i10prindx-description", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "iacomp", "Source": "iacomp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "initialexamfindings1", "Source": "initialexam", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fluvaccine", "Source": "fluvacc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "accesscase", "Source": "access case", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "me<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "me<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rankindschscale", "Source": "rankindschscale", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "prestrokemrsscore", "Source": "prestrokemrsscore", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "reastransfer", "Source": "reastransfer", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "telestrokeconsultation", "Source": "telestrokeconsult", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ichetiologydoc", "Source": "ichetiologydoc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "admitsource", "Source": "admitsource", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "relexclusion2", "Source": "relexclusion2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "relexclusion", "Source": "relexclusion", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": {"Advanced Stroke care (non-time critical therapy)": "Advanced stroke care (e.g., Neurocritical care, surgical or other time critical therapy)", "Post Management of IV Thrombolytics (e.g. Drip and Ship)": "Post Management of IV alteplase (e.g. Drip and Ship)", "Evaluation for IV Thrombolytics up to 4.5 hours": "Evaluation for IV alteplase up to 4.5 hours"}, "Computation": null}, {"Target": "rehabsvcs", "Source": "rehabsvcs", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_prestrokemrs", "Source": "prestrokemrs", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "merreasons", "Source": "merreasons", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gs_rsdeltra", "Source": "spec<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "i<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "ivtpadelay_mr", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "replace", "Args": {"old_values": ";", "new_values": ":"}, "is_computation": null}, {"Transform_Type": "replace", "Args": {"old_values": "medications,Management", "new_values": "medications:Management"}, "is_computation": null}, {"Transform_Type": "replace", "Args": {"old_values": "disorders,Management", "new_values": "disorders:Management"}, "is_computation": null}, {"Transform_Type": "replace", "Args": {"old_values": "disorders,Need", "new_values": "disorders:Need"}, "is_computation": null}]}, {"Target": "prevmedhistory", "Source": "medhist", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "replace", "Args": {"old_values": ";", "new_values": ":"}, "is_computation": null}]}, {"Target": "exclusionlessthan3hrc5", "Source": "exclusion", "Transformation": null, "Value Map": {"C5: Acute bleeding diathesis (low platelet count, increased PTT, INR >= 1.7 or use of NOAC)": "Yes", "1": "Yes"}, "Computation": null}, {"Target": "exclusion2", "Source": "exclusion2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "onsetcomments", "Source": "onsetcomments", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherdiagicd10", "Source": "i10otherdx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "i10otherdx_description", "Source": "i10otherdx-description", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceother", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "inr", "Source": "inr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "priorhf", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "status", "Source": "form_status", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "racewhite", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gender", "Source": "sex", "Transformation": null, "Value Map": {"1": "Male", "2": "Female", "M - Male": "Male", "F - Female": "Female", "U - Unknown": "Unknown"}, "Computation": null}, {"Target": "insnone", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "insprivate", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "insmedicaid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "insmedicare", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "height", "Source": "height", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "weight", "Source": "weight", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceotherhispanic", "Source": "his<PERSON><PERSON>", "Transformation": null, "Value Map": {"1": "Yes", "2": "No/UTD"}, "Computation": null}, {"Target": "systolicbp", "Source": "systolic", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "diastolicbp", "Source": "diastolic", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "creatinine", "Source": "scr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "crea<PERSON><PERSON>d", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "clinicaltrial", "Source": "clinicaltrial", "Transformation": null, "Value Map": {"1": "Yes", "2": "No"}, "Computation": null}, {"Target": "clinicaltrial", "Source": "clinicaltrial", "Transformation": null, "Value Map": {"1": "Yes", "2": "No"}, "Computation": null}, {"Target": "priorstroke", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "bmi", "Source": "bmi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceamericanindianalaskannative", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceblackafricanamerican", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lipidsldl", "Source": "ldl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dischatrialrhythmafib", "Source": "afibdischarge", "Transformation": null, "Value Map": {"1": "Yes", "2": "No/ND", "3": "NC"}, "Computation": null}, {"Target": "electivecarotidinterventionadmin", "Source": "electivecarotid", "Transformation": null, "Value Map": {"1": "Yes", "2": "No"}, "Computation": null}, {"Target": "locationcare", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "Emergency Department/Urgent Care", "false_val": null, "fields": [{"name": "admitsource", "values": ["Emergency Room"]}]}, "is_computation": null}]}, {"Target": "prehospems", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rankindischargeperformed", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": {"1": "Yes", "2": "No/ND"}, "Computation": null}, {"Target": "rankindischargescore", "Source": "rankindschtotal", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "initialnihss", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "nihsstotalscore", "Source": "nihssscore", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "insnd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "priorcarotidstenosis", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "priorpvd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pmhnone", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "resolutionofstrokesymptomsonpresentation", "Source": "symptresolve", "Transformation": null, "Value Map": {"1": "Yes", "2": "No", "3": "ND"}, "Computation": null}, {"Target": "nomedpriortoadmission", "Source": "nopriormeds", "Transformation": null, "Value Map": {"1": "Checked", "True": "Checked"}, "Computation": null}, {"Target": "antiplateletpreadmission", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "anticoagpreadmission", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "antihypertensivepreadmission", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cholesterolreducerpreadmission", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "diabeticmedpreadmission", "Source": "dmmedprior", "Transformation": null, "Value Map": {"1": "Yes", "2": "No/ND"}, "Computation": null}, {"Target": "ivtwarncontra3hrs", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivtpacontra4p5hrs", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivthrombolyisstarted", "Source": "ivthroinit", "Transformation": null, "Value Map": {"1": "Yes", "2": "No"}, "Computation": null}, {"Target": "ivtpaoutside", "Source": "ivtpaoutside", "Transformation": null, "Value Map": {"1": "Yes", "2": "No"}, "Computation": null}, {"Target": "comfortonlydocumenttiming", "Source": "comfortonly", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "icd10diagnosis", "Source": "i10prindx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ptnpothroughoutstay", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dysphagia", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "propylaxis2ndday", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vteinter<PERSON>uh", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vteinterventionlmwh", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vteinterventionipc", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vteinterventiongcs", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vteinterventionfactorxainhib", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vteinterventionwarfarin", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vteinterventionoralfactorxainhib", "Source": "vteproporalfactorxa", "Transformation": null, "Value Map": {"1": "Checked", "True": "Checked"}, "Computation": null}, {"Target": "otheranticoagdabigatranpradaxa", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otheranticoagargatroban", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otheranticoagdesirudiniprivask", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otheranticoagrivaroxabanxarelto", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otheranticoagapixabaneliquis", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otheranticoaglepirudinrefludan", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "docreasonoralfactorxa", "Source": "reason<PERSON><PERSON>", "Transformation": null, "Value Map": {"1": "Yes", "2": "No"}, "Computation": null}, {"Target": "docnovteadmission", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "antiplateletadmday2", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lipidsnd", "Source": "lipidsnd", "Transformation": null, "Value Map": {"1": "Checked", "True": "Checked"}, "Computation": null}, {"Target": "nodcantithrom", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": {"1": "Yes", "2": "No"}, "Computation": null}, {"Target": "why<PERSON>at<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "whynoatrialmedsmentalstat", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dischargeantihyper", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "antihyperarb", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "antihyperdiuretics", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "antihyperother", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cholreducstatintype", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cholreducstatindose", "Source": "statindose", "Transformation": null, "Value Map": {"100": "20/500 mg", "101": "20/750 mg", "102": "20/1000 mg", "103": "40/1000 mg", "105": "Unknown", "150": "20 mg", "151": "40 mg", "152": "60 mg", "154": "Unknown", "200": "2.5/10 mg", "201": "2.5/20 mg", "202": "2.5/40 mg", "203": "5/10 mg", "204": "5/20 mg", "205": "5/40 mg", "206": "5/80 mg", "207": "10/10 mg", "208": "10/20 mg", "209": "10/40 mg", "210": "10/80 mg", "212": "Unknown", "300": "5 mg", "301": "10 mg", "302": ">= 20 mg", "304": "Unknown", "400": "20 mg", "401": "40 mg", "402": "80 mg", "404": "Unknown", "500": "10 mg", "501": "20 mg", "502": ">= 40 mg", "504": "Unknown", "650": "1 mg", "651": "2 mg", "652": "4 mg", "656": "Unknown", "704": "10 mg", "700": "20 mg", "701": "40 mg", "703": "Unknown", "800": "10 mg", "801": "20 mg", "802": "40 mg", "803": "80 mg", "805": "Unknown", "900": "20/500 mg", "901": "20/750 mg", "902": "20/1000 mg", "903": "40/500 mg", "904": "40/1000 mg", "905": "40/2000 mg (2 x 20/1000 mg)", "907": "Unknown", "1000": "10/10 mg", "1001": "10/20 mg", "1002": "10/40 mg", "1003": "10/80 mg", "1005": "Unknown", "1100": "5 mg", "1101": "10 mg", "1102": "20 mg", "1103": "40 mg", "1104": "80 mg", "1106": "Unknown"}, "Computation": null}, {"Target": "cholreduchighstatintx", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "eduantismokingtx", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "edutlcdient", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "edusodiumdiet", "Source": "sodiumdiet", "Transformation": null, "Value Map": {"1": "Yes", "2": "No/ND", "3": "NC"}, "Computation": null}, {"Target": "edudiabetesteaching", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "edust<PERSON><PERSON><PERSON>", "Source": "edustrk", "Transformation": null, "Value Map": {"1": "Yes", "2": "No"}, "Computation": null}, {"Target": "eduusingems", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": "eduems"}, "is_computation": true}]}, {"Target": "edumeds", "Source": "edudismed", "Transformation": null, "Value Map": {"1": "Yes", "2": "No"}, "Computation": null}, {"Target": "edustrokewarningsigns", "Source": "eduwarning", "Transformation": null, "Value Map": {"1": "Yes", "2": "No"}, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": {"1": "Yes", "2": "No"}, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "assessrehab", "Transformation": null, "Value Map": {"1": "Yes", "2": "No"}, "Computation": null}, {"Target": "otherhealthcaredestin", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "firstimageinterpretation", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cpmcunit", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "procedureiatpa", "Source": "iatpa", "Transformation": null, "Value Map": {"1": "Yes", "2": "No"}, "Computation": null}, {"Target": "iatpaoutsidehosp", "Source": "iatpaoutside", "Transformation": null, "Value Map": {"1": "Yes", "2": "No"}, "Computation": null}, {"Target": "yearmonth", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cryptogenicstroke", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "implantcrmonitrng", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "intracranvascularimaging", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "carotid<PERSON><PERSON><PERSON>l", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cust_optional11", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cust_optional12", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dyn_stroke", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dyn_stroke_optional", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "formdata_seq", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "additionalrelativeexclusion", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "addiotinalwarnings", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "transfertohospital", "Source": "transferhosp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "transferfromhospitalnotdoc", "Source": "transfernd", "Transformation": null, "Value Map": {"1": "Checked"}, "Computation": null}, {"Target": "transferfromhospitalnotlisted", "Source": "transfernl", "Transformation": null, "Value Map": {"1": "Checked"}, "Computation": null}, {"Target": "transfertohospitalnotdoc", "Source": "transferhospnd", "Transformation": null, "Value Map": {"1": "Checked"}, "Computation": null}, {"Target": "transfertohospitalnotlisted", "Source": "transferhospnl", "Transformation": null, "Value Map": {"1": "Checked"}, "Computation": null}, {"Target": "antithrombotic1dose", "Source": "doseantithrombotic1", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "antithrombotic2dose", "Source": "doseantithrombotic2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "antithrombotic3dose", "Source": "doseantithrombotic3", "Transformation": null, "Value Map": {"300": "2 mg", "301": "2.5 mg", "302": "3 mg", "303": "4 mg", "304": "5 mg", "305": "6 mg", "306": "7.5 mg", "307": "Other", "308": "Unknown"}, "Computation": null}, {"Target": "antithrombotic3freq", "Source": "freqantithrombotic3", "Transformation": null, "Value Map": {"2": "Every Day", "3": "4 times a day", "4": "5 times a day", "6": "Other", "7": "Unknown"}, "Computation": null}, {"Target": "antithrombotic4", "Source": "medantithrombotic4", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "antithrombotic4class", "Source": "classantithrombotic4", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "antithrombotic4dose", "Source": "doseantithrombotic4", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "antithrombotic4freq", "Source": "freqantithrombotic4", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "bloodglucosend", "Source": "bloodglucose_nd", "Transformation": null, "Value Map": {"1": "ND", "2": "Too Low", "3": "Too High"}, "Computation": null}, {"Target": "bmind", "Source": "bmind", "Transformation": null, "Value Map": {"1": "Checked", "True": "Checked"}, "Computation": null}, {"Target": "cholesterol", "Source": "cholesterol", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "comments", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "compreperf", "Source": "thromthercomp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ctcomplete", "Source": "ctcomp", "Transformation": null, "Value Map": {"1": "Yes", "2": "No/ND", "3": "NC"}, "Computation": null}, {"Target": "ctinittimeprecision", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cttype", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gs_delintra", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gs_diagdm", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "d<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dischargebpnd", "Source": "bpnd", "Transformation": null, "Value Map": {"1": "Checked", "True": "Checked"}, "Computation": null}, {"Target": "dischargediastolic", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dischargesystolic", "Source": "sysdsch", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dmtx", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dvtdoc", "Source": "dvtdoc", "Transformation": null, "Value Map": {"1": "Yes", "2": "No/ND"}, "Computation": null}, {"Target": "dysphagia<PERSON>ult", "Source": "dysphagia<PERSON>ult", "Transformation": null, "Value Map": {"1": "Pass", "2": "Fail", "3": "ND"}, "Computation": null}, {"Target": "gs_eduyes", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "exclusionlessthan3hr", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "exclusiongrtrthan3hr", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fastingglucose", "Source": "fastingblood", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dischargefunctionstatus", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gs_globalrisk", "Source": "globalrisk", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hba1c", "Source": "hba1c", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hba1cnd", "Source": "hba1cnd", "Transformation": null, "Value Map": {"1": "Checked", "True": "Checked"}, "Computation": null}, {"Target": "hdl", "Source": "hdl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "heightnd", "Source": "heightnd", "Transformation": null, "Value Map": {"1": "Checked", "True": "Checked"}, "Computation": null}, {"Target": "heightunit", "Source": "heightu", "Transformation": null, "Value Map": {"1": "in", "2": "cm"}, "Computation": null}, {"Target": "homeless", "Source": "homeless", "Transformation": null, "Value Map": {"1": "Checked"}, "Computation": null}, {"Target": "heartrate", "Source": "heartrate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "icd9nostroke", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "icd10nostroke", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "initexamfindings", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "inrnotdoc", "Source": "inrnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gs_ischemicrisk", "Source": "ischemicrisk", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivtpaorderdatena", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivtpaorderdateprecision", "Source": "ivtpaordereddt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "teamarrivetimeprecision", "Source": "teamarrivedt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ctna", "Source": "ctna", "Transformation": null, "Value Map": {"1": "Checked"}, "Computation": null}, {"Target": "ctorderdatetimeprecision", "Source": "ctorddt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ctinterpretdatetimeprecision", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ecgcompldatetimeprecision", "Source": "ecgcompdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ecgna", "Source": "ecgna", "Transformation": null, "Value Map": {"1": "Checked", "True": "Checked"}, "Computation": null}, {"Target": "ecgorderdatetimeprecision", "Source": "ecgorddt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "assessdatetimeprecision", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "assessdatetimena", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "xraycomments", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "labcompdatetimeprecison", "Source": "labcompdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "labna", "Source": "labna", "Transformation": null, "Value Map": {"1": "Checked"}, "Computation": null}, {"Target": "laborderdatetimeprec", "Source": "laborddt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "neurosurgna", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "neurosurgdatetimeprecision", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "teamactivedatetimeprecision", "Source": "teamactivatedt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "teamactivedatetimena", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "xraycompdatetimeprecision", "Source": "cxrcompdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "xrayorderdatetimeprecision", "Source": "cxrorderdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "x<PERSON>na", "Source": "cxrna", "Transformation": null, "Value Map": {"1": "Checked"}, "Computation": null}, {"Target": "lastknownwellprecision", "Source": "lastknownwell.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lifestylerecomm", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lipidsnc", "Source": "lipidsnc", "Transformation": null, "Value Map": {"1": "Checked", "True": "Checked"}, "Computation": null}, {"Target": "strokemi<PERSON>diag", "Source": "strokemimics", "Transformation": null, "Value Map": {"1": "Migraine", "2": "Seizure", "3": "Delirium", "4": "Electrolyte or metabolic imbalance", "5": "Functional disorder", "6": "Other", "7": "Uncertain"}, "Computation": null}, {"Target": "dysart<PERSON><PERSON><PERSON><PERSON>", "Source": "nihss10", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "extinctionnihss", "Source": "nihss11", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "nihss1a", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ansquesnihss", "Source": "nihss1b", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON>s", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gazenihss", "Source": "nihss2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "visualnihss", "Source": "nihss3", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "paresisni<PERSON>s", "Source": "nihss4", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ltarmfuncnihss", "Source": "nihss5l", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rtarmfuncnihss", "Source": "nihss5r", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rtlegfuncnihss", "Source": "nihss6r", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ltlegfuncnihss", "Source": "nihss6l", "Transformation": null, "Value Map": {"U-Untestablee (Joint fused or limb amputated)": "U-Untestable (Joint fused or limb amputated)"}, "Computation": null}, {"Target": "limbataxianihss", "Source": "nihss7", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "sensorynihss", "Source": "nihss8", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "apha<PERSON><PERSON><PERSON>", "Source": "nihss9", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "nihssretro", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "nogu<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "noguideddosereasonunk", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tpacontraptt", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tpacontraptt2", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hospcontralst3hrs", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hospcontragrt3hrs", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gs_noivhosp2hist", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "no<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "noivthromreason2", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "noivwarninglst3hrs", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "noivwarninggrt3hrs", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "nothromcomments", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherantithrompres", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otheranticoagdose1", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otheranticoagdose2", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otheranticoagfreq1", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otheranticoagfreq2", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otheranticoagmed1", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otheranticoagmed2", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "admordersetused", "Source": "admorder", "Transformation": null, "Value Map": {"1": "Yes", "2": "No"}, "Computation": null}, {"Target": "dischargelistused", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "onsetcomm", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hospacqpneumonia", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "priorantithrom", "Source": "priorantithrom", "Transformation": null, "Value Map": {"1": "Yes", "2": "No/ND"}, "Computation": null}, {"Target": "priorantithrom3", "Source": "priorantithrom3", "Transformation": null, "Value Map": {"3": "aspirin ", "4": "ASA/dipyridamole (Aggrenox) ", "6": "clopidogrel (Plavix) ", "20": "prasug<PERSON> (Effient) ", "21": "ticag<PERSON><PERSON> (Brilinta) ", "7": "ticlopidine (Ticlid) ", "11": "Other Antiplatelet ", "8": "Unfractionated heparin IV ", "9": "full dose LMW heparin ", "5": "warfarin (Coumadin) ", "16": "dabigatran (Pradaxa) ", "15": "a<PERSON><PERSON><PERSON> ", "17": "<PERSON><PERSON><PERSON><PERSON> (Iprivask) ", "12": "fondaparinux (Arixtra) ", "14": "rivaroxaban (Xarelto) ", "19": "<PERSON><PERSON><PERSON><PERSON> (Eliquis) ", "18": "<PERSON><PERSON><PERSON><PERSON> (Refludan) ", "13": "Other Anticoagulant "}, "Computation": null}, {"Target": "priorantithrom3class", "Source": "priorantithrom3class", "Transformation": null, "Value Map": {"1": "Antiplatelet", "2": "Anticoagulant"}, "Computation": null}, {"Target": "ptcontractused", "Source": "ptcontract", "Transformation": null, "Value Map": {"1": "Yes", "2": "No"}, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rankindischarge", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "antithromcontra", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "refhospdischargedatetimeprecison", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "refhosparrivdatetimeprecision", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "refexclusionlst3hrs", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "refexclusiongrt3hrs", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "discoversamelastknwntime", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "smokingmedunspec", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ptstrokesymptomdatetimeprecision", "Source": "symptomdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "thrombolysisprotocol", "Source": "thromexp", "Transformation": null, "Value Map": {"1": "Yes", "2": "No"}, "Computation": null}, {"Target": "thrombolysisprotocoltype", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "transfernihss", "Source": "transfernihss", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "transfernihssnd", "Source": "transfernihssnd", "Transformation": null, "Value Map": {"1": "Checked", "True": "Checked"}, "Computation": null}, {"Target": "triglycerides", "Source": "triglycerides", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vitalsignsnd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "waist", "Source": "waist", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "waistunits", "Source": "<PERSON>u", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "waistnd", "Source": "waistnd", "Transformation": null, "Value Map": {"1": "Checked", "True": "Checked"}, "Computation": null}, {"Target": "weightnd", "Source": "weightnd", "Transformation": null, "Value Map": {"1": "Checked", "True": "Checked"}, "Computation": null}, {"Target": "weightunit", "Source": "weightu", "Transformation": null, "Value Map": {"1": "lb", "2": "kg"}, "Computation": null}, {"Target": "whynoatrialmedsalergy", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "zipcodeext", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "strokediagicd9discharge", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "strokediagicd10discharge", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivtpadela<PERSON>", "Source": "ivtpadela<PERSON>", "Transformation": null, "Value Map": {"1": "Yes", "2": "No"}, "Computation": null}, {"Target": "ivtpadelay30min", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivtpadelay45min", "Source": "ivtpadelay45", "Transformation": null, "Value Map": {"1": "Yes", "2": "No"}, "Computation": null}, {"Target": "ivtpadelayeligreasoncomment", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "i<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivtpadelaymedreasoncomment", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "i<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "admitdateprecision", "Source": "admdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "arrivaldatetimeprecision", "Source": "arrdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dobprecision", "Source": "dob.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivtpainitdatetimeprecision", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "nostatindischarge", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherdiag1", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherdiag10", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherdiag11", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherdiag12", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherdiag13", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherdiag14", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherdiag15", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherdiag16", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherdiag17", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherdiag18", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherdiag19", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherdiag2", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherdiag20", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherdiag21", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherdiag22", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherdiag23", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherdiag24", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherdiag3", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherdiag4", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherdiag5", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherdiag6", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherdiag7", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherdiag8", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otherdiag9", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vtedateprecision", "Source": "vtedate.p", "Transformation": null, "Value Map": {"5": "MM/DD/YYYY HH24:MM", "3": "MM/DD/YYYY", "0": "Unknown"}, "Computation": null}, {"Target": "aspirinprophylaxis", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ndprophylaxis", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "Checked", "false_val": null, "fields": [{"name": "vtepropnd", "values": ["1"]}]}, "is_computation": null}]}, {"Target": "vfpprophylaxis", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "merica", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "merimagedateprecision", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mermca", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "targles", "Transformation": null, "Value Map": {"1": "Yes", "2": "No/ND"}, "Computation": null}, {"Target": "imagingdone", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "imagingtype", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "imagingnd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vesocclusite", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "oh_optional1", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "oh_optional10", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "oh_optional13", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "oh_optional13_precision", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "oh_optional15", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "oh_optional14_precision", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "oh_optional2", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "oh_optional3", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "oh_optional4", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "oh_optional5", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "oh_optional6", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "oh_optional7", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "oh_optional8", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "oh_optional9", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "osi_form_type_bitmap", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "antidepmedpost", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "principaldiagnosis", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "antidepmedprior", "Source": "priorantidepmed", "Transformation": null, "Value Map": {"1": "Yes", "2": "No/ND"}, "Computation": null}, {"Target": "procstep_id", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "site_os_id", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": "hospname"}, "is_computation": true}]}, {"Target": "sp_ethnic_opt", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "study_id", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "antismokingtx", "Source": "antismokingtx", "Transformation": null, "Value Map": {"1": "Yes", "2": "No/ND", "3": "NC"}, "Computation": null}, {"Target": "dischargedateprecision", "Source": "disdate.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "laborderdatetimeprecision", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rankinscore", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cholreducothermed", "Source": "nostatindisc", "Transformation": null, "Value Map": {"Other med": "Yes", "1": "Yes"}, "Computation": null}, {"Target": "priorhypertension", "Source": "htntxprior", "Transformation": null, "Value Map": {"Hypertension": "Yes", "1": "Yes"}, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "Yes", "false_val": "prevmedhistory", "fields": [{"name": "prevmedhistory", "values": ["SMOKER"]}]}, "is_computation": true}, {"Transform_Type": "replace", "Args": {"old_values": "nan", "new_values": ""}, "is_computation": null}]}, {"Target": "posbrainimgdatetimeprecision", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "originalmrn", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "clientversion", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doortoneedle", "Source": "doortoneedle", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_ticigrade2", "Source": "thromticigrade2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_ticigrade", "Source": "thromticigrade", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_eligmer", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_lvo", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tq_disccode", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tq_em", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_princicd10procedure", "Source": "i10prinpcs-description", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proc6", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proc7", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proc8", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proc9", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proc16", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proc18", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proc24", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proc23", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "biomeencounterid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "timtointravthrp45excl", "Source": "timtointravthrp45excl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_posbrain_result", "Source": "pos<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dmtype", "Source": "dmtype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "presdmtx", "Source": "presdmtx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dmt<PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dxfupsched", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dxfups<PERSON>dt", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dxfupscheddt_precision", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dyn_diabetes", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gs_thrombadmin", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gs_tenecdosend", "Source": "tenecdosend", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gs_altedosend", "Source": "altedosend", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gs_altotaldose", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gs_thrombimg", "Source": "thrombimg", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gs_thrombimg_ot", "Source": "oththrombimg", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "noactiveinfec", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "activecoldflu", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gs_tenectotaldose", "Source": "tenectotaldose", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure10", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mer_delay", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_ichscore", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure_timeutd2", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proctimeutd9", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tq_encounterdt_precision", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proc1", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure14", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_sahna", "Source": "sahna", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proc15", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mer_imagepdt_precision", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_directadm", "Source": "directadmit", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure_timeutd12", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_nimodipine_dt_precision", "Source": "nimodipinedt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure_timeutd21", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_deteriorate36h", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure20", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "admittingdiagnosis", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure16", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dyn_comprehensive", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_ldlhigh", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure_timeutd19", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_inraftertx", "Source": "inraftertx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proc14", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate20_utd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_sahscale_value", "Source": "sahscalevalue", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure_timeutd3", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proc13", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proctimeutd15", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_nihss36ia", "Source": "nihss36ia", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proc21", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proctimeutd10", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proctimeutd7", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure_timeutd23", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdate20", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure2", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mer_reasoth", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proctimeutd4", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_nihss36h", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_sahscale", "Source": "sahscale", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_emsgcstot", "Source": "emsgcstot", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure_timeutd20", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_wfnssah", "Source": "wfnssah", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gs_uti", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proc11", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proc19", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_nihss36h_nd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure4", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_procoagulant_dt_precision", "Source": "procoagulantdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_<PERSON><PERSON>", "Source": "<PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure12", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "admittingdiagnosisicd10", "Source": "i10admitdx-description", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_emsgcsvoic", "Source": "emsgcsvoic", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure13", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mer_ticidt_precision", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_emsgcseye", "Source": "emsgcseye", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_ivoriatherapy", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_nihssivtutd", "Source": "nihssivtutd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mer_<PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_procoagulant", "Source": "procoagulant", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proctimeutd5", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_sahscaledt_precision", "Source": "sahscaledt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proctimeutd20", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_ivtpaprior", "Source": "ivtpaprior", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "jc_otherprocedure_timeutd11", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_iaroutetpa", "Source": "iaroutetpa", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_nihssperf", "Source": "ni<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proc17", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_funcscore", "Source": "ichfuncscore", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure19", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_proxdist", "Source": "proxdist", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_sahscaledt", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mer_<PERSON><PERSON><PERSON><PERSON>", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_nihssiatpautd", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gs_pc_emsgcstot", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_nihss_nd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_platelet", "Source": "platelet", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_nihssiatpamer", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_emsgcsintub", "Source": "emsgcsintub", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "jc_othericd10proc4", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_emsgcsmot", "Source": "emsgcsmot", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_noantithrodisc", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure_timeutd8", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure8", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_lastknown", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proc10", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure23", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_antithrodisc", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure7", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure_timeutd22", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure18", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_princproced_timeutd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure_timeutd7", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_addcm", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure_timeutd14", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gs_foley", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mer_ivscoreobt", "Source": "merivscoreobt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proctimeutd6", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gs_pc_emsgcseye", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_notstroke", "Source": "notstroke", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proc12", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdateutd16", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_athero", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_nihperf", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proctimeutd23", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_discdtutd", "Source": "cdiscdtutd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proc20", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mer_proc", "Source": "merproc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proc3", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_iathrominitdt_precision", "Source": "iathrominitdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_nimodipine", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure_timeutd16", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_inrperf", "Source": "inrperf", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure_timeutd24", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure_timeutd15", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_firstpass", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure9", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_antithroday2", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proc2", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure_timeutd5", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_reasonnonim", "Source": "reasonnon<PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_nihssobtutd", "Source": "nihs<PERSON>bt<PERSON>d", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "jc_othericd10proctimeutd12", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proctimeutd19", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_procoagulant_dt", "Source": "procoagulantdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dyn_strokecm", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_neuroimaging36h", "Source": "neuroimaging36h", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivtpastarted", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "Yes", "false_val": null, "fields": [{"name": "gs_thrombo", "values": ["Alteplase (Class 1 evidence)"]}, {"name": "ivthrombolyisstarted", "values": ["YES"]}]}, "is_computation": true}]}, {"Target": "ivtnkstarted", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": "Yes", "false_val": null, "fields": [{"name": "gs_thrombo", "values": ["Tenecteplase (Class 2b evidence)"]}, {"name": "ivthrombolyisstarted", "values": ["YES"]}]}, "is_computation": true}]}, {"Target": "jc_othericd10proctimeutd16", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_princicd10proced_dtutd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_nihss36iv", "Source": "nihss36iv", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gs_jp_periodq", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_surgicalich_hr", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_death36hr", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure24", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_statindisc", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gs_pc_emsgcsintub", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_skin", "Source": "skin", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mer_imnotperf", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_delayed", "Source": "delayed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proctimeutd3", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proctimeutd8", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_discdatetime_precision", "Source": "cdiscdatetime.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gs_pc_emsgcsmot", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proc5", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dyn_mer", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure_timeutd1", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proctimeutd2", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mer_present", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proctimeutd13", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure11", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_nihss36ivutd", "Source": "nihss36ivutd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_eddepart_precision", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure_timeutd18", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_reasnopro", "Source": "reasnopro", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_medicare", "Source": "medicarejc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mer_notperf", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_artpuncdt_precision", "Source": "artpuncdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure_timeutd17", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proctimeutd21", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure_timeutd9", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_iathrominitdt", "Source": "iathrominitdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure_timeutd4", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_nimodipine24h", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_iathrominit", "Source": "iathrominit", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dyn_coverdell", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proctimeutd18", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure_timeutd13", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure6", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_nihssivt", "Source": "nihssivt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proctimeutd1", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_ichscore_value", "Source": "ichscorevalue", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proc22", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure_timeutd10", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mer_scoreobt", "Source": "merscoreobt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherprocedure_timeutd6", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_inr_dt_precision", "Source": "inrdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_prelipidmed", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_noanticoagdisc", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_nihss", "Source": "nihss1c", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_noantithroday2", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10proctimeutd14", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_ichscoredt_precision", "Source": "ichscoredt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "timtointravthrp45den", "Source": "timtointravthrp45den", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "timtointravthrp45ipp", "Source": "timtointravthrp45ipp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "timtointravthrp30num", "Source": "timtointravthrp30num", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "timtointravthrp30excl", "Source": "timtointravthrp30excl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "timtointravthrp30den", "Source": "timtointravthrp30den", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "timtointravthrp30ipp", "Source": "timtointravthrp30ipp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivthromarrive2num", "Source": "ivthromarrive2num", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivthromarrive2excl", "Source": "ivthromarrive2excl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivthromarrive2den", "Source": "ivthromarrive2den", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivthromarrive2ipp", "Source": "ivthromarrive2ipp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doorindoorexcep", "Source": "doorindoorexcep", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doorindoorexcl", "Source": "doorindoorexcl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doorindoorden", "Source": "doorindoorden", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "timintrventhromthertmnum", "Source": "timintrventhromthertmnum", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "timintrventhromthertmexcl", "Source": "timintrventhromthertmexcl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "timintrventhromthertmden", "Source": "timintrventhromthertmden", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "timintrventhromthertmipp", "Source": "timintrventhromthertmipp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivthromblyarrvtmnum", "Source": "ivthromblyarrvtmnum", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivthromblyarrvtmexcl", "Source": "ivthromblyarrvtmexcl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivthromblyarrvtmden", "Source": "ivthromblyarrvtmden", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "timintrvent<PERSON><PERSON><PERSON>num", "Source": "timintrvent<PERSON><PERSON><PERSON>num", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "timintrventhromtherexcl", "Source": "timintrventhromtherexcl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "timint<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "timint<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tim<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "tim<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "strokeducationnum", "Source": "strokeducationnum", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "strokeducationexcl", "Source": "strokeducationexcl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "strokeducationden", "Source": "strokeducationden", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stroked<PERSON><PERSON><PERSON><PERSON>", "Source": "stroked<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rehabconsidnum", "Source": "rehabconsidnum", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rehabconsidexcl", "Source": "rehabconsidexcl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rehabconsidden", "Source": "rehabconsidden", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rehabconsidipp", "Source": "rehabconsidipp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "nihssreportnum", "Source": "nihssreportnum", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "nihssreportexcl", "Source": "nihssreportexcl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "nihssreportden", "Source": "nihssreportden", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "nihssreportipp", "Source": "nihssreportipp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ldldocumentednum", "Source": "ldldocumentednum", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ldldocumentedexcl", "Source": "ldldocumentedexcl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ldldocumentedden", "Source": "ldldocumentedden", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ldldocumentedipp", "Source": "ldldocumentedipp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dysphagscreennum", "Source": "dysphagscreennum", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dysphagscreenexcl", "Source": "dysphagscreenexcl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dysphags<PERSON><PERSON><PERSON>", "Source": "dysphags<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vteprophylxnum", "Source": "vteprophylxnum", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vteprophylxexcl", "Source": "vteprophylxexcl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vteprophylxden", "Source": "vteprophylxden", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vteprophylxipp", "Source": "vteprophylxipp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "smokcessationnum", "Source": "smokcessationnum", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "smokcessationexcl", "Source": "smokcessationexcl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "smokcessationden", "Source": "smokcessationden", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "smokcessationipp", "Source": "smokcessationipp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivthromblyarrvnum", "Source": "ivthromblyarrvnum", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivthromblyarrvexcl", "Source": "ivthromblyarrvexcl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivthromblyarrvden", "Source": "ivthromblyarrvden", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "iv<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "iv<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "intstatthernum", "Source": "intstatthernum", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "intstattherexcep", "Source": "intstattherexcep", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "intstattherexcl", "Source": "intstattherexcl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "intstattherden", "Source": "intstattherden", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "intstattheripp", "Source": "intstattheripp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gs_notcared", "Source": "notcared", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "anticoagafden", "Source": "anticoagafden", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "anticoagafexcl", "Source": "anticoagafexcl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "anticoagafnum", "Source": "anticoagafnum", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "antithrombdisipp", "Source": "antithrombdisipp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "antithrombdisden", "Source": "antithrombdisden", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "antithrombdisexcl", "Source": "antithrombdisexcl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "antithrombdisexcep", "Source": "antithrombdisexcep", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "antithrombdisnum", "Source": "antithrombdisnum", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "earlantithrombexcl", "Source": "earlantithrombexcl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "earlantithrombnum", "Source": "earlantithrombnum", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doortopunct", "Source": "doortopunct", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doortoneuroactiv", "Source": "doortoneuroactiv", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doortoniarriv", "Source": "doortoniarriv", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vtepropnd", "Source": "vtepropnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "novteadmis", "Source": "novteadmis", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doortodevice", "Source": "doortodevice", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivtpadelay30", "Source": "ivtpadelay30", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doortoctintp", "Source": "doortoctintp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doortoctinit", "Source": "doortoctinit", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doortoteam", "Source": "doortoteam", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doortophys", "Source": "doortophys", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lpa", "Source": "lpa", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lpand", "Source": "lpand", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lpaunit", "Source": "lpaunit", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "imagecdt_precision", "Source": "imagecdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vascperfimagend", "Source": "vascperfimagend", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vascimage", "Source": "vascimage", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vtepropaspirin", "Source": "vtepropaspirin", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vtepropvenftpump", "Source": "vtepropvenftpump", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vtepropwarfarin", "Source": "vtepropwarfarin", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vtepropfactorxa", "Source": "vtepropfactorxa", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vteproplmwh", "Source": "vteproplmwh", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vteproplduh", "Source": "vteproplduh", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "eduall", "Source": "eduall", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "eduems", "Source": "eduems", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "neuroactivdt_precision", "Source": "neuroactivdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "neuroarrdt_precision", "Source": "neuroarrdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dmteaching", "Source": "dmteaching", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "teamna", "Source": "teamna", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "compafterivtpa", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_weightloss", "Source": "fu_weightloss", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_bmi", "Source": "fu_bmi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antithrombotic3class", "Source": "fu_antithrombotic3class", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_returnedforfollowup", "Source": "fu_returnedforfollowup", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk05mx", "Source": "cstk05mx", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "x<PERSON><PERSON>", "Source": "x<PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "telethromb", "Source": "telethromb", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "sameaslastknown", "Source": "sameaslastknown", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "apttnd", "Source": "apttnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_glucose", "Source": "fu_glucose", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_repeatswallow", "Source": "fu_repeatswallow", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk12md", "Source": "cstk12md", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "cstk01me", "Source": "cstk01me", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_noappreason_other", "Source": "fu_noappreason_other", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antiplatelet", "Source": "fu_antiplatelet", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doortostartipp", "Source": "doortostartipp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk04mp", "Source": "stk04mp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_aspstopreas", "Source": "fu_aspstopreas", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk05mr", "Source": "cstk05mr", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "stk01mr", "Source": "stk01mr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doortostartnum", "Source": "doortostartnum", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01amx", "Source": "stkop01amx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_symptoms", "Source": "fu_symptoms", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_rehabtype", "Source": "fu_rehabtype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk01mx", "Source": "cstk01mx", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "othreversalpriorspec", "Source": "othreversalpriorspec", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cantithrodisc", "Source": "cantithrodisc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_diabtxifmissed", "Source": "fu_diabtxifmissed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "procoagulantprior", "Source": "procoagulantprior", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antithrombotic3dose", "Source": "fu_antithrombotic3dose", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aspectm2", "Source": "aspectm2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01amr", "Source": "stkop01amr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk04me", "Source": "cstk04me", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_datefollowcomp_p", "Source": "fu_datefollowcomp.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "telereqresp", "Source": "telereqresp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doortostartden", "Source": "doortostartden", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "nimodipinedt_p", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_holter", "Source": "fu_holter", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "prehospscreenot", "Source": "prehospscreenot", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_whomtocall", "Source": "fu_whomtocall", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_lvefspecify", "Source": "fu_lvefspecify", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "othreversalspec", "Source": "othreversalspec", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01gmx", "Source": "stkop01gmx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "firstpassdt_p", "Source": "firstpassdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "civthroext", "Source": "civthroext", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_newantith<PERSON><PERSON>s", "Source": "fu_newantith<PERSON><PERSON>s", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aspectm4", "Source": "aspectm4", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_diabtx", "Source": "fu_diabtx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_ernohosp", "Source": "fu_ernohosp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "bplower", "Source": "bplower", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_diedsincedisc", "Source": "fu_diedsincedisc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_conducted_other", "Source": "fu_conducted_other", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk06me", "Source": "cstk06me", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_antithrombotic4freq", "Source": "fu_antithrombotic4freq", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk03amx", "Source": "cstk03amx", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_eddate1_p", "Source": "fu_eddate1.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_fupstrokedt_p", "Source": "fu_fupstrokedt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk06mx", "Source": "cstk06mx", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "stkop01fmx", "Source": "stkop01fmx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivtpanc", "Source": "ivtpanc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emssevscore", "Source": "emssevscore", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antistopoth", "Source": "fu_antistopoth", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_physicalactivity", "Source": "fu_physicalactivity", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_telemanage", "Source": "fu_telemanage", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk06mx", "Source": "stk06mx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk05mp", "Source": "stk05mp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01amb", "Source": "stkop01amb", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk03bme", "Source": "cstk03bme", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_antidepresoth", "Source": "fu_antidepresoth", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_eddate_p", "Source": "fu_eddate.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_newdiabtx", "Source": "fu_newdiabtx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "firstpass", "Source": "firstpass", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_edvisitreason", "Source": "fu_edvisitreason", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doortostartexcep", "Source": "doortostartexcep", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsarrivescenedt_p", "Source": "emsarrivescenedt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ctrepdt_p", "Source": "ctrepdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "merothreasons", "Source": "merothreasons", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk05me", "Source": "cstk05me", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_medication", "Source": "fu_medication", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hypmed", "Source": "hypmed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_transesophogeal", "Source": "fu_transesophogeal", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antithrombotic1", "Source": "fu_antithrombotic1", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antithrombotic4dose", "Source": "fu_antithrombotic4dose", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lobar", "Source": "lobar", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antistopreas", "Source": "fu_antistopreas", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01emx", "Source": "stkop01emx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "sahscaledt_p", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsscenedepdt_p", "Source": "emsscenedepdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_postdismrsdate_p", "Source": "fu_postdismrsdate.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "newdmdxbasis", "Source": "newdmdxbasis", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "nihssobtlt6", "Source": "nihssobtlt6", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_rankindc", "Source": "c_rankindc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsfmctoecg", "Source": "emsfmctoecg", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsfirstmedcont_p", "Source": "emsfirstmedcont.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk04mr", "Source": "stk04mr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "transrechosp_p", "Source": "transrechosp.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk05amx", "Source": "cstk05amx", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "procoagulanttypeoth", "Source": "procoagulanttypeoth", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsptoactv", "Source": "emsptoactv", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_anticoagulant", "Source": "fu_anticoagulant", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aspectcaudate", "Source": "aspectcaudate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_waistcirc", "Source": "fu_waistcirc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_calorierestrict", "Source": "fu_calorierestrict", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vtepropgcs", "Source": "vtepropgcs", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01fmp", "Source": "stkop01fmp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_hr", "Source": "fu_hr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01dmr", "Source": "stkop01dmr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "medhistnone", "Source": "medhistnone", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_phq5", "Source": "fu_phq5", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk03mp", "Source": "stk03mp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk01md", "Source": "stk01md", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_<PERSON><PERSON>h", "Source": "fu_<PERSON><PERSON>h", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "telethrombdt_p", "Source": "telethrombdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk03bmp", "Source": "cstk03bmp", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "cstk05amd", "Source": "cstk05amd", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_newanticoag", "Source": "fu_newanticoag", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "televideo_p", "Source": "televideo.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aptt", "Source": "aptt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_monitorbp", "Source": "fu_monitorbp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lpatreatoth", "Source": "lpatreatoth", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lpatreat", "Source": "lpatreat", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antithrombotic2dose", "Source": "fu_antithrombotic2dose", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk08mb", "Source": "stk08mb", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_peripheral", "Source": "fu_peripheral", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emssevscorend", "Source": "emssevscorend", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antithromifmissed", "Source": "fu_antithromifmissed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01hmp", "Source": "stkop01hmp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk01mp", "Source": "cstk01mp", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "cstk12mx", "Source": "cstk12mx", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_typeoffice", "Source": "fu_typeoffice", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "altetotaldose", "Source": "altetotaldose", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emssevscaleot", "Source": "emssevscaleot", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_tobaccofreq", "Source": "fu_tobaccofreq", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ctcompdt_p", "Source": "ctcompdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_lvefpercent", "Source": "fu_lvefpercent", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_ldl", "Source": "fu_ldl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_disconanticoag", "Source": "fu_disconanticoag", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_smokedsincehosp", "Source": "fu_smokedsincehosp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_bpreport", "Source": "fu_bpreport", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_cholredtx", "Source": "fu_cholredtx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk01mp", "Source": "stk01mp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_revmed", "Source": "fu_revmed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk03amr", "Source": "cstk03amr", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_<PERSON>mi", "Source": "fu_<PERSON>mi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "uti", "Source": "uti", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_specificcause", "Source": "fu_specificcause", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01bmb", "Source": "stkop01bmb", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01emy", "Source": "stkop01emy", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_anticoagstopoth", "Source": "fu_anticoagstopoth", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01dmd", "Source": "stkop01dmd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "referdischarge_p", "Source": "referdischarge.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_rankin_reason", "Source": "c_rankin_reason", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk05me", "Source": "stk05me", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk06md", "Source": "cstk06md", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "civthroinit", "Source": "civthroinit", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_diagbasis", "Source": "fu_diagbasis", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antithrombotic2class", "Source": "fu_antithrombotic2class", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_rankin", "Source": "fu_rankin", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_<PERSON>ten", "Source": "fu_<PERSON>ten", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_statstopreas", "Source": "fu_statstopreas", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "soc<PERSON>assess", "Source": "soc<PERSON>assess", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk09amp", "Source": "cstk09amp", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_sympstroke", "Source": "fu_sympstroke", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_othmedstop", "Source": "fu_othmedstop", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_lvefdate_p", "Source": "fu_lvefdate.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk08mp", "Source": "cstk08mp", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_transthoracic", "Source": "fu_transthoracic", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antithrombotic4class", "Source": "fu_antithrombotic4class", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "referarrdt_p", "Source": "referarrdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "interfacilityemsagency", "Source": "interfacilityemsagency", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsarrtodep", "Source": "emsarrtodep", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_diabtxmisseddose", "Source": "fu_diabtxmisseddose", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk05mb", "Source": "cstk05mb", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "bulkset1", "Source": "bulkset1", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01imd", "Source": "stkop01imd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01hmx", "Source": "stkop01hmx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_phq1", "Source": "fu_phq1", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "bplowerdt_p", "Source": "bplowerdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01fmb", "Source": "stkop01fmb", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk05bmp", "Source": "cstk05bmp", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "cstk09bmx", "Source": "cstk09bmx", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "doortodeviceden", "Source": "doortodeviceden", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_lipid", "Source": "fu_lipid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_patientdispfirsted", "Source": "fu_patientdispfirsted", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antithrombotic3", "Source": "fu_antithrombotic3", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01gmy", "Source": "stkop01gmy", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_noappreasonauto", "Source": "fu_noappreasonauto", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk09bmp", "Source": "cstk09bmp", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "emsbpd", "Source": "emsbpd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk04mp", "Source": "cstk04mp", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "i10nostkdx", "Source": "i10nostkdx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aspectbasgang", "Source": "aspectbasgang", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_anticoagismissed", "Source": "fu_anticoagismissed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "intptoneedle", "Source": "intptoneedle", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "labsordertocomplete", "Source": "labsordertocomplete", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk06me", "Source": "stk06me", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01imx", "Source": "stkop01imx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk03amb", "Source": "cstk03amb", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_medadherence", "Source": "fu_medadherence", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "npo", "Source": "npo", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_mrct", "Source": "fu_mrct", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "subfacility", "Source": "subfacility", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "reversal90spec", "Source": "reversal90spec", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "modetrans", "Source": "modetrans", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk05amp", "Source": "cstk05amp", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_postdismrsscale", "Source": "fu_postdismrsscale", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsagencyunk", "Source": "emsagencyunk", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_rehablocation", "Source": "fu_rehablocation", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cause90delay", "Source": "cause90delay", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01dmp", "Source": "stkop01dmp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk11mp", "Source": "cstk11mp", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_rehabsvcs", "Source": "fu_rehabsvcs", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk05md", "Source": "stk05md", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "sustsysbpdt_p", "Source": "sustsysbpdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk01mb", "Source": "stk01mb", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "advnotice", "Source": "advnotice", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "priorantithromdt_p", "Source": "priorantithromdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "datealert_p", "Source": "datealert.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk03mp", "Source": "cstk03mp", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_phq8", "Source": "fu_phq8", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk04mb", "Source": "cstk04mb", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "transrefhosp_p", "Source": "transrefhosp.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_anticoagstopreas", "Source": "fu_anticoagstopreas", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk05bmr", "Source": "cstk05bmr", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "pcmodifierdate_p", "Source": "pcmodifierdate.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_cholreddosing", "Source": "fu_cholreddosing", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "covidvacctrial", "Source": "covidvacctrial", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk03ame", "Source": "cstk03ame", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "cantithroday2", "Source": "cantithroday2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01imp", "Source": "stkop01imp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emscallrecdt_p", "Source": "emscallrecdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "intake", "Source": "intake", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk09amx", "Source": "cstk09amx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_appprior", "Source": "fu_appprior", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "teleconsultdt_p", "Source": "teleconsultdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_smokecess", "Source": "fu_smokecess", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_sympbreath", "Source": "fu_sympbreath", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk03me", "Source": "cstk03me", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_datepostdisc_p", "Source": "fu_datepostdisc.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON>alert", "Source": "<PERSON>alert", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk12mp", "Source": "cstk12mp", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "cstk01mr", "Source": "cstk01mr", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "emsfmctoevt", "Source": "emsfmctoevt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "noantithroday2", "Source": "noantithroday2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_fallreported", "Source": "fu_fallreported", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "plateletutd", "Source": "plateletutd", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "stk10mp", "Source": "stk10mp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk02mb", "Source": "stk02mb", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cnoantithrodisc", "Source": "cnoantithrodisc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_actguidelines", "Source": "fu_actguidelines", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsbpnd", "Source": "emsbpnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_numberhosp", "Source": "fu_numberhosp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "scrnd", "Source": "scrnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivthrodt_p", "Source": "ivthrodt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "transarrdt_p", "Source": "transarrdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ems<PERSON><PERSON><PERSON>", "Source": "ems<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "nihs<PERSON>bt", "Source": "nihs<PERSON>bt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_tobacmed", "Source": "fu_tobacmed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivthromblyarrvtmipp", "Source": "ivthromblyarrvtmipp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk10mb", "Source": "stk10mb", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ichvolume", "Source": "ichvolume", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk11mr", "Source": "cstk11mr", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_followconducted", "Source": "fu_followconducted", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk02md", "Source": "stk02md", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk08mp", "Source": "stk08mp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk03md", "Source": "stk03md", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "othreversal", "Source": "othreversal", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstatindisc", "Source": "cstatindisc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dido", "Source": "dido", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antihyperdosing", "Source": "fu_antihyperdosing", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk05mb", "Source": "stk05mb", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_typicalbpreading", "Source": "fu_typicalbpreading", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "posbrain_p", "Source": "posbrain.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "opencounterdt_p", "Source": "opencounterdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_phq3", "Source": "fu_phq3", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_postdismrsscore", "Source": "fu_postdismrsscore", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_appkept", "Source": "fu_appkept", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk08mr", "Source": "cstk08mr", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "nodmtx<PERSON><PERSON>", "Source": "nodmtx<PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_stkafter", "Source": "fu_stkafter", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "reversal60delay", "Source": "reversal60delay", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_needmedfollowup", "Source": "fu_needmedfollowup", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_phq7", "Source": "fu_phq7", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk12me", "Source": "cstk12me", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_hemoglobin", "Source": "fu_hemoglobin", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "timtointravthrp45num", "Source": "timtointravthrp45num", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01gmd", "Source": "stkop01gmd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_phq6", "Source": "fu_phq6", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsfmctoneed", "Source": "emsfmctoneed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "iatpadt_p", "Source": "iatpadt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsmsuct_p", "Source": "emsmsuct.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "em<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "em<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01emr", "Source": "stkop01emr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_source_other", "Source": "fu_source_other", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antithrommisseddose", "Source": "fu_antithrommisseddose", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "transreqst_p", "Source": "transreqst.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_phq4", "Source": "fu_phq4", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_assessrehab", "Source": "fu_assessrehab", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_signsstroke", "Source": "fu_signsstroke", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk03me", "Source": "stk03me", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01emp", "Source": "stkop01emp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk09bmb", "Source": "cstk09bmb", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "<PERSON>ot", "Source": "<PERSON>ot", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antidiastopoth", "Source": "fu_antidiastopoth", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_noapp_other", "Source": "fu_noapp_other", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_source", "Source": "fu_source", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk03mx", "Source": "cstk03mx", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "stk10mx", "Source": "stk10mx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk08mb", "Source": "cstk08mb", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "ctcompsel", "Source": "ctcompsel", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsfmctoarr", "Source": "emsfmctoarr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk09amr", "Source": "cstk09amr", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_sympcp", "Source": "fu_sympcp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk06mp", "Source": "cstk06mp", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_admitdate_p", "Source": "fu_admitdate.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cause60delay", "Source": "cause60delay", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01imb", "Source": "stkop01imb", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsbgl", "Source": "emsbgl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_comprankindate_p", "Source": "fu_comprankindate.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk05md", "Source": "cstk05md", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "cstk04mr", "Source": "cstk04mr", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_hdl", "Source": "fu_hdl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_oth<PERSON><PERSON><PERSON><PERSON>", "Source": "fu_oth<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_medcaresystem", "Source": "fu_medcaresystem", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aspectm1", "Source": "aspectm1", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsfmctolab", "Source": "emsfmctolab", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk10me", "Source": "stk10me", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ichetiology", "Source": "ichetiology", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_cholredismissed", "Source": "fu_cholredismissed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk09amd", "Source": "cstk09amd", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "cstk09mx", "Source": "cstk09mx", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "repeatpro", "Source": "repeatpro", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_diabeducation", "Source": "fu_diabeducation", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_currentmedauto", "Source": "fu_currentmedauto", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_chemistries", "Source": "fu_chemistries", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk02mr", "Source": "stk02mr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_chol", "Source": "fu_chol", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk03mr", "Source": "cstk03mr", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_creatinine", "Source": "fu_creatinine", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk09bmd", "Source": "cstk09bmd", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "stkop01dmy", "Source": "stkop01dmy", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antismokingtx", "Source": "fu_antismokingtx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emslk<PERSON><PERSON><PERSON>r", "Source": "emslk<PERSON><PERSON><PERSON>r", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk05bmd", "Source": "cstk05bmd", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "endendodt_p", "Source": "endendodt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsfmctoctint", "Source": "emsfmctoctint", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01fmd", "Source": "stkop01fmd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "neuroassessdt_p", "Source": "neuroassessdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk03mx", "Source": "stk03mx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "othreversalprior", "Source": "othreversalprior", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_comprankinscore", "Source": "fu_comprankinscore", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsfmctoctinit", "Source": "emsfmctoctinit", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_diabtxdosing", "Source": "fu_diabtxdosing", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cnoanticoagdisc", "Source": "cnoanticoagdisc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "neuroassessnd", "Source": "neuroassessnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_rankin_oth", "Source": "c_rankin_oth", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01hmd", "Source": "stkop01hmd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "procoagulantpriortypeoth", "Source": "procoagulantpriortypeoth", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emslkwdt_p", "Source": "emslkwdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "teleex<PERSON>", "Source": "teleex<PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_monitorweight", "Source": "fu_monitorweight", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antithrombotic1dose", "Source": "fu_antithrombotic1dose", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivtpaorderedna", "Source": "ivtpaorderedna", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk12mr", "Source": "cstk12mr", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "thromticidt_p", "Source": "thromticidt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doorvessel", "Source": "doorvessel", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doortostartexcl", "Source": "doortostartexcl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_anticoagtherapy", "Source": "fu_anticoagtherapy", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk03bmd", "Source": "cstk03bmd", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "pltrandt_p", "Source": "pltrandt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk09amy", "Source": "cstk09amy", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk06mp", "Source": "stk06mp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "clastknowndttm_p", "Source": "clastknowndttm.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk03mb", "Source": "stk03mb", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_rankin_lack", "Source": "c_rankin_lack", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cnoivthro", "Source": "cnoivthro", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk09mp", "Source": "cstk09mp", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "cstk11me", "Source": "cstk11me", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "priorantipltdt_p", "Source": "priorantipltdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk08me", "Source": "stk08me", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pltransfusion", "Source": "pltransfusion", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_heightunits", "Source": "fu_heightunits", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dmdxfusched", "Source": "dmdxfusched", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_datedeath_p", "Source": "fu_datedeath.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_rankin_dateunk", "Source": "c_rankin_dateunk", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "edassessnd", "Source": "edassessnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk09my", "Source": "cstk09my", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_dietcounseling", "Source": "fu_dietcounseling", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ipcdoc", "Source": "ipcdoc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "earliestcstk03_p", "Source": "earliestcstk03.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_quallvdys", "Source": "fu_quallvdys", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_specificcausedeat<PERSON>_other", "Source": "fu_specificcausedeat<PERSON>_other", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "presantithrombotic", "Source": "presantithrombotic", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "i10admitdx", "Source": "i10admitdx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_pillcontainer", "Source": "fu_pillcontainer", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_o<PERSON><PERSON><PERSON><PERSON>", "Source": "fu_o<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antithrombotic1freq", "Source": "fu_antithrombotic1freq", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01bmd", "Source": "stkop01bmd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_height", "Source": "fu_height", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk10mr", "Source": "stk10mr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "reversal", "Source": "reversal", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01amp", "Source": "stkop01amp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "npi", "Source": "npi", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antithrombotic2", "Source": "fu_antithrombotic2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "edassessdt_p", "Source": "edassessdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_tobacstopnum", "Source": "fu_tobacstopnum", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emssymptomsdt_p", "Source": "emssymptomsdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk06mr", "Source": "stk06mr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_tobacstopattempt", "Source": "fu_tobacstopattempt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk12mb", "Source": "cstk12mb", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "stk03mr", "Source": "stk03mr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_phq2", "Source": "fu_phq2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antithromdosing", "Source": "fu_antithromdosing", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01bmp", "Source": "stkop01bmp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "currpreg", "Source": "currpreg", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aspectm3", "Source": "aspectm3", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aspectinsribb", "Source": "aspectinsribb", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aspectm5", "Source": "aspectm5", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk05mx", "Source": "stk05mx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ipcdt_p", "Source": "ipcdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "i10pcsnd", "Source": "i10pcsnd", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "cp<PERSON><PERSON><PERSON><PERSON>", "Source": "cp<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "canticoagdisc", "Source": "canticoagdisc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "bpnddsch", "Source": "bpnddsch", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "reversal90delay", "Source": "reversal90delay", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_phq9", "Source": "fu_phq9", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "antiplateletadm", "Source": "antiplateletadm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "departeddt_p", "Source": "departeddt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_nosymp", "Source": "fu_nosymp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_death", "Source": "fu_death", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_carotid", "Source": "fu_carotid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_newantihypertx", "Source": "fu_newantihypertx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_bloodsincedisc", "Source": "fu_bloodsincedisc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lkwtoarr", "Source": "lkwtoarr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "telerespdt_p", "Source": "telerespdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antidepresreas", "Source": "fu_antidepresreas", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01imy", "Source": "stkop01imy", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk10md", "Source": "stk10md", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01bmr", "Source": "stkop01bmr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsrecordavail", "Source": "emsrecordavail", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk08mx", "Source": "cstk08mx", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "stk04me", "Source": "stk04me", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "reversal60spec", "Source": "reversal60spec", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk05mr", "Source": "stk05mr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "punctorep", "Source": "punctorep", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "nothromcomment", "Source": "nothromcomment", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsprehospemsdt_p", "Source": "emsprehospemsdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_anticoagmisseddose", "Source": "fu_anticoagmisseddose", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_noapp", "Source": "fu_noapp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk04md", "Source": "cstk04md", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "redosedt_p", "Source": "redosedt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_30dayadmit", "Source": "fu_30dayadmit", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "weightmgmt", "Source": "weightmgmt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_newcholtx", "Source": "fu_newcholtx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_medsatdc", "Source": "fu_medsatdc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_outpatsincedisc", "Source": "fu_outpatsincedisc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doortodeviceexcl", "Source": "doortodeviceexcl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "civoriatherapy", "Source": "civoriatherapy", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "reasoncorti", "Source": "reasoncorti", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "sample2", "Source": "sample2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk09md", "Source": "cstk09md", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_antithrombotic3freq", "Source": "fu_antithrombotic3freq", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cdiscdatetime_p", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "merdelay", "Source": "merdelay", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "anticoagafipp", "Source": "anticoagafipp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "eligmer", "Source": "eligmer", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_anticoagdosing", "Source": "fu_anticoagdosing", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_cholredmisseddose", "Source": "fu_cholredmisseddose", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk05bmb", "Source": "cstk05bmb", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "stk08md", "Source": "stk08md", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_monitorbs", "Source": "fu_monitorbs", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "teleconsultenddt_p", "Source": "teleconsultenddt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk02mx", "Source": "stk02mx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk05amb", "Source": "cstk05amb", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_signshf", "Source": "fu_signshf", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_triglyc", "Source": "fu_triglyc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk08mx", "Source": "stk08mx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antihyperifmissed", "Source": "fu_antihyperifmissed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk04mx", "Source": "cstk04mx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_newdiagdm", "Source": "fu_newdiagdm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk03bmr", "Source": "cstk03bmr", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_numfalls", "Source": "fu_numfalls", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk04mb", "Source": "stk04mb", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_waistcircunits", "Source": "fu_waistcircunits", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doortodevicenum", "Source": "doortodevicenum", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk11mx", "Source": "cstk11mx", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "stkop01bmx", "Source": "stkop01bmx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsrecordlater", "Source": "emsrecordlater", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01dmx", "Source": "stkop01dmx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk01md", "Source": "cstk01md", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "stk04mx", "Source": "stk04mx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tlc", "Source": "tlc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aspectm6", "Source": "aspectm6", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aspecttotal", "Source": "aspecttotal", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01gmr", "Source": "stkop01gmr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ichvolumend", "Source": "ichvolumend", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_riskfactors", "Source": "fu_riskfactors", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "disctoarr", "Source": "disctoarr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk02mp", "Source": "stk02mp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_edvisitreasonoth", "Source": "fu_edvisitreasonoth", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "nihsscalc", "Source": "nihsscalc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_discdate_p", "Source": "fu_discdate.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsbps", "Source": "emsbps", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01amd", "Source": "stkop01amd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "covidvaccdate_p", "Source": "covidvaccdate.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_weightunits", "Source": "fu_weightunits", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON>wunk", "Source": "<PERSON><PERSON><PERSON>wunk", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk11md", "Source": "cstk11md", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "cstk09mr", "Source": "cstk09mr", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "scdysphagia", "Source": "scdysphagia", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "clastknown", "Source": "clastknown", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antithrombotic1class", "Source": "fu_antithrombotic1class", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "telerecc", "Source": "telerecc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_numberer", "Source": "fu_numberer", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk01mx", "Source": "stk01mx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk03amp", "Source": "cstk03amp", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "doortodeviceexcep", "Source": "doortodeviceexcep", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01gmb", "Source": "stkop01gmb", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_modifiedrankin", "Source": "fu_modifiedrankin", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk05bme", "Source": "cstk05bme", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "cstk09bmy", "Source": "cstk09bmy", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_patientlocation", "Source": "fu_patientlocation", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsthromcl", "Source": "emsthromcl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cfibflutter", "Source": "cfibflutter", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "thromexptype", "Source": "thromexptype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk05bmx", "Source": "cstk05bmx", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "cstk09amb", "Source": "cstk09amb", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_antidiastopreas", "Source": "fu_antidiastopreas", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk03bmx", "Source": "cstk03bmx", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_bpdias", "Source": "fu_bpdias", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01hmy", "Source": "stkop01hmy", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "aspectintcapsule", "Source": "aspectintcapsule", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_rankinyn", "Source": "fu_rankinyn", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk06mb", "Source": "cstk06mb", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_currentmed", "Source": "fu_currentmed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_strokerehab", "Source": "fu_strokerehab", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antithrombotic2freq", "Source": "fu_antithrombotic2freq", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk03md", "Source": "cstk03md", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_antihypermisseddose", "Source": "fu_antihypermisseddose", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_lvef", "Source": "fu_lvef", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_ptfall", "Source": "fu_ptfall", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_barthel", "Source": "fu_barthel", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_aspstopoth", "Source": "fu_aspstopoth", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_followupdate_p", "Source": "fu_followupdate.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01dmb", "Source": "stkop01dmb", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "bulkset2", "Source": "bulkset2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk01me", "Source": "stk01me", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_cov", "Source": "fu_cov", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_hospfor", "Source": "fu_hospfor", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cperiodq", "Source": "cperiodq", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_30dayadmit_p", "Source": "fu_30dayadmit.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "imagepriordt_p", "Source": "imagepriordt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk05ame", "Source": "cstk05ame", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "aspectnd", "Source": "aspectnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01emb", "Source": "stkop01emb", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_tobaccouse", "Source": "fu_tobaccouse", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ichsurgdt_p", "Source": "ichsurgdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_findings", "Source": "fu_findings", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "nihsssource", "Source": "nihsssource", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01imr", "Source": "stkop01imr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01emd", "Source": "stkop01emd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk02me", "Source": "stk02me", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_rankin_at", "Source": "c_rankin_at", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "earliestcstk01_p", "Source": "earliestcstk01.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk08mr", "Source": "stk08mr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antihypertx", "Source": "fu_antihypertx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01fmr", "Source": "stkop01fmr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_datepostdiscvisit_p", "Source": "fu_datepostdiscvisit.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsmsutpa_p", "Source": "emsmsutpa.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk03amd", "Source": "cstk03amd", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "cstk03bmb", "Source": "cstk03bmb", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "doortodeviceipp", "Source": "doortodeviceipp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_weight", "Source": "fu_weight", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk01mb", "Source": "cstk01mb", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "fu_antithrombotic4", "Source": "fu_antithrombotic4", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dmdxfudt_p", "Source": "dmdxfudt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsbglval", "Source": "emsbglval", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lvo", "Source": "lvo", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk03mb", "Source": "cstk03mb", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "arrivaltonihss", "Source": "arrivaltonihss", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk05amr", "Source": "cstk05amr", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "stk04md", "Source": "stk04md", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_bpsys", "Source": "fu_bpsys", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk06mb", "Source": "stk06mb", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_dietspecify", "Source": "fu_dietspecify", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "etiologyyn", "Source": "etiologyyn", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_statstopoth", "Source": "fu_statstopoth", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk06mr", "Source": "cstk06mr", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "cstk09bmr", "Source": "cstk09bmr", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "cstk08md", "Source": "cstk08md", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "csexgender", "Source": "csexgender", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emssymptomunk", "Source": "emssymptomunk", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "i10prinpcs", "Source": "i10prinpcs", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_noappreason", "Source": "fu_noappreason", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cholesterolprior", "Source": "cholesterolprior", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doortorep", "Source": "doortorep", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_fupstroketype", "Source": "fu_fupstroketype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stk06md", "Source": "stk06md", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "impagepriornd", "Source": "impagepriornd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gs_ivtpalatenc", "Source": "gs_ivtpalatenc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "i10dschstkdx", "Source": "i10dschstkdx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_conducted", "Source": "fu_conducted", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01amy", "Source": "stkop01amy", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "bulkset3", "Source": "bulkset3", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cstk08me", "Source": "cstk08me", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "cstk09mb", "Source": "cstk09mb", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "cstk05mp", "Source": "cstk05mp", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "cstk11mb", "Source": "cstk11mb", "Transformation": null, "Value Map": {"Yes": "1", "No": "0", "true": "1"}, "Computation": null}, {"Target": "stkop01hmb", "Source": "stkop01hmb", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01hmr", "Source": "stkop01hmr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivtpadelay_other", "Source": "ivtpadelay_other", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_antithrom", "Source": "fu_antithrom", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_tobedu", "Source": "fu_tobedu", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01fmy", "Source": "stkop01fmy", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_nomedication", "Source": "fu_nomedication", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01bmy", "Source": "stkop01bmy", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "stkop01gmp", "Source": "stkop01gmp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "imageprior", "Source": "imageprior", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vtepropipc", "Source": "vtepropipc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pneumonia", "Source": "pneumonia", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "los", "Source": "los", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "edpatient", "Source": "edpatient", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ichscore", "Source": "ichscore", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "nimodipine", "Source": "nimodipine", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsgcsnd", "Source": "emsgcsnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "failedthrom", "Source": "failedthrom", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "nihss36iautd", "Source": "nihss36iautd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "clinicaltrialtype", "Source": "clinicaltrialtype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "patori<PERSON><PERSON>", "Source": "patori<PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "facility_display_id", "Source": "facility_display_id", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "form_version", "Source": "form_version", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivh", "Source": "ivh", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mer_mca", "Source": "mer_mca", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "thrombadmin", "Source": "thrombadmin", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "uploaded_by", "Source": "uploaded_by", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "antithrombotic1class", "Source": "classantithrombotic1", "Transformation": null, "Value Map": {"1": "Antiplatelet", "2": "Anticoagulant"}, "Computation": null}, {"Target": "antithrombotic1freq", "Source": "freqantithrombotic1", "Transformation": null, "Value Map": {"2": "Every Day", "3": "2 times a day", "4": "3 times a day", "5": "4 times a day", "6": "Other", "7": "Unknown"}, "Computation": null}, {"Target": "antithrombotic2class", "Source": "classantithrombotic2", "Transformation": null, "Value Map": {"1": "Antiplatelet", "2": "Anticoagulant"}, "Computation": null}, {"Target": "antithrombotic2freq", "Source": "freqantithrombotic2", "Transformation": null, "Value Map": {"2": "Every Day", "3": "2 times a day", "4": "3 times a day", "5": "4 times a day", "6": "Other", "7": "Unknown"}, "Computation": null}, {"Target": "antithrombotic3class", "Source": "classantithrombotic3", "Transformation": null, "Value Map": {"1": "Antiplatelet", "2": "Anticoagulant"}, "Computation": null}, {"Target": "priorantithrom1class", "Source": "priorantithrom1class", "Transformation": null, "Value Map": {"1": "Antiplatelet", "2": "Anticoagulant"}, "Computation": null}, {"Target": "priorantithrom2class", "Source": "priorantithrom2class", "Transformation": null, "Value Map": {"1": "Antiplatelet", "2": "Anticoagulant"}, "Computation": null}, {"Target": "meddmtx1", "Source": "meddmtx1", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "meddmtx2", "Source": "meddmtx2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "meddmtx3", "Source": "meddmtx3", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "meddmtx4", "Source": "meddmtx4", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dmduration", "Source": "dmduration", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "scheduledformname", "Source": "scheduled_form_name", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "newdmdx", "Source": "newdmdx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emssuspstroke", "Source": "emssuspstroke", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emssevdest", "Source": "emssevdest", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsdispatch", "Source": "emsdispatch", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsposlvo", "Source": "emsposlvo", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "screenoutcome", "Source": "screenoutcome", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsbglnd", "Source": "emsbglnd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pmtused", "Source": "pmtused", "Transformation": null, "Value Map": {"1": "Concurrently", "2": "Retrospectively", "3": "Combination"}, "Computation": null}, {"Target": "cstkaddcm", "Source": "cstkaddcm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "age", "Source": "age", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "initialadmittingservice", "Source": "intadmit", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "intadmitoth", "Source": "intadmitoth", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "telestatus", "Source": "telestatus", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "prevstrokehistory", "Source": "medhiststroke", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "updatedby", "Source": "updated_by", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "firstimage", "Source": "firstimage", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "doac", "Source": "doac", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "teamarrivetime", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "raceasian", "Source": "asian", "Transformation": null, "Value Map": {"1": "Yes"}, "Computation": null}, {"Target": "tiaduration", "Source": "tiaduration", "Transformation": null, "Value Map": {"1": "Less then 10 minutes", "2": "10 – 59 minutes", "3": ">= 60 minutes", "4": "ND"}, "Computation": null}, {"Target": "comfortprior", "Source": "comfortprior", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "priorantideptype", "Source": "priorantideptype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "localert", "Source": "localert", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "caresettingoth", "Source": "caresettingoth", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "antithrombotic2", "Source": "medantithrombotic2", "Transformation": null, "Value Map": {"3": "aspirin", "4": "ASA/dipyridamole (Aggrenox)", "6": "clopidogrel (Plavix)", "7": "ticlopidine (Ticlid)", "8": "Unfractionated heparin IV", "9": "full dose LMW heparin", "5": "warfarin (Coumadin) ", "16": "dabigatran (Pradaxa)", "15": "a<PERSON><PERSON><PERSON>", "12": "fondaparinux (Arixtra)", "14": "rivaroxaban (Xarelto)", "19": "<PERSON><PERSON><PERSON><PERSON> (Eliquis)", "18": "<PERSON><PERSON><PERSON><PERSON> (Refludan)", "dipyridamole/aspirin (Aggrenox)": "ASA/dipyridamole (Aggrenox)", "prasugrel (Effient) *contraindication in stroke and TIA": "prasug<PERSON> (Effient)", "Other anticoagulant": "Other Anticoagulant"}, "Computation": null}, {"Target": "antithrombotic3", "Source": "medantithrombotic3", "Transformation": null, "Value Map": {"3": "aspirin", "4": "ASA/dipyridamole (Aggrenox)", "6": "clopidogrel (Plavix)", "7": "ticlopidine (Ticlid)", "8": "Unfractionated heparin IV", "9": "full dose LMW heparin", "5": "warfarin (Coumadin) ", "16": "dabigatran (Pradaxa)", "15": "a<PERSON><PERSON><PERSON>", "12": "fondaparinux (Arixtra)", "14": "rivaroxaban (Xarelto)", "19": "<PERSON><PERSON><PERSON><PERSON> (Eliquis)", "18": "<PERSON><PERSON><PERSON><PERSON> (Refludan)", "dipyridamole/aspirin (Aggrenox)": "ASA/dipyridamole (Aggrenox)", "prasugrel (Effient) *contraindication in stroke and TIA": "prasug<PERSON> (Effient)", "Other anticoagulant": "Other Anticoagulant"}, "Computation": null}, {"Target": "priorantithrom1", "Source": "priorantithrom1", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "priorantithrom2", "Source": "priorantithrom2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "telewhoprovide", "Source": "telewhoprovide", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vitk", "Source": "vitk", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "classdmtx1", "Source": "classdmtx1", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "classdmtx2", "Source": "classdmtx2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "classdmtx3", "Source": "classdmtx3", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "classdmtx4", "Source": "classdmtx4", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_comfortonly2", "Source": "ccomfortonly2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hawaiian", "Source": "hawaiian", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "earliestcstk01", "Source": "earliestcstk01", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "earliestcstk03", "Source": "earliestcstk03", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emssev<PERSON>le", "Source": "emssev<PERSON>le", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cholreducstatin", "Source": "statinmed", "Transformation": null, "Value Map": {"Statin": "Yes", "1": "Yes"}, "Computation": null}, {"Target": "imagepriorcomp", "Source": "imagepriorcomp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON>", "Source": "created_by", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "patorien", "Source": "patorien", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "clientfileid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "datasetname", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "antithrombotic1", "Source": "medantithrombotic1", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsdestdecoth", "Source": "emsdestdecoth", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "rankindschtype", "Source": "rankindschtype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivtpadelay_hro", "Source": "ivtpadelay_hro", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "npi_att", "Source": "npi_att", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cipp", "Source": "cipp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "antithrom2type", "Source": "antithrom2type", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "gs_tenecreason_ot", "Source": "o<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cholreductx", "Source": "cholredtx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "npi_other", "Source": "npi_other", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "covidvaccmanu", "Source": "covidvaccmanu", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mer_ica", "Source": "mer_ica", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "lpaobt", "Source": "lpaobt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "medrecn", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "zipcode", "Source": "zip", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "npi_disc", "Source": "npi_disc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "telestroketype", "Source": "telestroketype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "npi_ed", "Source": "npi_ed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "npi_strokenppa", "Source": "npi_strokenppa", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "i10othpcstab", "Source": "i10othpcstab", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "npi_neurosurgeon", "Source": "npi_neurosurgeon", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "i10prinpcstab", "Source": "i10prinpcstab", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_cartocc", "Source": "cartocc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "npi_adm", "Source": "npi_adm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "npi_int", "Source": "npi_int", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "i10admitdxtab", "Source": "i10admitdxtab", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "i10prindxtab", "Source": "i10prindxtab", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "race", "Source": "race", "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": "sp_race"}, "is_computation": true}]}, {"Target": "gs_thrombo", "Source": "thromboused", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "sp_race", "Source": "race", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "teleconmtd", "Source": "teleconmtd", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "firstcare", "Source": "firstcare", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "notadmit", "Source": "notadm", "Transformation": null, "Value Map": {"1": "Yes, not admitted", "2": "No, patient admitted as inpatient"}, "Computation": null}, {"Target": "opem", "Source": "opem", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dob", "Source": "dob", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "admitdate", "Source": "admdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "arrivaldatetime", "Source": "arrdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dcdatetime", "Source": "disdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ptlastknownwelldt", "Source": "lastknownwell", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ptstrokesymptomdatetime", "Source": "symptomdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivthromboinitiateddt", "Source": "iv<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "initvteprophylaxisdatetime", "Source": "vtedate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "reperfusiondatetime", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": "iatpainitiationdatetime"}, "is_computation": true}]}, {"Target": "createddatetime", "Source": "created_dt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "antidepmed", "Source": "presantidepmed", "Transformation": null, "Value Map": {"1": "Yes, SSRI", "2": "Yes, any other antidepressant class", "3": "No/ND"}, "Computation": null}, {"Target": "ctcompletedate", "Source": "ctcompdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "iatpainitiationdatetime", "Source": "iatpadt", "Transformation": null, "Value Map": {"5": "MM/DD/YYYY HH24:MM", "3": "MM/DD/YYYY", "0": "Unknown"}, "Computation": null}, {"Target": "ivtpaorderdate", "Source": "ivtpaordereddt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ctorderdatetime", "Source": "ctorddt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ctinterpretdatetime", "Source": "ctrepdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ecgcompldatetime", "Source": "ecgcompdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ecgorderdatetime", "Source": "ecgorddt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "assessdatetime", "Source": "edassessdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "labcompdatetime", "Source": "labcompdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "laborderdatetime", "Source": "laborddt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "neurosurgdatetime", "Source": "neuroassessdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "teamactivedatetime", "Source": "teamactivatedt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "xraycompdatetime", "Source": "cxrcompdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "xrayorderdatetime", "Source": "c<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "refhospdischargedatetime", "Source": "referdischarge", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "refhosparrivdatetime", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "merimagedate", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "updateddatetime", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "firstpassdatetime", "Source": "firstpassdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "initialnihssscoredatetime", "Source": "nihssdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_artpuncdt", "Source": "artpuncdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_discdatetime", "Source": "cdiscdatetime", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "mer_ticidt", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tq_encounterdt", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdate2", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdate19", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_posbrain", "Source": "posbrain", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdate7", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_nihssdt_precision", "Source": "nihssdt.p", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate5_utd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdate15", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdate14", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate23_utd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdateutd17", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate1_utd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate22", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdate5", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdateutd11", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate9_utd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdateutd18", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_princicd10procedurdt", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdate16", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate12_utd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdate24", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdateutd13", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate9", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate15", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdateutd21", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdateutd6", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate14_utd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdate9", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate24_utd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdateutd3", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdateutd14", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate20", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate19_utd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate16", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate2", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdate1", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate8", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate8_utd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate23", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "c_nimodipine_dt", "Source": "nimodipinedt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdate23", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdate3", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdate21", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdate10", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_princproced_dtutd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdate12", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate3", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate21", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdate4", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdate17", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate7_utd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate19", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdate11", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate3_utd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdate6", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdateutd7", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdate8", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate14", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdateutd9", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate10", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate11_utd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdateutd4", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate12", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdateutd20", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate24", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate15_utd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdate22", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdate18", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate22_utd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdateutd15", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate18", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate13", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdateutd2", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate11", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate18_utd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate13_utd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate17_utd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdate13", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate6", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdateutd24", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate6_utd", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate5", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_otherproceduredate1", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdateutd10", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "jc_othericd10procdateutd22", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "imagecdt", "Source": "imagecdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "neuroactivdt", "Source": "neuroactivdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "neuroar<PERSON>t", "Source": "neuroar<PERSON>t", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "modified_dt", "Source": "modified_dt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "teleconsultdt", "Source": "teleconsultdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emssymptomsdt", "Source": "emssymptomsdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "departeddt", "Source": "departeddt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "transreqst", "Source": "transreqst", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emslkwdt", "Source": "emslkwdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "transrefhosp", "Source": "transrefhosp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_postdismrsdate", "Source": "fu_postdismrsdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsmsutpa", "Source": "emsmsutpa", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "inrdt", "Source": "inrdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_eddate1", "Source": "fu_eddate1", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ichsurgdt", "Source": "ichsurgdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsmsuct", "Source": "emsmsuct", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsarrivescenedt", "Source": "emsarrivescenedt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsfirstmedcont", "Source": "emsfirstmedcont", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "redosedt", "Source": "redosedt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "image<PERSON><PERSON><PERSON><PERSON>", "Source": "image<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dmdxfudt", "Source": "dmdxfudt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_<PERSON><PERSON>h", "Source": "fu_<PERSON><PERSON>h", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_datepostdisc", "Source": "fu_datepostdisc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_fupstrokedt", "Source": "fu_fupstrokedt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ichscoredt", "Source": "ichscoredt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "covidvaccdate", "Source": "covidvaccdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "teleconsultenddt", "Source": "teleconsultenddt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_lvefdate", "Source": "fu_lvefdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_eddate", "Source": "fu_eddate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_datefollowcomp", "Source": "fu_datefollowcomp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "priorantipltdt", "Source": "priorantipltdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "b<PERSON><PERSON><PERSON><PERSON>", "Source": "b<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_followupdate", "Source": "fu_followupdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "televideo", "Source": "televideo", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_comprankindate", "Source": "fu_comprankindate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsscenedepdt", "Source": "emsscenedepdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "transarrdt", "Source": "transarrdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emscallrecdt", "Source": "emscallrecdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "clastknowndttm", "Source": "clastknowndttm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "telerespdt", "Source": "telerespdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "sustsysbpdt", "Source": "sustsysbpdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ipcdt", "Source": "ipcdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "endend<PERSON><PERSON>", "Source": "endend<PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "telethrombdt", "Source": "telethrombdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "throm<PERSON><PERSON><PERSON>", "Source": "throm<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "referar<PERSON><PERSON>", "Source": "referar<PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "pcmodifierdate", "Source": "pcmodifierdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "transrechosp", "Source": "transrechosp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_datepostdiscvisit", "Source": "fu_datepostdiscvisit", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsprehospemsdt", "Source": "emsprehospemsdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "opencounterdt", "Source": "opencounterdt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_admitdate", "Source": "fu_admitdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "datealert", "Source": "datealert", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_discdate", "Source": "fu_discdate", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "occlusionsite", "Source": "occlusionsite", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cholesterolreducertype_1", "Source": "cholredtype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hxinfectopt", "Source": "hxinfectopt", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "iatype", "Source": "iatype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "strokerelateddiagnosis", "Source": "stroketype", "Transformation": null, "Value Map": {"2": "Ischemic Stroke", "3": "Transient Ischemic Attack (< 24 hours)", "4": "Subarachnoid Hemorrhage", "5": "Intracerebral Hemorrhage", "6": "Stroke Not Otherwise Specified", "7": "No Stroke Related Diagnosis", "8": "Elective Carotid Intervention Only", "Elective Carotid Intervention only": "Elective Carotid Intervention Only", "Ischemic stroke": "Ischemic Stroke", "No stroke related diagnosis": "No Stroke Related Diagnosis", "Stroke not otherwise specified": "Stroke Not Otherwise Specified"}, "Computation": null}, {"Target": "npi_neurologist", "Source": "npi_neurologist", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "prehdata", "Source": "prehdata", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dschothfac", "Source": "dschothfac", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "etiologydocmntd", "Source": "etiologycrypt", "Transformation": null, "Value Map": {"1": "Yes", "2": "No"}, "Computation": [{"Transform_Type": "conditional_and", "Args": {"true_val": null, "false_val": "etiologydocmntd", "fields": [{"name": "etiologydocmntd", "values": ["Multiple potential etiologies identified", "Stroke of undetermined etiology", "Unspecified"]}]}, "is_computation": null}]}, {"Target": "otheranticoag", "Source": "otheranticoag", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "initscdysphagia", "Source": "initscdysphagia", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivtpadelay_mrspec", "Source": "ivtpadelay_mrspec", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ethnicys", "Source": "ethnicys", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "i10otherdxtab", "Source": "i10otherdxtab", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "methodpatientarrival", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": {"1": "EMS from home/scene", "10": "Mobile Stroke Unit", "2": "Private transport/taxi/other from home/scene", "3": "Transfer from other hospital", "9": "ND or unknown"}, "Computation": null}, {"Target": "dischargestatus", "Source": "dschstat", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dischargedisposition", "Source": null, "Transformation": null, "Value Map": null, "Computation": [{"Transform_Type": "additional_field", "Args": {"Source": "dischargestatus"}, "is_computation": true}]}, {"Target": "ivcomp", "Source": "ivcomp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "telecomment", "Source": "telecomment", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "foley", "Source": "foley", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "crace", "Source": "crace", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "vascperfimage", "Source": "vascperfimage", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsdestdec", "Source": "emsdestdec", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "corti", "Source": "corti", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dischargecodestk", "Source": "dischargecodestk", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "prehospscreen", "Source": "prehospscreen", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "reasonnoantithrom", "Source": "reasonnoantithrom", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "hospname", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "merreasoth", "Source": "merreasoth", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "noivhosp2", "Source": "noivhosp2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "noivhosp", "Source": "noivhosp", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "addrelexcl", "Source": "addrelexcl", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cardiacmon", "Source": "shortcardrhy<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": {"1": "Performed during this admission or in the 3 months prior", "2": "Planned post discharge", "3": "Not performed or planned"}, "Computation": null}, {"Target": "hypercoagtest", "Source": "hypercoagtest", "Transformation": null, "Value Map": {"1": "Performed during this admission or in the 3 months prior", "2": "Planned post discharge", "3": "Not performed or planned"}, "Computation": null}, {"Target": "carotidimaging", "Source": "carotidimaging", "Transformation": null, "Value Map": {"1": "Performed during this admission or in the 3 months prior", "2": "Planned post discharge", "3": "Not performed or planned"}, "Computation": null}, {"Target": "cardiacrhythmmonitoring", "Source": "extcardrhythmmon", "Transformation": null, "Value Map": {"1": "Performed during this admission or in the 3 months prior", "2": "Planned post discharge", "3": "Not performed or planned"}, "Computation": null}, {"Target": "cardiacultrasound", "Source": "cardultrasound", "Transformation": null, "Value Map": {"1": "Performed during this admission or in the 3 months prior", "2": "Planned post discharge", "3": "Not performed or planned"}, "Computation": null}, {"Target": "gs_tenecreason", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "icmilr", "Source": "icmilr", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "intracranvasc", "Source": "intracranvasc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "cardrevasc", "Source": "cardrevasc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "addlcomment", "Source": "addlcomment", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "otheretiology", "Source": "etiologyother", "Transformation": null, "Value Map": {"1": "Dissection", "2": "Hypercoagualability", "3": "Other (e.g., vasculopathy or other hematologic disorders)"}, "Computation": null}, {"Target": "ivtpadelayeli<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "ivtpadelay_er", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "comptransf", "Source": "comptransf", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "surgicalichtype", "Source": "surgicalichtype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ivtpadelay_erspec", "Source": "ivtpadelay_erspec", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "strokepatientid", "Source": "patient_display_id", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "uuid", "Source": "uuid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "case_id", "Source": "case_id", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "master_patient_id", "Source": "master_patient_id", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "facpatid", "Source": "facpatid", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "locationofstrokesymptoms", "Source": "patientlocation", "Transformation": null, "Value Map": {"1": "Not in a healthcare setting", "2": "Another acute care facility", "3": "Chronic health care facility", "5": "Outpatient healthcare setting", "4": "Stroke occurred after hospital arrival (in ED/Obs/inpatient)", "9": "ND or Cannot be determined", "ND or Cannot be Determined": "ND or Cannot be determined"}, "Computation": null}, {"Target": "caddstroke", "Source": "caddstroke", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsagencylist", "Source": "emsagencylist", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "transferhospitalname", "Source": "transfername", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "noivthromoth", "Source": "noivthromoth", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "dmmedspecadm", "Source": "dmmedspecadm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "noivthromoth2", "Source": "noivthromoth2", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "caresetting", "Source": "caresetting", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "socdetareas", "Source": "socdetareas", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "biomeimportdt", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "activeinfec", "Source": "activeinfec", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "smokingmed", "Source": "smokingmed", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "payor", "Source": "paysource", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ichet<PERSON><PERSON><PERSON>", "Source": "ichet<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "teledest", "Source": "teledest", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "ambulatadmit", "Source": "ambulatadmit", "Transformation": null, "Value Map": {"1": "Able to ambulate independently (no help from another person) w/ or w/o device", "2": "With assistance (from person)", "3": "Unable to ambulate", "4": "ND"}, "Computation": null}, {"Target": "ambulatorystatusprior", "Source": "ambulatprior", "Transformation": null, "Value Map": {"1": "Able to ambulate independently (no help from another person) w/ or w/o device", "2": "With assistance (from person)", "3": "Unable to ambulate", "9": "ND"}, "Computation": null}, {"Target": "funcstatus", "Source": "funcstatus", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "careentityid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "tenantid", "Source": null, "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "covidvaccdoc", "Source": "covidvacc", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "noguideddosereasonintoltostatin", "Source": "noguidedose", "Transformation": null, "Value Map": {"Intolerant to moderate (greater than 75yr) or high (less than or equal to 75yr) intensity statin": "Yes", "1": "Yes"}, "Computation": null}, {"Target": "strokeetiology", "Source": "etiology", "Transformation": null, "Value Map": {"1": "1: Large-artery atherosclerosis (e.g., carotid or basilar artery stenosis)", "2": "2: Cardioembolism (e.g., atrial fibrillation/flutter, prosthetic heart valve, recent MI)", "3": "3: Small-vessel disease (e.g., Subcortical or brain stem lacunar infarction <1.5 cm)", "4": "4: Stroke of other determined etiology", "5": "5: Cryptogenic Stroke"}, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "whynoatrialmeds", "Source": "whynoatrialmeds", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "htntx", "Source": "htntx", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "emsaddcm", "Source": "emsaddcm", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "<PERSON><PERSON><PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "fu_comprankin", "Source": "fu_comprankin", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "procoagulanttype", "Source": "procoagulanttype", "Transformation": null, "Value Map": null, "Computation": null}, {"Target": "procoagulantpriortype", "Source": "procoagulantpriortype", "Transformation": null, "Value Map": null, "Computation": null}]}}