import logging

from ingestion.data_dictionaries import read_data_dict
from ingestion.config import read_config

logging.basicConfig(
    format='%(asctime)s %(message)s',
    datefmt='%m/%d/%Y %I:%M:%S %p'
)
logging.root.setLevel(logging.INFO)


def compare_dictionaries(old_dict, new_dict, columns, on, sheet="Elements"):
    old_dic = read_data_dict(old_dict, sheet)[columns]
    new_dic = read_data_dict(new_dict, sheet)[columns]
    return old_dic.merge(new_dic, on=on, how="outer")


def fetch_biome_schema(config_file):
    logging.info("Fetching biome schema")

    biome_schema = read_config(config_file, 'biome_schema')[['Field']]
    biome_schema['Field'] = biome_schema['Field'].str.lower()
    biome_schema = biome_schema.rename(columns={'Field': 'Schema Field'})
    return biome_schema


def fetch_custom_fields(config_file):
    logging.info("Fetching custom fields")

    custom_fields = read_config(config_file, 'custom_fields')
    columns = ['Field', 'Derivative Field', 'Derivative Value', 'Derivative Value Code', 'Reference Field']
    for column in columns:
        if 'Code' not in column:
            custom_fields[column] = custom_fields[column].str.lower()

    return custom_fields


def fetch_rename_fields(config_file):
    logging.info("Fetching rename fields")

    rename_fields = read_config(config_file, 'rename_fields')
    rename_fields['New Field'] = rename_fields['New Field'].str.lower()
    return rename_fields


def fetch_config(conf_path):
    logging.info(f"Fetching {config_path} configurations ")

    conf = {
        "biome_schema": fetch_biome_schema(conf_path),
        "custom_fields": fetch_custom_fields(conf_path),
        "rename_fields": fetch_rename_fields(conf_path)
    }
    return conf


def find_biome_name(configurations, data):
    # Read Config file to determine the biome names
    extract_col = ['Derivative Field', 'Reference Field']
    biome_schema = configurations['biome_schema']
    custom_fields = configurations['custom_fields'][extract_col]
    rename_fields = configurations['rename_fields']

    # Find occurrences in biome_schema and rename_fields sections
    data['Old Short Name'] = data['Old Short Name'].str.lower()
    data = data.merge(rename_fields, left_on='Old Short Name', right_on='New Field', how='left')
    data = data.merge(biome_schema, left_on='Old Short Name', right_on='Schema Field', how='left')

    # Set Biome name accordingly
    data['Biome Field Name'] = data.apply(
        lambda x: x['Old Field'] if x['New Field'] == x['Old Short Name']
        else (x['Schema Field'] if x['Schema Field'] == x['Old Short Name']
              else ''), axis=1
    )

    # Determine fields which are being used in making custom fields
    data = data.merge(custom_fields, left_on='Old Short Name', right_on='Derivative Field', how='left')

    data['Biome Field Name'] = data.apply(
        lambda x: 'Custom Field Check Derivative Field "' + str(x['Derivative Field']) + '"'
        if x['Derivative Field'] == x['Old Short Name']
        else x['Biome Field Name'], axis=1
    )

    # For the reference fields where it is not equal to derivative field
    mask = (
        (data['Derivative Field'] != data['Reference Field']) &  # Values are not equal
        ~(data['Derivative Field'].isna() & data['Reference Field'].isna()))

    for index, row in data[mask].iterrows():
        match_index = data[data['Old Short Name'] == row['Reference Field']].index

        if not match_index.empty:
            data.at[match_index[0], 'Biome Field Name'] \
                = 'Custom Field Check Reference Field "' + row['Reference Field'] + '"'
    # data.to_excel("DD Comparison.xlsx", index=False)

    # drop irrelevant columns
    data.drop(columns={'New Field', 'Old Field', 'Schema Field', 'Derivative Field', 'Reference Field'}, inplace=True)

    return data


def find_schema_missing_fields(configurations, fields):
    """
    This function determines the fields which are neither in
    sections including cusotm_fields, rename_fields, and shcema_fields nor in data dictionary
    """

    extract_col = ['Field', 'Derivative Field', 'Reference Field']
    biome_schema = configurations['biome_schema']
    custom_fields = configurations['custom_fields'][extract_col]
    rename_fields = configurations['rename_fields']

    biome_schema = biome_schema[~biome_schema['Schema Field'].isin(rename_fields['Old Field'].str.lower().tolist())]
    biome_schema = biome_schema[~biome_schema['Schema Field'].isin(custom_fields['Field'].str.lower().tolist())]
    missing_fields = biome_schema[~biome_schema['Schema Field'].isin(fields['Old Short Name'].str.lower().tolist())]
    return missing_fields


def find_field_mapping(old_dict, new_dict, configurations):
    logging.info("Find Field Mapping for new version")

    columns_to_compare = ['Element Reference', 'Short Name']
    write_map_fields = True
    write_missing_fields = True

    field_to_map = compare_dictionaries(old_dict, new_dict,
                                        columns=columns_to_compare, on='Element Reference')

    field_to_map = field_to_map.rename(columns={'Element Reference': 'Sequence ID',
                                                'Short Name_x': 'Old Short Name',
                                                'Short Name_y': 'New Short Name'})
    mapped_fields = find_biome_name(configurations, field_to_map)

    if write_map_fields:
        mapped_fields.to_excel('Version Field Mapping.xlsx', index=False)
        logging.info("Field value mapping is written into Version Field Mapping.xlsx successfully.")

    logging.info("Find missing schema fields")
    schema_missing_fields = find_schema_missing_fields(configurations, mapped_fields)

    if not schema_missing_fields.empty:
        logging.info(f"The number of missing fields are: {schema_missing_fields.shape[0]}")

        if write_missing_fields:
            schema_missing_fields.to_excel("Schema Missing Fields.xlsx", index=False)
            logging.info("Missing schema fields are written into Schema Missing Fields.xlsx successfully.")
        else:
            print(schema_missing_fields)
    else:
        print("None of the fields are missing.")


def find_field_value_mapping(old_dict, new_dict, configurations):
    logging.info("Find Field Value Mapping for new version")

    columns = ['Element Reference', 'Code', 'Selection Name']
    write = True

    field_values = compare_dictionaries(old_dict, new_dict,
                                        columns=columns, sheet="Selections", on=['Element Reference', 'Code'])
    field_values = field_values.sort_values(['Element Reference', 'Code'])

    columns_to_compare = ['Element Reference', 'Short Name']
    dic_comparison = compare_dictionaries(old_dict, new_dict, columns=columns_to_compare, on='Element Reference')

    map_field_values = field_values.merge(dic_comparison, on='Element Reference', how='left')
    map_field_values = map_field_values.rename(
        columns={'Short Name_x': 'DD Name', 'Short Name_y': 'New DD Name',
                 'Selection Name_x': 'Old Selection Name', 'Selection Name_y': 'New Selection Name'})
    map_field_values['DD Name'] = map_field_values['DD Name'].str.lower()
    map_field_values['New DD Name'] = map_field_values['New DD Name'].str.lower()

    # Read Config file to determine the biome names
    extract_col = ['Field', 'Derivative Field', 'Derivative Value', 'Derivative Value Code', 'Reference Field']
    biome_schema = configurations['biome_schema']
    custom_fields = configurations['custom_fields'][extract_col]
    rename_fields = configurations['rename_fields']

    map_field_values = map_field_values.merge(biome_schema, left_on='DD Name', right_on='Schema Field', how='left')
    map_field_values = map_field_values.merge(rename_fields, left_on='DD Name', right_on='New Field', how='left')

    custom_fields['Derivative Value Code'] = custom_fields['Derivative Value Code'].astype(str)
    map_field_values = map_field_values.merge(custom_fields, left_on=['Code', 'DD Name'],
                                              right_on=['Derivative Value Code', 'Derivative Field'], how='left')

    map_field_values['Biome Name'] = map_field_values.apply(
        lambda x: x['Old Field'] if x['New Field'] == x['DD Name']
        else (x['Schema Field'] if x['Schema Field'] == x['DD Name']
              else ''), axis=1
    )

    map_field_values['Biome Name'] = map_field_values.apply(
        lambda x: x['Field'] if x['DD Name'] == x['Derivative Field']
        else x['Biome Name'], axis=1
    )
    # map_field_values.to_excel("DD Field Value Comparison.xlsx", index=False)

    # drop irrelevant columns
    columns = ['New Field', 'Old Field', 'Schema Field']
    column_list = columns + extract_col
    map_field_values.drop(columns=column_list, inplace=True)
    map_field_values = map_field_values.sort_values(['Element Reference', 'New Selection Name'])

    if write:
        map_field_values.to_excel('Version Field Value Mapping.xlsx', index=False)
        logging.info("Field Value Mappings are written into Version Field Value Mapping.xlsx successfully.")


def extract_biome_names(configurations, new_dict):
    # Read Config file to determine the biome names
    extract_col = ['Derivative Field', 'Reference Field', 'Field']
    biome_schema = configurations['biome_schema']
    custom_fields = configurations['custom_fields'][extract_col]
    rename_fields = configurations['rename_fields']

    # Read data dictionary's Element sheet
    new_fields = read_data_dict(new_dict, "Elements")[["Element Reference", "Short Name"]]

    # Find occurrences in biome_schema and rename_fields sections
    new_fields['Short Name'] = new_fields['Short Name'].str.lower()
    new_fields = new_fields.merge(rename_fields, left_on='Short Name', right_on='New Field', how='left')
    new_fields = new_fields.merge(biome_schema, left_on='Short Name', right_on='Schema Field', how='left')

    # Determine fields which are being used in making custom fields
    new_fields = new_fields.merge(custom_fields, left_on='Short Name', right_on='Derivative Field', how='left')

    # Set Biome name accordingly
    new_fields['Biome Field Name'] = new_fields.apply(
        lambda x: x['Old Field'] if x['New Field'] == x['Short Name']
        else (x['Schema Field'] if x['Schema Field'] == x['Short Name']
              else ''), axis=1
    )

    new_fields['Biome Field Name'] = new_fields.apply(
        lambda x: x['Field'] if x['Derivative Field'] == x['Short Name']
        else x['Biome Field Name'], axis=1)

    new_fields['Comments'] = new_fields.apply(
        lambda x: "Rename Fields" if x['New Field'] == x['Short Name']
        else ("Custom Field" if x['Derivative Field'] == x['Short Name'] else ""), axis=1)

    new_fields = new_fields.sort_values(['Element Reference', 'Short Name', 'Biome Field Name'])
    new_fields.to_excel("Extracted Biome Fields.xlsx", index=False)
    logging.info("Biome field names are fetched successfully!")


if __name__ == "__main__":

    old_dict_path = "ICD23.xlsx"
    new_dict_path = "ICD30.xlsx"
    config_path = "icd23"

    config = fetch_config(config_path)

    # This function finds the mappings of fields between old and new versions.
    # find_field_mapping(old_dict_path, new_dict_path, config)

    # This function finds the mappings of values along with field names between new and old versions.
    # find_field_value_mapping(old_dict_path, new_dict_path, config)

    # This function extracts biome names to utilize in new translator config from the existing config file
    extract_biome_names(config, new_dict_path)
