[{"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFTPR", "valueDataDict": "Initial device implant"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFibClass", "valueDataDict": "Paroxysmal"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFibFlutterCardioPlans", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AbConduction", "valueDataDict": "true"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ArrivalDate", "valueDataDict": "2025-02-12"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AtrialRhythm", "valueDataDict": "Sinus"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "B<PERSON>oDev", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "BUN", "valueDataDict": "33"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "BUNND", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "BUN_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "BradIndPres", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "CABG", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "CSLVLead", "valueDataDict": "Successfully implanted"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ClinicalTrial", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ConditionHx", "valueDataDict": "Atrial fibrillation"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "ConditionHx", "valueDataDict": "Cardiac arrest"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "ConditionHx", "valueDataDict": "Cardiomyopathy - ischemic"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "ConditionHx", "valueDataDict": "Cardiomyopathy - non-ischemic"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "ConditionHx", "valueDataDict": "Cerebrovascular disease"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "ConditionHx", "valueDataDict": "Chronic lung disease"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "ConditionHx", "valueDataDict": "Coronary artery disease"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "ConditionHx", "valueDataDict": "Currently on dialysis"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "ConditionHx", "valueDataDict": "Diabetes mellitus"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 10, "shortName": "ConditionHx", "valueDataDict": "Familial history of non-ischemic cardiomyopathy"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 11, "shortName": "ConditionHx", "valueDataDict": "Familial syndrome-risk of sudden death"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 12, "shortName": "ConditionHx", "valueDataDict": "Heart failure"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 13, "shortName": "ConditionHx", "valueDataDict": "Inotropic support"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 14, "shortName": "ConditionHx", "valueDataDict": "Myocardial infarction"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 15, "shortName": "ConditionHx", "valueDataDict": "Paroxysmal SVT history"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 16, "shortName": "ConditionHx", "valueDataDict": "Valvular heart disease"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 17, "shortName": "ConditionHx", "valueDataDict": "Structural abnormalities"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 18, "shortName": "ConditionHx", "valueDataDict": "Syncope"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 19, "shortName": "ConditionHx", "valueDataDict": "Syndromes of sudden death"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 20, "shortName": "ConditionHx", "valueDataDict": "Ventricular fibrillation (not due to reversible cause)"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 21, "shortName": "ConditionHx", "valueDataDict": "Ventricular tachycardia"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ConditionHxOccurence", "valueDataDict": "true"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "ConditionHxOccurence", "valueDataDict": "true"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "ConditionHxOccurence", "valueDataDict": "true"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "ConditionHxOccurence", "valueDataDict": "true"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "ConditionHxOccurence", "valueDataDict": "true"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 10, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 11, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 12, "shortName": "ConditionHxOccurence", "valueDataDict": "true"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 13, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 14, "shortName": "ConditionHxOccurence", "valueDataDict": "true"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 15, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 16, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 17, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 18, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 19, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 20, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 21, "shortName": "ConditionHxOccurence", "valueDataDict": "true"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "CoronaryAngioResults", "valueDataDict": "Significant disease"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DCDate", "valueDataDict": "2025-02-13"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DCLocation", "valueDataDict": "Home"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DCStatus", "valueDataDict": "Alive"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedAdmin", "valueDataDict": "No - Medical Reason"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedAdmin", "valueDataDict": "No - Medical Reason"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 13, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 14, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 15, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Aldosterone Antagonist"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedID", "valueDataDict": "Angiotensin Converting Enzyme Inhibitor"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedID", "valueDataDict": "Angiotensin Receptor-Neprilysin Inhibitor"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedID", "valueDataDict": "Angiotensin II Receptor Blocker"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedID", "valueDataDict": "Renin Inhibitor"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedID", "valueDataDict": "Antiarrhythmic Drug"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedID", "valueDataDict": "Antiplatelet Agent"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedID", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedID", "valueDataDict": "Beta Blocker"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedID", "valueDataDict": "Betrixaban"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedID", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 13, "shortName": "DC_MedID", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 14, "shortName": "DC_MedID", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 15, "shortName": "DC_MedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "34525", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "DOB", "valueDataDict": "1900-01-01"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "DeviceImplanted", "valueDataDict": "true"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ECG", "valueDataDict": "true"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ECGNormal", "valueDataDict": "true"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "EPStudy", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ElecDevImpPath", "valueDataDict": "Implantable cardioverter-defibrillator"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "EnrolledStudy", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "Final_Device_Type", "valueDataDict": "CRT-D"}, {"NCDRPatientId": "34525", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "FirstName", "valueDataDict": "<PERSON>"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "GenOpFName", "valueDataDict": "<PERSON>"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "GenOpLName", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "GenOpNPI", "valueDataDict": "**********"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HGB", "valueDataDict": "10.2"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HGBND", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HGB_unit", "valueDataDict": "g/dL"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HIPS", "valueDataDict": "Medicare Advantage (Part C)|Military health care"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HealthIns", "valueDataDict": "true"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HemoInstability", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "HisBunLead", "valueDataDict": "Not attempted"}, {"NCDRPatientId": "34525", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ICDImpID", "valueDataDict": "4612"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ICDImpSerNo", "valueDataDict": "810126417"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ICDIndication", "valueDataDict": "Primary prevention"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "INR", "valueDataDict": "1"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "INRND", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ISCMGDMTDose", "valueDataDict": "Yes (for 3 months)"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ISCMTimeframe", "valueDataDict": "Greater than or equal to 3 months"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "IntraVentConductionType", "valueDataDict": "Left bundle branch block (LBBB)"}, {"NCDRPatientId": "34525", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "LastName", "valueDataDict": "<PERSON><PERSON>"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadCounter", "valueDataDict": "1"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "LeadCounter", "valueDataDict": "2"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 3, "shortName": "LeadCounter", "valueDataDict": "3"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadDislodge", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadID", "valueDataDict": "1282"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "LeadID", "valueDataDict": "5394"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 3, "shortName": "LeadID", "valueDataDict": "302"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadLocation", "valueDataDict": "LV epicardial (CVS)"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "LeadLocation", "valueDataDict": "RA endocardial"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 3, "shortName": "LeadLocation", "valueDataDict": "RV endocardial"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadOpFName", "valueDataDict": "<PERSON>"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadOpLName", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadOpNPI", "valueDataDict": "**********"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadSerNo", "valueDataDict": "EJT045925"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "LeadSerNo", "valueDataDict": "EHJ046614"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 3, "shortName": "LeadSerNo", "valueDataDict": "EJG036528"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadType", "valueDataDict": "New"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "LeadType", "valueDataDict": "New"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 3, "shortName": "LeadType", "valueDataDict": "New"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "LeftBunLead", "valueDataDict": "Not attempted"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "MBI", "valueDataDict": "2AG2V13MM93"}, {"NCDRPatientId": "34525", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "MidName", "valueDataDict": "F"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "NVPQRS", "valueDataDict": "132"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "NVPQRS_unit", "valueDataDict": "msec"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "NYHA", "valueDataDict": "Class II"}, {"NCDRPatientId": "34525", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "OtherID", "valueDataDict": "12312123"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PCI", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "PostMarSur", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcEvent", "valueDataDict": "Bleeding - Access Site"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PostProcEvent", "valueDataDict": "Bleeding - Access Site"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "PostProcEvent", "valueDataDict": "Bleeding - Gastrointestinal"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 3, "shortName": "PostProcEvent", "valueDataDict": "Bleeding - Retroperitoneal"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 4, "shortName": "PostProcEvent", "valueDataDict": "Hematoma (Re-op, evac, or transfusion)"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 5, "shortName": "PostProcEvent", "valueDataDict": "Transfusion"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 6, "shortName": "PostProcEvent", "valueDataDict": "Vascular complications"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 7, "shortName": "PostProcEvent", "valueDataDict": "Cardiac arrest"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 8, "shortName": "PostProcEvent", "valueDataDict": "Cardiac perforation"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 9, "shortName": "PostProcEvent", "valueDataDict": "Coronary venous dissection"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 10, "shortName": "PostProcEvent", "valueDataDict": "Myocardial infarction"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 11, "shortName": "PostProcEvent", "valueDataDict": "Urgent cardiac surgery"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 12, "shortName": "PostProcEvent", "valueDataDict": "Pericardial effusion"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 13, "shortName": "PostProcEvent", "valueDataDict": "Cardiac tamponade"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 14, "shortName": "PostProcEvent", "valueDataDict": "Stroke (Any)"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 15, "shortName": "PostProcEvent", "valueDataDict": "Transient ischemic attack (TIA)"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 16, "shortName": "PostProcEvent", "valueDataDict": "Hemothorax"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 17, "shortName": "PostProcEvent", "valueDataDict": "Pneumothorax"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 18, "shortName": "PostProcEvent", "valueDataDict": "Infection requiring antibiotics"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 19, "shortName": "PostProcEvent", "valueDataDict": "Device embolization"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 3, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 4, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 5, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 6, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 7, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 8, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 9, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 10, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 11, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 12, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 13, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 14, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 15, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 16, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 17, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 18, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 19, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PreProcCreat", "valueDataDict": "1.86"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PreProcCreatND", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PreProcCreat_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorLVEF", "valueDataDict": "20"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorLVEFAssessed", "valueDataDict": "true"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorLVEFDate", "valueDataDict": "2025-02-11"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorLVEF_unit", "valueDataDict": "%"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorMIDate", "valueDataDict": "2007-06-27"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "ProcHistDate", "valueDataDict": "2007-06-28"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "ProcHistDate", "valueDataDict": "1991-02-01"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "ProcHistDate", "valueDataDict": "2007-06-28"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcHxOccur", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "ProcHxOccur", "valueDataDict": "true"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "ProcHxOccur", "valueDataDict": "true"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "ProcHxOccur", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "ProcHxOccur", "valueDataDict": "true"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "ProcHxOccur", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "ProcHxOccur", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "ProcHxOccur", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "ProcHxOccur", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcedHxName", "valueDataDict": "Aortic valve procedure"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "ProcedHxName", "valueDataDict": "Coronary angiography"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "ProcedHxName", "valueDataDict": "Prior coronary artery bypass graft"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "ProcedHxName", "valueDataDict": "CV implantable electronic device"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "ProcedHxName", "valueDataDict": "Prior PCI"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "ProcedHxName", "valueDataDict": "Candidate for VAD"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "ProcedHxName", "valueDataDict": "Currently on VAD"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "ProcedHxName", "valueDataDict": "On Heart Transplant Waiting List"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "ProcedHxName", "valueDataDict": "Candidate for transplant"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcedureEndDateTime", "valueDataDict": "2025-02-12T10:02:04"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcedureEntryTime", "valueDataDict": "2025-02-12T08:27:00"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcedureStartDateTime", "valueDataDict": "2025-02-12T08:50:42"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcedureStopTime", "valueDataDict": "2025-02-12T10:11:00"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcedureType", "valueDataDict": "Initial generator implant"}, {"NCDRPatientId": "34525", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceAmIndian", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Race<PERSON>ian", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceBlack", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceNatHaw", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "true"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ReasonForAdmit", "valueDataDict": "Admitted for this procedure"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ReasonPacIndic", "valueDataDict": "Anticipated requirement of > 40% RV pacing|HF unresponsive to GDMT"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "RevascOutcome", "valueDataDict": "Complete revascularization"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Revasc<PERSON>erf", "valueDataDict": "true"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "SDM_Proc", "valueDataDict": "true"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "SDM_Tool", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "SSN", "valueDataDict": ""}, {"NCDRPatientId": "34525", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "SSNNA", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "SetScrew", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Sex", "valueDataDict": "Male"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Sodium", "valueDataDict": "142"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "SodiumND", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Sodium_unit", "valueDataDict": "mEq/L"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "VPQRS", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "VPaced", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "VTDate", "valueDataDict": "2024-08-06"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "VTPostCardiacSurgery", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "VTReverseCause", "valueDataDict": "false"}, {"NCDRPatientId": "34525", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "VTType", "valueDataDict": " Non-sustained"}, {"NCDRPatientId": "34525", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCode", "valueDataDict": "94111"}, {"NCDRPatientId": "34525", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCodeNA", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AbConduction", "valueDataDict": "true"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ArrivalDate", "valueDataDict": "2025-01-05"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AtrialRhythm", "valueDataDict": "Sinus"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "B<PERSON>oDev", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "BUN", "valueDataDict": "22"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "BUNND", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "BUN_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "BradIndPres", "valueDataDict": "true"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "BradyArrest", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "CABG", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "CSLVLead", "valueDataDict": "Not attempted"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "CardiacArrestDate", "valueDataDict": "2025-01-05"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ClinicalTrial", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ConditionHx", "valueDataDict": "Atrial fibrillation"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "ConditionHx", "valueDataDict": "Cardiac arrest"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "ConditionHx", "valueDataDict": "Cardiomyopathy - ischemic"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "ConditionHx", "valueDataDict": "Cardiomyopathy - non-ischemic"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "ConditionHx", "valueDataDict": "Cerebrovascular disease"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "ConditionHx", "valueDataDict": "Chronic lung disease"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "ConditionHx", "valueDataDict": "Coronary artery disease"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "ConditionHx", "valueDataDict": "Currently on dialysis"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "ConditionHx", "valueDataDict": "Diabetes mellitus"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 10, "shortName": "ConditionHx", "valueDataDict": "Familial history of non-ischemic cardiomyopathy"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 11, "shortName": "ConditionHx", "valueDataDict": "Familial syndrome-risk of sudden death"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 12, "shortName": "ConditionHx", "valueDataDict": "Heart failure"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 13, "shortName": "ConditionHx", "valueDataDict": "Inotropic support"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 14, "shortName": "ConditionHx", "valueDataDict": "Myocardial infarction"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 15, "shortName": "ConditionHx", "valueDataDict": "Paroxysmal SVT history"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 16, "shortName": "ConditionHx", "valueDataDict": "Valvular heart disease"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 17, "shortName": "ConditionHx", "valueDataDict": "Structural abnormalities"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 18, "shortName": "ConditionHx", "valueDataDict": "Syncope"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 19, "shortName": "ConditionHx", "valueDataDict": "Syndromes of sudden death"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 20, "shortName": "ConditionHx", "valueDataDict": "Ventricular fibrillation (not due to reversible cause)"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 21, "shortName": "ConditionHx", "valueDataDict": "Ventricular tachycardia"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "ConditionHxOccurence", "valueDataDict": "true"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "ConditionHxOccurence", "valueDataDict": "true"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "ConditionHxOccurence", "valueDataDict": "true"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 10, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 11, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 12, "shortName": "ConditionHxOccurence", "valueDataDict": "true"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 13, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 14, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 15, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 16, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 17, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 18, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 19, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 20, "shortName": "ConditionHxOccurence", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 21, "shortName": "ConditionHxOccurence", "valueDataDict": "true"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "CoronaryAngioResults", "valueDataDict": "Non-revascularized significant disease"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DCDate", "valueDataDict": "2025-01-11"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DCLocation", "valueDataDict": "Skilled nursing facility"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DCStatus", "valueDataDict": "Alive"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 13, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 14, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 15, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Aldosterone Antagonist"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedID", "valueDataDict": "Angiotensin Converting Enzyme Inhibitor"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedID", "valueDataDict": "Angiotensin Receptor-Neprilysin Inhibitor"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedID", "valueDataDict": "Angiotensin II Receptor Blocker"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedID", "valueDataDict": "Renin Inhibitor"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedID", "valueDataDict": "Antiarrhythmic Drug"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedID", "valueDataDict": "Antiplatelet Agent"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedID", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedID", "valueDataDict": "Beta Blocker"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedID", "valueDataDict": "Betrixaban"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedID", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 13, "shortName": "DC_MedID", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 14, "shortName": "DC_MedID", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 15, "shortName": "DC_MedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "34527", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "DOB", "valueDataDict": "1900-01-01"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "DeviceImplanted", "valueDataDict": "true"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ECG", "valueDataDict": "true"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ECGNormal", "valueDataDict": "true"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "EPStudy", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ElecDevImpPath", "valueDataDict": "Implantable cardioverter-defibrillator"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "EnrolledStudy", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "Final_Device_Type", "valueDataDict": "ICD dual chamber"}, {"NCDRPatientId": "34527", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "FirstName", "valueDataDict": "<PERSON>"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "GenOpFName", "valueDataDict": "<PERSON>"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "GenOpLName", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "GenOpNPI", "valueDataDict": "**********"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HGB", "valueDataDict": "10.5"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HGBND", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HGB_unit", "valueDataDict": "g/dL"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HIPS", "valueDataDict": "Medicare (Part A or B)"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HealthIns", "valueDataDict": "true"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HemoInstability", "valueDataDict": "true"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "HisBunLead", "valueDataDict": "Not attempted"}, {"NCDRPatientId": "34527", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ICDImpID", "valueDataDict": "4612"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ICDImpSerNo", "valueDataDict": "211033985"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ICDIndication", "valueDataDict": "Secondary prevention"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "INR", "valueDataDict": "1.2"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "INRND", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ISCMGDMTDose", "valueDataDict": "Inability to complete"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ISCMTimeframe", "valueDataDict": "Less than 3 months"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "IntraVentConductionType", "valueDataDict": "Delay, nonspecific"}, {"NCDRPatientId": "34527", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "LastName", "valueDataDict": "<PERSON><PERSON>"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadCounter", "valueDataDict": "1"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "LeadCounter", "valueDataDict": "2"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadDislodge", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadID", "valueDataDict": "5394"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "LeadID", "valueDataDict": "302"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadLocation", "valueDataDict": "RA endocardial"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "LeadLocation", "valueDataDict": "RV endocardial"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadOpFName", "valueDataDict": "<PERSON>"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadOpLName", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadOpNPI", "valueDataDict": "**********"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadSerNo", "valueDataDict": "EHJ038863"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "LeadSerNo", "valueDataDict": "EJG035834"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadType", "valueDataDict": "New"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "LeadType", "valueDataDict": "New"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "LeftBunLead", "valueDataDict": "Not attempted"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "MBI", "valueDataDict": "5YN0RA6FW16"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "NVPQRS", "valueDataDict": "132"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "NVPQRS_unit", "valueDataDict": "msec"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "NYHA", "valueDataDict": "Class II"}, {"NCDRPatientId": "34527", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "OtherID", "valueDataDict": "12312123"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PCI", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PerfAfterRecentCA", "valueDataDict": "true"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "PostMarSur", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcEvent", "valueDataDict": "Bleeding - Access Site"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PostProcEvent", "valueDataDict": "Bleeding - Access Site"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "PostProcEvent", "valueDataDict": "Bleeding - Gastrointestinal"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 3, "shortName": "PostProcEvent", "valueDataDict": "Bleeding - Retroperitoneal"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 4, "shortName": "PostProcEvent", "valueDataDict": "Hematoma (Re-op, evac, or transfusion)"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 5, "shortName": "PostProcEvent", "valueDataDict": "Transfusion"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 6, "shortName": "PostProcEvent", "valueDataDict": "Vascular complications"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 7, "shortName": "PostProcEvent", "valueDataDict": "Cardiac arrest"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 8, "shortName": "PostProcEvent", "valueDataDict": "Cardiac perforation"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 9, "shortName": "PostProcEvent", "valueDataDict": "Coronary venous dissection"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 10, "shortName": "PostProcEvent", "valueDataDict": "Myocardial infarction"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 11, "shortName": "PostProcEvent", "valueDataDict": "Urgent cardiac surgery"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 12, "shortName": "PostProcEvent", "valueDataDict": "Pericardial effusion"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 13, "shortName": "PostProcEvent", "valueDataDict": "Cardiac tamponade"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 14, "shortName": "PostProcEvent", "valueDataDict": "Stroke (Any)"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 15, "shortName": "PostProcEvent", "valueDataDict": "Transient ischemic attack (TIA)"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 16, "shortName": "PostProcEvent", "valueDataDict": "Hemothorax"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 17, "shortName": "PostProcEvent", "valueDataDict": "Pneumothorax"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 18, "shortName": "PostProcEvent", "valueDataDict": "Infection requiring antibiotics"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 19, "shortName": "PostProcEvent", "valueDataDict": "Device embolization"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 3, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 4, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 5, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 6, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 7, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 8, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 9, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 10, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 11, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 12, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 13, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 14, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 15, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 16, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 17, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 18, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 19, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PreProcCreat", "valueDataDict": ".37"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PreProcCreatND", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PreProcCreat_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorLVEF", "valueDataDict": "45"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorLVEFAssessed", "valueDataDict": "true"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorLVEFDate", "valueDataDict": "2025-01-06"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorLVEF_unit", "valueDataDict": "%"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorPCICardioPresent", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "ProcHistDate", "valueDataDict": "2025-01-06"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "ProcHistDate", "valueDataDict": "2013-01-01"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcHxOccur", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "ProcHxOccur", "valueDataDict": "true"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "ProcHxOccur", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "ProcHxOccur", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "ProcHxOccur", "valueDataDict": "true"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "ProcHxOccur", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "ProcHxOccur", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "ProcHxOccur", "valueDataDict": "true"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "ProcHxOccur", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcedHxName", "valueDataDict": "Aortic valve procedure"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "ProcedHxName", "valueDataDict": "Coronary angiography"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "ProcedHxName", "valueDataDict": "Prior coronary artery bypass graft"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "ProcedHxName", "valueDataDict": "CV implantable electronic device"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "ProcedHxName", "valueDataDict": "Prior PCI"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "ProcedHxName", "valueDataDict": "Candidate for VAD"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "ProcedHxName", "valueDataDict": "Currently on VAD"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "ProcedHxName", "valueDataDict": "On Heart Transplant Waiting List"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "ProcedHxName", "valueDataDict": "Candidate for transplant"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcedureEndDateTime", "valueDataDict": "2025-01-08T11:21:03"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcedureEntryTime", "valueDataDict": "2025-01-08T10:07:00"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcedureStartDateTime", "valueDataDict": "2025-01-08T10:36:30"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcedureStopTime", "valueDataDict": "2025-01-08T11:36:00"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcedureType", "valueDataDict": "Initial generator implant"}, {"NCDRPatientId": "34527", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceAmIndian", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Race<PERSON>ian", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceBlack", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceNatHaw", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "true"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ReasonForAdmit", "valueDataDict": "Other"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ReasonPacIndic", "valueDataDict": "Chronotropic incompetence"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "SDM_Proc", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "SSN", "valueDataDict": ""}, {"NCDRPatientId": "34527", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "SSNNA", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "SetScrew", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Sex", "valueDataDict": "Female"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Sodium", "valueDataDict": "136"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "SodiumND", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Sodium_unit", "valueDataDict": "mEq/L"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "VFibArrest", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "VPQRS", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "VPaced", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "VTDate", "valueDataDict": "2025-01-05"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "VTPostCardiacSurgery", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "VTReverseCause", "valueDataDict": "false"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "VTType", "valueDataDict": "Monomorphic"}, {"NCDRPatientId": "34527", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "VTachArrest", "valueDataDict": "true"}, {"NCDRPatientId": "34527", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCode", "valueDataDict": "94111"}, {"NCDRPatientId": "34527", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCodeNA", "valueDataDict": "false"}]