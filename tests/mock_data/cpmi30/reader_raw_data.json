[{"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "AFlutterBA", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "AdmFName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "AdmLName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "AdmMName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "AdmNPI", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "AdmitDateTime", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "ArriMedCode", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 2, "shortName": "ArriMedCode", "valueDataDict": "Beta Blocker"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 3, "shortName": "ArriMedCode", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 4, "shortName": "ArriMedCode", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 5, "shortName": "ArriMedCode", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "ArrivalDateTime", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "AttFName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "AttLName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "AttMName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "AttNPI", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "BasicADLs", "valueDataDict": "Independent of all ADLs"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "BasicADLsUNK", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "CABGFirst", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "CAOutHospital", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "CATransferFac", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "CE_RBC", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "CXRPerf", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "Cancer", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "Cognition", "valueDataDict": "Normal"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "CognitionUnk", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "CreatPeak", "valueDataDict": "1.27"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "CreatPeakDateTime", "valueDataDict": "2022-07-01T04:35:00.000"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "CreatPeakND", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "CreatPeak_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "DCDateTime", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "DCFName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "DCHospice", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "DCLName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "DCLocation", "valueDataDict": "Skilled Nursing facility"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "DCNPI", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "DCStatus", "valueDataDict": "Alive"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_CardRehab", "valueDataDict": "No - Health Care System Reason Documented"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_Comfort", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 13, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 14, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 15, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 16, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 17, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 18, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedDose", "valueDataDict": "High Intensity Dose"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedDose", "valueDataDict": "Low Intensity Dose"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Angiotensin Converting Enzyme Inhibitor"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedID", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedID", "valueDataDict": "Evolocumab"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedID", "valueDataDict": "Aldosterone Antagonist"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedID", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedID", "valueDataDict": "Statin"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedID", "valueDataDict": "Sacubitril and Valsartan"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedID", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedID", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedID", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 13, "shortName": "DC_MedID", "valueDataDict": "Angiotensin II Receptor Blocker"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 14, "shortName": "DC_MedID", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 15, "shortName": "DC_MedID", "valueDataDict": "Beta Blocker"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 16, "shortName": "DC_MedID", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 17, "shortName": "DC_MedID", "valueDataDict": "Non-Statin"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 18, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "DOB", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "DiabetesBA", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "DiagCorAngio1st", "valueDataDict": "No - Pt. Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "Dyslipidemia", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "ECGCounter", "valueDataDict": "1"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "ECGDateTime", "valueDataDict": "2022-06-29T12:07:00.000"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "EDDisposition", "valueDataDict": "Inpatient"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "EDFName", "valueDataDict": "<PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "EDLName", "valueDataDict": "<PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "EDNPI", "valueDataDict": "**********"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "EnrolledStudy", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "EpiEvent", "valueDataDict": "New Requirement for Dialysis"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 2, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Other"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 3, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Access Site"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 4, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Surgical Procedure or Intervention Required for Bleeding Event"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 5, "shortName": "EpiEvent", "valueDataDict": "Myocardial Infarction"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 6, "shortName": "EpiEvent", "valueDataDict": "Stroke - Hemorrhagic"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 7, "shortName": "EpiEvent", "valueDataDict": "Stroke - Undetermined"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 8, "shortName": "EpiEvent", "valueDataDict": "Ventricular Tachycardia"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 9, "shortName": "EpiEvent", "valueDataDict": "Transient Ischemic Attack (TIA)"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 10, "shortName": "EpiEvent", "valueDataDict": "Cardiac Arrest"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 11, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Genitourinary"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 12, "shortName": "EpiEvent", "valueDataDict": "Stroke - Ischemic"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 13, "shortName": "EpiEvent", "valueDataDict": "Atrial Fibrillation"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 14, "shortName": "EpiEvent", "valueDataDict": "Intubation"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 15, "shortName": "EpiEvent", "valueDataDict": "Ventricular Fibrillation"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 16, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Gastrointestinal"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 17, "shortName": "EpiEvent", "valueDataDict": "Heart Failure"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 18, "shortName": "EpiEvent", "valueDataDict": "Cardiogenic Shock"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 19, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Retroperitoneal"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 5, "shortName": "EpiEventDateTime", "valueDataDict": "2022-07-04T06:50:00.000"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 2, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 3, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 4, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 5, "shortName": "EpiEventOccurred", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 6, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 7, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 8, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 9, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 10, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 11, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 12, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 13, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 14, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 15, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 16, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 17, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 18, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 19, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON>", "valueDataDict": "2022"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "FMCCreat", "valueDataDict": "0.98"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "FMCCreatND", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "FMCCreat_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "FirstFacMeansTrans", "valueDataDict": "Self/Family"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "FirstName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "HFFirstMedCon", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "HIPS", "valueDataDict": "Private Health Insurance"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "HRFirstMedCon", "valueDataDict": "94"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "HRFirstMedCon_unit", "valueDataDict": "bpm"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "HealthIns", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "HeightAD", "valueDataDict": "161.90"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "HeightAD_unit", "valueDataDict": "cm"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 2, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 3, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 4, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 5, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 6, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 7, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 8, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 9, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 10, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 11, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 12, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 13, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 14, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 15, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 16, "shortName": "HomeMedPrescrib", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 17, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 18, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 19, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 20, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 21, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 22, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 23, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 24, "shortName": "HomeMedPrescrib", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 25, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 26, "shortName": "HomeMedPrescrib", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 27, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 28, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "HomeMeds", "valueDataDict": "Angiotensin Converting Enzyme Inhibitor"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 2, "shortName": "HomeMeds", "valueDataDict": "Ezetimibe "}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 3, "shortName": "HomeMeds", "valueDataDict": "Sodium glucose cotransporter subtype 2 inhibitor"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 4, "shortName": "HomeMeds", "valueDataDict": "Aldosterone Antagonist"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 5, "shortName": "HomeMeds", "valueDataDict": "Fenofibrate"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 6, "shortName": "HomeMeds", "valueDataDict": "Sulfonylurea"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 7, "shortName": "HomeMeds", "valueDataDict": "Sacubitril and Valsartan"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 8, "shortName": "HomeMeds", "valueDataDict": "Non-Statin (Other)"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 9, "shortName": "HomeMeds", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 10, "shortName": "HomeMeds", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 11, "shortName": "HomeMeds", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 12, "shortName": "HomeMeds", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 13, "shortName": "HomeMeds", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 14, "shortName": "HomeMeds", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 15, "shortName": "HomeMeds", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 16, "shortName": "HomeMeds", "valueDataDict": "Angiotensin II Receptor Blocker"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 17, "shortName": "HomeMeds", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 18, "shortName": "HomeMeds", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 19, "shortName": "HomeMeds", "valueDataDict": "Beta Blocker"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 20, "shortName": "HomeMeds", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 21, "shortName": "HomeMeds", "valueDataDict": "Evolocumab"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 22, "shortName": "HomeMeds", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 23, "shortName": "HomeMeds", "valueDataDict": "DPP-4 inhibitor"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 24, "shortName": "HomeMeds", "valueDataDict": "Statin"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 25, "shortName": "HomeMeds", "valueDataDict": "GLP-1 Receptor Agonist"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 26, "shortName": "HomeMeds", "valueDataDict": "Metformin"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 27, "shortName": "HomeMeds", "valueDataDict": "Other Oral Hypoglycemic"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 28, "shortName": "HomeMeds", "valueDataDict": "Pioglitazone"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "HospClinicTrial", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "HxAFib", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "HxCVD", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "HxMIFF", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "Hypertension", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "InitHemoND", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "InitHemoValue", "valueDataDict": "5.4"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "InitHemoValue_unit", "valueDataDict": "%"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "InitHgbND", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "InitHgbValue", "valueDataDict": "12.60"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "InitHgbValue_unit", "valueDataDict": "g/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "InitINRDateTime", "valueDataDict": "2022-06-29T14:51:00.000"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "InitINRND", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "InitINRValue", "valueDataDict": "1.1"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "InitTropValue", "valueDataDict": "1918.000"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 2, "shortName": "InitTropValue", "valueDataDict": "1915.000"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 3, "shortName": "InitTropValue", "valueDataDict": "1616.000"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "InitTropValue_unit", "valueDataDict": "ng/L"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 2, "shortName": "InitTropValue_unit", "valueDataDict": "ng/L"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 3, "shortName": "InitTropValue_unit", "valueDataDict": "ng/L"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "IschemSymResolv", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "LDLND", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "LVEFAssessedAD", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "LVEFPercent", "valueDataDict": "58"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "LVEFPercent_unit", "valueDataDict": "%"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "LabTropAssay", "valueDataDict": "5121"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 2, "shortName": "LabTropAssay", "valueDataDict": "5121"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 3, "shortName": "LabTropAssay", "valueDataDict": "5121"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "LastName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsHDL6mFMC", "valueDataDict": "53"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsHDL6mFMC_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsHDLND6mFMC", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsLDL", "valueDataDict": "51"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsLDL_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsTC6mFMC", "valueDataDict": "119"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsTC6mFMC_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsTCND6mFMC", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsTrig", "valueDataDict": "74"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsTrigND", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsTrig_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "LocFirstEval", "valueDataDict": "ED"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "LowHgbND", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "LowHgbValue", "valueDataDict": "10.70"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "LowHgbValueDateTime", "valueDataDict": "2022-07-03T04:30:00.000"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "LowHgbValue_unit", "valueDataDict": "g/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "MechVentSupp", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "MedsArrival", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 2, "shortName": "MedsArrival", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 3, "shortName": "MedsArrival", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 4, "shortName": "MedsArrival", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 5, "shortName": "MedsArrival", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "MidName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "NSAIDAdmin", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "NonInvasTestMeth", "valueDataDict": "Rest "}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "NonInvasTestType", "valueDataDict": "Echocardiogram"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "NoninvasPerformed", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "OtherECGFindings", "valueDataDict": "None"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "OtherID", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "PCIArrivDC", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorCABG", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorHFFF", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorPAD", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorPCI", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "PtRestriction2", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "PtType", "valueDataDict": "NSTEMI"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceAmIndian", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Race<PERSON>ian", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceBlack", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceNatHaw", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "RiskScorePerf", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "SBPFirstMedCon", "valueDataDict": "184"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "SBPFirstMedCon_unit", "valueDataDict": "mm[Hg]"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "SSNNA", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Sex", "valueDataDict": "Female"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "ShockFirstMedCon", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "StemiFirstNotedFMC", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "TobaccoUse", "valueDataDict": "Former"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "TranOutEDDateTime", "valueDataDict": "2022-06-29T17:02:00.000"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "TranOutsideFac", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "TropCollDateTime", "valueDataDict": "2022-06-29T13:13:00.000"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 2, "shortName": "TropCollDateTime", "valueDataDict": "2022-06-29T14:51:00.000"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 3, "shortName": "TropCollDateTime", "valueDataDict": "2022-06-29T18:02:00.000"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "TropCount", "valueDataDict": "1"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 2, "shortName": "TropCount", "valueDataDict": "2"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 3, "shortName": "TropCount", "valueDataDict": "3"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "TropResDateTime", "valueDataDict": "2022-06-29T13:48:00.000"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 2, "shortName": "TropResDateTime", "valueDataDict": "2022-06-29T15:28:00.000"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 3, "shortName": "TropResDateTime", "valueDataDict": "2022-06-29T19:58:00.000"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "TropTestLoc", "valueDataDict": "Lab"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 2, "shortName": "TropTestLoc", "valueDataDict": "Lab"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 3, "shortName": "TropTestLoc", "valueDataDict": "Lab"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "Walking", "valueDataDict": "Assisted"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "WalkingUnk", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "WeightAD", "valueDataDict": "78.01"}, {"NCDRPatientId": "12345", "EpisodeKey": "2022", "VisitId": 0, "IncrementalId": 1, "shortName": "WeightAD_unit", "valueDataDict": "kg"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCode", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCodeNA", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "AFlutterBA", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "AccessSite", "valueDataDict": "Radial"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "AdmFName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "AdmLName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "AdmNPI", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "AdmitDateTime", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "ArriMedCode", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 2, "shortName": "ArriMedCode", "valueDataDict": "Beta Blocker"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 3, "shortName": "ArriMedCode", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 4, "shortName": "ArriMedCode", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 5, "shortName": "ArriMedCode", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "ArrivalDateTime", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "AttFName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "AttLName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "AttMName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "AttNPI", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "BasicADLs", "valueDataDict": "Independent of all ADLs"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "BasicADLsUNK", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "CABGFirst", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "CAOutHospital", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "CATransferFac", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "CE_RBC", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "CXRPerf", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "Cancer", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "CathArrivalDateTime", "valueDataDict": "2022-06-30T10:42:00.000"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "Cognition", "valueDataDict": "Normal"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "CognitionUnk", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "CreatPeak", "valueDataDict": "1.12"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "CreatPeakDateTime", "valueDataDict": "2022-06-30T03:35:00.000"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "CreatPeakND", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "CreatPeak_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "CurrStentTypeUnk", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "DCDateTime", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "DCFName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "DCHospice", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "DCLName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "DCLocation", "valueDataDict": "Home"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "DCMName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "DCNPI", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "DCStatus", "valueDataDict": "Alive"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_CardRehab", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_Comfort", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 13, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 14, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 15, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 16, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 17, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 18, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedDose", "valueDataDict": "High Intensity Dose"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedDose", "valueDataDict": "Low Intensity Dose"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Angiotensin Converting Enzyme Inhibitor"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedID", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedID", "valueDataDict": "Evolocumab"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedID", "valueDataDict": "Aldosterone Antagonist"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedID", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedID", "valueDataDict": "Statin"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedID", "valueDataDict": "Sacubitril and Valsartan"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedID", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedID", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedID", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 13, "shortName": "DC_MedID", "valueDataDict": "Angiotensin II Receptor Blocker"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 14, "shortName": "DC_MedID", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 15, "shortName": "DC_MedID", "valueDataDict": "Beta Blocker"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 16, "shortName": "DC_MedID", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 17, "shortName": "DC_MedID", "valueDataDict": "Non-Statin"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 18, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "DOB", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "DiabetesBA", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "DiagCorAngio1st", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "DiagCorAngioDateTime", "valueDataDict": "2022-06-30T11:57:00.000"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 3, "shortName": "DrugStartDT", "valueDataDict": "2022-06-29T19:27:00.000"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 5, "shortName": "DrugStartDT", "valueDataDict": "2022-06-30T11:43:00.000"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "Dyslipidemia", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "ECGCounter", "valueDataDict": "1"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 2, "shortName": "ECGCounter", "valueDataDict": "2"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "ECGDateTime", "valueDataDict": "2022-06-29T16:36:00.000"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 2, "shortName": "ECGDateTime", "valueDataDict": "2022-06-30T05:50:00.000"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "EDDisposition", "valueDataDict": "Inpatient"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "EDFName", "valueDataDict": "<PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "EDLName", "valueDataDict": "<PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "EDNPI", "valueDataDict": "**********"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "EnrolledStudy", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "EpiEvent", "valueDataDict": "New Requirement for Dialysis"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 2, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Other"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 3, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Access Site"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 4, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Access Site"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 5, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Surgical Procedure or Intervention Required for Bleeding Event"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 6, "shortName": "EpiEvent", "valueDataDict": "Myocardial Infarction"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 7, "shortName": "EpiEvent", "valueDataDict": "Stroke - Hemorrhagic"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 8, "shortName": "EpiEvent", "valueDataDict": "Stroke - Undetermined"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 9, "shortName": "EpiEvent", "valueDataDict": "Ventricular Tachycardia"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 10, "shortName": "EpiEvent", "valueDataDict": "Transient Ischemic Attack (TIA)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 11, "shortName": "EpiEvent", "valueDataDict": "Cardiac Arrest"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 12, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Genitourinary"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 13, "shortName": "EpiEvent", "valueDataDict": "Stroke - Ischemic"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 14, "shortName": "EpiEvent", "valueDataDict": "Atrial Fibrillation"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 15, "shortName": "EpiEvent", "valueDataDict": "Intubation"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 16, "shortName": "EpiEvent", "valueDataDict": "Ventricular Fibrillation"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 17, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Gastrointestinal"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 18, "shortName": "EpiEvent", "valueDataDict": "Heart Failure"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 19, "shortName": "EpiEvent", "valueDataDict": "Cardiogenic Shock"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 20, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Retroperitoneal"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 3, "shortName": "EpiEventDateTime", "valueDataDict": "2022-07-04T09:00:00.000"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 4, "shortName": "EpiEventDateTime", "valueDataDict": "2022-07-04T10:00:00.000"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 2, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 3, "shortName": "EpiEventOccurred", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 4, "shortName": "EpiEventOccurred", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 5, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 6, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 7, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 8, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 9, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 10, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 11, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 12, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 13, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 14, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 15, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 16, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 17, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 18, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 19, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 20, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON>", "valueDataDict": "2038"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "FMCCreat", "valueDataDict": "1.06"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "FMCCreatND", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "FMCCreat_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "FirstFacMeansTrans", "valueDataDict": "Self/Family"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "FirstName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "GRACEScore", "valueDataDict": "113"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "HFFirstMedCon", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "HIPS", "valueDataDict": "Private Health Insurance|Medicare"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "HRFirstMedCon", "valueDataDict": "81"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "HRFirstMedCon_unit", "valueDataDict": "bpm"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "HealthIns", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "HeightAD", "valueDataDict": "177.80"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "HeightAD_unit", "valueDataDict": "cm"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 2, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 3, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 4, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 5, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 6, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 7, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 8, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 9, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 10, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 11, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 12, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 13, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 14, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 15, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 16, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 17, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 18, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 19, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 20, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 21, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 22, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 23, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 24, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 25, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 26, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 27, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 28, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "HomeMeds", "valueDataDict": "Angiotensin Converting Enzyme Inhibitor"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 2, "shortName": "HomeMeds", "valueDataDict": "Ezetimibe "}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 3, "shortName": "HomeMeds", "valueDataDict": "Sodium glucose cotransporter subtype 2 inhibitor"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 4, "shortName": "HomeMeds", "valueDataDict": "Aldosterone Antagonist"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 5, "shortName": "HomeMeds", "valueDataDict": "Fenofibrate"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 6, "shortName": "HomeMeds", "valueDataDict": "Sulfonylurea"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 7, "shortName": "HomeMeds", "valueDataDict": "Sacubitril and Valsartan"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 8, "shortName": "HomeMeds", "valueDataDict": "Non-Statin (Other)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 9, "shortName": "HomeMeds", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 10, "shortName": "HomeMeds", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 11, "shortName": "HomeMeds", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 12, "shortName": "HomeMeds", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 13, "shortName": "HomeMeds", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 14, "shortName": "HomeMeds", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 15, "shortName": "HomeMeds", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 16, "shortName": "HomeMeds", "valueDataDict": "Angiotensin II Receptor Blocker"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 17, "shortName": "HomeMeds", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 18, "shortName": "HomeMeds", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 19, "shortName": "HomeMeds", "valueDataDict": "Beta Blocker"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 20, "shortName": "HomeMeds", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 21, "shortName": "HomeMeds", "valueDataDict": "Evolocumab"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 22, "shortName": "HomeMeds", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 23, "shortName": "HomeMeds", "valueDataDict": "DPP-4 inhibitor"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 24, "shortName": "HomeMeds", "valueDataDict": "Statin"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 25, "shortName": "HomeMeds", "valueDataDict": "GLP-1 Receptor Agonist"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 26, "shortName": "HomeMeds", "valueDataDict": "Metformin"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 27, "shortName": "HomeMeds", "valueDataDict": "Other Oral Hypoglycemic"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 28, "shortName": "HomeMeds", "valueDataDict": "Pioglitazone"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "HospClinicTrial", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "HxAFib", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "HxCVD", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "HxMIFF", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "Hypertension", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "InitHemoND", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "InitHemoValue", "valueDataDict": "5.1"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "InitHemoValue_unit", "valueDataDict": "%"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "InitHgbND", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "InitHgbValue", "valueDataDict": "15.70"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "InitHgbValue_unit", "valueDataDict": "g/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "InitINRDateTime", "valueDataDict": "2022-06-29T18:15:00.000"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "InitINRND", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "InitINRValue", "valueDataDict": "1.1"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "InitTropValue", "valueDataDict": "3308.000"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 2, "shortName": "InitTropValue", "valueDataDict": "4136.000"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 3, "shortName": "InitTropValue", "valueDataDict": "5874.000"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "InitTropValue_unit", "valueDataDict": "ng/L"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 2, "shortName": "InitTropValue_unit", "valueDataDict": "ng/L"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 3, "shortName": "InitTropValue_unit", "valueDataDict": "ng/L"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "IschemSymResolv", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "LDLND", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "LVEFAssessedAD", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "LVEFPercent", "valueDataDict": "42"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "LVEFPercent_unit", "valueDataDict": "%"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "LabTropAssay", "valueDataDict": "5121"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 2, "shortName": "LabTropAssay", "valueDataDict": "5121"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 3, "shortName": "LabTropAssay", "valueDataDict": "5121"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "LastName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsHDL6mFMC", "valueDataDict": "35"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsHDL6mFMC_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsHDLND6mFMC", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsLDL", "valueDataDict": "106"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsLDL_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsTC6mFMC", "valueDataDict": "168"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsTC6mFMC_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsTCND6mFMC", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsTrig", "valueDataDict": "133"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsTrigND", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsTrig_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "LocFirstEval", "valueDataDict": "ED"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "LowHgbND", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "LowHgbValue", "valueDataDict": "14.20"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "LowHgbValueDateTime", "valueDataDict": "2022-06-30T03:34:00.000"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "LowHgbValue_unit", "valueDataDict": "g/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "MechVentSupp", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 3, "shortName": "MedArrivalDose", "valueDataDict": "75"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 5, "shortName": "MedArrivalDose", "valueDataDict": "180"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 3, "shortName": "MedArrivalDose_unit", "valueDataDict": "mg"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 5, "shortName": "MedArrivalDose_unit", "valueDataDict": "mg"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "MedsArrival", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 2, "shortName": "MedsArrival", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 3, "shortName": "MedsArrival", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 4, "shortName": "MedsArrival", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 5, "shortName": "MedsArrival", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "MidName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "NSAIDAdmin", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "NVCoroVesselStenosis", "valueDataDict": "99"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 2, "shortName": "NVCoroVesselStenosis", "valueDataDict": "80"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "NVCoroVesselStenosis_unit", "valueDataDict": "%"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 2, "shortName": "NVCoroVesselStenosis_unit", "valueDataDict": "%"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "NVSegmentID", "valueDataDict": "1 - pRCA"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 2, "shortName": "NVSegmentID", "valueDataDict": "13 - mLAD"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "NVStenosis", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "NameRiskScore", "valueDataDict": "GRACE"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "NonInvasTestMeth", "valueDataDict": "Rest "}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "NonInvasTestType", "valueDataDict": "Echocardiogram"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "NoninvasPerformed", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "OtherECGFindings", "valueDataDict": "ST depression (New or Presumed New)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 2, "shortName": "OtherECGFindings", "valueDataDict": "None"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "OtherID", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "PCIArrivDC", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "PCIFName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "PCILName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "PCIMName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "PCINPI", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "PCI_Indication", "valueDataDict": "NSTE - ACS"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorCABG", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorHFFF", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorPAD", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorPCI", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 2, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 3, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 4, "shortName": "ProcMedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 5, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 6, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 7, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 8, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 9, "shortName": "ProcMedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 10, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 11, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 12, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 13, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 14, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 15, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 16, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcMedID", "valueDataDict": "Low Molecular Weight Heparin"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 2, "shortName": "ProcMedID", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 3, "shortName": "ProcMedID", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 4, "shortName": "ProcMedID", "valueDataDict": "Unfractionated Heparin"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 5, "shortName": "ProcMedID", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 6, "shortName": "ProcMedID", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 7, "shortName": "ProcMedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 8, "shortName": "ProcMedID", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 9, "shortName": "ProcMedID", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 10, "shortName": "ProcMedID", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 11, "shortName": "ProcMedID", "valueDataDict": "Vorapaxar"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 12, "shortName": "ProcMedID", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 13, "shortName": "ProcMedID", "valueDataDict": "Glycoprotein IIb IIIa Inhibitors"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 14, "shortName": "ProcMedID", "valueDataDict": "Fondaparinux"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 15, "shortName": "ProcMedID", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 16, "shortName": "ProcMedID", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "PtRestriction2", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "PtType", "valueDataDict": "NSTEMI"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceAmIndian", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Race<PERSON>ian", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceBlack", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceNatHaw", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "RiskScorePerf", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "SBPFirstMedCon", "valueDataDict": "188"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "SBPFirstMedCon_unit", "valueDataDict": "mm[Hg]"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "SSNNA", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Sex", "valueDataDict": "Male"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "ShockFirstMedCon", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "StemiFirstNotedFMC", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 2, "shortName": "StemiFirstNotedFMC", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "StentTypeFV", "valueDataDict": "DES"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "StentsImplanted", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "TobaccoUse", "valueDataDict": "Never"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "TranOutEDDateTime", "valueDataDict": "2022-06-29T22:25:00.000"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "TranOutsideFac", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "TropCollDateTime", "valueDataDict": "2022-06-29T17:13:00.000"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 2, "shortName": "TropCollDateTime", "valueDataDict": "2022-06-29T18:15:00.000"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 3, "shortName": "TropCollDateTime", "valueDataDict": "2022-06-29T21:08:00.000"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "TropCount", "valueDataDict": "1"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 2, "shortName": "TropCount", "valueDataDict": "2"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 3, "shortName": "TropCount", "valueDataDict": "3"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "TropResDateTime", "valueDataDict": "2022-06-29T17:41:00.000"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 2, "shortName": "TropResDateTime", "valueDataDict": "2022-06-29T18:58:00.000"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 3, "shortName": "TropResDateTime", "valueDataDict": "2022-06-29T22:17:00.000"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "TropTestLoc", "valueDataDict": "Lab"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 2, "shortName": "TropTestLoc", "valueDataDict": "Lab"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 3, "shortName": "TropTestLoc", "valueDataDict": "Lab"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "Walking", "valueDataDict": "Unassisted"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "WalkingUnk", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "WeightAD", "valueDataDict": "81.65"}, {"NCDRPatientId": "67890", "EpisodeKey": "2038", "VisitId": 0, "IncrementalId": 1, "shortName": "WeightAD_unit", "valueDataDict": "kg"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCode", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCodeNA", "valueDataDict": ""}]