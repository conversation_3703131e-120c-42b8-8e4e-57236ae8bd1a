[{"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "AFib", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "AFibClass", "valueDataDict": "Permanent"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "AFibFlutterCardioPlans", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "AbConduction", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "ArrivalDate", "valueDataDict": "2023-06-28"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "AtrialRhythm", "valueDataDict": "Atrial fibrillation"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "BUN", "valueDataDict": "16"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "BUNND", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "BUN_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "CABG", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "CAD", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "CArrest", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "CSLVLead", "valueDataDict": "Previously Implanted"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "CVDissect", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "CandidateforVAD", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "CardiacArrest", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "CardiacPerf", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "ChronicLungDisease", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "ClinicalTrial", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "CoronaryAngio", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "CurrentDialysis", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "CurrentlyonVAD", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "DCDate", "valueDataDict": "2023-07-02"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "DCLocation", "valueDataDict": "Skilled Nursing facility"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "DCStatus", "valueDataDict": "Alive"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - Medical Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - Medical Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - Medical Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - Medical Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 13, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 14, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 15, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 16, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Angiotensin Converting Enzyme Inhibitor"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedID", "valueDataDict": "Aldosterone Antagonist"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedID", "valueDataDict": "Angiotensin Receptor-Neprilysin Inhibitor"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedID", "valueDataDict": "Antiarrhythmic Drug"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedID", "valueDataDict": "Antiplatelet agent"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedID", "valueDataDict": "Angiotensin II Receptor Blocker"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedID", "valueDataDict": "Beta Blocker"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedID", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedID", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedID", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 13, "shortName": "DC_MedID", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 14, "shortName": "DC_MedID", "valueDataDict": "Renin Inhibitor"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 15, "shortName": "DC_MedID", "valueDataDict": "Selective Sinus Node I/f Channel Inhibitor"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 16, "shortName": "DC_MedID", "valueDataDict": "Statin"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "DOB", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "DeviceExplant", "valueDataDict": "Explanted"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "DeviceImplanted", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "Diabetes", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "ECG", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "ECGDate", "valueDataDict": "2023-06-17"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "ECGNormal", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "EPStudy", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "EnrolledStudy", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "ExLeadDate", "valueDataDict": "2008-12-11"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 2, "shortName": "ExLeadDate", "valueDataDict": "2008-12-11"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "ExLeadStat", "valueDataDict": "Reused"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 2, "shortName": "ExLeadStat", "valueDataDict": "Reused"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "FamilialHxNICM", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "FamilialSyndSuddenDeath", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "FinalDeviceType", "valueDataDict": "CRT-P"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "FirstName", "valueDataDict": "J"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "GenOpFName", "valueDataDict": "B"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "GenOpLName", "valueDataDict": "L"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "GenOpMName", "valueDataDict": "K"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "GenOpNPI", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "HF", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "HGB", "valueDataDict": "9.6"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "HGBND", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "HGB_unit", "valueDataDict": "g/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "HIPS", "valueDataDict": "Private Health Insurance|Medicare"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "HealthIns", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "Hematoma", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "Hemothorax", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "HisLBundleLead", "valueDataDict": "Not Attempted"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "ICDExpID", "valueDataDict": "696"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "ICDExpSerNo", "valueDataDict": "12345"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "ICDImpID", "valueDataDict": "696"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "ICDImpSerNo", "valueDataDict": "12345"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "ISCM", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "InfectionReqAnti", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "InotropicSupport", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "LastName", "valueDataDict": "B"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadCounter", "valueDataDict": "1"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 2, "shortName": "LeadCounter", "valueDataDict": "2"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadDislodge", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadID", "valueDataDict": "681"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 2, "shortName": "LeadID", "valueDataDict": "92"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadLocation", "valueDataDict": "RV endocardial"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 2, "shortName": "LeadLocation", "valueDataDict": "LV epicardial (CVS)"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadOpFName", "valueDataDict": "B"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadOpLName", "valueDataDict": "L"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadOpMName", "valueDataDict": "K"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadOpNPI", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadSerNo", "valueDataDict": "12345"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 2, "shortName": "LeadSerNo", "valueDataDict": "12345"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadType", "valueDataDict": "Existing"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 2, "shortName": "LeadType", "valueDataDict": "Existing"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "MBI", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "MidName", "valueDataDict": "M"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "NICM", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "NICMGDMTDose", "valueDataDict": "Yes (for 3 months)"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "NICMTimeframe", "valueDataDict": "3 months or more"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "NYHA", "valueDataDict": "Class I"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "OtherID", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "OtherStructAbn", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "PCI", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "ParoxySVTHistory", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "Pneumothorax", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "PostMI", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "PostTIA", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "PriPacMode", "valueDataDict": "VVI(R)"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "PrimBradIndPres", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "PrimaryValvularHD", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorCABG", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorCIED", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorCVD", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorLVEF", "valueDataDict": "55"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorLVEFAssessed", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorLVEFDate", "valueDataDict": "2023-06-20"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorLVEF_unit", "valueDataDict": "%"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorMI", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorPCI", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "Prior_AVProc", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcedureEndDateTime", "valueDataDict": "2023-06-28T11:13:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcedureStartDateTime", "valueDataDict": "2023-06-28T09:14:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcedureType", "valueDataDict": "Generator change"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "PtRestriction", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceAmIndian", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Race<PERSON>ian", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceBlack", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceNatHaw", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "ReImplantReason", "valueDataDict": "Reimplant Reason - Infection"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "ReasonForAdmit", "valueDataDict": "Admitted for procedure"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "ReasonPacIndic", "valueDataDict": "Complete heart block"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "SDM_Proc", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "SSNNA", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "SetScrew", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Sex", "valueDataDict": "Female"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "Sodium", "valueDataDict": "132"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "SodiumND", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "Sodium_unit", "valueDataDict": "mEq/L"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "Stroke", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "Syncope", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "SyndromeRiskDeath", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "Tamponade", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "TransplantCandidate", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "TransplantWaitList", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 1, "IncrementalId": 1, "shortName": "UrgentSurgery", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "VFib", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "VPQRS", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "VPaced", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "VPacedQRS", "valueDataDict": "138"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "VPacedQRS_unit", "valueDataDict": "msec"}, {"NCDRPatientId": "12345", "EpisodeKey": "ce62e39d-658e-46b7-96f9-55c603d7c810", "VisitId": 0, "IncrementalId": 1, "shortName": "VT", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCodeNA", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "AFib", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "AFibClass", "valueDataDict": "Paroxysmal"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "AFibFlutterCardioPlans", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "AbConduction", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "ArrivalDate", "valueDataDict": "2023-06-16"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "AtrialRhythm", "valueDataDict": "Atrial fibrillation"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "BUN", "valueDataDict": "38"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "BUNND", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "BUN_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "BradIndPres", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "CABG", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "CAD", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "CArrest", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "CSLVLead", "valueDataDict": "Not Attempted"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "CVDissect", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "CandidateforVAD", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "CardiacArrest", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "CardiacPerf", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "ChronicLungDisease", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "ClinicalTrial", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "CoronaryAngio", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "CurrentDialysis", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "CurrentlyonVAD", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "DCDate", "valueDataDict": "2023-07-29"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "DCLocation", "valueDataDict": "Discharged/transferred to an Extended care/TCU/rehab"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "DCStatus", "valueDataDict": "Alive"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 13, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 14, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 15, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 16, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Angiotensin Converting Enzyme Inhibitor"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedID", "valueDataDict": "Aldosterone Antagonist"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedID", "valueDataDict": "Angiotensin Receptor-Neprilysin Inhibitor"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedID", "valueDataDict": "Antiarrhythmic Drug"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedID", "valueDataDict": "Antiplatelet agent"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedID", "valueDataDict": "Angiotensin II Receptor Blocker"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedID", "valueDataDict": "Beta Blocker"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedID", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedID", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedID", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 13, "shortName": "DC_MedID", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 14, "shortName": "DC_MedID", "valueDataDict": "Renin Inhibitor"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 15, "shortName": "DC_MedID", "valueDataDict": "Selective Sinus Node I/f Channel Inhibitor"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 16, "shortName": "DC_MedID", "valueDataDict": "Statin"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "DOB", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "DeviceImplanted", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "Diabetes", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "ECG", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "ECGDate", "valueDataDict": "2023-07-04"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "ECGNormal", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "EPStudy", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "EnrolledStudy", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "FamilialHxNICM", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "FamilialSyndSuddenDeath", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "FinalDeviceType", "valueDataDict": "ICD Dual Chamber"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "FirstName", "valueDataDict": "R"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "GenOpFName", "valueDataDict": "J"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "GenOpLName", "valueDataDict": "M"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "GenOpNPI", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "HF", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "HGB", "valueDataDict": "7.4"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "HGBND", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "HGB_unit", "valueDataDict": "g/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "HIPS", "valueDataDict": "Private Health Insurance"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "HealthIns", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "Hematoma", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "HemoInstability", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "Hemothorax", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "HisLBundleLead", "valueDataDict": "Not Attempted"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "ICDImpID", "valueDataDict": "689"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "ICDImpSerNo", "valueDataDict": "12345"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "ICDIndication", "valueDataDict": "Secondary prevention"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "ISCM", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "InfectionReqAnti", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "InotropicSupport", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "IntraVentConductionType", "valueDataDict": "Left bundle branch block"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "LastName", "valueDataDict": "C"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadCounter", "valueDataDict": "1"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 2, "shortName": "LeadCounter", "valueDataDict": "2"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadDislodge", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadID", "valueDataDict": "4345"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 2, "shortName": "LeadID", "valueDataDict": "1340"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadLocation", "valueDataDict": "RA endocardial"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 2, "shortName": "LeadLocation", "valueDataDict": "RV endocardial"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadOpFName", "valueDataDict": "J"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadOpLName", "valueDataDict": "M"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadOpNPI", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadSerNo", "valueDataDict": "12345"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 2, "shortName": "LeadSerNo", "valueDataDict": "12345"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "LeadType", "valueDataDict": "New   "}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 2, "shortName": "LeadType", "valueDataDict": "New   "}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "NICM", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "NVPQRS", "valueDataDict": "147"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "NVPQRS_unit", "valueDataDict": "msec"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "OtherID", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "OtherStructAbn", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "PCI", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "ParoxySVTHistory", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "Pneumothorax", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "PostMI", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "PostTIA", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "PriPacMode", "valueDataDict": "DDD(R)"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "PrimTachIndPres", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "PrimaryValvularHD", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorCABG", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorCIED", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorCVD", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorLVEF", "valueDataDict": "50"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorLVEFAssessed", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorLVEFDate", "valueDataDict": "2023-07-05"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorLVEF_unit", "valueDataDict": "%"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorMI", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorPCI", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "Prior_AVProc", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcedureEndDateTime", "valueDataDict": "2023-07-07T18:50:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcedureStartDateTime", "valueDataDict": "2023-07-07T14:47:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcedureType", "valueDataDict": "Initial Generator Implant"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "PtRestriction", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceAmIndian", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Race<PERSON>ian", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceBlack", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceNatHaw", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "ReasonForAdmit", "valueDataDict": "Other Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "ReasonPacIndic", "valueDataDict": "Anticipated requirement of > 40% RV pacing"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "SDM_Proc", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "SSNNA", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "SetScrew", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Sex", "valueDataDict": "Female"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "Sodium", "valueDataDict": "139"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "SodiumND", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "Sodium_unit", "valueDataDict": "mEq/L"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "Stroke", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "Syncope", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "SyndromeRiskDeath", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "Tamponade", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "TransplantCandidate", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "TransplantWaitList", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 1, "IncrementalId": 1, "shortName": "UrgentSurgery", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "VFib", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "VPQRS", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "VPaced", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "VT", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "VTDate", "valueDataDict": "2023-06-17"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "VTPostCardiacSurgery", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "VTReverseCause", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "52efe20c-d809-4ce3-8b67-d93d9c8b86ac", "VisitId": 0, "IncrementalId": 1, "shortName": "VTType", "valueDataDict": "Polymorphic VT"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCode", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCodeNA", "valueDataDict": "true"}]