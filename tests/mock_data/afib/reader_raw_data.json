[{"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTBase", "valueDataDict": "true"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS1Q1", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q1", "valueDataDict": "Very bothered"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q10", "valueDataDict": "Quite a bit of difficulty"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q11", "valueDataDict": "Quite a bit of difficulty"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q12", "valueDataDict": "Quite a bit of difficulty"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q13", "valueDataDict": "Extremely bothered"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q14", "valueDataDict": "Extremely bothered"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q15", "valueDataDict": "Moderately bothered"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q16", "valueDataDict": "Moderately bothered"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q17", "valueDataDict": "Moderately bothered"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q18", "valueDataDict": "Moderately bothered"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q19", "valueDataDict": "Extremely dissatisfied"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q2", "valueDataDict": "Very bothered"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q20", "valueDataDict": "Extremely dissatisfied"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q3", "valueDataDict": "Very bothered"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q4", "valueDataDict": "Quite a bit bothered"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q5", "valueDataDict": "Very limited"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q6", "valueDataDict": "Moderately limited"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q7", "valueDataDict": "Quite a bit of difficulty"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q8", "valueDataDict": "Quite a bit of difficulty"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q9", "valueDataDict": "Quite a bit of difficulty"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFTachPresent", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFibCathAblDate", "valueDataDict": "2023-03-03"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFibClass", "valueDataDict": "Paroxysmal"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFibPriorAblStrategyCode", "valueDataDict": "Pulmonary Vein Isolation|Wide Area Circumferential Ablation"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFlutter", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ALKPhosND", "valueDataDict": "true"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ALTND", "valueDataDict": "true"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ASTND", "valueDataDict": "true"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AVFistula", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AVNodePace", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AblLesion", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AblStrategyCode", "valueDataDict": "Pulmonary Vein Isolation|Wide Area Circumferential Ablation|Complex Fractionated Atrial Electrogram"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AccessBleed", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AcuteRF", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AirEmbol", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Anesthesia", "valueDataDict": "General Anesthesia"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ArrivalDate", "valueDataDict": "2024-02-07"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ArtThromb", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AtrialRhythm", "valueDataDict": "Sinus node rhythm"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AtrialThromDetect", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "BaselineImagingPerf", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "BilirubinND", "valueDataDict": "true"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "BradyEvent", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "CAD", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "CArrest", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "CM", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "CVType", "valueDataDict": "true"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "CardTE", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "CathManipulation", "valueDataDict": "Catheter Manipulation - Manual"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadCHF", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadDM", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadHypertCont", "valueDataDict": "true"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadLVDysf", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadStroke", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadTE", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadTIA", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadVascDis", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChronicLungDisease", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Csurgery", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DCAtrialRhythm", "valueDataDict": "Sinus node rhythm"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DCCV", "valueDataDict": "true"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DCDate", "valueDataDict": "2024-02-08"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DCHospice", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DCLocation", "valueDataDict": "Home"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DCStatus", "valueDataDict": "Alive"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 13, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 14, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 15, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 16, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 17, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 18, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 19, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 20, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 21, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 22, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 23, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 24, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 25, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 26, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 27, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 28, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 29, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 30, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 31, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 32, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Digoxin"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedID", "valueDataDict": "Diltiazem"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedID", "valueDataDict": "Verapamil"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedID", "valueDataDict": "Amiodarone"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedID", "valueDataDict": "Disopyramide"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedID", "valueDataDict": "Dofetilide"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedID", "valueDataDict": "Dr<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedID", "valueDataDict": "Flecainide"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedID", "valueDataDict": "Procainamide"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedID", "valueDataDict": "Propafenone"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedID", "valueDataDict": "Quinidine"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedID", "valueDataDict": "Sotalol"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 13, "shortName": "DC_MedID", "valueDataDict": "Aggrenox"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 14, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 15, "shortName": "DC_MedID", "valueDataDict": "Vorapaxar"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 16, "shortName": "DC_MedID", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 17, "shortName": "DC_MedID", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 18, "shortName": "DC_MedID", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 19, "shortName": "DC_MedID", "valueDataDict": "Ticlopidine"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 20, "shortName": "DC_MedID", "valueDataDict": "Fondaparinux"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 21, "shortName": "DC_MedID", "valueDataDict": "Heparin Derivative"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 22, "shortName": "DC_MedID", "valueDataDict": "Low Molecular Weight Heparin"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 23, "shortName": "DC_MedID", "valueDataDict": "Unfractionated Heparin"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 24, "shortName": "DC_MedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 25, "shortName": "DC_MedID", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 26, "shortName": "DC_MedID", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 27, "shortName": "DC_MedID", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 28, "shortName": "DC_MedID", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 29, "shortName": "DC_MedID", "valueDataDict": "Angiotensin Converting Enzyme Inhibitor"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 30, "shortName": "DC_MedID", "valueDataDict": "Angiotensin II Receptor Blocker"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 31, "shortName": "DC_MedID", "valueDataDict": "Beta Blocker"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 32, "shortName": "DC_MedID", "valueDataDict": "Statin"}, {"NCDRPatientId": "34234", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "DOB", "valueDataDict": "1900-01-01"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DVT", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DevID", "valueDataDict": "5396"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DiastolicBP", "valueDataDict": "77"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DiastolicBP_unit", "valueDataDict": "mm[Hg]"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "EnrolledStudy", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "FirstName", "valueDataDict": "<PERSON>"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "FluoroDoseDAP", "valueDataDict": "10"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "FluoroDoseDAP_unit", "valueDataDict": "Gy/cm2"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "FluoroDoseKerm", "valueDataDict": "105"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "FluoroDoseKerm_unit", "valueDataDict": "mGy"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "GIHypo", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "GuidanceMethodID", "valueDataDict": "Electro Anatomic Mapping|Fluoroscopy"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HBAbnLiver", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HBAbnRenal", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HBAlcohol", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HBBleed", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HBDrugAP", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HBDrugNSAID", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HBHyperUncont", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HBLabINR", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HBStroke", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HIC", "valueDataDict": "3GM8P10EG23"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HIPS", "valueDataDict": "Private Health Insurance|Medicare"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HealthIns", "valueDataDict": "true"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Height", "valueDataDict": "175.26"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Height_unit", "valueDataDict": "cm"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Hemorrhage", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Hemothorax", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "INR", "valueDataDict": "1"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "INRND", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "IntraProcAnticoag", "valueDataDict": "true"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "IsolationConfirm", "valueDataDict": "Bidirectional Block"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "LAThrombus", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "LVEF", "valueDataDict": "54"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "LVEFAssessed", "valueDataDict": "true"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "LVEF_unit", "valueDataDict": "%"}, {"NCDRPatientId": "34234", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "LastName", "valueDataDict": "<PERSON><PERSON>"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "MedAdmin", "valueDataDict": "Past"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 10, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 11, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 12, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 13, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 14, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 15, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 16, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 17, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 18, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 19, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 20, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 21, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 22, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 23, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 24, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 25, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 26, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 27, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 28, "shortName": "MedAdmin", "valueDataDict": "Held"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 29, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 30, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 31, "shortName": "MedAdmin", "valueDataDict": "Held"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 32, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "MedID", "valueDataDict": "Digoxin"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "MedID", "valueDataDict": "Diltiazem"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "MedID", "valueDataDict": "Verapamil"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "MedID", "valueDataDict": "Amiodarone"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "MedID", "valueDataDict": "Disopyramide"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "MedID", "valueDataDict": "Dofetilide"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "MedID", "valueDataDict": "Dr<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "MedID", "valueDataDict": "Flecainide"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "MedID", "valueDataDict": "Procainamide"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 10, "shortName": "MedID", "valueDataDict": "Propafenone"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 11, "shortName": "MedID", "valueDataDict": "Quinidine"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 12, "shortName": "MedID", "valueDataDict": "Sotalol"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 13, "shortName": "MedID", "valueDataDict": "Aggrenox"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 14, "shortName": "MedID", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 15, "shortName": "MedID", "valueDataDict": "Vorapaxar"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 16, "shortName": "MedID", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 17, "shortName": "MedID", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 18, "shortName": "MedID", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 19, "shortName": "MedID", "valueDataDict": "Ticlopidine"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 20, "shortName": "MedID", "valueDataDict": "Fondaparinux"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 21, "shortName": "MedID", "valueDataDict": "Heparin Derivative"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 22, "shortName": "MedID", "valueDataDict": "Low Molecular Weight Heparin"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 23, "shortName": "MedID", "valueDataDict": "Unfractionated Heparin"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 24, "shortName": "MedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 25, "shortName": "MedID", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 26, "shortName": "MedID", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 27, "shortName": "MedID", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 28, "shortName": "MedID", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 29, "shortName": "MedID", "valueDataDict": "Angiotensin Converting Enzyme Inhibitor"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 30, "shortName": "MedID", "valueDataDict": "Angiotensin II Receptor Blocker"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 31, "shortName": "MedID", "valueDataDict": "Beta Blocker"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 32, "shortName": "MedID", "valueDataDict": "Statin"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "NumVeinsIso", "valueDataDict": "Two Vein"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "NumVeinsPres", "valueDataDict": "Four Vein"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "NumVeinsTarg", "valueDataDict": "Two Vein"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "OperA_FirstName", "valueDataDict": "<PERSON>"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "OperA_LastName", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "OperA_MidName", "valueDataDict": "<PERSON>"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "OperA_NPI", "valueDataDict": "**********"}, {"NCDRPatientId": "34234", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "OtherID", "valueDataDict": "32121321"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PT", "valueDataDict": "13.9"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PTND", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PT_unit", "valueDataDict": "sec"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PVDD", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PVIAssesCircVeinCath", "valueDataDict": "true"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PacingManeuversEval", "valueDataDict": "true"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PeriEffusionInterv", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PeriEffusionTamp", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PeripNerveInjury", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PharmaCV", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PhrenicNerveDam", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PhrenicNerveEval", "valueDataDict": "true"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PleuralEffusion", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Pneumonia", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Pneumothorax", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PostBleedHematoma", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PostGUBleed", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PostHF", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PostMI", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PostTIA", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PreProcCreat", "valueDataDict": "1.02"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PreProcCreatND", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PreProcCreat_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PrevAFibTerm", "valueDataDict": "true"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PrevAFibTermCA", "valueDataDict": "true"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PrevAFibTermDC", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PrevAFibTermPC", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PrevAFibTermSA", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcBivalirudin", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcHeparin", "valueDataDict": "true"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcHeparinInitAdmin", "valueDataDict": "Pre-transseptal Puncture"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcOtherAnticoag", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcStatus", "valueDataDict": "Elective Procedure"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcedureStartDateTime", "valueDataDict": "2024-02-07T09:03:00"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Pseudoaneurysm", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PtRestriction", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PulmonaryEmbolism", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Pulse", "valueDataDict": "63"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Pulse_unit", "valueDataDict": "bpm"}, {"NCDRPatientId": "34234", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceAmIndian", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Race<PERSON>ian", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceBlack", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceNatHaw", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "true"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "RespFailure", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "SSNNA", "valueDataDict": "true"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON>", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Sex", "valueDataDict": "Male"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "SleepApnea", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Stroke", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "SympDuringAFFlutter", "valueDataDict": "Symptomatic"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "SystolicBP", "valueDataDict": "147"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "SystolicBP_unit", "valueDataDict": "mm[Hg]"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "TEEDate", "valueDataDict": "2024-02-07"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "TEEPerf", "valueDataDict": "true"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "TTEPerf", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "TransseptCath", "valueDataDict": "Double"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ValveDam", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ValvularAF", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "VascularInjury", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Warfarin", "valueDataDict": "false"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Weight", "valueDataDict": "88.59"}, {"NCDRPatientId": "34234", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Weight_unit", "valueDataDict": "kg"}, {"NCDRPatientId": "34234", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCode", "valueDataDict": "97111"}, {"NCDRPatientId": "34234", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCodeNA", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTBase", "valueDataDict": "true"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS1Q1", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS1Q2", "valueDataDict": "Earlier today"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q1", "valueDataDict": "Quite a bit bothered"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q10", "valueDataDict": "No difficulty at all"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q11", "valueDataDict": "No difficulty at all"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q12", "valueDataDict": "No difficulty at all"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q13", "valueDataDict": "A little bothered"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q14", "valueDataDict": "A little bothered"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q15", "valueDataDict": "Not at all bothered"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q16", "valueDataDict": "A little bothered"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q17", "valueDataDict": "Hardly bothered "}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q18", "valueDataDict": "A little bothered"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q19", "valueDataDict": "Somewhat satisfied"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q2", "valueDataDict": "Quite a bit bothered"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q20", "valueDataDict": "Somewhat satisfied"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q3", "valueDataDict": "Moderately bothered"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q4", "valueDataDict": "A little bothered"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q5", "valueDataDict": "Hardly limited"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q6", "valueDataDict": "Not at all limited"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q7", "valueDataDict": "Hardly any difficulty"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q8", "valueDataDict": "Hardly any difficulty"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q9", "valueDataDict": "Hardly any difficulty"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFTachPresent", "valueDataDict": "true"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFibClass", "valueDataDict": "Paroxysmal"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFlutter", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ALKPhosND", "valueDataDict": "true"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ALTND", "valueDataDict": "true"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ASTND", "valueDataDict": "true"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AVFistula", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AVNodePace", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AblLesion", "valueDataDict": "true"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AblLesionLoc", "valueDataDict": "Other"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AblStrategyCode", "valueDataDict": "Pulmonary Vein Isolation|Wide Area Circumferential Ablation|Complex Fractionated Atrial Electrogram"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AccessBleed", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AcuteRF", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AirEmbol", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Anesthesia", "valueDataDict": "General Anesthesia"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ArrivalDate", "valueDataDict": "2024-01-25"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ArtThromb", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AtrialRhythm", "valueDataDict": "Sinus node rhythm"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AtrialThromDetect", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "BaselineImagingPerf", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "BilirubinND", "valueDataDict": "true"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "BradyEvent", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "CAD", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "CArrest", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "CM", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "CVType", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "CardTE", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "CathManipulation", "valueDataDict": "Catheter Manipulation - Manual"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadCHF", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadDM", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadHypertCont", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadLVDysf", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadStroke", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadTE", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadTIA", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadVascDis", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChronicLungDisease", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Csurgery", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DCAtrialRhythm", "valueDataDict": "Sinus node rhythm"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DCDate", "valueDataDict": "2024-01-26"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DCHospice", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DCLocation", "valueDataDict": "Home"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DCStatus", "valueDataDict": "Alive"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 13, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 14, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 15, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 16, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 17, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 18, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 19, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 20, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 21, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 22, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 23, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 24, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 25, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 26, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 27, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 28, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 29, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 30, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 31, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 32, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Digoxin"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedID", "valueDataDict": "Diltiazem"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedID", "valueDataDict": "Verapamil"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedID", "valueDataDict": "Amiodarone"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedID", "valueDataDict": "Disopyramide"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedID", "valueDataDict": "Dofetilide"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedID", "valueDataDict": "Dr<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedID", "valueDataDict": "Flecainide"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedID", "valueDataDict": "Procainamide"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedID", "valueDataDict": "Propafenone"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedID", "valueDataDict": "Quinidine"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedID", "valueDataDict": "Sotalol"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 13, "shortName": "DC_MedID", "valueDataDict": "Aggrenox"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 14, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 15, "shortName": "DC_MedID", "valueDataDict": "Vorapaxar"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 16, "shortName": "DC_MedID", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 17, "shortName": "DC_MedID", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 18, "shortName": "DC_MedID", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 19, "shortName": "DC_MedID", "valueDataDict": "Ticlopidine"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 20, "shortName": "DC_MedID", "valueDataDict": "Fondaparinux"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 21, "shortName": "DC_MedID", "valueDataDict": "Heparin Derivative"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 22, "shortName": "DC_MedID", "valueDataDict": "Low Molecular Weight Heparin"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 23, "shortName": "DC_MedID", "valueDataDict": "Unfractionated Heparin"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 24, "shortName": "DC_MedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 25, "shortName": "DC_MedID", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 26, "shortName": "DC_MedID", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 27, "shortName": "DC_MedID", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 28, "shortName": "DC_MedID", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 29, "shortName": "DC_MedID", "valueDataDict": "Angiotensin Converting Enzyme Inhibitor"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 30, "shortName": "DC_MedID", "valueDataDict": "Angiotensin II Receptor Blocker"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 31, "shortName": "DC_MedID", "valueDataDict": "Beta Blocker"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 32, "shortName": "DC_MedID", "valueDataDict": "Statin"}, {"NCDRPatientId": "34298", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "DOB", "valueDataDict": "1900-01-01"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DVT", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DevID", "valueDataDict": "5396"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DiastolicBP", "valueDataDict": "71"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DiastolicBP_unit", "valueDataDict": "mm[Hg]"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "EnrolledStudy", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "FirstName", "valueDataDict": "<PERSON>"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "GIHypo", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "GuidanceMethodID", "valueDataDict": "Electro Anatomic Mapping"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HBAbnLiver", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HBAbnRenal", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HBAlcohol", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HBBleed", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HBDrugAP", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HBDrugNSAID", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HBHyperUncont", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HBLabINR", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HBStroke", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HIPS", "valueDataDict": "Private Health Insurance"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HealthIns", "valueDataDict": "true"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Height", "valueDataDict": "170.18"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Height_unit", "valueDataDict": "cm"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Hemorrhage", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Hemothorax", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "INR", "valueDataDict": ".9"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "INRND", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "IntraProcAnticoag", "valueDataDict": "true"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "IsolationConfirm", "valueDataDict": "Bidirectional Block"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "LAThrombus", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "LVEF", "valueDataDict": "55"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "LVEFAssessed", "valueDataDict": "true"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "LVEF_unit", "valueDataDict": "%"}, {"NCDRPatientId": "34298", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "LastName", "valueDataDict": "<PERSON><PERSON>"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "MedAdmin", "valueDataDict": "Held"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 10, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 11, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 12, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 13, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 14, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 15, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 16, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 17, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 18, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 19, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 20, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 21, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 22, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 23, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 24, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 25, "shortName": "MedAdmin", "valueDataDict": "Held"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 26, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 27, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 28, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 29, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 30, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 31, "shortName": "MedAdmin", "valueDataDict": "Held"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 32, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "MedID", "valueDataDict": "Digoxin"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "MedID", "valueDataDict": "Diltiazem"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "MedID", "valueDataDict": "Verapamil"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "MedID", "valueDataDict": "Amiodarone"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "MedID", "valueDataDict": "Disopyramide"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "MedID", "valueDataDict": "Dofetilide"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "MedID", "valueDataDict": "Dr<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "MedID", "valueDataDict": "Flecainide"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "MedID", "valueDataDict": "Procainamide"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 10, "shortName": "MedID", "valueDataDict": "Propafenone"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 11, "shortName": "MedID", "valueDataDict": "Quinidine"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 12, "shortName": "MedID", "valueDataDict": "Sotalol"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 13, "shortName": "MedID", "valueDataDict": "Aggrenox"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 14, "shortName": "MedID", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 15, "shortName": "MedID", "valueDataDict": "Vorapaxar"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 16, "shortName": "MedID", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 17, "shortName": "MedID", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 18, "shortName": "MedID", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 19, "shortName": "MedID", "valueDataDict": "Ticlopidine"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 20, "shortName": "MedID", "valueDataDict": "Fondaparinux"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 21, "shortName": "MedID", "valueDataDict": "Heparin Derivative"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 22, "shortName": "MedID", "valueDataDict": "Low Molecular Weight Heparin"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 23, "shortName": "MedID", "valueDataDict": "Unfractionated Heparin"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 24, "shortName": "MedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 25, "shortName": "MedID", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 26, "shortName": "MedID", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 27, "shortName": "MedID", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 28, "shortName": "MedID", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 29, "shortName": "MedID", "valueDataDict": "Angiotensin Converting Enzyme Inhibitor"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 30, "shortName": "MedID", "valueDataDict": "Angiotensin II Receptor Blocker"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 31, "shortName": "MedID", "valueDataDict": "Beta Blocker"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 32, "shortName": "MedID", "valueDataDict": "Statin"}, {"NCDRPatientId": "34298", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "MidName", "valueDataDict": "F"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "NumVeinsIso", "valueDataDict": "Four Vein"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "NumVeinsPres", "valueDataDict": "Four Vein"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "NumVeinsTarg", "valueDataDict": "Four Vein"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "OperA_FirstName", "valueDataDict": "<PERSON>"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "OperA_LastName", "valueDataDict": "Salcedo"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "OperA_NPI", "valueDataDict": "**********"}, {"NCDRPatientId": "34298", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "OtherID", "valueDataDict": "32121321"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PT", "valueDataDict": "13.2"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PTND", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PT_unit", "valueDataDict": "sec"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PVDD", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PVIAssesCircVeinCath", "valueDataDict": "true"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PacingManeuversEval", "valueDataDict": "true"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PeriEffusionInterv", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PeriEffusionTamp", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PeripNerveInjury", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PhrenicNerveDam", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PhrenicNerveEval", "valueDataDict": "true"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PleuralEffusion", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Pneumonia", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Pneumothorax", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PostBleedHematoma", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PostGUBleed", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PostHF", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PostMI", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PostTIA", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PreProcCreat", "valueDataDict": ".8"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PreProcCreatND", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PreProcCreat_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PrevAFibTerm", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcBivalirudin", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcHeparin", "valueDataDict": "true"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcHeparinInitAdmin", "valueDataDict": "Post-transseptal Puncture"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcOtherAnticoag", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcStatus", "valueDataDict": "Elective Procedure"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcedureStartDateTime", "valueDataDict": "2024-01-25T13:21:00"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Pseudoaneurysm", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PtRestriction", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PulmonaryEmbolism", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Pulse", "valueDataDict": "54"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Pulse_unit", "valueDataDict": "bpm"}, {"NCDRPatientId": "34298", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceAmIndian", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Race<PERSON>ian", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceBlack", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceNatHaw", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "RespFailure", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "SSNNA", "valueDataDict": "true"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON>", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Sex", "valueDataDict": "Male"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "SleepApnea", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Stroke", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "SympDuringAFFlutter", "valueDataDict": "Symptomatic"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "SystolicBP", "valueDataDict": "103"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "SystolicBP_unit", "valueDataDict": "mm[Hg]"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "TEEDate", "valueDataDict": "2024-01-25"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "TEEPerf", "valueDataDict": "true"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "TTEPerf", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "TransseptCath", "valueDataDict": "Double"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ValveDam", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ValvularAF", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "VascularInjury", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Warfarin", "valueDataDict": "false"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Weight", "valueDataDict": "64.37"}, {"NCDRPatientId": "34298", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Weight_unit", "valueDataDict": "kg"}, {"NCDRPatientId": "34298", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCode", "valueDataDict": "97111"}, {"NCDRPatientId": "34298", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCodeNA", "valueDataDict": "false"}]