[{"Container Class": "patientContainer", "Parent Section": "Root", "Section Display Name": "<PERSON><PERSON>", "Section Code": "DEMOGRAPHICS", "Section Type": "Section", "Cardinality": "1..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "<PERSON>. Episode of Care", "Section Code": "EPISODEOFCARE", "Section Type": "Section", "Cardinality": "1..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "<PERSON>. Episode of Care", "Section Display Name": "Episode Information", "Section Code": "EOCINFO", "Section Type": "Section", "Cardinality": "1..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Episode Information", "Section Display Name": "Attending Providers", "Section Code": "ATTPROVIDERS", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "<PERSON>. Episode of Care", "Section Display Name": "Research Study", "Section Code": "RSTUDY", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "C. History and Risk Factors", "Section Code": "HXANDRISKFACTORS", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "E. Procedure Information", "Section Code": "PROCINFO", "Section Type": "Repeater Section", "Cardinality": "1..n"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "E. Procedure Information", "Section Display Name": "D. Pre-Procedure Information", "Section Code": "PREPROCINFO", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "D. Pre-Procedure Information", "Section Display Name": "Diagnostic Test", "Section Code": "DIAGNOSTICTEST", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Diagnostic Test", "Section Display Name": "Stress Test", "Section Code": "STRESSTEST", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "D. Pre-Procedure Information", "Section Display Name": "Pre-Procedure Medications", "Section Code": "PREPROCMED", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "D. Pre-Procedure Information", "Section Display Name": "SA Questionnaire", "Section Code": "SAQ", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "D. Pre-Procedure Information", "Section Display Name": "Rose Dyspnea Scale", "Section Code": "ROSE", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "E. Procedure Information", "Section Display Name": "Closure Methods", "Section Code": "CLMETHOD", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "E. Procedure Information", "Section Display Name": "F. Labs", "Section Code": "LABS", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "F. Labs", "Section Display Name": "Pre-Procedure Labs", "Section Code": "PREPROCLABS", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "F. Labs", "Section Display Name": "Post-Procedure Labs", "Section Code": "POSTPROCLABS", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "E. Procedure Information", "Section Display Name": "G. Cath Lab Visit", "Section Code": "LABVISIT", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "G. Cath Lab Visit", "Section Display Name": "Valvular Disease Stenosis", "Section Code": "VALVULARDZSTEN", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "G. Cath Lab Visit", "Section Display Name": "Valvular Disease Regurgitation", "Section Code": "VALVULARDZREGURG", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "E. Procedure Information", "Section Display Name": "H. Co<PERSON> Anatomy", "Section Code": "CORANATOMY", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "H. Co<PERSON> Anatomy", "Section Display Name": "Native Vessel", "Section Code": "NVESSEL", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "H. Co<PERSON> Anatomy", "Section Display Name": "<PERSON><PERSON>", "Section Code": "GVESSEL", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "E. Procedure Information", "Section Display Name": "I. PCI Procedure", "Section Code": "PCIPROC", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "I. PCI Procedure", "Section Display Name": "Procedure Medications", "Section Code": "PROCMED", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "I. PCI Procedure", "Section Display Name": "J. Lesions and Devices", "Section Code": "LESIONDEV", "Section Type": "Repeater Section", "Cardinality": "1..n"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "I. PCI Procedure", "Section Display Name": "Devices", "Section Code": "DEVICES", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "E. Procedure Information", "Section Display Name": "K. Intra and Post-Procedure Events", "Section Code": "INTPOSTEVENT", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "K. Intra and Post-Procedure Events", "Section Display Name": "Intra and Post-Procedure Events", "Section Code": "IPPEVENTS", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "<PERSON><PERSON>", "Section Code": "DISCHARGE", "Section Type": "Section", "Cardinality": "1..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "<PERSON><PERSON>", "Section Display Name": "Discharge Medications", "Section Code": "DISCHMED", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "followupContainer", "Parent Section": "Root", "Section Display Name": "<PERSON><PERSON> Follow-Up", "Section Code": "FUP", "Section Type": "Section", "Cardinality": "1..1"}, {"Container Class": "followupContainer", "Parent Section": "<PERSON><PERSON> Follow-Up", "Section Display Name": "Follow-Up Research Study", "Section Code": "FUP-RSTUDY", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "followupContainer", "Parent Section": "<PERSON><PERSON> Follow-Up", "Section Display Name": "Follow-Up Events", "Section Code": "FUP-EVENT", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "followupContainer", "Parent Section": "<PERSON><PERSON> Follow-Up", "Section Display Name": "Follow-Up Medications", "Section Code": "FUP-MEDPRES", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "followupContainer", "Parent Section": "<PERSON><PERSON> Follow-Up", "Section Display Name": "Follow-Up SA Questionnaire", "Section Code": "FUP-SAQ", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "followupContainer", "Parent Section": "<PERSON><PERSON> Follow-Up", "Section Display Name": "Follow-Up Rose Dyspnea Scale", "Section Code": "FUP-ROSE", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "submissionInfoContainer", "Parent Section": "Root", "Section Display Name": "Z. Administration", "Section Code": "ADMIN", "Section Type": "Section", "Cardinality": "1..1"}]