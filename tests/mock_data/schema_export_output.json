{"Dictionary": {"Dataset": {"Code": "", "Id": ""}, "Version": "", "Type": "<PERSON><PERSON><PERSON>", "Sections": [{"Container Class": "patientContainer", "Parent Section": "Root", "Section Display Name": "<PERSON><PERSON>", "Section Code": "DEMOGRAPHICS", "Section Type": "Section", "Cardinality": "1..1", "Table": "DEMOGRAPHICS", "Elements": [{"Element Reference": 2000, "Name": "Last Name", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate the patient's last name", "Target Value": "The value on arrival at this facility", "Short Name": "LastName", "Data Type": "LN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "DDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2010, "Name": "First Name", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate the patient's first name.", "Target Value": "The value on arrival at this facility", "Short Name": "FirstName", "Data Type": "FN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "DDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}, {"Element Reference": 2020, "Name": "Middle Name", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate the patient's middle name", "Target Value": "The value on arrival at this facility", "Short Name": "MidName", "Data Type": "MN", "Precision": "50", "Unit Of Measure": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "DDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code": "**********", "Code System": "2.16.840.1.113883.3.3478.6.1", "Code System Name": "ACC NCDR", "Vendor Instruction": null, "Selections": null, "Ranges": null, "Definition": null, "Parent Child Validations": null}]}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "<PERSON>. Episode of Care", "Section Code": "EPISODEOFCARE", "Section Type": "Section", "Cardinality": "1..1", "Table": "EPISODEOFCARE", "Elements": null}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "<PERSON>. Episode of Care", "Section Display Name": "Episode Information", "Section Code": "EOCINFO", "Section Type": "Section", "Cardinality": "1..1", "Table": "EPISODEOFCARE", "Elements": null}, {"Container Class": "submissionInfoContainer", "Parent Section": "ADMIN", "Section Display Name": "ADMIN", "Section Code": "ADMIN", "Section Type": "Section", "Cardinality": "1..1", "Table": "ADMIN", "Elements": null}]}}