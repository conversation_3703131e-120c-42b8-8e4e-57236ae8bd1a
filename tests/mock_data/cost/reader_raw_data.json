[{"encounternumber": "**********.0", "departmentname": "CAC - Primary Care", "svcitemchargecode": "2957", "chargecodename": "HB OFFICE-OUTPATIENT ESTABLISHED SF MDM 10 MIN", "cpt4hcpcscode": "99212", "cpt4hcpcsname": "Office/Outpatient Visit, Est Pat", "ub92revcode": "510", "ub92revname": "Clinic-general classification", "svcitemdate": "11/1/2024", "dayofstay": "Day 1", "units": "1", "charges": "$180.00 ", "directcost": "70.6857", "indirectcost": "16.3706", "additional_fields": "{\"departmentcode\": \"63400 - 7624\", \"directcostfixed\": \"21.2386\", \"directcostvariable\": \"49.4471\", \"fixedindirect\": \"18.4458\", \"indirectcostvariable\": \"-2.0752\"}", "ClientFileId": 1, "FileName": "cost", "BiomeImportDt": 1745914112000, "Version": "1.0", "DatasetName": "biome"}, {"encounternumber": "**********.0", "departmentname": "CAC - Primary Care", "svcitemchargecode": "NDC80777-110-01", "chargecodename": "SPIKEVAX 50 MCG-0.5ML IM SUSY", "cpt4hcpcscode": "90480", "cpt4hcpcsname": null, "ub92revcode": "771", "ub92revname": "Preventive care services-vaccine administration", "svcitemdate": "11/1/2024", "dayofstay": "Day 1", "units": "1", "charges": "$58.00 ", "directcost": "110.8293", "indirectcost": "32.5195", "additional_fields": "{\"departmentcode\": \"63400 - 7624\", \"directcostfixed\": \"18.217\", \"directcostvariable\": \"92.6123\", \"fixedindirect\": \"35.3271\", \"indirectcostvariable\": \"-2.8076\"}", "ClientFileId": 1, "FileName": "cost", "BiomeImportDt": 1745914112000, "Version": "1.0", "DatasetName": "biome"}, {"encounternumber": "1000012370.0", "departmentname": "ACC - Phlebotomy", "svcitemchargecode": "71431", "chargecodename": "HB LAB VENIPUNCTURE", "cpt4hcpcscode": "36415", "cpt4hcpcsname": "Routine Venipuncture", "ub92revcode": "300", "ub92revname": "Laboratory-general", "svcitemdate": "11/1/2024", "dayofstay": "Day 1", "units": "1", "charges": "$34.00 ", "directcost": "27.5087", "indirectcost": "6.5852", "additional_fields": "{\"departmentcode\": \"63400 - 7128\", \"directcostfixed\": \"14.8879\", \"directcostvariable\": \"12.6208\", \"fixedindirect\": \"6.607\", \"indirectcostvariable\": \"-0.0218\"}", "ClientFileId": 1, "FileName": "cost", "BiomeImportDt": 1745914112000, "Version": "1.0", "DatasetName": "biome"}, {"encounternumber": "1000012393.0", "departmentname": "CAC - Neuroscience", "svcitemchargecode": "8889", "chargecodename": "HB PROCEDURE ONLY VISIT", "cpt4hcpcscode": "0", "cpt4hcpcsname": null, "ub92revcode": "0", "ub92revname": null, "svcitemdate": "11/1/2024", "dayofstay": "Day 1", "units": "1", "charges": "$0.00 ", "directcost": "44.5467", "indirectcost": "17.645", "additional_fields": "{\"departmentcode\": \"63400 - 7622\", \"directcostfixed\": \"27.541\", \"directcostvariable\": \"17.0057\", \"fixedindirect\": \"18.6506\", \"indirectcostvariable\": \"-1.0056\"}", "ClientFileId": 1, "FileName": "cost", "BiomeImportDt": 1745914112000, "Version": "1.0", "DatasetName": "biome"}, {"encounternumber": "**********", "departmentname": "CAC - Multi Specialty", "svcitemchargecode": "2958", "chargecodename": "HB OFFICE-OUTPATIENT ESTABLISHED LOW MDM 20 MIN", "cpt4hcpcscode": "99213", "cpt4hcpcsname": "Office/Outpatient Visit, Est Pat", "ub92revcode": "510", "ub92revname": "Clinic-general classification", "svcitemdate": "11/1/2024", "dayofstay": "Day 1", "units": "1", "charges": "$180.00 ", "directcost": "50.2902", "indirectcost": "22.8145", "additional_fields": "{\"departmentcode\": \"63400 - 7621\", \"directcostfixed\": \"17.339\", \"directcostvariable\": \"32.9512\", \"fixedindirect\": \"23.3485\", \"indirectcostvariable\": \"-0.534\"}", "ClientFileId": 1, "FileName": "cost", "BiomeImportDt": 1745914112000, "Version": "1.0", "DatasetName": "biome"}, {"encounternumber": "**********", "departmentname": "Chemistry", "svcitemchargecode": "70378", "chargecodename": "HB PROTEIN TOTAL XCPT REFRACTOMETRY URINE", "cpt4hcpcscode": "84156", "cpt4hcpcsname": "<PERSON><PERSON>, <PERSON><PERSON>", "ub92revcode": "300", "ub92revname": "Laboratory-general", "svcitemdate": "11/1/2024", "dayofstay": "Day 1", "units": "1", "charges": "$19.00 ", "directcost": "4.2089", "indirectcost": "3.1661", "additional_fields": "{\"departmentcode\": \"63000 - 7063\", \"directcostfixed\": \"0.5352\", \"directcostvariable\": \"3.6737\", \"fixedindirect\": \"2.6532\", \"indirectcostvariable\": \"0.5129\"}", "ClientFileId": 1, "FileName": "cost", "BiomeImportDt": 1745914112000, "Version": "1.0", "DatasetName": "biome"}]