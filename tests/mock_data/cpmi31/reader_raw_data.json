[{"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "AdmitDateTime", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "AnatomicalImagingResult", "valueDataDict": "Not performed"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "AngioDelayPtReason", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "ArriMedCode", "valueDataDict": "<PERSON><PERSON><PERSON> (Any)"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 2, "shortName": "ArriMedCode", "valueDataDict": "<PERSON><PERSON><PERSON> (Any)"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "ArrivalDateTime", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "CABGDateTime", "valueDataDict": "2023-09-16T09:00:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "CABGFirst2", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "CADtype2", "valueDataDict": "Obstructive"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "CAOutHospital", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "CE_RBC", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "CE_RBCCABG", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "CE_RBCDate", "valueDataDict": "2023-09-06"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "CPCscore", "valueDataDict": "1 - Good cerebral performance"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "CSHAScaleArrival", "valueDataDict": "5: Mildly frail"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "CTAPerformed", "valueDataDict": "No - No reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "CardiacAngioResults", "valueDataDict": "CAD"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "CathArrivalDateTime", "valueDataDict": "2023-08-28T09:29:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "CathLabAct", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "ChestPainSymp", "valueDataDict": "Prior to arrival"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "ConditionHx", "valueDataDict": "Atrial Fibrillation"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 2, "shortName": "ConditionHx", "valueDataDict": "Atrial Flutter"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 3, "shortName": "ConditionHx", "valueDataDict": "Cancer"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 4, "shortName": "ConditionHx", "valueDataDict": "Dyslipidemia"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 5, "shortName": "ConditionHx", "valueDataDict": "Myocardial Infarction"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 6, "shortName": "ConditionHx", "valueDataDict": "Peripheral Arterial Disease"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "ConditionHxOccurenceArrival", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 2, "shortName": "ConditionHxOccurenceArrival", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 3, "shortName": "ConditionHxOccurenceArrival", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 4, "shortName": "ConditionHxOccurenceArrival", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 5, "shortName": "ConditionHxOccurenceArrival", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 6, "shortName": "ConditionHxOccurenceArrival", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "CreatNotDrawn", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "CreatPeak", "valueDataDict": "1.5"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "CreatPeakDateTime", "valueDataDict": "2023-09-20T07:19:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "CreatPeakNotDrawn", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "CreatPeak_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "DCDateTime", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "DCFName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "DCHospice", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "DCLName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "DCLocation", "valueDataDict": "Extended care/transitional care unit/Rehab"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "DCNPI", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "DCStatus", "valueDataDict": "Alive"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_Aspirin100MGplus", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_CardRehab", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_Comfort", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_LVEF", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedDose", "valueDataDict": "High"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON> (Any)"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedID", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedID", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedID", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedID", "valueDataDict": "Beta blocker (Any)"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedID", "valueDataDict": "Angiotensin converting enzyme inhibitor (ACE-I) (Any)"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedID", "valueDataDict": "Angiotensin receptor blocker (ARB) (Any)"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedID", "valueDataDict": "Angiotensin II receptor blocker neprilysin inhibitor (ARNI)"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedID", "valueDataDict": "Aldosterone receptor antagonist (Any)"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedID", "valueDataDict": "Direct oral anticoagulants (DOAC) (Any)"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON> (Any)"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "DCathFName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "DCathLName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "DCathNPI", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "DOB", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "DiabetesBA", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "DiagCorAngio1st", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "DiagCorAngioDateTime", "valueDataDict": "2023-08-28T09:56:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "ECGCounter", "valueDataDict": "1"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 2, "shortName": "ECGCounter", "valueDataDict": "2"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 3, "shortName": "ECGCounter", "valueDataDict": "3"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "ECGDateTime", "valueDataDict": "2023-08-27T21:22:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 2, "shortName": "ECGDateTime", "valueDataDict": "2023-08-27T23:16:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 3, "shortName": "ECGDateTime", "valueDataDict": "2023-08-29T13:00:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "ECGReadDateTime", "valueDataDict": "2023-08-27T21:27:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 3, "shortName": "ECGReadDateTime", "valueDataDict": "2023-09-25T13:35:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "EDDisposition", "valueDataDict": "Inpatient"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "EDFName", "valueDataDict": "GLEN"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "EDLName", "valueDataDict": "HOOKEY"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "EDMName", "valueDataDict": "CHARLES"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "EDNPI", "valueDataDict": "**********"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "EnrolledStudy", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "EpiEvent", "valueDataDict": "Atrial fibrillation"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 2, "shortName": "EpiEvent", "valueDataDict": "Atrial fibrillation"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 3, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Access site"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 4, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Gastrointestinal"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 5, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Genitourinary"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 6, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Hematoma at access site"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 7, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Other"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 8, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Retroperitoneal"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 9, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Surgical procedure or intervention required"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 10, "shortName": "EpiEvent", "valueDataDict": "Cardiac arrest"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 11, "shortName": "EpiEvent", "valueDataDict": "Cardiac arrest"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 12, "shortName": "EpiEvent", "valueDataDict": "Cardiogenic shock"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 13, "shortName": "EpiEvent", "valueDataDict": "Heart failure"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 14, "shortName": "EpiEvent", "valueDataDict": "Myocardial infarction"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 15, "shortName": "EpiEvent", "valueDataDict": "New requirement for dialysis"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 16, "shortName": "EpiEvent", "valueDataDict": "Respiratory support - Bi-PAP"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 17, "shortName": "EpiEvent", "valueDataDict": "Respiratory support - High-flow oxygen"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 18, "shortName": "EpiEvent", "valueDataDict": "Respiratory support - Intubation"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 19, "shortName": "EpiEvent", "valueDataDict": "Stroke - Hemorrhagic"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 20, "shortName": "EpiEvent", "valueDataDict": "Stroke - Ischemic"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 21, "shortName": "EpiEvent", "valueDataDict": "Stroke - Undetermined"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 22, "shortName": "EpiEvent", "valueDataDict": "Transient ischemic attack (TIA)"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 23, "shortName": "EpiEvent", "valueDataDict": "Ventricular fibrillation"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 24, "shortName": "EpiEvent", "valueDataDict": "Sustained ventricular tachycardia"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "EpiEventDateTime", "valueDataDict": "2023-09-19T11:00:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 2, "shortName": "EpiEventDateTime", "valueDataDict": "2023-09-21T11:00:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 10, "shortName": "EpiEventDateTime", "valueDataDict": "2023-09-20T03:58:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 11, "shortName": "EpiEventDateTime", "valueDataDict": "2023-09-20T04:58:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 16, "shortName": "EpiEventDateTime", "valueDataDict": "2023-09-19T05:11:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 17, "shortName": "EpiEventDateTime", "valueDataDict": "2023-09-17T19:00:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 18, "shortName": "EpiEventDateTime", "valueDataDict": "2023-09-20T04:11:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 24, "shortName": "EpiEventDateTime", "valueDataDict": "2023-09-20T03:58:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "EpiEventOccurred", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 2, "shortName": "EpiEventOccurred", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 3, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 4, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 5, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 6, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 7, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 8, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 9, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 10, "shortName": "EpiEventOccurred", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 11, "shortName": "EpiEventOccurred", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 12, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 13, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 14, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 15, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 16, "shortName": "EpiEventOccurred", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 17, "shortName": "EpiEventOccurred", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 18, "shortName": "EpiEventOccurred", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 19, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 20, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 21, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 22, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 23, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 24, "shortName": "EpiEventOccurred", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "FMCCreat", "valueDataDict": "0.9"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "FMCCreat_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "FirstFacMeansTrans", "valueDataDict": "Self/Family"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "FirstName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "FuncTestResult", "valueDataDict": "Not performed"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "HDLNotDrawn", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "HFFirstMedCon", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "HIPS", "valueDataDict": "Medicare (Part A or B)"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "HRFirstMedCon", "valueDataDict": "63"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "HRFirstMedCon_unit", "valueDataDict": "bpm"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "HbA1cNotDrawn", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "HbA1cValue", "valueDataDict": "5.8"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "HbA1cValue_unit", "valueDataDict": "%"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "HealthIns", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "HeightAD", "valueDataDict": "162.6"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "HeightAD_unit", "valueDataDict": "cm"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "HgbNotDrawn", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 2, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 3, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 4, "shortName": "HomeMedPrescrib", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 5, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 6, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "HomeMeds", "valueDataDict": "ACE Inhibitors"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 2, "shortName": "HomeMeds", "valueDataDict": "ARB (Angiotensin Receptor Blockers)"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 3, "shortName": "HomeMeds", "valueDataDict": "ARNI"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 4, "shortName": "HomeMeds", "valueDataDict": "Beta Blocker"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 5, "shortName": "HomeMeds", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 6, "shortName": "HomeMeds", "valueDataDict": "ACE Inhibitors"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "HospClinicTrial", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "HxCVD", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "Hypertension", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "HypothermiaInducedDateTime", "valueDataDict": "2023-09-20T04:30:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "HypothermiaInducedFMCDC", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "INRNotDrawn", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "InitHgbValue", "valueDataDict": "12.3"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "InitHgbValue_unit", "valueDataDict": "g/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "InitINRValue", "valueDataDict": "1"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "InitialTargetTempGoal", "valueDataDict": "36"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "InitialTargetTempGoal_unit", "valueDataDict": "°C"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "IschemiaEval", "valueDataDict": "No - No reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "LDLNotDrawn", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "LVEFPercent", "valueDataDict": "30"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "LVEFPercent_unit", "valueDataDict": "%"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "LabTropAssay", "valueDataDict": "5253"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 2, "shortName": "LabTropAssay", "valueDataDict": "5253"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 3, "shortName": "LabTropAssay", "valueDataDict": "5253"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "LastName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsHDL6mFMC", "valueDataDict": "53"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsHDL6mFMC_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsLDL", "valueDataDict": "149"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsLDL_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsTC6mFMC", "valueDataDict": "213"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsTC6mFMC_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsTrig", "valueDataDict": "54"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsTrig_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "LocFirstEval", "valueDataDict": "Emergency department (ED)"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "LowHgbNotDrawn", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "LowHgbValue", "valueDataDict": "6.3"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "LowHgbValueDateTime", "valueDataDict": "2023-09-19T05:01:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "LowHgbValue_unit", "valueDataDict": "g/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "MBI", "valueDataDict": "8JM3YV1XE81"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "MedsArrival", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 2, "shortName": "MedsArrival", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "NSAIDAdmin", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "OtherID", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "PCIArrivDC2", "valueDataDict": "No - No reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorHFFF", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcHxOccurrenceArrival", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 2, "shortName": "ProcHxOccurrenceArrival", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcedHxName", "valueDataDict": "Coronary Artery Bypass Graft"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 2, "shortName": "ProcedHxName", "valueDataDict": "Percutaneous Coronary Intervention"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "PtLocTempManagement", "valueDataDict": "ICU/CCU"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "PtType", "valueDataDict": "NSTEMI"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceAmIndian", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Race<PERSON>ian", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceBlack", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceNatHaw", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "RewarmingInitiatedDateTime", "valueDataDict": "2023-09-20T09:06:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "RiskStraNotDocumented", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "SBPFirstMedCon", "valueDataDict": "142"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "SBPFirstMedCon_unit", "valueDataDict": "mm[Hg]"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "SSNNA", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Sex", "valueDataDict": "Male"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "ShockFirstMedCon", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "StemiFirstNotedFMC", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 2, "shortName": "StemiFirstNotedFMC", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 3, "shortName": "StemiFirstNotedFMC", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "SymptomDate24", "valueDataDict": "2023-08-23"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "TargetTempAchievedDateTime", "valueDataDict": "2023-09-20T04:30:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "TimeUnknownSymptomPriorArrival", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "TobaccoUse", "valueDataDict": "Former"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "TotalCholNotDrawn", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "TranOutEDDateTime", "valueDataDict": "2023-08-28T06:02:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "TranOutsideFac", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "TriglycNotDrawn", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "TropCollDateTime", "valueDataDict": "2023-08-27T22:21:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 2, "shortName": "TropCollDateTime", "valueDataDict": "2023-08-27T23:21:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 3, "shortName": "TropCollDateTime", "valueDataDict": "2023-08-28T07:21:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "TropCount", "valueDataDict": "1"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 2, "shortName": "TropCount", "valueDataDict": "2"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 3, "shortName": "TropCount", "valueDataDict": "3"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "TropNotDrawn", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "TropResDateTime", "valueDataDict": "2023-08-27T23:08:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 2, "shortName": "TropResDateTime", "valueDataDict": "2023-08-28T00:07:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 3, "shortName": "TropResDateTime", "valueDataDict": "2023-08-28T08:06:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "TropTestLoc", "valueDataDict": "Lab"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 2, "shortName": "TropTestLoc", "valueDataDict": "Lab"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 3, "shortName": "TropTestLoc", "valueDataDict": "Lab"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "TroponinProtocol", "valueDataDict": "Not documented"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "TroponinValue", "valueDataDict": "2594"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 2, "shortName": "TroponinValue", "valueDataDict": "3501"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 3, "shortName": "TroponinValue", "valueDataDict": "7664"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "TroponinValue_unit", "valueDataDict": "ng/L"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 2, "shortName": "TroponinValue_unit", "valueDataDict": "ng/L"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 3, "shortName": "TroponinValue_unit", "valueDataDict": "ng/L"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "WeightAD", "valueDataDict": "99"}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "WeightAD_unit", "valueDataDict": "kg"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCode", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCodeNA", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "f2653c98-d1e8-477b-afe2-b55a0a492211", "VisitId": 0, "IncrementalId": 1, "shortName": "eCigarette", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "AdmitDateTime", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "AnatomicalImagingResult", "valueDataDict": "Not performed"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "AngioDelayPtReason", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "ArriMedCode", "valueDataDict": "<PERSON><PERSON><PERSON> (Any)"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 2, "shortName": "ArriMedCode", "valueDataDict": "<PERSON><PERSON><PERSON> (Any)"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "ArrivalDateTime", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "CABGDateTime", "valueDataDict": "2023-09-18T08:55:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "CABGFirst2", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "CADtype2", "valueDataDict": "Obstructive"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "CAOutHospital", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "CE_RBC", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "CE_RBCCABG", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "CE_RBCDate", "valueDataDict": "2023-09-18"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "CSHAScaleArrival", "valueDataDict": "5: Mildly frail"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "CTAPerformed", "valueDataDict": "No - No reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "CardiacAngioResults", "valueDataDict": "CAD"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "CathArrivalDateTime", "valueDataDict": "2023-09-14T13:43:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "CathLabAct", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "ChestPainSymp", "valueDataDict": "Prior to arrival"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "ConditionHx", "valueDataDict": "Atrial Fibrillation"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 2, "shortName": "ConditionHx", "valueDataDict": "Atrial Flutter"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 3, "shortName": "ConditionHx", "valueDataDict": "Cancer"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 4, "shortName": "ConditionHx", "valueDataDict": "Dyslipidemia"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 5, "shortName": "ConditionHx", "valueDataDict": "Myocardial Infarction"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 6, "shortName": "ConditionHx", "valueDataDict": "Peripheral Arterial Disease"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "ConditionHxOccurenceArrival", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 2, "shortName": "ConditionHxOccurenceArrival", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 3, "shortName": "ConditionHxOccurenceArrival", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 4, "shortName": "ConditionHxOccurenceArrival", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 5, "shortName": "ConditionHxOccurenceArrival", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 6, "shortName": "ConditionHxOccurenceArrival", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "CreatNotDrawn", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "CreatPeak", "valueDataDict": "1.6"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "CreatPeakDateTime", "valueDataDict": "2023-09-19T02:48:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "CreatPeakNotDrawn", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "CreatPeak_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "DCDateTime", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "DCFName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "DCHospice", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "DCLName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "DCLocation", "valueDataDict": "Skilled nursing facility"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "DCNPI", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "DCStatus", "valueDataDict": "Alive"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_Aspirin100MGplus", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_CardRehab", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_Comfort", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_LVEF", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedAdmin", "valueDataDict": "No - Medical Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON> (Any)"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedID", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedID", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedID", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedID", "valueDataDict": "Beta blocker (Any)"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedID", "valueDataDict": "Angiotensin converting enzyme inhibitor (ACE-I) (Any)"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedID", "valueDataDict": "Angiotensin receptor blocker (ARB) (Any)"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedID", "valueDataDict": "Angiotensin II receptor blocker neprilysin inhibitor (ARNI)"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedID", "valueDataDict": "Aldosterone receptor antagonist (Any)"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedID", "valueDataDict": "Direct oral anticoagulants (DOAC) (Any)"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON> (Any)"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "DCathFName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "DCathLName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "DCathNPI", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "DOB", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "DiabetesBA", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "DiagCorAngio1st", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "DiagCorAngioDateTime", "valueDataDict": "2023-09-14T13:59:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "ECGCounter", "valueDataDict": "1"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 2, "shortName": "ECGCounter", "valueDataDict": "2"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 3, "shortName": "ECGCounter", "valueDataDict": "3"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "ECGDateTime", "valueDataDict": "2023-09-13T14:38:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 2, "shortName": "ECGDateTime", "valueDataDict": "2023-09-13T15:13:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 3, "shortName": "ECGDateTime", "valueDataDict": "2023-09-13T22:40:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 2, "shortName": "ECGReadDateTime", "valueDataDict": "2023-09-13T15:16:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 3, "shortName": "ECGReadDateTime", "valueDataDict": "2023-09-26T16:17:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "EDDisposition", "valueDataDict": "Inpatient"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "EDFName", "valueDataDict": "JESSICA"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "EDLName", "valueDataDict": "WISER"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "EDNPI", "valueDataDict": "**********"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "EMS911CallDateTime", "valueDataDict": "2023-09-13T14:21:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "EMSDispatchDateTime", "valueDataDict": "2023-09-13T14:22:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "EMSFirstMedConReasonforDelay", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "EMSLeavingSceneDateTime", "valueDataDict": "2023-09-13T14:42:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "EMSNPI", "valueDataDict": "**********"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "EMSRunNumber", "valueDataDict": "03677291"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "EMSSTEMIAlert", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "EnrolledStudy", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "EpiEvent", "valueDataDict": "Atrial fibrillation"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 2, "shortName": "EpiEvent", "valueDataDict": "Atrial fibrillation"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 3, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Access site"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 4, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Gastrointestinal"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 5, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Genitourinary"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 6, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Hematoma at access site"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 7, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Other"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 8, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Retroperitoneal"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 9, "shortName": "EpiEvent", "valueDataDict": "Bleeding - Surgical procedure or intervention required"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 10, "shortName": "EpiEvent", "valueDataDict": "Cardiac arrest"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 11, "shortName": "EpiEvent", "valueDataDict": "Cardiogenic shock"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 12, "shortName": "EpiEvent", "valueDataDict": "Heart failure"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 13, "shortName": "EpiEvent", "valueDataDict": "Myocardial infarction"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 14, "shortName": "EpiEvent", "valueDataDict": "New requirement for dialysis"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 15, "shortName": "EpiEvent", "valueDataDict": "Respiratory support - Bi-PAP"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 16, "shortName": "EpiEvent", "valueDataDict": "Respiratory support - High-flow oxygen"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 17, "shortName": "EpiEvent", "valueDataDict": "Respiratory support - Intubation"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 18, "shortName": "EpiEvent", "valueDataDict": "Stroke - Hemorrhagic"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 19, "shortName": "EpiEvent", "valueDataDict": "Stroke - Ischemic"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 20, "shortName": "EpiEvent", "valueDataDict": "Stroke - Undetermined"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 21, "shortName": "EpiEvent", "valueDataDict": "Transient ischemic attack (TIA)"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 22, "shortName": "EpiEvent", "valueDataDict": "Ventricular fibrillation"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 23, "shortName": "EpiEvent", "valueDataDict": "Sustained ventricular tachycardia"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "EpiEventDateTime", "valueDataDict": "2023-09-19T07:00:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 2, "shortName": "EpiEventDateTime", "valueDataDict": "2023-09-20T07:00:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 16, "shortName": "EpiEventDateTime", "valueDataDict": "2023-09-20T12:00:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "EpiEventOccurred", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 2, "shortName": "EpiEventOccurred", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 3, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 4, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 5, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 6, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 7, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 8, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 9, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 10, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 11, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 12, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 13, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 14, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 15, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 16, "shortName": "EpiEventOccurred", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 17, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 18, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 19, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 20, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 21, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 22, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 23, "shortName": "EpiEventOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "FMCCreat", "valueDataDict": "1.5"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "FMCCreat_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "FirstFacMeansTrans", "valueDataDict": "EMS - Ambulance"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "FirstMedConDateTime", "valueDataDict": "2023-09-13T14:30:00"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "FirstName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "FuncTestResult", "valueDataDict": "Not performed"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "HDLNotDrawn", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "HFFirstMedCon", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "HIPS", "valueDataDict": "Private health insurance"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "HRFirstMedCon", "valueDataDict": "93"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "HRFirstMedCon_unit", "valueDataDict": "bpm"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "HbA1cNotDrawn", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "HbA1cValue", "valueDataDict": "6.2"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "HbA1cValue_unit", "valueDataDict": "%"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "HealthIns", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "HeightAD", "valueDataDict": "162.6"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "HeightAD_unit", "valueDataDict": "cm"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "HgbNotDrawn", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "HomeMedPrescrib", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 2, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 3, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 4, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 5, "shortName": "HomeMedPrescrib", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 6, "shortName": "HomeMedPrescrib", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "HomeMeds", "valueDataDict": "ACE Inhibitors"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 2, "shortName": "HomeMeds", "valueDataDict": "ARB (Angiotensin Receptor Blockers)"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 3, "shortName": "HomeMeds", "valueDataDict": "ARNI"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 4, "shortName": "HomeMeds", "valueDataDict": "Beta Blocker"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 5, "shortName": "HomeMeds", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 6, "shortName": "HomeMeds", "valueDataDict": "ACE Inhibitors"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "HospClinicTrial", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "HxCVD", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "HxTIA", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "Hypertension", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "INRNotDrawn", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "InitHgbValue", "valueDataDict": "11.9"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "InitHgbValue_unit", "valueDataDict": "g/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "InitINRValue", "valueDataDict": "1.6"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "IschemiaEval", "valueDataDict": "No - No reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "LDLNotDrawn", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "LVEFPercent", "valueDataDict": "30"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "LVEFPercent_unit", "valueDataDict": "%"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "LabTropAssay", "valueDataDict": "5252"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 2, "shortName": "LabTropAssay", "valueDataDict": "5252"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 3, "shortName": "LabTropAssay", "valueDataDict": "5252"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "LastName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsHDL6mFMC", "valueDataDict": "40"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsHDL6mFMC_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsLDL", "valueDataDict": "65"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsLDL_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsTC6mFMC", "valueDataDict": "143"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsTC6mFMC_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsTrig", "valueDataDict": "188"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "LipidsTrig_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "LocFirstEval", "valueDataDict": "Emergency department (ED)"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "LowHgbNotDrawn", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "LowHgbValue", "valueDataDict": "7.6"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "LowHgbValueDateTime", "valueDataDict": "2023-09-18T15:26:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "LowHgbValue_unit", "valueDataDict": "g/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "MedsArrival", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 2, "shortName": "MedsArrival", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "NSAIDAdmin", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "OtherID", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "PCIArrivDC2", "valueDataDict": "No - No reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorHFFF", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 2, "shortName": "ProcHistDateArrival", "valueDataDict": "2014-01-01"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcHxOccurrenceArrival", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 2, "shortName": "ProcHxOccurrenceArrival", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcedHxName", "valueDataDict": "Coronary Artery Bypass Graft"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 2, "shortName": "ProcedHxName", "valueDataDict": "Percutaneous Coronary Intervention"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "PtType", "valueDataDict": "NSTEMI"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceAmIndian", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Race<PERSON>ian", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceBlack", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceNatHaw", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "RiskAssessmentTool", "valueDataDict": "HEART risk score"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "RiskAssessmentToolNotDocumented", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "RiskStraNotDocumented", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "RiskStratPerfTransferFacility", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "RiskStratification", "valueDataDict": "High"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "SBPFirstMedCon", "valueDataDict": "174"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "SBPFirstMedCon_unit", "valueDataDict": "mm[Hg]"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "SSNNA", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Sex", "valueDataDict": "Female"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "ShockFirstMedCon", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "StemiFirstNotedFMC", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 2, "shortName": "StemiFirstNotedFMC", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 3, "shortName": "StemiFirstNotedFMC", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "StrokeBA", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "SymptomDate24", "valueDataDict": "2023-09-13"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "SymptomTime24", "valueDataDict": "14:04:00.0000000-05:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "TimeUnknownSymptomPriorArrival", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "TobaccoUse", "valueDataDict": "Current"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "TotalCholNotDrawn", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "TranOutEDDateTime", "valueDataDict": "2023-09-13T19:58:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "TranOutsideFac", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "TriglycNotDrawn", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "TropCollDateTime", "valueDataDict": "2023-09-13T15:45:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 2, "shortName": "TropCollDateTime", "valueDataDict": "2023-09-13T16:28:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 3, "shortName": "TropCollDateTime", "valueDataDict": "2023-09-13T18:40:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "TropCount", "valueDataDict": "1"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 2, "shortName": "TropCount", "valueDataDict": "2"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 3, "shortName": "TropCount", "valueDataDict": "3"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "TropNotDrawn", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "TropResDateTime", "valueDataDict": "2023-09-13T17:17:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 2, "shortName": "TropResDateTime", "valueDataDict": "2023-09-13T17:17:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 3, "shortName": "TropResDateTime", "valueDataDict": "2023-09-13T19:35:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "TropTestLoc", "valueDataDict": "Lab"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 2, "shortName": "TropTestLoc", "valueDataDict": "Lab"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 3, "shortName": "TropTestLoc", "valueDataDict": "Lab"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "TroponinProtocol", "valueDataDict": "0-1 hour"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "TroponinValue", "valueDataDict": "340"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 2, "shortName": "TroponinValue", "valueDataDict": "845"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 3, "shortName": "TroponinValue", "valueDataDict": "3616"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "TroponinValue_unit", "valueDataDict": "ng/L"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 2, "shortName": "TroponinValue_unit", "valueDataDict": "ng/L"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 3, "shortName": "TroponinValue_unit", "valueDataDict": "ng/L"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "WeightAD", "valueDataDict": "82.6"}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "WeightAD_unit", "valueDataDict": "kg"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCode", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCodeNA", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "b8f18d64-b1cb-4925-9a93-3f7871fc2805", "VisitId": 0, "IncrementalId": 1, "shortName": "eCigarette", "valueDataDict": "No"}]