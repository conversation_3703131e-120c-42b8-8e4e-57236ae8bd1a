[{"Container Class": "patientContainer", "Parent Section": "Root", "Section Code": "DEMOGRAPHICS", "Section Type": "Section", "Cardinality": "1..1", "Table": "DEMOGRAPHICS", "Name Elements": "Last Name", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate the patient's last name", "Target Value": "The value on arrival at this facility", "Short Name": "LastName", "Data Type": "LN", "Precision": "50", "Unit Of Measure Elements": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "DDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code Elements": "**********", "Code System Elements": "2.16.840.1.113883.3.3478.6.1", "Code System Name Elements": "ACC NCDR", "Vendor Instruction": null, "Name Selections": null, "Code Selections": null, "Code System Selections": null, "Code System Name Selections": null, "Selection Name": null, "Selection Definition": null, "Display Order": null, "Title": null, "Definition": null, "Source": null, "Unit Of Measure Ranges": null, "Usual Range Min": null, "Usual Range Max": null, "Valid Range Min": null, "Valid Range Max": null}, {"Container Class": "patientContainer", "Parent Section": "Root", "Section Code": "DEMOGRAPHICS", "Section Type": "Section", "Cardinality": "1..1", "Table": "DEMOGRAPHICS", "Name Elements": "First Name", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate the patient's first name.", "Target Value": "The value on arrival at this facility", "Short Name": "FirstName", "Data Type": "FN", "Precision": "50", "Unit Of Measure Elements": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "DDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code Elements": "**********", "Code System Elements": "2.16.840.1.113883.3.3478.6.1", "Code System Name Elements": "ACC NCDR", "Vendor Instruction": null, "Name Selections": null, "Code Selections": null, "Code System Selections": null, "Code System Name Selections": null, "Selection Name": null, "Selection Definition": null, "Display Order": null, "Title": null, "Definition": null, "Source": null, "Unit Of Measure Ranges": null, "Usual Range Min": null, "Usual Range Max": null, "Valid Range Min": null, "Valid Range Max": null}, {"Container Class": "patientContainer", "Parent Section": "Root", "Section Code": "DEMOGRAPHICS", "Section Type": "Section", "Cardinality": "1..1", "Table": "DEMOGRAPHICS", "Name Elements": "Middle Name", "Section Display Name": "<PERSON><PERSON>", "Coding Instructions": "Indicate the patient's middle name", "Target Value": "The value on arrival at this facility", "Short Name": "MidName", "Data Type": "MN", "Precision": "50", "Unit Of Measure Elements": null, "Selection Type": "Single", "Default Value": "<PERSON><PERSON>", "Is Dynamic List": "No", "Missing Data": "Report", "Is Harvested": "Yes", "Dataset": "DDS", "Data Source": "User", "Is Identifier": "No", "Is Base Element": "Yes", "Is Followup Element": "Yes", "Code Elements": "**********", "Code System Elements": "2.16.840.1.113883.3.3478.6.1", "Code System Name Elements": "ACC NCDR", "Vendor Instruction": null, "Name Selections": null, "Code Selections": null, "Code System Selections": null, "Code System Name Selections": null, "Selection Name": null, "Selection Definition": null, "Display Order": null, "Title": null, "Definition": null, "Source": null, "Unit Of Measure Ranges": null, "Usual Range Min": null, "Usual Range Max": null, "Valid Range Min": null, "Valid Range Max": null}]