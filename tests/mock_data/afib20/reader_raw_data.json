[{"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTBase", "valueDataDict": "true"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS1Q1", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS1Q2", "valueDataDict": "Within the past week"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q1", "valueDataDict": "Moderately bothered"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q10", "valueDataDict": "Quite a bit of difficulty"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q11", "valueDataDict": "Quite a bit of difficulty"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q12", "valueDataDict": "Quite a bit of difficulty"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q13", "valueDataDict": "A little bothered"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q14", "valueDataDict": "A little bothered"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q15", "valueDataDict": "Hardly bothered "}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q16", "valueDataDict": "A little bothered"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q17", "valueDataDict": "Moderately bothered"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q18", "valueDataDict": "Moderately bothered"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q19", "valueDataDict": "Somewhat dissatisfied"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q2", "valueDataDict": "Moderately bothered"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q20", "valueDataDict": "Somewhat dissatisfied"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q5", "valueDataDict": "Moderately limited"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q6", "valueDataDict": "A little limited"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q7", "valueDataDict": "Moderate difficulty"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q8", "valueDataDict": "Quite a bit of difficulty"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q9", "valueDataDict": "Moderate difficulty"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "AFObserved", "valueDataDict": "No"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFibClass", "valueDataDict": "Paroxysmal"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFlutterType", "valueDataDict": "No"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ATObserved", "valueDataDict": "No"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "AblLesion", "valueDataDict": "true"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 10, "shortName": "AblLesionEnergy", "valueDataDict": "Pulsed Field Ablation"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "AblLesionLocSingSel", "valueDataDict": "SVC isolation"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "AblLesionLocSingSel", "valueDataDict": "Coronary sinus isolation"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 3, "shortName": "AblLesionLocSingSel", "valueDataDict": "Cavotricuspid isthmus (CTI)"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 4, "shortName": "AblLesionLocSingSel", "valueDataDict": "Ligament/vein of marshall"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 5, "shortName": "AblLesionLocSingSel", "valueDataDict": "LA roof line"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 6, "shortName": "AblLesionLocSingSel", "valueDataDict": "Left auricular appendage"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 7, "shortName": "AblLesionLocSingSel", "valueDataDict": "LA floor line"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 8, "shortName": "AblLesionLocSingSel", "valueDataDict": "Mitral isthmus line"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 9, "shortName": "AblLesionLocSingSel", "valueDataDict": "Posterior wall isolation"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 10, "shortName": "AblLesionLocSingSel", "valueDataDict": "Other"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "AblLesionOcc", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "AblLesionOcc", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 3, "shortName": "AblLesionOcc", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 4, "shortName": "AblLesionOcc", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 5, "shortName": "AblLesionOcc", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 6, "shortName": "AblLesionOcc", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 7, "shortName": "AblLesionOcc", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 8, "shortName": "AblLesionOcc", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 9, "shortName": "AblLesionOcc", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 10, "shortName": "AblLesionOcc", "valueDataDict": "true"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "AddAbl", "valueDataDict": "true"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "AddAblEnergy", "valueDataDict": "Pulsed Field Ablation"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 6, "shortName": "AddAblEnergy", "valueDataDict": "Pulsed Field Ablation"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "AddAblOcc", "valueDataDict": "true"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "AddAblOcc", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 3, "shortName": "AddAblOcc", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 4, "shortName": "AddAblOcc", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 5, "shortName": "AddAblOcc", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 6, "shortName": "AddAblOcc", "valueDataDict": "true"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "AddAblTech", "valueDataDict": "Complex fractionated electrogram"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "AddAblTech", "valueDataDict": "Focal/trigger ablation"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 3, "shortName": "AddAblTech", "valueDataDict": "Ganglion plexus ablation"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 4, "shortName": "AddAblTech", "valueDataDict": "Rotor-based mapping"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 5, "shortName": "AddAblTech", "valueDataDict": "Temporo-spatial dispersion mapping/ablation"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 6, "shortName": "AddAblTech", "valueDataDict": "Other"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "Anesthesia", "valueDataDict": "General Anesthesia"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ArrivalDateTime", "valueDataDict": "2024-10-21T10:40:00"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AtrialRhythm", "valueDataDict": "Sinus|Atrial paced"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "BaselineImagingPerf", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "CVandType", "valueDataDict": "No"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "CathAblationUDI", "valueDataDict": "M004PF41M401"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "CathManipulation", "valueDataDict": "Manual"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadCHF", "valueDataDict": "true"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadDM", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadHypertCont", "valueDataDict": "true"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadLVDysf", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadStroke", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadTE", "valueDataDict": "true"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadTIA", "valueDataDict": "true"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadVascDis", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ConditionHx", "valueDataDict": "Symptoms During Afib/Aflutter"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "ConditionHx", "valueDataDict": "Cardiomyopathy"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "ConditionHx", "valueDataDict": "Chronic Lung Disease"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "ConditionHx", "valueDataDict": "Coronary Artery Disease"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "ConditionHx", "valueDataDict": "Sleep Apnea"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "ConditionHx", "valueDataDict": "Valvular Atrial Fibrillation"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ConditionHxOccurenceArrival", "valueDataDict": "true"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "ConditionHxOccurenceArrival", "valueDataDict": "true"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "ConditionHxOccurenceArrival", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "ConditionHxOccurenceArrival", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "ConditionHxOccurenceArrival", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "ConditionHxOccurenceArrival", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DCAtrialRhythm", "valueDataDict": "Sinus|Atrial paced"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DCDateTime", "valueDataDict": "2024-10-21T18:14:00"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DCStatus", "valueDataDict": "Alive"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 13, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 14, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 15, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 16, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 17, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 18, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 19, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 20, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 21, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 22, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 23, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 24, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 25, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 26, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 27, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 28, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 29, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 30, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 31, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 32, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 33, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 34, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 35, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Digoxin"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedID", "valueDataDict": "Diltiazem"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedID", "valueDataDict": "Verapamil"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedID", "valueDataDict": "Amiodarone"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedID", "valueDataDict": "Disopyramide"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedID", "valueDataDict": "Dofetilide"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedID", "valueDataDict": "Dr<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedID", "valueDataDict": "Flecainide"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedID", "valueDataDict": "Procainamide"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedID", "valueDataDict": "Propafenone"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedID", "valueDataDict": "Quinidine"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedID", "valueDataDict": "Sotalol"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 13, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 14, "shortName": "DC_MedID", "valueDataDict": "Aspirin, Extended-Release Dipyridamole"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 15, "shortName": "DC_MedID", "valueDataDict": "Vorapaxar"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 16, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 17, "shortName": "DC_MedID", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 18, "shortName": "DC_MedID", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 19, "shortName": "DC_MedID", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 20, "shortName": "DC_MedID", "valueDataDict": "Ticlopidine"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 21, "shortName": "DC_MedID", "valueDataDict": "Betrixaban"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 22, "shortName": "DC_MedID", "valueDataDict": "Heparin Derivative"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 23, "shortName": "DC_MedID", "valueDataDict": "Low Molecular Weight Heparin"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 24, "shortName": "DC_MedID", "valueDataDict": "Unfractionated Heparin"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 25, "shortName": "DC_MedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 26, "shortName": "DC_MedID", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 27, "shortName": "DC_MedID", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 28, "shortName": "DC_MedID", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 29, "shortName": "DC_MedID", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 30, "shortName": "DC_MedID", "valueDataDict": "Angiotensin converting enzyme inhibitor (ACE-I) (Any)"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 31, "shortName": "DC_MedID", "valueDataDict": "Angiotensin II receptor blocker neprilysin inhibitor (ARNI)"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 32, "shortName": "DC_MedID", "valueDataDict": "Angiotensin receptor blocker (ARB) (Any)"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 33, "shortName": "DC_MedID", "valueDataDict": "Beta blocker (Any)"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 34, "shortName": "DC_MedID", "valueDataDict": "GLP-1 agonist"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 35, "shortName": "DC_MedID", "valueDataDict": "SGLT inhibitor"}, {"NCDRPatientId": "9096", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "DOB", "valueDataDict": "1900-01-01"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "DevID", "valueDataDict": "5433"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DiastolicBP", "valueDataDict": "78"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DiastolicBP_unit", "valueDataDict": "mm[Hg]"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "EchocardiogramResults", "valueDataDict": "LV hypertrophy - none|RA Not enlarged|LA Not enlarged"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "EnrolledStudy", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "FirstName", "valueDataDict": "<PERSON>"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "FluoroDoseDAP2", "valueDataDict": "6"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "FluoroDoseDAP2_unit", "valueDataDict": "Gy·cm²"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "FluoroDoseKerm", "valueDataDict": "64"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "FluoroDoseKerm_unit", "valueDataDict": "mGy"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "FluoroTime", "valueDataDict": "5"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "FluoroTime_unit", "valueDataDict": "min"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HGB", "valueDataDict": "15.5"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HGBND", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HGB_unit", "valueDataDict": "g/dL"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HIPS", "valueDataDict": "Private health insurance"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HealthIns", "valueDataDict": "true"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Height", "valueDataDict": "162.56"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Height_unit", "valueDataDict": "cm"}, {"NCDRPatientId": "9096", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "INRND", "valueDataDict": "true"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "IntraProcAnticoag", "valueDataDict": "true"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "LVEF", "valueDataDict": "40"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "LVEFAssessed", "valueDataDict": "true"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "LVEF_unit", "valueDataDict": "%"}, {"NCDRPatientId": "9096", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "LastName", "valueDataDict": "<PERSON><PERSON>"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "MappingDevID", "valueDataDict": "5426"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "MedAdmin", "valueDataDict": "Current"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 10, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 11, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 12, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 13, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 14, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 15, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 16, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 17, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 18, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 19, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 20, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 21, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 22, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 23, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 24, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 25, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 26, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 27, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 28, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 29, "shortName": "MedAdmin", "valueDataDict": "Current"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 30, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 31, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 32, "shortName": "MedAdmin", "valueDataDict": "Current"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 33, "shortName": "MedAdmin", "valueDataDict": "Current"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 34, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 35, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "MedID", "valueDataDict": "Digoxin"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "MedID", "valueDataDict": "Diltiazem"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "MedID", "valueDataDict": "Verapamil"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "MedID", "valueDataDict": "Amiodarone"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "MedID", "valueDataDict": "Disopyramide"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "MedID", "valueDataDict": "Dofetilide"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "MedID", "valueDataDict": "Dr<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "MedID", "valueDataDict": "Flecainide"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "MedID", "valueDataDict": "Procainamide"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 10, "shortName": "MedID", "valueDataDict": "Propafenone"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 11, "shortName": "MedID", "valueDataDict": "Quinidine"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 12, "shortName": "MedID", "valueDataDict": "Sotalol"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 13, "shortName": "MedID", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 14, "shortName": "MedID", "valueDataDict": "Aspirin, Extended-Release Dipyridamole"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 15, "shortName": "MedID", "valueDataDict": "Vorapaxar"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 16, "shortName": "MedID", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 17, "shortName": "MedID", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 18, "shortName": "MedID", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 19, "shortName": "MedID", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 20, "shortName": "MedID", "valueDataDict": "Ticlopidine"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 21, "shortName": "MedID", "valueDataDict": "Betrixaban"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 22, "shortName": "MedID", "valueDataDict": "Heparin Derivative"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 23, "shortName": "MedID", "valueDataDict": "Low Molecular Weight Heparin"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 24, "shortName": "MedID", "valueDataDict": "Unfractionated Heparin"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 25, "shortName": "MedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 26, "shortName": "MedID", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 27, "shortName": "MedID", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 28, "shortName": "MedID", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 29, "shortName": "MedID", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 30, "shortName": "MedID", "valueDataDict": "Angiotensin converting enzyme inhibitor (ACE-I) (Any)"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 31, "shortName": "MedID", "valueDataDict": "Angiotensin II receptor blocker neprilysin inhibitor (ARNI)"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 32, "shortName": "MedID", "valueDataDict": "Angiotensin receptor blocker (ARB) (Any)"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 33, "shortName": "MedID", "valueDataDict": "Beta blocker (Any)"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 34, "shortName": "MedID", "valueDataDict": "GLP-1 agonist"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 35, "shortName": "MedID", "valueDataDict": "SGLT inhibitor"}, {"NCDRPatientId": "9096", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "MidName", "valueDataDict": "F"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "MitralRegurg", "valueDataDict": "Trace/Trivial"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "MitralStenosis", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "NoFluoroUsed", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "NoRadiationKerm", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "OperA_FirstName", "valueDataDict": "<PERSON><PERSON>"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "OperA_LastName", "valueDataDict": "<PERSON>"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "OperA_MidName", "valueDataDict": "<PERSON><PERSON>"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "OperA_NPI", "valueDataDict": "**********"}, {"NCDRPatientId": "9096", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "OtherID", "valueDataDict": "32153214"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "PVI", "valueDataDict": "true"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "PVIEnergySource", "valueDataDict": "Pulsed Field Ablation"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "PhrenicNerveEval", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcEvent", "valueDataDict": "A-V fistula requiring intervention"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PostProcEvent", "valueDataDict": "A-V fistula requiring intervention"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "PostProcEvent", "valueDataDict": "Acute kidney injury"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 3, "shortName": "PostProcEvent", "valueDataDict": "Bleeding - access site (transfusion)"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 4, "shortName": "PostProcEvent", "valueDataDict": "Bradycardia adverse events"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 5, "shortName": "PostProcEvent", "valueDataDict": "Cardiac arrest"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 6, "shortName": "PostProcEvent", "valueDataDict": "Cardiac surgery (unplanned emergent)"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 7, "shortName": "PostProcEvent", "valueDataDict": "Deep vein thrombosis"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 8, "shortName": "PostProcEvent", "valueDataDict": "GU Bleeding"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 9, "shortName": "PostProcEvent", "valueDataDict": "Heart failure"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 10, "shortName": "PostProcEvent", "valueDataDict": "Hematoma at access site"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 11, "shortName": "PostProcEvent", "valueDataDict": "Hemolysis"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 12, "shortName": "PostProcEvent", "valueDataDict": "Hemorrhage (non access site)"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 13, "shortName": "PostProcEvent", "valueDataDict": "Hemothorax"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 14, "shortName": "PostProcEvent", "valueDataDict": "Myocardial infarction"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 15, "shortName": "PostProcEvent", "valueDataDict": "Pericardial effusion requiring intervention"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 16, "shortName": "PostProcEvent", "valueDataDict": "Pericardial effusion resulting in cardiac tamponade"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 17, "shortName": "PostProcEvent", "valueDataDict": "Phrenic nerve damage"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 18, "shortName": "PostProcEvent", "valueDataDict": "Pleural effusion"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 19, "shortName": "PostProcEvent", "valueDataDict": "Pneumonia"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 20, "shortName": "PostProcEvent", "valueDataDict": "Pneumothorax"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 21, "shortName": "PostProcEvent", "valueDataDict": "Pseudoaneurysm requiring intervention"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 22, "shortName": "PostProcEvent", "valueDataDict": "Pulmonary embolism"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 23, "shortName": "PostProcEvent", "valueDataDict": "Pulmonary vein damage/dissection"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 24, "shortName": "PostProcEvent", "valueDataDict": "Respiratory failure"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 25, "shortName": "PostProcEvent", "valueDataDict": "<PERSON><PERSON>"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 26, "shortName": "PostProcEvent", "valueDataDict": "Stroke"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 27, "shortName": "PostProcEvent", "valueDataDict": "Transient ischemic attack (TIA)"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 28, "shortName": "PostProcEvent", "valueDataDict": "Vascular injury requiring surgical intervention"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PostProcHgbND2", "valueDataDict": "true"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 3, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 4, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 5, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 6, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 7, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 8, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 9, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 10, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 11, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 12, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 13, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 14, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 15, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 16, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 17, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 18, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 19, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 20, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 21, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 22, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 23, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 24, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 25, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 26, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 27, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 28, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PreProcBNPNotDrawn", "valueDataDict": "true"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PreProcCreat", "valueDataDict": ".98"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PreProcCreatND", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PreProcCreat_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "PreProcICEPerf", "valueDataDict": "Yes - 2D"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PreProcNTBNPNotDrawn", "valueDataDict": "true"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorCMType", "valueDataDict": "Non-ischemic"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcHxOccur", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "ProcHxOccur", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "ProcHxOccur", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "ProcHxOccur", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcStatus", "valueDataDict": "Outpatient"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcedHxName", "valueDataDict": "AV Node ablation with Pacemaker Implantation"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "ProcedHxName", "valueDataDict": "Left Atrial Appendage Occlusion"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "ProcedHxName", "valueDataDict": "Atrial Fibrillation Termination Attempt"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "ProcedHxName", "valueDataDict": "Atrial Flutter Termination Attempt"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcedureEndDateTime", "valueDataDict": "2024-10-21T15:52:00"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcedureStartDateTime", "valueDataDict": "2024-10-21T13:34:00"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Pulse", "valueDataDict": "72"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Pulse_unit", "valueDataDict": "bpm"}, {"NCDRPatientId": "9096", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceAmIndian", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Race<PERSON>ian", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceBlack", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceNatHaw", "valueDataDict": "false"}, {"NCDRPatientId": "9096", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "true"}, {"NCDRPatientId": "9096", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "SSNNA", "valueDataDict": "true"}, {"NCDRPatientId": "9096", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Sex", "valueDataDict": "Male"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "SxExperienced", "valueDataDict": "Anxiety|Fatigue|Irregular heartbeat|Palpitations"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "SystolicBP", "valueDataDict": "142"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "SystolicBP_unit", "valueDataDict": "mm[Hg]"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "TTEDate", "valueDataDict": "2024-10-16"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "TTEPerf", "valueDataDict": "true"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "TransseptCath", "valueDataDict": "Single"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "UnintAnticoagTx", "valueDataDict": "true"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Weight", "valueDataDict": "65.09"}, {"NCDRPatientId": "9096", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Weight_unit", "valueDataDict": "kg"}, {"NCDRPatientId": "9096", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCode", "valueDataDict": "94111"}, {"NCDRPatientId": "9096", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCodeNA", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTBase", "valueDataDict": "true"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS1Q1", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS1Q2", "valueDataDict": "Within the past month"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q1", "valueDataDict": "Hardly bothered "}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q10", "valueDataDict": "A little difficulty"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q11", "valueDataDict": "Quite a bit of difficulty"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q12", "valueDataDict": "Moderate difficulty"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q13", "valueDataDict": "Hardly bothered "}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q14", "valueDataDict": "Hardly bothered "}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q15", "valueDataDict": "Moderately bothered"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q16", "valueDataDict": "Hardly bothered "}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q17", "valueDataDict": "Moderately bothered"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q18", "valueDataDict": "Hardly bothered "}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q19", "valueDataDict": "Very satisfied"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q2", "valueDataDict": "A little bothered"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q20", "valueDataDict": "Very satisfied"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q3", "valueDataDict": "Not at all bothered or I did not have this symptom"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q4", "valueDataDict": "Hardly bothered "}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q5", "valueDataDict": "Moderately limited"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q6", "valueDataDict": "Not at all limited"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q7", "valueDataDict": "Moderate difficulty"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q8", "valueDataDict": "Moderate difficulty"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFEQTS2Q9", "valueDataDict": "A little difficulty"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "AFObserved", "valueDataDict": "Yes - Ablated"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFibClass", "valueDataDict": "Persistent"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AFlutterType", "valueDataDict": "No"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ATObserved", "valueDataDict": "No"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "AblLesion", "valueDataDict": "true"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 3, "shortName": "AblLesionEnergy", "valueDataDict": "Radiofrequency"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 5, "shortName": "AblLesionEnergy", "valueDataDict": "Radiofrequency"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 9, "shortName": "AblLesionEnergy", "valueDataDict": "Radiofrequency"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "AblLesionLocSingSel", "valueDataDict": "SVC isolation"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "AblLesionLocSingSel", "valueDataDict": "Coronary sinus isolation"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 3, "shortName": "AblLesionLocSingSel", "valueDataDict": "Cavotricuspid isthmus (CTI)"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 4, "shortName": "AblLesionLocSingSel", "valueDataDict": "Ligament/vein of marshall"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 5, "shortName": "AblLesionLocSingSel", "valueDataDict": "LA roof line"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 6, "shortName": "AblLesionLocSingSel", "valueDataDict": "Left auricular appendage"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 7, "shortName": "AblLesionLocSingSel", "valueDataDict": "LA floor line"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 8, "shortName": "AblLesionLocSingSel", "valueDataDict": "Mitral isthmus line"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 9, "shortName": "AblLesionLocSingSel", "valueDataDict": "Posterior wall isolation"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 10, "shortName": "AblLesionLocSingSel", "valueDataDict": "Other"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "AblLesionOcc", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "AblLesionOcc", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 3, "shortName": "AblLesionOcc", "valueDataDict": "true"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 4, "shortName": "AblLesionOcc", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 5, "shortName": "AblLesionOcc", "valueDataDict": "true"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 6, "shortName": "AblLesionOcc", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 7, "shortName": "AblLesionOcc", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 8, "shortName": "AblLesionOcc", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 9, "shortName": "AblLesionOcc", "valueDataDict": "true"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 10, "shortName": "AblLesionOcc", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "AddAbl", "valueDataDict": "true"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "AddAblEnergy", "valueDataDict": "Radiofrequency"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "AddAblOcc", "valueDataDict": "true"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "AddAblOcc", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 3, "shortName": "AddAblOcc", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 4, "shortName": "AddAblOcc", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 5, "shortName": "AddAblOcc", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 6, "shortName": "AddAblOcc", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "AddAblTech", "valueDataDict": "Complex fractionated electrogram"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "AddAblTech", "valueDataDict": "Focal/trigger ablation"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 3, "shortName": "AddAblTech", "valueDataDict": "Ganglion plexus ablation"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 4, "shortName": "AddAblTech", "valueDataDict": "Rotor-based mapping"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 5, "shortName": "AddAblTech", "valueDataDict": "Temporo-spatial dispersion mapping/ablation"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 6, "shortName": "AddAblTech", "valueDataDict": "Other"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "Anesthesia", "valueDataDict": "General Anesthesia"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ArrivalDateTime", "valueDataDict": "2024-11-14T06:18:00"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "AtrialRhythm", "valueDataDict": "Sinus"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "BaselineImagingPerf", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "CVandType", "valueDataDict": "Yes - DC"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "CathAblationUDI", "valueDataDict": "A-TFSE-FJ"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "CathManipulation", "valueDataDict": "Manual"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadCHF", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadDM", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadHypertCont", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadLVDysf", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadStroke", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadTE", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadTIA", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ChadVascDis", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ConditionHx", "valueDataDict": "Symptoms During Afib/Aflutter"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "ConditionHx", "valueDataDict": "Cardiomyopathy"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "ConditionHx", "valueDataDict": "Chronic Lung Disease"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "ConditionHx", "valueDataDict": "Coronary Artery Disease"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "ConditionHx", "valueDataDict": "Sleep Apnea"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "ConditionHx", "valueDataDict": "Valvular Atrial Fibrillation"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ConditionHxOccurenceArrival", "valueDataDict": "true"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "ConditionHxOccurenceArrival", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "ConditionHxOccurenceArrival", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "ConditionHxOccurenceArrival", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "ConditionHxOccurenceArrival", "valueDataDict": "true"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "ConditionHxOccurenceArrival", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DCAtrialRhythm", "valueDataDict": "Sinus"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DCDateTime", "valueDataDict": "2024-11-15T09:48:00"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DCStatus", "valueDataDict": "Alive"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - Medical Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 13, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 14, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 15, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 16, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 17, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 18, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 19, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 20, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 21, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 22, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 23, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 24, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 25, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 26, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 27, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 28, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 29, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 30, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 31, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 32, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 33, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 34, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 35, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Digoxin"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedID", "valueDataDict": "Diltiazem"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedID", "valueDataDict": "Verapamil"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedID", "valueDataDict": "Amiodarone"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedID", "valueDataDict": "Disopyramide"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedID", "valueDataDict": "Dofetilide"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedID", "valueDataDict": "Dr<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedID", "valueDataDict": "Flecainide"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedID", "valueDataDict": "Procainamide"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedID", "valueDataDict": "Propafenone"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedID", "valueDataDict": "Quinidine"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedID", "valueDataDict": "Sotalol"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 13, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 14, "shortName": "DC_MedID", "valueDataDict": "Aspirin, Extended-Release Dipyridamole"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 15, "shortName": "DC_MedID", "valueDataDict": "Vorapaxar"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 16, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 17, "shortName": "DC_MedID", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 18, "shortName": "DC_MedID", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 19, "shortName": "DC_MedID", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 20, "shortName": "DC_MedID", "valueDataDict": "Ticlopidine"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 21, "shortName": "DC_MedID", "valueDataDict": "Betrixaban"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 22, "shortName": "DC_MedID", "valueDataDict": "Heparin Derivative"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 23, "shortName": "DC_MedID", "valueDataDict": "Low Molecular Weight Heparin"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 24, "shortName": "DC_MedID", "valueDataDict": "Unfractionated Heparin"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 25, "shortName": "DC_MedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 26, "shortName": "DC_MedID", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 27, "shortName": "DC_MedID", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 28, "shortName": "DC_MedID", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 29, "shortName": "DC_MedID", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 30, "shortName": "DC_MedID", "valueDataDict": "Angiotensin converting enzyme inhibitor (ACE-I) (Any)"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 31, "shortName": "DC_MedID", "valueDataDict": "Angiotensin II receptor blocker neprilysin inhibitor (ARNI)"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 32, "shortName": "DC_MedID", "valueDataDict": "Angiotensin receptor blocker (ARB) (Any)"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 33, "shortName": "DC_MedID", "valueDataDict": "Beta blocker (Any)"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 34, "shortName": "DC_MedID", "valueDataDict": "GLP-1 agonist"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 35, "shortName": "DC_MedID", "valueDataDict": "SGLT inhibitor"}, {"NCDRPatientId": "9136", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "DOB", "valueDataDict": "1900-01-01"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "DevID", "valueDataDict": "5398"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DiastolicBP", "valueDataDict": "71"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "DiastolicBP_unit", "valueDataDict": "mm[Hg]"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "EnrolledStudy", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "FIT_FirstName", "valueDataDict": "<PERSON><PERSON>"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "FIT_LastName", "valueDataDict": "Oommen"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "FIT_MidName", "valueDataDict": "<PERSON>"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "FIT_NPI", "valueDataDict": "**********"}, {"NCDRPatientId": "9136", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "FirstName", "valueDataDict": "<PERSON>"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HGB", "valueDataDict": "12.4"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HGBND", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HGB_unit", "valueDataDict": "g/dL"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HIPS", "valueDataDict": "Private health insurance|Medicare (Part A or B)"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "HealthIns", "valueDataDict": "true"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Height", "valueDataDict": "170.18"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Height_unit", "valueDataDict": "cm"}, {"NCDRPatientId": "9136", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "INRND", "valueDataDict": "true"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "IntraProcAnticoag", "valueDataDict": "true"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "LVEF", "valueDataDict": "60"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "LVEFAssessed", "valueDataDict": "true"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "LVEF_unit", "valueDataDict": "%"}, {"NCDRPatientId": "9136", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "LastName", "valueDataDict": "<PERSON><PERSON>"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "MBI", "valueDataDict": "6CJ0NG3XW23"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "MappingDevID", "valueDataDict": "5426"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "MedAdmin", "valueDataDict": "Held"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 10, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 11, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 12, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 13, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 14, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 15, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 16, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 17, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 18, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 19, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 20, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 21, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 22, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 23, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 24, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 25, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 26, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 27, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 28, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 29, "shortName": "MedAdmin", "valueDataDict": "Current"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 30, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 31, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 32, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 33, "shortName": "MedAdmin", "valueDataDict": "Current"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 34, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 35, "shortName": "MedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "MedID", "valueDataDict": "Digoxin"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "MedID", "valueDataDict": "Diltiazem"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "MedID", "valueDataDict": "Verapamil"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "MedID", "valueDataDict": "Amiodarone"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 5, "shortName": "MedID", "valueDataDict": "Disopyramide"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 6, "shortName": "MedID", "valueDataDict": "Dofetilide"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 7, "shortName": "MedID", "valueDataDict": "Dr<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 8, "shortName": "MedID", "valueDataDict": "Flecainide"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 9, "shortName": "MedID", "valueDataDict": "Procainamide"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 10, "shortName": "MedID", "valueDataDict": "Propafenone"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 11, "shortName": "MedID", "valueDataDict": "Quinidine"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 12, "shortName": "MedID", "valueDataDict": "Sotalol"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 13, "shortName": "MedID", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 14, "shortName": "MedID", "valueDataDict": "Aspirin, Extended-Release Dipyridamole"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 15, "shortName": "MedID", "valueDataDict": "Vorapaxar"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 16, "shortName": "MedID", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 17, "shortName": "MedID", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 18, "shortName": "MedID", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 19, "shortName": "MedID", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 20, "shortName": "MedID", "valueDataDict": "Ticlopidine"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 21, "shortName": "MedID", "valueDataDict": "Betrixaban"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 22, "shortName": "MedID", "valueDataDict": "Heparin Derivative"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 23, "shortName": "MedID", "valueDataDict": "Low Molecular Weight Heparin"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 24, "shortName": "MedID", "valueDataDict": "Unfractionated Heparin"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 25, "shortName": "MedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 26, "shortName": "MedID", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 27, "shortName": "MedID", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 28, "shortName": "MedID", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 29, "shortName": "MedID", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 30, "shortName": "MedID", "valueDataDict": "Angiotensin converting enzyme inhibitor (ACE-I) (Any)"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 31, "shortName": "MedID", "valueDataDict": "Angiotensin II receptor blocker neprilysin inhibitor (ARNI)"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 32, "shortName": "MedID", "valueDataDict": "Angiotensin receptor blocker (ARB) (Any)"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 33, "shortName": "MedID", "valueDataDict": "Beta blocker (Any)"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 34, "shortName": "MedID", "valueDataDict": "GLP-1 agonist"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 35, "shortName": "MedID", "valueDataDict": "SGLT inhibitor"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "MitralRegurg", "valueDataDict": "None"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "MitralStenosis", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "NoFluoroUsed", "valueDataDict": "true"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "NoRadiationKerm", "valueDataDict": "true"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "OperA_FirstName", "valueDataDict": "<PERSON><PERSON>"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "OperA_LastName", "valueDataDict": "Oommen"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "OperA_MidName", "valueDataDict": "<PERSON>"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "OperA_NPI", "valueDataDict": "**********"}, {"NCDRPatientId": "9136", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "OtherID", "valueDataDict": "32153214"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "PVI", "valueDataDict": "true"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "PVIEnergySource", "valueDataDict": "Radiofrequency"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "PhrenicNerveEval", "valueDataDict": "true"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcEvent", "valueDataDict": "A-V fistula requiring intervention"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PostProcEvent", "valueDataDict": "A-V fistula requiring intervention"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "PostProcEvent", "valueDataDict": "Acute kidney injury"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 3, "shortName": "PostProcEvent", "valueDataDict": "Bleeding - access site (transfusion)"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 4, "shortName": "PostProcEvent", "valueDataDict": "Bradycardia adverse events"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 5, "shortName": "PostProcEvent", "valueDataDict": "Cardiac arrest"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 6, "shortName": "PostProcEvent", "valueDataDict": "Cardiac surgery (unplanned emergent)"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 7, "shortName": "PostProcEvent", "valueDataDict": "Deep vein thrombosis"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 8, "shortName": "PostProcEvent", "valueDataDict": "GU Bleeding"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 9, "shortName": "PostProcEvent", "valueDataDict": "Heart failure"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 10, "shortName": "PostProcEvent", "valueDataDict": "Hematoma at access site"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 11, "shortName": "PostProcEvent", "valueDataDict": "Hemolysis"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 12, "shortName": "PostProcEvent", "valueDataDict": "Hemorrhage (non access site)"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 13, "shortName": "PostProcEvent", "valueDataDict": "Hemothorax"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 14, "shortName": "PostProcEvent", "valueDataDict": "Myocardial infarction"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 15, "shortName": "PostProcEvent", "valueDataDict": "Pericardial effusion requiring intervention"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 16, "shortName": "PostProcEvent", "valueDataDict": "Pericardial effusion resulting in cardiac tamponade"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 17, "shortName": "PostProcEvent", "valueDataDict": "Phrenic nerve damage"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 18, "shortName": "PostProcEvent", "valueDataDict": "Pleural effusion"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 19, "shortName": "PostProcEvent", "valueDataDict": "Pneumonia"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 20, "shortName": "PostProcEvent", "valueDataDict": "Pneumothorax"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 21, "shortName": "PostProcEvent", "valueDataDict": "Pseudoaneurysm requiring intervention"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 22, "shortName": "PostProcEvent", "valueDataDict": "Pulmonary embolism"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 23, "shortName": "PostProcEvent", "valueDataDict": "Pulmonary vein damage/dissection"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 24, "shortName": "PostProcEvent", "valueDataDict": "Respiratory failure"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 25, "shortName": "PostProcEvent", "valueDataDict": "<PERSON><PERSON>"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 26, "shortName": "PostProcEvent", "valueDataDict": "Stroke"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 27, "shortName": "PostProcEvent", "valueDataDict": "Transient ischemic attack (TIA)"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 28, "shortName": "PostProcEvent", "valueDataDict": "Vascular injury requiring surgical intervention"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PostProcHgbND2", "valueDataDict": "true"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 2, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 3, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 4, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 5, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 6, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 7, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 8, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 9, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 10, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 11, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 12, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 13, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 14, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 15, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 16, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 17, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 18, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 19, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 20, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 21, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 22, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 23, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 24, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 25, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 26, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 27, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 28, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PreProcBNPNotDrawn", "valueDataDict": "true"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PreProcCreat", "valueDataDict": ".86"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PreProcCreatND", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PreProcCreat_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "PreProcICEPerf", "valueDataDict": "Yes - 2D"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "PreProcNTBNPNotDrawn", "valueDataDict": "true"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcHxOccur", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "ProcHxOccur", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "ProcHxOccur", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "ProcHxOccur", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcStatus", "valueDataDict": "Outpatient"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "ProcedHxName", "valueDataDict": "AV Node ablation with Pacemaker Implantation"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 2, "shortName": "ProcedHxName", "valueDataDict": "Left Atrial Appendage Occlusion"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 3, "shortName": "ProcedHxName", "valueDataDict": "Atrial Fibrillation Termination Attempt"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 4, "shortName": "ProcedHxName", "valueDataDict": "Atrial Flutter Termination Attempt"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcedureEndDateTime", "valueDataDict": "2024-11-14T13:35:00"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcedureStartDateTime", "valueDataDict": "2024-11-14T09:06:00"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Pulse", "valueDataDict": "48"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Pulse_unit", "valueDataDict": "bpm"}, {"NCDRPatientId": "9136", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceAmIndian", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Race<PERSON>ian", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceBlack", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceNatHaw", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "true"}, {"NCDRPatientId": "9136", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "SSNNA", "valueDataDict": "true"}, {"NCDRPatientId": "9136", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Sex", "valueDataDict": "Male"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "SleepApneaRxFollowed", "valueDataDict": "true"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "SxExperienced", "valueDataDict": "Fatigue|Anxiety|Irregular heartbeat"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "SystolicBP", "valueDataDict": "119"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "SystolicBP_unit", "valueDataDict": "mm[Hg]"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "TTEPerf", "valueDataDict": "false"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "TransseptCath", "valueDataDict": "Single"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 1, "IncrementalId": 1, "shortName": "UnintAnticoagTx", "valueDataDict": "true"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Weight", "valueDataDict": "106.91"}, {"NCDRPatientId": "9136", "EpisodeKey": "**********", "VisitId": 0, "IncrementalId": 1, "shortName": "Weight_unit", "valueDataDict": "kg"}, {"NCDRPatientId": "9136", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCode", "valueDataDict": "94111"}, {"NCDRPatientId": "9136", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCodeNA", "valueDataDict": "false"}]