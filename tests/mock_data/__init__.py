import os
import json
import pandas as pd


def dir_path():
    return os.path.dirname(os.path.abspath(__file__))


def get_mock_data_file_path(file_name, dataset=None):
    if dataset:
        return os.path.join(dir_path(), dataset, file_name)
    else:
        return os.path.join(dir_path(), file_name)


def load_mock_data(file_name, as_df=True, dataset=None):
    """
    load mock json data from file and return as dict or pandas dataframe
    :param dataset:
    :param file_name:
    :param as_df:
    :return:
    """
    file_path = get_mock_data_file_path(file_name + '.json', dataset=dataset)
    with open(file_path, 'r') as f:
        data = json.load(f)
    if as_df:
        data = pd.DataFrame(data)
    return data
