[{"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ADJ_BleedAdjStatus", "valueDataDict": "Alive"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ADJ_BleedDevRelated", "valueDataDict": "Unlikely"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ADJ_BleedEndOrganDamage", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ADJ_BleedImagePerf", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ADJ_BleedInvInter", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ADJ_BleedMajorSurgery", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ADJ_BleedPCI", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ADJ_BleedProcRelated", "valueDataDict": "Possible"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ADJ_BleedRBCTransfusion", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 3, "ParentIncrementalId": 1, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 4, "ParentIncrementalId": 1, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 5, "ParentIncrementalId": 1, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 6, "ParentIncrementalId": 1, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 7, "ParentIncrementalId": 1, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 8, "ParentIncrementalId": 1, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 9, "ParentIncrementalId": 1, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 10, "ParentIncrementalId": 1, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 11, "ParentIncrementalId": 1, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 12, "ParentIncrementalId": 1, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 13, "ParentIncrementalId": 1, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 14, "ParentIncrementalId": 1, "shortName": "ADJ_MedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 15, "ParentIncrementalId": 1, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 16, "ParentIncrementalId": 1, "shortName": "ADJ_MedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 17, "ParentIncrementalId": 1, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 18, "ParentIncrementalId": 1, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 19, "ParentIncrementalId": 1, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 20, "ParentIncrementalId": 1, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 21, "ParentIncrementalId": 2, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 22, "ParentIncrementalId": 2, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 23, "ParentIncrementalId": 2, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 24, "ParentIncrementalId": 2, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 25, "ParentIncrementalId": 2, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 26, "ParentIncrementalId": 2, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 27, "ParentIncrementalId": 2, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 28, "ParentIncrementalId": 2, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 29, "ParentIncrementalId": 2, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 30, "ParentIncrementalId": 2, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 31, "ParentIncrementalId": 2, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 32, "ParentIncrementalId": 2, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 33, "ParentIncrementalId": 2, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 34, "ParentIncrementalId": 2, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 35, "ParentIncrementalId": 2, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 36, "ParentIncrementalId": 2, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 37, "ParentIncrementalId": 2, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 38, "ParentIncrementalId": 2, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 39, "ParentIncrementalId": 2, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 40, "ParentIncrementalId": 2, "shortName": "ADJ_MedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ADJ_MedID", "valueDataDict": "Fondaparinux"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "ADJ_MedID", "valueDataDict": "Heparin Derivative"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 3, "ParentIncrementalId": 1, "shortName": "ADJ_MedID", "valueDataDict": "Low Molecular Weight Heparin"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 4, "ParentIncrementalId": 1, "shortName": "ADJ_MedID", "valueDataDict": "Unfractionated Heparin"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 5, "ParentIncrementalId": 1, "shortName": "ADJ_MedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 6, "ParentIncrementalId": 1, "shortName": "ADJ_MedID", "valueDataDict": "Aspirin 81 to 100 mg"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 7, "ParentIncrementalId": 1, "shortName": "ADJ_MedID", "valueDataDict": "Aspirin 101 to 324 mg"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 8, "ParentIncrementalId": 1, "shortName": "ADJ_MedID", "valueDataDict": "Aspirin 325 mg"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 9, "ParentIncrementalId": 1, "shortName": "ADJ_MedID", "valueDataDict": "Aspirin/Dipyridamole"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 10, "ParentIncrementalId": 1, "shortName": "ADJ_MedID", "valueDataDict": "Vorapaxar"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 11, "ParentIncrementalId": 1, "shortName": "ADJ_MedID", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 12, "ParentIncrementalId": 1, "shortName": "ADJ_MedID", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 13, "ParentIncrementalId": 1, "shortName": "ADJ_MedID", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 14, "ParentIncrementalId": 1, "shortName": "ADJ_MedID", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 15, "ParentIncrementalId": 1, "shortName": "ADJ_MedID", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 16, "ParentIncrementalId": 1, "shortName": "ADJ_MedID", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 17, "ParentIncrementalId": 1, "shortName": "ADJ_MedID", "valueDataDict": "Other P2Y12"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 18, "ParentIncrementalId": 1, "shortName": "ADJ_MedID", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 19, "ParentIncrementalId": 1, "shortName": "ADJ_MedID", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 20, "ParentIncrementalId": 1, "shortName": "ADJ_MedID", "valueDataDict": "Ticlopidine"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 21, "ParentIncrementalId": 2, "shortName": "ADJ_MedID", "valueDataDict": "Fondaparinux"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 22, "ParentIncrementalId": 2, "shortName": "ADJ_MedID", "valueDataDict": "Heparin Derivative"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 23, "ParentIncrementalId": 2, "shortName": "ADJ_MedID", "valueDataDict": "Low Molecular Weight Heparin"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 24, "ParentIncrementalId": 2, "shortName": "ADJ_MedID", "valueDataDict": "Unfractionated Heparin"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 25, "ParentIncrementalId": 2, "shortName": "ADJ_MedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 26, "ParentIncrementalId": 2, "shortName": "ADJ_MedID", "valueDataDict": "Aspirin 81 to 100 mg"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 27, "ParentIncrementalId": 2, "shortName": "ADJ_MedID", "valueDataDict": "Aspirin 101 to 324 mg"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 28, "ParentIncrementalId": 2, "shortName": "ADJ_MedID", "valueDataDict": "Aspirin 325 mg"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 29, "ParentIncrementalId": 2, "shortName": "ADJ_MedID", "valueDataDict": "Aspirin/Dipyridamole"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 30, "ParentIncrementalId": 2, "shortName": "ADJ_MedID", "valueDataDict": "Vorapaxar"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 31, "ParentIncrementalId": 2, "shortName": "ADJ_MedID", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 32, "ParentIncrementalId": 2, "shortName": "ADJ_MedID", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 33, "ParentIncrementalId": 2, "shortName": "ADJ_MedID", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 34, "ParentIncrementalId": 2, "shortName": "ADJ_MedID", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 35, "ParentIncrementalId": 2, "shortName": "ADJ_MedID", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 36, "ParentIncrementalId": 2, "shortName": "ADJ_MedID", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 37, "ParentIncrementalId": 2, "shortName": "ADJ_MedID", "valueDataDict": "Other P2Y12"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 38, "ParentIncrementalId": 2, "shortName": "ADJ_MedID", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 39, "ParentIncrementalId": 2, "shortName": "ADJ_MedID", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 40, "ParentIncrementalId": 2, "shortName": "ADJ_MedID", "valueDataDict": "Ticlopidine"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "ADJ_NeuroAdjStatus", "valueDataDict": "Alive"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "ADJ_NeuroBrainImaging", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "ADJ_NeuroBrainImagingType", "valueDataDict": "Computed Tomography"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "ADJ_NeuroClinicPresent", "valueDataDict": "Stroke-related"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "ADJ_NeuroDeficit", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "ADJ_NeuroDeficitType", "valueDataDict": "Infarction"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ADJ_NeuroDevRelated", "valueDataDict": "Unlikely"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "ADJ_NeuroDxConfirmed", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "ADJ_NeuroEndoTheraInter", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "ADJ_NeuroIVrTPA", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "ADJ_NeuroProcRelated", "valueDataDict": "Possible"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "ADJ_NeuroSxDuration", "valueDataDict": "Greater than 24 Hours"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "ADJ_NeuroSxOnset", "valueDataDict": "2023-11-27"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "ADJ_NeuroTrauma", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "ADJ_RankinScale", "valueDataDict": "5: Severe disability"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "ADJ_RankinScaleNA", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "AFibClass", "valueDataDict": "Paroxysmal (terminating spontaneously within 7 days)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "AFibInd", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "AFlutter", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "AJ_AdjudEvent", "valueDataDict": "Other Hemorrhage (non-intracranial) (Complete Adjudication)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "AJ_AdjudEvent", "valueDataDict": "Ischemic Stroke (Complete Adjudication)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "AJ_EventDate", "valueDataDict": "2023-11-27"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "AJ_EventDate", "valueDataDict": "2023-11-27"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "AccessSysCounter", "valueDataDict": "1"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "AccessSysCounter", "valueDataDict": "2"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "AccessSysID", "valueDataDict": "5188"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "AccessSysID", "valueDataDict": "5186"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "Albumin_ND", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "Anesthesia", "valueDataDict": "General Anesthesia"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "AnticoagReversal", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ArrivalDate", "valueDataDict": "2023-11-27"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "AtrialRhythm", "valueDataDict": "Atrial fibrillation"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "BaselineImagingPerf", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "BleedEventType", "valueDataDict": "Gastrointestinal Bleed"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "CAD", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "CM", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "CardStrucInterv", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ChadCHF", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ChadDM", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ChadHypertCont", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ChadLVDysf", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ChadStroke", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ChadTE", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ChadTIA", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ChadVascDis", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ChronicLungDisease", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ClinicBleedEvent", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ConAntiCoagTx", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ConcomitantProcPerf", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ContrastVol", "valueDataDict": "50"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ContrastVol_unit", "valueDataDict": "mL"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "DCDate", "valueDataDict": "2023-11-27"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "DCHospice", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "DCLocation", "valueDataDict": "Home"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "DCStatus", "valueDataDict": "Alive"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 3, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 4, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 5, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 6, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 7, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 8, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 9, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 10, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 11, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 12, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 13, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 14, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 15, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 16, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 17, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 18, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Fondaparinux"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Heparin Derivative"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 3, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Low Molecular Weight Heparin"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 4, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Unfractionated Heparin"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 5, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 6, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 7, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Aspirin/Dipyridamole"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 8, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Vorapaxar"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 9, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 10, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 11, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 12, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 13, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 14, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 15, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Other P2Y12"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 16, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 17, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 18, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Ticlopidine"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "DOB", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "DevCounter", "valueDataDict": "1"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "DevCounter", "valueDataDict": "2"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 3, "ParentIncrementalId": 2, "shortName": "DevCounter", "valueDataDict": "1"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "DevSucdep", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "DevSucdep", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 3, "ParentIncrementalId": 2, "shortName": "DevSucdep", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "DiastolicBP", "valueDataDict": "72"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "DiastolicBP_unit", "valueDataDict": "mm[Hg]"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "EnrolledStudy", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "EpicardialAppCons", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "271376002"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "410429000"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 3, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "84114007"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 4, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "368009"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 5, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "473360003"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 6, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "22298006"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 7, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002125"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 8, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "3238004"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 9, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "39579001"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 10, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "65198009"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 11, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "128053003"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 12, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002126"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 13, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002127"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 14, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002128"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 15, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "100014076"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 16, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "100001141"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 17, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002137"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 18, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "370512004"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 19, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000001839"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 20, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002138"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 21, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002139"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 22, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002140"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 23, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002141"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 24, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002143"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 25, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002144"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 26, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002145"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 27, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002146"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 28, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "230706003"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 29, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "1386000"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 30, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "422504002"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 31, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "266257000"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 32, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "230713003"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 33, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "**********"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 34, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "74474003"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 35, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "385494008"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 36, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002147"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 37, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "100001011"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 38, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "50960005"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 39, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002148"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 40, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002149"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 41, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002150"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 42, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "95549001"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 43, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "213217008"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 44, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "60046008"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 45, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "233604007"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 46, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002153"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 47, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002152"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 48, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "59282003"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 49, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "409622000"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "FirstName", "valueDataDict": "K"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "FluoroDoseKerm", "valueDataDict": "1040"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "FluoroDoseKerm_unit", "valueDataDict": "mGy"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "GeneticCoag", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "GuidanceMethodID", "valueDataDict": "Fluoroscopy|Transesophageal Echocardiogram (TEE)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HBAbnLiver", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HBAbnRenal", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HBAlcohol", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HBBleed", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HBDrugAP", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HBDrugNSAID", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HBHyperUncont", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HBLabINR", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HBStroke", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HGB", "valueDataDict": "12.2"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HGBND", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HGB_unit", "valueDataDict": "g/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HIPS", "valueDataDict": "Private Health Insurance"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HealthIns", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "Height", "valueDataDict": "167"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "Height_unit", "valueDataDict": "cm"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "INR", "valueDataDict": "1.1"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "INRND", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "IncrFallRisk", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "IntraProcAnticoag", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "LAADevID", "valueDataDict": "5235"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "LAADevID", "valueDataDict": "5234"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 3, "ParentIncrementalId": 2, "shortName": "LAADevID", "valueDataDict": "5242"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "LAAIsolationApproach", "valueDataDict": "Percutaneous"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "LAAIsolationApproach", "valueDataDict": "Percutaneous"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 3, "ParentIncrementalId": 2, "shortName": "LAAIsolationApproach", "valueDataDict": "Percutaneous"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "LAAOInterv", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "LAAO_Adm", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "LAAO_OrWid", "valueDataDict": "20.4"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "LAAO_OrWid_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "LVEF", "valueDataDict": "60"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "LVEFAssessed", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "LVEF_unit", "valueDataDict": "%"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "LastName", "valueDataDict": "W"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Fondaparinux"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Heparin Derivative"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 3, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Low Molecular Weight Heparin"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 4, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Unfractionated Heparin"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 5, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 6, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Aspirin 81 to 100 mg"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 7, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Aspirin 101 to 324 mg"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 8, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Aspirin 325 mg"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 9, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Aspirin/Dipyridamole"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 10, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Vorapaxar"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 11, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 12, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 13, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 14, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 15, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 16, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 17, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Other P2Y12"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 18, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 19, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 20, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Ticlopidine"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "OHSConversion", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "OperA_FirstName2", "valueDataDict": "E"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "OperA_FirstName2", "valueDataDict": "A"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "OperA_LastName2", "valueDataDict": "G"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "OperA_LastName2", "valueDataDict": "S"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "OperA_MidName2", "valueDataDict": "M"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "OperA_NPI2", "valueDataDict": "12345"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "OperA_NPI2", "valueDataDict": "45678"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "OrigNCDRVen", "valueDataDict": "ACC"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "OrigPtID", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "OtherID", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "OutDevUnsucDepl", "valueDataDict": "<PERSON><PERSON> retrieved"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "OutDevUnsucDepl", "valueDataDict": "<PERSON><PERSON> retrieved"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 3, "ParentIncrementalId": 2, "shortName": "OutDevUnsucDepl", "valueDataDict": "<PERSON><PERSON> retrieved"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON>", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PT", "valueDataDict": "15.1"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PTND", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PT_unit", "valueDataDict": "sec"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PlateletCt", "valueDataDict": "199000"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PlateletCtND", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PlateletCt_unit", "valueDataDict": "μL"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PostProcCreatND2", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PostProcHgb2", "valueDataDict": "12.2"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PostProcHgb2_unit", "valueDataDict": "g/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PostProcHgbND2", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 3, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 4, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 5, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 6, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 7, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 8, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 9, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 10, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 11, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 12, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 13, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 14, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 15, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 16, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 17, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 18, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 19, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 20, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 21, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 22, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 23, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 24, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 25, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 26, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 27, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 28, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 29, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 30, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 31, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 32, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 33, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 34, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 35, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 36, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 37, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 38, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 39, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 40, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 41, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 42, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 43, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 44, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 45, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 46, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 47, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 48, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 49, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PostProcPeakCreatND", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PostProc_RankinScaleNA", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 3, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 4, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 5, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 6, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Past"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 7, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 8, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 9, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 10, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 11, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Held"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 12, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 13, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 14, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 15, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 16, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 17, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 18, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 19, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 20, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PreProcCreat", "valueDataDict": "1.16"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PreProcCreatND", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PreProcCreat_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PrevAFibTerm", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PrevAFibTermCA", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PrevAFibTermDC", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PrevAFibTermPC", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PrevAFibTermSA", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PriorVD", "valueDataDict": "Coronary Artery Disease (CAD)*"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ProcAborted", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ProcAbortedReason", "valueDataDict": "Anatomy not conducive for implant"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ProcAtrialThromDetect", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ProcBivalirudin2", "valueDataDict": "No - Not Prescribed"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ProcCanceled", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Air Embolism"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Cardiac Arrest"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 3, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Heart Failure"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 4, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Heart Valve Damage"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 5, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Left Atrial Thrombus"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 6, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Myocardial Infarction"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 7, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pericardial Effusion (no intervention required)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 8, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 9, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 10, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Arterial Thrombosis"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 11, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Deep Vein Thrombosis"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 12, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Systemic Thromboembolism (other than stroke) (Complete Adjudication)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 13, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Esophageal Injury (resulting from TEE probe)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 14, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Hepatic Injury"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 15, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "New Requirement for Dialysis"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 16, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Device Explant"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 17, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Device Infection"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 18, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "<PERSON><PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 19, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "<PERSON><PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 20, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Device Systemic Embolization (catheter retrieval)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 21, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Device Systemic Embolization (surgical retrieval)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 22, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "AV Fistula (no intervention required)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 23, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "AV Fistula (requiring surgical repair) (Complete Adjudication)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 24, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pseudoaneurysm (no intervention required)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 25, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pseudoaneurysm (requiring endovascular repair) (Complete Adjudication)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 26, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pseudoaneurysm (requiring surgical repair) (Complete Adjudication)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 27, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pseudoaneurysm (requiring thrombin injection only) (Complete Adjudication)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 28, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Hemorrhagic Stroke (Complete Adjudication)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 29, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Intracranial Hemorrhage (other than hemorrhagic stroke) (Complete Adjudication)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 30, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Ischemic Stroke (Complete Adjudication)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 31, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "TIA (Complete Adjudication)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 32, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Undetermined Stroke (Complete Adjudication)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 33, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Access Site Bleeding (Complete Adjudication)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 34, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "GI Bleeding (Complete Adjudication)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 35, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Hematoma (Complete Adjudication)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 36, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Hemothorax (not requiring drainage) (Complete Adjudication)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 37, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Hemothorax (requiring drainage) (Complete Adjudication)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 38, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Other Hemorrhage (non-intracranial) (Complete Adjudication)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 39, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pericardial Effusion (requiring open heart surgery) (Complete Adjudication)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 40, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pericardial Effusion with tamponade (requiring percutaneous drainage) (Complete Adjudication)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 41, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pericardial Effusion without tamponade (requiring percutaneous drainage) (Complete Adjudication)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 42, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Retroperitoneal Bleeding (Complete Adjudication)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 43, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Vascular Complications (Complete Adjudication)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 44, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pleural Effusion"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 45, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pneumonia"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 46, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pneumothorax (no intervention required)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 47, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pneumothorax (requiring intervention)"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 48, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pulmonary Embolism"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 49, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Respiratory Failure"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ProcHeparin2", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ProcHeparinInitAdminTime", "valueDataDict": "Pre-transseptal Puncture"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ProcLAAOInd", "valueDataDict": "High fall risk|History of major bleed|Increased thromboembolic stroke risk"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ProcOtherAnticoag2", "valueDataDict": "No - Not Prescribed"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ProcedureEndDateTime", "valueDataDict": "2023-11-27T12:16:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ProcedureLocation", "valueDataDict": "Cardiac Catheterization Laboratory"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ProcedureStartDateTime", "valueDataDict": "2023-11-27T10:23:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "Pulse", "valueDataDict": "107"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "Pulse_unit", "valueDataDict": "bpm"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "RaceAmIndian", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "Race<PERSON>ian", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "RaceBlack", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "RaceNatHaw", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "SDM_Proc", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "SDM_Tool", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "SSNNA", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "Sex", "valueDataDict": "Male"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "SleepApnea", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "Sx_F", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "SystolicBP", "valueDataDict": "166"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "SystolicBP_unit", "valueDataDict": "mm[Hg]"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "TEEDateLAAO", "valueDataDict": "2023-11-27"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "TEEPerfLAAO", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "TTEPerf", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ValvularAF", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "Warfarin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "Weight", "valueDataDict": "77.7"}, {"NCDRPatientId": "12345", "EpisodeKey": "434528d0-b10d-474a-9772-da310d73b49d", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "Weight_unit", "valueDataDict": "kg"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ZipCode", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ZipCodeNA", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "AFibClass", "valueDataDict": "Paroxysmal (terminating spontaneously within 7 days)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "AFibInd", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "AFlutter", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "AccessSysCounter", "valueDataDict": "1"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "AccessSysID", "valueDataDict": "5188"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "Albumin_ND", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "Anesthesia", "valueDataDict": "General Anesthesia"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "AnticoagReversal", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ArrivalDate", "valueDataDict": "2023-11-26"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "AtrialRhythm", "valueDataDict": "Sinus node rhythm"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "BaselineImagingPerf", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "BleedEventType", "valueDataDict": "Gastrointestinal Bleed"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "CAD", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "CM", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "CardStrucInterv", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ChadCHF", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ChadDM", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ChadHypertCont", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ChadLVDysf", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ChadStroke", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ChadTE", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ChadTIA", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ChadVascDis", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ChronicLungDisease", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ClinicBleedEvent", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ConAntiCoagTx", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ConcomitantProcPerf", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ContrastVol", "valueDataDict": "10"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ContrastVol_unit", "valueDataDict": "mL"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "DCDate", "valueDataDict": "2023-11-27"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "DCHospice", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "DCLocation", "valueDataDict": "Home"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "DCStatus", "valueDataDict": "Alive"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 3, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 4, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 5, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 6, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 7, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 8, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 9, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 10, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 11, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 12, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 13, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 14, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 15, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 16, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 17, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 18, "ParentIncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "No - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 6, "ParentIncrementalId": 1, "shortName": "DC_MedDose", "valueDataDict": "Aspirin 81 to 100 mg"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Fondaparinux"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Heparin Derivative"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 3, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Low Molecular Weight Heparin"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 4, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Unfractionated Heparin"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 5, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 6, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 7, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Aspirin/Dipyridamole"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 8, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Vorapaxar"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 9, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 10, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 11, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 12, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 13, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 14, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 15, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Other P2Y12"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 16, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 17, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 18, "ParentIncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Ticlopidine"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "DOB", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "DevCounter", "valueDataDict": "1"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "DevSucdep", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "DiastolicBP", "valueDataDict": "49"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "DiastolicBP_unit", "valueDataDict": "mm[Hg]"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "EnrolledStudy", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "EpicardialAppCons", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "271376002"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "410429000"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 3, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "84114007"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 4, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "368009"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 5, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "473360003"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 6, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "22298006"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 7, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002125"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 8, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "3238004"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 9, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "39579001"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 10, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "65198009"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 11, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "128053003"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 12, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002126"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 13, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002127"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 14, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002128"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 15, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "100014076"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 16, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "100001141"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 17, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002137"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 18, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "370512004"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 19, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000001839"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 20, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002138"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 21, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002139"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 22, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002140"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 23, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002141"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 24, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002143"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 25, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002144"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 26, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002145"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 27, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002146"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 28, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "230706003"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 29, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "1386000"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 30, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "422504002"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 31, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "266257000"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 32, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "230713003"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 33, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "**********"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 34, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "74474003"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 35, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "385494008"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 36, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002147"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 37, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "100001011"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 38, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "50960005"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 39, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002148"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 40, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002149"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 41, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002150"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 42, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "95549001"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 43, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "213217008"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 44, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "60046008"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 45, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "233604007"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 46, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002153"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 47, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "112000002152"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 48, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "59282003"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 49, "ParentIncrementalId": 1, "shortName": "EventId", "valueDataDict": "409622000"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "FirstName", "valueDataDict": "L"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "FluoroDoseKerm", "valueDataDict": "64"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "FluoroDoseKerm_unit", "valueDataDict": "mGy"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "GeneticCoag", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "GuidanceMethodID", "valueDataDict": "Fluoroscopy|Transesophageal Echocardiogram (TEE)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HBAbnLiver", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HBAbnRenal", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HBAlcohol", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HBBleed", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HBDrugAP", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HBDrugNSAID", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HBHyperUncont", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HBLabINR", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HBStroke", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HGB", "valueDataDict": "9.8"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HGBND", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HGB_unit", "valueDataDict": "g/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HIPS", "valueDataDict": "State-Specific Plan (non-Medicaid)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "HealthIns", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "Height", "valueDataDict": "142"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "Height_unit", "valueDataDict": "cm"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "INR", "valueDataDict": "0.9"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "INRND", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "IncrFallRisk", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "IntraProcAnticoag", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "LAADevID", "valueDataDict": "5375"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "LAAIsolationApproach", "valueDataDict": "Percutaneous"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "LAAOInterv", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "LAAO_Adm", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "LAAO_OrWid", "valueDataDict": "21.7"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "LAAO_OrWid_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "LVEFAssessed", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "LastName", "valueDataDict": "M"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Fondaparinux"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Heparin Derivative"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 3, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Low Molecular Weight Heparin"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 4, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Unfractionated Heparin"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 5, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 6, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Aspirin 81 to 100 mg"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 7, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Aspirin 101 to 324 mg"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 8, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Aspirin 325 mg"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 9, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Aspirin/Dipyridamole"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 10, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Vorapaxar"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 11, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 12, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 13, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 14, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 15, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 16, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 17, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Other P2Y12"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 18, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 19, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 20, "ParentIncrementalId": 1, "shortName": "MedID", "valueDataDict": "Ticlopidine"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "OHSConversion", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "OperA_FirstName2", "valueDataDict": "<PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "OperA_FirstName2", "valueDataDict": "<PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "OperA_LastName2", "valueDataDict": "Groves"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "OperA_LastName2", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "OperA_MidName2", "valueDataDict": "<PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "OperA_NPI2", "valueDataDict": "**********"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "OperA_NPI2", "valueDataDict": "**********"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "OrigNCDRVen", "valueDataDict": "ACC"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "OrigPtID", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "OtherID", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON>", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PT", "valueDataDict": "12.9"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PTND", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PT_unit", "valueDataDict": "sec"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PlateletCt", "valueDataDict": "208000"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PlateletCtND", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PlateletCt_unit", "valueDataDict": "μL"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PostProcCreat2", "valueDataDict": "1.4"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PostProcCreat2_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PostProcCreatND2", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PostProcHgb2", "valueDataDict": "7.5"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PostProcHgb2_unit", "valueDataDict": "g/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PostProcHgbND2", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 3, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 4, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 5, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 6, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 7, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 8, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 9, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 10, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 11, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 12, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 13, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 14, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 15, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 16, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 17, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 18, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 19, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 20, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 21, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 22, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 23, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 24, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 25, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 26, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 27, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 28, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 29, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 30, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 31, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 32, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 33, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 34, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 35, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 36, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 37, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 38, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 39, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 40, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 41, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 42, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 43, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 44, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 45, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 46, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 47, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 48, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 49, "ParentIncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PostProcPeakCreatND", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PostProc_RankinScaleNA", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 3, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 4, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 5, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 6, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Past"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 7, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 8, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 9, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 10, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 11, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 12, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 13, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 14, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 15, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 16, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Current"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 17, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 18, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 19, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 20, "ParentIncrementalId": 1, "shortName": "PreMedAdmin", "valueDataDict": "Never"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PreProcCreat", "valueDataDict": "1.47"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PreProcCreatND", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PreProcCreat_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PrevAFibTerm", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "PriorVD", "valueDataDict": "Coronary Artery Disease (CAD)*|Percutaneous Coronary Intervention (PCI)*"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ProcAborted", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ProcAtrialThromDetect", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ProcBivalirudin2", "valueDataDict": "No - Not Prescribed"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ProcCanceled", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Air Embolism"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 2, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Cardiac Arrest"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 3, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Heart Failure"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 4, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Heart Valve Damage"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 5, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Left Atrial Thrombus"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 6, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Myocardial Infarction"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 7, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pericardial Effusion (no intervention required)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 8, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 9, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 10, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Arterial Thrombosis"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 11, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Deep Vein Thrombosis"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 12, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Systemic Thromboembolism (other than stroke) (Complete Adjudication)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 13, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Esophageal Injury (resulting from TEE probe)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 14, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Hepatic Injury"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 15, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "New Requirement for Dialysis"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 16, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Device Explant"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 17, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Device Infection"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 18, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "<PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 19, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "<PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 20, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Device Systemic Embolization (catheter retrieval)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 21, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Device Systemic Embolization (surgical retrieval)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 22, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "AV Fistula (no intervention required)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 23, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "AV Fistula (requiring surgical repair) (Complete Adjudication)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 24, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pseudoaneurysm (no intervention required)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 25, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pseudoaneurysm (requiring endovascular repair) (Complete Adjudication)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 26, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pseudoaneurysm (requiring surgical repair) (Complete Adjudication)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 27, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pseudoaneurysm (requiring thrombin injection only) (Complete Adjudication)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 28, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Hemorrhagic Stroke (Complete Adjudication)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 29, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Intracranial Hemorrhage (other than hemorrhagic stroke) (Complete Adjudication)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 30, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Ischemic Stroke (Complete Adjudication)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 31, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "TIA (Complete Adjudication)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 32, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Undetermined Stroke (Complete Adjudication)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 33, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Access Site Bleeding (Complete Adjudication)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 34, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "GI Bleeding (Complete Adjudication)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 35, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Hematoma (Complete Adjudication)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 36, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Hemothorax (not requiring drainage) (Complete Adjudication)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 37, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Hemothorax (requiring drainage) (Complete Adjudication)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 38, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Other Hemorrhage (non-intracranial) (Complete Adjudication)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 39, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pericardial Effusion (requiring open heart surgery) (Complete Adjudication)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 40, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pericardial Effusion with tamponade (requiring percutaneous drainage) (Complete Adjudication)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 41, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pericardial Effusion without tamponade (requiring percutaneous drainage) (Complete Adjudication)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 42, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Retroperitoneal Bleeding (Complete Adjudication)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 43, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Vascular Complications (Complete Adjudication)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 44, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pleural Effusion"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 45, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pneumonia"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 46, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pneumothorax (no intervention required)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 47, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pneumothorax (requiring intervention)"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 48, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Pulmonary Embolism"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 49, "ParentIncrementalId": 1, "shortName": "ProcEvents", "valueDataDict": "Respiratory Failure"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ProcHeparin2", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ProcHeparinInitAdminTime", "valueDataDict": "Pre-transseptal Puncture"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ProcLAAOInd", "valueDataDict": "High fall risk|History of major bleed|Increased thromboembolic stroke risk"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ProcOtherAnticoag2", "valueDataDict": "No - Not Prescribed"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ProcedureEndDateTime", "valueDataDict": "2023-11-26T13:49:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ProcedureLocation", "valueDataDict": "Cardiac Catheterization Laboratory"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ProcedureStartDateTime", "valueDataDict": "2023-11-26T12:50:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "Pulse", "valueDataDict": "57"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "Pulse_unit", "valueDataDict": "bpm"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "RaceAmIndian", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "Race<PERSON>ian", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "RaceBlack", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "RaceNatHaw", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ResidualLeak", "valueDataDict": "0"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ResidualLeakNA", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ResidualLeak_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "SDM_Proc", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "SDM_Tool", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "SSNNA", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "Sex", "valueDataDict": "Female"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "SleepApnea", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "Sx_F", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "SystolicBP", "valueDataDict": "141"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "SystolicBP_unit", "valueDataDict": "mm[Hg]"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "TEEDateLAAO", "valueDataDict": "2023-11-26"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "TEEPerfLAAO", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "TTEPerf", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ValvularAF", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 1, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "Warfarin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "Weight", "valueDataDict": "45.8"}, {"NCDRPatientId": "67890", "EpisodeKey": "2e963a5b-99af-4fdb-8001-425ea86b2261", "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "Weight_unit", "valueDataDict": "kg"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ZipCode", "valueDataDict": "00000"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "ParentIncrementalId": 1, "shortName": "ZipCodeNA", "valueDataDict": "No"}]