[{"Container Class": "patientContainer", "Parent Section": "Root", "Section Display Name": "Demographics", "Section Code": "DEMOGRAPHICS", "Section Type": "Section", "Cardinality": "1..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "Episode of Care", "Section Code": "EOC", "Section Type": "Section", "Cardinality": "1..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Episode of Care", "Section Display Name": "Episode Information", "Section Code": "EOCINFO", "Section Type": "Section", "Cardinality": "1..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Episode of Care", "Section Display Name": "Research Study", "Section Code": "RSTUDY", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Episode of Care", "Section Display Name": "LAAO Intervention", "Section Code": "LAAOINTERVENT", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "History and Risk Factors", "Section Code": "HISTORYANDRISK", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "History and Risk Factors", "Section Display Name": "CHA2DS2-VASc Risk Scores", "Section Code": "CHA2DS2", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "History and Risk Factors", "Section Display Name": "HAS-BLED Risk Scores", "Section Code": "HASBLED", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "History and Risk Factors", "Section Display Name": "Additional Stroke and Bleeding Risk Factors", "Section Code": "STROKEBLEED", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "History and Risk Factors", "Section Display Name": "Rhythm History", "Section Code": "RHYTHM", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "History and Risk Factors", "Section Display Name": "Interventions", "Section Code": "INTERVENT", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "History and Risk Factors", "Section Display Name": "Additional History and Risk Factors", "Section Code": "AHISTORYANDRISK", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "History and Risk Factors", "Section Display Name": "Epicardial Access Assessment", "Section Code": "EPICARDIAL", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "Diagnostic Studies", "Section Code": "DIAGNOSTICS", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "Physical Exam and Labs", "Section Code": "PREPROCLABS", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "Pre-Procedure Medications", "Section Code": "PREPROCMED", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "Procedure Information", "Section Code": "PROCINFO", "Section Type": "Repeater Section", "Cardinality": "1..n"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Procedure Information", "Section Display Name": "Pre-Procedure Diagnostics", "Section Code": "PROCDIAGNOSTICS", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Procedure Information", "Section Display Name": "Procedure", "Section Code": "PROC", "Section Type": "Section", "Cardinality": "1..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Procedure", "Section Display Name": "Operator Information", "Section Code": "OPRINFO", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Procedure", "Section Display Name": "Fellow Information", "Section Code": "FELLOW", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Procedure Information", "Section Display Name": "Access Systems", "Section Code": "ACCESSSYS", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Access Systems", "Section Display Name": "Devices", "Section Code": "DEVICES", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Procedure Information", "Section Display Name": "Radiation Exposure", "Section Code": "RADIATION", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Procedure Information", "Section Display Name": "Intraprocedure Anticoagulation Strategy", "Section Code": "INTRAPROCANTICOAG", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Procedure Information", "Section Display Name": "Intra or Post-Procedure Events", "Section Code": "IPPEVENTS", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Procedure Information", "Section Display Name": "In-Hospital Adjudication", "Section Code": "HOSPEVEADJ", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "In-Hospital Adjudication", "Section Display Name": "Neurologic", "Section Code": "NEURO", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Neurologic", "Section Display Name": "Neurologic", "Section Code": "NEURODEV", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "In-Hospital Adjudication", "Section Display Name": "Bleeding", "Section Code": "BLEED", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Bleeding", "Section Display Name": "Bleeding", "Section Code": "BLEEDDEV", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "In-Hospital Adjudication", "Section Display Name": "Systemic Thromboembolism", "Section Code": "SYSTHROMB", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "In-Hospital Adjudication", "Section Display Name": "In-Hospital Adjudication Medications", "Section Code": "ADJMEDS", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Procedure Information", "Section Display Name": "Post Procedure Labs", "Section Code": "POSTPROCLABS", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Post Procedure Labs", "Section Display Name": "Post Procedure Creatinine", "Section Code": "POSTPROCCRT", "Section Type": "Section", "Cardinality": "0..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Root", "Section Display Name": "Discharge", "Section Code": "DISCHARGE", "Section Type": "Section", "Cardinality": "1..1"}, {"Container Class": "episode<PERSON><PERSON><PERSON>", "Parent Section": "Discharge", "Section Display Name": "Discharge Medications", "Section Code": "DCMEDS", "Section Type": "Repeater Section", "Cardinality": "0..n"}, {"Container Class": "submissionInfoContainer", "Parent Section": "Root", "Section Display Name": "Administration", "Section Code": "ADMIN", "Section Type": "Section", "Cardinality": "1..1"}]