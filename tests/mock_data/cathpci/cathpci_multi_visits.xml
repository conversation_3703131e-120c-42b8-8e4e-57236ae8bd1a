<registryDocument xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" schemaVersion="1.0">
  <submission xmsnId="424283">
    <section code="ADMIN" displayName="Z. Administration">
      <element code="2.16.840.1.113883.3.3478.4.836" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Participant ID">
        <value value="123" xsi:type="NUM" />
      </element>
      <element code="2.16.840.1.113883.3.3478.4.836" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Participant Name">
        <value value="CVHS" xsi:type="ST" />
      </element>
      <element code="*******.4.1.19376.*******.5.45" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Time Frame of Data Submission">
        <value value="2022Q1" xsi:type="ST" />
      </element>
      <element code="*******.4.1.19376.*******.5.45" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Transmission Number">
        <value value="" xsi:type="NUM" />
      </element>
      <element code="2.16.840.1.113883.3.3478.4.840" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Vendor Identifier">
        <value value="ACC" xsi:type="ST" />
      </element>
      <element code="2.16.840.1.113883.3.3478.4.847" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Vendor Software Version">
        <value value="ACC01" xsi:type="ST" />
      </element>
      <element code="2.16.840.1.113883.3.3478.4.841" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Registry Identifier">
        <value value="ACC-NCDR-CathPCI-5.0" xsi:type="ST" />
      </element>
      <element code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Submission Type">
        <value code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Episode of Care Records Only" xsi:type="CD" />
      </element>
      <element code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Registry Schema Version">
        <value value="1" xsi:type="NUM" />
      </element>
    </section>
  </submission>
  <patient ncdrPatientId="12345">
    <section code="DEMOGRAPHICS" displayName="A. Demographics">
      <element code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Last Name">
        <value value="Whats in the name?" xsi:type="LN" />
      </element>
      <element code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="First Name">
        <value value="Whats in the name?" xsi:type="FN" />
      </element>
      <element code="2.16.840.1.113883.4.1" codeSystem="2.16.840.1.113883.4.1" displayName="SSN N/A">
        <value value="" xsi:type="BL" />
      </element>
      <element code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Birth Date">
        <value value="" xsi:type="DT" />
      </element>
      <element code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Sex">
        <value code="M" codeSystem="2.16.840.1.113883.5.1" displayName="Male" xsi:type="CD" />
      </element>
      <element code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Patient Zip Code">
        <value value="" xsi:type="ST" />
      </element>
      <element code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Zip Code N/A">
        <value value="" xsi:type="BL" />
      </element>
      <element code="2106-3" codeSystem="2.16.840.1.113883.5.104" displayName="White">
        <value value="true" xsi:type="BL" />
      </element>
      <element code="2054-5" codeSystem="2.16.840.1.113883.5.104" displayName="Black/African American">
        <value value="false" xsi:type="BL" />
      </element>
      <element code="2028-9" codeSystem="2.16.840.1.113883.5.104" displayName="Asian">
        <value value="false" xsi:type="BL" />
      </element>
      <element code="1002-5" codeSystem="2.16.840.1.113883.5.104" displayName="American Indian/Alaskan Native">
        <value value="false" xsi:type="BL" />
      </element>
      <element code="2076-8" codeSystem="2.16.840.1.113883.5.104" displayName="Native Hawaiian/Pacific Islander">
        <value value="false" xsi:type="BL" />
      </element>
      <element code="2135-2" codeSystem="2.16.840.1.113883.5.50" displayName="Hispanic or Latino Ethnicity">
        <value value="false" xsi:type="BL" />
      </element>
      <element code="2.16.840.1.113883.3.3478.4.843" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Other ID">
        <value value="" xsi:type="ST" />
      </element>
    </section>
    <episode episodeKey="52b016aa-1010-4924-9b8e-4f70ba3dc18f">
      <section code="EPISODEOFCARE" displayName="B. Episode of Care">
        <section code="EOCINFO" displayName="Episode Information">
          <element code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Arrival Date and Time">
            <value value="2023-09-11T06:45:00" xsi:type="TS" />
          </element>
          <element code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1">
            <value value="" xsi:type="NUM" />
          </element>
          <element code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1">
            <value value="" xsi:type="LN" />
          </element>
          <element code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1">
            <value value="" xsi:type="FN" />
          </element>
          <element code="63513-6" codeSystem="2.16.840.1.113883.6.1" displayName="Health Insurance">
            <value value="false" xsi:type="BL" />
          </element>
          <element code="100001095" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Research Study">
            <value value="false" xsi:type="BL" />
          </element>
          <element code="100000922" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Patient Restriction">
            <value value="false" xsi:type="BL" />
          </element>
          <section code="ATTPROVIDERS" displayName="Attending Providers">
            <element code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1">
              <value value="" xsi:type="NUM" />
            </element>
            <element code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1">
              <value value="" xsi:type="LN" />
            </element>
            <element code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1">
              <value value="" xsi:type="FN" />
            </element>
          </section>
        </section>
      </section>
      <section code="HXANDRISKFACTORS" displayName="C. History and Risk Factors">
        <element code="38341003" codeSystem="2.16.840.1.113883.6.96" displayName="Hypertension">
          <value value="true" xsi:type="BL" />
        </element>
        <element code="8302-2" codeSystem="2.16.840.1.113883.6.1" displayName="Height">
          <value unit="cm" value="188" xsi:type="PQ" />
        </element>
        <element code="370992007" codeSystem="2.16.840.1.113883.6.96" displayName="Dyslipidemia">
          <value value="true" xsi:type="BL" />
        </element>
        <element code="3141-9" codeSystem="2.16.840.1.113883.6.1" displayName="Weight">
          <value unit="kg" value="88.5" xsi:type="PQ" />
        </element>
        <element code="22298006" codeSystem="2.16.840.1.113883.6.96" displayName="Prior MI">
          <value value="false" xsi:type="BL" />
        </element>
        <element code="134439009" codeSystem="2.16.840.1.113883.6.96" displayName="Family Hx of Premature CAD">
          <value value="false" xsi:type="BL" />
        </element>
        <element code="62914000" codeSystem="2.16.840.1.113883.6.96" displayName="Cerebrovascular Disease">
          <value value="false" xsi:type="BL" />
        </element>
        <element code="415070008" codeSystem="2.16.840.1.113883.6.96" displayName="Prior Percutaneous Coronary Intervention">
          <value value="false" xsi:type="BL" />
        </element>
        <element code="399957001" codeSystem="2.16.840.1.113883.6.96" displayName="Peripheral Arterial Disease">
          <value value="false" xsi:type="BL" />
        </element>
        <element code="413839001" codeSystem="2.16.840.1.113883.6.96" displayName="Chronic Lung Disease">
          <value value="false" xsi:type="BL" />
        </element>
        <element code="232717009" codeSystem="2.16.840.1.113883.6.96" displayName="Prior Coronary Artery Bypass Graft">
          <value value="false" xsi:type="BL" />
        </element>
        <element code="110483000" codeSystem="2.16.840.1.113883.6.96" displayName="Tobacco Use">
          <value code="266927001" codeSystem="2.16.840.1.113883.6.96" displayName="Unknown if Ever Smoked" xsi:type="CD" />
        </element>
        <element code="10001424808" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Cardiac Arrest Out of Healthcare Facility">
          <value value="false" xsi:type="BL" />
        </element>
        <element code="100014016" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Cardiac Arrest at Transferring Healthcare Facility">
          <value value="false" xsi:type="BL" />
        </element>
        <element code="73211009" codeSystem="2.16.840.1.113883.6.96" displayName="Diabetes Mellitus">
          <value value="false" xsi:type="BL" />
        </element>
        <element code="108241001" codeSystem="2.16.840.1.113883.6.96" displayName="Currently on Dialysis">
          <value value="false" xsi:type="BL" />
        </element>
        <element code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="CSHA Clinical Frailty Scale">
          <value code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="CSHA Clinical Frailty Scale 4: Vulnerable" xsi:type="CD" />
        </element>
      </section>
      <section code="PROCINFO" displayName="E. Procedure Information">
        <element code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Procedure Start Date and Time">
          <value value="2023-09-11T09:45:00" xsi:type="TS" />
        </element>
        <element code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Procedure End Date and Time">
          <value value="2023-09-11T10:45:00" xsi:type="TS" />
        </element>
        <element code="100001201" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Diagnostic Coronary Angiography Procedure">
          <value value="true" xsi:type="BL" />
        </element>
        <element code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1">
          <value value="" xsi:type="NUM" />
        </element>
        <element code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1">
          <value value="" xsi:type="LN" />
        </element>
        <element code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1">
          <value value="" xsi:type="FN" />
        </element>
        <element code="415070008" codeSystem="2.16.840.1.113883.6.96" displayName="Percutaneous Coronary Intervention (PCI)">
          <value value="true" xsi:type="BL" />
        </element>
        <element code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1">
          <value value="" xsi:type="NUM" />
        </element>
        <element code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1">
          <value value="" xsi:type="LN" />
        </element>
        <element code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1">
          <value value="" xsi:type="FN" />
        </element>
        <element code="67629009" codeSystem="2.16.840.1.113883.6.96" displayName="Diagnostic Left Heart Cath">
          <value value="false" xsi:type="BL" />
        </element>
        <element code="100001271" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Concomitant Procedures Performed">
          <value value="false" xsi:type="BL" />
        </element>
        <element code="100014079" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Arterial Access Site">
          <value code="45631007" codeSystem="2.16.840.1.113883.6.96" displayName="Structure of radial artery" xsi:type="CD" />
        </element>
        <element code="100014075" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Arterial Cross Over">
          <value value="false" xsi:type="BL" />
        </element>
        <element code="112000000349" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Closure Method Not Documented">
          <value value="false" xsi:type="BL" />
        </element>
        <element code="1000142421" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Venous Access">
          <value value="false" xsi:type="BL" />
        </element>
        <element code="8480-6" codeSystem="2.16.840.1.113883.6.1" displayName="Systolic BP">
          <value unit="mm[Hg]" value="124" xsi:type="PQ" />
        </element>
        <element code="100014017" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Cardiac Arrest at this Facility">
          <value value="false" xsi:type="BL" />
        </element>
        <element code="100014077" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Fluoro Time">
          <value unit="min" value="41.3" xsi:type="PQ" />
        </element>
        <element code="80242-1" codeSystem="2.16.840.1.113883.6.1" displayName="Contrast Volume">
          <value unit="mL" value="420" xsi:type="PQ" />
        </element>
        <element code="228850003" codeSystem="2.16.840.1.113883.6.96" displayName="Cumulative Air Kerma">
          <value unit="mGy" value="2469" xsi:type="PQ" />
        </element>
        <element code="100000994" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Dose Area Product">
          <value unit="cGy/cm2" value="19100" xsi:type="PQ" />
        </element>
        <section code="PREPROCINFO" displayName="D. Pre-Procedure Information">
          <element code="84114007" codeSystem="2.16.840.1.113883.6.96" displayName="Heart Failure">
            <value value="false" xsi:type="BL" />
          </element>
          <section code="DIAGNOSTICTEST" displayName="Diagnostic Test">
            <element code="***********" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Electrocardiac Assessment Method">
              <value code="164847006" codeSystem="2.16.840.1.113883.6.96" displayName="ECG Performed" xsi:type="CD" />
            </element>
            <element code="1000142467" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Electrocardiac Assessment Results">
              <value code="263654008" codeSystem="2.16.840.1.113883.6.96" displayName="Abnormal" xsi:type="CD" />
            </element>
            <element code="1000142469" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="New Antiarrhythmic Therapy Initiated Prior to Cath Lab">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="102594003" codeSystem="2.16.840.1.113883.6.96" displayName="Electrocardiac Abnormality Type">
              <value code="***********" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="ST changes &gt;= .5 mm" xsi:type="CD" />
            </element>
            <element code="1000142431" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Stress Test Performed">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="59255-0" codeSystem="2.16.840.1.113883.6.1" displayName="Cardiac CTA Performed">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="450360000" codeSystem="2.16.840.1.113883.6.96" displayName="Agatston Calcium Score Assessed">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="100001027" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="LVEF Assessed">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="***********" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Prior Dx Coronary Angiography Procedure">
              <value value="false" xsi:type="BL" />
            </element>
          <section code="STRESSTEST" displayName="Stress Test">
          <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR"
                   code="1000142432" displayName="StressTestType">
              <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC"
                     code="18107-3" displayName="Stress echocardiogram"/>
          </element>
          <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR"
                   code="1000142431" displayName="StressTestDate">
              <value xsi:type="DT" value="2021-03-19"/>
          </element>
          <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR"
                   code="10001424303" displayName="StressTestResult">
              <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR"
                     code="100013083" displayName="Negative"/>
          </element>
          </section>
          </section>
          <section code="PREPROCMED" displayName="Pre-Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PreProcedure Medication Code">
              <value code="41549009" codeSystem="2.16.840.1.113883.6.96" displayName="Angiotensin Converting Enzyme Inhibitor" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Pre-Procedure Medication Administered">
              <value code="112000000168" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - Not Prescribed" xsi:type="CD" />
            </element>
          </section>
          <section code="PREPROCMED" displayName="Pre-Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PreProcedure Medication Code">
              <value code="1656341" codeSystem="2.16.840.1.113883.6.88" displayName="Sacubitril and Valsartan" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Pre-Procedure Medication Administered">
              <value code="112000000168" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - Not Prescribed" xsi:type="CD" />
            </element>
          </section>
          <section code="PREPROCMED" displayName="Pre-Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PreProcedure Medication Code">
              <value code="35829" codeSystem="2.16.840.1.113883.6.88" displayName="Ranolazine" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Pre-Procedure Medication Administered">
              <value code="112000000168" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - Not Prescribed" xsi:type="CD" />
            </element>
          </section>
          <section code="PREPROCMED" displayName="Pre-Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PreProcedure Medication Code">
              <value code="100014162" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Antiarrhythmic Agent Other" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Pre-Procedure Medication Administered">
              <value code="112000000168" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - Not Prescribed" xsi:type="CD" />
            </element>
          </section>
          <section code="PREPROCMED" displayName="Pre-Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PreProcedure Medication Code">
              <value code="1191" codeSystem="2.16.840.1.113883.6.88" displayName="Aspirin" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Pre-Procedure Medication Administered">
              <value code="100001247" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Yes - Prescribed" xsi:type="CD" />
            </element>
          </section>
          <section code="PREPROCMED" displayName="Pre-Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PreProcedure Medication Code">
              <value code="372913009" codeSystem="2.16.840.1.113883.6.96" displayName="Angiotensin II Receptor Blocker" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Pre-Procedure Medication Administered">
              <value code="112000000168" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - Not Prescribed" xsi:type="CD" />
            </element>
          </section>
          <section code="PREPROCMED" displayName="Pre-Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PreProcedure Medication Code">
              <value code="33252009" codeSystem="2.16.840.1.113883.6.96" displayName="Beta Blocker" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Pre-Procedure Medication Administered">
              <value code="112000000168" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - Not Prescribed" xsi:type="CD" />
            </element>
          </section>
          <section code="PREPROCMED" displayName="Pre-Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PreProcedure Medication Code">
              <value code="48698004" codeSystem="2.16.840.1.113883.6.96" displayName="Calcium Channel Blocking Agent" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Pre-Procedure Medication Administered">
              <value code="112000000168" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - Not Prescribed" xsi:type="CD" />
            </element>
          </section>
          <section code="PREPROCMED" displayName="Pre-Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PreProcedure Medication Code">
              <value code="31970009" codeSystem="2.16.840.1.113883.6.96" displayName="Long Acting Nitrate" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Pre-Procedure Medication Administered">
              <value code="112000000168" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - Not Prescribed" xsi:type="CD" />
            </element>
          </section>
          <section code="PREPROCMED" displayName="Pre-Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PreProcedure Medication Code">
              <value code="100014161" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Non-Statin" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Pre-Procedure Medication Administered">
              <value code="112000000168" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - Not Prescribed" xsi:type="CD" />
            </element>
          </section>
          <section code="PREPROCMED" displayName="Pre-Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PreProcedure Medication Code">
              <value code="112000000694" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Proprotein Convertase Subtilisin Kexin Type 9 Inhibitor" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Pre-Procedure Medication Administered">
              <value code="112000000168" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - Not Prescribed" xsi:type="CD" />
            </element>
          </section>
          <section code="PREPROCMED" displayName="Pre-Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PreProcedure Medication Code">
              <value code="96302009" codeSystem="2.16.840.1.113883.6.96" displayName="Statin" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Pre-Procedure Medication Administered">
              <value code="112000000168" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - Not Prescribed" xsi:type="CD" />
            </element>
          </section>
        </section>
        <section code="CLMETHOD" displayName="Closure Methods">
          <element code="100014083" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Closure Device Counter">
            <value value="1" xsi:type="CTR" />
          </element>
          <element code="100014074" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Arterial Access Closure Method">
            <value code="2" codeSystem="2.16.840.1.113883.3.3478.6.1.100" displayName="Mechanical Compression ()" xsi:type="CD" />
          </element>
        </section>
        <section code="LABS" displayName="F. Labs">
          <section code="PREPROCLABS" displayName="Pre-Procedure Labs">
            <element code="10839-9" codeSystem="2.16.840.1.113883.6.1" displayName="Troponin I (Pre-Procedure)">
              <value unit="ng/mL" value="0.02" xsi:type="PQ" />
            </element>
            <element code="10839-9" codeSystem="2.16.840.1.113883.6.1" displayName="Not Drawn">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="6598-7" codeSystem="2.16.840.1.113883.6.1" displayName="Not Drawn">
              <value value="true" xsi:type="BL" />
            </element>
            <element code="2160-0" codeSystem="2.16.840.1.113883.6.1" displayName="Creatinine">
              <value unit="mg/dL" value="1.04" xsi:type="PQ" />
            </element>
            <element code="2160-0" codeSystem="2.16.840.1.113883.6.1" displayName="Creatinine Not Drawn">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="718-7" codeSystem="2.16.840.1.113883.6.1" displayName="Hemoglobin">
              <value unit="g/dL" value="14" xsi:type="PQ" />
            </element>
            <element code="718-7" codeSystem="2.16.840.1.113883.6.1" displayName="Not Drawn">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="2093-3" codeSystem="2.16.840.1.113883.6.1" displayName="Not Drawn">
              <value value="true" xsi:type="BL" />
            </element>
            <element code="2085-9" codeSystem="2.16.840.1.113883.6.1" displayName="Not Drawn">
              <value value="true" xsi:type="BL" />
            </element>
          </section>
          <section code="POSTPROCLABS" displayName="Post-Procedure Labs">
            <element code="10839-9" codeSystem="2.16.840.1.113883.6.1" displayName="Troponin I (Post-Procedure)">
              <value unit="ng/mL" value="97.14" xsi:type="PQ" />
            </element>
            <element code="10839-9" codeSystem="2.16.840.1.113883.6.1" displayName="Not Drawn">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="6598-7" codeSystem="2.16.840.1.113883.6.1" displayName="Not Drawn">
              <value value="true" xsi:type="BL" />
            </element>
            <element code="2160-0" codeSystem="2.16.840.1.113883.6.1" displayName="Creatinine">
              <value unit="mg/dL" value="0.94" xsi:type="PQ" />
            </element>
            <element code="2160-0" codeSystem="2.16.840.1.113883.6.1" displayName="Not Drawn">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="718-7" codeSystem="2.16.840.1.113883.6.1" displayName="Hemoglobin">
              <value unit="g/dL" value="13.1" xsi:type="PQ" />
            </element>
            <element code="718-7" codeSystem="2.16.840.1.113883.6.1" displayName="Not Drawn">
              <value value="false" xsi:type="BL" />
            </element>
          </section>
        </section>
        <section code="LABVISIT" displayName="G. Cath Lab Visit">
          <element code="100014000" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Indications for Cath Lab Visit">
            <value code="1000142358" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="ACS &lt;= 24 hrs prior to cath lab visit" xsi:type="CD" />
            <value code="233821000" codeSystem="2.16.840.1.113883.6.96" displayName="New Onset Angina" xsi:type="CD" />
            <value code="100014003" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Stable Suspected Coronary Artery Desease" xsi:type="CD" />
          </element>
          <element code="100001274" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Chest Pain Symptom Assessment">
            <value code="429559004" codeSystem="2.16.840.1.113883.6.96" displayName="Typical Angina" xsi:type="CD" />
          </element>
          <element code="100014004" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Cardiovascular Instability">
            <value value="true" xsi:type="BL" />
          </element>
          <element code="100014005" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Cardiovascular Instability Type">
            <value code="100014006" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Persistent Ischemic Symptoms" xsi:type="CD" />
          </element>
          <element code="100001276" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Ventricular Support">
            <value value="false" xsi:type="BL" />
          </element>
        </section>
        <section code="CORANATOMY" displayName="H. Coronary Anatomy">
          <element code="253727002" codeSystem="2.16.840.1.113883.6.96" displayName="Dominance">
            <value code="253728007" codeSystem="2.16.840.1.113883.6.96" displayName="Right dominant coronary system" xsi:type="CD" />
          </element>
          <element code="100001297" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Native Vessel with Stenosis &gt;= 50%">
            <value value="true" xsi:type="BL" />
          </element>
          <section code="NVESSEL" displayName="Native Vessel">
            <element code="100012984" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Native Lesion Segment Number">
              <value code="68787002" codeSystem="2.16.840.1.113883.6.96" displayName="Structure of proximal portion of anterior descending branch of left coronary artery" xsi:type="CD" />
            </element>
            <element code="100012981" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Native Coronary Vessel Stenosis">
              <value unit="%" value="100" xsi:type="PQ" />
            </element>
            <element code="100012979" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Adjunctive  Measurements Obtained">
              <value value="false" xsi:type="BL" />
            </element>
          </section>
          <section code="NVESSEL" displayName="Native Vessel">
            <element code="100012984" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Native Lesion Segment Number">
              <value code="91748002" codeSystem="2.16.840.1.113883.6.96" displayName="Structure of mid portion of anterior descending branch of left coronary artery" xsi:type="CD" />
            </element>
            <element code="100012981" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Native Coronary Vessel Stenosis">
              <value unit="%" value="100" xsi:type="PQ" />
            </element>
            <element code="100012979" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Adjunctive  Measurements Obtained">
              <value value="false" xsi:type="BL" />
            </element>
          </section>
          <section code="NVESSEL" displayName="Native Vessel">
            <element code="100012984" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Native Lesion Segment Number">
              <value code="36672000" codeSystem="2.16.840.1.113883.6.96" displayName="Structure of distal portion of anterior descending branch of left coronary artery" xsi:type="CD" />
            </element>
            <element code="100012981" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Native Coronary Vessel Stenosis">
              <value unit="%" value="100" xsi:type="PQ" />
            </element>
            <element code="100012979" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Adjunctive  Measurements Obtained">
              <value value="false" xsi:type="BL" />
            </element>
          </section>
          <section code="NVESSEL" displayName="Native Vessel">
            <element code="100012984" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Native Lesion Segment Number">
              <value code="91750005" codeSystem="2.16.840.1.113883.6.96" displayName="Structure of first diagonal branch of anterior descending branch of left coronary artery" xsi:type="CD" />
            </element>
            <element code="100012981" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Native Coronary Vessel Stenosis">
              <value unit="%" value="70" xsi:type="PQ" />
            </element>
            <element code="100012979" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Adjunctive  Measurements Obtained">
              <value value="false" xsi:type="BL" />
            </element>
          </section>
        </section>
        <section code="PCIPROC" displayName="I. PCI Procedure">
          <element code="100012986" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PCI Status">
            <value code="100012989" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PCI Emergency Procedure" xsi:type="CD" />
          </element>
          <element code="1000142366" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Decision for PCI with Surgical Consult">
            <value value="false" xsi:type="BL" />
          </element>
          <element code="100013007" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PCI for MultiVessel Disease">
            <value value="false" xsi:type="BL" />
          </element>
          <element code="*********" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Percutaneous Coronary Intervention Indication">
            <value code="100000570" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Primary PCI for Acute STEMI" xsi:type="CD" />
          </element>
          <element code="100013003" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="ACS Symptom Date">
            <value value="2022-02-03" xsi:type="DT" />
          </element>
          <element code="100013004" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="ACS Symptom Time">
            <value value="13:30:00.0000000-04:00" xsi:type="TM" />
          </element>
          <element code="100013004" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Time Unknown">
            <value value="false" xsi:type="BL" />
          </element>
          <element code="100000180" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="STEMI or STEMI Equivalent First Noted">
            <value code="100000578" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="During First ECG" xsi:type="CD" />
          </element>
          <element code="100014084" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Transferred In for Primary PCI for STEMI">
            <value value="false" xsi:type="BL" />
          </element>
          <element code="100012993" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="First Device Activation Date and Time">
            <value value="2022-02-03T14:50:00" xsi:type="TS" />
          </element>
          <element code="100013002" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Patient Centered Reason for Delay in PCI">
            <value value="false" xsi:type="BL" />
          </element>
          <section code="PROCMED" displayName="Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PCI Procedure Medication Code">
              <value code="15202" codeSystem="2.16.840.1.113883.6.88" displayName="Argatroban" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Procedure Medications Administered">
              <value code="100014173" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - not given, reason unspecified" xsi:type="CD" />
            </element>
          </section>
          <section code="PROCMED" displayName="Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PCI Procedure Medication Code">
              <value code="400610005" codeSystem="2.16.840.1.113883.6.96" displayName="Bivalirudin" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Procedure Medications Administered">
              <value code="100014173" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - not given, reason unspecified" xsi:type="CD" />
            </element>
          </section>
          <section code="PROCMED" displayName="Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PCI Procedure Medication Code">
              <value code="321208" codeSystem="2.16.840.1.113883.6.88" displayName="Fondaparinux" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Procedure Medications Administered">
              <value code="100014173" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - not given, reason unspecified" xsi:type="CD" />
            </element>
          </section>
          <section code="PROCMED" displayName="Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PCI Procedure Medication Code">
              <value code="373294004" codeSystem="2.16.840.1.113883.6.96" displayName="Low Molecular Weight Heparin" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Procedure Medications Administered">
              <value code="100014173" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - not given, reason unspecified" xsi:type="CD" />
            </element>
          </section>
          <section code="PROCMED" displayName="Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PCI Procedure Medication Code">
              <value code="96382006" codeSystem="2.16.840.1.113883.6.96" displayName="Unfractionated Heparin" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Procedure Medications Administered">
              <value code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Drug Administered" xsi:type="CD" />
            </element>
          </section>
          <section code="PROCMED" displayName="Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PCI Procedure Medication Code">
              <value code="11289" codeSystem="2.16.840.1.113883.6.88" displayName="Warfarin" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Procedure Medications Administered">
              <value code="100014173" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - not given, reason unspecified" xsi:type="CD" />
            </element>
          </section>
          <section code="PROCMED" displayName="Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PCI Procedure Medication Code">
              <value code="1537034" codeSystem="2.16.840.1.113883.6.88" displayName="Vorapaxar" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Procedure Medications Administered">
              <value code="100014173" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - not given, reason unspecified" xsi:type="CD" />
            </element>
          </section>
          <section code="PROCMED" displayName="Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PCI Procedure Medication Code">
              <value code="**********" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Glycoprotein IIb IIIa Inhibitors" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Procedure Medications Administered">
              <value code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Drug Administered" xsi:type="CD" />
            </element>
          </section>
          <section code="PROCMED" displayName="Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PCI Procedure Medication Code">
              <value code="1364430" codeSystem="2.16.840.1.113883.6.88" displayName="Apixaban" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Procedure Medications Administered">
              <value code="100014173" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - not given, reason unspecified" xsi:type="CD" />
            </element>
          </section>
          <section code="PROCMED" displayName="Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PCI Procedure Medication Code">
              <value code="1546356" codeSystem="2.16.840.1.113883.6.88" displayName="Dabigatran" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Procedure Medications Administered">
              <value code="100014173" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - not given, reason unspecified" xsi:type="CD" />
            </element>
          </section>
          <section code="PROCMED" displayName="Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PCI Procedure Medication Code">
              <value code="1599538" codeSystem="2.16.840.1.113883.6.88" displayName="Edoxaban" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Procedure Medications Administered">
              <value code="100014173" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - not given, reason unspecified" xsi:type="CD" />
            </element>
          </section>
          <section code="PROCMED" displayName="Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PCI Procedure Medication Code">
              <value code="1114195" codeSystem="2.16.840.1.113883.6.88" displayName="Rivaroxaban" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Procedure Medications Administered">
              <value code="100014173" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - not given, reason unspecified" xsi:type="CD" />
            </element>
          </section>
          <section code="PROCMED" displayName="Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PCI Procedure Medication Code">
              <value code="1656052" codeSystem="2.16.840.1.113883.6.88" displayName="Cangrelor" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Procedure Medications Administered">
              <value code="100014173" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - not given, reason unspecified" xsi:type="CD" />
            </element>
          </section>
          <section code="PROCMED" displayName="Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PCI Procedure Medication Code">
              <value code="32968" codeSystem="2.16.840.1.113883.6.88" displayName="Clopidogrel" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Procedure Medications Administered">
              <value code="100014173" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - not given, reason unspecified" xsi:type="CD" />
            </element>
          </section>
          <section code="PROCMED" displayName="Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PCI Procedure Medication Code">
              <value code="613391" codeSystem="2.16.840.1.113883.6.88" displayName="Prasugrel" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Procedure Medications Administered">
              <value code="100014173" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - not given, reason unspecified" xsi:type="CD" />
            </element>
          </section>
          <section code="PROCMED" displayName="Procedure Medications">
            <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="PCI Procedure Medication Code">
              <value code="1116632" codeSystem="2.16.840.1.113883.6.88" displayName="Ticagrelor" xsi:type="CD" />
            </element>
            <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Procedure Medications Administered">
              <value code="100014173" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - not given, reason unspecified" xsi:type="CD" />
            </element>
          </section>
          <section code="LESIONDEV" displayName="J. Lesions and Devices">
            <element code="1000142441" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Lesion Counter">
              <value value="1" xsi:type="CTR" />
            </element>
            <element code="100012984" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Native Lesion Segment Number">
              <value code="68787002" codeSystem="2.16.840.1.113883.6.96" displayName="Structure of proximal portion of anterior descending branch of left coronary artery" xsi:type="CD" />
            </element>
            <element code="112000000347" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Culprit Stenosis Unknown">
              <value value="true" xsi:type="BL" />
            </element>
            <element code="1000142442" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Stenosis Immediately Prior to Treatment">
              <value unit="%" value="100" xsi:type="PQ" />
            </element>
            <element code="100000290" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Chronic Total Occlusion">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="112000000345" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Chronic Total Occlusion Unknown">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="112000000348" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="TIMI Flow (Pre-Intervention)">
              <value code="371867000" codeSystem="2.16.840.1.113883.6.96" displayName="Thrombolysis in Myocardial Infarction grade 0: no perfusion" xsi:type="CD" />
            </element>
            <element code="100013015" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Previously Treated Lesion">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="1000142443" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Lesion In Graft">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="1000142348" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Navigate through Graft to Native Lesion">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="100000866" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Lesion Complexity">
              <value code="100000584" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="High/C Lesion (Dupe)" xsi:type="CD" />
            </element>
            <element code="100013030" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Lesion Length">
              <value unit="mm" value="20" xsi:type="PQ" />
            </element>
            <element code="1000142350" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Severe Calcification">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="371894001" codeSystem="2.16.840.1.113883.6.96" displayName="Bifurcation Lesion">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="100000851" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Guidewire Across Lesion">
              <value value="true" xsi:type="BL" />
            </element>
            <element code="1000142349" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Device Deployed">
              <value value="true" xsi:type="BL" />
            </element>
            <element code="1000142461" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Stenosis (Post-Intervention)">
              <value unit="%" value="0" xsi:type="PQ" />
            </element>
            <element code="100013016" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="TIMI Flow (Post-Intervention)">
              <value code="371865008" codeSystem="2.16.840.1.113883.6.96" displayName="Thrombolysis in Myocardial Infarction grade 3: complete perfusion" xsi:type="CD" />
            </element>
          </section>
          <section code="LESIONDEV" displayName="J. Lesions and Devices">
            <element code="1000142441" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Lesion Counter">
              <value value="2" xsi:type="CTR" />
            </element>
            <element code="100012984" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Native Lesion Segment Number">
              <value code="91748002" codeSystem="2.16.840.1.113883.6.96" displayName="Structure of mid portion of anterior descending branch of left coronary artery" xsi:type="CD" />
              <value code="36672000" codeSystem="2.16.840.1.113883.6.96" displayName="Structure of distal portion of anterior descending branch of left coronary artery" xsi:type="CD" />
            </element>
            <element code="112000000347" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Culprit Stenosis Unknown">
              <value value="true" xsi:type="BL" />
            </element>
            <element code="1000142442" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Stenosis Immediately Prior to Treatment">
              <value unit="%" value="100" xsi:type="PQ" />
            </element>
            <element code="100000290" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Chronic Total Occlusion">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="112000000345" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Chronic Total Occlusion Unknown">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="112000000348" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="TIMI Flow (Pre-Intervention)">
              <value code="371867000" codeSystem="2.16.840.1.113883.6.96" displayName="Thrombolysis in Myocardial Infarction grade 0: no perfusion" xsi:type="CD" />
            </element>
            <element code="100013015" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Previously Treated Lesion">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="1000142443" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Lesion In Graft">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="1000142348" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Navigate through Graft to Native Lesion">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="100000866" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Lesion Complexity">
              <value code="100000584" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="High/C Lesion (Dupe)" xsi:type="CD" />
            </element>
            <element code="100013030" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Lesion Length">
              <value unit="mm" value="62" xsi:type="PQ" />
            </element>
            <element code="1000142350" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Severe Calcification">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="371894001" codeSystem="2.16.840.1.113883.6.96" displayName="Bifurcation Lesion">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="100000851" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Guidewire Across Lesion">
              <value value="true" xsi:type="BL" />
            </element>
            <element code="1000142349" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Device Deployed">
              <value value="true" xsi:type="BL" />
            </element>
            <element code="1000142461" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Stenosis (Post-Intervention)">
              <value unit="%" value="0" xsi:type="PQ" />
            </element>
            <element code="100013016" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="TIMI Flow (Post-Intervention)">
              <value code="371865008" codeSystem="2.16.840.1.113883.6.96" displayName="Thrombolysis in Myocardial Infarction grade 3: complete perfusion" xsi:type="CD" />
            </element>
          </section>
          <section code="LESIONDEV" displayName="J. Lesions and Devices">
            <element code="1000142441" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Lesion Counter">
              <value value="3" xsi:type="CTR" />
            </element>
            <element code="100012984" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Native Lesion Segment Number">
              <value code="91750005" codeSystem="2.16.840.1.113883.6.96" displayName="Structure of first diagonal branch of anterior descending branch of left coronary artery" xsi:type="CD" />
            </element>
            <element code="112000000347" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Culprit Stenosis Unknown">
              <value value="true" xsi:type="BL" />
            </element>
            <element code="1000142442" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Stenosis Immediately Prior to Treatment">
              <value unit="%" value="70" xsi:type="PQ" />
            </element>
            <element code="112000000348" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="TIMI Flow (Pre-Intervention)">
              <value code="371866009" codeSystem="2.16.840.1.113883.6.96" displayName="Thrombolysis in Myocardial Infarction grade 1: penetration without perfusion" xsi:type="CD" />
            </element>
            <element code="100013015" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Previously Treated Lesion">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="1000142443" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Lesion In Graft">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="1000142348" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Navigate through Graft to Native Lesion">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="100000866" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Lesion Complexity">
              <value code="100000583" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Non-High/Non-C Lesion" xsi:type="CD" />
            </element>
            <element code="100013030" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Lesion Length">
              <value unit="mm" value="12" xsi:type="PQ" />
            </element>
            <element code="1000142350" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Severe Calcification">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="371894001" codeSystem="2.16.840.1.113883.6.96" displayName="Bifurcation Lesion">
              <value value="false" xsi:type="BL" />
            </element>
            <element code="100000851" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Guidewire Across Lesion">
              <value value="true" xsi:type="BL" />
            </element>
            <element code="1000142349" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Device Deployed">
              <value value="true" xsi:type="BL" />
            </element>
            <element code="1000142461" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Stenosis (Post-Intervention)">
              <value unit="%" value="0" xsi:type="PQ" />
            </element>
            <element code="100013016" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="TIMI Flow (Post-Intervention)">
              <value code="371865008" codeSystem="2.16.840.1.113883.6.96" displayName="Thrombolysis in Myocardial Infarction grade 3: complete perfusion" xsi:type="CD" />
            </element>
          </section>
          <section code="DEVICES" displayName="Devices">
            <element code="2.16.840.1.113883.3.3478.4.851" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Counter">
              <value value="1" xsi:type="CTR" />
            </element>
            <element code="1000142374" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device(s) Used">
              <value code="227" codeSystem="2.16.840.1.113883.3.3478.6.1.101" displayName="Emerge PTCA Dilatation Catheter Monorail ()" xsi:type="CD" />
            </element>
            <element code="1000142398" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Associated Lesion">
              <value value="1" xsi:type="NUM" />
              <value value="2" xsi:type="NUM" />
              <value value="3" xsi:type="NUM" />
            </element>
            <element code="1000142375" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Diameter">
              <value unit="mm" value="2.5" xsi:type="PQ" />
            </element>
            <element code="1000142376" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Length">
              <value unit="mm" value="12" xsi:type="PQ" />
            </element>
          </section>
          <section code="DEVICES" displayName="Devices">
            <element code="2.16.840.1.113883.3.3478.4.851" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Counter">
              <value value="2" xsi:type="CTR" />
            </element>
            <element code="1000142374" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device(s) Used">
              <value code="4591" codeSystem="2.16.840.1.113883.3.3478.6.1.101" displayName="Synergy XD Stent ()" xsi:type="CD" />
            </element>
            <element code="1000142398" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Associated Lesion">
              <value value="1" xsi:type="NUM" />
            </element>
            <element code="1000142375" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Diameter">
              <value unit="mm" value="3.5" xsi:type="PQ" />
            </element>
            <element code="1000142376" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Length">
              <value unit="mm" value="20" xsi:type="PQ" />
            </element>
          </section>
          <section code="DEVICES" displayName="Devices">
            <element code="2.16.840.1.113883.3.3478.4.851" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Counter">
              <value value="3" xsi:type="CTR" />
            </element>
            <element code="1000142374" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device(s) Used">
              <value code="227" codeSystem="2.16.840.1.113883.3.3478.6.1.101" displayName="Emerge PTCA Dilatation Catheter Monorail ()" xsi:type="CD" />
            </element>
            <element code="1000142398" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Associated Lesion">
              <value value="2" xsi:type="NUM" />
            </element>
            <element code="1000142375" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Diameter">
              <value unit="mm" value="2.5" xsi:type="PQ" />
            </element>
            <element code="1000142376" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Length">
              <value unit="mm" value="20" xsi:type="PQ" />
            </element>
          </section>
          <section code="DEVICES" displayName="Devices">
            <element code="2.16.840.1.113883.3.3478.4.851" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Counter">
              <value value="4" xsi:type="CTR" />
            </element>
            <element code="1000142374" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device(s) Used">
              <value code="227" codeSystem="2.16.840.1.113883.3.3478.6.1.101" displayName="Emerge PTCA Dilatation Catheter Monorail ()" xsi:type="CD" />
            </element>
            <element code="1000142398" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Associated Lesion">
              <value value="3" xsi:type="NUM" />
            </element>
            <element code="1000142375" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Diameter">
              <value unit="mm" value="2" xsi:type="PQ" />
            </element>
            <element code="1000142376" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Length">
              <value unit="mm" value="12" xsi:type="PQ" />
            </element>
          </section>
          <section code="DEVICES" displayName="Devices">
            <element code="2.16.840.1.113883.3.3478.4.851" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Counter">
              <value value="5" xsi:type="CTR" />
            </element>
            <element code="1000142374" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device(s) Used">
              <value code="4591" codeSystem="2.16.840.1.113883.3.3478.6.1.101" displayName="Synergy XD Stent ()" xsi:type="CD" />
            </element>
            <element code="1000142398" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Associated Lesion">
              <value value="2" xsi:type="NUM" />
            </element>
            <element code="1000142375" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Diameter">
              <value unit="mm" value="2.5" xsi:type="PQ" />
            </element>
            <element code="1000142376" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Length">
              <value unit="mm" value="48" xsi:type="PQ" />
            </element>
          </section>
          <section code="DEVICES" displayName="Devices">
            <element code="2.16.840.1.113883.3.3478.4.851" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Counter">
              <value value="6" xsi:type="CTR" />
            </element>
            <element code="1000142374" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device(s) Used">
              <value code="4591" codeSystem="2.16.840.1.113883.3.3478.6.1.101" displayName="Synergy XD Stent ()" xsi:type="CD" />
            </element>
            <element code="1000142398" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Associated Lesion">
              <value value="2" xsi:type="NUM" />
            </element>
            <element code="1000142375" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Diameter">
              <value unit="mm" value="2.5" xsi:type="PQ" />
            </element>
            <element code="1000142376" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Length">
              <value unit="mm" value="16" xsi:type="PQ" />
            </element>
          </section>
          <section code="DEVICES" displayName="Devices">
            <element code="2.16.840.1.113883.3.3478.4.851" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Counter">
              <value value="7" xsi:type="CTR" />
            </element>
            <element code="1000142374" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device(s) Used">
              <value code="262" codeSystem="2.16.840.1.113883.3.3478.6.1.101" displayName="NC Emerge RX ()" xsi:type="CD" />
            </element>
            <element code="1000142398" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Associated Lesion">
              <value value="2" xsi:type="NUM" />
            </element>
            <element code="1000142375" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Diameter">
              <value unit="mm" value="3" xsi:type="PQ" />
            </element>
            <element code="1000142376" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Length">
              <value unit="mm" value="20" xsi:type="PQ" />
            </element>
          </section>
          <section code="DEVICES" displayName="Devices">
            <element code="2.16.840.1.113883.3.3478.4.851" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Counter">
              <value value="8" xsi:type="CTR" />
            </element>
            <element code="1000142374" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device(s) Used">
              <value code="262" codeSystem="2.16.840.1.113883.3.3478.6.1.101" displayName="NC Emerge RX ()" xsi:type="CD" />
            </element>
            <element code="1000142398" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Associated Lesion">
              <value value="1" xsi:type="NUM" />
              <value value="2" xsi:type="NUM" />
            </element>
            <element code="1000142375" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Diameter">
              <value unit="mm" value="4" xsi:type="PQ" />
            </element>
            <element code="1000142376" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Length">
              <value unit="mm" value="20" xsi:type="PQ" />
            </element>
          </section>
          <section code="DEVICES" displayName="Devices">
            <element code="2.16.840.1.113883.3.3478.4.851" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Counter">
              <value value="9" xsi:type="CTR" />
            </element>
            <element code="1000142374" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device(s) Used">
              <value code="262" codeSystem="2.16.840.1.113883.3.3478.6.1.101" displayName="NC Emerge RX ()" xsi:type="CD" />
            </element>
            <element code="1000142398" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Associated Lesion">
              <value value="1" xsi:type="NUM" />
            </element>
            <element code="1000142375" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Diameter">
              <value unit="mm" value="4.5" xsi:type="PQ" />
            </element>
            <element code="1000142376" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Length">
              <value unit="mm" value="8" xsi:type="PQ" />
            </element>
          </section>
          <section code="DEVICES" displayName="Devices">
            <element code="2.16.840.1.113883.3.3478.4.851" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Counter">
              <value value="10" xsi:type="CTR" />
            </element>
            <element code="1000142374" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device(s) Used">
              <value code="262" codeSystem="2.16.840.1.113883.3.3478.6.1.101" displayName="NC Emerge RX ()" xsi:type="CD" />
            </element>
            <element code="1000142398" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Associated Lesion">
              <value value="1" xsi:type="NUM" />
            </element>
            <element code="1000142375" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Diameter">
              <value unit="mm" value="4" xsi:type="PQ" />
            </element>
            <element code="1000142376" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Length">
              <value unit="mm" value="20" xsi:type="PQ" />
            </element>
          </section>
          <section code="DEVICES" displayName="Devices">
            <element code="2.16.840.1.113883.3.3478.4.851" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Counter">
              <value value="11" xsi:type="CTR" />
            </element>
            <element code="1000142374" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device(s) Used">
              <value code="961" codeSystem="2.16.840.1.113883.3.3478.6.1.101" displayName="Thrombectomy ()" xsi:type="CD" />
            </element>
            <element code="1000142398" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Associated Lesion">
              <value value="3" xsi:type="NUM" />
            </element>
          </section>
          <section code="DEVICES" displayName="Devices">
            <element code="2.16.840.1.113883.3.3478.4.851" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Counter">
              <value value="12" xsi:type="CTR" />
            </element>
            <element code="1000142374" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device(s) Used">
              <value code="262" codeSystem="2.16.840.1.113883.3.3478.6.1.101" displayName="NC Emerge RX ()" xsi:type="CD" />
            </element>
            <element code="1000142398" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Associated Lesion">
              <value value="1" xsi:type="NUM" />
            </element>
            <element code="1000142375" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Diameter">
              <value unit="mm" value="5" xsi:type="PQ" />
            </element>
            <element code="1000142376" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Length">
              <value unit="mm" value="12" xsi:type="PQ" />
            </element>
          </section>
          <section code="DEVICES" displayName="Devices">
            <element code="2.16.840.1.113883.3.3478.4.851" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Counter">
              <value value="13" xsi:type="CTR" />
            </element>
            <element code="1000142374" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device(s) Used">
              <value code="262" codeSystem="2.16.840.1.113883.3.3478.6.1.101" displayName="NC Emerge RX ()" xsi:type="CD" />
            </element>
            <element code="1000142398" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Associated Lesion">
              <value value="2" xsi:type="NUM" />
            </element>
            <element code="1000142375" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Diameter">
              <value unit="mm" value="4.5" xsi:type="PQ" />
            </element>
            <element code="1000142376" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intracoronary Device Length">
              <value unit="mm" value="20" xsi:type="PQ" />
            </element>
          </section>
        </section>
        <section code="INTPOSTEVENT" displayName="K. Intra and Post-Procedure Events">
          <element code="234010000" codeSystem="2.16.840.1.113883.6.96" displayName="Coronary Artery Perforation">
            <value value="false" xsi:type="BL" />
          </element>
          <element code="100000883" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Significant Coronary Artery Dissection">
            <value value="false" xsi:type="BL" />
          </element>
          <element code="71493000" codeSystem="2.16.840.1.113883.6.96" displayName="PRBCs Transfused">
            <value value="false" xsi:type="BL" />
          </element>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events">
            <element code="1000142478" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events">
              <value code="1000142440" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Bleeding at Access Site" xsi:type="CD" />
            </element>
            <element code="1000142479" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events Occurred">
              <value value="false" xsi:type="BL" />
            </element>
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events">
            <element code="1000142478" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events">
              <value code="74474003" codeSystem="2.16.840.1.113883.6.96" displayName="Gastrointestinal Bleeding" xsi:type="CD" />
            </element>
            <element code="1000142479" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events Occurred">
              <value value="true" xsi:type="BL" />
            </element>
            <element code="10001424780" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Event Date and Time">
              <value value="2021-12-29T17:07:00" xsi:type="TS" />
            </element>
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events">
            <element code="1000142478" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events">
              <value code="417941003" codeSystem="2.16.840.1.113883.6.96" displayName="Genitourinary (GU) Bleed" xsi:type="CD" />
            </element>
            <element code="1000142479" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events Occurred">
              <value value="false" xsi:type="BL" />
            </element>
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events">
            <element code="1000142478" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events">
              <value code="1000142371" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Other Bleed" xsi:type="CD" />
            </element>
            <element code="1000142479" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events Occurred">
              <value value="false" xsi:type="BL" />
            </element>
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events">
            <element code="1000142478" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events">
              <value code="95549001" codeSystem="2.16.840.1.113883.6.96" displayName="Retroperitoneal Bleed" xsi:type="CD" />
            </element>
            <element code="1000142479" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events Occurred">
              <value value="true" xsi:type="BL" />
            </element>
            <element code="10001424780" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Event Date and Time">
              <value value="2021-12-29T17:10:00" xsi:type="TS" />
            </element>
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events">
            <element code="1000142478" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events">
              <value code="95549001" codeSystem="2.16.840.1.113883.6.96" displayName="Retroperitoneal Bleed" xsi:type="CD" />
            </element>
            <element code="1000142479" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events Occurred">
              <value value="true" xsi:type="BL" />
            </element>
            <element code="10001424780" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Event Date and Time">
              <value value="2021-12-29T19:10:00" xsi:type="TS" />
            </element>
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events">
            <element code="1000142478" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events">
              <value code="410429000" codeSystem="2.16.840.1.113883.6.96" displayName="Cardiac Arrest" xsi:type="CD" />
            </element>
            <element code="1000142479" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events Occurred">
              <value value="false" xsi:type="BL" />
            </element>
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events">
            <element code="1000142478" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events">
              <value code="89138009" codeSystem="2.16.840.1.113883.6.96" displayName="Cardiogenic Shock" xsi:type="CD" />
            </element>
            <element code="1000142479" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events Occurred">
              <value value="false" xsi:type="BL" />
            </element>
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events">
            <element code="1000142478" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events">
              <value code="84114007" codeSystem="2.16.840.1.113883.6.96" displayName="Heart Failure" xsi:type="CD" />
            </element>
            <element code="1000142479" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events Occurred">
              <value value="false" xsi:type="BL" />
            </element>
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events">
            <element code="1000142478" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events">
              <value code="22298006" codeSystem="2.16.840.1.113883.6.96" displayName="Myocardial Infarction" xsi:type="CD" />
            </element>
            <element code="1000142479" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events Occurred">
              <value value="false" xsi:type="BL" />
            </element>
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events">
            <element code="1000142478" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events">
              <value code="100014076" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="New Requirement for Dialysis" xsi:type="CD" />
            </element>
            <element code="1000142479" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events Occurred">
              <value value="false" xsi:type="BL" />
            </element>
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events">
            <element code="1000142478" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events">
              <value code="230706003" codeSystem="2.16.840.1.113883.6.96" displayName="Hemorrhagic Stroke" xsi:type="CD" />
            </element>
            <element code="1000142479" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events Occurred">
              <value value="false" xsi:type="BL" />
            </element>
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events">
            <element code="1000142478" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events">
              <value code="422504002" codeSystem="2.16.840.1.113883.6.96" displayName="Ischemic Stroke" xsi:type="CD" />
            </element>
            <element code="1000142479" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events Occurred">
              <value value="false" xsi:type="BL" />
            </element>
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events">
            <element code="1000142478" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events">
              <value code="230713003" codeSystem="2.16.840.1.113883.6.96" displayName="Undetermined Stroke" xsi:type="CD" />
            </element>
            <element code="1000142479" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events Occurred">
              <value value="false" xsi:type="BL" />
            </element>
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events">
            <element code="1000142478" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events">
              <value code="385494008" codeSystem="2.16.840.1.113883.6.96" displayName="Hematoma at Access Site" xsi:type="CD" />
            </element>
            <element code="1000142479" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events Occurred">
              <value value="false" xsi:type="BL" />
            </element>
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events">
            <element code="1000142478" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events">
              <value code="35304003" codeSystem="2.16.840.1.113883.6.96" displayName="Cardiac Tamponade" xsi:type="CD" />
            </element>
            <element code="1000142479" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events Occurred">
              <value value="false" xsi:type="BL" />
            </element>
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events">
            <element code="1000142478" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events">
              <value code="1000142419" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Other Vascular Complications Requiring Treatment" xsi:type="CD" />
            </element>
            <element code="1000142479" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Intra/Post-Procedure Events Occurred">
              <value value="false" xsi:type="BL" />
            </element>
          </section>
        </section>
      </section>
      <section code="DISCHARGE" displayName="L. Discharge">
        <element code="100001283" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Interventions this Hospitalization">
          <value value="false" xsi:type="BL" />
        </element>
        <element code="2160-0" codeSystem="2.16.840.1.113883.6.1" displayName="Creatinine">
          <value unit="mg/dL" value="0.93" xsi:type="PQ" />
        </element>
        <element code="2160-0" codeSystem="2.16.840.1.113883.6.1" displayName="Creatinine Not Drawn">
          <value value="false" xsi:type="BL" />
        </element>
        <element code="718-7" codeSystem="2.16.840.1.113883.6.1" displayName="Hemoglobin">
          <value unit="g/dL" value="13.1" xsi:type="PQ" />
        </element>
        <element code="718-7" codeSystem="2.16.840.1.113883.6.1" displayName="Hemoglobin Not Drawn">
          <value value="false" xsi:type="BL" />
        </element>
        <element code="1000142457" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Discharge Date and Time">
          <value value="2023-09-15T08:45:00" xsi:type="TS" />
        </element>
        <element code="1000142453" codeSystem="2.16.840.1.113883.3.3478.6.1">
          <value value="" xsi:type="NUM" />
        </element>
        <element code="1000142453" codeSystem="2.16.840.1.113883.3.3478.6.1">
          <value value="" xsi:type="LN" />
        </element>
        <element code="1000142453" codeSystem="2.16.840.1.113883.3.3478.6.1">
          <value value="" xsi:type="FN" />
        </element>
        <element code="133918004" codeSystem="2.16.840.1.113883.6.96" displayName="Comfort Measures Only">
          <value value="false" xsi:type="BL" />
        </element>
        <element code="75527-2" codeSystem="2.16.840.1.113883.6.1" displayName="Discharge Status">
          <value code="438949009" codeSystem="2.16.840.1.113883.6.96" displayName="Alive" xsi:type="CD" />
        </element>
        <element code="75528-0" codeSystem="2.16.840.1.113883.6.1" displayName="Discharge Location">
          <value code="01" codeSystem="2.16.840.1.113883.12.112" displayName="Home" xsi:type="CD" />
        </element>
        <element code="10001424792" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="CABG Planned after Discharge">
          <value value="false" xsi:type="BL" />
        </element>
        <element code="385763009" codeSystem="2.16.840.1.113883.6.96" displayName="Hospice Care">
          <value value="false" xsi:type="BL" />
        </element>
        <element code="100014067" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Cardiac Rehabilitation Referral">
          <value code="100014065" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="No - Heatlh Care System Reason Documented" xsi:type="CD" />
        </element>
        <element code="100013084" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Discharge Medication Reconciliation Completed">
          <value value="true" xsi:type="BL" />
        </element>
        <element code="100013085" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Discharge Medications Reconciled">
          <value code="100013086" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Prescriptions: Cardiac" xsi:type="CD" />
          <value code="100013087" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Prescriptions: Non-Cardiac" xsi:type="CD" />
          <value code="100013088" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Non-Prescription (OTC) Medications" xsi:type="CD" />
        </element>
        <section code="DISCHMED" displayName="Discharge Medications">
          <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Discharge Medication Code">
            <value code="41549009" codeSystem="2.16.840.1.113883.6.96" displayName="Angiotensin Converting Enzyme Inhibitor" xsi:type="CD" />
          </element>
          <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Discharge Medications">
            <value code="100001048" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Not Prescribed - No Reason" xsi:type="CD" />
          </element>
        </section>
        <section code="DISCHMED" displayName="Discharge Medications">
          <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Discharge Medication Code">
            <value code="11289" codeSystem="2.16.840.1.113883.6.88" displayName="Warfarin" xsi:type="CD" />
          </element>
          <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Discharge Medications">
            <value code="100001048" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Not Prescribed - No Reason" xsi:type="CD" />
          </element>
        </section>
        <section code="DISCHMED" displayName="Discharge Medications">
          <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Discharge Medication Code">
            <value code="1191" codeSystem="2.16.840.1.113883.6.88" displayName="Aspirin" xsi:type="CD" />
          </element>
          <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Discharge Medications">
            <value code="100001247" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Yes - Prescribed" xsi:type="CD" />
          </element>
        </section>
        <section code="DISCHMED" displayName="Discharge Medications">
          <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Discharge Medication Code">
            <value code="1537034" codeSystem="2.16.840.1.113883.6.88" displayName="Vorapaxar" xsi:type="CD" />
          </element>
          <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Discharge Medications">
            <value code="100001048" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Not Prescribed - No Reason" xsi:type="CD" />
          </element>
        </section>
        <section code="DISCHMED" displayName="Discharge Medications">
          <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Discharge Medication Code">
            <value code="372913009" codeSystem="2.16.840.1.113883.6.96" displayName="Angiotensin II Receptor Blocker" xsi:type="CD" />
          </element>
          <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Discharge Medications">
            <value code="100001247" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Yes - Prescribed" xsi:type="CD" />
          </element>
        </section>
        <section code="DISCHMED" displayName="Discharge Medications">
          <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Discharge Medication Code">
            <value code="33252009" codeSystem="2.16.840.1.113883.6.96" displayName="Beta Blocker" xsi:type="CD" />
          </element>
          <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Discharge Medications">
            <value code="100001247" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Yes - Prescribed" xsi:type="CD" />
          </element>
        </section>
        <section code="DISCHMED" displayName="Discharge Medications">
          <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Discharge Medication Code">
            <value code="100014161" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Non-Statin" xsi:type="CD" />
          </element>
          <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Discharge Medications">
            <value code="100001048" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Not Prescribed - No Reason" xsi:type="CD" />
          </element>
        </section>
        <section code="DISCHMED" displayName="Discharge Medications">
          <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Discharge Medication Code">
            <value code="1364430" codeSystem="2.16.840.1.113883.6.88" displayName="Apixaban" xsi:type="CD" />
          </element>
          <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Discharge Medications">
            <value code="100001048" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Not Prescribed - No Reason" xsi:type="CD" />
          </element>
        </section>
        <section code="DISCHMED" displayName="Discharge Medications">
          <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Discharge Medication Code">
            <value code="1546356" codeSystem="2.16.840.1.113883.6.88" displayName="Dabigatran" xsi:type="CD" />
          </element>
          <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Discharge Medications">
            <value code="100001048" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Not Prescribed - No Reason" xsi:type="CD" />
          </element>
        </section>
        <section code="DISCHMED" displayName="Discharge Medications">
          <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Discharge Medication Code">
            <value code="1599538" codeSystem="2.16.840.1.113883.6.88" displayName="Edoxaban" xsi:type="CD" />
          </element>
          <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Discharge Medications">
            <value code="100001048" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Not Prescribed - No Reason" xsi:type="CD" />
          </element>
        </section>
        <section code="DISCHMED" displayName="Discharge Medications">
          <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Discharge Medication Code">
            <value code="1114195" codeSystem="2.16.840.1.113883.6.88" displayName="Rivaroxaban" xsi:type="CD" />
          </element>
          <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Discharge Medications">
            <value code="100001048" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Not Prescribed - No Reason" xsi:type="CD" />
          </element>
        </section>
        <section code="DISCHMED" displayName="Discharge Medications">
          <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Discharge Medication Code">
            <value code="32968" codeSystem="2.16.840.1.113883.6.88" displayName="Clopidogrel" xsi:type="CD" />
          </element>
          <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Discharge Medications">
            <value code="100001247" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Yes - Prescribed" xsi:type="CD" />
          </element>
        </section>
        <section code="DISCHMED" displayName="Discharge Medications">
          <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Discharge Medication Code">
            <value code="613391" codeSystem="2.16.840.1.113883.6.88" displayName="Prasugrel" xsi:type="CD" />
          </element>
          <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Discharge Medications">
            <value code="100001048" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Not Prescribed - No Reason" xsi:type="CD" />
          </element>
        </section>
        <section code="DISCHMED" displayName="Discharge Medications">
          <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Discharge Medication Code">
            <value code="1116632" codeSystem="2.16.840.1.113883.6.88" displayName="Ticagrelor" xsi:type="CD" />
          </element>
          <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Discharge Medications">
            <value code="100001048" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Not Prescribed - No Reason" xsi:type="CD" />
          </element>
        </section>
        <section code="DISCHMED" displayName="Discharge Medications">
          <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Discharge Medication Code">
            <value code="10594" codeSystem="2.16.840.1.113883.6.88" displayName="Ticlopidine" xsi:type="CD" />
          </element>
          <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Discharge Medications">
            <value code="100001048" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Not Prescribed - No Reason" xsi:type="CD" />
          </element>
        </section>
        <section code="DISCHMED" displayName="Discharge Medications">
          <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Discharge Medication Code">
            <value code="1659152" codeSystem="2.16.840.1.113883.6.88" displayName="Alirocumab" xsi:type="CD" />
          </element>
          <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Discharge Medications">
            <value code="100001048" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Not Prescribed - No Reason" xsi:type="CD" />
          </element>
        </section>
        <section code="DISCHMED" displayName="Discharge Medications">
          <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Discharge Medication Code">
            <value code="1665684" codeSystem="2.16.840.1.113883.6.88" displayName="Evolocumab" xsi:type="CD" />
          </element>
          <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Discharge Medications">
            <value code="100001048" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Not Prescribed - No Reason" xsi:type="CD" />
          </element>
        </section>
        <section code="DISCHMED" displayName="Discharge Medications">
          <element code="100013057" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Discharge Medication Code">
            <value code="96302009" codeSystem="2.16.840.1.113883.6.96" displayName="Statin" xsi:type="CD" />
          </element>
          <element code="432102000" codeSystem="2.16.840.1.113883.6.96" displayName="Discharge Medications">
            <value code="100001247" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Yes - Prescribed" xsi:type="CD" />
          </element>
          <element code="100014233" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="Discharge Medication Dose">
            <value code="100014034" codeSystem="2.16.840.1.113883.3.3478.6.1" displayName="High Dose" xsi:type="CD" />
          </element>
        </section>
      </section>
    </episode>
  </patient>
  <patient ncdrPatientId="20000">
        <section code="DEMOGRAPHICS" displayName="A. DEMOGRAPHICS">
        <!--demo a-->
<!--Demographics-->
    <!-- #2045 : OtherID : Other ID (Demographics) -->
  <!-- is NULL -->
    <!-- #2050 : DOB : Birth Date (Demographics) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="**********" displayName="DOB">
    <value xsi:type="DT" value="1959-10-21"/>
  </element>
    <!-- #2060 : Sex : Sex (Demographics) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="**********" displayName="Sex">
    <value codeSystem="2.16.840.1.113883.5.1" codeSystemName="HL7 Administrative Gender" displayName="Female" xsi:type="CD" code="F"/>
  </element>
    <!-- #2070 : RaceWhite : White (Demographics) -->
  <element codeSystem="2.16.840.1.113883.5.104" codeSystemName="HL7 Race" code="2106-3" displayName="RaceWhite">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #2071 : RaceBlack : Black/African American (Demographics) -->
  <element codeSystem="2.16.840.1.113883.5.104" codeSystemName="HL7 Race" code="2054-5" displayName="RaceBlack">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #2073 : RaceAmIndian : American Indian/Alaskan Native (Demographics) -->
  <element codeSystem="2.16.840.1.113883.5.104" codeSystemName="HL7 Race" code="1002-5" displayName="RaceAmIndian">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #2072 : RaceAsian : Asian (Demographics) -->
  <element codeSystem="2.16.840.1.113883.5.104" codeSystemName="HL7 Race" code="2028-9" displayName="RaceAsian">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #2074 : RaceNatHaw : Native Hawaiian/Pacific Islander: (Demographics) -->
  <element codeSystem="2.16.840.1.113883.5.104" codeSystemName="HL7 Race" code="2076-8" displayName="RaceNatHaw">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #2076 : HispOrig : Hispanic or Latino Ethnicity (Demographics) -->
  <element codeSystem="2.16.840.1.113883.5.50" codeSystemName="HL7 Ethnicity" code="2135-2" displayName="HispOrig">
    <value xsi:type="BL" value="false"/>
  </element>
        </section>
        <episode episodeKey="200">
        <!--episode a-->
        <section code="EPISODEOFCARE" displayName="B. EPISODE OF CARE">
        <!--section episodeofcare a-->
             <section code="EOCINFO" displayName="Episode Information">
<!--Episode of Care-->
    <!-- #3001 : ArrivalDateTime : Arrival Date and Time -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="**********" displayName="ArrivalDateTime">
    <value xsi:type="TS" value="2023-07-18T05:32:00"/>
  </element>
    <!-- #3005 : HealthIns : Health Insurance (Episode of Care) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="63513-6" displayName="HealthIns">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- # : HIPS : Payment Source (Episode of Care) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001072" displayName="HIPS">
    <value codeSystem="2.16.840.1.113883.3.221.5" codeSystemName="PHDSC" displayName="Insurance Payors - Private Health Insurance" xsi:type="CD" code="5"/>
    <value codeSystem="2.16.840.1.113883.3.221.5" codeSystemName="PHDSC" displayName="Insurance Payors - Medicare" xsi:type="CD" code="1"/>
  </element>
    <!-- #3020 : EnrolledStudy : Patient Enrolled in Research Study (Episode of Care) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001095" displayName="EnrolledStudy">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #3036 : PtRestriction2 : Patient Restriction (Episode of Care) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100000922" displayName="PtRestriction2">
    <value xsi:type="BL" value="false"/>
  </element>
        </section>
        <!--end eocinfo a-->
             </section>
        <!--end episodeofcare a-->
             <section code="HXANDRISKFACTORS" displayName="C. HISTORY AND RISK FACTORS">
<!--History and Risk Factors-->
    <!-- #4615 : Hypertension : Hypertension (History and Risk Factors) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="38341003" displayName="Hypertension">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #6000 : Height : Height (History and Risk Factors) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="8302-2" displayName="Height">
    <value xsi:type="PQ" value="165.00" unit="cm"/>
  </element>
    <!-- #6005 : Weight : Weight (History and Risk Factors) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="3141-9" displayName="Weight">
    <value xsi:type="PQ" value=" 70.00" unit="kg"/>
  </element>
    <!-- #4620 : Dyslipidemia : Dyslipidemia (History and Risk Factors) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="370992007" displayName="Dyslipidemia">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #4287 : FamilyHxCAD : Family History of Premature CAD (History and Risk Factors) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="134439009" displayName="FamilyHxCAD">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #4291 : PriorMI : Prior MI (History and Risk Factors) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="22298006" displayName="HxMI">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #4296 : PriorMIDate : Most Recent MI Date (History and Risk Factors) -->
  <!-- is NULL -->
    <!-- #4551 : HxCVD : Cerebrovascular Disease (History and Risk Factors) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="62914000" displayName="HxCVD">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #4610 : PriorPAD : Peripheral Arterial Disease (History and Risk Factors) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="399957001" displayName="PriorPAD">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #4495 : PriorPCI : Prior PCI (History and Risk Factors) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="415070008" displayName="PriorPCI">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #4501 : LMPCI : PCI LM Coronary Artery (History and Risk Factors) -->
  <!-- is NULL -->
    <!-- #4502 : LMPCIUnk : PCI LM Coronary Artery Unk (History and Risk Factors) -->
  <!-- is NULL -->
    <!-- #4503 : HxPCIDate : Most Recent PCI Date (History and Risk Factors) -->
  <!-- is NULL -->
    <!-- #4576 : HxChronicLungDisease : Chronic Lung Disease (History and Risk Factors) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="413839001" displayName="HxChronicLungDisease">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #4515 : PriorCABG : Prior CABG (History and Risk Factors) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="232717009" displayName="PriorCABG">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #4521 : HxCABGDate : Most Recent CABG Date (History and Risk Factors) -->
  <!-- is NULL -->
    <!-- #4625 : TobaccoUse : Tobacco Use (History and Risk Factors) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="110483000" displayName="TobaccoUse">
    <value codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" displayName="Former" xsi:type="CD" code="8517006"/>
  </element>
    <!-- # : TobaccoType : Tobacco Type (History and Risk Factors) -->
  <!-- is NULL -->
    <!-- #4627 : SmokeAmount : Smoking Amount (History and Risk Factors) -->
  <!-- is NULL -->
    <!-- #4630 : CAOutHospital : Cardiac Arrest Out of Hospital (History and Risk Factors) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="10001424808" displayName="CAOutHospital">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #4631 : CAWitness : Cardiac Arrest Witnessed (History and Risk Factors) -->
  <!-- is NULL -->
    <!-- #4632 : CAPostEMS : Cardiac Arrest After Arrival of Emergency Medical Services (History and Risk Factors) -->
  <!-- is NULL -->
    <!-- #4633 : InitCARhythm : First Cardiac Arrest Rhythm (History and Risk Factors) -->
  <!-- is NULL -->
    <!-- #4635 : CATransferFac : Cardiac Arrest at Transferring Facility (History and Risk Factors) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014016" displayName="CATransferFac">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #4555 : Diabetes : Diabetes Mellitus (History and Risk Factors) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="73211009" displayName="Diabetes">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #4560 : CurrentDialysis : Currently on Dialysis (History and Risk Factors) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="108241001" displayName="CurrentDialysis">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #4561 : CSHAScale : Canadian Study of Health and Aging (CSHA) Clinical Frailty Scale (History and Risk Factors) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="**********" displayName="CSHAScale">
    <value codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" displayName="2: Well" xsi:type="CD" code="**********"/>
  </element>
             </section>
             <section code="PROCINFO" displayName="E. PROCEDURE INFORMATION">
        <!--PROCINFO a-->
<!--Procedure Information-->
    <!-- #7000 : ProcedureStartDateTime : Date of Procedure (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="**********" displayName="ProcedureStartDateTime">
    <value xsi:type="TS" value="2023-07-18T07:09:00"/>
  </element>
    <!-- #7005 : ProcedureEndDateTime : End Date of Procedure (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="**********" displayName="ProcedureEndDateTime">
    <value xsi:type="TS" value="2023-07-18T08:12:00"/>
  </element>
    <!-- #7045 : DiagCorAngio : Diagnostic Coronary Angiography Procedure (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001201" displayName="DiagCorAngio">
    <value xsi:type="BL" value="true"/>
  </element>
                     <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="**********" displayName="DCathLName">
                        <value xsi:type="LN" value=""/>
                     </element>
                     <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="**********" displayName="DCathFName">
                        <value xsi:type="FN" value=""/>
                     </element>
                     <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="**********" displayName="DCathMName">
                        <value xsi:type="MN" value="."/>
                     </element>
                     <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="**********" displayName="DCathNPI">
                        <value xsi:type="NUM" value=""/>
                     </element>
    <!-- #7050 : PCIProc  -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="415070008" displayName="PCIProc">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #7060 : LeftHeartCath : Diagnostic Left Heart Cath (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="67629009" displayName="LeftHeartCath">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #7061 : PrePCILVEF : Pre-PCI Left Ventricular Ejection Fraction (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="10230-1" displayName="PrePCILVEF">
    <value xsi:type="PQ" value="65" unit="%"/>
  </element>
    <!-- #7320 : AccessSite : Arterial Access Site (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014079" displayName="AccessSite">
    <value displayName="Femoral" code="7657000" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" xsi:type="CD"/>
  </element>
    <!-- #7325 : Crossover : Arterial Cross Over (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014075" displayName="Crossover">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #7332 : ClosureMethodNA : Closure Method Not Documented (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="112000000349" displayName="ClosureMethodNA">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #7335 : VenousAccess : Venous Access (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142421" displayName="VenousAccess">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #6016 : ProcSystolicBP : Systolic Blood Pressure (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="8480-6" displayName="ProcSystolicBP">
    <value xsi:type="PQ" value="161" unit="mm[Hg]"/>
  </element>
    <!-- #7340 : CAInHosp : Cardiac Arrest at this Facility (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014017" displayName="CAInHosp">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #7210 : FluoroDoseDAP (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="228850003" displayName="FluoroDoseKerm">
    <value xsi:type="PQ" value="365" unit="mGy"/>
  </element>
    <!-- #7220 : FluoroDoseDAP (Procedure Information) Dose Area Product-->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100000994" displayName="FluoroDoseDAP">
    <value xsi:type="PQ" value="23" unit="Gy/cm2"/>
  </element>
    <!-- #7214 : FluoroTime : Fluoroscopy Time (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014077" displayName="FluoroTime">
    <value xsi:type="PQ" value="8.4" unit="min"/>
  </element>
    <!-- #7215 : ContrastVol : Contrast Volume (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="80242-1" displayName="ContrastVol">
    <value xsi:type="PQ" value="30" unit="mL"/>
  </element>
                               <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001271" displayName="ConcomProc">
                                    <value xsi:type="BL" value="false"/>
                               </element>
               <section code="PREPROCINFO" displayName="D. PRE-PROCEDURE INFORMATION">
        <!--preprocinfo a-->
<!--Pre-Procedure Information-->
    <!-- #4001 : HxHF : Heart Failure (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="84114007" displayName="HxHF">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #4011 : PriorNYHA : New York Heart Association Classification (Pre-Procedure Information) -->
  <!-- is NULL -->
    <!-- #4012 : HFNewDiag : Heart Failure Newly Diagnosed (Pre-Procedure Information) -->
  <!-- is NULL -->
    <!-- #4013 : HFType : Heart Failure Type (Pre-Procedure Information) -->
  <!-- is NULL -->
                 <section code="DIAGNOSTICTEST" displayName="Diagnostic Test">
    <!-- #5037 : ECAssessMethod : Electrocardiac Assessment Method (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="***********" displayName="ECAssessMethod">
    <value displayName="ECG" code="164847006" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" xsi:type="CD"/>
  </element>
    <!-- #5032 : ECGResults : Electrocardiac Assessment Results (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142467" displayName="ECGResults">
    <value displayName="Abnormal" code="263654008" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" xsi:type="CD"/>
  </element>
    <!-- #5033 : AntiArrhyTherapy : New Antiarrhythmic Therapy Initiated Prior to Cath Lab (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142469" displayName="AntiArrhyTherapy">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- # : ECGFindings : Electrocardiac Abnormality Type (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="102594003" displayName="ECGFindings">
    <value codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" displayName="PVC - Infrequent" xsi:type="CD" code="1000142472"/>
    <value codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" displayName="Other Electrocardiac Abnormality" xsi:type="CD" code="1000142474"/>
  </element>
    <!-- # : NSVTType : Type (Pre-Procedure Information) -->
  <!-- is NULL -->
    <!-- #6011 : HR : Heart Rate (Pre-Procedure Information) -->
  <!-- is NULL -->
    <!-- #5200 :  : Stress Test Performed (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142431" displayName="StressPerformed">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #5220 : CardiacCTA : Cardiac CTA (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="59255-0" displayName="CardiacCTA">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #5226 : CardiacCTADate : Cardiac CTA Date (Pre-Procedure Information) -->
  <!-- is NULL -->
    <!-- # : CardiacCTAResults : Results (Pre-Procedure Information) -->
  <!-- is NULL -->
    <!-- #5256 : CalciumScoreAssessed : Agatston Calcium Score Assessed (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="450360000" displayName="CalciumScoreAssessed">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #5255 : CalciumScore : Agatston Calcium Score (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="450360000" displayName="CalciumScore">
    <value xsi:type="NUM" value="132"/>
  </element>
    <!-- #5257 : CalciumScoreDate : Agatston Calcium Score Date (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="450360000" displayName="CalciumScoreDate">
    <value xsi:type="DT" value="2022-01-07"/>
  </element>
    <!-- #5111 : PreProcLVEFAssessed : LVEF Assessed (Pre-Procedure) (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001027" displayName="PreProcLVEFAssessed">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #5116 : PreProcLVEF : LVEF % (Pre-Procedure) (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="10230-1" displayName="PreProcLVEF">
    <value xsi:type="PQ" value="67" unit="%"/>
  </element>
    <!-- #5263 : PriorDxAngioProc : Prior Diagnostic Coronary Angiography Procedure without intervention (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="***********" displayName="PriorDxAngioProc">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #5264 : PriorDxAngioDate : Prior Diagnostic Coronary Angiography Procedure Date (Pre-Procedure Information) -->
  <!-- is NULL -->
    <!-- # : PriorDxAngioResults : If Yes, Results (Pre-Procedure Information) -->
  <!-- is NULL -->
    <!-- #5266 : PriorDxCathResultsUnk : Prior Diagnostic Coronary Angiography Procedure Results Unknown (Pre-Procedure Information) -->
                                                <section code="STRESSTEST" displayName="Stress Test 1">
                                                <!--Element Ref#5201-->
                                                     <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142432" displayName="StressTestType">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="49569-7" displayName="Stress Nuclear"/>
                                                     </element>
                                                <!--Element Ref#5202-->
                                                     <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="10001424303" displayName="StressTestResult">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013093" displayName="Positive"/>
                                                     </element>
                                                <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142434" displayName="StressTestRisk">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013098" displayName="Intermediate"/>
                                                     </element>
                                                <!--Element Ref#5204-->
                                                     <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142431" displayName="StressTestDate">
                                                          <value xsi:type="DT" value="2023-06-15"/>
                                                     </element>
                                                </section>
                 </section>
        <!--end DIAGNOSTICTEST a-->
                                                <section code="PREPROCMED" displayName="Pre-Procedure Meds">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="PreProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014161" displayName="Non-Statin"/>
                                                   </element>
                                                     <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="PreProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="112000000168" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PREPROCMED" displayName="Pre-Procedure Meds">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="PreProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014162" displayName="Antiarrhythmic Agent Other"/>
                                                   </element>
                                                     <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="PreProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="112000000168" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PREPROCMED" displayName="Pre-Procedure Meds">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="PreProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="112000000694" displayName="Proprotein Convertase Subtilisin"/>
                                                   </element>
                                                     <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="PreProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="112000000168" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PREPROCMED" displayName="Pre-Procedure Meds">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="PreProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="1191" displayName="Aspirin"/>
                                                   </element>
                                                     <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="PreProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001247" displayName="Yes"/>
                                                     </element>
                                               </section>
                                                <section code="PREPROCMED" displayName="Pre-Procedure Meds">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="PreProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="1656341" displayName="Sacubitril and Valsartan"/>
                                                   </element>
                                                     <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="PreProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="112000000168" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PREPROCMED" displayName="Pre-Procedure Meds">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="PreProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="31970009" displayName="Long Acting Nitrate"/>
                                                   </element>
                                                     <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="PreProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="112000000168" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PREPROCMED" displayName="Pre-Procedure Meds">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="PreProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="33252009" displayName="Beta Blocker"/>
                                                   </element>
                                                     <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="PreProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="112000000168" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PREPROCMED" displayName="Pre-Procedure Meds">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="PreProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="35829" displayName="Ranolazine"/>
                                                   </element>
                                                     <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="PreProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="112000000168" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PREPROCMED" displayName="Pre-Procedure Meds">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="PreProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="372913009" displayName="Angiotensin II Receptor Blocker"/>
                                                   </element>
                                                     <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="PreProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="112000000168" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PREPROCMED" displayName="Pre-Procedure Meds">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="PreProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="41549009" displayName="Angiotensin Converting Enzyme In"/>
                                                   </element>
                                                     <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="PreProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="112000000168" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PREPROCMED" displayName="Pre-Procedure Meds">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="PreProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="48698004" displayName="Calcium Channel Blocking Agent"/>
                                                   </element>
                                                     <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="PreProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001247" displayName="Yes"/>
                                                     </element>
                                               </section>
                                                <section code="PREPROCMED" displayName="Pre-Procedure Meds">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="PreProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="96302009" displayName="Statin"/>
                                                   </element>
                                                     <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="PreProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="112000000168" displayName="No"/>
                                                     </element>
                                               </section>
               </section>
        <!--end PREPROCINFO a-->
          <section code="LABS" displayName="F. LABS">
        <!-- LABS a-->
                 <section code="PREPROCLABS" displayName="Pre-Procedure Labs">
<!--LABS-->
    <!-- #6090 : PreProcTnI : Troponin I Pre-Procedure (LABS) -->
  <!-- is NULL -->
    <!-- #6091 : PreProcTnIND : Troponin l Pre-Procedure Not Drawn (LABS) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="10839-9" displayName="PreProcTnIND">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #6095 : PreProcTnT : Troponin T Pre-Procedure (LABS) -->
  <!-- is NULL -->
    <!-- #6096 : PreProcTnTND : Troponin T Pre-Procedure Not Drawn (LABS) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="6598-7" displayName="PreProcTnTND">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #6050 : PreProcCreat : Creatinine Pre-Procedure (LABS) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="2160-0" displayName="PreProcCreat">
    <value xsi:type="PQ" value="0.81" unit="mg/dL"/>
  </element>
    <!-- #6051 : PreProcCreatND : Creatinine Pre-Procedure  Not Drawn (LABS) -->
  <!-- is NULL -->
    <!-- #6030 : PreProcHgb : Hemoglobin Pre-Procedure (LABS) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="718-7" displayName="PreProcHgb">
    <value xsi:type="PQ" value="12.70" unit="g/dL"/>
  </element>
    <!-- #6031 : PreProcHgbND : Hemoglobin Pre-Procedure Not Drawn (LABS) -->
  <!-- is NULL -->
    <!-- #6100 : LipidsTC : Total Cholesterol (LABS) -->
  <!-- is NULL -->
    <!-- #6101 : LipidsTCND : Total Cholesterol Not Drawn (LABS) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="2093-3" displayName="LipidsTCND">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #6105 : LipidsHDL : High-density Lipoprotein (LABS) -->
  <!-- is NULL -->
    <!-- #6106 : LipidsHDLND : High-density Lipoprotein Not Drawn (LABS) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="2085-9" displayName="LipidsHDLND">
    <value xsi:type="BL" value="true"/>
  </element>
                 </section>
                 <section code="POSTPROCLABS" displayName="Post-Procedure Labs">
    <!-- #8515 : PostProcTnI : Troponin I Post-Procedure (LABS) -->
  <!-- is NULL -->
    <!-- #8516 : PostProcTnIND : Troponin I Post-Procedure Not Drawn (LABS) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="10839-9" displayName="PostProcTnIND">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #8520 : PostProcTnT : Troponin T Post-Procedure (LABS) -->
  <!-- is NULL -->
    <!-- #8521 : PostProcTnTND : Troponin T Post-Procedure Not Drawn (LABS) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="6598-7" displayName="PostProcTnTND">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #8510 : PostProcCreat : Creatinine Post-Procedure (LABS) -->
  <!-- is NULL -->
    <!-- #8511 : PostProcCreatND : Creatinine Post-Procedure Not Drawn (LABS) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="2160-0" displayName="PostProcCreatND">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #8505 : PostProcHgb : Hemoglobin Post-Procedure (LABS) -->
  <!-- is NULL -->
    <!-- #8506 : PostProcHgbND : Hemoglobin Post-Procedure Not Drawn (LABS) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="718-7" displayName="PostProcHgbND">
    <value xsi:type="BL" value="true"/>
  </element>
                 </section>
          </section>
        <!-- end LABS a-->
          <section code="LABVISIT" displayName="G. CATH LAB VISIT">
        <!-- LABVISIT a-->
                               <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014000" displayName="CathLabVisitIndication">
                                     <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014003" displayName="Suspected CAD"/>
                                     <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="10001424790" displayName="Worsening Angina"/>
                               </element>
<!--Cath Lab Visit-->
    <!-- #7405 : CPSxAssess : Chest Pain Symptom Assessment (Cath Lab Visit) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001274" displayName="CPSxAssess">
    <value displayName="Typical Angina" code="429559004" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" xsi:type="CD"/>
  </element>
    <!-- #7410 : CVInstability : Cardiovascular Instability (Cath Lab Visit) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014004" displayName="CVInstability">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- # : CVInstabilityType : Cardiovascular Instability Type (Cath Lab Visit) -->
  <!-- is NULL -->
    <!-- #7420 : VSupport : Ventricular Support (Cath Lab Visit) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001276" displayName="VSupport">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #7421 : PharmVasoSupp : Pharmacologic Vasopressor Support (Cath Lab Visit) -->
  <!-- is NULL -->
    <!-- #7422 : MechVentSupp : Mechanical Ventricular Support (Cath Lab Visit) -->
  <!-- is NULL -->
    <!-- #7424 : MVSupportTiming : Mechanical Ventricular Support Timing (Cath Lab Visit) -->
  <!-- is NULL -->
    <!-- #7465 : PreOPEval : Evaluation for Surgery Type (Cath Lab Visit) -->
  <!-- is NULL -->
    <!-- #7466 : FuncCapacity : Functional Capacity (Cath Lab Visit) -->
  <!-- is NULL -->
    <!-- #7468 : SurgRisk : Surgical Risk (Cath Lab Visit) -->
  <!-- is NULL -->
    <!-- #7469 : OrganTransplantSurg : Solid Organ Transplant Surgery (Cath Lab Visit) -->
  <!-- is NULL -->
    <!-- #7470 : OrganTransplantDonor : Solid Organ Transplant Donor (Cath Lab Visit) -->
  <!-- is NULL -->
  <!-- is NULL -->
          </section>
        <!-- end LABVISIT a-->
          <section code="CORANATOMY" displayName="H. CORONARY ANATOMY">
<!--Coronary Anatomy-->
    <!-- #7500 : Dominance : Coronary Circulation Dominance (Coronary Anatomy) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="253727002" displayName="Dominance">
    <value codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" displayName="Right" xsi:type="CD" code="253728007"/>
  </element>
    <!-- #7505 : NVStenosis : Native Vessel with Stenosis &gt;= 50% (Coronary Anatomy) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001297" displayName="NVStenosis">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #7525 : GraftStenosis : Graft Vessel with Stenosis &gt;= 50% (Coronary Anatomy) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100012978" displayName="GraftStenosis">
    <value xsi:type="BL" value="false"/>
  </element>
                                                <section code="NVESSEL" displayName="Native Vessel Segment =1 ">
                                                    <!--Element Ref#7507-->
                                                    <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100012984" displayName="NVSegmentID">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="450960006" displayName="2 - mRCA"/>
                                                     </element>
                                                     <!--Element Ref#7508-->
                                                     <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100012981" displayName="NVCoroVesselStenosis">
                                                          <value xsi:type="PQ" value="60" unit="%"/>
                                                     </element>
                                                     <!--Element Ref#7511-->
                                                     <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100012979" displayName="NVAdjuncMeasObtained">
                                                          <value xsi:type="BL" value="false"/>
                                                     </element>
                                                     <!--Element Ref#7512-->
                                                </section>
                                                <section code="NVESSEL" displayName="Native Vessel Segment =2 ">
                                                    <!--Element Ref#7507-->
                                                    <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100012984" displayName="NVSegmentID">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="68787002" displayName="12 - pLAD"/>
                                                     </element>
                                                     <!--Element Ref#7508-->
                                                     <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100012981" displayName="NVCoroVesselStenosis">
                                                          <value xsi:type="PQ" value="90" unit="%"/>
                                                     </element>
                                                     <!--Element Ref#7511-->
                                                     <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100012979" displayName="NVAdjuncMeasObtained">
                                                          <value xsi:type="BL" value="false"/>
                                                     </element>
                                                     <!--Element Ref#7512-->
                                                </section>
          </section>
          <section code="INTPOSTEVENT" displayName="K. INTRA AND POST-PROCEDURE EVENTS">
<!--Intra and Post Procedure Events-->
    <!-- #9275 : PostTransfusion : Packed Red Blood Cell Transfusion (Intra and Post Procedure Events) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="71493000" displayName="PostTransfusion">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #9276 : PRBCUnits : Number of units of PRBCs transfused (Intra and Post Procedure Events) -->
  <!-- is NULL -->
    <!-- #9277 : TransfusPostPCI : Transfusion PCI (Intra and Post Procedure Events) -->
  <!-- is NULL -->
    <!-- #9278 : TransfusionPostSurg : Transfusion Surgery (Intra and Post Procedure Events) -->
  <!-- is NULL -->
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #1">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014076" displayName="New Requirement for Dialysis"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #2">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142371" displayName="Bleeding - Other"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #3">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142419" displayName="Other Vascular Complications Requiring Treatment"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #4">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142440" displayName="Bleeding - Access Site"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #5">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="22298006" displayName="Myocardial Infarction"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #6">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="230706003" displayName="Stroke - Hemorrhagic"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #7">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="230713003" displayName="Stroke - Undetermined"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #8">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="35304003" displayName="Cardiac Tamponade"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #9">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="385494008" displayName="Bleeding - Hematoma at Access Site"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #10">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="410429000" displayName="Cardiac Arrest"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #11">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="417941003" displayName="Bleeding - Genitourinary"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #12">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="422504002" displayName="Stroke - Ischemic"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #13">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="74474003" displayName="Bleeding - Gastrointestinal"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #14">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="84114007" displayName="Heart Failure"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #15">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="89138009" displayName="Cardiogenic Shock"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #16">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="95549001" displayName="Bleeding - Retroperitoneal"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          </section>
        <!-- end INTPOSTEVENT a-->
        </section>
        <!-- end PROCINFO a-->
             <section code="PROCINFO" displayName="E. PROCEDURE INFORMATION">
        <!--PROCINFO a-->
<!--Procedure Information-->
    <!-- #7000 : ProcedureStartDateTime : Date of Procedure (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="**********" displayName="ProcedureStartDateTime">
    <value xsi:type="TS" value="2023-07-18T09:50:00"/>
  </element>
    <!-- #7005 : ProcedureEndDateTime : End Date of Procedure (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="**********" displayName="ProcedureEndDateTime">
    <value xsi:type="TS" value="2023-07-18T10:30:00"/>
  </element>
    <!-- #7045 : DiagCorAngio : Diagnostic Coronary Angiography Procedure (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001201" displayName="DiagCorAngio">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #7050 : PCIProc  -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="415070008" displayName="PCIProc">
    <value xsi:type="BL" value="true"/>
  </element>
                     <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="**********" displayName="PCILName">
                        <value xsi:type="LN" value=""/>
                     </element>
                     <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="**********" displayName="PCIFName">
                        <value xsi:type="FN" value=""/>
                     </element>
                     <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="**********" displayName="PCIMName">
                        <value xsi:type="MN" value="."/>
                     </element>
                     <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="**********" displayName="PCINPI">
                        <value xsi:type="NUM" value=""/>
                     </element>
    <!-- #7060 : LeftHeartCath : Diagnostic Left Heart Cath (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="67629009" displayName="LeftHeartCath">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #7061 : PrePCILVEF : Pre-PCI Left Ventricular Ejection Fraction (Procedure Information) -->
  <!-- is NULL -->
    <!-- #7320 : AccessSite : Arterial Access Site (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014079" displayName="AccessSite">
    <value displayName="Femoral" code="7657000" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" xsi:type="CD"/>
  </element>
    <!-- #7325 : Crossover : Arterial Cross Over (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014075" displayName="Crossover">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #7332 : ClosureMethodNA : Closure Method Not Documented (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="112000000349" displayName="ClosureMethodNA">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #7335 : VenousAccess : Venous Access (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142421" displayName="VenousAccess">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #6016 : ProcSystolicBP : Systolic Blood Pressure (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="8480-6" displayName="ProcSystolicBP">
    <value xsi:type="PQ" value="149" unit="mm[Hg]"/>
  </element>
    <!-- #7340 : CAInHosp : Cardiac Arrest at this Facility (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014017" displayName="CAInHosp">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #7210 : FluoroDoseDAP (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="228850003" displayName="FluoroDoseKerm">
    <value xsi:type="PQ" value="451" unit="mGy"/>
  </element>
    <!-- #7220 : FluoroDoseDAP (Procedure Information) Dose Area Product-->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100000994" displayName="FluoroDoseDAP">
    <value xsi:type="PQ" value="40" unit="Gy/cm2"/>
  </element>
    <!-- #7214 : FluoroTime : Fluoroscopy Time (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014077" displayName="FluoroTime">
    <value xsi:type="PQ" value="11.3" unit="min"/>
  </element>
    <!-- #7215 : ContrastVol : Contrast Volume (Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="80242-1" displayName="ContrastVol">
    <value xsi:type="PQ" value="30" unit="mL"/>
  </element>
                               <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001271" displayName="ConcomProc">
                                    <value xsi:type="BL" value="false"/>
                               </element>
               <section code="PREPROCINFO" displayName="D. PRE-PROCEDURE INFORMATION">
        <!--preprocinfo a-->
<!--Pre-Procedure Information-->
    <!-- #4001 : HxHF : Heart Failure (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="84114007" displayName="HxHF">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #4011 : PriorNYHA : New York Heart Association Classification (Pre-Procedure Information) -->
  <!-- is NULL -->
    <!-- #4012 : HFNewDiag : Heart Failure Newly Diagnosed (Pre-Procedure Information) -->
  <!-- is NULL -->
    <!-- #4013 : HFType : Heart Failure Type (Pre-Procedure Information) -->
  <!-- is NULL -->
                 <section code="DIAGNOSTICTEST" displayName="Diagnostic Test">
    <!-- #5037 : ECAssessMethod : Electrocardiac Assessment Method (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="***********" displayName="ECAssessMethod">
    <value displayName="ECG" code="164847006" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" xsi:type="CD"/>
  </element>
    <!-- #5032 : ECGResults : Electrocardiac Assessment Results (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142467" displayName="ECGResults">
    <value displayName="Abnormal" code="263654008" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" xsi:type="CD"/>
  </element>
    <!-- #5033 : AntiArrhyTherapy : New Antiarrhythmic Therapy Initiated Prior to Cath Lab (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142469" displayName="AntiArrhyTherapy">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- # : ECGFindings : Electrocardiac Abnormality Type (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="102594003" displayName="ECGFindings">
    <value codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" displayName="PVC - Infrequent" xsi:type="CD" code="1000142472"/>
    <value codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" displayName="Other Electrocardiac Abnormality" xsi:type="CD" code="1000142474"/>
  </element>
    <!-- # : NSVTType : Type (Pre-Procedure Information) -->
  <!-- is NULL -->
    <!-- #6011 : HR : Heart Rate (Pre-Procedure Information) -->
  <!-- is NULL -->
    <!-- #5200 :  : Stress Test Performed (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142431" displayName="StressPerformed">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #5220 : CardiacCTA : Cardiac CTA (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="59255-0" displayName="CardiacCTA">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #5226 : CardiacCTADate : Cardiac CTA Date (Pre-Procedure Information) -->
  <!-- is NULL -->
    <!-- # : CardiacCTAResults : Results (Pre-Procedure Information) -->
  <!-- is NULL -->
    <!-- #5256 : CalciumScoreAssessed : Agatston Calcium Score Assessed (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="450360000" displayName="CalciumScoreAssessed">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #5255 : CalciumScore : Agatston Calcium Score (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="450360000" displayName="CalciumScore">
    <value xsi:type="NUM" value="132"/>
  </element>
    <!-- #5257 : CalciumScoreDate : Agatston Calcium Score Date (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="450360000" displayName="CalciumScoreDate">
    <value xsi:type="DT" value="2022-01-07"/>
  </element>
    <!-- #5111 : PreProcLVEFAssessed : LVEF Assessed (Pre-Procedure) (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001027" displayName="PreProcLVEFAssessed">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #5116 : PreProcLVEF : LVEF % (Pre-Procedure) (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="10230-1" displayName="PreProcLVEF">
    <value xsi:type="PQ" value="65" unit="%"/>
  </element>
    <!-- #5263 : PriorDxAngioProc : Prior Diagnostic Coronary Angiography Procedure without intervention (Pre-Procedure Information) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="***********" displayName="PriorDxAngioProc">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #5264 : PriorDxAngioDate : Prior Diagnostic Coronary Angiography Procedure Date (Pre-Procedure Information) -->
  <!-- is NULL -->
    <!-- # : PriorDxAngioResults : If Yes, Results (Pre-Procedure Information) -->
  <!-- is NULL -->
    <!-- #5266 : PriorDxCathResultsUnk : Prior Diagnostic Coronary Angiography Procedure Results Unknown (Pre-Procedure Information) -->
                                                <section code="STRESSTEST" displayName="Stress Test 1">
                                                <!--Element Ref#5201-->
                                                     <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142432" displayName="StressTestType">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="49569-7" displayName="Stress Nuclear"/>
                                                     </element>
                                                <!--Element Ref#5202-->
                                                     <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="10001424303" displayName="StressTestResult">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013093" displayName="Positive"/>
                                                     </element>
                                                <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142434" displayName="StressTestRisk">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013098" displayName="Intermediate"/>
                                                     </element>
                                                <!--Element Ref#5204-->
                                                     <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142431" displayName="StressTestDate">
                                                          <value xsi:type="DT" value="2023-06-15"/>
                                                     </element>
                                                </section>
                 </section>
        <!--end DIAGNOSTICTEST a-->
                                                <section code="PREPROCMED" displayName="Pre-Procedure Meds">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="PreProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014161" displayName="Non-Statin"/>
                                                   </element>
                                                     <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="PreProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="112000000168" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PREPROCMED" displayName="Pre-Procedure Meds">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="PreProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014162" displayName="Antiarrhythmic Agent Other"/>
                                                   </element>
                                                     <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="PreProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="112000000168" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PREPROCMED" displayName="Pre-Procedure Meds">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="PreProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="112000000694" displayName="Proprotein Convertase Subtilisin"/>
                                                   </element>
                                                     <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="PreProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="112000000168" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PREPROCMED" displayName="Pre-Procedure Meds">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="PreProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="1191" displayName="Aspirin"/>
                                                   </element>
                                                     <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="PreProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001247" displayName="Yes"/>
                                                     </element>
                                               </section>
                                                <section code="PREPROCMED" displayName="Pre-Procedure Meds">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="PreProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="1656341" displayName="Sacubitril and Valsartan"/>
                                                   </element>
                                                     <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="PreProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="112000000168" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PREPROCMED" displayName="Pre-Procedure Meds">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="PreProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="31970009" displayName="Long Acting Nitrate"/>
                                                   </element>
                                                     <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="PreProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="112000000168" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PREPROCMED" displayName="Pre-Procedure Meds">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="PreProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="33252009" displayName="Beta Blocker"/>
                                                   </element>
                                                     <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="PreProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="112000000168" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PREPROCMED" displayName="Pre-Procedure Meds">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="PreProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="35829" displayName="Ranolazine"/>
                                                   </element>
                                                     <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="PreProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="112000000168" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PREPROCMED" displayName="Pre-Procedure Meds">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="PreProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="372913009" displayName="Angiotensin II Receptor Blocker"/>
                                                   </element>
                                                     <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="PreProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="112000000168" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PREPROCMED" displayName="Pre-Procedure Meds">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="PreProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="41549009" displayName="Angiotensin Converting Enzyme In"/>
                                                   </element>
                                                     <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="PreProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="112000000168" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PREPROCMED" displayName="Pre-Procedure Meds">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="PreProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="48698004" displayName="Calcium Channel Blocking Agent"/>
                                                   </element>
                                                     <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="PreProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001247" displayName="Yes"/>
                                                     </element>
                                               </section>
                                                <section code="PREPROCMED" displayName="Pre-Procedure Meds">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="PreProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="96302009" displayName="Statin"/>
                                                   </element>
                                                     <!--Element Ref#5203-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="PreProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="112000000168" displayName="No"/>
                                                     </element>
                                               </section>
               </section>
        <!--end PREPROCINFO a-->
                                          <section code="CLMETHOD" displayName="Closure Methods1">
                                                   <!--Element Ref#7330-->
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014083" displayName="ClosureCounter">
                                                        <value xsi:type="CTR" value="1"/>
                                                   </element>
                                                   <!--Element Ref#7331-->
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014074" displayName="ClosureDevID">
                                                        <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="5158" displayName="Perclose ProStyle"/>
                                                   </element>
<!--Procedure Information-Closure Devices-->
    <!-- #7333 : ClosureUDI : Closure Method Unique Device Identifier (Procedure Information-Closure Devices) -->
  <!-- is NULL -->
                                          </section>
          <section code="LABS" displayName="F. LABS">
        <!-- LABS a-->
                 <section code="PREPROCLABS" displayName="Pre-Procedure Labs">
<!--LABS-->
    <!-- #6090 : PreProcTnI : Troponin I Pre-Procedure (LABS) -->
  <!-- is NULL -->
    <!-- #6091 : PreProcTnIND : Troponin l Pre-Procedure Not Drawn (LABS) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="10839-9" displayName="PreProcTnIND">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #6095 : PreProcTnT : Troponin T Pre-Procedure (LABS) -->
  <!-- is NULL -->
    <!-- #6096 : PreProcTnTND : Troponin T Pre-Procedure Not Drawn (LABS) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="6598-7" displayName="PreProcTnTND">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #6050 : PreProcCreat : Creatinine Pre-Procedure (LABS) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="2160-0" displayName="PreProcCreat">
    <value xsi:type="PQ" value="0.81" unit="mg/dL"/>
  </element>
    <!-- #6051 : PreProcCreatND : Creatinine Pre-Procedure  Not Drawn (LABS) -->
  <!-- is NULL -->
    <!-- #6030 : PreProcHgb : Hemoglobin Pre-Procedure (LABS) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="718-7" displayName="PreProcHgb">
    <value xsi:type="PQ" value="12.70" unit="g/dL"/>
  </element>
    <!-- #6031 : PreProcHgbND : Hemoglobin Pre-Procedure Not Drawn (LABS) -->
  <!-- is NULL -->
    <!-- #6100 : LipidsTC : Total Cholesterol (LABS) -->
  <!-- is NULL -->
    <!-- #6101 : LipidsTCND : Total Cholesterol Not Drawn (LABS) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="2093-3" displayName="LipidsTCND">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #6105 : LipidsHDL : High-density Lipoprotein (LABS) -->
  <!-- is NULL -->
    <!-- #6106 : LipidsHDLND : High-density Lipoprotein Not Drawn (LABS) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="2085-9" displayName="LipidsHDLND">
    <value xsi:type="BL" value="true"/>
  </element>
                 </section>
                 <section code="POSTPROCLABS" displayName="Post-Procedure Labs">
    <!-- #8515 : PostProcTnI : Troponin I Post-Procedure (LABS) -->
  <!-- is NULL -->
    <!-- #8516 : PostProcTnIND : Troponin I Post-Procedure Not Drawn (LABS) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="10839-9" displayName="PostProcTnIND">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #8520 : PostProcTnT : Troponin T Post-Procedure (LABS) -->
  <!-- is NULL -->
    <!-- #8521 : PostProcTnTND : Troponin T Post-Procedure Not Drawn (LABS) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="6598-7" displayName="PostProcTnTND">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #8510 : PostProcCreat : Creatinine Post-Procedure (LABS) -->
  <!-- is NULL -->
    <!-- #8511 : PostProcCreatND : Creatinine Post-Procedure Not Drawn (LABS) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="2160-0" displayName="PostProcCreatND">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #8505 : PostProcHgb : Hemoglobin Post-Procedure (LABS) -->
  <!-- is NULL -->
    <!-- #8506 : PostProcHgbND : Hemoglobin Post-Procedure Not Drawn (LABS) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="718-7" displayName="PostProcHgbND">
    <value xsi:type="BL" value="true"/>
  </element>
                 </section>
          </section>
        <!-- end LABS a-->
          <section code="LABVISIT" displayName="G. CATH LAB VISIT">
        <!-- LABVISIT a-->
                               <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014000" displayName="CathLabVisitIndication">
                                     <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="10001424790" displayName="Worsening Angina"/>
                               </element>
<!--Cath Lab Visit-->
    <!-- #7405 : CPSxAssess : Chest Pain Symptom Assessment (Cath Lab Visit) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001274" displayName="CPSxAssess">
    <value displayName="Typical Angina" code="429559004" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" xsi:type="CD"/>
  </element>
    <!-- #7410 : CVInstability : Cardiovascular Instability (Cath Lab Visit) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014004" displayName="CVInstability">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- # : CVInstabilityType : Cardiovascular Instability Type (Cath Lab Visit) -->
  <!-- is NULL -->
    <!-- #7420 : VSupport : Ventricular Support (Cath Lab Visit) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001276" displayName="VSupport">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #7421 : PharmVasoSupp : Pharmacologic Vasopressor Support (Cath Lab Visit) -->
  <!-- is NULL -->
    <!-- #7422 : MechVentSupp : Mechanical Ventricular Support (Cath Lab Visit) -->
  <!-- is NULL -->
    <!-- #7424 : MVSupportTiming : Mechanical Ventricular Support Timing (Cath Lab Visit) -->
  <!-- is NULL -->
    <!-- #7465 : PreOPEval : Evaluation for Surgery Type (Cath Lab Visit) -->
  <!-- is NULL -->
    <!-- #7466 : FuncCapacity : Functional Capacity (Cath Lab Visit) -->
  <!-- is NULL -->
    <!-- #7468 : SurgRisk : Surgical Risk (Cath Lab Visit) -->
  <!-- is NULL -->
    <!-- #7469 : OrganTransplantSurg : Solid Organ Transplant Surgery (Cath Lab Visit) -->
  <!-- is NULL -->
    <!-- #7470 : OrganTransplantDonor : Solid Organ Transplant Donor (Cath Lab Visit) -->
  <!-- is NULL -->
  <!-- is NULL -->
          </section>
        <!-- end LABVISIT a-->
          <section code="CORANATOMY" displayName="H. CORONARY ANATOMY">
<!--Coronary Anatomy-->
    <!-- #7500 : Dominance : Coronary Circulation Dominance (Coronary Anatomy) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="253727002" displayName="Dominance">
    <value codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" displayName="Right" xsi:type="CD" code="253728007"/>
  </element>
    <!-- #7505 : NVStenosis : Native Vessel with Stenosis &gt;= 50% (Coronary Anatomy) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001297" displayName="NVStenosis">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #7525 : GraftStenosis : Graft Vessel with Stenosis &gt;= 50% (Coronary Anatomy) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100012978" displayName="GraftStenosis">
    <value xsi:type="BL" value="false"/>
  </element>
                                                <section code="NVESSEL" displayName="Native Vessel Segment =1 ">
                                                    <!--Element Ref#7507-->
                                                    <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100012984" displayName="NVSegmentID">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="450960006" displayName="2 - mRCA"/>
                                                     </element>
                                                     <!--Element Ref#7508-->
                                                     <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100012981" displayName="NVCoroVesselStenosis">
                                                          <value xsi:type="PQ" value="60" unit="%"/>
                                                     </element>
                                                     <!--Element Ref#7511-->
                                                     <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100012979" displayName="NVAdjuncMeasObtained">
                                                          <value xsi:type="BL" value="false"/>
                                                     </element>
                                                     <!--Element Ref#7512-->
                                                </section>
                                                <section code="NVESSEL" displayName="Native Vessel Segment =2 ">
                                                    <!--Element Ref#7507-->
                                                    <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100012984" displayName="NVSegmentID">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="68787002" displayName="12 - pLAD"/>
                                                     </element>
                                                     <!--Element Ref#7508-->
                                                     <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100012981" displayName="NVCoroVesselStenosis">
                                                          <value xsi:type="PQ" value="70" unit="%"/>
                                                     </element>
                                                     <!--Element Ref#7511-->
                                                     <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100012979" displayName="NVAdjuncMeasObtained">
                                                          <value xsi:type="BL" value="true"/>
                                                     </element>
                                                     <!--Element Ref#7512-->
                                                     <!--Element Ref#7514-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="431945005" displayName="NV_IVUS">
                                                          <value xsi:type="PQ" value=" 3.20"  unit="mm2"/>
                                                     </element>
                                                </section>
          </section>
          <section code="PCIPROC" displayName="I. PCI PROCEDURE">
<!--PCI Procedure-->
    <!-- #7800 : PCIStatus : PCI Status (PCI Procedure) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100012986" displayName="PCIStatus">
    <value codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" displayName="Elective" xsi:type="CD" code="100012987"/>
  </element>
    <!-- #7806 : HypothermiaInduced : Hypothermia Induced (PCI Procedure) -->
  <!-- is NULL -->
    <!-- #7807 : HypothermiaInducedTiming : Hypothermia Induced Timing (PCI Procedure) -->
  <!-- is NULL -->
    <!-- #7810 : LOCProc : Level of Consciousness (PCI Procedure) (PCI Procedure) -->
  <!-- is NULL -->
    <!-- #7815 : PCIDecision : Decision for PCI with Surgical Consult (PCI Procedure) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142366" displayName="PCIDecision">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #7816 : CVTxDecision : Cardiovascular Treatment Decision (PCI Procedure) -->
  <!-- is NULL -->
    <!-- #7820 : MultiVesselDz : PCI for Multi-vessel Disease (PCI Procedure) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013007" displayName="MultiVesselDz">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #7821 : MultiVessProcType : Multi-vessel Procedure Type (PCI Procedure) -->
  <!-- is NULL -->
                               <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="*********" displayName="PCIIndication">
                                     <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="10001424795" displayName="Other PCI Indication"/>
                               </element>
    <!-- #7831 : SyntaxScore : Syntax Score (PCI Procedure) -->
  <!-- is NULL -->
    <!-- #7832 : SyntaxScoreUnk : Syntax Score Unknown (PCI Procedure) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="10001424796" displayName="SyntaxScoreUnk">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #7835 : StemiFirstNoted : &#0133; STEMI or STEMI Equivalent First Noted (PCI Procedure) -->
  <!-- is NULL -->
    <!-- #7836 : SubECGDate : Date/Time (PCI Procedure) -->
  <!-- is NULL -->
    <!-- #7840 : SubECGED : Subsequent ECG obtained in Emergency Department (PCI Procedure) -->
  <!-- is NULL -->
    <!-- #7841 : PatientTransPCI : &#0133; Transferred in for Immediate PCI for STEMI (PCI Procedure) -->
  <!-- is NULL -->
    <!-- #7842 : EDPresentDate : ED Presentation at Referring Facility Date/Time (PCI Procedure) -->
  <!-- is NULL -->
    <!-- #7845 : FirstDevActiDate : &#0133; First Device Activation Date/Time (PCI Procedure) -->
  <!-- is NULL -->
        <!-- PCIPROC a-->
                                                <section code="PROCMED" displayName="PCI Procedure Med 1">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="ProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="**********" displayName="Glycoprotein IIb IIIa Inhibitors"/>
                                                   </element>
                                                     <!--Element Ref#7995-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="ProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014173" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PROCMED" displayName="PCI Procedure Med 2">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="ProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="1114195" displayName="Rivaroxaban"/>
                                                   </element>
                                                     <!--Element Ref#7995-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="ProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014173" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PROCMED" displayName="PCI Procedure Med 3">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="ProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="1116632" displayName="Ticagrelor"/>
                                                   </element>
                                                     <!--Element Ref#7995-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="ProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014173" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PROCMED" displayName="PCI Procedure Med 4">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="ProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="11289" displayName="Warfarin"/>
                                                   </element>
                                                     <!--Element Ref#7995-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="ProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014173" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PROCMED" displayName="PCI Procedure Med 5">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="ProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="1364430" displayName="Apixaban"/>
                                                   </element>
                                                     <!--Element Ref#7995-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="ProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014173" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PROCMED" displayName="PCI Procedure Med 6">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="ProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="15202" displayName="Argatroban"/>
                                                   </element>
                                                     <!--Element Ref#7995-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="ProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014173" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PROCMED" displayName="PCI Procedure Med 7">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="ProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="1537034" displayName="Vorapaxar"/>
                                                   </element>
                                                     <!--Element Ref#7995-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="ProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014173" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PROCMED" displayName="PCI Procedure Med 8">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="ProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="1546356" displayName="Dabigatran"/>
                                                   </element>
                                                     <!--Element Ref#7995-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="ProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014173" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PROCMED" displayName="PCI Procedure Med 9">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="ProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="1599538" displayName="Edoxaban"/>
                                                   </element>
                                                     <!--Element Ref#7995-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="ProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014173" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PROCMED" displayName="PCI Procedure Med 10">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="ProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="1656052" displayName="Cangrelor"/>
                                                   </element>
                                                     <!--Element Ref#7995-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="ProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014173" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PROCMED" displayName="PCI Procedure Med 11">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="ProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="321208" displayName="Fondaparinux"/>
                                                   </element>
                                                     <!--Element Ref#7995-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="ProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014173" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PROCMED" displayName="PCI Procedure Med 12">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="ProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="32968" displayName="Clopidogrel"/>
                                                   </element>
                                                     <!--Element Ref#7995-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="ProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="Yes"/>
                                                     </element>
                                               </section>
                                                <section code="PROCMED" displayName="PCI Procedure Med 13">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="ProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="373294004" displayName="Low Molecular Weight Heparin"/>
                                                   </element>
                                                     <!--Element Ref#7995-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="ProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014173" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PROCMED" displayName="PCI Procedure Med 14">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="ProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="400610005" displayName="Bivalirudin"/>
                                                   </element>
                                                     <!--Element Ref#7995-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="ProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014173" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PROCMED" displayName="PCI Procedure Med 15">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="ProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="613391" displayName="Prasugrel"/>
                                                   </element>
                                                     <!--Element Ref#7995-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="ProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014173" displayName="No"/>
                                                     </element>
                                               </section>
                                                <section code="PROCMED" displayName="PCI Procedure Med 16">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="ProcMedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="96382006" displayName="Unfractionated Heparin"/>
                                                   </element>
                                                     <!--Element Ref#7995-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="ProcMedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="Yes"/>
                                                     </element>
                                               </section>
                                           <section code="LESIONDEV" displayName="J. LESIONS AND DEVICES #1">
                                                <!--Element Ref#8000-->
                                                <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142441" displayName="LesionCounter">
                                                  <value xsi:type="CTR" value="1"/>
                                                </element>
                                                <!--Element Ref#8001-->
                                                <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100012984" displayName="SegmentID">
                                                  <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="68787002" displayName="12 - pLAD"/>
                                                  <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142403" displayName="11c - Distal"/>
                                                </element>
<!--Lesions-->
    <!-- #8004 : StenosisPriorTreat : Stenosis Immediately Prior to RX (Lesions) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142442" displayName="StenosisPriorTreat">
    <value xsi:type="PQ" value="70" unit="%"/>
  </element>
    <!-- #8007 : PreProcTIMI : Pre-Procedure TIMI Flow (Lesions) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="112000000348" displayName="PreProcTIMI">
    <value codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" displayName="TIMI - 3" xsi:type="CD" code="371865008"/>
  </element>
    <!-- #8008 : PrevTreatedLesion : Previously Treated Lesion (Lesions) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013015" displayName="PrevTreatedLesion">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #8009 : PrevLesionDate : Previously Treated Lesion Date (Lesions) -->
  <!-- is NULL -->
    <!-- #8010 : PreviousStent : Treated with Stent (Lesions) -->
  <!-- is NULL -->
    <!-- #8011 : InRestenosis : In-stent Restenosis (Lesions) -->
  <!-- is NULL -->
    <!-- #8012 : InThrombosis : In-stent Thrombosis (Lesions) -->
  <!-- is NULL -->
    <!-- #8013 : StentType : Stent Type (Lesions) -->
    <!-- #8015 : LesionGraft : Lesion In Graft (Lesions) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142443" displayName="LesionGraft">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #8016 : LesionGraftType : Type of CABG Graft (Lesions) -->
  <!-- is NULL -->
    <!-- #8017 : LocGraft : Location in Graft (Lesions) -->
  <!-- is NULL -->
    <!-- #8018 : NavGraftNatLes : Navigate through Graft to Native Lesion (Lesions) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142348" displayName="NavGraftNatLes">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #8019 : LesionComplexity : Lesion Complexity (Lesions) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100000866" displayName="LesionComplexity">
    <value codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" displayName="Non-High/Non-C Lesion" xsi:type="CD" code="100000583"/>
  </element>
    <!-- #8020 : LesionLength : Lesion Length (Lesions) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013030" displayName="LesionLength">
    <value xsi:type="PQ" value="12.00" unit="mm"/>
  </element>
    <!-- #8021 : SevereCalcification : Severe Calcification (Lesions) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142350" displayName="SevereCalcification">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #8022 : BifurcationLesion : Bifurication Lesion (Lesions) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="371894001" displayName="BifurcationLesion">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #8023 : GuidewireLesion : Guidewire Across Lesion (Lesions) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100000851" displayName="GuidewireLesion">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #8024 : DeviceDeployed : Device Deployed (Lesions) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142349" displayName="DeviceDeployed">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #8025 : StenosisPostProc : Stenosis Post-Procedure (Lesions) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142461" displayName="StenosisPostProc">
    <value xsi:type="PQ" value="0" unit="%"/>
  </element>
    <!-- #8026 : PostProcTIMI : Post-Procedure TIMI Flow (Lesions) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013016" displayName="PostProcTIMI">
    <value codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" displayName="TIMI - 3" xsi:type="CD" code="371865008"/>
  </element>
                                           </section>
                                           <section code="DEVICES" displayName="Devices 1">
                                                <!--Element Ref#8027-->
                                                <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="2.16.840.1.113883.3.3478.4.851" displayName="ICDevCounter">
                                                  <value xsi:type="CTR" value="1"/>
                                                </element>
                                                <!--Element Ref#8028-->
                                                <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142374" displayName="ICDevID">
                                                  <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1.101" codeSystemName="ACC NCDR Intracoronary Devices" code="5295" displayName="Onyx Frontier"/>
                                                </element>
                                                <!--Element Ref#8029-->
                                                <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142398" displayName="ICDevCounterAssn">
                                                     <value xsi:type="NUM" value="1"/>
                                                </element>
                                                <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142375" displayName="DeviceDiameter">
                                                  <value xsi:type="PQ" value="3.50" unit="mm"/>
                                                </element>
                                                <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142376" displayName="DeviceLength">
                                                  <value xsi:type="PQ" value="12" unit="mm"/>
                                                </element>
                                           </section>
                                           <section code="DEVICES" displayName="Devices 2">
                                                <!--Element Ref#8027-->
                                                <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="2.16.840.1.113883.3.3478.4.851" displayName="ICDevCounter">
                                                  <value xsi:type="CTR" value="2"/>
                                                </element>
                                                <!--Element Ref#8028-->
                                                <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142374" displayName="ICDevID">
                                                  <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1.101" codeSystemName="ACC NCDR Intracoronary Devices" code="255" displayName="NC Euphora RX"/>
                                                </element>
                                                <!--Element Ref#8029-->
                                                <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142398" displayName="ICDevCounterAssn">
                                                     <value xsi:type="NUM" value="1"/>
                                                </element>
                                                <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142375" displayName="DeviceDiameter">
                                                  <value xsi:type="PQ" value="3.50" unit="mm"/>
                                                </element>
                                                <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142376" displayName="DeviceLength">
                                                  <value xsi:type="PQ" value="12" unit="mm"/>
                                                </element>
                                           </section>
                                           <section code="DEVICES" displayName="Devices 3">
                                                <!--Element Ref#8027-->
                                                <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="2.16.840.1.113883.3.3478.4.851" displayName="ICDevCounter">
                                                  <value xsi:type="CTR" value="3"/>
                                                </element>
                                                <!--Element Ref#8028-->
                                                <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142374" displayName="ICDevID">
                                                  <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1.101" codeSystemName="ACC NCDR Intracoronary Devices" code="255" displayName="NC Euphora RX"/>
                                                </element>
                                                <!--Element Ref#8029-->
                                                <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142398" displayName="ICDevCounterAssn">
                                                     <value xsi:type="NUM" value="1"/>
                                                </element>
                                                <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142375" displayName="DeviceDiameter">
                                                  <value xsi:type="PQ" value="4.00" unit="mm"/>
                                                </element>
                                                <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142376" displayName="DeviceLength">
                                                  <value xsi:type="PQ" value="8" unit="mm"/>
                                                </element>
                                           </section>
          </section>
        <!-- end PCIPROC a-->
          <section code="INTPOSTEVENT" displayName="K. INTRA AND POST-PROCEDURE EVENTS">
<!--Intra and Post Procedure Events-->
    <!-- #9145 : PerfSeg : Perforation (Intra and Post Procedure Events) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="234010000" displayName="PerfSeg">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #9146 : DissectionSeg : Significant Dissection (Intra and Post Procedure Events) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100000883" displayName="DissectionSeg">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #9275 : PostTransfusion : Packed Red Blood Cell Transfusion (Intra and Post Procedure Events) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="71493000" displayName="PostTransfusion">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #9276 : PRBCUnits : Number of units of PRBCs transfused (Intra and Post Procedure Events) -->
  <!-- is NULL -->
    <!-- #9277 : TransfusPostPCI : Transfusion PCI (Intra and Post Procedure Events) -->
  <!-- is NULL -->
    <!-- #9278 : TransfusionPostSurg : Transfusion Surgery (Intra and Post Procedure Events) -->
  <!-- is NULL -->
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #1">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014076" displayName="New Requirement for Dialysis"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #2">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142371" displayName="Bleeding - Other"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #3">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142419" displayName="Other Vascular Complications Requiring Treatment"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #4">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142440" displayName="Bleeding - Access Site"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #5">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="22298006" displayName="Myocardial Infarction"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #6">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="230706003" displayName="Stroke - Hemorrhagic"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #7">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="230713003" displayName="Stroke - Undetermined"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #8">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="35304003" displayName="Cardiac Tamponade"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #9">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="385494008" displayName="Bleeding - Hematoma at Access Site"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #10">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="410429000" displayName="Cardiac Arrest"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #11">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="417941003" displayName="Bleeding - Genitourinary"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #12">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="422504002" displayName="Stroke - Ischemic"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #13">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="74474003" displayName="Bleeding - Gastrointestinal"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #14">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="84114007" displayName="Heart Failure"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #15">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="89138009" displayName="Cardiogenic Shock"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          <section code="IPPEVENTS" displayName="Intra and Post-Procedure Events #16">
            <!--Element Ref#9001-->
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142478" displayName="PostProcEvent">
                             <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="95549001" displayName="Bleeding - Retroperitoneal"/>
            </element>
            <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142479" displayName="PostProcOccurred">
            <value xsi:type="BL" value="false"/>
            </element>
  <!-- is NULL -->
          </section>
          </section>
        <!-- end INTPOSTEVENT a-->
        </section>
        <!-- end PROCINFO a-->
        <section code="DISCHARGE" displayName="L. DISCHARGE">
<!--Discharge-->
    <!-- #10030 : HospIntervention : Interventions this Hospitalization (Discharge) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001283" displayName="HospIntervention">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- # : HospInterventionType : Type of Intervention this Hospitalization (Discharge) -->
  <!-- is NULL -->
    <!-- #10035 : CABGStatus : CABG Status (Discharge) -->
  <!-- is NULL -->
    <!-- #10036 : CABGIndication : CABG Indication (Discharge) -->
  <!-- is NULL -->
    <!-- #10011 : CABGDateTime : CABG Date/Time (Discharge) -->
  <!-- is NULL -->
    <!-- #10060 : DCCreatinine : Creatinine (Discharge) -->
  <!-- is NULL -->
    <!-- #10061 : DCCreatinineND : Creatinine Not Drawn (Discharge) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="2160-0" displayName="DCCreatinineND">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #10065 : DCHgb : Hemoglobin (Discharge) -->
  <!-- is NULL -->
    <!-- #10066 : DCHgbND : Hemoglobin Not Drawn (Discharge) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="718-7" displayName="DCHgbND">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- #10101 : DCDate : Discharge Date/Time (Discharge) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="1000142457" displayName="DCDateTime">
    <value xsi:type="TS" value="2023-07-11T15:09:00"/>
  </element>
    <!-- #10075 : DC_Comfort : Comfort Measures Only (Discharge) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="133918004" displayName="DC_Comfort">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #10105 : DCSTATUS : DCSTATUS (Discharge) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="75527-2" displayName="DCStatus">
    <value codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" displayName="Alive" xsi:type="CD" code="438949009"/>
  </element>
    <!-- #10110 : DCLocation : Discharge Location (Discharge) -->
  <element codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" code="75528-0" displayName="DCLocation">
    <value displayName="Home" code="01" codeSystem="2.16.840.1.113883.12.112" codeSystemName="HL7 Discharge disposition" xsi:type="CD"/>
  </element>
    <!-- #10112 : CABGPlannedDC : CABG Planned after Discharge (Discharge) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="10001424792" displayName="CABGPlannedDC">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #10115 : DCHospice : Hospice Care (Discharge) -->
  <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="385763009" displayName="DCHospice">
    <value xsi:type="BL" value="false"/>
  </element>
    <!-- #10116 : DC_CardRehab : Cardiac Rehab Referral (Discharge) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014067" displayName="DC_CardRehab">
    <value codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" displayName="Yes" xsi:type="CD" code="100013072"/>
  </element>
    <!-- #10117 : DC_LOC : Level of Consciousness (Discharge) (Discharge) -->
  <!-- is NULL -->
    <!-- #10120 : DeathProcedure : Death During the Procedure (Discharge) -->
  <!-- is NULL -->
    <!-- #10125 : DeathCause : Cause of Death (Discharge) -->
  <!-- is NULL -->
    <!-- #10220 : DC_MedReconCompleted : Discharge Medication Reconciliation Completed (Discharge) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013084" displayName="DC_MedReconCompleted">
    <value xsi:type="BL" value="true"/>
  </element>
    <!-- # : DC_MedReconciled : Reconciled Medications (Discharge) -->
  <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013085" displayName="DC_MedReconciled">
    <value codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" displayName="Prescriptions: Cardiac" xsi:type="CD" code="100013086"/>
    <value codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" displayName="Prescriptions: Non-Cardiac" xsi:type="CD" code="100013087"/>
    <value codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" displayName="Over the Counter (OTC) Medications" xsi:type="CD" code="100013088"/>
    <value codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" displayName="Vitamins/Minerals" xsi:type="CD" code="100013089"/>
  </element>
<!--Medications-->
                                                <section code="DISCHMED" displayName="Discharge Med 1">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="DC_MedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014161" displayName="Non-Statin"/>
                                                   </element>
                                                     <!--Element Ref#10205-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="DC_MedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001048" displayName="Not Prescribed - No Reason "/>
                                                     </element>
                                               </section>
                                                <section code="DISCHMED" displayName="Discharge Med 2">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="DC_MedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="10594" displayName="Ticlopidine"/>
                                                   </element>
                                                     <!--Element Ref#10205-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="DC_MedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001048" displayName="Not Prescribed - No Reason "/>
                                                     </element>
                                               </section>
                                                <section code="DISCHMED" displayName="Discharge Med 3">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="DC_MedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="1114195" displayName="Rivaroxaban"/>
                                                   </element>
                                                     <!--Element Ref#10205-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="DC_MedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001048" displayName="Not Prescribed - No Reason "/>
                                                     </element>
                                               </section>
                                                <section code="DISCHMED" displayName="Discharge Med 4">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="DC_MedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="1116632" displayName="Ticagrelor"/>
                                                   </element>
                                                     <!--Element Ref#10205-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="DC_MedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001048" displayName="Not Prescribed - No Reason "/>
                                                     </element>
                                               </section>
                                                <section code="DISCHMED" displayName="Discharge Med 5">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="DC_MedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="11289" displayName="Warfarin"/>
                                                   </element>
                                                     <!--Element Ref#10205-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="DC_MedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001048" displayName="Not Prescribed - No Reason "/>
                                                     </element>
                                               </section>
                                                <section code="DISCHMED" displayName="Discharge Med 6">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="DC_MedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="1191" displayName="Aspirin"/>
                                                   </element>
                                                     <!--Element Ref#10205-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="DC_MedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001247" displayName="Yes"/>
                                                     </element>
                                               </section>
                                                <section code="DISCHMED" displayName="Discharge Med 7">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="DC_MedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="1364430" displayName="Apixaban"/>
                                                   </element>
                                                     <!--Element Ref#10205-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="DC_MedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001048" displayName="Not Prescribed - No Reason "/>
                                                     </element>
                                               </section>
                                                <section code="DISCHMED" displayName="Discharge Med 8">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="DC_MedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="1537034" displayName="Vorapaxar"/>
                                                   </element>
                                                     <!--Element Ref#10205-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="DC_MedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001048" displayName="Not Prescribed - No Reason "/>
                                                     </element>
                                               </section>
                                                <section code="DISCHMED" displayName="Discharge Med 9">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="DC_MedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="1546356" displayName="Dabigatran"/>
                                                   </element>
                                                     <!--Element Ref#10205-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="DC_MedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001048" displayName="Not Prescribed - No Reason "/>
                                                     </element>
                                               </section>
                                                <section code="DISCHMED" displayName="Discharge Med 10">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="DC_MedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="1599538" displayName="Edoxaban"/>
                                                   </element>
                                                     <!--Element Ref#10205-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="DC_MedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001048" displayName="Not Prescribed - No Reason "/>
                                                     </element>
                                               </section>
                                                <section code="DISCHMED" displayName="Discharge Med 11">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="DC_MedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="1659152" displayName="Alirocumab"/>
                                                   </element>
                                                     <!--Element Ref#10205-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="DC_MedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001048" displayName="Not Prescribed - No Reason "/>
                                                     </element>
                                               </section>
                                                <section code="DISCHMED" displayName="Discharge Med 12">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="DC_MedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="1665684" displayName="Evolocumab"/>
                                                   </element>
                                                     <!--Element Ref#10205-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="DC_MedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001048" displayName="Not Prescribed - No Reason "/>
                                                     </element>
                                               </section>
                                                <section code="DISCHMED" displayName="Discharge Med 13">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="DC_MedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="32968" displayName="Clopidogrel"/>
                                                   </element>
                                                     <!--Element Ref#10205-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="DC_MedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001247" displayName="Yes"/>
                                                     </element>
                                               </section>
                                                <section code="DISCHMED" displayName="Discharge Med 14">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="DC_MedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="33252009" displayName="Beta Blocker"/>
                                                   </element>
                                                     <!--Element Ref#10205-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="DC_MedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001048" displayName="Not Prescribed - No Reason "/>
                                                     </element>
                                               </section>
                                                <section code="DISCHMED" displayName="Discharge Med 15">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="DC_MedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="372913009" displayName="Angiotensin II Receptor Blocker"/>
                                                   </element>
                                                     <!--Element Ref#10205-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="DC_MedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001048" displayName="Not Prescribed - No Reason "/>
                                                     </element>
                                               </section>
                                                <section code="DISCHMED" displayName="Discharge Med 16">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="DC_MedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="41549009" displayName="Angiotensin Converting Enzyme In"/>
                                                   </element>
                                                     <!--Element Ref#10205-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="DC_MedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001048" displayName="Not Prescribed - No Reason "/>
                                                     </element>
                                               </section>
                                                <section code="DISCHMED" displayName="Discharge Med 17">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="DC_MedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.88" codeSystemName="RxNorm" code="613391" displayName="Prasugrel"/>
                                                   </element>
                                                     <!--Element Ref#10205-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="DC_MedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001048" displayName="Not Prescribed - No Reason "/>
                                                     </element>
                                               </section>
                                                <section code="DISCHMED" displayName="Discharge Med 18">
                                                   <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100013057" displayName="DC_MedID">
                                                      <value xsi:type="CD" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="96302009" displayName="Statin"/>
                                                   </element>
                                                     <!--Element Ref#10205-->
                                                     <element codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" code="432102000" displayName="DC_MedAdmin">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100001247" displayName="Yes"/>
                                                     </element>
                                                     <!--Element Ref#10207-->
                                                     <element codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014233" displayName="DC_MEDDOSE">
                                                          <value xsi:type="CD" codeSystem="2.16.840.1.113883.3.3478.6.1" codeSystemName="ACC NCDR" code="100014034" displayName="High Intensity Dose"/>
                                                     </element>
                                               </section>
        </section>
      </episode>
        <!-- end Episode a-->
  </patient>
        <!-- end Patient a-->

  
</registryDocument>