[{"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "AccessSite", "valueDataDict": "Radial"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "AdmFName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "AdmLName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "AdmNPI", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "AntiArrhyTherapy", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "ArrivalDateTime", "valueDataDict": "2023-09-11T06:45:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "AttFName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "AttLName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "AttNPI", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "BifurcationLesion", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "BifurcationLesion", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "BifurcationLesion", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "CABGPlannedDC", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "CAInHosp", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "CAOutHospital", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "CATransferFac", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "CPSxAssess", "valueDataDict": "Typical Angina"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "CSHAScale", "valueDataDict": "4: Vulnerable"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "CVInstability", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "CVInstabilityType", "valueDataDict": "Persistent Ischemic Symptoms (chest pain, STE)"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "CalciumScoreAssessed", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "CardiacCTA", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "CathLabVisitIndication", "valueDataDict": "ACS <= 24 hrs|New Onset Angina <= 2 months|Suspected CAD"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "ChronicOcclusion", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "ChronicOcclusion", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "ChronicOcclusionUnk", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "ChronicOcclusionUnk", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "ClosureCounter", "valueDataDict": "1"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "ClosureDevID", "valueDataDict": "2"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "ClosureMethodNA", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "ConcomProc", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "ContrastVol", "valueDataDict": "420"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "ContrastVol_unit", "valueDataDict": "mL"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "Crossover", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "CulpritArteryUnk", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "CulpritArteryUnk", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "CulpritArteryUnk", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "CurrentDialysis", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "DCCreatinine", "valueDataDict": "0.93"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "DCCreatinineND", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "DCCreatinine_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "DCDateTime", "valueDataDict": "2023-09-15T08:45:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "DCFName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "DCHgb", "valueDataDict": "13.1"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "DCHgbND", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "DCHgb_unit", "valueDataDict": "g/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "DCHospice", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "DCLName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "DCLocation", "valueDataDict": "Home"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "DCNPI", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "DCStatus", "valueDataDict": "Alive"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_CardRehab", "valueDataDict": "No - Health Care System Reason Documented"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_Comfort", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 13, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 14, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 15, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 16, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 17, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 18, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 18, "shortName": "DC_MedDose", "valueDataDict": "High Intensity Dose"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Angiotensin Converting Enzyme Inhibitor"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedID", "valueDataDict": "Vorapaxar"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedID", "valueDataDict": "Angiotensin II Receptor Blocker"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedID", "valueDataDict": "Beta Blocker"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedID", "valueDataDict": "Non-Statin"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedID", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedID", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedID", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedID", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedID", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 13, "shortName": "DC_MedID", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 14, "shortName": "DC_MedID", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 15, "shortName": "DC_MedID", "valueDataDict": "Ticlopidine"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 16, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 17, "shortName": "DC_MedID", "valueDataDict": "Evolocumab"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 18, "shortName": "DC_MedID", "valueDataDict": "Statin"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedReconCompleted", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedReconciled", "valueDataDict": "Prescriptions: Cardiac|Prescriptions: Non-Cardiac|Over the Counter (OTC) Medications"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "DCathFName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "DCathLName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "DCathNPI", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "DOB", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "DeviceDeployed", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "DeviceDeployed", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "DeviceDeployed", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "<PERSON>ce<PERSON>iam<PERSON>", "valueDataDict": "2.5"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "<PERSON>ce<PERSON>iam<PERSON>", "valueDataDict": "3.5"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "<PERSON>ce<PERSON>iam<PERSON>", "valueDataDict": "2.5"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 4, "shortName": "<PERSON>ce<PERSON>iam<PERSON>", "valueDataDict": "2"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 5, "shortName": "<PERSON>ce<PERSON>iam<PERSON>", "valueDataDict": "2.5"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 6, "shortName": "<PERSON>ce<PERSON>iam<PERSON>", "valueDataDict": "2.5"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 7, "shortName": "<PERSON>ce<PERSON>iam<PERSON>", "valueDataDict": "3"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 8, "shortName": "<PERSON>ce<PERSON>iam<PERSON>", "valueDataDict": "4"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 9, "shortName": "<PERSON>ce<PERSON>iam<PERSON>", "valueDataDict": "4.5"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 10, "shortName": "<PERSON>ce<PERSON>iam<PERSON>", "valueDataDict": "4"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 12, "shortName": "<PERSON>ce<PERSON>iam<PERSON>", "valueDataDict": "5"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 13, "shortName": "<PERSON>ce<PERSON>iam<PERSON>", "valueDataDict": "4.5"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "DeviceDiameter_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "DeviceDiameter_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "DeviceDiameter_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 4, "shortName": "DeviceDiameter_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 5, "shortName": "DeviceDiameter_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 6, "shortName": "DeviceDiameter_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 7, "shortName": "DeviceDiameter_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 8, "shortName": "DeviceDiameter_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 9, "shortName": "DeviceDiameter_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 10, "shortName": "DeviceDiameter_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 12, "shortName": "DeviceDiameter_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 13, "shortName": "DeviceDiameter_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "12"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "20"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "20"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 4, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "12"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 5, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "48"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 6, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "16"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 7, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "20"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 8, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "20"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 9, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "8"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 10, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "20"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 12, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "12"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 13, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "20"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "DeviceLength_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "DeviceLength_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "DeviceLength_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 4, "shortName": "DeviceLength_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 5, "shortName": "DeviceLength_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 6, "shortName": "DeviceLength_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 7, "shortName": "DeviceLength_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 8, "shortName": "DeviceLength_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 9, "shortName": "DeviceLength_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 10, "shortName": "DeviceLength_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 12, "shortName": "DeviceLength_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 13, "shortName": "DeviceLength_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "Diabetes", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "DiagCorAngio", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "DissectionSeg", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "Dominance", "valueDataDict": "Right"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "Dyslipidemia", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "ECAssessMethod", "valueDataDict": "ECG"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "ECGFindings", "valueDataDict": "ST deviation >= 0.5 mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "ECGResults", "valueDataDict": "Abnormal"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "EnrolledStudy", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "FamilyHxCAD", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "FirstDevActiDateTime", "valueDataDict": "2022-02-03T14:50:00"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "FirstName", "valueDataDict": "Whats in the name?"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "FluoroDoseDAP", "valueDataDict": "19100"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "FluoroDoseDAP_unit", "valueDataDict": "cGy/cm2"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "FluoroDoseKerm", "valueDataDict": "2469"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "FluoroDoseKerm_unit", "valueDataDict": "mGy"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "FluoroTime", "valueDataDict": "41.3"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "FluoroTime_unit", "valueDataDict": "min"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "GuidewireLesion", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "GuidewireLesion", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "GuidewireLesion", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "HGB", "valueDataDict": "14"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "HGBND", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "HGB_unit", "valueDataDict": "g/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "HealthIns", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "Height", "valueDataDict": "188"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "Height_unit", "valueDataDict": "cm"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "HospIntervention", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "HxCVD", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "HxChronicLungDisease", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "HxHF", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "HxMI", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "Hypertension", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "ICDev<PERSON><PERSON>nter", "valueDataDict": "1"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "ICDev<PERSON><PERSON>nter", "valueDataDict": "2"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "ICDev<PERSON><PERSON>nter", "valueDataDict": "3"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 4, "shortName": "ICDev<PERSON><PERSON>nter", "valueDataDict": "4"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 5, "shortName": "ICDev<PERSON><PERSON>nter", "valueDataDict": "5"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 6, "shortName": "ICDev<PERSON><PERSON>nter", "valueDataDict": "6"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 7, "shortName": "ICDev<PERSON><PERSON>nter", "valueDataDict": "7"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 8, "shortName": "ICDev<PERSON><PERSON>nter", "valueDataDict": "8"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 9, "shortName": "ICDev<PERSON><PERSON>nter", "valueDataDict": "9"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 10, "shortName": "ICDev<PERSON><PERSON>nter", "valueDataDict": "10"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 11, "shortName": "ICDev<PERSON><PERSON>nter", "valueDataDict": "11"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 12, "shortName": "ICDev<PERSON><PERSON>nter", "valueDataDict": "12"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 13, "shortName": "ICDev<PERSON><PERSON>nter", "valueDataDict": "13"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "ICDevCounterAssn", "valueDataDict": "1|2|3"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "ICDevCounterAssn", "valueDataDict": "1"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "ICDevCounterAssn", "valueDataDict": "2"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 4, "shortName": "ICDevCounterAssn", "valueDataDict": "3"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 5, "shortName": "ICDevCounterAssn", "valueDataDict": "2"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 6, "shortName": "ICDevCounterAssn", "valueDataDict": "2"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 7, "shortName": "ICDevCounterAssn", "valueDataDict": "2"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 8, "shortName": "ICDevCounterAssn", "valueDataDict": "1|2"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 9, "shortName": "ICDevCounterAssn", "valueDataDict": "1"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 10, "shortName": "ICDevCounterAssn", "valueDataDict": "1"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 11, "shortName": "ICDevCounterAssn", "valueDataDict": "3"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 12, "shortName": "ICDevCounterAssn", "valueDataDict": "1"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 13, "shortName": "ICDevCounterAssn", "valueDataDict": "2"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "ICDevID", "valueDataDict": "227"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "ICDevID", "valueDataDict": "4591"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "ICDevID", "valueDataDict": "227"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 4, "shortName": "ICDevID", "valueDataDict": "227"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 5, "shortName": "ICDevID", "valueDataDict": "4591"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 6, "shortName": "ICDevID", "valueDataDict": "4591"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 7, "shortName": "ICDevID", "valueDataDict": "262"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 8, "shortName": "ICDevID", "valueDataDict": "262"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 9, "shortName": "ICDevID", "valueDataDict": "262"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 10, "shortName": "ICDevID", "valueDataDict": "262"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 11, "shortName": "ICDevID", "valueDataDict": "961"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 12, "shortName": "ICDevID", "valueDataDict": "262"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 13, "shortName": "ICDevID", "valueDataDict": "262"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "LastName", "valueDataDict": "Whats in the name?"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "LeftHeartCath", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "LesionComplexity", "valueDataDict": "High/C"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "LesionComplexity", "valueDataDict": "High/C"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "LesionComplexity", "valueDataDict": "Non-High/Non-C"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "1"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "2"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "3"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "LesionGraft", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "LesionGraft", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "LesionGraft", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "20"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "62"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "12"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "LesionLength_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "LesionLength_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "LesionLength_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "LipidsHDLND", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "LipidsTCND", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "MultiVesselDz", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "NVAdjuncMeasObtained", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "NVAdjuncMeasObtained", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "NVAdjuncMeasObtained", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 4, "shortName": "NVAdjuncMeasObtained", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "NVCoroVesselStenosis", "valueDataDict": "100"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "NVCoroVesselStenosis", "valueDataDict": "100"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "NVCoroVesselStenosis", "valueDataDict": "100"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 4, "shortName": "NVCoroVesselStenosis", "valueDataDict": "70"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "NVCoroVesselStenosis_unit", "valueDataDict": "%"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "NVCoroVesselStenosis_unit", "valueDataDict": "%"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "NVCoroVesselStenosis_unit", "valueDataDict": "%"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 4, "shortName": "NVCoroVesselStenosis_unit", "valueDataDict": "%"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "NVSegmentID", "valueDataDict": "12 - pLAD"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "NVSegmentID", "valueDataDict": "13 - mLAD"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "NVSegmentID", "valueDataDict": "14 - dLAD"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 4, "shortName": "NVSegmentID", "valueDataDict": "15 - 1st <PERSON><PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "NVStenosis", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "NavGraftNatLes", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "NavGraftNatLes", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "NavGraftNatLes", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "OtherID", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PCIDecision", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PCIFName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PCIIndication", "valueDataDict": "STEMI - Immediate PCI for Acute STEMI"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PCILName", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PCINPI", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PCIProc", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PCIStatus", "valueDataDict": "Emergency"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PatientTransPCI", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PerfSeg", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcCreat", "valueDataDict": "0.94"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcCreatND", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcCreat_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "PostProcDateTime", "valueDataDict": "2021-12-29T17:07:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 5, "shortName": "PostProcDateTime", "valueDataDict": "2021-12-29T17:10:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 6, "shortName": "PostProcDateTime", "valueDataDict": "2021-12-29T19:10:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcEvent", "valueDataDict": "Bleeding - Access Site"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "PostProcEvent", "valueDataDict": "Bleeding - Gastrointestinal"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "PostProcEvent", "valueDataDict": "Bleeding - Genitourinary"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 4, "shortName": "PostProcEvent", "valueDataDict": "Bleeding - Other"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 5, "shortName": "PostProcEvent", "valueDataDict": "Bleeding - Retroperitoneal"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 6, "shortName": "PostProcEvent", "valueDataDict": "Bleeding - Retroperitoneal"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 7, "shortName": "PostProcEvent", "valueDataDict": "Cardiac Arrest"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 8, "shortName": "PostProcEvent", "valueDataDict": "Cardiogenic Shock"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 9, "shortName": "PostProcEvent", "valueDataDict": "Heart Failure"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 10, "shortName": "PostProcEvent", "valueDataDict": "Myocardial Infarction"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 11, "shortName": "PostProcEvent", "valueDataDict": "New Requirement for Dialysis"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 12, "shortName": "PostProcEvent", "valueDataDict": "Stroke - Hemorrhagic"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 13, "shortName": "PostProcEvent", "valueDataDict": "Stroke - Ischemic"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 14, "shortName": "PostProcEvent", "valueDataDict": "Stroke - Undetermined"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 15, "shortName": "PostProcEvent", "valueDataDict": "Bleeding - Hematoma at Access Site"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 16, "shortName": "PostProcEvent", "valueDataDict": "Cardiac Tamponade"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 17, "shortName": "PostProcEvent", "valueDataDict": "Other Vascular Complications Requiring Treatment"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcHgb", "valueDataDict": "13.1"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcHgbND", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcHgb_unit", "valueDataDict": "g/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "PostProcOccurred", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 4, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 5, "shortName": "PostProcOccurred", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 6, "shortName": "PostProcOccurred", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 7, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 8, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 9, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 10, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 11, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 12, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 13, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 14, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 15, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 16, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 17, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcTIMI", "valueDataDict": "TIMI-3"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "PostProcTIMI", "valueDataDict": "TIMI-3"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "PostProcTIMI", "valueDataDict": "TIMI-3"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcTnI", "valueDataDict": "97.14"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcTnIND", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcTnI_unit", "valueDataDict": "ng/mL"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcTnTND", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PostTransfusion", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PreProcCreat", "valueDataDict": "1.04"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PreProcCreatND", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PreProcCreat_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PreProcLVEFAssessed", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PreProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "PreProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "PreProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 4, "shortName": "PreProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 5, "shortName": "PreProcMedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 6, "shortName": "PreProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 7, "shortName": "PreProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 8, "shortName": "PreProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 9, "shortName": "PreProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 10, "shortName": "PreProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 11, "shortName": "PreProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 12, "shortName": "PreProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PreProcMedID", "valueDataDict": "Angiotensin Converting Enzyme Inhibitor"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "PreProcMedID", "valueDataDict": "Sacubitril and Valsartan"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "PreProcMedID", "valueDataDict": "Ranolazine"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 4, "shortName": "PreProcMedID", "valueDataDict": "Antiarrhythmic Agent Other"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 5, "shortName": "PreProcMedID", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 6, "shortName": "PreProcMedID", "valueDataDict": "Angiotensin II Receptor Blocker"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 7, "shortName": "PreProcMedID", "valueDataDict": "Beta Blocker"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 8, "shortName": "PreProcMedID", "valueDataDict": "Calcium Channel Blocking Agent"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 9, "shortName": "PreProcMedID", "valueDataDict": "<PERSON> Acting Nitrate"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 10, "shortName": "PreProcMedID", "valueDataDict": "Non-Statin"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 11, "shortName": "PreProcMedID", "valueDataDict": "Proprotein Convertase Subtilisin Kexin Type 9 Inhibitor"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 12, "shortName": "PreProcMedID", "valueDataDict": "Statin"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PreProcTIMI", "valueDataDict": "TIMI-0"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "PreProcTIMI", "valueDataDict": "TIMI-0"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "PreProcTIMI", "valueDataDict": "TIMI-1"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PreProcTnI", "valueDataDict": "0.02"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PreProcTnIND", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PreProcTnI_unit", "valueDataDict": "ng/mL"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PreProcTnTND", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PrevTreatedLesion", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "PrevTreatedLesion", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "PrevTreatedLesion", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorCABG", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PriorDxAngioProc", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorPAD", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorPCI", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 4, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 5, "shortName": "ProcMedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 6, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 7, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 8, "shortName": "ProcMedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 9, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 10, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 11, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 12, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 13, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 14, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 15, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 16, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcMedID", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "ProcMedID", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "ProcMedID", "valueDataDict": "Fondaparinux"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 4, "shortName": "ProcMedID", "valueDataDict": "Low Molecular Weight Heparin"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 5, "shortName": "ProcMedID", "valueDataDict": "Unfractionated Heparin"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 6, "shortName": "ProcMedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 7, "shortName": "ProcMedID", "valueDataDict": "Vorapaxar"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 8, "shortName": "ProcMedID", "valueDataDict": "Glycoprotein IIb IIIa Inhibitors"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 9, "shortName": "ProcMedID", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 10, "shortName": "ProcMedID", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 11, "shortName": "ProcMedID", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 12, "shortName": "ProcMedID", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 13, "shortName": "ProcMedID", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 14, "shortName": "ProcMedID", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 15, "shortName": "ProcMedID", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 16, "shortName": "ProcMedID", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcSystolicBP", "valueDataDict": "124"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcSystolicBP_unit", "valueDataDict": "mm[Hg]"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcedureEndDateTime", "valueDataDict": "2023-09-11T10:45:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcedureStartDateTime", "valueDataDict": "2023-09-11T09:45:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "PtPCIDelayReason", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "PtRestriction2", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceAmIndian", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Race<PERSON>ian", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceBlack", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceNatHaw", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "true"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "SSNNA", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "SegmentID", "valueDataDict": "12 - pLAD"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "SegmentID", "valueDataDict": "13 - mLAD|14 - dLAD"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "SegmentID", "valueDataDict": "15 - 1st <PERSON><PERSON>"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "SevereCalcification", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "SevereCalcification", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "SevereCalcification", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Sex", "valueDataDict": "Male"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "StemiFirstNoted", "valueDataDict": "First ECG"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "StenosisPostProc", "valueDataDict": "0"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "StenosisPostProc", "valueDataDict": "0"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "StenosisPostProc", "valueDataDict": "0"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "StenosisPostProc_unit", "valueDataDict": "%"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "StenosisPostProc_unit", "valueDataDict": "%"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "StenosisPostProc_unit", "valueDataDict": "%"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "StenosisPriorTreat", "valueDataDict": "100"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "StenosisPriorTreat", "valueDataDict": "100"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "StenosisPriorTreat", "valueDataDict": "70"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "StenosisPriorTreat_unit", "valueDataDict": "%"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 2, "shortName": "StenosisPriorTreat_unit", "valueDataDict": "%"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 3, "shortName": "StenosisPriorTreat_unit", "valueDataDict": "%"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "StressPerformed", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "StressTestDate", "valueDataDict": "2021-03-19"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "StressTestResult", "valueDataDict": "Negative"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "StressTestType", "valueDataDict": "Stress Echocardiogram"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "SymptomDate", "valueDataDict": "2022-02-03"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "SymptomTime", "valueDataDict": "13:30:00.0000000-04:00"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "SymptomTimeUnk", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "TobaccoUse", "valueDataDict": "Unknown if ever smoked"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "VSupport", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 1, "IncrementalId": 1, "shortName": "VenousAccess", "valueDataDict": "false"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "Weight", "valueDataDict": "88.5"}, {"NCDRPatientId": "12345", "EpisodeKey": "52b016aa-1010-4924-9b8e-4f70ba3dc18f", "VisitId": 0, "IncrementalId": 1, "shortName": "Weight_unit", "valueDataDict": "kg"}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCode", "valueDataDict": ""}, {"NCDRPatientId": "12345", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCodeNA", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "AccessSite", "valueDataDict": "Femoral"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "AdmFName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "AdmLName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "AdmNPI", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "AntiArrhyTherapy", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "ArrivalDateTime", "valueDataDict": "2023-10-11T06:10:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "AttFName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "AttLName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "AttMName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "AttNPI", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "BifurcationLesion", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "CABGPlannedDC", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "CAInHosp", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "CAOutHospital", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "CATransferFac", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "CPSxAssess", "valueDataDict": "Typical Angina"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "CSHAScale", "valueDataDict": "6: <PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "CVInstability", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "CVInstabilityType", "valueDataDict": "Persistent Ischemic Symptoms (chest pain, STE)"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "CalciumScoreAssessed", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "CardiacCTA", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "CathLabVisitIndication", "valueDataDict": "ACS > 24 hrs|New Onset Angina <= 2 months"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "ChronicOcclusion", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "ChronicOcclusionUnk", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "ClosureCounter", "valueDataDict": "1"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "ClosureDevID", "valueDataDict": "9"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "ClosureMethodNA", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "ConcomProc", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "ConcomProcType", "valueDataDict": "Right Heart Cath"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "ContrastVol", "valueDataDict": "240"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "ContrastVol_unit", "valueDataDict": "mL"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "Crossover", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "CulpritArtery", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "CulpritArteryUnk", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "CurrentDialysis", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "DCCreatinine", "valueDataDict": "1.56"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "DCCreatinineND", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "DCCreatinine_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "DCDateTime", "valueDataDict": "2023-10-15T06:45:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "DCFName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "DCHgb", "valueDataDict": "16.1"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "DCHgbND", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "DCHgb_unit", "valueDataDict": "g/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "DCHospice", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "DCLName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "DCLocation", "valueDataDict": "Skilled Nursing facility"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "DCNPI", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "DCStatus", "valueDataDict": "Alive"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_CardRehab", "valueDataDict": "No - Reason Not Documented"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_Comfort", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 13, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 14, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 15, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 16, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 17, "shortName": "DC_MedAdmin", "valueDataDict": "Yes - Prescribed"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 18, "shortName": "DC_MedAdmin", "valueDataDict": "Not Prescribed - No Reason"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 17, "shortName": "DC_MedDose", "valueDataDict": "High Intensity Dose"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedID", "valueDataDict": "Angiotensin Converting Enzyme Inhibitor"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 2, "shortName": "DC_MedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 3, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 4, "shortName": "DC_MedID", "valueDataDict": "Vorapaxar"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 5, "shortName": "DC_MedID", "valueDataDict": "Angiotensin II Receptor Blocker"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 6, "shortName": "DC_MedID", "valueDataDict": "Beta Blocker"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 7, "shortName": "DC_MedID", "valueDataDict": "Non-Statin"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 8, "shortName": "DC_MedID", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 9, "shortName": "DC_MedID", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 10, "shortName": "DC_MedID", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 11, "shortName": "DC_MedID", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 12, "shortName": "DC_MedID", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 13, "shortName": "DC_MedID", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 14, "shortName": "DC_MedID", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 15, "shortName": "DC_MedID", "valueDataDict": "Ticlopidine"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 16, "shortName": "DC_MedID", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 17, "shortName": "DC_MedID", "valueDataDict": "Statin"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 18, "shortName": "DC_MedID", "valueDataDict": "Evolocumab"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedReconCompleted", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "DC_MedReconciled", "valueDataDict": "Prescriptions: Cardiac|Prescriptions: Non-Cardiac|Over the Counter (OTC) Medications"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "DCathFName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "DCathLName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "DCathNPI", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "DOB", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "DeviceDeployed", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "<PERSON>ce<PERSON>iam<PERSON>", "valueDataDict": "2.5"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 3, "shortName": "<PERSON>ce<PERSON>iam<PERSON>", "valueDataDict": "2.75"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 4, "shortName": "<PERSON>ce<PERSON>iam<PERSON>", "valueDataDict": "3"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 5, "shortName": "<PERSON>ce<PERSON>iam<PERSON>", "valueDataDict": "3.5"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "DeviceDiameter_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 3, "shortName": "DeviceDiameter_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 4, "shortName": "DeviceDiameter_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 5, "shortName": "DeviceDiameter_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "15"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 3, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "38"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 4, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "15"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 5, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "15"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "DeviceLength_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 3, "shortName": "DeviceLength_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 4, "shortName": "DeviceLength_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 5, "shortName": "DeviceLength_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "Diabetes", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "DiagCorAngio", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "DissectionSeg", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "Dominance", "valueDataDict": "Co-dominant"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "Dyslipidemia", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "ECAssessMethod", "valueDataDict": "ECG"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "ECGFindings", "valueDataDict": "ST deviation >= 0.5 mm"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "ECGResults", "valueDataDict": "Abnormal"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "EnrolledStudy", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "FamilyHxCAD", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "FirstName", "valueDataDict": "Whats in the name?"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "FluoroDoseKerm", "valueDataDict": "784"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "FluoroDoseKerm_unit", "valueDataDict": "mGy"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "GuidewireLesion", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "HGB", "valueDataDict": "17"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "HGBND", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "HGB_unit", "valueDataDict": "g/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "HIPS", "valueDataDict": "Medicaid"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "HealthIns", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "Height", "valueDataDict": "183"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "Height_unit", "valueDataDict": "cm"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "HospIntervention", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "HxCVD", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "HxChronicLungDisease", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "HxHF", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "HxMI", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "Hypertension", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "ICDev<PERSON><PERSON>nter", "valueDataDict": "1"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 2, "shortName": "ICDev<PERSON><PERSON>nter", "valueDataDict": "2"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 3, "shortName": "ICDev<PERSON><PERSON>nter", "valueDataDict": "3"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 4, "shortName": "ICDev<PERSON><PERSON>nter", "valueDataDict": "4"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 5, "shortName": "ICDev<PERSON><PERSON>nter", "valueDataDict": "5"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "ICDevCounterAssn", "valueDataDict": "1"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 2, "shortName": "ICDevCounterAssn", "valueDataDict": "1"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 3, "shortName": "ICDevCounterAssn", "valueDataDict": "1"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 4, "shortName": "ICDevCounterAssn", "valueDataDict": "1"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 5, "shortName": "ICDevCounterAssn", "valueDataDict": "1"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "ICDevID", "valueDataDict": "227"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 2, "shortName": "ICDevID", "valueDataDict": "10"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 3, "shortName": "ICDevID", "valueDataDict": "268"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 4, "shortName": "ICDevID", "valueDataDict": "262"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 5, "shortName": "ICDevID", "valueDataDict": "262"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "LastName", "valueDataDict": "Whats in the name?"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "LeftHeartCath", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "LesionComplexity", "valueDataDict": "High/C"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "1"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "LesionGraft", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "38"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "LesionLength_unit", "valueDataDict": "mm"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "LipidsHDLND", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "LipidsTCND", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "MultiVesselDz", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "NVAdjuncMeasObtained", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 2, "shortName": "NVAdjuncMeasObtained", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 3, "shortName": "NVAdjuncMeasObtained", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "NVCoroVesselStenosis", "valueDataDict": "90"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 2, "shortName": "NVCoroVesselStenosis", "valueDataDict": "70"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 3, "shortName": "NVCoroVesselStenosis", "valueDataDict": "100"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "NVCoroVesselStenosis_unit", "valueDataDict": "%"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 2, "shortName": "NVCoroVesselStenosis_unit", "valueDataDict": "%"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 3, "shortName": "NVCoroVesselStenosis_unit", "valueDataDict": "%"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "NVSegmentID", "valueDataDict": "15 - 1st <PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 2, "shortName": "NVSegmentID", "valueDataDict": "16 - 2nd <PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 3, "shortName": "NVSegmentID", "valueDataDict": "21 - 2nd OM"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "NVStenosis", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "NavGraftNatLes", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "OtherID", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PCIDecision", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PCIFName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PCIIndication", "valueDataDict": "STEMI - Unstable (> 12 hrs from Sx)"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PCILName", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PCINPI", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PCIProc", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PCIStatus", "valueDataDict": "<PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PerfSeg", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcCreat", "valueDataDict": "1.79"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcCreatND", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcCreat_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 15, "shortName": "PostProcDateTime", "valueDataDict": "2021-12-30T17:07:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcEvent", "valueDataDict": "Bleeding - Access Site"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 2, "shortName": "PostProcEvent", "valueDataDict": "Bleeding - Gastrointestinal"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 3, "shortName": "PostProcEvent", "valueDataDict": "Bleeding - Genitourinary"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 4, "shortName": "PostProcEvent", "valueDataDict": "Bleeding - Other"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 5, "shortName": "PostProcEvent", "valueDataDict": "Bleeding - Retroperitoneal"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 6, "shortName": "PostProcEvent", "valueDataDict": "Cardiac Arrest"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 7, "shortName": "PostProcEvent", "valueDataDict": "Cardiogenic Shock"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 8, "shortName": "PostProcEvent", "valueDataDict": "Heart Failure"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 9, "shortName": "PostProcEvent", "valueDataDict": "Myocardial Infarction"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 10, "shortName": "PostProcEvent", "valueDataDict": "New Requirement for Dialysis"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 11, "shortName": "PostProcEvent", "valueDataDict": "Stroke - Hemorrhagic"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 12, "shortName": "PostProcEvent", "valueDataDict": "Stroke - Ischemic"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 13, "shortName": "PostProcEvent", "valueDataDict": "Stroke - Undetermined"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 14, "shortName": "PostProcEvent", "valueDataDict": "Bleeding - Hematoma at Access Site"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 15, "shortName": "PostProcEvent", "valueDataDict": "Cardiac Tamponade"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 16, "shortName": "PostProcEvent", "valueDataDict": "Other Vascular Complications Requiring Treatment"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcHgb", "valueDataDict": "12.8"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcHgbND", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcHgb_unit", "valueDataDict": "g/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 2, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 3, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 4, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 5, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 6, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 7, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 8, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 9, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 10, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 11, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 12, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 13, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 14, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 15, "shortName": "PostProcOccurred", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 16, "shortName": "PostProcOccurred", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcTIMI", "valueDataDict": "TIMI-3"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcTnIND", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PostProcTnTND", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PostTransfusion", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PreProcCreat", "valueDataDict": "1.5"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PreProcCreatND", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PreProcCreat_unit", "valueDataDict": "mg/dL"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PreProcLVEFAssessed", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PreProcMedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 2, "shortName": "PreProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 3, "shortName": "PreProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 4, "shortName": "PreProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 5, "shortName": "PreProcMedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 6, "shortName": "PreProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 7, "shortName": "PreProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 8, "shortName": "PreProcMedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 9, "shortName": "PreProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 10, "shortName": "PreProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 11, "shortName": "PreProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 12, "shortName": "PreProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PreProcMedID", "valueDataDict": "Angiotensin Converting Enzyme Inhibitor"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 2, "shortName": "PreProcMedID", "valueDataDict": "Sacubitril and Valsartan"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 3, "shortName": "PreProcMedID", "valueDataDict": "Ranolazine"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 4, "shortName": "PreProcMedID", "valueDataDict": "Antiarrhythmic Agent Other"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 5, "shortName": "PreProcMedID", "valueDataDict": "<PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 6, "shortName": "PreProcMedID", "valueDataDict": "Angiotensin II Receptor Blocker"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 7, "shortName": "PreProcMedID", "valueDataDict": "Beta Blocker"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 8, "shortName": "PreProcMedID", "valueDataDict": "Calcium Channel Blocking Agent"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 9, "shortName": "PreProcMedID", "valueDataDict": "<PERSON> Acting Nitrate"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 10, "shortName": "PreProcMedID", "valueDataDict": "Non-Statin"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 11, "shortName": "PreProcMedID", "valueDataDict": "Proprotein Convertase Subtilisin Kexin Type 9 Inhibitor"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 12, "shortName": "PreProcMedID", "valueDataDict": "Statin"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PreProcTIMI", "valueDataDict": "TIMI-0"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PreProcTnI", "valueDataDict": "31.1"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PreProcTnIND", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PreProcTnI_unit", "valueDataDict": "ng/mL"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PreProcTnTND", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PrevTreatedLesion", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorCABG", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "PriorDxAngioProc", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorPAD", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "PriorPCI", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 2, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 3, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 4, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 5, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 6, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 7, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 8, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 9, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 10, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 11, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 12, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 13, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 14, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 15, "shortName": "ProcMedAdmin", "valueDataDict": "No"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 16, "shortName": "ProcMedAdmin", "valueDataDict": "Yes"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcMedID", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 2, "shortName": "ProcMedID", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 3, "shortName": "ProcMedID", "valueDataDict": "Fondaparinux"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 4, "shortName": "ProcMedID", "valueDataDict": "Low Molecular Weight Heparin"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 5, "shortName": "ProcMedID", "valueDataDict": "Unfractionated Heparin"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 6, "shortName": "ProcMedID", "valueDataDict": "Warfarin"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 7, "shortName": "ProcMedID", "valueDataDict": "Vorapaxar"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 8, "shortName": "ProcMedID", "valueDataDict": "Glycoprotein IIb IIIa Inhibitors"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 9, "shortName": "ProcMedID", "valueDataDict": "Apixaban"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 10, "shortName": "ProcMedID", "valueDataDict": "Dabigatran"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 11, "shortName": "ProcMedID", "valueDataDict": "Edoxaban"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 12, "shortName": "ProcMedID", "valueDataDict": "Rivaroxaban"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 13, "shortName": "ProcMedID", "valueDataDict": "<PERSON><PERSON><PERSON><PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 14, "shortName": "ProcMedID", "valueDataDict": "Clopidogrel"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 15, "shortName": "ProcMedID", "valueDataDict": "Prasug<PERSON>"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 16, "shortName": "ProcMedID", "valueDataDict": "Ticagrelor"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcSystolicBP", "valueDataDict": "137"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcSystolicBP_unit", "valueDataDict": "mm[Hg]"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcedureEndDateTime", "valueDataDict": "2023-10-11T010:45:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "ProcedureStartDateTime", "valueDataDict": "2023-10-11T09:45:00"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "PtRestriction2", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceAmIndian", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Race<PERSON>ian", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceBlack", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "RaceNatHaw", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "SSNNA", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "SegmentID", "valueDataDict": "21 - 2nd OM"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "SevereCalcification", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "Sex", "valueDataDict": "Male"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "StenosisPostProc", "valueDataDict": "0"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "StenosisPostProc_unit", "valueDataDict": "%"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "StenosisPriorTreat", "valueDataDict": "100"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "StenosisPriorTreat_unit", "valueDataDict": "%"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "StressPerformed", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "StressTestDate", "valueDataDict": "2021-03-18"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "StressTestResult", "valueDataDict": "Negative"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "StressTestType", "valueDataDict": "Stress Echocardiogram"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "SymptomDate", "valueDataDict": "2022-01-18"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "SymptomTimeUnk", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "TobaccoUse", "valueDataDict": "Unknown if ever smoked"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "VSupport", "valueDataDict": "false"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 1, "IncrementalId": 1, "shortName": "VenousAccess", "valueDataDict": "true"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "Weight", "valueDataDict": "111.6"}, {"NCDRPatientId": "67890", "EpisodeKey": "42918735-2bb5-4e75-811d-cbac74f62f39", "VisitId": 0, "IncrementalId": 1, "shortName": "Weight_unit", "valueDataDict": "kg"}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCode", "valueDataDict": ""}, {"NCDRPatientId": "67890", "EpisodeKey": 0, "VisitId": 0, "IncrementalId": 1, "shortName": "ZipCodeNA", "valueDataDict": ""}]