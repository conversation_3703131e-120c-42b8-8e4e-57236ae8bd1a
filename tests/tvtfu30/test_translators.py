import unittest
from unittest.mock import patch

import pandas as pd
from pandas.testing import assert_frame_equal

from ingestion.translators.translate_tvtfu30_to_biome import TranslateTVTFU30ToBiome
from tests.mock_data import get_mock_data_file_path, load_mock_data


ingestion_file_info = {
    'id': '1',
    'client': 'client1',
    'created_on': '1709750572',
    'version': 2023091800,
    'file_name': 'tvtfu30raw',
}

ce = pd.DataFrame({
    'id': ['1'],
    'tenant_id': ['tenant1'],
    'code': ['hosp1']
})


class TestTranslateTVTFU30ToBiome(unittest.TestCase):

    @patch('ingestion.translators.translate_tvtfu30_to_biome.FileInfo.get_file_info',
           return_value=ingestion_file_info)
    def setUp(self, mock_get_file_info):
        self.dataset = 'tvtfu30'
        self.translator = TranslateTVTFU30ToBiome(
            filepath=get_mock_data_file_path('tvtfu30raw.xml', dataset=self.dataset),
            schema=None, reader=None, file_id=None, client='client1')

    def test_flatten_output(self):
        self.translator.denormalize()
        output = self.translator.output
        self.assertEqual(output.shape, (96, 143))
        self.assertEqual(output[output['NCDRPatientId'] == '345676'].shape[0], 24)
        self.assertEqual(output[output['NCDRPatientId'] == '345677'].shape[0], 24)
        self.assertEqual(output[output['NCDRPatientId'] == '345678'].shape[0], 24)
        self.assertEqual(output[output['NCDRPatientId'] == '345679'].shape[0], 24)

    def test_append_custom_fields(self):
        self.translator.denormalize()
        self.translator.append_custom_fields()
        output = self.translator.output
        self.assertEqual(output.shape, (96, 160))
        self.assertEqual(output[output['NCDRPatientId'] == '345676'].shape[0], 24)
        self.assertEqual(output[output['NCDRPatientId'] == '345677'].shape[0], 24)
        self.assertEqual(output[output['NCDRPatientId'] == '345678'].shape[0], 24)
        self.assertEqual(output[output['NCDRPatientId'] == '345679'].shape[0], 24)

    @patch('ingestion.translators.get_care_entities', return_value=ce)
    def test_translator_output(self, mock_get_care_entities):
        self.translator.denormalize()
        self.translator.append_custom_fields()
        self.translator.lower_case_fields()
        self.translator.rename_fields()
        self.translator.change_delimiters()
        self.translator.transform_field_values()
        self.translator.value_mapping()
        self.translator.post_process()
        self.translator.standardize_datetime()
        self.translator.enforce_schema()

        output = self.translator.output
        output = output.sort_values(['ncdrpatientid', 'followupkey', 'arrivaldate',
                                     'dischargedate']).reset_index(drop=True)
        output['ncdrpatientid'] = output['ncdrpatientid'].astype('int')
        output['f_avarea'] = output['f_avarea'].astype('float')

        drop_fields = ['biomeimportdt', 'biomeencounterid']
        output.drop(drop_fields, axis=1, inplace=True)
        desired = load_mock_data('translator_output', dataset=self.dataset)
        desired.drop(drop_fields, axis=1, inplace=True)
        desired['f_cr'] = desired['f_cr'].astype('str')
        desired['f_lvef'] = desired['f_lvef'].astype('str')
        desired['f_hgb'] = desired['f_hgb'].astype('str')
        output['f_kccq12_overall'] = output['f_kccq12_overall'].astype('float')
        desired['f_post_aorticvalvemeangradient'] = desired['f_post_aorticvalvemeangradient'].astype('str')
        assert_frame_equal(output[desired.columns], desired, check_index_type=False, check_dtype=False,
                           check_names=False)
