import unittest
from unittest.mock import patch

from tests.mock_data import get_mock_data_file_path
from ingestion.readers.cstk import ReaderCSTK
from ingestion.readers.csv import BaseReaderCSV

ingestion_file_info = {
    'id': 1,
    'client': 'client1',
    'created_on': '1709750572',
    'version': '1.0',
    'file_name': 'cstk',
}


class TestReaderCSTK(unittest.TestCase):

    @patch('ingestion.readers.csv.FileInfo.get_file_info', return_value=ingestion_file_info)
    def setUp(self, mock_file_info):
        self.reader = ReaderCSTK(
            filepath=get_mock_data_file_path('cstk.csv', dataset='cstk'), schema=None)

        self.reader.dataset = 'cstk'
        self.reader.file_info = self.reader.get_file_info()
        BaseReaderCSV.execute(self.reader)

    def test_pre_process(self):
        self.reader.pre_process()
        self.assertEqual(self.reader.out.shape, (2, 245))

    def test_append_file_info(self):
        self.reader.pre_process()
        self.reader.append_file_info()
        self.assertEqual(self.reader.out.shape, (2, 250))

    def test_update_file_info(self):
        self.reader.update_file_info()
        self.assertEqual(self.reader.file_info['dataframes'], [{'name': 'CSTK', 'row_count': 2}])
        self.assertEqual(self.reader.file_info['num_patients'], 2)

    def test_execute(self):
        self.reader.pre_process()
        self.reader.update_file_info()
        self.reader.append_file_info()
        self.assertEqual(self.reader.out.shape, (2, 250))
        self.assertListEqual(self.reader.out['FACILITY_NAME'].tolist(), ['Hosp1', 'Hosp2'])
