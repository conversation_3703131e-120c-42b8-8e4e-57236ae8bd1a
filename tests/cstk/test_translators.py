import unittest
from unittest.mock import patch

import pandas as pd
from pandas.testing import assert_frame_equal

from ingestion.translators.translate_cstk_to_biome import TranslateCSTKToBiome
from tests.mock_data import get_mock_data_file_path, load_mock_data


ingestion_file_info = {
    'id': 1,
    'client': 'client1',
    'created_on': '1709750572',
    'version': '1.0',
    'file_name': 'cstk',
    'data_received_date': '2021-01-01'
}

ce = pd.DataFrame({
    'id': {'1': 'ce1', '2': 'ce2'},
    'tenant_id': {'1': 'tenant1', '2': 'tenant2'},
    'code': {'1': 'hosp1', '2': 'hosp2'}
})


class TranslateCSTKToBiomeTest(unittest.TestCase):

    @patch('ingestion.translators.translate_cstk_to_biome.FileInfo.get_file_info',
           return_value=ingestion_file_info)
    @patch('ingestion.translators.translate_cstk_to_biome.ReadRawTable.execute',
           return_value=None)
    def setUp(self, mock_execute, mock_get_file_info):
        self.dataset = 'cstk'
        self.translator = TranslateCSTKToBiome(
            filepath=get_mock_data_file_path('cstk.csv', dataset=self.dataset),
            schema=None, reader=None, file_id=None, client='client1', db_as_source=True, source_db='test_db')
        self.translator.reader.out = load_mock_data('reader_raw_data', dataset=self.dataset)
        self.translator.file_info = ingestion_file_info

    def test_denormalize(self):
        self.translator.denormalize()
        self.assertEqual(self.translator.output.shape, (2, 250))

    @patch('ingestion.translators.translate_cstk_to_biome.get_care_entity_code_map', return_value=ce)
    @patch('ingestion.translators.translate_cstk_to_biome.FileInfo.get_file_info',
           return_value=ingestion_file_info)
    def test_execute(self, mock_ce, mock_get_file_info):
        self.translator.execute()
        drop_fields = ['biomeimportdt']
        desired = load_mock_data('translator_output', dataset=self.dataset).drop(columns=drop_fields)
        output = self.translator.output.drop(columns=drop_fields)
        assert_frame_equal(output.sort_values('strokepatientid')[desired.columns],
                           desired.sort_values('strokepatientid'),
                           check_dtype=False, check_index_type=False, check_names=False)

        self.assertEqual('_'.join(self.translator.target_table.split('_')[:-1]), '20210101_cstk_1_client1_24q1')
