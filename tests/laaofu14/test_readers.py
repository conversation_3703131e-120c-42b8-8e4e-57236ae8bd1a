import unittest
from unittest.mock import patch
from pandas.testing import assert_frame_equal

from ingestion.readers.laao import ReaderLAAOFU14, add_event_id, add_parent_id
from tests.mock_data import get_mock_data_file_path, load_mock_data

ingestion_file_info = {
    'id': 1,
    'client': 'client1',
    'created_on': '**********',
    'version': '1.4',
    'file_name': 'laao',
}


class TestReaderLAAOFU14(unittest.TestCase):

    def setUp(self):
        self.dataset = 'laaofu14'
        self.reader = ReaderLAAOFU14(
            filepath=get_mock_data_file_path('laaofu14.xml', dataset=self.dataset), schema=None)

        self.reader.gen_parent_child_sections()
        self.reader.get_procedure_section_if_any()

        self.patients = self.reader.root.findall('patient')
        self.sections = self.patients[0].findall('section')
        self.elements = self.sections[0].findall('element')

    def test_get_submission_info(self):
        self.reader.get_submission_info()
        self.assertEqual(self.reader.part_id, '12345')
        self.assertEqual(self.reader.part_name, 'CVHS')

    def test_parent_child(self):
        self.assertEqual(self.reader.parent_child.shape, (15, 8))

        parent_section = self.reader.get_parent_section_code('FOLLOWUP')
        self.assertEqual(parent_section, 'Root')

        parent_section = self.reader.get_parent_section_code('FUPMEDS')
        self.assertEqual(parent_section, 'FOLLOWUP')

        parent_section = self.reader.get_parent_section_code('FADJMEDS')
        self.assertEqual(parent_section, 'FADJ')

    def test_extract_data(self):
        data = self.reader.extract_data(self.elements[0])
        desired = {'codeSystem': '2.16.840.1.113883.3.3478.6.1', 'codeSystemName': None, 'codeField': '**********',
                   'displayName': 'Last Name', 'valueType': 'LN', 'value': 'C', 'codeValue': None, 'unit': None,
                   'valueLength': 1, 'valueDisplayName': None}
        self.assertDictEqual(data, desired)

    def test_parse_patients(self):
        self.reader.parse_patients()
        self.assertEqual(self.reader.df_raw.shape, (497, 17))

    def test_join_data_dict(self):
        self.reader.parse_patients()
        self.reader.join_data_dict()
        self.assertEqual(self.reader.df_raw.shape, (506, 26))

    @patch('ingestion.readers.FileInfo.get_file_info',
           return_value=ingestion_file_info)
    def test_reader_output(self, mock_get_file_info):
        self.reader.parse_patients()
        self.reader.join_data_dict()
        self.reader.df_raw = add_event_id(self.reader.df_raw, 'FUPEVENTS', '**********')
        self.reader.add_field_suffix()
        self.reader.df_raw = add_parent_id(self.reader.df_raw, self.dataset)
        self.reader.map_values()
        self.reader.get_file_info()

        out = self.reader.df_raw
        sort_cols = ['NCDRPatientId', 'shortName', 'IncrementalId']
        out = out[
            ['NCDRPatientId', 'FollowupKey', 'VisitId', 'IncrementalId', 'ParentIncrementalId', 'shortName',
             'valueDataDict']].sort_values(sort_cols).reset_index(drop=True)
        desired = load_mock_data('reader_raw_data', dataset=self.dataset).sort_values(sort_cols)
        assert_frame_equal(out, desired, check_index_type=False, check_dtype=False)

        self.reader.slice_tables()
        ncdr_tables = self.reader.ncdr_tables
        self.assertEqual(len(ncdr_tables), 6)

        self.assertEqual(ncdr_tables['DEMOGRAPHICS'].shape, (2, 30))
        self.assertEqual(ncdr_tables['FADJ'].shape, (3, 55))
        self.assertEqual(ncdr_tables['FADJMEDS'].shape, (60, 14))
        self.assertEqual(ncdr_tables['FOLLOWUP'].shape, (2, 72))
        self.assertEqual(ncdr_tables['FUPEVENTS'].shape, (79, 16))
        self.assertEqual(ncdr_tables['FUPMEDS'].shape, (36, 15))
