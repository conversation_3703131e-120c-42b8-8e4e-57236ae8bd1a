import unittest
from unittest.mock import patch
from io import BytesIO
from ingestion.readers.csv import BaseReaderCSV

import polars as pl


class TestBaseReaderCSV(unittest.TestCase):

    def test_has_null_bytes_in_header_no_null(self):
        reader = BaseReaderCSV(path='test.csv')
        reader.raw_bytes_utf8 = b'col1,col2,col3\nvalue1,value2,value3\n'
        result = reader._has_null_bytes_in_header(num_lines=2)
        self.assertFalse(result)

    def test_has_null_bytes_in_header_with_null(self):
        reader = BaseReaderCSV(path='test.csv')
        reader.raw_bytes_utf8 = b'col1\x00,col2,col3\nvalue1,value2,value3\n'
        result = reader._has_null_bytes_in_header()
        self.assertTrue(result)

    def test_clean_null_bytes_in_file(self):
        reader = BaseReaderCSV(path='test.csv')
        reader.raw_bytes_utf8 = b'col1\x00,col2,col3\nvalue1\x00,value2,value3\n'
        reader._clean_null_bytes_in_file()

    @patch.object(BaseReaderCSV, '_clean_null_bytes_in_file')
    @patch.object(BaseReaderCSV, '_has_null_bytes_in_header', return_value=True)
    def test_check_and_clean_csv(self, mock_has_null, mock_clean_null):
        reader = BaseReaderCSV(path='test.csv')
        reader.check_and_clean_csv()
        mock_has_null.assert_called_once()
        mock_clean_null.assert_called_once()

    @patch('polars.read_csv')
    def test_read(self, mock_read_csv):
        reader = BaseReaderCSV(path='test.csv')
        reader.raw_bytes_utf8 = b'col1,col2,col3\nvalue1,value2,value3\n'
        reader.delimiter = ','

        # Call the read method
        reader.read()

        # Ensure polars.read_csv was called with BytesIO containing UTF-8 encoded data
        args, kwargs = mock_read_csv.call_args
        # Check if the first argument passed to `read_csv` is a BytesIO instance
        self.assertIsInstance(args[0], BytesIO)
        mock_read_csv.assert_called_once_with(args[0], separator=',', infer_schema_length=0, eol_char='\n',
                                              dtypes={"*": pl.Utf8}, null_values=reader.null_values)
