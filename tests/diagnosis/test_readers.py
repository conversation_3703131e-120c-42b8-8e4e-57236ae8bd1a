import json
import unittest
from unittest.mock import patch

from tests.mock_data import get_mock_data_file_path
from ingestion.readers.diagnosis import ReaderDiagnosis

ingestion_file_info = {
    'id': 1,
    'client': 'biome',
    'created_on': '1709750572',
    'version': '1.0',
    'file_name': 'diagnosis',
}


class TestReaderDiagnosis(unittest.TestCase):

    @patch('ingestion.readers.excel.FileInfo.get_file_info', return_value=ingestion_file_info)
    def setUp(self, mock_file_info):
        self.reader = ReaderDiagnosis(filepath=get_mock_data_file_path('diagnosis.txt', dataset='diagnosis'),
                                      client='rwj', schema=None)

        self.reader.base_reader.file_info = self.reader.base_reader.get_file_info()
        self.reader.base_reader.execute()
        self.reader.file_info = self.reader.base_reader.file_info
        if not self.reader.is_excel:
            self.reader.datasetlist = [{'dataframe': self.reader.base_reader.out.to_pandas(), 'sheet_name': 'default'}]
        else:
            self.reader.datasetlist = self.reader.base_reader.datasetlist

        self.reader.rename_columns()
        self.reader.lower_case_fields()

    @patch('ingestion.readers.excel.FileInfo.get_file_info', return_value=ingestion_file_info)
    def test_pre_process(self, mock_file_info):
        self.reader.validate_input_completeness()
        self.reader.pre_process()

        self.assertListEqual(list(
            json.loads(self.reader.out['additional_fields'].values[0]).keys()
        ), ['entity code', 'medrecn'])

        self.assertEqual(self.reader.out.shape, (9, 6))

    @patch('ingestion.readers.excel.FileInfo.get_file_info', return_value=ingestion_file_info)
    def test_append_file_info(self, mock_file_info):
        self.reader.validate_input_completeness()
        self.reader.pre_process()
        self.reader.append_file_info()
        self.assertEqual(self.reader.out.shape, (9, 11))

    def test_update_file_info(self):
        self.reader.validate_input_completeness()
        self.reader.pre_process()
        self.reader.update_file_info()
        self.reader.append_file_info()
        self.assertEqual(self.reader.file_info['dataframes'], [{'name': 'Admin_Diagnosis', 'row_count': 9}])
        self.assertEqual(self.reader.file_info['num_patients'], 2)
