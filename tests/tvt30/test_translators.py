import unittest
from unittest.mock import patch
import pandas as pd
from pandas.testing import assert_frame_equal

from ingestion.translators.translate_tvt30_to_biome import TranslateTVT30ToBiome
from tests.mock_data import get_mock_data_file_path, load_mock_data


ingestion_file_info = {
    'id': 1,
    'client': 'client1',
    'created_on': '1709750572',
    'version': '2021013186',
    'file_name': 'tvt30raw',
}

ce = pd.DataFrame({
    'id': [1],
    'tenant_id': ['tenant1'],
    'code': ['hosp1']
})


class TestTranslateTVT30ToBiome(unittest.TestCase):

    @patch('ingestion.translators.translate_tvt30_to_biome.FileInfo.get_file_info',
           return_value=ingestion_file_info)
    def setUp(self, mock_get_file_info):
        self.dataset = 'tvt30'
        self.translator = TranslateTVT30ToBiome(
            filepath=get_mock_data_file_path('tvt30raw.xml', dataset=self.dataset),
            schema=None, reader=None, file_id=None, client='client1')

    def test_flatten_output(self):
        self.translator.denormalize()
        output = self.translator.output
        self.assertEqual(output.shape, (96, 444))
        self.assertEqual(output[output['NCDRPatientId'] == '50558'].shape[0], 64)
        self.assertEqual(output[output['NCDRPatientId'] == '50559'].shape[0], 32)

    def test_append_custom_fields(self):
        self.translator.denormalize()
        self.translator.append_custom_fields()
        output = self.translator.output
        self.assertEqual(output.shape, (96, 600))
        self.assertEqual(output[output['NCDRPatientId'] == '50558'].shape[0], 64)
        self.assertEqual(output[output['NCDRPatientId'] == '50559'].shape[0], 32)

    @patch('ingestion.translators.get_care_entities', return_value=ce)
    def test_translator_output(self, mock_get_care_entities):
        self.translator.denormalize()
        self.translator.append_custom_fields()
        self.translator.lower_case_fields()
        self.translator.rename_fields()
        self.translator.change_delimiters()
        self.translator.transform_field_values()
        self.translator.value_mapping()
        self.translator.post_process()
        self.translator.standardize_datetime()
        self.translator.enforce_schema()

        output = self.translator.output
        output = output.sort_values(['ncdrpatientid', 'episodeid', 'arrivaldatetime',
                                     'dischargedate']).reset_index(drop=True)
        drop_fields = ['procedurestartdate', 'arrivaldate', 'dischargedate', 'biomeimportdt', 'biomeencounterid']
        output.drop(drop_fields, axis=1, inplace=True)
        desired = load_mock_data('translator_output', dataset=self.dataset).drop(drop_fields, axis=1)
        output = output.apply(pd.to_numeric, errors='coerce').fillna(output)
        desired = desired.apply(pd.to_numeric, errors='coerce').fillna(desired)
        assert_frame_equal(output[desired.columns], desired, check_index_type=False, check_dtype=False,
                           check_names=False)
