import unittest
from unittest.mock import patch, MagicMock
from datetime import datetime
from ingestion.utils.events import build_event, publish_event


class TestEventFunctions(unittest.TestCase):
    @patch("ingestion.utils.events.event_type_base", "test.ingestion.")
    def test_build_event(self):
        subject = "Test Subject"
        data = {"client": "test_client"}
        event_type = "test_event"
        event_time = datetime(2024, 11, 5, 15, 30, 0)
        timestamp_format = "%Y%m%d-%H%M%S%f"

        expected_event = {
            'id': 'test_event-test_client-20241105-153000000000',
            'subject': subject,
            'data': data,
            'eventType': "test.ingestion.test_event",
            'eventTime': event_time,
            'dataVersion': '0.9'
        }

        result = build_event(subject, data, event_type, event_time,
                             data_version='0.9', timestamp_format=timestamp_format)
        self.assertEqual(result, expected_event)

    @patch("ingestion.utils.events.EventGridPublisherClient")
    @patch("ingestion.utils.events.EnvironmentCredential")
    @patch("logging.info")
    def test_publish_event_success(self, mock_logging_info,
                                   mock_environment_credential, mock_event_grid_client):

        mock_publisher_client = MagicMock()
        mock_event_grid_client.return_value = mock_publisher_client

        publish = True
        topic_endpoint = "https://test-endpoint.eventgrid.azure.net"
        event_content = {"example": "content"}

        publish_event(publish, topic_endpoint, event_content)

        mock_environment_credential.assert_called_once()
        mock_event_grid_client.assert_called_once_with(topic_endpoint, mock_environment_credential.return_value)
        mock_publisher_client.send.assert_called_once_with([event_content])
        mock_logging_info.assert_any_call(f"Event successfully published to {topic_endpoint}")
        mock_logging_info.assert_any_call(f'Event content: {event_content}')

    @patch("logging.info")
    def test_publish_event_not_published(self, mock_logging_info):
        publish = False
        topic_endpoint = "https://test-endpoint.eventgrid.azure.net"
        event_content = {"example": "content"}

        publish_event(publish, topic_endpoint, event_content)

        mock_logging_info.assert_any_call("Event not published because azure_publishing is set to False")
        mock_logging_info.assert_any_call(f'Event content: {event_content}')

    @patch("logging.error")
    @patch("ingestion.utils.events.EventGridPublisherClient")
    @patch("ingestion.utils.events.EnvironmentCredential")
    def test_publish_event_exception(self, mock_environment_credential, mock_event_grid_client, mock_logging_error):
        # Mocking an exception
        mock_event_grid_client.side_effect = Exception("Connection error")

        publish = True
        topic_endpoint = "https://test-endpoint.eventgrid.azure.net"
        event_content = {"example": "content"}

        publish_event(publish, topic_endpoint, event_content)

        mock_logging_error.assert_called_once_with("Error publishing event: Connection error")


if __name__ == '__main__':
    unittest.main()
