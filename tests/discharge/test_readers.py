import unittest
from unittest.mock import patch

from tests.mock_data import get_mock_data_file_path
from ingestion.readers.discharge import ReaderDischarge

ingestion_file_info = {
    'id': 1,
    'client': 'biome',
    'created_on': '1709750572',
    'version': '1.0',
    'file_name': 'discharge',
}


class TestReaderDischarge(unittest.TestCase):

    @patch('ingestion.readers.excel.FileInfo.get_file_info', return_value=ingestion_file_info)
    def setUp(self, mock_file_info):
        self.reader = ReaderDischarge(filepath=get_mock_data_file_path('discharge.xlsx', dataset='discharge'),
                                      client='biome', schema=None)

        self.reader.dataset = 'Discharges'
        self.reader.base_reader.file_info = self.reader.base_reader.get_file_info()
        self.reader.execute()

    def test_pre_process(self):
        self.reader.pre_process()
        self.assertEqual(self.reader.out.shape, (4, 27))

    def test_append_file_info(self):
        self.reader.pre_process()
        self.reader.append_file_info()
        self.assertEqual(self.reader.out.shape, (4, 32))

    def test_update_file_info(self):
        self.reader.update_file_info()
        self.assertEqual(self.reader.file_info['dataframes'], [{'name': 'Admin_Discharges', 'row_count': 4}])
        self.assertEqual(self.reader.file_info['num_patients'], 4)

    def test_execute(self):
        self.reader.pre_process()
        self.reader.update_file_info()
        self.reader.append_file_info()
        self.assertEqual(self.reader.out.shape, (4, 32))
        self.assertListEqual(self.reader.out['servicesitename'].unique().tolist(), ['hosp2', 'hosp1'])
