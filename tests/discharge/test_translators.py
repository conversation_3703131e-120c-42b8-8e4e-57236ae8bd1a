import unittest
from unittest.mock import patch

import pandas as pd
from pandas.testing import assert_frame_equal

from ingestion.translators.translate_discharges_to_biome import TranslateDischargesToBiome
from tests.mock_data import get_mock_data_file_path, load_mock_data


ingestion_file_info = {
    'id': 1,
    'client': 'biome',
    'created_on': '1709750572',
    'version': '1.0',
    'file_name': 'discharge',
    'data_received_date': '2021-01-01'
}

ce1 = pd.DataFrame({
    'id': {'1': 'ce1', '2': 'ce2'},
    'ids': {'1': '1', '2': '2'},
    'name': {'1': 'hosp1', '2': 'hosp2'},
    'tenants': {'1': {'id': '1', 'code': 'hosp1'}, '2': {'id': '2', 'code': 'hosp2'}},
    'tenant_id': {'1': 'tenant1', '2': 'tenant2'},
    'code': {'1': 'hosp1', '2': 'hosp2'},
    'tenant_code': {'1': 'biome', '2': 'biome'}
})
ce2 = pd.DataFrame({
    'id': {'1': 'ce1', '2': 'ce2'},
    'tenant_id': {'1': 'tenant1', '2': 'tenant2'},
    'code': {'1': 'hosp1', '2': 'hosp2'}
})


class TranslateDischargesBiomeTest(unittest.TestCase):

    @patch('ingestion.translators.translate_discharges_to_biome.FileInfo.get_file_info',
           return_value=ingestion_file_info)
    @patch('ingestion.translators.translate_discharges_to_biome.ReadRawTable.execute',
           return_value=None)
    def setUp(self, mock_execute, mock_get_file_info):
        self.dataset = 'discharge'
        self.translator = TranslateDischargesToBiome(
            filepath=get_mock_data_file_path('discharge.xlsx', dataset=self.dataset),
            schema=None, reader=None, file_id=None, client='biome', db_as_source=True, source_db='test_db')
        self.translator.reader.out = load_mock_data('reader_raw_data', dataset=self.dataset)
        self.translator.file_info = ingestion_file_info

    def test_denormalize(self):
        self.translator.denormalize()
        self.assertEqual(self.translator.output.shape, (4, 48))

    @patch('ingestion.translators.translate_discharges_to_biome.get_all_care_entities', return_value=ce1)
    @patch('ingestion.utils.odin.get_all_care_entities', return_value=ce1)
    @patch('ingestion.utils.common.get_all_care_entities', return_value=ce1)
    @patch('ingestion.translators.translate_discharges_to_biome.FileInfo.get_file_info',
           return_value=ingestion_file_info)
    def test_post_process(self, mock_get_all_care_entities, mock_get_care_entities, mock_get_all_care_entities1,
                          mock_get_file_info):
        self.translator.denormalize()
        self.translator.post_process()
        self.assertEqual(self.translator.output['hospital'].unique()[0], 'hosp2')
        self.assertEqual(self.translator.output['hospname'].unique()[0], 'hosp2')
        self.assertEqual(self.translator.output['servicesitename'].unique()[0], 'hosp2')
        self.assertEqual(self.translator.output['careentityid'].unique()[0], 'ce2')
        self.assertEqual(self.translator.output['tenantid'].unique()[0], 'tenant2')

    @patch('ingestion.translators.translate_discharges_to_biome.get_all_care_entities', return_value=ce1)
    @patch('ingestion.utils.odin.get_all_care_entities', return_value=ce1)
    @patch('ingestion.utils.common.get_all_care_entities', return_value=ce1)
    @patch('ingestion.translators.translate_discharges_to_biome.FileInfo.get_file_info',
           return_value=ingestion_file_info)
    def test_mrn_handling(self, mock_get_all_care_entities, mock_get_care_entities, mock_get_all_care_entities1,
                          mock_get_file_info):
        self.translator.denormalize()
        self.translator.post_process()
        self.assertEqual(self.translator.output['medrecn'].unique()[0], '00525542')
        self.assertEqual(self.translator.output['medrecn'].unique()[1], '00231145')

    @patch('ingestion.translators.translate_discharges_to_biome.get_all_care_entities', return_value=ce1)
    @patch('ingestion.utils.odin.get_all_care_entities', return_value=ce1)
    @patch('ingestion.utils.common.get_all_care_entities', return_value=ce1)
    @patch('ingestion.translators.translate_discharges_to_biome.FileInfo.get_file_info',
           return_value=ingestion_file_info)
    def test_yearmonth(self, mock_get_all_care_entities, mock_get_care_entities, mock_get_all_care_entities1,
                       mock_get_file_info):
        self.translator.denormalize()
        self.translator.post_process()
        self.assertEqual(self.translator.output['yearmonth'].unique()[0], '202202')
        self.assertTrue(pd.isna(self.translator.output['yearmonth'].unique()[1]))

    @patch('ingestion.translators.translate_discharges_to_biome.get_all_care_entities', return_value=ce1)
    @patch('ingestion.utils.odin.get_all_care_entities', return_value=ce1)
    @patch('ingestion.utils.common.get_all_care_entities', return_value=ce1)
    @patch('ingestion.translators.translate_discharges_to_biome.FileInfo.get_file_info',
           return_value=ingestion_file_info)
    def test_execute(self, mock_get_all_care_entities, mock_get_care_entities, mock_get_all_care_entities1,
                     mock_get_file_info):
        self.translator.execute()
        drop_fields = ['biomeimportdt', 'biomeencounterid']
        desired = load_mock_data('translator_output', dataset=self.dataset).drop(columns=drop_fields)
        output = self.translator.output.drop(columns=drop_fields)
        assert_frame_equal(output.sort_values('encounternumber')[desired.columns],
                           desired.sort_values('encounternumber'),
                           check_dtype=False, check_index_type=False, check_names=False)
        target_tablename = '20210101_admin_discharges_1_biome_22q1'
        self.assertEqual('_'.join(self.translator.target_table.split('_')[:-1]), target_tablename)

    @patch('ingestion.translators.translate_discharges_to_biome.get_all_care_entities', return_value=ce1)
    @patch('ingestion.utils.odin.get_all_care_entities', return_value=ce1)
    @patch('ingestion.utils.common.get_all_care_entities', return_value=ce1)
    @patch('ingestion.translators.translate_discharges_to_biome.FileInfo.get_file_info',
           return_value=ingestion_file_info)
    def test_create_table_name(self, mock_get_all_care_entities, mock_get_care_entities, mock_get_all_care_entities1,
                               mock_get_file_info):
        self.translator.denormalize()
        self.translator.post_process()
        self.translator.create_table_name()
        target_tablename = '20210101_admin_discharges_1_biome_22q1'
        self.assertEqual('_'.join(self.translator.target_table.split('_')[:-1]), target_tablename)
