import unittest
import pandas as pd
from numpy import nan
from pandas.testing import assert_frame_equal

from ingestion.schema.schema_gen import BuildSchema
from tests.mock_data import load_mock_data


desired_create_table_output = """DROP TABLE IF EXISTS Table1; Create table if not exists Table1 (
NCDRPatientId bigint
,EpisodeKey text
,VisitId tinyint
,IncrementalId tinyint
,ClientFileId text
,FileName text
,Version int
,BiomeImportDt text
,DatasetName varchar(64)
,PartId text
,PartName text
,FirstName varchar(50) comment "Reference: 1"
,Height decimal(6,2) comment "Reference: 2"
,Height_unit varchar(15) CHARACTER SET utf8mb4 comment "Units for Reference: 2"
);
DROP TABLE IF EXISTS Table2; Create table if not exists Table2 (
NCDRPatientId bigint
,EpisodeKey text
,VisitId tinyint
,IncrementalId tinyint
,ClientFileId text
,FileName text
,Version int
,BiomeImportDt text
,DatasetName varchar(64)
,PartId text
,PartName text
,Sex varchar(128) comment "Reference: 3"
,AdmitDate datetime comment "Reference: 4"
);"""


class TestSchemaBuilder(unittest.TestCase):

    def setUp(self):
        self.bs = BuildSchema('', '', '', '')
        self.bs.sections = load_mock_data('schema_sections')
        self.bs.elements = load_mock_data('schema_elements')
        self.bs.selections = load_mock_data('schema_selections')
        self.bs.definitions = load_mock_data('schema_definitions')
        self.bs.ranges = load_mock_data('schema_ranges')

        self.bs.create_section_tables()

    def test_create_section_tables(self):
        self.bs.sections = load_mock_data('schema_sections_all')
        self.bs.create_section_tables()
        assert_frame_equal(
            self.bs.sections[['Section Code', 'Table']],
            load_mock_data('schema_section_tables'),
        )

    def test_join_info(self):
        self.bs.join_info()
        assert_frame_equal(self.bs.output.reset_index(drop=True), load_mock_data('schema_flat_output'),
                           check_index_type=False, check_dtype=False)

    def test_create_table_stmts(self):
        self.bs.output = pd.DataFrame([
            {'Short Name': 'FirstName', 'Data Type': 'FN', 'Precision': '50', 'Unit Of Measure Elements': nan,
             'Element Reference': 1, 'Coding Instructions': 'This is first name', 'Table': 'Table 1'},
            {'Short Name': 'Height', 'Data Type': 'PQ', 'Precision': '6,2', 'Unit Of Measure Elements': 'cm',
             'Element Reference': 2, 'Coding Instructions': 'This is height', 'Table': 'Table 1'},
            {'Short Name': 'Sex', 'Data Type': 'FN', 'Precision': nan, 'Unit Of Measure Elements': nan,
             'Element Reference': 3, 'Coding Instructions': 'This is sex', 'Table': 'Table2'},
            {'Short Name': 'AdmitDate', 'Data Type': 'TS', 'Precision': nan, 'Unit Of Measure Elements': nan,
             'Element Reference': 4, 'Coding Instructions': 'This is admit date', 'Table': 'Table2'},
        ])

        sql = self.bs.create_table_stmts()
        self.assertEqual(sql, desired_create_table_output)

    def test_export_schema_json(self):
        out = self.bs.export_schema_json(is_test=True)
        self.assertDictEqual(out, load_mock_data('schema_export_output', as_df=False))
