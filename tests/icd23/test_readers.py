import unittest
from unittest.mock import patch
from pandas.testing import assert_frame_equal

from ingestion.readers.icd import ReaderICD23
from tests.mock_data import get_mock_data_file_path, load_mock_data

ingestion_file_info = {
    'id': 1,
    'client': 'client1',
    'created_on': '**********',
    'version': '2.3',
    'file_name': 'icd',
}


class TestReaderICD23(unittest.TestCase):

    def setUp(self):
        self.dataset = 'icd23'
        self.reader = ReaderICD23(
            filepath=get_mock_data_file_path('icd23.xml', dataset=self.dataset), schema=None)

        self.reader.gen_parent_child_sections()
        self.reader.get_procedure_section_if_any()

        self.patients = self.reader.root.findall('patient')
        self.sections = self.patients[0].findall('section')
        self.elements = self.sections[0].findall('element')

    def test_get_submission_info(self):
        self.reader.get_submission_info()
        self.assertEqual(self.reader.part_id, '12345')
        self.assertEqual(self.reader.part_name, 'CVHS')

    def test_parent_child(self):
        self.assertEqual(self.reader.parent_child.shape, (25, 8))

        parent_section = self.reader.get_parent_section_code('PROCINFO')
        self.assertEqual(parent_section, 'Root')

        parent_section = self.reader.get_parent_section_code('EOC')
        self.assertEqual(parent_section, 'Root')

        parent_section = self.reader.get_parent_section_code('IMPLEXPL')
        self.assertEqual(parent_section, 'PROCINFO')

    def test_extract_data(self):
        data = self.reader.extract_data(self.elements[0])
        desired = {'codeField': '**********', 'codeSystem': '2.16.840.1.113883.3.3478.6.1', 'codeSystemName': None,
                   'codeValue': None, 'displayName': 'Last Name', 'unit': None, 'value': 'B', 'valueDisplayName': None,
                   'valueLength': 1, 'valueType': 'LN'}
        self.assertDictEqual(data, desired)

    def test_parse_patients(self):
        self.reader.parse_patients()
        self.assertEqual(self.reader.df_raw.shape, (325, 16))

    def test_join_data_dict(self):
        self.reader.parse_patients()
        self.reader.join_data_dict()
        self.assertEqual(self.reader.df_raw.shape, (335, 25))

    @patch('ingestion.readers.FileInfo.get_file_info',
           return_value=ingestion_file_info)
    def test_reader_output(self, mock_get_file_info):
        self.reader.parse_patients()
        self.reader.join_data_dict()
        self.reader.add_field_suffix()
        self.reader.get_file_info()

        out = self.reader.df_raw
        sort_cols = ['NCDRPatientId', 'shortName', 'IncrementalId']
        out = out[
            ['NCDRPatientId', 'EpisodeKey', 'VisitId', 'IncrementalId', 'shortName', 'valueDataDict']].sort_values(
            sort_cols).reset_index(drop=True)
        desired = load_mock_data('reader_raw_data', dataset=self.dataset).sort_values(sort_cols)
        assert_frame_equal(out, desired, check_index_type=False, check_dtype=False)

        self.reader.slice_tables()
        ncdr_tables = self.reader.ncdr_tables
        self.assertEqual(len(ncdr_tables), 5)

        self.assertEqual(ncdr_tables['DEMOGRAPHICS'].shape, (2, 42))
        self.assertEqual(ncdr_tables['DISCHARGEMEDS'].shape, (32, 13))
        self.assertEqual(ncdr_tables['EPISODEOFCARE'].shape, (2, 119))
        self.assertEqual(ncdr_tables['LEADS'].shape, (4, 19))
        self.assertEqual(ncdr_tables['PROCINFO'].shape, (2, 61))
