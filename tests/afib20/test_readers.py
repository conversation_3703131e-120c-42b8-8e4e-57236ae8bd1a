import unittest
from unittest.mock import patch
from pandas.testing import assert_frame_equal

from ingestion.readers.afib import ReaderAFib20
from tests.mock_data import get_mock_data_file_path, load_mock_data


ingestion_file_info = {
    'id': 1,
    'client': 'client1',
    'created_on': '**********',
    'version': '2.0',
    'file_name': 'afib'
}


class TestReaderAFib20(unittest.TestCase):

    def setUp(self) -> None:
        self.dataset = 'afib20'
        self.reader = ReaderAFib20(
            filepath=get_mock_data_file_path('afib20.xml', dataset=self.dataset), schema=None)

        self.reader.gen_parent_child_sections()
        self.reader.get_procedure_section_if_any()

        self.patients = self.reader.root.findall('patient')
        self.sections = self.patients[0].findall('section')
        self.elements = self.sections[0].findall('element')

    def test_get_submission_info(self):
        self.reader.get_submission_info()
        self.assertEqual(self.reader.part_id, '267989')
        self.assertEqual(self.reader.part_name, 'Alta Bates Summit Medical Center, Summit Campus')

    def test_parent_child(self):
        self.assertEqual(self.reader.parent_child.shape, (37, 8))

        parent_section = self.reader.get_parent_section_code('PROCINFO')
        self.assertEqual(parent_section, 'Root')

        parent_section = self.reader.get_parent_section_code('EOC')
        self.assertEqual(parent_section, 'Root')

        parent_section = self.reader.get_parent_section_code('IPPEVENTS')
        self.assertEqual(parent_section, 'IPPEVENTS')

        parent_section = self.reader.get_parent_section_code('CHA2DS2')
        self.assertEqual(parent_section, 'HISTORYANDRISK')

        parent_section = self.reader.get_parent_section_code('DEVICE')
        self.assertEqual(parent_section, 'PROCINFO')

    def test_extract_data(self):
        data = self.reader.extract_data(self.elements[0])
        desired = {'codeSystem': '2.16.840.1.113883.3.3478.6.1', 'codeSystemName': 'ACC NCDR',
                   'codeField': '**********', 'displayName': 'Last Name', 'valueType': 'LN', 'value': 'Doe',
                   'codeValue': None, 'unit': None, 'valueDisplayName': None, 'valueLength': 1}
        self.assertDictEqual(data, desired)

    def test_parse_patients(self):
        self.reader.parse_patients()
        self.assertEqual(self.reader.df_raw.shape, (722, 16))

    def test_join_data_dict(self):
        self.reader.parse_patients()
        self.reader.join_data_dict()
        self.assertEqual(self.reader.df_raw.shape, (739, 25))

    def test_add_device_name(self):
        self.reader.parse_patients()
        self.reader.join_data_dict()
        self.reader.add_device_name()
        self.assertEqual(self.reader.df_raw.shape, (741, 25))

    @patch('ingestion.readers.FileInfo.get_file_info',
           return_value=ingestion_file_info)
    def test_reader_output(self, mock_get_file_info):
        self.reader.parse_patients()
        self.reader.join_data_dict()
        self.reader.add_field_suffix()
        self.reader.get_file_info()

        out = self.reader.df_raw
        sort_cols = ['NCDRPatientId', 'shortName', 'IncrementalId']
        out = out[
            ['NCDRPatientId', 'EpisodeKey', 'VisitId', 'IncrementalId', 'shortName', 'valueDataDict']].sort_values(
            sort_cols).reset_index(drop=True)
        desired = load_mock_data('reader_raw_data', dataset=self.dataset).sort_values(sort_cols)
        desired['valueDataDict'] = desired['valueDataDict'].replace('GyÂ·cmÂ²', 'Gy·cm²')
        assert_frame_equal(out, desired, check_index_type=False, check_dtype=False)

        self.reader.slice_tables()
        ncdr_tables = self.reader.ncdr_tables
        self.assertEqual(len(ncdr_tables), 12)

        self.assertEqual(ncdr_tables['DEMOGRAPHICS'].shape, (2, 27))
        self.assertEqual(ncdr_tables['EPISODEOFCARE'].shape, (2, 107))
        self.assertEqual(ncdr_tables['PROCINFO'].shape, (2, 45))
        self.assertEqual(ncdr_tables['PREPROCMEDS'].shape, (70, 13))
        self.assertEqual(ncdr_tables['DCMEDS'].shape, (70, 13))
        self.assertEqual(ncdr_tables['IPPEVENTS'].shape, (58, 16))
        self.assertEqual(ncdr_tables['PROCHX'].shape, (8, 13))
        self.assertEqual(ncdr_tables['CATHABLDEV'].shape, (2, 13))
        self.assertEqual(ncdr_tables['ELECTROMAPSYS'].shape, (2, 12))
        self.assertEqual(ncdr_tables['ABLAPPR'].shape, (12, 14))
        self.assertEqual(ncdr_tables['ABLLOC'].shape, (20, 14))
        self.assertEqual(ncdr_tables['CONDHX'].shape, (12, 13))
