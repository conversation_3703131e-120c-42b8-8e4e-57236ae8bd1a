import unittest
from unittest.mock import patch

import pandas as pd
from pandas.testing import assert_frame_equal

from ingestion.translators.translate_cathpci5_to_biome import TranslateCathPCI5ToBiome
from tests.mock_data import get_mock_data_file_path, load_mock_data


ingestion_file_info = {
    'id': 1,
    'client': 'client1',
    'created_on': '1709750572',
    'version': '5.0',
    'file_name': 'cathpci',
}

ce = pd.DataFrame({
    'id': [1],
    'tenant_id': ['tenant1'],
    'code': ['hosp1']
})


class TestTranslator(unittest.TestCase):

    @patch('ingestion.translators.translate_cathpci5_to_biome.FileInfo.get_file_info',
           return_value=ingestion_file_info)
    def setUp(self, mock_get_file_info):
        self.dataset = 'cathpci'
        self.translator = TranslateCathPCI5ToBiome(
            filepath=get_mock_data_file_path('cathpci.xml', dataset=self.dataset),
            schema=None, reader=None, file_id=None, client='client1')

    def test_flatten_output(self):
        self.translator.denormalize()
        output = self.translator.output
        self.assertEqual(output.shape, (171, 308))
        self.assertEqual(output[output['NCDRPatientId'] == '12345'].shape[0], 156)
        self.assertEqual(output[output['NCDRPatientId'] == '67890'].shape[0], 15)

    def test_append_custom_fields(self):
        self.translator.denormalize()
        self.translator.append_custom_fields()
        output = self.translator.output
        self.assertEqual(output.shape, (327, 468))
        # records for id 12345 should double because of multiple event occurrences for a same event
        self.assertEqual(output[output['NCDRPatientId'] == '12345'].shape[0], 312)
        self.assertEqual(output[output['NCDRPatientId'] == '67890'].shape[0], 15)

    @patch('ingestion.translators.get_care_entities', return_value=ce)
    def test_translator_output(self, mock_get_care_entities):
        self.translator.denormalize()
        self.translator.append_custom_fields()
        self.translator.lower_case_fields()
        self.translator.rename_fields()
        self.translator.change_delimiters()
        self.translator.enforce_schema()
        self.translator.value_mapping()
        self.translator.fix_field_lengths()
        self.translator.post_process()

        output = self.translator.output
        output = output.sort_values(['ncdrpatientid', 'episodeid', 'arrivaldatetime',
                                     'dcdatetime']).reset_index(drop=True)
        drop_fields = ['proceduredate', 'arrivaldate', 'dischargedate', 'biomeimportdt']
        output.drop(drop_fields, axis=1, inplace=True)
        desired = load_mock_data('translator_output', dataset=self.dataset)
        # remove columns that end with 'rowid', removed unwanted columns from biome schema, e.g. patientrowid
        desired = desired.loc[:, ~desired.columns.str.endswith('rowid')]
        assert_frame_equal(output[desired.columns], desired, check_index_type=False, check_dtype=False,
                           check_names=False)
