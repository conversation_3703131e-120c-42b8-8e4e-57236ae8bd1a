import unittest
from unittest.mock import patch
from pandas.testing import assert_frame_equal

from ingestion.readers.cathpci import ReaderCathPCI5
from tests.mock_data import get_mock_data_file_path, load_mock_data


ingestion_file_info = {
    'id': 1,
    'client': 'client1',
    'created_on': '**********',
    'version': '5.0',
    'file_name': 'cathpci',
}


class TestReader(unittest.TestCase):

    def setUp(self):
        self.reader = ReaderCathPCI5(
            filepath=get_mock_data_file_path('cathpci.xml', dataset='cathpci'), schema=None)

        self.reader.gen_parent_child_sections()
        self.reader.get_procedure_section_if_any()

        self.patients = self.reader.root.findall('patient')
        self.sections = self.patients[0].findall('section')
        self.elements = self.sections[0].findall('element')

        self.dataset = 'cathpci'

    def test_get_submission_info(self):
        self.reader.get_submission_info()
        self.assertEqual(self.reader.part_id, '123')
        self.assertEqual(self.reader.part_name, 'CVHS')

    def test_parent_child(self):
        self.assertEqual(self.reader.parent_child.shape, (32, 8))

        parent_section = self.reader.get_parent_section_code('PROCINFO')
        self.assertEqual(parent_section, 'Root')

        parent_section = self.reader.get_parent_section_code('EOCINFO')
        self.assertEqual(parent_section, 'EPISODEOFCARE')

    def test_extract_data(self):
        data = self.reader.extract_data(self.elements[0])
        desired = {'codeSystem': '2.16.840.1.113883.3.3478.6.1', 'codeSystemName': None, 'codeField': '**********',
                   'displayName': 'Last Name', 'valueType': 'LN', 'value': 'Whats in the name?', 'codeValue': None,
                   'valueLength': 1, 'unit': None, 'valueDisplayName': None}
        self.assertDictEqual(data, desired)

    def test_parse_patients(self):
        self.reader.parse_patients()
        self.assertEqual(self.reader.df_raw.shape, (693, 16))

    def test_join_data_dict(self):
        self.reader.parse_patients()
        self.reader.join_data_dict()
        self.assertEqual(self.reader.df_raw.shape, (771, 25))

    @patch('ingestion.translators.translate_cathpci5_to_biome.FileInfo.get_file_info',
           return_value=ingestion_file_info)
    def test_reader_output(self, mock_get_file_info):
        self.reader.parse_patients()
        self.reader.join_data_dict()
        self.reader.add_field_suffix()
        self.reader.get_file_info()

        out = self.reader.df_raw
        sort_cols = ['NCDRPatientId', 'shortName', 'IncrementalId']
        out = out[
            ['NCDRPatientId', 'EpisodeKey', 'VisitId', 'IncrementalId', 'shortName', 'valueDataDict']].sort_values(
            sort_cols).reset_index(drop=True)
        desired = load_mock_data('reader_raw_data', dataset=self.dataset).sort_values(sort_cols)
        assert_frame_equal(out, desired, check_index_type=False, check_dtype=False)

        self.reader.slice_tables()
        ncdr_tables = self.reader.ncdr_tables
        self.assertEqual(len(ncdr_tables), 13)

        self.assertEqual(ncdr_tables['ATTPROVIDERS'].shape, (2, 15))
        self.assertEqual(ncdr_tables['CLMETHOD'].shape, (2, 14))
        self.assertEqual(ncdr_tables['DEMOGRAPHICS'].shape, (2, 42))
        self.assertEqual(ncdr_tables['DEVICES'].shape, (18, 19))
        self.assertEqual(ncdr_tables['DISCHMED'].shape, (36, 15))
        self.assertEqual(ncdr_tables['EPISODEOFCARE'].shape, (2, 79))
        self.assertEqual(ncdr_tables['IPPEVENTS'].shape, (33, 14))
        self.assertEqual(ncdr_tables['LESIONDEV'].shape, (4, 41))
        self.assertEqual(ncdr_tables['NVESSEL'].shape, (7, 19))
        self.assertEqual(ncdr_tables['PREPROCMED'].shape, (24, 13))
        self.assertEqual(ncdr_tables['PROCINFO'].shape, (2, 153))
        self.assertEqual(ncdr_tables['PROCMED'].shape, (32, 13))
        self.assertEqual(ncdr_tables['STRESSTEST'].shape, (2, 15))

    @patch('ingestion.translators.translate_cathpci5_to_biome.FileInfo.get_file_info',
           return_value=ingestion_file_info)
    def test_reader_multi_episodes(self, mock_get_file_info):
        self.reader = ReaderCathPCI5(
            filepath=get_mock_data_file_path('cathpci_multi_episodes.xml', dataset=self.dataset), schema=None)

        self.reader.gen_parent_child_sections()
        self.reader.get_procedure_section_if_any()

        self.reader.parse_patients()
        self.reader.join_data_dict()
        self.reader.add_field_suffix()
        self.reader.get_file_info()

        out = self.reader.df_raw
        sort_cols = ['NCDRPatientId', 'shortName', 'IncrementalId']
        out = out[
            ['NCDRPatientId', 'EpisodeKey', 'VisitId', 'IncrementalId', 'shortName', 'valueDataDict']].sort_values(
            sort_cols).reset_index(drop=True)
        desired = load_mock_data('reader_raw_data_multi_episodes', dataset=self.dataset).sort_values(sort_cols)
        assert_frame_equal(out, desired, check_index_type=False, check_dtype=False)

        self.reader.slice_tables()
        ncdr_tables = self.reader.ncdr_tables
        self.assertEqual(len(ncdr_tables), 13)

        self.assertEqual(ncdr_tables['ATTPROVIDERS'].shape, (1, 15))
        self.assertEqual(ncdr_tables['CLMETHOD'].shape, (3, 14))
        self.assertEqual(ncdr_tables['DEMOGRAPHICS'].shape, (2, 42))
        self.assertEqual(ncdr_tables['DEVICES'].shape, (17, 19))
        self.assertEqual(ncdr_tables['DISCHMED'].shape, (54, 15))
        self.assertEqual(ncdr_tables['EPISODEOFCARE'].shape, (3, 79))
        self.assertEqual(ncdr_tables['IPPEVENTS'].shape, (49, 14))
        self.assertEqual(ncdr_tables['LESIONDEV'].shape, (5, 41))
        self.assertEqual(ncdr_tables['NVESSEL'].shape, (10, 19))
        self.assertEqual(ncdr_tables['PREPROCMED'].shape, (36, 13))
        self.assertEqual(ncdr_tables['PROCINFO'].shape, (3, 154))
        self.assertEqual(ncdr_tables['PROCMED'].shape, (48, 13))
        self.assertEqual(ncdr_tables['STRESSTEST'].shape, (1, 15))

    @patch('ingestion.translators.translate_cathpci5_to_biome.FileInfo.get_file_info',
           return_value=ingestion_file_info)
    def test_reader_multi_visits(self, mock_get_file_info):
        self.reader = ReaderCathPCI5(
            filepath=get_mock_data_file_path('cathpci_multi_visits.xml', dataset=self.dataset), schema=None)

        self.reader.gen_parent_child_sections()
        self.reader.get_procedure_section_if_any()

        self.reader.parse_patients()
        self.reader.join_data_dict()
        self.reader.add_field_suffix()
        self.reader.get_file_info()

        out = self.reader.df_raw
        sort_cols = ['NCDRPatientId', 'shortName', 'IncrementalId']
        out = out[
            ['NCDRPatientId', 'EpisodeKey', 'VisitId', 'IncrementalId', 'shortName', 'valueDataDict']].sort_values(
            sort_cols).reset_index(drop=True)
        desired = load_mock_data('reader_raw_data_multi_visits', dataset=self.dataset).sort_values(sort_cols)
        assert_frame_equal(out, desired, check_index_type=False, check_dtype=False)

        self.reader.slice_tables()
        ncdr_tables = self.reader.ncdr_tables
        self.assertEqual(len(ncdr_tables), 13)

        self.assertEqual(ncdr_tables['ATTPROVIDERS'].shape, (1, 15))
        self.assertEqual(ncdr_tables['CLMETHOD'].shape, (2, 14))
        self.assertEqual(ncdr_tables['DEMOGRAPHICS'].shape, (2, 42))
        self.assertEqual(ncdr_tables['DEVICES'].shape, (16, 19))
        self.assertEqual(ncdr_tables['DISCHMED'].shape, (36, 15))
        self.assertEqual(ncdr_tables['EPISODEOFCARE'].shape, (2, 79))
        self.assertEqual(ncdr_tables['IPPEVENTS'].shape, (49, 14))
        self.assertEqual(ncdr_tables['LESIONDEV'].shape, (4, 41))
        self.assertEqual(ncdr_tables['NVESSEL'].shape, (8, 20))
        self.assertEqual(ncdr_tables['PREPROCMED'].shape, (36, 13))
        self.assertEqual(ncdr_tables['PROCINFO'].shape, (3, 155))
        self.assertEqual(ncdr_tables['PROCMED'].shape, (32, 13))
        self.assertEqual(ncdr_tables['STRESSTEST'].shape, (3, 15))
