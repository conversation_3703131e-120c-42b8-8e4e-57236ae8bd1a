import unittest
import pandas as pd
import numpy as np
from ingestion.utils import datetime as dt


class TestStandardizeDate(unittest.TestCase):

    def test_parse_time1(self):
        """
        test time conversion from 12 hour 24

        """
        time_df = pd.DataFrame({
            'in_time': ['01:00 pm', '01:00pm', '1:00 pm', '1:00 PM', '01:00:00am', '1:00:0 AM', '1:00:0 PM',
                        '123:456 am'],
            'out_time': ['13:00:00', '13:00:00', '13:00:00', '13:00:00', '01:00:00', '01:00:00', '13:00:00', pd.NaT]
        })
        time_df['in_time'] = dt.standardize_time_format(time_df['in_time'])
        self.assertListEqual(time_df['in_time'].fillna('').to_list(), time_df['out_time'].fillna('').to_list())

    def test_parse_time2(self):
        """
        test time conversion from army-format to HH:MM:SS format

        """
        time_df = pd.DataFrame({
            'in_time': [100, 1200, 1334, 334, None, np.nan],
            'out_time': ['01:00:00', '12:00:00', '13:34:00', '03:34:00', None, None]
        })
        time_df['in_time'] = dt.standardize_time_format(time_df['in_time'])
        self.assertListEqual(time_df['in_time'].fillna('').to_list(), time_df['out_time'].fillna('').to_list())

    def test_parse_time3(self):
        """
        test time conversion from HH:MM and H:MM format to HH:MM:SS format

        """
        time_df = pd.DataFrame({
            'in_time': ['01:00', '12:00', '13:34', '03:34', '5:16', None, np.nan],
            'out_time': ['01:00:00', '12:00:00', '13:34:00', '03:34:00', '05:16:00', None, None]
        })
        time_df['in_time'] = dt.standardize_time_format(time_df['in_time'])
        self.assertListEqual(time_df['in_time'].fillna('').to_list(), time_df['out_time'].fillna('').to_list())

    def test_parse_time4(self):
        """
        test time conversion from YYYYMMDDhhmmss formatto HH:MM:SS format
        """
        time_df = pd.DataFrame({
            'in_time': ['20220427133400', '20210427033400', None, np.nan],
            'out_time': ['13:34:00', '03:34:00', None, None]
        })
        time_df['in_time'] = dt.standardize_time_format(time_df['in_time'])
        self.assertListEqual(time_df['in_time'].fillna('').to_list(), time_df['out_time'].fillna('').to_list())

    def test_parse_time5(self):
        """
        test time conversion from zone-format to HH:MM:SS format

        """
        time_df = pd.DataFrame({
            'in_time': ['08:26:00-07:00', '02:22:00+07:00', '02:22:00.0000000+07:00', '15:16:00.0000000-05:00', None,
                        np.nan],
            'out_time': ['08:26:00', '02:22:00', '02:22:00', '15:16:00', None, None]
        })
        time_df['in_time'] = dt.standardize_time_format(time_df['in_time'])
        self.assertListEqual(time_df['in_time'].fillna('').to_list(), time_df['out_time'].fillna('').to_list())

    def test_parse_time6(self):
        """
        test time conversion from YYYYMMDDhhmm format to HH:MM:SS format

        """
        time_df = pd.DataFrame({
            'in_time': ['202204271334', '202104270334', None, np.nan],
            'out_time': ['13:34:00', '03:34:00', None, None]
        })
        time_df['in_time'] = dt.standardize_time_format(time_df['in_time'])
        self.assertListEqual(time_df['in_time'].fillna('').to_list(), time_df['out_time'].fillna('').to_list())

    def test_parse_time7(self):
        """
        test time conversion from YYYY-MM-DD-h:mm:ss am|pm format to HH:MM:SS format

        """
        time_df = pd.DataFrame({
            'in_time': ['2019-07-04 2:31:00 pm', '2019-07-04 2:31:00 am', None, np.nan],
            'out_time': ['14:31:00', '02:31:00', None, None]
        })
        time_df['in_time'] = dt.standardize_time_format(time_df['in_time'])
        self.assertListEqual(time_df['in_time'].fillna('').to_list(), time_df['out_time'].fillna('').to_list())

    def test_parse_time8(self):
        """
        test time conversion from YYYY-MM-DD hh:mm:ss format to HH:MM:SS format
        """
        time_df = pd.DataFrame({
            'in_time': ['2023-10-02 19:15:00.000', '2023-10-02 19:15:00.0'],
            'out_time': ['19:15:00', '19:15:00']
        })
        time_df['in_time'] = dt.standardize_time_format(time_df['in_time'])
        self.assertListEqual(time_df['in_time'].fillna('').to_list(), time_df['out_time'].fillna('').to_list())

    def test_parse_time9(self):
        """
        test time conversion from date + hh:mm format to HH:MM:SS format
        """
        time_df = pd.DataFrame({
            'in_time': ['04/04/2002 14:33'],
            'out_time': ['14:33:00']
        })
        time_df['in_time'] = dt.standardize_time_format(time_df['in_time'])
        self.assertListEqual(time_df['in_time'].fillna('').to_list(), time_df['out_time'].fillna('').to_list())

    def test_parse_time10(self):
        """
        test time conversion from date + hh:mm format to HH:MM:SS format
        """
        time_df = pd.DataFrame({
            'in_time': ['2002/04/04 14:33'],
            'out_time': ['14:33:00']
        })
        time_df['in_time'] = dt.standardize_time_format(time_df['in_time'])
        self.assertListEqual(time_df['in_time'].fillna('').to_list(), time_df['out_time'].fillna('').to_list())

    def test_parse_time11(self):
        """
        test time conversion from DD-MM-YYYY hh:mm:ss format to HH:MM:SS format
        """
        time_df = pd.DataFrame({
            'in_time': ['04/04/2002 14:33:00'],
            'out_time': ['14:33:00']
        })
        time_df['in_time'] = dt.standardize_time_format(time_df['in_time'])
        self.assertListEqual(time_df['in_time'].fillna('').to_list(), time_df['out_time'].fillna('').to_list())

    def test_parse_time12(self):
        """
        negative test case
        """
        time_df = pd.DataFrame({
            'in_time': [-36, 'Yes', None, '-367', -367],
            'out_time': ['-36', 'Yes', None, '-367', '-367']
        })
        time_df['in_time'] = dt.standardize_time_format(time_df['in_time'])
        self.assertListEqual(time_df['in_time'].fillna('').to_list(), time_df['out_time'].fillna('').to_list())

    def test_parse_time13(self):
        """
        new data format ddmmmyy:HH:MM - 03JAN22:12:00 -> 2022-01-03 12:00:00
        and ddmmmyyyy:HH:MM.0000000 - 03JAN2022:12:00.0000000 -> 2022-01-03 12:00:00
        """
        time_df = pd.DataFrame({
            'in_time': ['03JAN22:12:00'],
            'out_time': ['12:00:00']
        })
        time_df['in_time'] = dt.standardize_time_format(time_df['in_time'])
        self.assertListEqual(time_df['in_time'].fillna('').to_list(), time_df['out_time'].fillna('').to_list())

    def test_parse_time14(self):
        """
        new data format ddmmmyy:HH:MM - 03JAN22:12:00 -> 2022-01-03 12:00:00
        and ddmmmyyyy:HH:MM.0000000 - 03JAN2022:12:00.0000000 -> 2022-01-03 12:00:00
        """
        time_df = pd.DataFrame({
            'in_time': ['03JAN2024:16:15:00.0000000'],
            'out_time': ['16:15:00']
        })
        time_df['in_time'] = dt.standardize_time_format(time_df['in_time'])
        self.assertListEqual(time_df['in_time'].fillna('').to_list(), time_df['out_time'].fillna('').to_list())

    def test_parse_time15(self):
        """
        test time conversion from YYYY-MM-DD hh:mm:ss format to HH:MM:SS format
        """
        time_df = pd.DataFrame({
            'in_time': ['2023-10-02T19:15:00.000'],
            'out_time': ['19:15:00']
        })
        time_df['in_time'] = dt.standardize_time_format(time_df['in_time'])
        self.assertListEqual(time_df['in_time'].fillna('').to_list(), time_df['out_time'].fillna('').to_list())

    def test_parse_time16(self):
        """
        test time conversion from YYYY-MM-DD hh:mm:ss format to HH:MM:SS format
        """
        time_df = pd.DataFrame({
            'in_time': ['2023-10-02 19:15:00'],
            'out_time': ['19:15:00']
        })
        time_df['in_time'] = dt.standardize_time_format(time_df['in_time'])
        self.assertListEqual(time_df['in_time'].fillna('').to_list(), time_df['out_time'].fillna('').to_list())

    def test_parse_time17(self):
        """
        test time conversion from YYYY-MM-DD hh:mm:ss format to HH:MM:SS format
        """
        time_df = pd.DataFrame({
            'in_time': ['2023/10/02 19:15:00'],
            'out_time': ['19:15:00']
        })
        time_df['in_time'] = dt.standardize_time_format(time_df['in_time'])
        self.assertListEqual(time_df['in_time'].fillna('').to_list(), time_df['out_time'].fillna('').to_list())

    def test_parse_only_date1(self):
        """
        test time conversion from YYYY/mm/dd HH:MM:SS format to YYYY-mm-dd HH:MM:SS format

        """
        date_df = pd.DataFrame({
            'in_date': ['2022/08/09 13:34:00', '2022/08/09 13:23:00', '2022/08/09 13:00:11', None, ''],
            'out_date': ['2022-08-09T00:00:00', '2022-08-09T00:00:00', '2022-08-09T00:00:00', None, None]

        })
        date_df['in_date'] = dt.standardize_date_only_format(date_df['in_date'])
        self.assertListEqual(list(map(str, date_df['in_date'].fillna('').to_list())),
                             list(map(str, date_df['out_date'].fillna('').to_list())))

    def test_parse_only_date2(self):
        """
        test time conversion from ddmmmYY:HH:MM format to YYYY-mm-dd HH:MM:SS format
        and ddmmmyyyy:HH:MM.0000000 - 03JAN2022:12:00.0000000 -> 2022-01-03 12:00:00
        """
        date_df = pd.DataFrame({
            'in_date': ['03JAN22:12:00'],
            'out_date': ['2022-01-03T00:00:00']

        })
        date_df['in_date'] = dt.standardize_date_only_format(date_df['in_date'])
        self.assertListEqual(list(map(str, date_df['in_date'].fillna('').to_list())),
                             list(map(str, date_df['out_date'].fillna('').to_list())))

    def test_parse_only_date3(self):
        """
        test time conversion from ddmmmYY:HH:MM format to YYYY-mm-dd HH:MM:SS format
        and ddmmmyyyy:HH:MM.0000000 - 03JAN2022:12:00.0000000 -> 2022-01-03 12:00:00
        """
        date_df = pd.DataFrame({
            'in_date': ['03JAN2024:16:15:00.0000000'],
            'out_date': ['2024-01-03T00:00:00']

        })
        date_df['in_date'] = dt.standardize_date_only_format(date_df['in_date'])
        self.assertListEqual(list(map(str, date_df['in_date'].fillna('').to_list())),
                             list(map(str, date_df['out_date'].fillna('').to_list())))

    def test_parse_date1(self):
        """
        test date conversion from zone-format to YYYY-mm-dd HH:MM:SS format

        """
        date_df = pd.DataFrame({
            'in_date': ['2022-03-29 08:26:00-07:00', '2022-05-16 02:22:00+07:00', None, ''],
            'out_date': ['2022-03-29T08:26:00', '2022-05-16T02:22:00', None, None]

        })
        date_df['in_date'] = dt.standardize_date_format(date_df['in_date'])
        self.assertListEqual(list(map(str, date_df['in_date'].fillna('').to_list())),
                             list(map(str, date_df['out_date'].fillna('').to_list())))

    def test_parse_date2(self):
        """
        test date conversion from ddmmmYY:HH:MM to YYYY-mm-dd HH:MM:SS format
        and ddmmmyyyy:HH:MM.0000000 - 03JAN2022:12:00.0000000 -> 2022-01-03 12:00:00
        """
        date_df = pd.DataFrame({
            'in_date': ['29JAN22:08:26', '22FEB24:23:00'],
            'out_date': ['2022-01-29T08:26:00', '2024-02-22T23:00:00']

        })
        date_df['in_date'] = dt.standardize_date_format(date_df['in_date'])
        self.assertListEqual(list(map(str, date_df['in_date'].fillna('').to_list())),
                             list(map(str, date_df['out_date'].fillna('').to_list())))

    def test_parse_date3(self):
        """
        test date conversion from ddmmmYY:HH:MM to YYYY-mm-dd HH:MM:SS format
        and ddmmmyyyy:HH:MM.0000000 - 03JAN2022:12:00.0000000 -> 2022-01-03 12:00:00
        """
        date_df = pd.DataFrame({
            'in_date': ['03JAN2024:16:15:00.0000000'],
            'out_date': ['2024-01-03T16:15:00']

        })
        date_df['in_date'] = dt.standardize_date_format(date_df['in_date'])
        self.assertListEqual(list(map(str, date_df['in_date'].fillna('').to_list())),
                             list(map(str, date_df['out_date'].fillna('').to_list())))

    def test_parse_date4(self):
        """
        test date conversion from zone-format to YYYY-mm-ddTHH:MM:SS format
        """
        date_df = pd.DataFrame({
            'in_date': ['10/10/2024 03:33:00 PM'],
            'out_date': ['2024-10-10T15:33:00']
        })
        date_df['in_date'] = dt.standardize_date_format(date_df['in_date'])
        self.assertListEqual(list(map(str, date_df['in_date'].fillna('').to_list())),
                             list(map(str, date_df['out_date'].fillna('').to_list())))
