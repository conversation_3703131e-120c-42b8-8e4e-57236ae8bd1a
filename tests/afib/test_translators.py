import unittest
from unittest.mock import patch

import pandas as pd
from pandas.testing import assert_frame_equal

from ingestion.translators.translate_afib10_to_biome import TranslateAFib10ToBiome
from tests.mock_data import get_mock_data_file_path, load_mock_data


ingestion_file_info = {
    'id': 1,
    'client': 'client1',
    'created_on': '1709750572',
    'version': '1.0',
    'file_name': 'afib',
    'data_received_date': '2021-01-01'
}

ce = pd.DataFrame({
    'id': [1],
    'tenant_id': ['tenant1'],
    'code': ['hosp1']
})


class TranslateAFib10ToBiomeTest(unittest.TestCase):

    @patch('ingestion.translators.translate_afib10_to_biome.FileInfo.get_file_info',
           return_value=ingestion_file_info)
    def setUp(self, mock_get_file_info):
        self.dataset = 'afib'
        self.translator = TranslateAFib10ToBiome(
            filepath=get_mock_data_file_path('afib.xml', dataset=self.dataset),
            schema=None, reader=None, file_id=None, client='client1')

    def test_flatten_output(self):
        self.translator.denormalize()
        output = self.translator.output
        self.assertEqual(output.shape, (2, 247))
        self.assertEqual(output[output['NCDRPatientId'] == '34234'].shape[0], 1)
        self.assertEqual(output[output['NCDRPatientId'] == '34298'].shape[0], 1)

    def test_append_custom_fields(self):
        self.translator.denormalize()
        self.translator.append_custom_fields()
        output = self.translator.output
        self.assertEqual(output.shape, (2, 379))
        self.assertEqual(output[output['NCDRPatientId'] == '34234'].shape[0], 1)
        self.assertEqual(output[output['NCDRPatientId'] == '34298'].shape[0], 1)

    @patch('ingestion.translators.get_care_entities', return_value=ce)
    def test_translator_output(self, mock_get_care_entities):
        self.translator.denormalize()
        self.translator.append_custom_fields()
        self.translator.lower_case_fields()
        self.translator.rename_fields()
        self.translator.change_delimiters()
        self.translator.transform_field_values()
        self.translator.value_mapping()
        self.translator.post_process()
        self.translator.enforce_schema()
        self.translator.fix_field_lengths()

        output = self.translator.output
        output = output.sort_values(['ncdrpatientid', 'episodeid', 'arrivaldate',
                                     'dischargedate']).reset_index(drop=True)
        drop_fields = ['biomeimportdt', 'proceduretime']
        output.drop(drop_fields, axis=1, inplace=True)
        desired = load_mock_data('translator_output', dataset=self.dataset).drop('biomeencounterid', axis=1)
        assert_frame_equal(output[desired.columns], desired, check_index_type=False, check_dtype=False,
                           check_names=False)
