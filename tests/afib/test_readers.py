import unittest
from unittest.mock import patch
from pandas.testing import assert_frame_equal

from ingestion.readers.afib import ReaderAFib10
from tests.mock_data import get_mock_data_file_path, load_mock_data


ingestion_file_info = {
    'id': 1,
    'client': 'client1',
    'created_on': '**********',
    'version': '1.0',
    'file_name': 'afib'
}


class TestReaderAFib10(unittest.TestCase):

    def setUp(self) -> None:
        self.dataset = 'afib'
        self.reader = ReaderAFib10(
            filepath=get_mock_data_file_path('afib.xml', dataset=self.dataset), schema=None)

        self.reader.gen_parent_child_sections()
        self.reader.get_procedure_section_if_any()

        self.patients = self.reader.root.findall('patient')
        self.sections = self.patients[0].findall('section')
        self.elements = self.sections[0].findall('element')

    def test_get_submission_info(self):
        self.reader.get_submission_info()
        self.assertEqual(self.reader.part_id, '873200')
        self.assertEqual(self.reader.part_name, 'Mills-Peninsula Medical Center')

    def test_parent_child(self):
        self.assertEqual(self.reader.parent_child.shape, (26, 8))

        parent_section = self.reader.get_parent_section_code('ProcedureInformation')
        self.assertEqual(parent_section, 'Root')

        parent_section = self.reader.get_parent_section_code('EOC')
        self.assertEqual(parent_section, 'Root')

        parent_section = self.reader.get_parent_section_code('HXandRF')
        self.assertEqual(parent_section, 'Root')

        parent_section = self.reader.get_parent_section_code('CHA2DS2')
        self.assertEqual(parent_section, 'HXandRF')

        parent_section = self.reader.get_parent_section_code('DevicesUsed')
        self.assertEqual(parent_section, 'ProcedureInformation')

    def test_extract_data(self):
        data = self.reader.extract_data(self.elements[0])
        desired = {'codeSystem': '2.16.840.1.113883.3.3478.6.1', 'codeSystemName': 'ACC NCDR',
                   'codeField': '**********', 'displayName': 'LastName', 'valueType': 'LN', 'value': 'Doe',
                   'codeValue': None, 'unit': None, 'valueDisplayName': None, 'valueLength': 1}
        self.assertDictEqual(data, desired)

    def test_parse_patients(self):
        self.reader.parse_patients()
        self.assertEqual(self.reader.df_raw.shape, (589, 16))

    def test_join_data_dict(self):
        self.reader.parse_patients()
        self.reader.join_data_dict()
        self.assertEqual(self.reader.df_raw.shape, (605, 25))

    @patch('ingestion.readers.FileInfo.get_file_info',
           return_value=ingestion_file_info)
    def test_reader_output(self, mock_get_file_info):
        self.reader.parse_patients()
        self.reader.join_data_dict()
        self.reader.add_field_suffix()
        self.reader.get_file_info()

        out = self.reader.df_raw
        sort_cols = ['NCDRPatientId', 'shortName', 'IncrementalId']
        out = out[
            ['NCDRPatientId', 'EpisodeKey', 'VisitId', 'IncrementalId', 'shortName', 'valueDataDict']].sort_values(
            sort_cols).reset_index(drop=True)
        desired = load_mock_data('reader_raw_data', dataset=self.dataset).sort_values(sort_cols)
        assert_frame_equal(out, desired, check_index_type=False, check_dtype=False)

        self.reader.slice_tables()
        ncdr_tables = self.reader.ncdr_tables
        self.assertEqual(len(ncdr_tables), 6)

        self.assertEqual(ncdr_tables['DEMOGRAPHICS'].shape, (2, 42))
        self.assertEqual(ncdr_tables['EPISODEOFCARE'].shape, (2, 139))
        self.assertEqual(ncdr_tables['PROCEDUREINFORMATION'].shape, (2, 86))
        self.assertEqual(ncdr_tables['MEDICATIONS'].shape, (64, 13))
        self.assertEqual(ncdr_tables['DISCHARGEMEDS'].shape, (64, 13))
