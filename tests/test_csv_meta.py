import unittest
from unittest.mock import patch, mock_open
from ingestion.readers.csv import CSVMeta


class TestCSVMeta(unittest.TestCase):

    def test_read_header(self):
        csv_meta = CSVMeta(path='test.csv')
        csv_meta.raw_data = 'col1,col2,col3\nvalue1,value2,value3\n'
        header = csv_meta.read_header()
        self.assertEqual(header, 'col1,col2,col3')

    @patch('builtins.open', new_callable=mock_open, read_data='col1;col2;col3\nvalue1;value2;value3\n')
    def test_find_delimiter_semicolon(self, mock_file):
        csv_meta = CSVMeta(path='test.csv')
        csv_meta.header = 'col1;col2;col3\n'
        delimiter = csv_meta.find_delimiter()
        self.assertEqual(delimiter, ';')

    @patch('builtins.open', new_callable=mock_open, read_data='col1|col2|col3\nvalue1|value2|value3\n')
    def test_find_delimiter_pipe(self, mock_file):
        csv_meta = CSVMeta(path='test.csv')
        csv_meta.header = 'col1|col2|col3\n'
        delimiter = csv_meta.find_delimiter()
        self.assertEqual(delimiter, '|')

    def test_find_delimiter_in_file(self):
        csv_meta = CSVMeta(path='test.csv')
        csv_meta.header = 'col1|col2|col3\nvalue1|value2|value3\n'
        delimiter = csv_meta.find_delimiter(n_lines=1)
        self.assertEqual(delimiter, '|')

    @patch('builtins.open', new_callable=mock_open, read_data='col1;col2;col3\nvalue1;value2;value3\n')
    def test_find_delimiter_in_header(self, mock_file):
        csv_meta = CSVMeta(path='test.csv')
        csv_meta.header = 'col1;col2;col3\n'
        csv_meta.raw_bytes_utf8 = 'col1;col2;col3\nvalue1;value2;value3\n'
        delimiter = csv_meta.find_delimiter()
        self.assertEqual(delimiter, ';')

    @patch('ingestion.utils.file.FileInfo.find_dataset', return_value='test_dataset')
    def test_identify_dataset(self, mock_find_dataset):
        csv_meta = CSVMeta(path='test.csv', file_id=1, client='test_client')
        csv_meta.header = 'col1,col2,col3\n'
        csv_meta.delimiter = ','
        csv_meta.identify_dataset()
        self.assertEqual(csv_meta.dataset, 'test_dataset')
        mock_find_dataset.assert_called_once()
