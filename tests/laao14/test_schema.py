import unittest
from pandas.testing import assert_frame_equal

from ingestion.schema.laao import SchemaLAAO14
from tests.mock_data import load_mock_data


class TestSchemaLAAO14(unittest.TestCase):

    def setUp(self):
        self.bs = SchemaLAAO14()
        self.dataset = 'laao14'

    def test_create_section_tables(self):
        self.bs.sections = load_mock_data('schema_sections_all', dataset=self.dataset)
        self.bs.create_section_tables()
        assert_frame_equal(
            self.bs.sections[['Section Code', 'Table']],
            load_mock_data('schema_section_tables', dataset=self.dataset),
        )
