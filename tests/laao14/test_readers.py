import unittest
from unittest.mock import patch
from pandas.testing import assert_frame_equal

from ingestion.readers.laao import ReaderLAAO14, add_event_id, add_parent_id
from tests.mock_data import get_mock_data_file_path, load_mock_data


ingestion_file_info = {
    'id': 1,
    'client': 'client1',
    'created_on': '**********',
    'version': '1.4',
    'file_name': 'laao',
}


class TestReaderLAAO14(unittest.TestCase):

    def setUp(self):
        self.dataset = 'laao14'
        self.reader = ReaderLAAO14(
            filepath=get_mock_data_file_path('laao14.xml', dataset=self.dataset), schema=None)

        self.reader.gen_parent_child_sections()
        self.reader.get_procedure_section_if_any()

        self.patients = self.reader.root.findall('patient')
        self.sections = self.patients[0].findall('section')
        self.elements = self.sections[0].findall('element')

    def test_get_submission_info(self):
        self.reader.get_submission_info()
        self.assertEqual(self.reader.part_id, '12345')
        self.assertEqual(self.reader.part_name, 'CVHS')

    def test_parent_child(self):
        self.assertEqual(self.reader.parent_child.shape, (40, 8))

        parent_section = self.reader.get_parent_section_code('PROCINFO')
        self.assertEqual(parent_section, 'Root')

        parent_section = self.reader.get_parent_section_code('EOCINFO')
        self.assertEqual(parent_section, 'EOC')

        parent_section = self.reader.get_parent_section_code('DEVICES')
        self.assertEqual(parent_section, 'ACCESSSYS')

    def test_extract_data(self):
        data = self.reader.extract_data(self.elements[0])
        desired = {'codeSystem': '2.16.840.1.113883.3.3478.6.1', 'codeSystemName': None,
                   'codeField': '**********',
                   'displayName': 'Last Name', 'valueType': 'LN', 'value': 'W', 'codeValue': None,
                   'valueLength': 1, 'unit': None, 'valueDisplayName': None}
        self.assertDictEqual(data, desired)

    def test_parse_patients(self):
        self.reader.parse_patients()
        self.assertEqual(self.reader.df_raw.shape, (730, 17))

    def test_join_data_dict(self):
        self.reader.parse_patients()
        self.reader.join_data_dict()
        self.assertEqual(self.reader.df_raw.shape, (759, 26))

    @patch('ingestion.readers.FileInfo.get_file_info',
           return_value=ingestion_file_info)
    def test_reader_output(self, mock_get_file_info):
        self.reader.parse_patients()
        self.reader.join_data_dict()
        self.reader.df_raw = add_event_id(self.reader.df_raw, 'IPPEVENTS', '**********')
        self.reader.add_field_suffix()
        self.reader.df_raw = add_parent_id(self.reader.df_raw, self.dataset)
        self.reader.map_values()
        self.reader.get_file_info()

        out = self.reader.df_raw
        sort_cols = ['NCDRPatientId', 'shortName', 'IncrementalId']
        out = out[
            ['NCDRPatientId', 'EpisodeKey', 'VisitId', 'IncrementalId', 'ParentIncrementalId', 'shortName',
             'valueDataDict']].sort_values(sort_cols).reset_index(drop=True)
        desired = load_mock_data('reader_raw_data', dataset=self.dataset).sort_values(sort_cols)
        assert_frame_equal(out, desired, check_index_type=False, check_dtype=False)

        self.reader.slice_tables()
        ncdr_tables = self.reader.ncdr_tables
        self.assertEqual(len(ncdr_tables), 11)

        self.assertEqual(ncdr_tables['ACCESSSYS'].shape, (3, 14))
        self.assertEqual(ncdr_tables['ADJMEDS'].shape, (40, 14))
        self.assertEqual(ncdr_tables['DCMEDS'].shape, (36, 15))
        self.assertEqual(ncdr_tables['DEMOGRAPHICS'].shape, (2, 30))
        self.assertEqual(ncdr_tables['DEVICES'].shape, (4, 18))
        self.assertEqual(ncdr_tables['EPISODEOFCARE'].shape, (2, 127))
        self.assertEqual(ncdr_tables['HOSPEVEADJ'].shape, (2, 52))
        self.assertEqual(ncdr_tables['IPPEVENTS'].shape, (98, 16))
        self.assertEqual(ncdr_tables['OPRINFO'].shape, (4, 16))
        self.assertEqual(ncdr_tables['PREPROCMED'].shape, (40, 14))
        self.assertEqual(ncdr_tables['PROCINFO'].shape, (2, 57))
