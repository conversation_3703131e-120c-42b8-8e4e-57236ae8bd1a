import unittest
from unittest.mock import patch

import pandas as pd
from pandas.testing import assert_frame_equal

from ingestion.translators.translate_laao14_to_biome import TranslateLAAO14ToBiome
from tests.mock_data import get_mock_data_file_path, load_mock_data


ingestion_file_info = {
    'id': 1,
    'client': 'client1',
    'created_on': '1709750572',
    'version': '1.4',
    'file_name': 'laao',
}

ce = pd.DataFrame({
    'id': [1],
    'tenant_id': ['tenant1'],
    'code': ['hosp1']
})


class TestTranslatorLAAO14(unittest.TestCase):

    @patch('ingestion.translators.translate_laao14_to_biome.FileInfo.get_file_info',
           return_value=ingestion_file_info)
    def setUp(self, mock_get_file_info):
        self.dataset = 'laao14'
        self.translator = TranslateLAAO14ToBiome(
            filepath=get_mock_data_file_path('laao14.xml', dataset=self.dataset),
            schema=None, reader=None, file_id=None, client='client1')

    def test_flatten_output(self):
        self.translator.denormalize()
        output = self.translator.output
        self.assertEqual(output.shape, (686, 248))
        self.assertEqual(output[output['NCDRPatientId'] == '12345'].shape[0], 588)
        self.assertEqual(output[output['NCDRPatientId'] == '67890'].shape[0], 98)

    def test_append_custom_fields(self):
        self.translator.denormalize()
        self.translator.append_custom_fields()
        output = self.translator.output
        self.assertEqual(output.shape, (686, 438))
        self.assertEqual(output[output['NCDRPatientId'] == '12345'].shape[0], 588)
        self.assertEqual(output[output['NCDRPatientId'] == '67890'].shape[0], 98)

    @patch('ingestion.translators.get_care_entities', return_value=ce)
    def test_translator_output(self, mock_get_care_entities):
        self.translator.denormalize()
        self.translator.append_custom_fields()
        self.translator.lower_case_fields()
        self.translator.rename_fields()
        self.translator.change_delimiters()
        self.translator.value_mapping()
        self.translator.post_process()
        self.translator.enforce_schema()
        self.translator.fix_field_lengths()

        output = self.translator.output
        output = output.sort_values(['ncdrpatientid', 'episodeid', 'arrivaldate',
                                     'dischargedate']).reset_index(drop=True)
        drop_fields = ['biomeimportdt', 'procedurestartdate', 'procedurestopdate', 'procedurestarttime',
                       'procedurestoptime']
        output.drop(drop_fields, axis=1, inplace=True)
        desired = load_mock_data('translator_output', dataset=self.dataset)
        assert_frame_equal(output[desired.columns], desired, check_index_type=False, check_dtype=False,
                           check_names=False)
