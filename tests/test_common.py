import unittest
import pandas as pd
from unittest.mock import patch
from ingestion.utils.common import infer_id_fields, get_file_name_from_path, create_table_name, getenv


class TestCommonFuncs(unittest.TestCase):

    def test_infer_id_fields(self):
        df = pd.DataFrame({
            'NCDRPatientId': [1, 2, 3],
            'EpisodeKey': ['0', '0', '0'],
            'VisitId': ['0', '0', '0']
        })
        self.assertEqual(infer_id_fields(df), ['NCDRPatientId'])

        df = pd.DataFrame({
            'NCDRPatientId': [1, 2, 3],
            'EpisodeKey': ['1', '2', '3'],
            'VisitId': ['0', '0', '0']
        })
        self.assertEqual(infer_id_fields(df), ['NCDRPatientId', 'EpisodeKey'])

        df = pd.DataFrame({
            'NCDRPatientId': [1, 2, 3],
            'EpisodeKey': ['1', '2', '3'],
            'VisitId': ['1', '2', '3']
        })
        self.assertEqual(infer_id_fields(df), ['NCDRPatientId', 'EpisodeKey', 'VisitId'])

    def test_get_file_name_from_path(self):
        self.assertEqual(get_file_name_from_path('path\\to\\file.xml'), 'file')
        self.assertEqual(get_file_name_from_path('path/to/file.xml'), 'file')

    def test_create_table_name(self):
        file_info = {'id': 'file_id', 'data_received_date': '20210101'}
        table_name = create_table_name(file_info, 'dsm', 'Hospname', '22Q2').split('_')[:-1]
        self.assertEqual('_'.join(table_name), '20210101_dsm_hospname_22q2')

    @patch('os.getenv')
    def test_getenv(self, mock_getenv):
        mock_getenv.return_value = 'value'
        self.assertEqual(getenv('key'), 'value')
