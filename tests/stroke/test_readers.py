import unittest
from unittest.mock import patch

from tests.mock_data import get_mock_data_file_path
from ingestion.readers.stroke import ReaderStroke
from ingestion.readers.csv import BaseReaderCSV

ingestion_file_info = {
    'id': 1,
    'client': 'client1',
    'created_on': '1709750572',
    'version': '1.0',
    'file_name': 'stroke',
}


class TestReaderStroke(unittest.TestCase):

    @patch('ingestion.readers.csv.FileInfo.get_file_info', return_value=ingestion_file_info)
    def setUp(self, mock_file_info):
        self.reader = ReaderStroke(
            filepath=get_mock_data_file_path('stroke.csv', dataset='stroke'), schema=None)

        self.reader.dataset = 'stroke'
        self.reader.file_info = self.reader.get_file_info()
        BaseReaderCSV.execute(self.reader)

    def test_pre_process(self):
        self.reader.pre_process()
        self.assertEqual(self.reader.out.shape, (3, 1226))

    def test_append_file_info(self):
        self.reader.pre_process()
        self.reader.append_file_info()
        self.assertEqual(self.reader.out.shape, (3, 1231))

    def test_update_file_info(self):
        self.reader.update_file_info()
        self.assertEqual(self.reader.file_info['dataframes'], [{'name': 'Stroke', 'row_count': 3}])
        self.assertEqual(self.reader.file_info['num_patients'], 1)

    def test_execute(self):
        self.reader.pre_process()
        self.reader.update_file_info()
        self.reader.append_file_info()
        self.assertEqual(self.reader.out.shape, (3, 1231))
        self.assertListEqual(self.reader.out['FACILITY_NAME'].tolist(), ['hosp1', 'hosp1', 'hosp1'])
