import unittest
from unittest.mock import patch

import pandas as pd
from pandas.testing import assert_frame_equal

from ingestion.translators.translate_stroke_to_biome import TranslateStrokeToBiome
from tests.mock_data import get_mock_data_file_path, load_mock_data


ingestion_file_info = {
    'id': 1,
    'client': 'client1',
    'created_on': '1709750572',
    'version': '2025012724',
    'file_name': 'stroke',
    'data_received_date': '2025-01-27'
}

ce = pd.DataFrame({
    'id': {'1': 'ce1', '2': 'ce2'},
    'ids': {'1': '1', '2': '2'},
    'name': {'1': 'hosp1', '2': 'hosp2'},
    'tenants': {'1': {'id': '1', 'code': 'hosp1'}, '2': {'id': '2', 'code': 'hosp2'}},
    'tenant_id': {'1': 'tenant1', '2': 'tenant2'},
    'code': {'1': 'hosp1', '2': 'hosp2'},
    'tenant_code': {'1': 'client1', '2': 'client2'}
})


class TranslateStrokeToBiomeTest(unittest.TestCase):

    @patch('ingestion.translators.translate_stroke_to_biome.FileInfo.get_file_info',
           return_value=ingestion_file_info)
    @patch('ingestion.translators.translate_stroke_to_biome.ReadRawTable.execute',
           return_value=None)
    def setUp(self, mock_execute, mock_get_file_info):
        self.dataset = 'stroke'
        self.translator = TranslateStrokeToBiome(
            filepath=get_mock_data_file_path('stroke.csv', dataset=self.dataset),
            schema=None, reader=None, file_id=None, client='client1', db_as_source=True, source_db='test_db')
        self.translator.reader.out = load_mock_data('reader_raw_data', dataset=self.dataset)
        self.translator.file_info = ingestion_file_info

    def test_denormalize(self):
        self.translator.denormalize()
        self.assertEqual(self.translator.output.shape, (3, 1231))

    @patch('ingestion.translators.translate_stroke_to_biome.get_all_care_entities', return_value=ce)
    @patch('ingestion.utils.odin.get_all_care_entities', return_value=ce)
    @patch('ingestion.translators.translate_stroke_to_biome.FileInfo.get_file_info',
           return_value=ingestion_file_info)
    def test_execute(self, mock_ce1, mock_ce2, mock_get_file_info):
        self.translator.execute()
        desired = load_mock_data('translator_output', dataset=self.dataset)
        output = self.translator.output
        assert_frame_equal(output.sort_values('strokepatientid')[desired.columns],
                           desired.sort_values('strokepatientid'),
                           check_dtype=False, check_index_type=False, check_names=False)

        self.assertEqual('_'.join(self.translator.target_table.split('_')[:-1]), '20250127_stroke_client1_24q4')
